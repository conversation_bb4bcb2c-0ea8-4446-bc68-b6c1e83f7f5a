{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"@app/services/projects.service\";\nexport class UpdateProjectComponent {\n  constructor(route, fb, projetService, router) {\n    this.route = route;\n    this.fb = fb;\n    this.projetService = projetService;\n    this.router = router;\n  }\n  ngOnInit() {\n    this.projectId = this.route.snapshot.paramMap.get('id') || '';\n    this.updateForm = this.fb.group({\n      titre: ['', Validators.required],\n      description: [''],\n      groupe: [''],\n      dateLimite: ['', Validators.required]\n    });\n    this.projetService.getProjetById(this.projectId).subscribe(projet => {\n      this.updateForm.patchValue({\n        titre: projet.titre,\n        description: projet.description,\n        groupe: projet.groupe,\n        dateLimite: projet.dateLimite\n      });\n    });\n  }\n  onSubmit() {\n    if (this.updateForm.valid) {\n      this.projetService.updateProjet(this.projectId, this.updateForm.value).subscribe(() => {\n        alert('Projet mis à jour avec succès');\n        this.router.navigate(['/projects']); // adapte selon ta route\n      });\n    }\n  }\n\n  static {\n    this.ɵfac = function UpdateProjectComponent_Factory(t) {\n      return new (t || UpdateProjectComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i3.ProjetService), i0.ɵɵdirectiveInject(i1.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: UpdateProjectComponent,\n      selectors: [[\"app-update-project\"]],\n      decls: 22,\n      vars: 2,\n      consts: [[1, \"max-w-xl\", \"mx-auto\", \"mt-10\", \"p-6\", \"bg-white\", \"shadow-md\", \"rounded-xl\"], [1, \"text-2xl\", \"font-bold\", \"text-[#4f5fad]\", \"mb-4\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"mb-4\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"formControlName\", \"titre\", 1, \"mt-1\", \"block\", \"w-full\", \"border\", \"border-gray-300\", \"rounded-md\", \"p-2\"], [\"formControlName\", \"groupe\", 1, \"mt-1\", \"block\", \"w-full\", \"border\", \"border-gray-300\", \"rounded-md\", \"p-2\"], [\"type\", \"date\", \"formControlName\", \"dateLimite\", 1, \"mt-1\", \"block\", \"w-full\", \"border\", \"border-gray-300\", \"rounded-md\", \"p-2\"], [\"formControlName\", \"description\", 1, \"mt-1\", \"block\", \"w-full\", \"border\", \"border-gray-300\", \"rounded-md\", \"p-2\"], [\"type\", \"submit\", 1, \"bg-[#7826b5]\", \"text-white\", \"px-4\", \"py-2\", \"rounded\", \"hover:bg-[#5f1d8f]\", 3, \"disabled\"]],\n      template: function UpdateProjectComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"h2\", 1);\n          i0.ɵɵtext(2, \"Modifier le projet\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"form\", 2);\n          i0.ɵɵlistener(\"ngSubmit\", function UpdateProjectComponent_Template_form_ngSubmit_3_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(4, \"div\", 3)(5, \"label\", 4);\n          i0.ɵɵtext(6, \"Titre\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(7, \"input\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"div\", 3)(9, \"label\", 4);\n          i0.ɵɵtext(10, \"Groupe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(11, \"input\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"div\", 3)(13, \"label\", 4);\n          i0.ɵɵtext(14, \"Date limite\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(15, \"input\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"div\", 3)(17, \"label\", 4);\n          i0.ɵɵtext(18, \"Description\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(19, \"textarea\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"button\", 9);\n          i0.ɵɵtext(21, \" Mettre \\u00E0 jour \");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"formGroup\", ctx.updateForm);\n          i0.ɵɵadvance(17);\n          i0.ɵɵproperty(\"disabled\", !ctx.updateForm.valid);\n        }\n      },\n      dependencies: [i2.ɵNgNoValidate, i2.DefaultValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.FormGroupDirective, i2.FormControlName],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJ1cGRhdGUtcHJvamVjdC5jb21wb25lbnQuY3NzIn0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvYWRtaW4vcHJvamVjdHMvdXBkYXRlLXByb2plY3QvdXBkYXRlLXByb2plY3QuY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0FBQ0EsNEtBQTRLIiwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "UpdateProjectComponent", "constructor", "route", "fb", "projetService", "router", "ngOnInit", "projectId", "snapshot", "paramMap", "get", "updateForm", "group", "titre", "required", "description", "groupe", "dateLimite", "getProjetById", "subscribe", "projet", "patchValue", "onSubmit", "valid", "updateProjet", "value", "alert", "navigate", "i0", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "i2", "FormBuilder", "i3", "ProjetService", "Router", "selectors", "decls", "vars", "consts", "template", "UpdateProjectComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "UpdateProjectComponent_Template_form_ngSubmit_3_listener", "ɵɵelement", "ɵɵadvance", "ɵɵproperty"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\admin\\projects\\update-project\\update-project.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\admin\\projects\\update-project\\update-project.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { ProjetService } from '@app/services/projects.service';\n\n@Component({\n  selector: 'app-update-project',\n  templateUrl: './update-project.component.html',\n  styleUrls: ['./update-project.component.css'],\n})\nexport class UpdateProjectComponent implements OnInit {\n  updateForm!: FormGroup;\n  projectId!: string;\n\n  constructor(\n    private route: ActivatedRoute,\n    private fb: FormBuilder,\n    private projetService: ProjetService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    this.projectId = this.route.snapshot.paramMap.get('id') || '';\n    this.updateForm = this.fb.group({\n      titre: ['', Validators.required],\n      description: [''],\n      groupe: [''],\n      dateLimite: ['', Validators.required],\n    });\n\n    this.projetService.getProjetById(this.projectId).subscribe((projet) => {\n      this.updateForm.patchValue({\n        titre: projet.titre,\n        description: projet.description,\n        groupe: projet.groupe,\n        dateLimite: projet.dateLimite,\n      });\n    });\n  }\n\n  onSubmit(): void {\n    if (this.updateForm.valid) {\n      this.projetService\n        .updateProjet(this.projectId, this.updateForm.value)\n        .subscribe(() => {\n          alert('Projet mis à jour avec succès');\n          this.router.navigate(['/projects']); // adapte selon ta route\n        });\n    }\n  }\n}\n", "<div class=\"max-w-xl mx-auto mt-10 p-6 bg-white shadow-md rounded-xl\">\n    <h2 class=\"text-2xl font-bold text-[#4f5fad] mb-4\">Modifier le projet</h2>\n    <form [formGroup]=\"updateForm\" (ngSubmit)=\"onSubmit()\">\n      <div class=\"mb-4\">\n        <label class=\"block text-sm font-medium text-gray-700\">Titre</label>\n        <input formControlName=\"titre\" class=\"mt-1 block w-full border border-gray-300 rounded-md p-2\" />\n      </div>\n  \n      <div class=\"mb-4\">\n        <label class=\"block text-sm font-medium text-gray-700\">Groupe</label>\n        <input formControlName=\"groupe\" class=\"mt-1 block w-full border border-gray-300 rounded-md p-2\" />\n      </div>\n  \n      <div class=\"mb-4\">\n        <label class=\"block text-sm font-medium text-gray-700\">Date limite</label>\n        <input type=\"date\" formControlName=\"dateLimite\" class=\"mt-1 block w-full border border-gray-300 rounded-md p-2\" />\n      </div>\n  \n      <div class=\"mb-4\">\n        <label class=\"block text-sm font-medium text-gray-700\">Description</label>\n        <textarea formControlName=\"description\" class=\"mt-1 block w-full border border-gray-300 rounded-md p-2\"></textarea>\n      </div>\n  \n      <button type=\"submit\" [disabled]=\"!updateForm.valid\" class=\"bg-[#7826b5] text-white px-4 py-2 rounded hover:bg-[#5f1d8f]\">\n        Mettre à jour\n      </button>\n    </form>\n  </div>\n  "], "mappings": "AAEA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;AAQnE,OAAM,MAAOC,sBAAsB;EAIjCC,YACUC,KAAqB,EACrBC,EAAe,EACfC,aAA4B,EAC5BC,MAAc;IAHd,KAAAH,KAAK,GAALA,KAAK;IACL,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,MAAM,GAANA,MAAM;EACb;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,SAAS,GAAG,IAAI,CAACL,KAAK,CAACM,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE;IAC7D,IAAI,CAACC,UAAU,GAAG,IAAI,CAACR,EAAE,CAACS,KAAK,CAAC;MAC9BC,KAAK,EAAE,CAAC,EAAE,EAAEd,UAAU,CAACe,QAAQ,CAAC;MAChCC,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,MAAM,EAAE,CAAC,EAAE,CAAC;MACZC,UAAU,EAAE,CAAC,EAAE,EAAElB,UAAU,CAACe,QAAQ;KACrC,CAAC;IAEF,IAAI,CAACV,aAAa,CAACc,aAAa,CAAC,IAAI,CAACX,SAAS,CAAC,CAACY,SAAS,CAAEC,MAAM,IAAI;MACpE,IAAI,CAACT,UAAU,CAACU,UAAU,CAAC;QACzBR,KAAK,EAAEO,MAAM,CAACP,KAAK;QACnBE,WAAW,EAAEK,MAAM,CAACL,WAAW;QAC/BC,MAAM,EAAEI,MAAM,CAACJ,MAAM;QACrBC,UAAU,EAAEG,MAAM,CAACH;OACpB,CAAC;IACJ,CAAC,CAAC;EACJ;EAEAK,QAAQA,CAAA;IACN,IAAI,IAAI,CAACX,UAAU,CAACY,KAAK,EAAE;MACzB,IAAI,CAACnB,aAAa,CACfoB,YAAY,CAAC,IAAI,CAACjB,SAAS,EAAE,IAAI,CAACI,UAAU,CAACc,KAAK,CAAC,CACnDN,SAAS,CAAC,MAAK;QACdO,KAAK,CAAC,+BAA+B,CAAC;QACtC,IAAI,CAACrB,MAAM,CAACsB,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;MACvC,CAAC,CAAC;;EAER;;;;uBAvCW3B,sBAAsB,EAAA4B,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,aAAA,GAAAP,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAM,MAAA;IAAA;EAAA;;;YAAtBpC,sBAAsB;MAAAqC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCVnCf,EAAA,CAAAiB,cAAA,aAAsE;UACfjB,EAAA,CAAAkB,MAAA,yBAAkB;UAAAlB,EAAA,CAAAmB,YAAA,EAAK;UAC1EnB,EAAA,CAAAiB,cAAA,cAAuD;UAAxBjB,EAAA,CAAAoB,UAAA,sBAAAC,yDAAA;YAAA,OAAYL,GAAA,CAAAtB,QAAA,EAAU;UAAA,EAAC;UACpDM,EAAA,CAAAiB,cAAA,aAAkB;UACuCjB,EAAA,CAAAkB,MAAA,YAAK;UAAAlB,EAAA,CAAAmB,YAAA,EAAQ;UACpEnB,EAAA,CAAAsB,SAAA,eAAiG;UACnGtB,EAAA,CAAAmB,YAAA,EAAM;UAENnB,EAAA,CAAAiB,cAAA,aAAkB;UACuCjB,EAAA,CAAAkB,MAAA,cAAM;UAAAlB,EAAA,CAAAmB,YAAA,EAAQ;UACrEnB,EAAA,CAAAsB,SAAA,gBAAkG;UACpGtB,EAAA,CAAAmB,YAAA,EAAM;UAENnB,EAAA,CAAAiB,cAAA,cAAkB;UACuCjB,EAAA,CAAAkB,MAAA,mBAAW;UAAAlB,EAAA,CAAAmB,YAAA,EAAQ;UAC1EnB,EAAA,CAAAsB,SAAA,gBAAkH;UACpHtB,EAAA,CAAAmB,YAAA,EAAM;UAENnB,EAAA,CAAAiB,cAAA,cAAkB;UACuCjB,EAAA,CAAAkB,MAAA,mBAAW;UAAAlB,EAAA,CAAAmB,YAAA,EAAQ;UAC1EnB,EAAA,CAAAsB,SAAA,mBAAmH;UACrHtB,EAAA,CAAAmB,YAAA,EAAM;UAENnB,EAAA,CAAAiB,cAAA,iBAA0H;UACxHjB,EAAA,CAAAkB,MAAA,4BACF;UAAAlB,EAAA,CAAAmB,YAAA,EAAS;;;UAvBLnB,EAAA,CAAAuB,SAAA,GAAwB;UAAxBvB,EAAA,CAAAwB,UAAA,cAAAR,GAAA,CAAAjC,UAAA,CAAwB;UAqBNiB,EAAA,CAAAuB,SAAA,IAA8B;UAA9BvB,EAAA,CAAAwB,UAAA,cAAAR,GAAA,CAAAjC,UAAA,CAAAY,KAAA,CAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}