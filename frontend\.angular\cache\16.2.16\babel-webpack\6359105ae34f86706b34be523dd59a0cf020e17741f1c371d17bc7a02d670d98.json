{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, Input } from '@angular/core';\nimport { finalize } from 'rxjs/operators';\nexport let AiChatComponent = class AiChatComponent {\n  constructor(aiService, taskService, notificationService) {\n    this.aiService = aiService;\n    this.taskService = taskService;\n    this.notificationService = notificationService;\n    this.projectTitle = '';\n    this.isGenerating = false;\n    this.generatedContent = null;\n    this.error = null;\n    // Pour le chat\n    this.messages = [];\n    this.userQuestion = '';\n    this.isAskingQuestion = false;\n  }\n  ngOnInit() {\n    // Ajouter un message de bienvenue\n    this.messages.push({\n      role: 'assistant',\n      content: 'Bonjour ! Je suis votre assistant IA pour la gestion de projet. Entrez le titre de votre projet pour que je puisse vous aider à le diviser en tâches, ou posez-moi une question sur la gestion de projet.'\n    });\n  }\n  generateTasks() {\n    if (!this.projectTitle.trim()) {\n      this.notificationService.showError('Veuillez entrer un titre de projet');\n      return;\n    }\n    // Vérifier si l'équipe a des membres, sinon utiliser un nombre par défaut\n    let memberCount = this.team && this.team.members ? this.team.members.length : 3;\n    // S'assurer que nous avons au moins 3 entités pour un projet significatif\n    const effectiveMemberCount = Math.max(memberCount, 3);\n    if (memberCount === 0) {\n      this.notificationService.showWarning(\"L'équipe n'a pas de membres. Des tâches génériques seront créées.\");\n    }\n    this.isGenerating = true;\n    this.error = null;\n    console.log(`Génération de tâches pour ${effectiveMemberCount} entités (équipe de ${memberCount} membres)`);\n    // Ajouter la demande aux messages\n    this.messages.push({\n      role: 'user',\n      content: `Génère des tâches pour le projet \"${this.projectTitle}\" avec exactement ${effectiveMemberCount} entités, une pour chaque membre de l'équipe. Chaque entité doit représenter un module distinct du projet.`\n    });\n    // Ajouter un message de chargement\n    const loadingMessageIndex = this.messages.length;\n    this.messages.push({\n      role: 'assistant',\n      content: 'Je génère des tâches pour votre projet...'\n    });\n    // Récupérer les informations sur les membres de l'équipe\n    let teamMembers = [];\n    if (this.team && this.team.members) {\n      // Utiliser les IDs des membres\n      teamMembers = this.team.members.map((memberId, index) => {\n        return {\n          id: memberId,\n          name: `Membre ${index + 1}`,\n          role: 'membre'\n        };\n      });\n      console.log(\"Informations sur les membres passées à l'IA:\", teamMembers);\n    }\n    this.aiService.generateProjectTasks(this.projectTitle, memberCount, teamMembers).pipe(finalize(() => this.isGenerating = false)).subscribe({\n      next: result => {\n        if (!result || !result.entities || result.entities.length === 0) {\n          console.error(\"Résultat invalide reçu de l'API:\", result);\n          this.handleGenerationError(loadingMessageIndex, 'Format de réponse invalide');\n          return;\n        }\n        this.generatedContent = result;\n        // Remplacer le message de chargement par la réponse\n        this.messages[loadingMessageIndex] = {\n          role: 'assistant',\n          content: `J'ai généré ${result.entities.length} entités pour votre projet \"${result.projectTitle}\" avec un total de ${this.countTasks(result)} tâches.`\n        };\n        this.notificationService.showSuccess('Tâches générées avec succès');\n      },\n      error: error => {\n        console.error('Erreur lors de la génération des tâches:', error);\n        this.handleGenerationError(loadingMessageIndex, error.message || 'Erreur inconnue');\n      }\n    });\n  }\n  // Méthode pour gérer les erreurs de génération\n  handleGenerationError(messageIndex, errorDetails) {\n    this.error = 'Impossible de générer les tâches. Veuillez réessayer.';\n    // Remplacer le message de chargement par le message d'erreur\n    this.messages[messageIndex] = {\n      role: 'assistant',\n      content: \"Désolé, je n'ai pas pu générer les tâches. Veuillez réessayer avec un titre de projet différent.\"\n    };\n    this.notificationService.showError('Erreur lors de la génération des tâches: ' + errorDetails);\n  }\n  askQuestion() {\n    if (!this.userQuestion.trim()) {\n      return;\n    }\n    const question = this.userQuestion.trim();\n    this.userQuestion = '';\n    this.isAskingQuestion = true;\n    // Ajouter la question aux messages\n    this.messages.push({\n      role: 'user',\n      content: question\n    });\n    const projectContext = {\n      title: this.projectTitle || (this.generatedContent ? this.generatedContent.projectTitle : ''),\n      description: \"Projet géré par l'équipe \" + (this.team ? this.team.name : '')\n    };\n    this.aiService.askProjectQuestion(question, projectContext).pipe(finalize(() => this.isAskingQuestion = false)).subscribe({\n      next: response => {\n        // Ajouter la réponse aux messages\n        this.messages.push({\n          role: 'assistant',\n          content: response\n        });\n      },\n      error: error => {\n        console.error(\"Erreur lors de la demande à l'IA:\", error);\n        // Ajouter l'erreur aux messages\n        this.messages.push({\n          role: 'assistant',\n          content: \"Désolé, je n'ai pas pu répondre à votre question. Veuillez réessayer.\"\n        });\n        this.notificationService.showError(\"Erreur lors de la communication avec l'IA\");\n      }\n    });\n  }\n  createTasks() {\n    if (!this.generatedContent || !this.team || !this.team._id) {\n      this.notificationService.showError('Aucune tâche générée ou équipe invalide');\n      return;\n    }\n    let createdCount = 0;\n    const totalTasks = this.countTasks(this.generatedContent);\n    // Vérifier si l'équipe a des membres\n    if (!this.team.members || this.team.members.length === 0) {\n      this.notificationService.showError(\"L'équipe n'a pas de membres pour assigner les tâches\");\n      return;\n    }\n    // Préparer la liste des membres de l'équipe\n    const teamMembers = this.team.members.map(member => {\n      return typeof member === 'string' ? member : member.userId;\n    });\n    // Créer un mapping des noms de membres vers leurs IDs\n    const memberNameToIdMap = {};\n    teamMembers.forEach((memberId, index) => {\n      memberNameToIdMap[`Membre ${index + 1}`] = memberId;\n    });\n    console.log(\"Membres de l'équipe disponibles pour l'assignation:\", teamMembers);\n    console.log('Mapping des noms de membres vers leurs IDs:', memberNameToIdMap);\n    // Pour chaque entité\n    this.generatedContent.entities.forEach(entity => {\n      // Déterminer le membre assigné à cette entité\n      let assignedMemberId;\n      // Si l'IA a suggéré une assignation\n      if (entity.assignedTo) {\n        // Essayer de trouver l'ID du membre à partir du nom suggéré\n        const memberName = entity.assignedTo;\n        if (memberNameToIdMap[memberName]) {\n          assignedMemberId = memberNameToIdMap[memberName];\n          console.log(`Assignation suggérée par l'IA: Entité \"${entity.name}\" assignée à \"${memberName}\" (ID: ${assignedMemberId})`);\n        } else {\n          // Si le nom n'est pas trouvé, assigner aléatoirement\n          const randomMemberIndex = Math.floor(Math.random() * teamMembers.length);\n          assignedMemberId = teamMembers[randomMemberIndex];\n          console.log(`Nom de membre \"${memberName}\" non trouvé, assignation aléatoire à l'index ${randomMemberIndex}`);\n        }\n      } else {\n        // Si pas d'assignation suggérée, assigner aléatoirement\n        const randomMemberIndex = Math.floor(Math.random() * teamMembers.length);\n        assignedMemberId = teamMembers[randomMemberIndex];\n        console.log(`Pas d'assignation suggérée, assignation aléatoire à l'index ${randomMemberIndex}`);\n      }\n      // Pour chaque tâche dans l'entité\n      entity.tasks.forEach(taskData => {\n        const task = {\n          title: taskData.title,\n          description: `[${entity.name}] ${taskData.description}`,\n          status: taskData.status || 'todo',\n          priority: taskData.priority || 'medium',\n          teamId: this.team._id || '',\n          // Utiliser l'ID du membre assigné à l'entité\n          assignedTo: assignedMemberId\n        };\n        this.taskService.createTask(task).subscribe({\n          next: () => {\n            createdCount++;\n            if (createdCount === totalTasks) {\n              this.notificationService.showSuccess(`${createdCount} tâches créées avec succès et assignées aux membres de l'équipe`);\n              // Réinitialiser après création\n              this.generatedContent = null;\n              this.projectTitle = '';\n            }\n          },\n          error: error => {\n            console.error('Erreur lors de la création de la tâche:', error);\n            this.notificationService.showError('Erreur lors de la création des tâches');\n          }\n        });\n      });\n    });\n  }\n  countTasks(content) {\n    if (!content || !content.entities) return 0;\n    return content.entities.reduce((total, entity) => {\n      return total + (entity.tasks ? entity.tasks.length : 0);\n    }, 0);\n  }\n  // Méthode pour obtenir un dégradé de couleur basé sur l'index\n  getGradientForIndex(index) {\n    // Liste de dégradés prédéfinis\n    const gradients = ['linear-gradient(45deg, #007bff, #6610f2)', 'linear-gradient(45deg, #11998e, #38ef7d)', 'linear-gradient(45deg, #FC5C7D, #6A82FB)', 'linear-gradient(45deg, #FF8008, #FFC837)', 'linear-gradient(45deg, #8E2DE2, #4A00E0)', 'linear-gradient(45deg, #2193b0, #6dd5ed)', 'linear-gradient(45deg, #373B44, #4286f4)', 'linear-gradient(45deg, #834d9b, #d04ed6)', 'linear-gradient(45deg, #0cebeb, #20e3b2, #29ffc6)' // Turquoise\n    ];\n    // Utiliser le modulo pour s'assurer que nous ne dépassons pas le tableau\n    return gradients[index % gradients.length];\n  }\n  // Méthode pour obtenir une couleur unique basée sur l'index\n  getColorForIndex(index) {\n    // Liste de couleurs prédéfinies\n    const colors = ['#007bff', '#11998e', '#FC5C7D', '#FF8008', '#8E2DE2', '#2193b0', '#373B44', '#834d9b', '#0cebeb' // Turquoise\n    ];\n    // Utiliser le modulo pour s'assurer que nous ne dépassons pas le tableau\n    return colors[index % colors.length];\n  }\n  // Méthode pour obtenir une icône en fonction du nom du module\n  getIconForModule(moduleName) {\n    // Convertir le nom du module en minuscules pour faciliter la comparaison\n    const name = moduleName.toLowerCase();\n    // Mapper les noms de modules courants à des icônes Bootstrap\n    if (name.includes('crud') || name.includes('api') || name.includes('données') || name.includes('base')) {\n      return 'bi-database-fill';\n    } else if (name.includes('interface') || name.includes('ui') || name.includes('front') || name.includes('utilisateur')) {\n      return 'bi-window';\n    } else if (name.includes('déploiement') || name.includes('serveur') || name.includes('cloud')) {\n      return 'bi-cloud-arrow-up-fill';\n    } else if (name.includes('test') || name.includes('qualité') || name.includes('qa')) {\n      return 'bi-bug-fill';\n    } else if (name.includes('sécurité') || name.includes('auth')) {\n      return 'bi-shield-lock-fill';\n    } else if (name.includes('paiement') || name.includes('transaction')) {\n      return 'bi-credit-card-fill';\n    } else if (name.includes('utilisateur') || name.includes('user') || name.includes('profil')) {\n      return 'bi-person-fill';\n    } else if (name.includes('doc') || name.includes('documentation')) {\n      return 'bi-file-text-fill';\n    } else if (name.includes('mobile') || name.includes('app')) {\n      return 'bi-phone-fill';\n    } else if (name.includes('backend') || name.includes('serveur')) {\n      return 'bi-server';\n    } else if (name.includes('analytics') || name.includes('statistique') || name.includes('seo')) {\n      return 'bi-graph-up';\n    }\n    // Icône par défaut si aucune correspondance n'est trouvée\n    return 'bi-code-square';\n  }\n  // Méthode pour obtenir l'heure actuelle au format HH:MM\n  getCurrentTime() {\n    const now = new Date();\n    const hours = now.getHours().toString().padStart(2, '0');\n    const minutes = now.getMinutes().toString().padStart(2, '0');\n    return `${hours}:${minutes}`;\n  }\n};\n__decorate([Input()], AiChatComponent.prototype, \"team\", void 0);\nAiChatComponent = __decorate([Component({\n  selector: 'app-ai-chat',\n  templateUrl: './ai-chat.component.html',\n  styleUrls: ['./ai-chat.component.css']\n})], AiChatComponent);", "map": {"version": 3, "names": ["Component", "Input", "finalize", "AiChatComponent", "constructor", "aiService", "taskService", "notificationService", "projectTitle", "isGenerating", "generatedContent", "error", "messages", "userQuestion", "isAskingQuestion", "ngOnInit", "push", "role", "content", "generateTasks", "trim", "showError", "memberCount", "team", "members", "length", "effectiveMemberCount", "Math", "max", "showWarning", "console", "log", "loadingMessageIndex", "teamMembers", "map", "memberId", "index", "id", "name", "generateProjectTasks", "pipe", "subscribe", "next", "result", "entities", "handleGenerationError", "countTasks", "showSuccess", "message", "messageIndex", "errorDetails", "askQuestion", "question", "projectContext", "title", "description", "askProjectQuestion", "response", "createTasks", "_id", "createdCount", "totalTasks", "member", "userId", "memberNameToIdMap", "for<PERSON>ach", "entity", "assignedMemberId", "assignedTo", "memberName", "randomMemberIndex", "floor", "random", "tasks", "taskData", "task", "status", "priority", "teamId", "createTask", "reduce", "total", "getGradientForIndex", "gradients", "getColorForIndex", "colors", "getIconForModule", "moduleName", "toLowerCase", "includes", "getCurrentTime", "now", "Date", "hours", "getHours", "toString", "padStart", "minutes", "getMinutes", "__decorate", "selector", "templateUrl", "styleUrls"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\admin\\equipes\\ai-chat\\ai-chat.component.ts"], "sourcesContent": ["import { Component, Input, OnInit } from '@angular/core';\nimport { AiService } from 'src/app/services/ai.service';\nimport { TaskService } from 'src/app/services/task.service';\nimport { NotificationService } from 'src/app/services/notification.service';\nimport { Equipe } from 'src/app/models/equipe.model';\nimport { Task } from 'src/app/models/task.model';\nimport { finalize } from 'rxjs/operators';\n@Component({\n  selector: 'app-ai-chat',\n  templateUrl: './ai-chat.component.html',\n  styleUrls: ['./ai-chat.component.css'],\n})\nexport class AiChatComponent implements OnInit {\n  @Input() team!: Equipe;\n\n  projectTitle: string = '';\n  isGenerating: boolean = false;\n  generatedContent: any = null;\n  error: string | null = null;\n\n  // Pour le chat\n  messages: { role: 'user' | 'assistant'; content: string }[] = [];\n  userQuestion: string = '';\n  isAskingQuestion: boolean = false;\n\n  constructor(\n    private aiService: AiService,\n    private taskService: TaskService,\n    private notificationService: NotificationService\n  ) {}\n\n  ngOnInit(): void {\n    // Ajouter un message de bienvenue\n    this.messages.push({\n      role: 'assistant',\n      content:\n        'Bonjour ! Je suis votre assistant IA pour la gestion de projet. Entrez le titre de votre projet pour que je puisse vous aider à le diviser en tâches, ou posez-moi une question sur la gestion de projet.',\n    });\n  }\n\n  generateTasks(): void {\n    if (!this.projectTitle.trim()) {\n      this.notificationService.showError('Veuillez entrer un titre de projet');\n      return;\n    }\n\n    // Vérifier si l'équipe a des membres, sinon utiliser un nombre par défaut\n    let memberCount =\n      this.team && this.team.members ? this.team.members.length : 3;\n\n    // S'assurer que nous avons au moins 3 entités pour un projet significatif\n    const effectiveMemberCount = Math.max(memberCount, 3);\n\n    if (memberCount === 0) {\n      this.notificationService.showWarning(\n        \"L'équipe n'a pas de membres. Des tâches génériques seront créées.\"\n      );\n    }\n\n    this.isGenerating = true;\n    this.error = null;\n\n    console.log(\n      `Génération de tâches pour ${effectiveMemberCount} entités (équipe de ${memberCount} membres)`\n    );\n\n    // Ajouter la demande aux messages\n    this.messages.push({\n      role: 'user',\n      content: `Génère des tâches pour le projet \"${this.projectTitle}\" avec exactement ${effectiveMemberCount} entités, une pour chaque membre de l'équipe. Chaque entité doit représenter un module distinct du projet.`,\n    });\n\n    // Ajouter un message de chargement\n    const loadingMessageIndex = this.messages.length;\n    this.messages.push({\n      role: 'assistant',\n      content: 'Je génère des tâches pour votre projet...',\n    });\n\n    // Récupérer les informations sur les membres de l'équipe\n    let teamMembers: any[] = [];\n    if (this.team && this.team.members) {\n      // Utiliser les IDs des membres\n      teamMembers = this.team.members.map((memberId: string, index: number) => {\n        return { id: memberId, name: `Membre ${index + 1}`, role: 'membre' };\n      });\n\n      console.log(\"Informations sur les membres passées à l'IA:\", teamMembers);\n    }\n\n    this.aiService\n      .generateProjectTasks(this.projectTitle, memberCount, teamMembers)\n      .pipe(finalize(() => (this.isGenerating = false)))\n      .subscribe({\n        next: (result: any) => {\n          if (!result || !result.entities || result.entities.length === 0) {\n            console.error(\"Résultat invalide reçu de l'API:\", result);\n            this.handleGenerationError(\n              loadingMessageIndex,\n              'Format de réponse invalide'\n            );\n            return;\n          }\n\n          this.generatedContent = result;\n\n          // Remplacer le message de chargement par la réponse\n          this.messages[loadingMessageIndex] = {\n            role: 'assistant',\n            content: `J'ai généré ${\n              result.entities.length\n            } entités pour votre projet \"${\n              result.projectTitle\n            }\" avec un total de ${this.countTasks(result)} tâches.`,\n          };\n\n          this.notificationService.showSuccess('Tâches générées avec succès');\n        },\n        error: (error: any) => {\n          console.error('Erreur lors de la génération des tâches:', error);\n          this.handleGenerationError(\n            loadingMessageIndex,\n            error.message || 'Erreur inconnue'\n          );\n        },\n      });\n  }\n\n  // Méthode pour gérer les erreurs de génération\n  private handleGenerationError(\n    messageIndex: number,\n    errorDetails: string\n  ): void {\n    this.error = 'Impossible de générer les tâches. Veuillez réessayer.';\n\n    // Remplacer le message de chargement par le message d'erreur\n    this.messages[messageIndex] = {\n      role: 'assistant',\n      content:\n        \"Désolé, je n'ai pas pu générer les tâches. Veuillez réessayer avec un titre de projet différent.\",\n    };\n\n    this.notificationService.showError(\n      'Erreur lors de la génération des tâches: ' + errorDetails\n    );\n  }\n\n  askQuestion(): void {\n    if (!this.userQuestion.trim()) {\n      return;\n    }\n\n    const question = this.userQuestion.trim();\n    this.userQuestion = '';\n    this.isAskingQuestion = true;\n\n    // Ajouter la question aux messages\n    this.messages.push({\n      role: 'user',\n      content: question,\n    });\n\n    const projectContext = {\n      title:\n        this.projectTitle ||\n        (this.generatedContent ? this.generatedContent.projectTitle : ''),\n      description:\n        \"Projet géré par l'équipe \" + (this.team ? this.team.name : ''),\n    };\n\n    this.aiService\n      .askProjectQuestion(question, projectContext)\n      .pipe(finalize(() => (this.isAskingQuestion = false)))\n      .subscribe({\n        next: (response: string) => {\n          // Ajouter la réponse aux messages\n          this.messages.push({\n            role: 'assistant',\n            content: response,\n          });\n        },\n        error: (error: any) => {\n          console.error(\"Erreur lors de la demande à l'IA:\", error);\n\n          // Ajouter l'erreur aux messages\n          this.messages.push({\n            role: 'assistant',\n            content:\n              \"Désolé, je n'ai pas pu répondre à votre question. Veuillez réessayer.\",\n          });\n\n          this.notificationService.showError(\n            \"Erreur lors de la communication avec l'IA\"\n          );\n        },\n      });\n  }\n\n  createTasks(): void {\n    if (!this.generatedContent || !this.team || !this.team._id) {\n      this.notificationService.showError(\n        'Aucune tâche générée ou équipe invalide'\n      );\n      return;\n    }\n\n    let createdCount = 0;\n    const totalTasks = this.countTasks(this.generatedContent);\n\n    // Vérifier si l'équipe a des membres\n    if (!this.team.members || this.team.members.length === 0) {\n      this.notificationService.showError(\n        \"L'équipe n'a pas de membres pour assigner les tâches\"\n      );\n      return;\n    }\n\n    // Préparer la liste des membres de l'équipe\n    const teamMembers = this.team.members.map((member) => {\n      return typeof member === 'string' ? member : (member as any).userId;\n    });\n\n    // Créer un mapping des noms de membres vers leurs IDs\n    const memberNameToIdMap: { [key: string]: string } = {};\n    teamMembers.forEach((memberId, index) => {\n      memberNameToIdMap[`Membre ${index + 1}`] = memberId;\n    });\n\n    console.log(\n      \"Membres de l'équipe disponibles pour l'assignation:\",\n      teamMembers\n    );\n    console.log(\n      'Mapping des noms de membres vers leurs IDs:',\n      memberNameToIdMap\n    );\n\n    // Pour chaque entité\n    this.generatedContent.entities.forEach((entity: any) => {\n      // Déterminer le membre assigné à cette entité\n      let assignedMemberId: string | undefined;\n\n      // Si l'IA a suggéré une assignation\n      if (entity.assignedTo) {\n        // Essayer de trouver l'ID du membre à partir du nom suggéré\n        const memberName = entity.assignedTo;\n        if (memberNameToIdMap[memberName]) {\n          assignedMemberId = memberNameToIdMap[memberName];\n          console.log(\n            `Assignation suggérée par l'IA: Entité \"${entity.name}\" assignée à \"${memberName}\" (ID: ${assignedMemberId})`\n          );\n        } else {\n          // Si le nom n'est pas trouvé, assigner aléatoirement\n          const randomMemberIndex = Math.floor(\n            Math.random() * teamMembers.length\n          );\n          assignedMemberId = teamMembers[randomMemberIndex];\n          console.log(\n            `Nom de membre \"${memberName}\" non trouvé, assignation aléatoire à l'index ${randomMemberIndex}`\n          );\n        }\n      } else {\n        // Si pas d'assignation suggérée, assigner aléatoirement\n        const randomMemberIndex = Math.floor(\n          Math.random() * teamMembers.length\n        );\n        assignedMemberId = teamMembers[randomMemberIndex];\n        console.log(\n          `Pas d'assignation suggérée, assignation aléatoire à l'index ${randomMemberIndex}`\n        );\n      }\n\n      // Pour chaque tâche dans l'entité\n      entity.tasks.forEach((taskData: any) => {\n        const task: Task = {\n          title: taskData.title,\n          description: `[${entity.name}] ${taskData.description}`,\n          status: taskData.status || 'todo',\n          priority: taskData.priority || 'medium',\n          teamId: this.team._id || '',\n          // Utiliser l'ID du membre assigné à l'entité\n          assignedTo: assignedMemberId,\n        };\n\n        this.taskService.createTask(task).subscribe({\n          next: () => {\n            createdCount++;\n            if (createdCount === totalTasks) {\n              this.notificationService.showSuccess(\n                `${createdCount} tâches créées avec succès et assignées aux membres de l'équipe`\n              );\n              // Réinitialiser après création\n              this.generatedContent = null;\n              this.projectTitle = '';\n            }\n          },\n          error: (error) => {\n            console.error('Erreur lors de la création de la tâche:', error);\n            this.notificationService.showError(\n              'Erreur lors de la création des tâches'\n            );\n          },\n        });\n      });\n    });\n  }\n\n  countTasks(content: any): number {\n    if (!content || !content.entities) return 0;\n\n    return content.entities.reduce((total: number, entity: any) => {\n      return total + (entity.tasks ? entity.tasks.length : 0);\n    }, 0);\n  }\n\n  // Méthode pour obtenir un dégradé de couleur basé sur l'index\n  getGradientForIndex(index: number): string {\n    // Liste de dégradés prédéfinis\n    const gradients = [\n      'linear-gradient(45deg, #007bff, #6610f2)', // Bleu-Violet\n      'linear-gradient(45deg, #11998e, #38ef7d)', // Vert\n      'linear-gradient(45deg, #FC5C7D, #6A82FB)', // Rose-Bleu\n      'linear-gradient(45deg, #FF8008, #FFC837)', // Orange-Jaune\n      'linear-gradient(45deg, #8E2DE2, #4A00E0)', // Violet\n      'linear-gradient(45deg, #2193b0, #6dd5ed)', // Bleu clair\n      'linear-gradient(45deg, #373B44, #4286f4)', // Gris-Bleu\n      'linear-gradient(45deg, #834d9b, #d04ed6)', // Violet-Rose\n      'linear-gradient(45deg, #0cebeb, #20e3b2, #29ffc6)', // Turquoise\n    ];\n\n    // Utiliser le modulo pour s'assurer que nous ne dépassons pas le tableau\n    return gradients[index % gradients.length];\n  }\n\n  // Méthode pour obtenir une couleur unique basée sur l'index\n  getColorForIndex(index: number): string {\n    // Liste de couleurs prédéfinies\n    const colors = [\n      '#007bff', // Bleu\n      '#11998e', // Vert\n      '#FC5C7D', // Rose\n      '#FF8008', // Orange\n      '#8E2DE2', // Violet\n      '#2193b0', // Bleu clair\n      '#373B44', // Gris foncé\n      '#834d9b', // Violet\n      '#0cebeb', // Turquoise\n    ];\n\n    // Utiliser le modulo pour s'assurer que nous ne dépassons pas le tableau\n    return colors[index % colors.length];\n  }\n\n  // Méthode pour obtenir une icône en fonction du nom du module\n  getIconForModule(moduleName: string): string {\n    // Convertir le nom du module en minuscules pour faciliter la comparaison\n    const name = moduleName.toLowerCase();\n\n    // Mapper les noms de modules courants à des icônes Bootstrap\n    if (\n      name.includes('crud') ||\n      name.includes('api') ||\n      name.includes('données') ||\n      name.includes('base')\n    ) {\n      return 'bi-database-fill';\n    } else if (\n      name.includes('interface') ||\n      name.includes('ui') ||\n      name.includes('front') ||\n      name.includes('utilisateur')\n    ) {\n      return 'bi-window';\n    } else if (\n      name.includes('déploiement') ||\n      name.includes('serveur') ||\n      name.includes('cloud')\n    ) {\n      return 'bi-cloud-arrow-up-fill';\n    } else if (\n      name.includes('test') ||\n      name.includes('qualité') ||\n      name.includes('qa')\n    ) {\n      return 'bi-bug-fill';\n    } else if (name.includes('sécurité') || name.includes('auth')) {\n      return 'bi-shield-lock-fill';\n    } else if (name.includes('paiement') || name.includes('transaction')) {\n      return 'bi-credit-card-fill';\n    } else if (\n      name.includes('utilisateur') ||\n      name.includes('user') ||\n      name.includes('profil')\n    ) {\n      return 'bi-person-fill';\n    } else if (name.includes('doc') || name.includes('documentation')) {\n      return 'bi-file-text-fill';\n    } else if (name.includes('mobile') || name.includes('app')) {\n      return 'bi-phone-fill';\n    } else if (name.includes('backend') || name.includes('serveur')) {\n      return 'bi-server';\n    } else if (\n      name.includes('analytics') ||\n      name.includes('statistique') ||\n      name.includes('seo')\n    ) {\n      return 'bi-graph-up';\n    }\n\n    // Icône par défaut si aucune correspondance n'est trouvée\n    return 'bi-code-square';\n  }\n\n  // Méthode pour obtenir l'heure actuelle au format HH:MM\n  getCurrentTime(): string {\n    const now = new Date();\n    const hours = now.getHours().toString().padStart(2, '0');\n    const minutes = now.getMinutes().toString().padStart(2, '0');\n    return `${hours}:${minutes}`;\n  }\n}\n"], "mappings": ";AAAA,SAASA,SAAS,EAAEC,KAAK,QAAgB,eAAe;AAMxD,SAASC,QAAQ,QAAQ,gBAAgB;AAMlC,WAAMC,eAAe,GAArB,MAAMA,eAAe;EAa1BC,YACUC,SAAoB,EACpBC,WAAwB,EACxBC,mBAAwC;IAFxC,KAAAF,SAAS,GAATA,SAAS;IACT,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,mBAAmB,GAAnBA,mBAAmB;IAb7B,KAAAC,YAAY,GAAW,EAAE;IACzB,KAAAC,YAAY,GAAY,KAAK;IAC7B,KAAAC,gBAAgB,GAAQ,IAAI;IAC5B,KAAAC,KAAK,GAAkB,IAAI;IAE3B;IACA,KAAAC,QAAQ,GAAsD,EAAE;IAChE,KAAAC,YAAY,GAAW,EAAE;IACzB,KAAAC,gBAAgB,GAAY,KAAK;EAM9B;EAEHC,QAAQA,CAAA;IACN;IACA,IAAI,CAACH,QAAQ,CAACI,IAAI,CAAC;MACjBC,IAAI,EAAE,WAAW;MACjBC,OAAO,EACL;KACH,CAAC;EACJ;EAEAC,aAAaA,CAAA;IACX,IAAI,CAAC,IAAI,CAACX,YAAY,CAACY,IAAI,EAAE,EAAE;MAC7B,IAAI,CAACb,mBAAmB,CAACc,SAAS,CAAC,oCAAoC,CAAC;MACxE;;IAGF;IACA,IAAIC,WAAW,GACb,IAAI,CAACC,IAAI,IAAI,IAAI,CAACA,IAAI,CAACC,OAAO,GAAG,IAAI,CAACD,IAAI,CAACC,OAAO,CAACC,MAAM,GAAG,CAAC;IAE/D;IACA,MAAMC,oBAAoB,GAAGC,IAAI,CAACC,GAAG,CAACN,WAAW,EAAE,CAAC,CAAC;IAErD,IAAIA,WAAW,KAAK,CAAC,EAAE;MACrB,IAAI,CAACf,mBAAmB,CAACsB,WAAW,CAClC,mEAAmE,CACpE;;IAGH,IAAI,CAACpB,YAAY,GAAG,IAAI;IACxB,IAAI,CAACE,KAAK,GAAG,IAAI;IAEjBmB,OAAO,CAACC,GAAG,CACT,6BAA6BL,oBAAoB,uBAAuBJ,WAAW,WAAW,CAC/F;IAED;IACA,IAAI,CAACV,QAAQ,CAACI,IAAI,CAAC;MACjBC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,qCAAqC,IAAI,CAACV,YAAY,qBAAqBkB,oBAAoB;KACzG,CAAC;IAEF;IACA,MAAMM,mBAAmB,GAAG,IAAI,CAACpB,QAAQ,CAACa,MAAM;IAChD,IAAI,CAACb,QAAQ,CAACI,IAAI,CAAC;MACjBC,IAAI,EAAE,WAAW;MACjBC,OAAO,EAAE;KACV,CAAC;IAEF;IACA,IAAIe,WAAW,GAAU,EAAE;IAC3B,IAAI,IAAI,CAACV,IAAI,IAAI,IAAI,CAACA,IAAI,CAACC,OAAO,EAAE;MAClC;MACAS,WAAW,GAAG,IAAI,CAACV,IAAI,CAACC,OAAO,CAACU,GAAG,CAAC,CAACC,QAAgB,EAAEC,KAAa,KAAI;QACtE,OAAO;UAAEC,EAAE,EAAEF,QAAQ;UAAEG,IAAI,EAAE,UAAUF,KAAK,GAAG,CAAC,EAAE;UAAEnB,IAAI,EAAE;QAAQ,CAAE;MACtE,CAAC,CAAC;MAEFa,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEE,WAAW,CAAC;;IAG1E,IAAI,CAAC5B,SAAS,CACXkC,oBAAoB,CAAC,IAAI,CAAC/B,YAAY,EAAEc,WAAW,EAAEW,WAAW,CAAC,CACjEO,IAAI,CAACtC,QAAQ,CAAC,MAAO,IAAI,CAACO,YAAY,GAAG,KAAM,CAAC,CAAC,CACjDgC,SAAS,CAAC;MACTC,IAAI,EAAGC,MAAW,IAAI;QACpB,IAAI,CAACA,MAAM,IAAI,CAACA,MAAM,CAACC,QAAQ,IAAID,MAAM,CAACC,QAAQ,CAACnB,MAAM,KAAK,CAAC,EAAE;UAC/DK,OAAO,CAACnB,KAAK,CAAC,kCAAkC,EAAEgC,MAAM,CAAC;UACzD,IAAI,CAACE,qBAAqB,CACxBb,mBAAmB,EACnB,4BAA4B,CAC7B;UACD;;QAGF,IAAI,CAACtB,gBAAgB,GAAGiC,MAAM;QAE9B;QACA,IAAI,CAAC/B,QAAQ,CAACoB,mBAAmB,CAAC,GAAG;UACnCf,IAAI,EAAE,WAAW;UACjBC,OAAO,EAAE,eACPyB,MAAM,CAACC,QAAQ,CAACnB,MAClB,+BACEkB,MAAM,CAACnC,YACT,sBAAsB,IAAI,CAACsC,UAAU,CAACH,MAAM,CAAC;SAC9C;QAED,IAAI,CAACpC,mBAAmB,CAACwC,WAAW,CAAC,6BAA6B,CAAC;MACrE,CAAC;MACDpC,KAAK,EAAGA,KAAU,IAAI;QACpBmB,OAAO,CAACnB,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;QAChE,IAAI,CAACkC,qBAAqB,CACxBb,mBAAmB,EACnBrB,KAAK,CAACqC,OAAO,IAAI,iBAAiB,CACnC;MACH;KACD,CAAC;EACN;EAEA;EACQH,qBAAqBA,CAC3BI,YAAoB,EACpBC,YAAoB;IAEpB,IAAI,CAACvC,KAAK,GAAG,uDAAuD;IAEpE;IACA,IAAI,CAACC,QAAQ,CAACqC,YAAY,CAAC,GAAG;MAC5BhC,IAAI,EAAE,WAAW;MACjBC,OAAO,EACL;KACH;IAED,IAAI,CAACX,mBAAmB,CAACc,SAAS,CAChC,2CAA2C,GAAG6B,YAAY,CAC3D;EACH;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACtC,YAAY,CAACO,IAAI,EAAE,EAAE;MAC7B;;IAGF,MAAMgC,QAAQ,GAAG,IAAI,CAACvC,YAAY,CAACO,IAAI,EAAE;IACzC,IAAI,CAACP,YAAY,GAAG,EAAE;IACtB,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAE5B;IACA,IAAI,CAACF,QAAQ,CAACI,IAAI,CAAC;MACjBC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAEkC;KACV,CAAC;IAEF,MAAMC,cAAc,GAAG;MACrBC,KAAK,EACH,IAAI,CAAC9C,YAAY,KAChB,IAAI,CAACE,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAACF,YAAY,GAAG,EAAE,CAAC;MACnE+C,WAAW,EACT,2BAA2B,IAAI,IAAI,CAAChC,IAAI,GAAG,IAAI,CAACA,IAAI,CAACe,IAAI,GAAG,EAAE;KACjE;IAED,IAAI,CAACjC,SAAS,CACXmD,kBAAkB,CAACJ,QAAQ,EAAEC,cAAc,CAAC,CAC5Cb,IAAI,CAACtC,QAAQ,CAAC,MAAO,IAAI,CAACY,gBAAgB,GAAG,KAAM,CAAC,CAAC,CACrD2B,SAAS,CAAC;MACTC,IAAI,EAAGe,QAAgB,IAAI;QACzB;QACA,IAAI,CAAC7C,QAAQ,CAACI,IAAI,CAAC;UACjBC,IAAI,EAAE,WAAW;UACjBC,OAAO,EAAEuC;SACV,CAAC;MACJ,CAAC;MACD9C,KAAK,EAAGA,KAAU,IAAI;QACpBmB,OAAO,CAACnB,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QAEzD;QACA,IAAI,CAACC,QAAQ,CAACI,IAAI,CAAC;UACjBC,IAAI,EAAE,WAAW;UACjBC,OAAO,EACL;SACH,CAAC;QAEF,IAAI,CAACX,mBAAmB,CAACc,SAAS,CAChC,2CAA2C,CAC5C;MACH;KACD,CAAC;EACN;EAEAqC,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAAChD,gBAAgB,IAAI,CAAC,IAAI,CAACa,IAAI,IAAI,CAAC,IAAI,CAACA,IAAI,CAACoC,GAAG,EAAE;MAC1D,IAAI,CAACpD,mBAAmB,CAACc,SAAS,CAChC,yCAAyC,CAC1C;MACD;;IAGF,IAAIuC,YAAY,GAAG,CAAC;IACpB,MAAMC,UAAU,GAAG,IAAI,CAACf,UAAU,CAAC,IAAI,CAACpC,gBAAgB,CAAC;IAEzD;IACA,IAAI,CAAC,IAAI,CAACa,IAAI,CAACC,OAAO,IAAI,IAAI,CAACD,IAAI,CAACC,OAAO,CAACC,MAAM,KAAK,CAAC,EAAE;MACxD,IAAI,CAAClB,mBAAmB,CAACc,SAAS,CAChC,sDAAsD,CACvD;MACD;;IAGF;IACA,MAAMY,WAAW,GAAG,IAAI,CAACV,IAAI,CAACC,OAAO,CAACU,GAAG,CAAE4B,MAAM,IAAI;MACnD,OAAO,OAAOA,MAAM,KAAK,QAAQ,GAAGA,MAAM,GAAIA,MAAc,CAACC,MAAM;IACrE,CAAC,CAAC;IAEF;IACA,MAAMC,iBAAiB,GAA8B,EAAE;IACvD/B,WAAW,CAACgC,OAAO,CAAC,CAAC9B,QAAQ,EAAEC,KAAK,KAAI;MACtC4B,iBAAiB,CAAC,UAAU5B,KAAK,GAAG,CAAC,EAAE,CAAC,GAAGD,QAAQ;IACrD,CAAC,CAAC;IAEFL,OAAO,CAACC,GAAG,CACT,qDAAqD,EACrDE,WAAW,CACZ;IACDH,OAAO,CAACC,GAAG,CACT,6CAA6C,EAC7CiC,iBAAiB,CAClB;IAED;IACA,IAAI,CAACtD,gBAAgB,CAACkC,QAAQ,CAACqB,OAAO,CAAEC,MAAW,IAAI;MACrD;MACA,IAAIC,gBAAoC;MAExC;MACA,IAAID,MAAM,CAACE,UAAU,EAAE;QACrB;QACA,MAAMC,UAAU,GAAGH,MAAM,CAACE,UAAU;QACpC,IAAIJ,iBAAiB,CAACK,UAAU,CAAC,EAAE;UACjCF,gBAAgB,GAAGH,iBAAiB,CAACK,UAAU,CAAC;UAChDvC,OAAO,CAACC,GAAG,CACT,0CAA0CmC,MAAM,CAAC5B,IAAI,iBAAiB+B,UAAU,UAAUF,gBAAgB,GAAG,CAC9G;SACF,MAAM;UACL;UACA,MAAMG,iBAAiB,GAAG3C,IAAI,CAAC4C,KAAK,CAClC5C,IAAI,CAAC6C,MAAM,EAAE,GAAGvC,WAAW,CAACR,MAAM,CACnC;UACD0C,gBAAgB,GAAGlC,WAAW,CAACqC,iBAAiB,CAAC;UACjDxC,OAAO,CAACC,GAAG,CACT,kBAAkBsC,UAAU,iDAAiDC,iBAAiB,EAAE,CACjG;;OAEJ,MAAM;QACL;QACA,MAAMA,iBAAiB,GAAG3C,IAAI,CAAC4C,KAAK,CAClC5C,IAAI,CAAC6C,MAAM,EAAE,GAAGvC,WAAW,CAACR,MAAM,CACnC;QACD0C,gBAAgB,GAAGlC,WAAW,CAACqC,iBAAiB,CAAC;QACjDxC,OAAO,CAACC,GAAG,CACT,+DAA+DuC,iBAAiB,EAAE,CACnF;;MAGH;MACAJ,MAAM,CAACO,KAAK,CAACR,OAAO,CAAES,QAAa,IAAI;QACrC,MAAMC,IAAI,GAAS;UACjBrB,KAAK,EAAEoB,QAAQ,CAACpB,KAAK;UACrBC,WAAW,EAAE,IAAIW,MAAM,CAAC5B,IAAI,KAAKoC,QAAQ,CAACnB,WAAW,EAAE;UACvDqB,MAAM,EAAEF,QAAQ,CAACE,MAAM,IAAI,MAAM;UACjCC,QAAQ,EAAEH,QAAQ,CAACG,QAAQ,IAAI,QAAQ;UACvCC,MAAM,EAAE,IAAI,CAACvD,IAAI,CAACoC,GAAG,IAAI,EAAE;UAC3B;UACAS,UAAU,EAAED;SACb;QAED,IAAI,CAAC7D,WAAW,CAACyE,UAAU,CAACJ,IAAI,CAAC,CAAClC,SAAS,CAAC;UAC1CC,IAAI,EAAEA,CAAA,KAAK;YACTkB,YAAY,EAAE;YACd,IAAIA,YAAY,KAAKC,UAAU,EAAE;cAC/B,IAAI,CAACtD,mBAAmB,CAACwC,WAAW,CAClC,GAAGa,YAAY,iEAAiE,CACjF;cACD;cACA,IAAI,CAAClD,gBAAgB,GAAG,IAAI;cAC5B,IAAI,CAACF,YAAY,GAAG,EAAE;;UAE1B,CAAC;UACDG,KAAK,EAAGA,KAAK,IAAI;YACfmB,OAAO,CAACnB,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;YAC/D,IAAI,CAACJ,mBAAmB,CAACc,SAAS,CAChC,uCAAuC,CACxC;UACH;SACD,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEAyB,UAAUA,CAAC5B,OAAY;IACrB,IAAI,CAACA,OAAO,IAAI,CAACA,OAAO,CAAC0B,QAAQ,EAAE,OAAO,CAAC;IAE3C,OAAO1B,OAAO,CAAC0B,QAAQ,CAACoC,MAAM,CAAC,CAACC,KAAa,EAAEf,MAAW,KAAI;MAC5D,OAAOe,KAAK,IAAIf,MAAM,CAACO,KAAK,GAAGP,MAAM,CAACO,KAAK,CAAChD,MAAM,GAAG,CAAC,CAAC;IACzD,CAAC,EAAE,CAAC,CAAC;EACP;EAEA;EACAyD,mBAAmBA,CAAC9C,KAAa;IAC/B;IACA,MAAM+C,SAAS,GAAG,CAChB,0CAA0C,EAC1C,0CAA0C,EAC1C,0CAA0C,EAC1C,0CAA0C,EAC1C,0CAA0C,EAC1C,0CAA0C,EAC1C,0CAA0C,EAC1C,0CAA0C,EAC1C,mDAAmD,CAAE;IAAA,CACtD;IAED;IACA,OAAOA,SAAS,CAAC/C,KAAK,GAAG+C,SAAS,CAAC1D,MAAM,CAAC;EAC5C;EAEA;EACA2D,gBAAgBA,CAAChD,KAAa;IAC5B;IACA,MAAMiD,MAAM,GAAG,CACb,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CAAE;IAAA,CACZ;IAED;IACA,OAAOA,MAAM,CAACjD,KAAK,GAAGiD,MAAM,CAAC5D,MAAM,CAAC;EACtC;EAEA;EACA6D,gBAAgBA,CAACC,UAAkB;IACjC;IACA,MAAMjD,IAAI,GAAGiD,UAAU,CAACC,WAAW,EAAE;IAErC;IACA,IACElD,IAAI,CAACmD,QAAQ,CAAC,MAAM,CAAC,IACrBnD,IAAI,CAACmD,QAAQ,CAAC,KAAK,CAAC,IACpBnD,IAAI,CAACmD,QAAQ,CAAC,SAAS,CAAC,IACxBnD,IAAI,CAACmD,QAAQ,CAAC,MAAM,CAAC,EACrB;MACA,OAAO,kBAAkB;KAC1B,MAAM,IACLnD,IAAI,CAACmD,QAAQ,CAAC,WAAW,CAAC,IAC1BnD,IAAI,CAACmD,QAAQ,CAAC,IAAI,CAAC,IACnBnD,IAAI,CAACmD,QAAQ,CAAC,OAAO,CAAC,IACtBnD,IAAI,CAACmD,QAAQ,CAAC,aAAa,CAAC,EAC5B;MACA,OAAO,WAAW;KACnB,MAAM,IACLnD,IAAI,CAACmD,QAAQ,CAAC,aAAa,CAAC,IAC5BnD,IAAI,CAACmD,QAAQ,CAAC,SAAS,CAAC,IACxBnD,IAAI,CAACmD,QAAQ,CAAC,OAAO,CAAC,EACtB;MACA,OAAO,wBAAwB;KAChC,MAAM,IACLnD,IAAI,CAACmD,QAAQ,CAAC,MAAM,CAAC,IACrBnD,IAAI,CAACmD,QAAQ,CAAC,SAAS,CAAC,IACxBnD,IAAI,CAACmD,QAAQ,CAAC,IAAI,CAAC,EACnB;MACA,OAAO,aAAa;KACrB,MAAM,IAAInD,IAAI,CAACmD,QAAQ,CAAC,UAAU,CAAC,IAAInD,IAAI,CAACmD,QAAQ,CAAC,MAAM,CAAC,EAAE;MAC7D,OAAO,qBAAqB;KAC7B,MAAM,IAAInD,IAAI,CAACmD,QAAQ,CAAC,UAAU,CAAC,IAAInD,IAAI,CAACmD,QAAQ,CAAC,aAAa,CAAC,EAAE;MACpE,OAAO,qBAAqB;KAC7B,MAAM,IACLnD,IAAI,CAACmD,QAAQ,CAAC,aAAa,CAAC,IAC5BnD,IAAI,CAACmD,QAAQ,CAAC,MAAM,CAAC,IACrBnD,IAAI,CAACmD,QAAQ,CAAC,QAAQ,CAAC,EACvB;MACA,OAAO,gBAAgB;KACxB,MAAM,IAAInD,IAAI,CAACmD,QAAQ,CAAC,KAAK,CAAC,IAAInD,IAAI,CAACmD,QAAQ,CAAC,eAAe,CAAC,EAAE;MACjE,OAAO,mBAAmB;KAC3B,MAAM,IAAInD,IAAI,CAACmD,QAAQ,CAAC,QAAQ,CAAC,IAAInD,IAAI,CAACmD,QAAQ,CAAC,KAAK,CAAC,EAAE;MAC1D,OAAO,eAAe;KACvB,MAAM,IAAInD,IAAI,CAACmD,QAAQ,CAAC,SAAS,CAAC,IAAInD,IAAI,CAACmD,QAAQ,CAAC,SAAS,CAAC,EAAE;MAC/D,OAAO,WAAW;KACnB,MAAM,IACLnD,IAAI,CAACmD,QAAQ,CAAC,WAAW,CAAC,IAC1BnD,IAAI,CAACmD,QAAQ,CAAC,aAAa,CAAC,IAC5BnD,IAAI,CAACmD,QAAQ,CAAC,KAAK,CAAC,EACpB;MACA,OAAO,aAAa;;IAGtB;IACA,OAAO,gBAAgB;EACzB;EAEA;EACAC,cAAcA,CAAA;IACZ,MAAMC,GAAG,GAAG,IAAIC,IAAI,EAAE;IACtB,MAAMC,KAAK,GAAGF,GAAG,CAACG,QAAQ,EAAE,CAACC,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACxD,MAAMC,OAAO,GAAGN,GAAG,CAACO,UAAU,EAAE,CAACH,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC5D,OAAO,GAAGH,KAAK,IAAII,OAAO,EAAE;EAC9B;CACD;AAvZUE,UAAA,EAARlG,KAAK,EAAE,C,4CAAe;AADZE,eAAe,GAAAgG,UAAA,EAL3BnG,SAAS,CAAC;EACToG,QAAQ,EAAE,aAAa;EACvBC,WAAW,EAAE,0BAA0B;EACvCC,SAAS,EAAE,CAAC,yBAAyB;CACtC,CAAC,C,EACWnG,eAAe,CAwZ3B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}