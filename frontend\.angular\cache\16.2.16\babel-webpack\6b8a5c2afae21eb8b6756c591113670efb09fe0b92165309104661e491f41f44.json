{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nexport let EquipeDetailComponent = class EquipeDetailComponent {\n  constructor(equipeService, userService, route, router) {\n    this.equipeService = equipeService;\n    this.userService = userService;\n    this.route = route;\n    this.router = router;\n    this.equipe = null;\n    this.loading = false;\n    this.error = null;\n    this.equipeId = null;\n    this.newMembre = {\n      id: '',\n      role: 'membre'\n    };\n    this.availableUsers = [];\n    this.memberNames = {}; // Map pour stocker les noms des membres\n    this.teamMembers = []; // Liste des membres de l'équipe avec leurs détails\n  }\n\n  ngOnInit() {\n    this.equipeId = this.route.snapshot.paramMap.get('id');\n    // Charger tous les utilisateurs disponibles\n    this.loadUsers();\n    if (this.equipeId) {\n      this.loadEquipe(this.equipeId);\n    } else {\n      this.error = \"ID d'équipe non spécifié\";\n    }\n  }\n  // Méthode pour charger tous les utilisateurs\n  loadUsers() {\n    // TODO: Implémenter l'API pour récupérer les utilisateurs\n    // Pour l'instant, utiliser des données mockées\n    const mockUsers = [{\n      _id: 'user1',\n      username: 'john_doe',\n      email: '<EMAIL>',\n      role: 'admin',\n      isActive: true\n    }, {\n      _id: 'user2',\n      username: 'jane_smith',\n      email: '<EMAIL>',\n      role: 'student',\n      isActive: true\n    }, {\n      _id: 'user3',\n      username: 'bob_wilson',\n      email: '<EMAIL>',\n      role: 'teacher',\n      isActive: true\n    }];\n    // Simuler un délai d'API\n    setTimeout(() => {\n      // Stocker tous les utilisateurs pour la recherche de noms\n      const allUsers = [...mockUsers];\n      console.log('Tous les utilisateurs chargés (mock):', allUsers);\n      // Filtrer les utilisateurs disponibles (non membres de l'équipe)\n      if (this.teamMembers && this.teamMembers.length > 0) {\n        const memberUserIds = this.teamMembers.map(m => m.user);\n        this.availableUsers = mockUsers.filter(user => !memberUserIds.includes(user._id || user.id || ''));\n      } else {\n        this.availableUsers = mockUsers;\n      }\n      console.log('Utilisateurs disponibles:', this.availableUsers);\n      // Si l'équipe est déjà chargée, mettre à jour les noms des membres\n      if (this.equipe && this.equipe.members) {\n        this.updateMemberNames();\n      }\n    }, 500);\n  }\n  // Méthode pour mettre à jour les noms des membres\n  updateMemberNames() {\n    if (!this.equipe || !this.equipe.members) return;\n    this.equipe.members.forEach(membreId => {\n      const user = this.availableUsers.find(u => u._id === membreId || u.id === membreId);\n      if (user && user.name) {\n        this.memberNames[membreId] = user.name;\n      } else {\n        // Si l'utilisateur n'est pas trouvé dans la liste, essayer de le récupérer individuellement\n        // TODO: Implémenter getUser dans AuthuserService\n        // Pour l'instant, utiliser l'ID comme nom par défaut\n        this.memberNames[membreId] = membreId;\n      }\n    });\n  }\n  // Méthode pour obtenir le nom d'un membre\n  getMembreName(membreId) {\n    return this.memberNames[membreId] || membreId;\n  }\n  // Méthode pour obtenir le nom d'un utilisateur à partir de son ID\n  getUserName(userId) {\n    if (!userId) {\n      return 'Non défini';\n    }\n    const user = this.availableUsers.find(u => u._id === userId || u.id === userId);\n    if (user) {\n      if (user.firstName && user.lastName) {\n        return `${user.firstName} ${user.lastName}`;\n      } else if (user.name) {\n        return user.name;\n      }\n    }\n    return userId;\n  }\n  // Méthode pour obtenir la profession d'un utilisateur à partir de son ID\n  getUserProfession(userId) {\n    if (!userId) {\n      return '';\n    }\n    const user = this.availableUsers.find(u => u._id === userId || u.id === userId);\n    if (user) {\n      return user.profession || user.role || '';\n    }\n    return '';\n  }\n  loadEquipe(id) {\n    this.loading = true;\n    this.error = null;\n    this.equipeService.getEquipe(id).subscribe({\n      next: data => {\n        console.log(\"Détails de l'équipe chargés:\", data);\n        this.equipe = data;\n        // Charger les détails des membres de l'équipe\n        this.loadTeamMembers(id);\n        // Mettre à jour les noms des membres\n        if (this.equipe && this.equipe.members && this.equipe.members.length > 0) {\n          this.updateMemberNames();\n        }\n        this.loading = false;\n      },\n      error: error => {\n        console.error(\"Erreur lors du chargement des détails de l'équipe:\", error);\n        this.error = \"Impossible de charger les détails de l'équipe. Veuillez réessayer plus tard.\";\n        this.loading = false;\n      }\n    });\n  }\n  // Méthode pour charger les détails des membres de l'équipe\n  loadTeamMembers(teamId) {\n    this.equipeService.getTeamMembers(teamId).subscribe({\n      next: members => {\n        console.log('Détails des membres chargés:', members);\n        this.teamMembers = members;\n      },\n      error: error => {\n        console.error('Erreur lors du chargement des détails des membres:', error);\n      }\n    });\n  }\n  navigateToEditEquipe() {\n    if (this.equipeId) {\n      this.router.navigate(['/equipes/modifier', this.equipeId]);\n    }\n  }\n  navigateToEquipeList() {\n    this.router.navigate(['/equipes/liste']);\n  }\n  navigateToTasks() {\n    if (this.equipeId) {\n      this.router.navigate(['/equipes/tasks', this.equipeId]);\n    }\n  }\n  // Méthode pour formater les dates\n  formatDate(date) {\n    if (!date) {\n      return 'N/A';\n    }\n    try {\n      let dateObj;\n      if (typeof date === 'string') {\n        dateObj = new Date(date);\n      } else {\n        dateObj = date;\n      }\n      if (isNaN(dateObj.getTime())) {\n        return 'Date invalide';\n      }\n      // Format: JJ/MM/AAAA\n      return dateObj.toLocaleDateString('fr-FR', {\n        day: '2-digit',\n        month: '2-digit',\n        year: 'numeric'\n      });\n    } catch (error) {\n      console.error('Erreur lors du formatage de la date:', error);\n      return 'Erreur de date';\n    }\n  }\n  // Méthode pour ajouter un membre à l'équipe\n  addMembre(userId, role) {\n    console.log(`Ajout de l'utilisateur ${userId} avec le rôle ${role}`);\n    if (!this.equipeId || !userId) {\n      console.error(\"ID d'équipe ou ID d'utilisateur manquant\");\n      this.error = \"ID d'équipe ou ID d'utilisateur manquant\";\n      return;\n    }\n    // Vérifier si l'utilisateur est déjà membre de l'équipe\n    const isAlreadyMember = this.teamMembers.some(m => m.user === userId);\n    if (isAlreadyMember) {\n      this.error = \"Cet utilisateur est déjà membre de l'équipe\";\n      alert(\"Cet utilisateur est déjà membre de l'équipe\");\n      return;\n    }\n    // Créer l'objet membre avec le rôle spécifié\n    const membre = {\n      id: userId,\n      role: role || 'membre'\n    };\n    // Récupérer les informations de l'utilisateur pour afficher un message plus informatif\n    const userName = this.getUserName(userId);\n    const roleName = role === 'admin' ? 'administrateur' : 'membre';\n    this.equipeService.addMembreToEquipe(this.equipeId, membre).subscribe({\n      next: response => {\n        console.log(`Utilisateur \"${userName}\" ajouté comme ${roleName} avec succès:`, response);\n        // Afficher un message de succès\n        alert(`Utilisateur \"${userName}\" ajouté comme ${roleName} avec succès`);\n        // Recharger les membres de l'équipe\n        this.loadTeamMembers(this.equipeId);\n        // Recharger l'équipe pour mettre à jour la liste des membres\n        this.loadEquipe(this.equipeId);\n        // Mettre à jour la liste des utilisateurs disponibles\n        this.updateAvailableUsers();\n      },\n      error: error => {\n        console.error(\"Erreur lors de l'ajout de l'utilisateur comme membre:\", error);\n        this.error = `Impossible d'ajouter l'utilisateur \"${userName}\" comme ${roleName}. Veuillez réessayer plus tard.`;\n        alert(this.error);\n      }\n    });\n  }\n  // Méthode pour mettre à jour la liste des utilisateurs disponibles\n  updateAvailableUsers() {\n    this.userService.getUsers().subscribe({\n      next: users => {\n        // Filtrer les utilisateurs qui ne sont pas déjà membres de l'équipe\n        if (this.teamMembers && this.teamMembers.length > 0) {\n          const memberUserIds = this.teamMembers.map(m => m.user);\n          this.availableUsers = users.filter(user => !memberUserIds.includes(user._id || user.id || ''));\n        } else {\n          this.availableUsers = users;\n        }\n        console.log('Liste des utilisateurs disponibles mise à jour:', this.availableUsers);\n      },\n      error: error => {\n        console.error('Erreur lors de la mise à jour des utilisateurs disponibles:', error);\n      }\n    });\n  }\n  // Ancienne méthode maintenue pour compatibilité\n  addMembreToEquipe() {\n    if (!this.equipeId || !this.newMembre.id) {\n      console.error(\"ID d'équipe ou ID de membre manquant\");\n      return;\n    }\n    this.addMembre(this.newMembre.id, this.newMembre.role || 'membre');\n  }\n  removeMembreFromEquipe(membreId) {\n    console.log('Méthode removeMembreFromEquipe appelée avec ID:', membreId);\n    if (!this.equipeId) {\n      console.error(\"ID d'équipe manquant\");\n      this.error = \"ID d'équipe manquant. Impossible de retirer le membre.\";\n      return;\n    }\n    // Dans notre implémentation actuelle, membre._id est l'ID de l'utilisateur\n    const userId = membreId;\n    // Récupérer le nom de l'utilisateur pour un message plus informatif\n    const userName = this.getUserName(userId);\n    console.log(`Tentative de retrait de l'utilisateur ${userId} (${userName}) de l'équipe ${this.equipeId}`);\n    if (confirm(`Êtes-vous sûr de vouloir retirer l'utilisateur \"${userName}\" de l'équipe?`)) {\n      console.log('Confirmation acceptée, suppression en cours...');\n      this.loading = true;\n      this.error = null;\n      this.equipeService.removeMembreFromEquipe(this.equipeId, userId).subscribe({\n        next: response => {\n          console.log(`Utilisateur \"${userName}\" retiré avec succès de l'équipe:`, response);\n          this.loading = false;\n          // Afficher un message de succès\n          alert(`Utilisateur \"${userName}\" retiré avec succès de l'équipe`);\n          // Recharger les membres de l'équipe\n          this.loadTeamMembers(this.equipeId);\n          // Recharger l'équipe pour mettre à jour la liste des membres\n          this.loadEquipe(this.equipeId);\n          // Mettre à jour la liste des utilisateurs disponibles\n          this.updateAvailableUsers();\n        },\n        error: error => {\n          console.error(`Erreur lors du retrait de l'utilisateur \"${userName}\":`, error);\n          this.loading = false;\n          this.error = `Impossible de retirer l'utilisateur \"${userName}\" de l'équipe: ${error.message || 'Erreur inconnue'}`;\n        }\n      });\n    } else {\n      console.log(\"Suppression annulée par l'utilisateur\");\n    }\n  }\n  deleteEquipe() {\n    console.log('Méthode deleteEquipe appelée');\n    if (!this.equipeId) {\n      console.error(\"ID d'équipe manquant\");\n      this.error = \"ID d'équipe manquant. Impossible de supprimer l'équipe.\";\n      return;\n    }\n    console.log(\"ID de l'équipe à supprimer:\", this.equipeId);\n    if (confirm(`Êtes-vous sûr de vouloir supprimer l'équipe \"${this.equipe?.name}\"? Cette action est irréversible.`)) {\n      console.log('Confirmation acceptée, suppression en cours...');\n      this.loading = true;\n      this.error = null;\n      this.equipeService.deleteEquipe(this.equipeId).subscribe({\n        next: () => {\n          console.log('Équipe supprimée avec succès');\n          this.loading = false;\n          alert('Équipe supprimée avec succès');\n          this.router.navigate(['/equipes/liste']);\n        },\n        error: error => {\n          console.error(\"Erreur lors de la suppression de l'équipe:\", error);\n          this.loading = false;\n          this.error = `Impossible de supprimer l'équipe: ${error.message || 'Erreur inconnue'}`;\n          alert(`Erreur lors de la suppression: ${this.error}`);\n        }\n      });\n    } else {\n      console.log(\"Suppression annulée par l'utilisateur\");\n    }\n  }\n};\nEquipeDetailComponent = __decorate([Component({\n  selector: 'app-equipe-detail',\n  templateUrl: './equipe-detail.component.html',\n  styleUrls: ['./equipe-detail.component.css']\n})], EquipeDetailComponent);", "map": {"version": 3, "names": ["Component", "EquipeDetailComponent", "constructor", "equipeService", "userService", "route", "router", "equipe", "loading", "error", "equipeId", "newMembre", "id", "role", "availableUsers", "memberNames", "teamMembers", "ngOnInit", "snapshot", "paramMap", "get", "loadUsers", "loadEquipe", "mockUsers", "_id", "username", "email", "isActive", "setTimeout", "allUsers", "console", "log", "length", "memberUserIds", "map", "m", "user", "filter", "includes", "members", "updateMemberNames", "for<PERSON>ach", "membreId", "find", "u", "name", "getMembreName", "getUserName", "userId", "firstName", "lastName", "getUserProfession", "profession", "getEquipe", "subscribe", "next", "data", "loadTeamMembers", "teamId", "getTeamMembers", "navigateToEditEquipe", "navigate", "navigateToEquipeList", "navigateToTasks", "formatDate", "date", "date<PERSON><PERSON>j", "Date", "isNaN", "getTime", "toLocaleDateString", "day", "month", "year", "addMembre", "isAlreadyMember", "some", "alert", "membre", "userName", "<PERSON><PERSON><PERSON>", "addMembreToEquipe", "response", "updateAvailableUsers", "getUsers", "users", "removeMembreFromEquipe", "confirm", "message", "deleteEquipe", "__decorate", "selector", "templateUrl", "styleUrls"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\equipes\\equipe-detail\\equipe-detail.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { EquipeService } from 'src/app/services/equipe.service';\nimport { AuthuserService } from 'src/app/services/auth.service';\nimport { Equipe } from 'src/app/models/equipe.model';\nimport { Membre } from 'src/app/models/membre.model';\nimport { User } from 'src/app/models/user.model';\n@Component({\n  selector: 'app-equipe-detail',\n  templateUrl: './equipe-detail.component.html',\n  styleUrls: ['./equipe-detail.component.css'],\n})\nexport class EquipeDetailComponent implements OnInit {\n  equipe: Equipe | null = null;\n  loading = false;\n  error: string | null = null;\n  equipeId: string | null = null;\n  newMembre: any = { id: '', role: 'membre' };\n  availableUsers: User[] = [];\n  memberNames: { [key: string]: string } = {}; // Map pour stocker les noms des membres\n  teamMembers: any[] = []; // Liste des membres de l'équipe avec leurs détails\n\n  constructor(\n    private equipeService: EquipeService,\n    private userService: AuthuserService,\n    private route: ActivatedRoute,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    this.equipeId = this.route.snapshot.paramMap.get('id');\n\n    // Charger tous les utilisateurs disponibles\n    this.loadUsers();\n\n    if (this.equipeId) {\n      this.loadEquipe(this.equipeId);\n    } else {\n      this.error = \"ID d'équipe non spécifié\";\n    }\n  }\n\n  // Méthode pour charger tous les utilisateurs\n  loadUsers(): void {\n    // TODO: Implémenter l'API pour récupérer les utilisateurs\n    // Pour l'instant, utiliser des données mockées\n    const mockUsers: User[] = [\n      {\n        _id: 'user1',\n        username: 'john_doe',\n        email: '<EMAIL>',\n        role: 'admin',\n        isActive: true,\n      },\n      {\n        _id: 'user2',\n        username: 'jane_smith',\n        email: '<EMAIL>',\n        role: 'student',\n        isActive: true,\n      },\n      {\n        _id: 'user3',\n        username: 'bob_wilson',\n        email: '<EMAIL>',\n        role: 'teacher',\n        isActive: true,\n      },\n    ];\n\n    // Simuler un délai d'API\n    setTimeout(() => {\n      // Stocker tous les utilisateurs pour la recherche de noms\n      const allUsers = [...mockUsers];\n      console.log('Tous les utilisateurs chargés (mock):', allUsers);\n\n      // Filtrer les utilisateurs disponibles (non membres de l'équipe)\n      if (this.teamMembers && this.teamMembers.length > 0) {\n        const memberUserIds = this.teamMembers.map((m) => m.user);\n        this.availableUsers = mockUsers.filter(\n          (user) => !memberUserIds.includes(user._id || user.id || '')\n        );\n      } else {\n        this.availableUsers = mockUsers;\n      }\n\n      console.log('Utilisateurs disponibles:', this.availableUsers);\n\n      // Si l'équipe est déjà chargée, mettre à jour les noms des membres\n      if (this.equipe && this.equipe.members) {\n        this.updateMemberNames();\n      }\n    }, 500);\n  }\n\n  // Méthode pour mettre à jour les noms des membres\n  updateMemberNames(): void {\n    if (!this.equipe || !this.equipe.members) return;\n\n    this.equipe.members.forEach((membreId) => {\n      const user = this.availableUsers.find(\n        (u) => u._id === membreId || u.id === membreId\n      );\n      if (user && user.name) {\n        this.memberNames[membreId] = user.name;\n      } else {\n        // Si l'utilisateur n'est pas trouvé dans la liste, essayer de le récupérer individuellement\n        // TODO: Implémenter getUser dans AuthuserService\n        // Pour l'instant, utiliser l'ID comme nom par défaut\n        this.memberNames[membreId] = membreId;\n      }\n    });\n  }\n\n  // Méthode pour obtenir le nom d'un membre\n  getMembreName(membreId: string): string {\n    return this.memberNames[membreId] || membreId;\n  }\n\n  // Méthode pour obtenir le nom d'un utilisateur à partir de son ID\n  getUserName(userId: string | undefined): string {\n    if (!userId) {\n      return 'Non défini';\n    }\n\n    const user = this.availableUsers.find(\n      (u) => u._id === userId || u.id === userId\n    );\n    if (user) {\n      if (user.firstName && user.lastName) {\n        return `${user.firstName} ${user.lastName}`;\n      } else if (user.name) {\n        return user.name;\n      }\n    }\n    return userId;\n  }\n\n  // Méthode pour obtenir la profession d'un utilisateur à partir de son ID\n  getUserProfession(userId: string | undefined): string {\n    if (!userId) {\n      return '';\n    }\n\n    const user = this.availableUsers.find(\n      (u) => u._id === userId || u.id === userId\n    );\n    if (user) {\n      return user.profession || user.role || '';\n    }\n    return '';\n  }\n\n  loadEquipe(id: string): void {\n    this.loading = true;\n    this.error = null;\n\n    this.equipeService.getEquipe(id).subscribe({\n      next: (data) => {\n        console.log(\"Détails de l'équipe chargés:\", data);\n        this.equipe = data;\n\n        // Charger les détails des membres de l'équipe\n        this.loadTeamMembers(id);\n\n        // Mettre à jour les noms des membres\n        if (\n          this.equipe &&\n          this.equipe.members &&\n          this.equipe.members.length > 0\n        ) {\n          this.updateMemberNames();\n        }\n\n        this.loading = false;\n      },\n      error: (error) => {\n        console.error(\n          \"Erreur lors du chargement des détails de l'équipe:\",\n          error\n        );\n        this.error =\n          \"Impossible de charger les détails de l'équipe. Veuillez réessayer plus tard.\";\n        this.loading = false;\n      },\n    });\n  }\n\n  // Méthode pour charger les détails des membres de l'équipe\n  loadTeamMembers(teamId: string): void {\n    this.equipeService.getTeamMembers(teamId).subscribe({\n      next: (members) => {\n        console.log('Détails des membres chargés:', members);\n        this.teamMembers = members;\n      },\n      error: (error) => {\n        console.error(\n          'Erreur lors du chargement des détails des membres:',\n          error\n        );\n      },\n    });\n  }\n\n  navigateToEditEquipe(): void {\n    if (this.equipeId) {\n      this.router.navigate(['/equipes/modifier', this.equipeId]);\n    }\n  }\n\n  navigateToEquipeList(): void {\n    this.router.navigate(['/equipes/liste']);\n  }\n\n  navigateToTasks(): void {\n    if (this.equipeId) {\n      this.router.navigate(['/equipes/tasks', this.equipeId]);\n    }\n  }\n\n  // Méthode pour formater les dates\n  formatDate(date: Date | string | undefined): string {\n    if (!date) {\n      return 'N/A';\n    }\n\n    try {\n      let dateObj: Date;\n\n      if (typeof date === 'string') {\n        dateObj = new Date(date);\n      } else {\n        dateObj = date;\n      }\n\n      if (isNaN(dateObj.getTime())) {\n        return 'Date invalide';\n      }\n\n      // Format: JJ/MM/AAAA\n      return dateObj.toLocaleDateString('fr-FR', {\n        day: '2-digit',\n        month: '2-digit',\n        year: 'numeric',\n      });\n    } catch (error) {\n      console.error('Erreur lors du formatage de la date:', error);\n      return 'Erreur de date';\n    }\n  }\n\n  // Méthode pour ajouter un membre à l'équipe\n  addMembre(userId: string, role: string): void {\n    console.log(`Ajout de l'utilisateur ${userId} avec le rôle ${role}`);\n\n    if (!this.equipeId || !userId) {\n      console.error(\"ID d'équipe ou ID d'utilisateur manquant\");\n      this.error = \"ID d'équipe ou ID d'utilisateur manquant\";\n      return;\n    }\n\n    // Vérifier si l'utilisateur est déjà membre de l'équipe\n    const isAlreadyMember = this.teamMembers.some((m) => m.user === userId);\n    if (isAlreadyMember) {\n      this.error = \"Cet utilisateur est déjà membre de l'équipe\";\n      alert(\"Cet utilisateur est déjà membre de l'équipe\");\n      return;\n    }\n\n    // Créer l'objet membre avec le rôle spécifié\n    const membre: Membre = {\n      id: userId,\n      role: role || 'membre',\n    };\n\n    // Récupérer les informations de l'utilisateur pour afficher un message plus informatif\n    const userName = this.getUserName(userId);\n    const roleName = role === 'admin' ? 'administrateur' : 'membre';\n\n    this.equipeService.addMembreToEquipe(this.equipeId, membre).subscribe({\n      next: (response) => {\n        console.log(\n          `Utilisateur \"${userName}\" ajouté comme ${roleName} avec succès:`,\n          response\n        );\n\n        // Afficher un message de succès\n        alert(`Utilisateur \"${userName}\" ajouté comme ${roleName} avec succès`);\n\n        // Recharger les membres de l'équipe\n        this.loadTeamMembers(this.equipeId!);\n\n        // Recharger l'équipe pour mettre à jour la liste des membres\n        this.loadEquipe(this.equipeId!);\n\n        // Mettre à jour la liste des utilisateurs disponibles\n        this.updateAvailableUsers();\n      },\n      error: (error) => {\n        console.error(\n          \"Erreur lors de l'ajout de l'utilisateur comme membre:\",\n          error\n        );\n        this.error = `Impossible d'ajouter l'utilisateur \"${userName}\" comme ${roleName}. Veuillez réessayer plus tard.`;\n        alert(this.error);\n      },\n    });\n  }\n\n  // Méthode pour mettre à jour la liste des utilisateurs disponibles\n  updateAvailableUsers(): void {\n    this.userService.getUsers().subscribe({\n      next: (users) => {\n        // Filtrer les utilisateurs qui ne sont pas déjà membres de l'équipe\n        if (this.teamMembers && this.teamMembers.length > 0) {\n          const memberUserIds = this.teamMembers.map((m) => m.user);\n          this.availableUsers = users.filter(\n            (user) => !memberUserIds.includes(user._id || user.id || '')\n          );\n        } else {\n          this.availableUsers = users;\n        }\n\n        console.log(\n          'Liste des utilisateurs disponibles mise à jour:',\n          this.availableUsers\n        );\n      },\n      error: (error) => {\n        console.error(\n          'Erreur lors de la mise à jour des utilisateurs disponibles:',\n          error\n        );\n      },\n    });\n  }\n\n  // Ancienne méthode maintenue pour compatibilité\n  addMembreToEquipe(): void {\n    if (!this.equipeId || !this.newMembre.id) {\n      console.error(\"ID d'équipe ou ID de membre manquant\");\n      return;\n    }\n\n    this.addMembre(this.newMembre.id, this.newMembre.role || 'membre');\n  }\n\n  removeMembreFromEquipe(membreId: string): void {\n    console.log('Méthode removeMembreFromEquipe appelée avec ID:', membreId);\n\n    if (!this.equipeId) {\n      console.error(\"ID d'équipe manquant\");\n      this.error = \"ID d'équipe manquant. Impossible de retirer le membre.\";\n      return;\n    }\n\n    // Dans notre implémentation actuelle, membre._id est l'ID de l'utilisateur\n    const userId = membreId;\n\n    // Récupérer le nom de l'utilisateur pour un message plus informatif\n    const userName = this.getUserName(userId);\n\n    console.log(\n      `Tentative de retrait de l'utilisateur ${userId} (${userName}) de l'équipe ${this.equipeId}`\n    );\n\n    if (\n      confirm(\n        `Êtes-vous sûr de vouloir retirer l'utilisateur \"${userName}\" de l'équipe?`\n      )\n    ) {\n      console.log('Confirmation acceptée, suppression en cours...');\n\n      this.loading = true;\n      this.error = null;\n\n      this.equipeService\n        .removeMembreFromEquipe(this.equipeId, userId)\n        .subscribe({\n          next: (response) => {\n            console.log(\n              `Utilisateur \"${userName}\" retiré avec succès de l'équipe:`,\n              response\n            );\n            this.loading = false;\n\n            // Afficher un message de succès\n            alert(`Utilisateur \"${userName}\" retiré avec succès de l'équipe`);\n\n            // Recharger les membres de l'équipe\n            this.loadTeamMembers(this.equipeId!);\n\n            // Recharger l'équipe pour mettre à jour la liste des membres\n            this.loadEquipe(this.equipeId!);\n\n            // Mettre à jour la liste des utilisateurs disponibles\n            this.updateAvailableUsers();\n          },\n          error: (error) => {\n            console.error(\n              `Erreur lors du retrait de l'utilisateur \"${userName}\":`,\n              error\n            );\n            this.loading = false;\n            this.error = `Impossible de retirer l'utilisateur \"${userName}\" de l'équipe: ${\n              error.message || 'Erreur inconnue'\n            }`;\n          },\n        });\n    } else {\n      console.log(\"Suppression annulée par l'utilisateur\");\n    }\n  }\n\n  deleteEquipe(): void {\n    console.log('Méthode deleteEquipe appelée');\n\n    if (!this.equipeId) {\n      console.error(\"ID d'équipe manquant\");\n      this.error = \"ID d'équipe manquant. Impossible de supprimer l'équipe.\";\n      return;\n    }\n\n    console.log(\"ID de l'équipe à supprimer:\", this.equipeId);\n\n    if (\n      confirm(\n        `Êtes-vous sûr de vouloir supprimer l'équipe \"${this.equipe?.name}\"? Cette action est irréversible.`\n      )\n    ) {\n      console.log('Confirmation acceptée, suppression en cours...');\n\n      this.loading = true;\n      this.error = null;\n\n      this.equipeService.deleteEquipe(this.equipeId).subscribe({\n        next: () => {\n          console.log('Équipe supprimée avec succès');\n          this.loading = false;\n          alert('Équipe supprimée avec succès');\n          this.router.navigate(['/equipes/liste']);\n        },\n        error: (error) => {\n          console.error(\"Erreur lors de la suppression de l'équipe:\", error);\n          this.loading = false;\n          this.error = `Impossible de supprimer l'équipe: ${\n            error.message || 'Erreur inconnue'\n          }`;\n          alert(`Erreur lors de la suppression: ${this.error}`);\n        },\n      });\n    } else {\n      console.log(\"Suppression annulée par l'utilisateur\");\n    }\n  }\n}\n"], "mappings": ";AAAA,SAASA,SAAS,QAAgB,eAAe;AAY1C,WAAMC,qBAAqB,GAA3B,MAAMA,qBAAqB;EAUhCC,YACUC,aAA4B,EAC5BC,WAA4B,EAC5BC,KAAqB,EACrBC,MAAc;IAHd,KAAAH,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IAbhB,KAAAC,MAAM,GAAkB,IAAI;IAC5B,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAC,KAAK,GAAkB,IAAI;IAC3B,KAAAC,QAAQ,GAAkB,IAAI;IAC9B,KAAAC,SAAS,GAAQ;MAAEC,EAAE,EAAE,EAAE;MAAEC,IAAI,EAAE;IAAQ,CAAE;IAC3C,KAAAC,cAAc,GAAW,EAAE;IAC3B,KAAAC,WAAW,GAA8B,EAAE,CAAC,CAAC;IAC7C,KAAAC,WAAW,GAAU,EAAE,CAAC,CAAC;EAOtB;;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACP,QAAQ,GAAG,IAAI,CAACL,KAAK,CAACa,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC;IAEtD;IACA,IAAI,CAACC,SAAS,EAAE;IAEhB,IAAI,IAAI,CAACX,QAAQ,EAAE;MACjB,IAAI,CAACY,UAAU,CAAC,IAAI,CAACZ,QAAQ,CAAC;KAC/B,MAAM;MACL,IAAI,CAACD,KAAK,GAAG,0BAA0B;;EAE3C;EAEA;EACAY,SAASA,CAAA;IACP;IACA;IACA,MAAME,SAAS,GAAW,CACxB;MACEC,GAAG,EAAE,OAAO;MACZC,QAAQ,EAAE,UAAU;MACpBC,KAAK,EAAE,kBAAkB;MACzBb,IAAI,EAAE,OAAO;MACbc,QAAQ,EAAE;KACX,EACD;MACEH,GAAG,EAAE,OAAO;MACZC,QAAQ,EAAE,YAAY;MACtBC,KAAK,EAAE,kBAAkB;MACzBb,IAAI,EAAE,SAAS;MACfc,QAAQ,EAAE;KACX,EACD;MACEH,GAAG,EAAE,OAAO;MACZC,QAAQ,EAAE,YAAY;MACtBC,KAAK,EAAE,iBAAiB;MACxBb,IAAI,EAAE,SAAS;MACfc,QAAQ,EAAE;KACX,CACF;IAED;IACAC,UAAU,CAAC,MAAK;MACd;MACA,MAAMC,QAAQ,GAAG,CAAC,GAAGN,SAAS,CAAC;MAC/BO,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEF,QAAQ,CAAC;MAE9D;MACA,IAAI,IAAI,CAACb,WAAW,IAAI,IAAI,CAACA,WAAW,CAACgB,MAAM,GAAG,CAAC,EAAE;QACnD,MAAMC,aAAa,GAAG,IAAI,CAACjB,WAAW,CAACkB,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACC,IAAI,CAAC;QACzD,IAAI,CAACtB,cAAc,GAAGS,SAAS,CAACc,MAAM,CACnCD,IAAI,IAAK,CAACH,aAAa,CAACK,QAAQ,CAACF,IAAI,CAACZ,GAAG,IAAIY,IAAI,CAACxB,EAAE,IAAI,EAAE,CAAC,CAC7D;OACF,MAAM;QACL,IAAI,CAACE,cAAc,GAAGS,SAAS;;MAGjCO,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE,IAAI,CAACjB,cAAc,CAAC;MAE7D;MACA,IAAI,IAAI,CAACP,MAAM,IAAI,IAAI,CAACA,MAAM,CAACgC,OAAO,EAAE;QACtC,IAAI,CAACC,iBAAiB,EAAE;;IAE5B,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;EACAA,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAACjC,MAAM,IAAI,CAAC,IAAI,CAACA,MAAM,CAACgC,OAAO,EAAE;IAE1C,IAAI,CAAChC,MAAM,CAACgC,OAAO,CAACE,OAAO,CAAEC,QAAQ,IAAI;MACvC,MAAMN,IAAI,GAAG,IAAI,CAACtB,cAAc,CAAC6B,IAAI,CAClCC,CAAC,IAAKA,CAAC,CAACpB,GAAG,KAAKkB,QAAQ,IAAIE,CAAC,CAAChC,EAAE,KAAK8B,QAAQ,CAC/C;MACD,IAAIN,IAAI,IAAIA,IAAI,CAACS,IAAI,EAAE;QACrB,IAAI,CAAC9B,WAAW,CAAC2B,QAAQ,CAAC,GAAGN,IAAI,CAACS,IAAI;OACvC,MAAM;QACL;QACA;QACA;QACA,IAAI,CAAC9B,WAAW,CAAC2B,QAAQ,CAAC,GAAGA,QAAQ;;IAEzC,CAAC,CAAC;EACJ;EAEA;EACAI,aAAaA,CAACJ,QAAgB;IAC5B,OAAO,IAAI,CAAC3B,WAAW,CAAC2B,QAAQ,CAAC,IAAIA,QAAQ;EAC/C;EAEA;EACAK,WAAWA,CAACC,MAA0B;IACpC,IAAI,CAACA,MAAM,EAAE;MACX,OAAO,YAAY;;IAGrB,MAAMZ,IAAI,GAAG,IAAI,CAACtB,cAAc,CAAC6B,IAAI,CAClCC,CAAC,IAAKA,CAAC,CAACpB,GAAG,KAAKwB,MAAM,IAAIJ,CAAC,CAAChC,EAAE,KAAKoC,MAAM,CAC3C;IACD,IAAIZ,IAAI,EAAE;MACR,IAAIA,IAAI,CAACa,SAAS,IAAIb,IAAI,CAACc,QAAQ,EAAE;QACnC,OAAO,GAAGd,IAAI,CAACa,SAAS,IAAIb,IAAI,CAACc,QAAQ,EAAE;OAC5C,MAAM,IAAId,IAAI,CAACS,IAAI,EAAE;QACpB,OAAOT,IAAI,CAACS,IAAI;;;IAGpB,OAAOG,MAAM;EACf;EAEA;EACAG,iBAAiBA,CAACH,MAA0B;IAC1C,IAAI,CAACA,MAAM,EAAE;MACX,OAAO,EAAE;;IAGX,MAAMZ,IAAI,GAAG,IAAI,CAACtB,cAAc,CAAC6B,IAAI,CAClCC,CAAC,IAAKA,CAAC,CAACpB,GAAG,KAAKwB,MAAM,IAAIJ,CAAC,CAAChC,EAAE,KAAKoC,MAAM,CAC3C;IACD,IAAIZ,IAAI,EAAE;MACR,OAAOA,IAAI,CAACgB,UAAU,IAAIhB,IAAI,CAACvB,IAAI,IAAI,EAAE;;IAE3C,OAAO,EAAE;EACX;EAEAS,UAAUA,CAACV,EAAU;IACnB,IAAI,CAACJ,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,KAAK,GAAG,IAAI;IAEjB,IAAI,CAACN,aAAa,CAACkD,SAAS,CAACzC,EAAE,CAAC,CAAC0C,SAAS,CAAC;MACzCC,IAAI,EAAGC,IAAI,IAAI;QACb1B,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEyB,IAAI,CAAC;QACjD,IAAI,CAACjD,MAAM,GAAGiD,IAAI;QAElB;QACA,IAAI,CAACC,eAAe,CAAC7C,EAAE,CAAC;QAExB;QACA,IACE,IAAI,CAACL,MAAM,IACX,IAAI,CAACA,MAAM,CAACgC,OAAO,IACnB,IAAI,CAAChC,MAAM,CAACgC,OAAO,CAACP,MAAM,GAAG,CAAC,EAC9B;UACA,IAAI,CAACQ,iBAAiB,EAAE;;QAG1B,IAAI,CAAChC,OAAO,GAAG,KAAK;MACtB,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfqB,OAAO,CAACrB,KAAK,CACX,oDAAoD,EACpDA,KAAK,CACN;QACD,IAAI,CAACA,KAAK,GACR,8EAA8E;QAChF,IAAI,CAACD,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEA;EACAiD,eAAeA,CAACC,MAAc;IAC5B,IAAI,CAACvD,aAAa,CAACwD,cAAc,CAACD,MAAM,CAAC,CAACJ,SAAS,CAAC;MAClDC,IAAI,EAAGhB,OAAO,IAAI;QAChBT,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEQ,OAAO,CAAC;QACpD,IAAI,CAACvB,WAAW,GAAGuB,OAAO;MAC5B,CAAC;MACD9B,KAAK,EAAGA,KAAK,IAAI;QACfqB,OAAO,CAACrB,KAAK,CACX,oDAAoD,EACpDA,KAAK,CACN;MACH;KACD,CAAC;EACJ;EAEAmD,oBAAoBA,CAAA;IAClB,IAAI,IAAI,CAAClD,QAAQ,EAAE;MACjB,IAAI,CAACJ,MAAM,CAACuD,QAAQ,CAAC,CAAC,mBAAmB,EAAE,IAAI,CAACnD,QAAQ,CAAC,CAAC;;EAE9D;EAEAoD,oBAAoBA,CAAA;IAClB,IAAI,CAACxD,MAAM,CAACuD,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;EAC1C;EAEAE,eAAeA,CAAA;IACb,IAAI,IAAI,CAACrD,QAAQ,EAAE;MACjB,IAAI,CAACJ,MAAM,CAACuD,QAAQ,CAAC,CAAC,gBAAgB,EAAE,IAAI,CAACnD,QAAQ,CAAC,CAAC;;EAE3D;EAEA;EACAsD,UAAUA,CAACC,IAA+B;IACxC,IAAI,CAACA,IAAI,EAAE;MACT,OAAO,KAAK;;IAGd,IAAI;MACF,IAAIC,OAAa;MAEjB,IAAI,OAAOD,IAAI,KAAK,QAAQ,EAAE;QAC5BC,OAAO,GAAG,IAAIC,IAAI,CAACF,IAAI,CAAC;OACzB,MAAM;QACLC,OAAO,GAAGD,IAAI;;MAGhB,IAAIG,KAAK,CAACF,OAAO,CAACG,OAAO,EAAE,CAAC,EAAE;QAC5B,OAAO,eAAe;;MAGxB;MACA,OAAOH,OAAO,CAACI,kBAAkB,CAAC,OAAO,EAAE;QACzCC,GAAG,EAAE,SAAS;QACdC,KAAK,EAAE,SAAS;QAChBC,IAAI,EAAE;OACP,CAAC;KACH,CAAC,OAAOhE,KAAK,EAAE;MACdqB,OAAO,CAACrB,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D,OAAO,gBAAgB;;EAE3B;EAEA;EACAiE,SAASA,CAAC1B,MAAc,EAAEnC,IAAY;IACpCiB,OAAO,CAACC,GAAG,CAAC,0BAA0BiB,MAAM,iBAAiBnC,IAAI,EAAE,CAAC;IAEpE,IAAI,CAAC,IAAI,CAACH,QAAQ,IAAI,CAACsC,MAAM,EAAE;MAC7BlB,OAAO,CAACrB,KAAK,CAAC,0CAA0C,CAAC;MACzD,IAAI,CAACA,KAAK,GAAG,0CAA0C;MACvD;;IAGF;IACA,MAAMkE,eAAe,GAAG,IAAI,CAAC3D,WAAW,CAAC4D,IAAI,CAAEzC,CAAC,IAAKA,CAAC,CAACC,IAAI,KAAKY,MAAM,CAAC;IACvE,IAAI2B,eAAe,EAAE;MACnB,IAAI,CAAClE,KAAK,GAAG,6CAA6C;MAC1DoE,KAAK,CAAC,6CAA6C,CAAC;MACpD;;IAGF;IACA,MAAMC,MAAM,GAAW;MACrBlE,EAAE,EAAEoC,MAAM;MACVnC,IAAI,EAAEA,IAAI,IAAI;KACf;IAED;IACA,MAAMkE,QAAQ,GAAG,IAAI,CAAChC,WAAW,CAACC,MAAM,CAAC;IACzC,MAAMgC,QAAQ,GAAGnE,IAAI,KAAK,OAAO,GAAG,gBAAgB,GAAG,QAAQ;IAE/D,IAAI,CAACV,aAAa,CAAC8E,iBAAiB,CAAC,IAAI,CAACvE,QAAQ,EAAEoE,MAAM,CAAC,CAACxB,SAAS,CAAC;MACpEC,IAAI,EAAG2B,QAAQ,IAAI;QACjBpD,OAAO,CAACC,GAAG,CACT,gBAAgBgD,QAAQ,kBAAkBC,QAAQ,eAAe,EACjEE,QAAQ,CACT;QAED;QACAL,KAAK,CAAC,gBAAgBE,QAAQ,kBAAkBC,QAAQ,cAAc,CAAC;QAEvE;QACA,IAAI,CAACvB,eAAe,CAAC,IAAI,CAAC/C,QAAS,CAAC;QAEpC;QACA,IAAI,CAACY,UAAU,CAAC,IAAI,CAACZ,QAAS,CAAC;QAE/B;QACA,IAAI,CAACyE,oBAAoB,EAAE;MAC7B,CAAC;MACD1E,KAAK,EAAGA,KAAK,IAAI;QACfqB,OAAO,CAACrB,KAAK,CACX,uDAAuD,EACvDA,KAAK,CACN;QACD,IAAI,CAACA,KAAK,GAAG,uCAAuCsE,QAAQ,WAAWC,QAAQ,iCAAiC;QAChHH,KAAK,CAAC,IAAI,CAACpE,KAAK,CAAC;MACnB;KACD,CAAC;EACJ;EAEA;EACA0E,oBAAoBA,CAAA;IAClB,IAAI,CAAC/E,WAAW,CAACgF,QAAQ,EAAE,CAAC9B,SAAS,CAAC;MACpCC,IAAI,EAAG8B,KAAK,IAAI;QACd;QACA,IAAI,IAAI,CAACrE,WAAW,IAAI,IAAI,CAACA,WAAW,CAACgB,MAAM,GAAG,CAAC,EAAE;UACnD,MAAMC,aAAa,GAAG,IAAI,CAACjB,WAAW,CAACkB,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACC,IAAI,CAAC;UACzD,IAAI,CAACtB,cAAc,GAAGuE,KAAK,CAAChD,MAAM,CAC/BD,IAAI,IAAK,CAACH,aAAa,CAACK,QAAQ,CAACF,IAAI,CAACZ,GAAG,IAAIY,IAAI,CAACxB,EAAE,IAAI,EAAE,CAAC,CAC7D;SACF,MAAM;UACL,IAAI,CAACE,cAAc,GAAGuE,KAAK;;QAG7BvD,OAAO,CAACC,GAAG,CACT,iDAAiD,EACjD,IAAI,CAACjB,cAAc,CACpB;MACH,CAAC;MACDL,KAAK,EAAGA,KAAK,IAAI;QACfqB,OAAO,CAACrB,KAAK,CACX,6DAA6D,EAC7DA,KAAK,CACN;MACH;KACD,CAAC;EACJ;EAEA;EACAwE,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAACvE,QAAQ,IAAI,CAAC,IAAI,CAACC,SAAS,CAACC,EAAE,EAAE;MACxCkB,OAAO,CAACrB,KAAK,CAAC,sCAAsC,CAAC;MACrD;;IAGF,IAAI,CAACiE,SAAS,CAAC,IAAI,CAAC/D,SAAS,CAACC,EAAE,EAAE,IAAI,CAACD,SAAS,CAACE,IAAI,IAAI,QAAQ,CAAC;EACpE;EAEAyE,sBAAsBA,CAAC5C,QAAgB;IACrCZ,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAEW,QAAQ,CAAC;IAExE,IAAI,CAAC,IAAI,CAAChC,QAAQ,EAAE;MAClBoB,OAAO,CAACrB,KAAK,CAAC,sBAAsB,CAAC;MACrC,IAAI,CAACA,KAAK,GAAG,wDAAwD;MACrE;;IAGF;IACA,MAAMuC,MAAM,GAAGN,QAAQ;IAEvB;IACA,MAAMqC,QAAQ,GAAG,IAAI,CAAChC,WAAW,CAACC,MAAM,CAAC;IAEzClB,OAAO,CAACC,GAAG,CACT,yCAAyCiB,MAAM,KAAK+B,QAAQ,iBAAiB,IAAI,CAACrE,QAAQ,EAAE,CAC7F;IAED,IACE6E,OAAO,CACL,mDAAmDR,QAAQ,gBAAgB,CAC5E,EACD;MACAjD,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;MAE7D,IAAI,CAACvB,OAAO,GAAG,IAAI;MACnB,IAAI,CAACC,KAAK,GAAG,IAAI;MAEjB,IAAI,CAACN,aAAa,CACfmF,sBAAsB,CAAC,IAAI,CAAC5E,QAAQ,EAAEsC,MAAM,CAAC,CAC7CM,SAAS,CAAC;QACTC,IAAI,EAAG2B,QAAQ,IAAI;UACjBpD,OAAO,CAACC,GAAG,CACT,gBAAgBgD,QAAQ,mCAAmC,EAC3DG,QAAQ,CACT;UACD,IAAI,CAAC1E,OAAO,GAAG,KAAK;UAEpB;UACAqE,KAAK,CAAC,gBAAgBE,QAAQ,kCAAkC,CAAC;UAEjE;UACA,IAAI,CAACtB,eAAe,CAAC,IAAI,CAAC/C,QAAS,CAAC;UAEpC;UACA,IAAI,CAACY,UAAU,CAAC,IAAI,CAACZ,QAAS,CAAC;UAE/B;UACA,IAAI,CAACyE,oBAAoB,EAAE;QAC7B,CAAC;QACD1E,KAAK,EAAGA,KAAK,IAAI;UACfqB,OAAO,CAACrB,KAAK,CACX,4CAA4CsE,QAAQ,IAAI,EACxDtE,KAAK,CACN;UACD,IAAI,CAACD,OAAO,GAAG,KAAK;UACpB,IAAI,CAACC,KAAK,GAAG,wCAAwCsE,QAAQ,kBAC3DtE,KAAK,CAAC+E,OAAO,IAAI,iBACnB,EAAE;QACJ;OACD,CAAC;KACL,MAAM;MACL1D,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;;EAExD;EAEA0D,YAAYA,CAAA;IACV3D,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;IAE3C,IAAI,CAAC,IAAI,CAACrB,QAAQ,EAAE;MAClBoB,OAAO,CAACrB,KAAK,CAAC,sBAAsB,CAAC;MACrC,IAAI,CAACA,KAAK,GAAG,yDAAyD;MACtE;;IAGFqB,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE,IAAI,CAACrB,QAAQ,CAAC;IAEzD,IACE6E,OAAO,CACL,gDAAgD,IAAI,CAAChF,MAAM,EAAEsC,IAAI,mCAAmC,CACrG,EACD;MACAf,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;MAE7D,IAAI,CAACvB,OAAO,GAAG,IAAI;MACnB,IAAI,CAACC,KAAK,GAAG,IAAI;MAEjB,IAAI,CAACN,aAAa,CAACsF,YAAY,CAAC,IAAI,CAAC/E,QAAQ,CAAC,CAAC4C,SAAS,CAAC;QACvDC,IAAI,EAAEA,CAAA,KAAK;UACTzB,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;UAC3C,IAAI,CAACvB,OAAO,GAAG,KAAK;UACpBqE,KAAK,CAAC,8BAA8B,CAAC;UACrC,IAAI,CAACvE,MAAM,CAACuD,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;QAC1C,CAAC;QACDpD,KAAK,EAAGA,KAAK,IAAI;UACfqB,OAAO,CAACrB,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;UAClE,IAAI,CAACD,OAAO,GAAG,KAAK;UACpB,IAAI,CAACC,KAAK,GAAG,qCACXA,KAAK,CAAC+E,OAAO,IAAI,iBACnB,EAAE;UACFX,KAAK,CAAC,kCAAkC,IAAI,CAACpE,KAAK,EAAE,CAAC;QACvD;OACD,CAAC;KACH,MAAM;MACLqB,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;;EAExD;CACD;AA3bY9B,qBAAqB,GAAAyF,UAAA,EALjC1F,SAAS,CAAC;EACT2F,QAAQ,EAAE,mBAAmB;EAC7BC,WAAW,EAAE,gCAAgC;EAC7CC,SAAS,EAAE,CAAC,+BAA+B;CAC5C,CAAC,C,EACW5F,qBAAqB,CA2bjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}