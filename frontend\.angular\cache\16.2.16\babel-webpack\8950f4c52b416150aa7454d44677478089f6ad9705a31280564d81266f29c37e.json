{"ast": null, "code": "import { finalize } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/ai.service\";\nimport * as i2 from \"src/app/services/task.service\";\nimport * as i3 from \"src/app/services/notification.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nfunction AiChatComponent_div_3_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"div\", 22)(2, \"div\", 23);\n    i0.ɵɵelement(3, \"p\", 24);\n    i0.ɵɵelementStart(4, \"div\", 25);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 26);\n    i0.ɵɵelement(7, \"i\", 27);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const message_r8 = i0.ɵɵnextContext().$implicit;\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"innerHTML\", message_r8.content, i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" Vous \\u2022 \", ctx_r10.getCurrentTime(), \" \");\n  }\n}\nfunction AiChatComponent_div_3_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"div\", 22)(2, \"div\", 29);\n    i0.ɵɵelement(3, \"i\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 31);\n    i0.ɵɵelement(5, \"p\", 32);\n    i0.ɵɵelementStart(6, \"div\", 33);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const message_r8 = i0.ɵɵnextContext().$implicit;\n    const ctx_r11 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"innerHTML\", message_r8.content, i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" Assistant IA \\u2022 \", ctx_r11.getCurrentTime(), \" \");\n  }\n}\nfunction AiChatComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵtemplate(1, AiChatComponent_div_3_div_1_Template, 8, 2, \"div\", 19);\n    i0.ɵɵtemplate(2, AiChatComponent_div_3_div_2_Template, 8, 2, \"div\", 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r8 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r8.role === \"user\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r8.role === \"assistant\");\n  }\n}\nfunction AiChatComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35)(2, \"div\", 29);\n    i0.ɵɵelement(3, \"i\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 31)(5, \"div\", 36);\n    i0.ɵɵelement(6, \"div\", 37)(7, \"div\", 38)(8, \"div\", 39);\n    i0.ɵɵelementEnd()()()();\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    background: a0\n  };\n};\nconst _c1 = function (a0) {\n  return {\n    color: a0\n  };\n};\nfunction AiChatComponent_div_5_div_15_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88)(1, \"div\", 89);\n    i0.ɵɵelement(2, \"i\", 90);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\")(4, \"div\", 91);\n    i0.ɵɵtext(5, \" Responsable \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 92);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r19 = i0.ɵɵnextContext();\n    const i_r16 = ctx_r19.index;\n    const entity_r15 = ctx_r19.$implicit;\n    const ctx_r17 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(4, _c0, ctx_r17.getColorForIndex(i_r16) + \"10\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(6, _c0, ctx_r17.getGradientForIndex(i_r16)));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(8, _c1, ctx_r17.getColorForIndex(i_r16)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", entity_r15.assignedTo, \" \");\n  }\n}\nconst _c2 = function (a0, a1, a2) {\n  return {\n    \"border-[#ff6b69]/20 bg-[#ff6b69]/5\": a0,\n    \"border-[#ffa726]/20 bg-[#ffa726]/5\": a1,\n    \"border-[#42a5f5]/20 bg-[#42a5f5]/5\": a2\n  };\n};\nconst _c3 = function (a0, a1, a2) {\n  return {\n    \"bg-[#ff6b69] text-white\": a0,\n    \"bg-[#ffa726] text-white\": a1,\n    \"bg-[#42a5f5] text-white\": a2\n  };\n};\nfunction AiChatComponent_div_5_div_15_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 93)(1, \"div\", 94)(2, \"h6\", 95);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 96);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"p\", 97);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const task_r20 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(5, _c2, task_r20.priority === \"high\", task_r20.priority === \"medium\", task_r20.priority === \"low\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", task_r20.title, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(9, _c3, task_r20.priority === \"high\", task_r20.priority === \"medium\", task_r20.priority === \"low\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", task_r20.priority === \"high\" ? \"Haute\" : task_r20.priority === \"medium\" ? \"Moyenne\" : \"Basse\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", task_r20.description, \" \");\n  }\n}\nconst _c4 = function (a0) {\n  return {\n    \"border-color\": a0\n  };\n};\nfunction AiChatComponent_div_5_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 70)(1, \"div\", 71);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 72)(4, \"div\", 73);\n    i0.ɵɵelement(5, \"div\", 74);\n    i0.ɵɵelementStart(6, \"div\", 75)(7, \"div\", 76);\n    i0.ɵɵelement(8, \"i\", 77);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"h4\", 78);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 50)(12, \"div\", 79)(13, \"p\", 80);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(15, AiChatComponent_div_5_div_15_div_15_Template, 8, 10, \"div\", 81);\n    i0.ɵɵelementStart(16, \"div\", 82)(17, \"h5\", 83);\n    i0.ɵɵelement(18, \"i\", 84);\n    i0.ɵɵtext(19, \" T\\u00E2ches \\u00E0 r\\u00E9aliser \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"span\", 85);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 86);\n    i0.ɵɵtemplate(23, AiChatComponent_div_5_div_15_div_23_Template, 8, 13, \"div\", 87);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const entity_r15 = ctx.$implicit;\n    const i_r16 = ctx.index;\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(12, _c0, ctx_r14.getGradientForIndex(i_r16)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i_r16 + 1, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(14, _c0, ctx_r14.getGradientForIndex(i_r16)));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", ctx_r14.getIconForModule(entity_r15.name));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(entity_r15.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(16, _c4, ctx_r14.getColorForIndex(i_r16)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", entity_r15.description, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", entity_r15.assignedTo);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(18, _c1, ctx_r14.getColorForIndex(i_r16)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(20, _c0, ctx_r14.getGradientForIndex(i_r16)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", entity_r15.tasks.length, \" t\\u00E2ches \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", entity_r15.tasks);\n  }\n}\nfunction AiChatComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"div\", 41)(2, \"div\", 42)(3, \"div\", 43)(4, \"h3\", 44);\n    i0.ɵɵelement(5, \"i\", 45);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 46);\n    i0.ɵɵelement(8, \"i\", 47);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 48);\n    i0.ɵɵelement(11, \"i\", 49);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(13, \"div\", 50)(14, \"div\", 51);\n    i0.ɵɵtemplate(15, AiChatComponent_div_5_div_15_Template, 24, 22, \"div\", 52);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 53)(17, \"div\", 54)(18, \"div\", 55)(19, \"div\", 56)(20, \"h4\", 57);\n    i0.ɵɵelement(21, \"i\", 58);\n    i0.ɵɵtext(22, \" Plan de projet pr\\u00EAt \\u00E0 \\u00EAtre impl\\u00E9ment\\u00E9 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 59)(24, \"div\", 60);\n    i0.ɵɵtext(25, \" 1 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\")(27, \"h6\", 61);\n    i0.ɵɵtext(28, \" Cr\\u00E9ation des t\\u00E2ches \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"p\", 62);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(31, \"div\", 59)(32, \"div\", 63);\n    i0.ɵɵtext(33, \" 2 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"div\")(35, \"h6\", 61);\n    i0.ɵɵtext(36, \" Assignation aux membres \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"p\", 62);\n    i0.ɵɵtext(38);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(39, \"div\", 59)(40, \"div\", 64);\n    i0.ɵɵtext(41, \" 3 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"div\")(43, \"h6\", 61);\n    i0.ɵɵtext(44, \" Suivi du projet \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"p\", 62);\n    i0.ɵɵtext(46, \" Vous pourrez suivre l'avancement dans le tableau de bord des t\\u00E2ches \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(47, \"div\", 65)(48, \"button\", 66);\n    i0.ɵɵlistener(\"click\", function AiChatComponent_div_5_Template_button_click_48_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r22 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r22.createTasks());\n    });\n    i0.ɵɵelement(49, \"i\", 67);\n    i0.ɵɵtext(50, \" Cr\\u00E9er les t\\u00E2ches \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(51, \"p\", 68);\n    i0.ɵɵelement(52, \"i\", 69);\n    i0.ɵɵtext(53, \" Cette action est irr\\u00E9versible \");\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" Plan du projet \\\"\", ctx_r3.generatedContent.projectTitle, \"\\\" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r3.generatedContent.entities.length, \" modules g\\u00E9n\\u00E9r\\u00E9s avec \", ctx_r3.countTasks(ctx_r3.generatedContent), \" t\\u00E2ches au total \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.team && ctx_r3.team.members ? ctx_r3.team.members.length : 0, \" membres \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.generatedContent.entities);\n    i0.ɵɵadvance(15);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.countTasks(ctx_r3.generatedContent), \" t\\u00E2ches seront cr\\u00E9\\u00E9es dans le syst\\u00E8me \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" Les t\\u00E2ches seront assign\\u00E9es aux \", ctx_r3.team && ctx_r3.team.members ? ctx_r3.team.members.length : 0, \" membres de l'\\u00E9quipe \");\n  }\n}\nfunction AiChatComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 98)(1, \"div\", 99)(2, \"div\", 59)(3, \"div\", 100);\n    i0.ɵɵelement(4, \"i\", 101);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 80);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.error, \" \");\n  }\n}\nfunction AiChatComponent_div_8_span_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 115);\n  }\n}\nfunction AiChatComponent_div_8_i_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 105);\n  }\n}\nfunction AiChatComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 102)(1, \"div\", 103)(2, \"h4\", 104);\n    i0.ɵɵelement(3, \"i\", 105);\n    i0.ɵɵtext(4, \" G\\u00E9n\\u00E9rer des t\\u00E2ches avec l'IA \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 106)(6, \"div\")(7, \"label\", 107);\n    i0.ɵɵtext(8, \" Titre de votre projet \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 108)(10, \"div\", 12);\n    i0.ɵɵelement(11, \"i\", 109);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"input\", 110);\n    i0.ɵɵlistener(\"ngModelChange\", function AiChatComponent_div_8_Template_input_ngModelChange_12_listener($event) {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r26 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r26.projectTitle = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"p\", 111);\n    i0.ɵɵelement(14, \"i\", 47);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"button\", 112);\n    i0.ɵɵlistener(\"click\", function AiChatComponent_div_8_Template_button_click_16_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r28 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r28.generateTasks());\n    });\n    i0.ɵɵtemplate(17, AiChatComponent_div_8_span_17_Template, 1, 0, \"span\", 113);\n    i0.ɵɵtemplate(18, AiChatComponent_div_8_i_18_Template, 1, 0, \"i\", 114);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(12);\n    i0.ɵɵproperty(\"ngModel\", ctx_r5.projectTitle)(\"disabled\", ctx_r5.isGenerating);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" L'IA g\\u00E9n\\u00E9rera \", ctx_r5.team && ctx_r5.team.members ? ctx_r5.team.members.length : 3, \" modules, un pour chaque membre de l'\\u00E9quipe. \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r5.isGenerating || !ctx_r5.projectTitle.trim());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.isGenerating);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r5.isGenerating);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r5.isGenerating ? \"G\\u00E9n\\u00E9ration en cours...\" : \"G\\u00E9n\\u00E9rer des t\\u00E2ches\", \" \");\n  }\n}\nfunction AiChatComponent_span_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 116);\n  }\n}\nfunction AiChatComponent_i_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 117);\n  }\n}\nexport class AiChatComponent {\n  constructor(aiService, taskService, notificationService) {\n    this.aiService = aiService;\n    this.taskService = taskService;\n    this.notificationService = notificationService;\n    this.projectTitle = '';\n    this.isGenerating = false;\n    this.generatedContent = null;\n    this.error = null;\n    // Pour le chat\n    this.messages = [];\n    this.userQuestion = '';\n    this.isAskingQuestion = false;\n  }\n  ngOnInit() {\n    // Ajouter un message de bienvenue\n    this.messages.push({\n      role: 'assistant',\n      content: 'Bonjour ! Je suis votre assistant IA pour la gestion de projet. Entrez le titre de votre projet pour que je puisse vous aider à le diviser en tâches, ou posez-moi une question sur la gestion de projet.'\n    });\n  }\n  generateTasks() {\n    if (!this.projectTitle.trim()) {\n      this.notificationService.showError('Veuillez entrer un titre de projet');\n      return;\n    }\n    // Vérifier si l'équipe a des membres, sinon utiliser un nombre par défaut\n    let memberCount = this.team && this.team.members ? this.team.members.length : 3;\n    // S'assurer que nous avons au moins 3 entités pour un projet significatif\n    const effectiveMemberCount = Math.max(memberCount, 3);\n    if (memberCount === 0) {\n      this.notificationService.showWarning(\"L'équipe n'a pas de membres. Des tâches génériques seront créées.\");\n    }\n    this.isGenerating = true;\n    this.error = null;\n    console.log(`Génération de tâches pour ${effectiveMemberCount} entités (équipe de ${memberCount} membres)`);\n    // Ajouter la demande aux messages\n    this.messages.push({\n      role: 'user',\n      content: `Génère des tâches pour le projet \"${this.projectTitle}\" avec exactement ${effectiveMemberCount} entités, une pour chaque membre de l'équipe. Chaque entité doit représenter un module distinct du projet.`\n    });\n    // Ajouter un message de chargement\n    const loadingMessageIndex = this.messages.length;\n    this.messages.push({\n      role: 'assistant',\n      content: 'Je génère des tâches pour votre projet...'\n    });\n    // Récupérer les informations sur les membres de l'équipe\n    let teamMembers = [];\n    if (this.team && this.team.members) {\n      // Utiliser les IDs des membres\n      teamMembers = this.team.members.map((memberId, index) => {\n        return {\n          id: memberId,\n          name: `Membre ${index + 1}`,\n          role: 'membre'\n        };\n      });\n      console.log(\"Informations sur les membres passées à l'IA:\", teamMembers);\n    }\n    this.aiService.generateProjectTasks(this.projectTitle, memberCount, teamMembers).pipe(finalize(() => this.isGenerating = false)).subscribe({\n      next: result => {\n        if (!result || !result.entities || result.entities.length === 0) {\n          console.error(\"Résultat invalide reçu de l'API:\", result);\n          this.handleGenerationError(loadingMessageIndex, 'Format de réponse invalide');\n          return;\n        }\n        this.generatedContent = result;\n        // Remplacer le message de chargement par la réponse\n        this.messages[loadingMessageIndex] = {\n          role: 'assistant',\n          content: `J'ai généré ${result.entities.length} entités pour votre projet \"${result.projectTitle}\" avec un total de ${this.countTasks(result)} tâches.`\n        };\n        this.notificationService.showSuccess('Tâches générées avec succès');\n      },\n      error: error => {\n        console.error('Erreur lors de la génération des tâches:', error);\n        this.handleGenerationError(loadingMessageIndex, error.message || 'Erreur inconnue');\n      }\n    });\n  }\n  // Méthode pour gérer les erreurs de génération\n  handleGenerationError(messageIndex, errorDetails) {\n    this.error = 'Impossible de générer les tâches. Veuillez réessayer.';\n    // Remplacer le message de chargement par le message d'erreur\n    this.messages[messageIndex] = {\n      role: 'assistant',\n      content: \"Désolé, je n'ai pas pu générer les tâches. Veuillez réessayer avec un titre de projet différent.\"\n    };\n    this.notificationService.showError('Erreur lors de la génération des tâches: ' + errorDetails);\n  }\n  askQuestion() {\n    if (!this.userQuestion.trim()) {\n      return;\n    }\n    const question = this.userQuestion.trim();\n    this.userQuestion = '';\n    this.isAskingQuestion = true;\n    // Ajouter la question aux messages\n    this.messages.push({\n      role: 'user',\n      content: question\n    });\n    const projectContext = {\n      title: this.projectTitle || (this.generatedContent ? this.generatedContent.projectTitle : ''),\n      description: \"Projet géré par l'équipe \" + (this.team ? this.team.name : '')\n    };\n    this.aiService.askProjectQuestion(question, projectContext).pipe(finalize(() => this.isAskingQuestion = false)).subscribe({\n      next: response => {\n        // Ajouter la réponse aux messages\n        this.messages.push({\n          role: 'assistant',\n          content: response\n        });\n      },\n      error: error => {\n        console.error(\"Erreur lors de la demande à l'IA:\", error);\n        // Ajouter l'erreur aux messages\n        this.messages.push({\n          role: 'assistant',\n          content: \"Désolé, je n'ai pas pu répondre à votre question. Veuillez réessayer.\"\n        });\n        this.notificationService.showError(\"Erreur lors de la communication avec l'IA\");\n      }\n    });\n  }\n  createTasks() {\n    if (!this.generatedContent || !this.team || !this.team._id) {\n      this.notificationService.showError('Aucune tâche générée ou équipe invalide');\n      return;\n    }\n    let createdCount = 0;\n    const totalTasks = this.countTasks(this.generatedContent);\n    // Vérifier si l'équipe a des membres\n    if (!this.team.members || this.team.members.length === 0) {\n      this.notificationService.showError(\"L'équipe n'a pas de membres pour assigner les tâches\");\n      return;\n    }\n    // Préparer la liste des membres de l'équipe\n    const teamMembers = this.team.members.map(member => {\n      return typeof member === 'string' ? member : member.userId;\n    });\n    // Créer un mapping des noms de membres vers leurs IDs\n    const memberNameToIdMap = {};\n    teamMembers.forEach((memberId, index) => {\n      memberNameToIdMap[`Membre ${index + 1}`] = memberId;\n    });\n    console.log(\"Membres de l'équipe disponibles pour l'assignation:\", teamMembers);\n    console.log('Mapping des noms de membres vers leurs IDs:', memberNameToIdMap);\n    // Pour chaque entité\n    this.generatedContent.entities.forEach(entity => {\n      // Déterminer le membre assigné à cette entité\n      let assignedMemberId;\n      // Si l'IA a suggéré une assignation\n      if (entity.assignedTo) {\n        // Essayer de trouver l'ID du membre à partir du nom suggéré\n        const memberName = entity.assignedTo;\n        if (memberNameToIdMap[memberName]) {\n          assignedMemberId = memberNameToIdMap[memberName];\n          console.log(`Assignation suggérée par l'IA: Entité \"${entity.name}\" assignée à \"${memberName}\" (ID: ${assignedMemberId})`);\n        } else {\n          // Si le nom n'est pas trouvé, assigner aléatoirement\n          const randomMemberIndex = Math.floor(Math.random() * teamMembers.length);\n          assignedMemberId = teamMembers[randomMemberIndex];\n          console.log(`Nom de membre \"${memberName}\" non trouvé, assignation aléatoire à l'index ${randomMemberIndex}`);\n        }\n      } else {\n        // Si pas d'assignation suggérée, assigner aléatoirement\n        const randomMemberIndex = Math.floor(Math.random() * teamMembers.length);\n        assignedMemberId = teamMembers[randomMemberIndex];\n        console.log(`Pas d'assignation suggérée, assignation aléatoire à l'index ${randomMemberIndex}`);\n      }\n      // Pour chaque tâche dans l'entité\n      entity.tasks.forEach(taskData => {\n        const task = {\n          title: taskData.title,\n          description: `[${entity.name}] ${taskData.description}`,\n          status: taskData.status || 'todo',\n          priority: taskData.priority || 'medium',\n          teamId: this.team._id || '',\n          // Utiliser l'ID du membre assigné à l'entité\n          assignedTo: assignedMemberId\n        };\n        this.taskService.createTask(task).subscribe({\n          next: () => {\n            createdCount++;\n            if (createdCount === totalTasks) {\n              this.notificationService.showSuccess(`${createdCount} tâches créées avec succès et assignées aux membres de l'équipe`);\n              // Réinitialiser après création\n              this.generatedContent = null;\n              this.projectTitle = '';\n            }\n          },\n          error: error => {\n            console.error('Erreur lors de la création de la tâche:', error);\n            this.notificationService.showError('Erreur lors de la création des tâches');\n          }\n        });\n      });\n    });\n  }\n  countTasks(content) {\n    if (!content || !content.entities) return 0;\n    return content.entities.reduce((total, entity) => {\n      return total + (entity.tasks ? entity.tasks.length : 0);\n    }, 0);\n  }\n  // Méthode pour obtenir un dégradé de couleur basé sur l'index\n  getGradientForIndex(index) {\n    // Liste de dégradés prédéfinis\n    const gradients = ['linear-gradient(45deg, #007bff, #6610f2)', 'linear-gradient(45deg, #11998e, #38ef7d)', 'linear-gradient(45deg, #FC5C7D, #6A82FB)', 'linear-gradient(45deg, #FF8008, #FFC837)', 'linear-gradient(45deg, #8E2DE2, #4A00E0)', 'linear-gradient(45deg, #2193b0, #6dd5ed)', 'linear-gradient(45deg, #373B44, #4286f4)', 'linear-gradient(45deg, #834d9b, #d04ed6)', 'linear-gradient(45deg, #0cebeb, #20e3b2, #29ffc6)' // Turquoise\n    ];\n    // Utiliser le modulo pour s'assurer que nous ne dépassons pas le tableau\n    return gradients[index % gradients.length];\n  }\n  // Méthode pour obtenir une couleur unique basée sur l'index\n  getColorForIndex(index) {\n    // Liste de couleurs prédéfinies\n    const colors = ['#007bff', '#11998e', '#FC5C7D', '#FF8008', '#8E2DE2', '#2193b0', '#373B44', '#834d9b', '#0cebeb' // Turquoise\n    ];\n    // Utiliser le modulo pour s'assurer que nous ne dépassons pas le tableau\n    return colors[index % colors.length];\n  }\n  // Méthode pour obtenir une icône en fonction du nom du module\n  getIconForModule(moduleName) {\n    // Convertir le nom du module en minuscules pour faciliter la comparaison\n    const name = moduleName.toLowerCase();\n    // Mapper les noms de modules courants à des icônes Bootstrap\n    if (name.includes('crud') || name.includes('api') || name.includes('données') || name.includes('base')) {\n      return 'bi-database-fill';\n    } else if (name.includes('interface') || name.includes('ui') || name.includes('front') || name.includes('utilisateur')) {\n      return 'bi-window';\n    } else if (name.includes('déploiement') || name.includes('serveur') || name.includes('cloud')) {\n      return 'bi-cloud-arrow-up-fill';\n    } else if (name.includes('test') || name.includes('qualité') || name.includes('qa')) {\n      return 'bi-bug-fill';\n    } else if (name.includes('sécurité') || name.includes('auth')) {\n      return 'bi-shield-lock-fill';\n    } else if (name.includes('paiement') || name.includes('transaction')) {\n      return 'bi-credit-card-fill';\n    } else if (name.includes('utilisateur') || name.includes('user') || name.includes('profil')) {\n      return 'bi-person-fill';\n    } else if (name.includes('doc') || name.includes('documentation')) {\n      return 'bi-file-text-fill';\n    } else if (name.includes('mobile') || name.includes('app')) {\n      return 'bi-phone-fill';\n    } else if (name.includes('backend') || name.includes('serveur')) {\n      return 'bi-server';\n    } else if (name.includes('analytics') || name.includes('statistique') || name.includes('seo')) {\n      return 'bi-graph-up';\n    }\n    // Icône par défaut si aucune correspondance n'est trouvée\n    return 'bi-code-square';\n  }\n  // Méthode pour obtenir l'heure actuelle au format HH:MM\n  getCurrentTime() {\n    const now = new Date();\n    const hours = now.getHours().toString().padStart(2, '0');\n    const minutes = now.getMinutes().toString().padStart(2, '0');\n    return `${hours}:${minutes}`;\n  }\n  static {\n    this.ɵfac = function AiChatComponent_Factory(t) {\n      return new (t || AiChatComponent)(i0.ɵɵdirectiveInject(i1.AiService), i0.ɵɵdirectiveInject(i2.TaskService), i0.ɵɵdirectiveInject(i3.NotificationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AiChatComponent,\n      selectors: [[\"app-ai-chat\"]],\n      inputs: {\n        team: \"team\"\n      },\n      decls: 18,\n      vars: 10,\n      consts: [[1, \"w-full\"], [1, \"p-6\", \"max-h-96\", \"overflow-y-auto\"], [\"chatContainer\", \"\"], [\"class\", \"mb-4\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"flex justify-start mb-4\", 4, \"ngIf\"], [\"class\", \"border-t border-[#8b5a9f]/20 dark:border-[#00f7ff]/20\", 4, \"ngIf\"], [\"class\", \"p-4 border-t border-[#ff6b69]/20 dark:border-[#ff3b30]/20\", 4, \"ngIf\"], [1, \"p-6\", \"border-t\", \"border-[#8b5a9f]/20\", \"dark:border-[#00f7ff]/20\", \"bg-[#f0f4f8]\", \"dark:bg-[#0a0a0a]\"], [\"class\", \"mb-6\", 4, \"ngIf\"], [1, \"bg-white\", \"dark:bg-[#1a1a1a]\", \"rounded-xl\", \"shadow-lg\", \"dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)]\", \"p-4\", \"border\", \"border-[#8b5a9f]/20\", \"dark:border-[#00f7ff]/20\"], [1, \"flex\", \"space-x-3\"], [1, \"relative\", \"flex-1\"], [1, \"absolute\", \"inset-y-0\", \"left-0\", \"pl-3\", \"flex\", \"items-center\", \"pointer-events-none\"], [1, \"fas\", \"fa-comment-dots\", \"text-[#8b5a9f]\", \"dark:text-[#00f7ff]\"], [\"type\", \"text\", \"placeholder\", \"Posez une question sur la gestion de projet...\", 1, \"w-full\", \"pl-10\", \"pr-4\", \"py-3\", \"bg-[#f0f4f8]\", \"dark:bg-[#0a0a0a]\", \"border\", \"border-[#8b5a9f]/20\", \"dark:border-[#00f7ff]/20\", \"rounded-lg\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"placeholder-[#6d6870]/50\", \"dark:placeholder-[#a0a0a0]/50\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-[#8b5a9f]\", \"dark:focus:ring-[#00f7ff]\", \"focus:border-transparent\", \"transition-all\", 3, \"ngModel\", \"disabled\", \"ngModelChange\", \"keyup.enter\"], [1, \"bg-gradient-to-r\", \"from-[#8b5a9f]\", \"to-[#4a00e0]\", \"dark:from-[#00f7ff]\", \"dark:to-[#8b5a9f]\", \"text-white\", \"px-4\", \"py-3\", \"rounded-lg\", \"font-medium\", \"transition-all\", \"duration-300\", \"hover:scale-105\", \"shadow-lg\", \"disabled:opacity-50\", \"disabled:cursor-not-allowed\", \"disabled:hover:scale-100\", 3, \"disabled\", \"click\"], [\"class\", \"inline-block w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin\", 4, \"ngIf\"], [\"class\", \"fas fa-paper-plane\", 4, \"ngIf\"], [1, \"mb-4\"], [\"class\", \"flex justify-end\", 4, \"ngIf\"], [\"class\", \"flex justify-start\", 4, \"ngIf\"], [1, \"flex\", \"justify-end\"], [1, \"flex\", \"items-end\", \"space-x-2\", \"max-w-xs\", \"lg:max-w-md\"], [1, \"bg-gradient-to-r\", \"from-[#dac4ea]\", \"to-[#8b5a9f]\", \"dark:from-[#00f7ff]\", \"dark:to-[#dac4ea]\", \"text-white\", \"p-3\", \"rounded-lg\", \"rounded-br-none\", \"shadow-lg\"], [1, \"text-sm\", 3, \"innerHTML\"], [1, \"text-xs\", \"text-white/80\", \"mt-1\", \"text-right\"], [1, \"w-8\", \"h-8\", \"bg-gradient-to-br\", \"from-[#dac4ea]\", \"to-[#8b5a9f]\", \"dark:from-[#00f7ff]\", \"dark:to-[#dac4ea]\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\"], [1, \"fas\", \"fa-user\", \"text-white\", \"text-sm\"], [1, \"flex\", \"justify-start\"], [1, \"w-8\", \"h-8\", \"bg-gradient-to-br\", \"from-[#8b5a9f]\", \"to-[#4a00e0]\", \"dark:from-[#00f7ff]\", \"dark:to-[#8b5a9f]\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\"], [1, \"fas\", \"fa-robot\", \"text-white\", \"text-sm\"], [1, \"bg-[#f0f4f8]\", \"dark:bg-[#0a0a0a]\", \"border\", \"border-[#8b5a9f]/20\", \"dark:border-[#00f7ff]/20\", \"p-3\", \"rounded-lg\", \"rounded-bl-none\", \"shadow-lg\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", 3, \"innerHTML\"], [1, \"text-xs\", \"text-[#6d6870]/60\", \"dark:text-[#a0a0a0]/60\", \"mt-1\"], [1, \"flex\", \"justify-start\", \"mb-4\"], [1, \"flex\", \"items-end\", \"space-x-2\"], [1, \"flex\", \"space-x-1\"], [1, \"w-2\", \"h-2\", \"bg-[#8b5a9f]\", \"dark:bg-[#00f7ff]\", \"rounded-full\", \"animate-bounce\"], [1, \"w-2\", \"h-2\", \"bg-[#8b5a9f]\", \"dark:bg-[#00f7ff]\", \"rounded-full\", \"animate-bounce\", 2, \"animation-delay\", \"0.1s\"], [1, \"w-2\", \"h-2\", \"bg-[#8b5a9f]\", \"dark:bg-[#00f7ff]\", \"rounded-full\", \"animate-bounce\", 2, \"animation-delay\", \"0.2s\"], [1, \"border-t\", \"border-[#8b5a9f]/20\", \"dark:border-[#00f7ff]/20\"], [1, \"p-6\", \"bg-gradient-to-r\", \"from-[#8b5a9f]/10\", \"to-[#4a00e0]/10\", \"dark:from-[#00f7ff]/10\", \"dark:to-[#8b5a9f]/10\"], [1, \"flex\", \"flex-col\", \"lg:flex-row\", \"lg:items-center\", \"lg:justify-between\"], [1, \"mb-4\", \"lg:mb-0\"], [1, \"text-xl\", \"font-bold\", \"text-[#8b5a9f]\", \"dark:text-[#00f7ff]\", \"mb-2\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-project-diagram\", \"mr-2\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"text-sm\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-info-circle\", \"mr-2\"], [1, \"bg-[#8b5a9f]/20\", \"dark:bg-[#00f7ff]/20\", \"text-[#8b5a9f]\", \"dark:text-[#00f7ff]\", \"px-4\", \"py-2\", \"rounded-full\", \"text-sm\", \"font-medium\"], [1, \"fas\", \"fa-users\", \"mr-2\"], [1, \"p-6\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"xl:grid-cols-3\", \"gap-6\"], [\"class\", \"group relative\", 4, \"ngFor\", \"ngForOf\"], [1, \"p-6\", \"border-t\", \"border-[#8b5a9f]/20\", \"dark:border-[#00f7ff]/20\"], [1, \"bg-gradient-to-r\", \"from-[#00ff9d]/10\", \"to-[#38ef7d]/10\", \"dark:from-[#00f7ff]/10\", \"dark:to-[#00ff9d]/10\", \"rounded-xl\", \"p-6\"], [1, \"grid\", \"grid-cols-1\", \"lg:grid-cols-3\", \"gap-6\", \"items-center\"], [1, \"lg:col-span-2\", \"space-y-4\"], [1, \"text-xl\", \"font-bold\", \"text-[#00ff9d]\", \"dark:text-[#00f7ff]\", \"mb-4\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-check-circle\", \"mr-2\"], [1, \"flex\", \"items-center\"], [1, \"w-8\", \"h-8\", \"bg-[#00ff9d]\", \"dark:bg-[#00f7ff]\", \"text-white\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\", \"font-bold\", \"text-sm\", \"mr-4\"], [1, \"font-semibold\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [1, \"text-sm\", \"text-[#6d6870]/80\", \"dark:text-[#a0a0a0]/80\"], [1, \"w-8\", \"h-8\", \"bg-[#4f5fad]\", \"dark:bg-[#8b5a9f]\", \"text-white\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\", \"font-bold\", \"text-sm\", \"mr-4\"], [1, \"w-8\", \"h-8\", \"bg-[#42a5f5]\", \"text-white\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\", \"font-bold\", \"text-sm\", \"mr-4\"], [1, \"text-center\"], [1, \"bg-gradient-to-r\", \"from-[#00ff9d]\", \"to-[#38ef7d]\", \"dark:from-[#00f7ff]\", \"dark:to-[#00ff9d]\", \"text-white\", \"px-8\", \"py-4\", \"rounded-xl\", \"font-bold\", \"text-lg\", \"transition-all\", \"duration-300\", \"hover:scale-105\", \"shadow-lg\", \"hover:shadow-[0_0_25px_rgba(0,255,157,0.4)]\", \"dark:hover:shadow-[0_0_25px_rgba(0,247,255,0.4)]\", 3, \"click\"], [1, \"fas\", \"fa-plus-circle\", \"mr-2\"], [1, \"text-xs\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mt-2\"], [1, \"fas\", \"fa-info-circle\", \"mr-1\"], [1, \"group\", \"relative\"], [1, \"absolute\", \"-top-2\", \"-right-2\", \"z-10\", \"w-8\", \"h-8\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\", \"text-white\", \"text-sm\", \"font-bold\", \"shadow-lg\", 3, \"ngStyle\"], [1, \"bg-white\", \"dark:bg-[#1a1a1a]\", \"rounded-xl\", \"shadow-lg\", \"dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)]\", \"overflow-hidden\", \"border\", \"border-[#8b5a9f]/20\", \"dark:border-[#00f7ff]/20\", \"hover:shadow-xl\", \"transition-all\", \"duration-300\", \"hover:-translate-y-1\"], [1, \"p-6\", \"text-white\", \"relative\", \"overflow-hidden\", 3, \"ngStyle\"], [1, \"absolute\", \"top-0\", \"right-0\", \"w-20\", \"h-20\", \"rounded-full\", \"bg-white/10\", \"-mr-10\", \"-mt-10\"], [1, \"relative\", \"z-10\"], [1, \"w-12\", \"h-12\", \"bg-white/20\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\", \"mb-4\"], [1, \"text-xl\", 3, \"ngClass\"], [1, \"text-lg\", \"font-bold\"], [1, \"bg-[#f0f4f8]\", \"dark:bg-[#0a0a0a]\", \"border-l-4\", \"rounded-lg\", \"p-3\", \"mb-4\", 3, \"ngStyle\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [\"class\", \"flex items-center p-3 rounded-lg mb-4\", 3, \"ngStyle\", 4, \"ngIf\"], [1, \"flex\", \"items-center\", \"justify-between\", \"mb-4\", \"pb-2\", \"border-b\", \"border-[#8b5a9f]/10\", \"dark:border-[#00f7ff]/10\"], [1, \"text-sm\", \"font-semibold\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-tasks\", \"mr-2\", 3, \"ngStyle\"], [1, \"px-2\", \"py-1\", \"rounded-full\", \"text-xs\", \"font-medium\", \"text-white\", 3, \"ngStyle\"], [1, \"space-y-3\", \"max-h-64\", \"overflow-y-auto\"], [\"class\", \"p-3 rounded-lg border transition-all hover:shadow-md\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"items-center\", \"p-3\", \"rounded-lg\", \"mb-4\", 3, \"ngStyle\"], [1, \"w-8\", \"h-8\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\", \"text-white\", \"mr-3\", 3, \"ngStyle\"], [1, \"fas\", \"fa-user\", \"text-sm\"], [1, \"text-xs\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [1, \"font-semibold\", 3, \"ngStyle\"], [1, \"p-3\", \"rounded-lg\", \"border\", \"transition-all\", \"hover:shadow-md\", 3, \"ngClass\"], [1, \"flex\", \"items-start\", \"justify-between\", \"mb-2\"], [1, \"text-sm\", \"font-semibold\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"flex-1\", \"mr-2\"], [1, \"px-2\", \"py-1\", \"rounded-full\", \"text-xs\", \"font-medium\", 3, \"ngClass\"], [1, \"text-xs\", \"text-[#6d6870]/80\", \"dark:text-[#a0a0a0]/80\"], [1, \"p-4\", \"border-t\", \"border-[#ff6b69]/20\", \"dark:border-[#ff3b30]/20\"], [1, \"bg-[#ff6b69]/10\", \"dark:bg-[#ff3b30]/10\", \"border-l-4\", \"border-[#ff6b69]\", \"dark:border-[#ff3b30]\", \"rounded-lg\", \"p-4\"], [1, \"text-[#ff6b69]\", \"dark:text-[#ff3b30]\", \"mr-3\", \"text-lg\"], [1, \"fas\", \"fa-exclamation-triangle\"], [1, \"mb-6\"], [1, \"bg-white\", \"dark:bg-[#1a1a1a]\", \"rounded-xl\", \"shadow-lg\", \"dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)]\", \"p-6\", \"border\", \"border-[#8b5a9f]/20\", \"dark:border-[#00f7ff]/20\"], [1, \"text-lg\", \"font-bold\", \"text-[#8b5a9f]\", \"dark:text-[#00f7ff]\", \"mb-4\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-magic\", \"mr-2\"], [1, \"space-y-4\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-[#8b5a9f]\", \"dark:text-[#00f7ff]\", \"mb-2\"], [1, \"relative\"], [1, \"fas\", \"fa-lightbulb\", \"text-[#8b5a9f]\", \"dark:text-[#00f7ff]\"], [\"type\", \"text\", \"id\", \"projectTitle\", \"placeholder\", \"Ex: Site e-commerce, Application mobile, Syst\\u00E8me de gestion...\", 1, \"w-full\", \"pl-10\", \"pr-4\", \"py-3\", \"bg-[#f0f4f8]\", \"dark:bg-[#0a0a0a]\", \"border\", \"border-[#8b5a9f]/20\", \"dark:border-[#00f7ff]/20\", \"rounded-lg\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"placeholder-[#6d6870]/50\", \"dark:placeholder-[#a0a0a0]/50\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-[#8b5a9f]\", \"dark:focus:ring-[#00f7ff]\", \"focus:border-transparent\", \"transition-all\", 3, \"ngModel\", \"disabled\", \"ngModelChange\"], [1, \"mt-2\", \"text-xs\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"flex\", \"items-center\"], [1, \"w-full\", \"bg-gradient-to-r\", \"from-[#8b5a9f]\", \"to-[#4a00e0]\", \"dark:from-[#00f7ff]\", \"dark:to-[#8b5a9f]\", \"text-white\", \"px-6\", \"py-3\", \"rounded-lg\", \"font-medium\", \"transition-all\", \"duration-300\", \"hover:scale-105\", \"shadow-lg\", \"disabled:opacity-50\", \"disabled:cursor-not-allowed\", \"disabled:hover:scale-100\", 3, \"disabled\", \"click\"], [\"class\", \"inline-block w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2\", 4, \"ngIf\"], [\"class\", \"fas fa-magic mr-2\", 4, \"ngIf\"], [1, \"inline-block\", \"w-4\", \"h-4\", \"border-2\", \"border-white/30\", \"border-t-white\", \"rounded-full\", \"animate-spin\", \"mr-2\"], [1, \"inline-block\", \"w-4\", \"h-4\", \"border-2\", \"border-white/30\", \"border-t-white\", \"rounded-full\", \"animate-spin\"], [1, \"fas\", \"fa-paper-plane\"]],\n      template: function AiChatComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1, 2);\n          i0.ɵɵtemplate(3, AiChatComponent_div_3_Template, 3, 2, \"div\", 3);\n          i0.ɵɵtemplate(4, AiChatComponent_div_4_Template, 9, 0, \"div\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(5, AiChatComponent_div_5_Template, 54, 7, \"div\", 5);\n          i0.ɵɵtemplate(6, AiChatComponent_div_6_Template, 7, 1, \"div\", 6);\n          i0.ɵɵelementStart(7, \"div\", 7);\n          i0.ɵɵtemplate(8, AiChatComponent_div_8_Template, 20, 7, \"div\", 8);\n          i0.ɵɵelementStart(9, \"div\", 9)(10, \"div\", 10)(11, \"div\", 11)(12, \"div\", 12);\n          i0.ɵɵelement(13, \"i\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"input\", 14);\n          i0.ɵɵlistener(\"ngModelChange\", function AiChatComponent_Template_input_ngModelChange_14_listener($event) {\n            return ctx.userQuestion = $event;\n          })(\"keyup.enter\", function AiChatComponent_Template_input_keyup_enter_14_listener() {\n            return ctx.askQuestion();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(15, \"button\", 15);\n          i0.ɵɵlistener(\"click\", function AiChatComponent_Template_button_click_15_listener() {\n            return ctx.askQuestion();\n          });\n          i0.ɵɵtemplate(16, AiChatComponent_span_16_Template, 1, 0, \"span\", 16);\n          i0.ɵɵtemplate(17, AiChatComponent_i_17_Template, 1, 0, \"i\", 17);\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.messages);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isGenerating || ctx.isAskingQuestion);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.generatedContent);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.generatedContent);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngModel\", ctx.userQuestion)(\"disabled\", ctx.isAskingQuestion);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", ctx.isAskingQuestion || !ctx.userQuestion.trim());\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isAskingQuestion);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isAskingQuestion);\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i4.NgStyle, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgModel],\n      styles: [\".ai-chat-container[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.card[_ngcontent-%COMP%] {\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.card-body[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  flex: 1;\\n  overflow: hidden;\\n}\\n\\n.chat-messages[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  max-height: 500px; \\n\\n}\\n\\n.message[_ngcontent-%COMP%] {\\n  display: flex;\\n  margin-bottom: 15px;\\n}\\n\\n.user-message[_ngcontent-%COMP%] {\\n  justify-content: flex-end;\\n}\\n\\n.assistant-message[_ngcontent-%COMP%] {\\n  justify-content: flex-start;\\n}\\n\\n\\n\\n.message-avatar[_ngcontent-%COMP%] {\\n  width: 36px;\\n  height: 36px;\\n  font-size: 1rem;\\n  color: white;\\n  flex-shrink: 0;\\n}\\n\\n\\n\\n.message-bubble[_ngcontent-%COMP%] {\\n  max-width: 80%;\\n  word-wrap: break-word;\\n  position: relative;\\n  transition: all 0.3s ease;\\n}\\n\\n.message-bubble[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n}\\n\\n.user-bubble[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #007bff, #6610f2);\\n  color: white;\\n  border-top-right-radius: 0 !important;\\n}\\n\\n.assistant-bubble[_ngcontent-%COMP%] {\\n  background-color: white;\\n  color: #343a40;\\n  border-top-left-radius: 0 !important;\\n}\\n\\n\\n\\n.message-time[_ngcontent-%COMP%] {\\n  font-size: 0.7rem;\\n  opacity: 0.7;\\n}\\n\\n.user-bubble[_ngcontent-%COMP%]   .message-time[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.8) !important;\\n}\\n\\n\\n\\n.typing-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  height: 8px;\\n  width: 8px;\\n  background-color: #343a40;\\n  border-radius: 50%;\\n  display: inline-block;\\n  margin-right: 5px;\\n  animation: _ngcontent-%COMP%_typing 1s infinite ease-in-out;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(1) {\\n  animation-delay: 0s;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(2) {\\n  animation-delay: 0.2s;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(3) {\\n  animation-delay: 0.4s;\\n  margin-right: 0;\\n}\\n\\n@keyframes _ngcontent-%COMP%_typing {\\n  0% {\\n    transform: translateY(0px);\\n    opacity: 0.4;\\n  }\\n  50% {\\n    transform: translateY(-5px);\\n    opacity: 0.8;\\n  }\\n  100% {\\n    transform: translateY(0px);\\n    opacity: 0.4;\\n  }\\n}\\n\\n\\n\\n.spin[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_spin 1.5s infinite linear;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n\\n.generated-content[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  border-radius: 0.25rem;\\n  max-height: 800px; \\n\\n  overflow-y: auto;\\n  width: 100%; \\n\\n}\\n\\n\\n\\n.generated-header[_ngcontent-%COMP%] {\\n  border-left: 5px solid #007bff;\\n}\\n\\n\\n\\n.generated-content[_ngcontent-%COMP%]   .module-card[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease;\\n  border: none;\\n  height: 100%;\\n  border-radius: 16px;\\n  overflow: hidden;\\n  position: relative;\\n}\\n\\n.generated-content[_ngcontent-%COMP%]   .module-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-8px);\\n  box-shadow: 0 20px 40px rgba(0,0,0,0.15) !important;\\n}\\n\\n\\n\\n.module-ribbon[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 15px;\\n  right: -35px;\\n  transform: rotate(45deg);\\n  width: 150px;\\n  text-align: center;\\n  padding: 5px;\\n  font-size: 0.8rem;\\n  font-weight: bold;\\n  color: white;\\n  z-index: 10;\\n  box-shadow: 0 3px 10px rgba(0,0,0,0.1);\\n}\\n\\n.generated-content[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%] {\\n  border-radius: 0;\\n  font-weight: 600;\\n  padding: 30px 15px;\\n  text-align: center;\\n}\\n\\n\\n\\n.module-icon-large[_ngcontent-%COMP%] {\\n  width: 70px;\\n  height: 70px;\\n  font-size: 2rem;\\n  margin: 0 auto;\\n  position: relative;\\n  z-index: 5;\\n}\\n\\n\\n\\n.member-avatar[_ngcontent-%COMP%] {\\n  width: 45px;\\n  height: 45px;\\n  font-size: 1.2rem;\\n  flex-shrink: 0;\\n}\\n\\n\\n\\n.assignation-badge[_ngcontent-%COMP%] {\\n  box-shadow: 0 3px 10px rgba(0,0,0,0.05);\\n  transition: all 0.3s ease;\\n}\\n\\n.assignation-badge[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 5px 15px rgba(0,0,0,0.08);\\n}\\n\\n\\n\\n.description-box[_ngcontent-%COMP%] {\\n  border-left: 4px solid #e9ecef;\\n  font-style: italic;\\n}\\n\\n\\n\\n.task-list[_ngcontent-%COMP%] {\\n  max-height: 300px;\\n  overflow-y: auto;\\n  padding-right: 5px;\\n  margin-bottom: 10px;\\n}\\n\\n.task-header[_ngcontent-%COMP%] {\\n  position: sticky;\\n  top: 0;\\n  background-color: white;\\n  z-index: 5;\\n}\\n\\n.task-item[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease;\\n  border-left: 4px solid transparent;\\n  background-color: white;\\n}\\n\\n.task-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-3px);\\n  box-shadow: 0 10px 20px rgba(0,0,0,0.1) !important;\\n}\\n\\n.high-priority[_ngcontent-%COMP%] {\\n  border-left-color: #dc3545;\\n}\\n\\n.medium-priority[_ngcontent-%COMP%] {\\n  border-left-color: #ffc107;\\n}\\n\\n.low-priority[_ngcontent-%COMP%] {\\n  border-left-color: #17a2b8;\\n}\\n\\n.task-title[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #343a40;\\n}\\n\\n.task-description[_ngcontent-%COMP%] {\\n  padding-top: 8px;\\n  border-top: 1px dashed #dee2e6;\\n  margin-top: 5px;\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from { opacity: 0; transform: translateY(10px); }\\n  to { opacity: 1; transform: translateY(0); }\\n}\\n\\n\\n\\n.create-tasks-card[_ngcontent-%COMP%] {\\n  background: linear-gradient(120deg, rgba(255,255,255,1), rgba(248,249,250,1));\\n  border-left: 5px solid #28a745;\\n}\\n\\n\\n\\n.step-circle[_ngcontent-%COMP%] {\\n  width: 30px;\\n  height: 30px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-weight: bold;\\n  flex-shrink: 0;\\n}\\n\\n\\n\\n.create-button[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease;\\n  box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3) !important;\\n}\\n\\n.create-button[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 10px 25px rgba(40, 167, 69, 0.4) !important;\\n}\\n\\n.accordion-button[_ngcontent-%COMP%]:not(.collapsed) {\\n  background-color: #e7f1ff;\\n  color: #0d6efd;\\n}\\n\\n.accordion-button[_ngcontent-%COMP%]:focus {\\n  box-shadow: none;\\n  border-color: rgba(0,0,0,.125);\\n}\\n\\n.chat-input[_ngcontent-%COMP%] {\\n  background-color: #fff;\\n  border-top: 1px solid #dee2e6;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["finalize", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "message_r8", "content", "ɵɵsanitizeHtml", "ɵɵtextInterpolate1", "ctx_r10", "getCurrentTime", "ctx_r11", "ɵɵtemplate", "AiChatComponent_div_3_div_1_Template", "AiChatComponent_div_3_div_2_Template", "role", "ɵɵpureFunction1", "_c0", "ctx_r17", "getColorForIndex", "i_r16", "getGradientForIndex", "_c1", "entity_r15", "assignedTo", "ɵɵpureFunction3", "_c2", "task_r20", "priority", "title", "_c3", "description", "AiChatComponent_div_5_div_15_div_15_Template", "AiChatComponent_div_5_div_15_div_23_Template", "ctx_r14", "getIconForModule", "name", "ɵɵtextInterpolate", "_c4", "tasks", "length", "AiChatComponent_div_5_div_15_Template", "ɵɵlistener", "AiChatComponent_div_5_Template_button_click_48_listener", "ɵɵrestoreView", "_r23", "ctx_r22", "ɵɵnextContext", "ɵɵresetView", "createTasks", "ctx_r3", "generatedContent", "projectTitle", "ɵɵtextInterpolate2", "entities", "countTasks", "team", "members", "ctx_r4", "error", "AiChatComponent_div_8_Template_input_ngModelChange_12_listener", "$event", "_r27", "ctx_r26", "AiChatComponent_div_8_Template_button_click_16_listener", "ctx_r28", "generateTasks", "AiChatComponent_div_8_span_17_Template", "AiChatComponent_div_8_i_18_Template", "ctx_r5", "isGenerating", "trim", "AiChatComponent", "constructor", "aiService", "taskService", "notificationService", "messages", "userQuestion", "isAskingQuestion", "ngOnInit", "push", "showError", "memberCount", "effectiveMemberCount", "Math", "max", "showWarning", "console", "log", "loadingMessageIndex", "teamMembers", "map", "memberId", "index", "id", "generateProjectTasks", "pipe", "subscribe", "next", "result", "handleGenerationError", "showSuccess", "message", "messageIndex", "errorDetails", "askQuestion", "question", "projectContext", "askProjectQuestion", "response", "_id", "createdCount", "totalTasks", "member", "userId", "memberNameToIdMap", "for<PERSON>ach", "entity", "assignedMemberId", "memberName", "randomMemberIndex", "floor", "random", "taskData", "task", "status", "teamId", "createTask", "reduce", "total", "gradients", "colors", "moduleName", "toLowerCase", "includes", "now", "Date", "hours", "getHours", "toString", "padStart", "minutes", "getMinutes", "ɵɵdirectiveInject", "i1", "AiService", "i2", "TaskService", "i3", "NotificationService", "selectors", "inputs", "decls", "vars", "consts", "template", "AiChatComponent_Template", "rf", "ctx", "AiChatComponent_div_3_Template", "AiChatComponent_div_4_Template", "AiChatComponent_div_5_Template", "AiChatComponent_div_6_Template", "AiChatComponent_div_8_Template", "AiChatComponent_Template_input_ngModelChange_14_listener", "AiChatComponent_Template_input_keyup_enter_14_listener", "AiChatComponent_Template_button_click_15_listener", "AiChatComponent_span_16_Template", "AiChatComponent_i_17_Template"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\admin\\equipes\\ai-chat\\ai-chat.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\admin\\equipes\\ai-chat\\ai-chat.component.html"], "sourcesContent": ["import { Component, Input, OnInit } from '@angular/core';\nimport { AiService } from 'src/app/services/ai.service';\nimport { TaskService } from 'src/app/services/task.service';\nimport { NotificationService } from 'src/app/services/notification.service';\nimport { Equipe } from 'src/app/models/equipe.model';\nimport { Task } from 'src/app/models/task.model';\nimport { finalize } from 'rxjs/operators';\n@Component({\n  selector: 'app-ai-chat',\n  templateUrl: './ai-chat.component.html',\n  styleUrls: ['./ai-chat.component.css'],\n})\nexport class AiChatComponent implements OnInit {\n  @Input() team!: Equipe;\n\n  projectTitle: string = '';\n  isGenerating: boolean = false;\n  generatedContent: any = null;\n  error: string | null = null;\n\n  // Pour le chat\n  messages: { role: 'user' | 'assistant'; content: string }[] = [];\n  userQuestion: string = '';\n  isAskingQuestion: boolean = false;\n\n  constructor(\n    private aiService: AiService,\n    private taskService: TaskService,\n    private notificationService: NotificationService\n  ) {}\n\n  ngOnInit(): void {\n    // Ajouter un message de bienvenue\n    this.messages.push({\n      role: 'assistant',\n      content:\n        'Bonjour ! Je suis votre assistant IA pour la gestion de projet. Entrez le titre de votre projet pour que je puisse vous aider à le diviser en tâches, ou posez-moi une question sur la gestion de projet.',\n    });\n  }\n\n  generateTasks(): void {\n    if (!this.projectTitle.trim()) {\n      this.notificationService.showError('Veuillez entrer un titre de projet');\n      return;\n    }\n\n    // Vérifier si l'équipe a des membres, sinon utiliser un nombre par défaut\n    let memberCount =\n      this.team && this.team.members ? this.team.members.length : 3;\n\n    // S'assurer que nous avons au moins 3 entités pour un projet significatif\n    const effectiveMemberCount = Math.max(memberCount, 3);\n\n    if (memberCount === 0) {\n      this.notificationService.showWarning(\n        \"L'équipe n'a pas de membres. Des tâches génériques seront créées.\"\n      );\n    }\n\n    this.isGenerating = true;\n    this.error = null;\n\n    console.log(\n      `Génération de tâches pour ${effectiveMemberCount} entités (équipe de ${memberCount} membres)`\n    );\n\n    // Ajouter la demande aux messages\n    this.messages.push({\n      role: 'user',\n      content: `Génère des tâches pour le projet \"${this.projectTitle}\" avec exactement ${effectiveMemberCount} entités, une pour chaque membre de l'équipe. Chaque entité doit représenter un module distinct du projet.`,\n    });\n\n    // Ajouter un message de chargement\n    const loadingMessageIndex = this.messages.length;\n    this.messages.push({\n      role: 'assistant',\n      content: 'Je génère des tâches pour votre projet...',\n    });\n\n    // Récupérer les informations sur les membres de l'équipe\n    let teamMembers: any[] = [];\n    if (this.team && this.team.members) {\n      // Utiliser les IDs des membres\n      teamMembers = this.team.members.map((memberId: string, index: number) => {\n        return { id: memberId, name: `Membre ${index + 1}`, role: 'membre' };\n      });\n\n      console.log(\"Informations sur les membres passées à l'IA:\", teamMembers);\n    }\n\n    this.aiService\n      .generateProjectTasks(this.projectTitle, memberCount, teamMembers)\n      .pipe(finalize(() => (this.isGenerating = false)))\n      .subscribe({\n        next: (result: any) => {\n          if (!result || !result.entities || result.entities.length === 0) {\n            console.error(\"Résultat invalide reçu de l'API:\", result);\n            this.handleGenerationError(\n              loadingMessageIndex,\n              'Format de réponse invalide'\n            );\n            return;\n          }\n\n          this.generatedContent = result;\n\n          // Remplacer le message de chargement par la réponse\n          this.messages[loadingMessageIndex] = {\n            role: 'assistant',\n            content: `J'ai généré ${\n              result.entities.length\n            } entités pour votre projet \"${\n              result.projectTitle\n            }\" avec un total de ${this.countTasks(result)} tâches.`,\n          };\n\n          this.notificationService.showSuccess('Tâches générées avec succès');\n        },\n        error: (error: any) => {\n          console.error('Erreur lors de la génération des tâches:', error);\n          this.handleGenerationError(\n            loadingMessageIndex,\n            error.message || 'Erreur inconnue'\n          );\n        },\n      });\n  }\n\n  // Méthode pour gérer les erreurs de génération\n  private handleGenerationError(\n    messageIndex: number,\n    errorDetails: string\n  ): void {\n    this.error = 'Impossible de générer les tâches. Veuillez réessayer.';\n\n    // Remplacer le message de chargement par le message d'erreur\n    this.messages[messageIndex] = {\n      role: 'assistant',\n      content:\n        \"Désolé, je n'ai pas pu générer les tâches. Veuillez réessayer avec un titre de projet différent.\",\n    };\n\n    this.notificationService.showError(\n      'Erreur lors de la génération des tâches: ' + errorDetails\n    );\n  }\n\n  askQuestion(): void {\n    if (!this.userQuestion.trim()) {\n      return;\n    }\n\n    const question = this.userQuestion.trim();\n    this.userQuestion = '';\n    this.isAskingQuestion = true;\n\n    // Ajouter la question aux messages\n    this.messages.push({\n      role: 'user',\n      content: question,\n    });\n\n    const projectContext = {\n      title:\n        this.projectTitle ||\n        (this.generatedContent ? this.generatedContent.projectTitle : ''),\n      description:\n        \"Projet géré par l'équipe \" + (this.team ? this.team.name : ''),\n    };\n\n    this.aiService\n      .askProjectQuestion(question, projectContext)\n      .pipe(finalize(() => (this.isAskingQuestion = false)))\n      .subscribe({\n        next: (response: string) => {\n          // Ajouter la réponse aux messages\n          this.messages.push({\n            role: 'assistant',\n            content: response,\n          });\n        },\n        error: (error: any) => {\n          console.error(\"Erreur lors de la demande à l'IA:\", error);\n\n          // Ajouter l'erreur aux messages\n          this.messages.push({\n            role: 'assistant',\n            content:\n              \"Désolé, je n'ai pas pu répondre à votre question. Veuillez réessayer.\",\n          });\n\n          this.notificationService.showError(\n            \"Erreur lors de la communication avec l'IA\"\n          );\n        },\n      });\n  }\n\n  createTasks(): void {\n    if (!this.generatedContent || !this.team || !this.team._id) {\n      this.notificationService.showError(\n        'Aucune tâche générée ou équipe invalide'\n      );\n      return;\n    }\n\n    let createdCount = 0;\n    const totalTasks = this.countTasks(this.generatedContent);\n\n    // Vérifier si l'équipe a des membres\n    if (!this.team.members || this.team.members.length === 0) {\n      this.notificationService.showError(\n        \"L'équipe n'a pas de membres pour assigner les tâches\"\n      );\n      return;\n    }\n\n    // Préparer la liste des membres de l'équipe\n    const teamMembers = this.team.members.map((member) => {\n      return typeof member === 'string' ? member : (member as any).userId;\n    });\n\n    // Créer un mapping des noms de membres vers leurs IDs\n    const memberNameToIdMap: { [key: string]: string } = {};\n    teamMembers.forEach((memberId, index) => {\n      memberNameToIdMap[`Membre ${index + 1}`] = memberId;\n    });\n\n    console.log(\n      \"Membres de l'équipe disponibles pour l'assignation:\",\n      teamMembers\n    );\n    console.log(\n      'Mapping des noms de membres vers leurs IDs:',\n      memberNameToIdMap\n    );\n\n    // Pour chaque entité\n    this.generatedContent.entities.forEach((entity: any) => {\n      // Déterminer le membre assigné à cette entité\n      let assignedMemberId: string | undefined;\n\n      // Si l'IA a suggéré une assignation\n      if (entity.assignedTo) {\n        // Essayer de trouver l'ID du membre à partir du nom suggéré\n        const memberName = entity.assignedTo;\n        if (memberNameToIdMap[memberName]) {\n          assignedMemberId = memberNameToIdMap[memberName];\n          console.log(\n            `Assignation suggérée par l'IA: Entité \"${entity.name}\" assignée à \"${memberName}\" (ID: ${assignedMemberId})`\n          );\n        } else {\n          // Si le nom n'est pas trouvé, assigner aléatoirement\n          const randomMemberIndex = Math.floor(\n            Math.random() * teamMembers.length\n          );\n          assignedMemberId = teamMembers[randomMemberIndex];\n          console.log(\n            `Nom de membre \"${memberName}\" non trouvé, assignation aléatoire à l'index ${randomMemberIndex}`\n          );\n        }\n      } else {\n        // Si pas d'assignation suggérée, assigner aléatoirement\n        const randomMemberIndex = Math.floor(\n          Math.random() * teamMembers.length\n        );\n        assignedMemberId = teamMembers[randomMemberIndex];\n        console.log(\n          `Pas d'assignation suggérée, assignation aléatoire à l'index ${randomMemberIndex}`\n        );\n      }\n\n      // Pour chaque tâche dans l'entité\n      entity.tasks.forEach((taskData: any) => {\n        const task: Task = {\n          title: taskData.title,\n          description: `[${entity.name}] ${taskData.description}`,\n          status: taskData.status || 'todo',\n          priority: taskData.priority || 'medium',\n          teamId: this.team._id || '',\n          // Utiliser l'ID du membre assigné à l'entité\n          assignedTo: assignedMemberId,\n        };\n\n        this.taskService.createTask(task).subscribe({\n          next: () => {\n            createdCount++;\n            if (createdCount === totalTasks) {\n              this.notificationService.showSuccess(\n                `${createdCount} tâches créées avec succès et assignées aux membres de l'équipe`\n              );\n              // Réinitialiser après création\n              this.generatedContent = null;\n              this.projectTitle = '';\n            }\n          },\n          error: (error) => {\n            console.error('Erreur lors de la création de la tâche:', error);\n            this.notificationService.showError(\n              'Erreur lors de la création des tâches'\n            );\n          },\n        });\n      });\n    });\n  }\n\n  countTasks(content: any): number {\n    if (!content || !content.entities) return 0;\n\n    return content.entities.reduce((total: number, entity: any) => {\n      return total + (entity.tasks ? entity.tasks.length : 0);\n    }, 0);\n  }\n\n  // Méthode pour obtenir un dégradé de couleur basé sur l'index\n  getGradientForIndex(index: number): string {\n    // Liste de dégradés prédéfinis\n    const gradients = [\n      'linear-gradient(45deg, #007bff, #6610f2)', // Bleu-Violet\n      'linear-gradient(45deg, #11998e, #38ef7d)', // Vert\n      'linear-gradient(45deg, #FC5C7D, #6A82FB)', // Rose-Bleu\n      'linear-gradient(45deg, #FF8008, #FFC837)', // Orange-Jaune\n      'linear-gradient(45deg, #8E2DE2, #4A00E0)', // Violet\n      'linear-gradient(45deg, #2193b0, #6dd5ed)', // Bleu clair\n      'linear-gradient(45deg, #373B44, #4286f4)', // Gris-Bleu\n      'linear-gradient(45deg, #834d9b, #d04ed6)', // Violet-Rose\n      'linear-gradient(45deg, #0cebeb, #20e3b2, #29ffc6)', // Turquoise\n    ];\n\n    // Utiliser le modulo pour s'assurer que nous ne dépassons pas le tableau\n    return gradients[index % gradients.length];\n  }\n\n  // Méthode pour obtenir une couleur unique basée sur l'index\n  getColorForIndex(index: number): string {\n    // Liste de couleurs prédéfinies\n    const colors = [\n      '#007bff', // Bleu\n      '#11998e', // Vert\n      '#FC5C7D', // Rose\n      '#FF8008', // Orange\n      '#8E2DE2', // Violet\n      '#2193b0', // Bleu clair\n      '#373B44', // Gris foncé\n      '#834d9b', // Violet\n      '#0cebeb', // Turquoise\n    ];\n\n    // Utiliser le modulo pour s'assurer que nous ne dépassons pas le tableau\n    return colors[index % colors.length];\n  }\n\n  // Méthode pour obtenir une icône en fonction du nom du module\n  getIconForModule(moduleName: string): string {\n    // Convertir le nom du module en minuscules pour faciliter la comparaison\n    const name = moduleName.toLowerCase();\n\n    // Mapper les noms de modules courants à des icônes Bootstrap\n    if (\n      name.includes('crud') ||\n      name.includes('api') ||\n      name.includes('données') ||\n      name.includes('base')\n    ) {\n      return 'bi-database-fill';\n    } else if (\n      name.includes('interface') ||\n      name.includes('ui') ||\n      name.includes('front') ||\n      name.includes('utilisateur')\n    ) {\n      return 'bi-window';\n    } else if (\n      name.includes('déploiement') ||\n      name.includes('serveur') ||\n      name.includes('cloud')\n    ) {\n      return 'bi-cloud-arrow-up-fill';\n    } else if (\n      name.includes('test') ||\n      name.includes('qualité') ||\n      name.includes('qa')\n    ) {\n      return 'bi-bug-fill';\n    } else if (name.includes('sécurité') || name.includes('auth')) {\n      return 'bi-shield-lock-fill';\n    } else if (name.includes('paiement') || name.includes('transaction')) {\n      return 'bi-credit-card-fill';\n    } else if (\n      name.includes('utilisateur') ||\n      name.includes('user') ||\n      name.includes('profil')\n    ) {\n      return 'bi-person-fill';\n    } else if (name.includes('doc') || name.includes('documentation')) {\n      return 'bi-file-text-fill';\n    } else if (name.includes('mobile') || name.includes('app')) {\n      return 'bi-phone-fill';\n    } else if (name.includes('backend') || name.includes('serveur')) {\n      return 'bi-server';\n    } else if (\n      name.includes('analytics') ||\n      name.includes('statistique') ||\n      name.includes('seo')\n    ) {\n      return 'bi-graph-up';\n    }\n\n    // Icône par défaut si aucune correspondance n'est trouvée\n    return 'bi-code-square';\n  }\n\n  // Méthode pour obtenir l'heure actuelle au format HH:MM\n  getCurrentTime(): string {\n    const now = new Date();\n    const hours = now.getHours().toString().padStart(2, '0');\n    const minutes = now.getMinutes().toString().padStart(2, '0');\n    return `${hours}:${minutes}`;\n  }\n}\n", "<div class=\"w-full\">\n  <!-- Messages du chat -->\n  <div class=\"p-6 max-h-96 overflow-y-auto\" #chatContainer>\n    <div *ngFor=\"let message of messages; let i = index\" class=\"mb-4\">\n      <!-- Message utilisateur -->\n      <div *ngIf=\"message.role === 'user'\" class=\"flex justify-end\">\n        <div class=\"flex items-end space-x-2 max-w-xs lg:max-w-md\">\n          <div\n            class=\"bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea] text-white p-3 rounded-lg rounded-br-none shadow-lg\"\n          >\n            <p class=\"text-sm\" [innerHTML]=\"message.content\"></p>\n            <div class=\"text-xs text-white/80 mt-1 text-right\">\n              Vous • {{ getCurrentTime() }}\n            </div>\n          </div>\n          <div\n            class=\"w-8 h-8 bg-gradient-to-br from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea] rounded-full flex items-center justify-center\"\n          >\n            <i class=\"fas fa-user text-white text-sm\"></i>\n          </div>\n        </div>\n      </div>\n\n      <!-- Message assistant -->\n      <div *ngIf=\"message.role === 'assistant'\" class=\"flex justify-start\">\n        <div class=\"flex items-end space-x-2 max-w-xs lg:max-w-md\">\n          <div\n            class=\"w-8 h-8 bg-gradient-to-br from-[#8b5a9f] to-[#4a00e0] dark:from-[#00f7ff] dark:to-[#8b5a9f] rounded-full flex items-center justify-center\"\n          >\n            <i class=\"fas fa-robot text-white text-sm\"></i>\n          </div>\n          <div\n            class=\"bg-[#f0f4f8] dark:bg-[#0a0a0a] border border-[#8b5a9f]/20 dark:border-[#00f7ff]/20 p-3 rounded-lg rounded-bl-none shadow-lg\"\n          >\n            <p\n              class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0]\"\n              [innerHTML]=\"message.content\"\n            ></p>\n            <div class=\"text-xs text-[#6d6870]/60 dark:text-[#a0a0a0]/60 mt-1\">\n              Assistant IA • {{ getCurrentTime() }}\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Indicateur de chargement -->\n    <div\n      *ngIf=\"isGenerating || isAskingQuestion\"\n      class=\"flex justify-start mb-4\"\n    >\n      <div class=\"flex items-end space-x-2\">\n        <div\n          class=\"w-8 h-8 bg-gradient-to-br from-[#8b5a9f] to-[#4a00e0] dark:from-[#00f7ff] dark:to-[#8b5a9f] rounded-full flex items-center justify-center\"\n        >\n          <i class=\"fas fa-robot text-white text-sm\"></i>\n        </div>\n        <div\n          class=\"bg-[#f0f4f8] dark:bg-[#0a0a0a] border border-[#8b5a9f]/20 dark:border-[#00f7ff]/20 p-3 rounded-lg rounded-bl-none shadow-lg\"\n        >\n          <div class=\"flex space-x-1\">\n            <div\n              class=\"w-2 h-2 bg-[#8b5a9f] dark:bg-[#00f7ff] rounded-full animate-bounce\"\n            ></div>\n            <div\n              class=\"w-2 h-2 bg-[#8b5a9f] dark:bg-[#00f7ff] rounded-full animate-bounce\"\n              style=\"animation-delay: 0.1s\"\n            ></div>\n            <div\n              class=\"w-2 h-2 bg-[#8b5a9f] dark:bg-[#00f7ff] rounded-full animate-bounce\"\n              style=\"animation-delay: 0.2s\"\n            ></div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Résultats générés -->\n  <div\n    *ngIf=\"generatedContent\"\n    class=\"border-t border-[#8b5a9f]/20 dark:border-[#00f7ff]/20\"\n  >\n    <!-- Header du projet généré -->\n    <div\n      class=\"p-6 bg-gradient-to-r from-[#8b5a9f]/10 to-[#4a00e0]/10 dark:from-[#00f7ff]/10 dark:to-[#8b5a9f]/10\"\n    >\n      <div class=\"flex flex-col lg:flex-row lg:items-center lg:justify-between\">\n        <div class=\"mb-4 lg:mb-0\">\n          <h3\n            class=\"text-xl font-bold text-[#8b5a9f] dark:text-[#00f7ff] mb-2 flex items-center\"\n          >\n            <i class=\"fas fa-project-diagram mr-2\"></i>\n            Plan du projet \"{{ generatedContent.projectTitle }}\"\n          </h3>\n          <p\n            class=\"text-[#6d6870] dark:text-[#a0a0a0] text-sm flex items-center\"\n          >\n            <i class=\"fas fa-info-circle mr-2\"></i>\n            {{ generatedContent.entities.length }} modules générés avec\n            {{ countTasks(generatedContent) }} tâches au total\n          </p>\n        </div>\n        <div\n          class=\"bg-[#8b5a9f]/20 dark:bg-[#00f7ff]/20 text-[#8b5a9f] dark:text-[#00f7ff] px-4 py-2 rounded-full text-sm font-medium\"\n        >\n          <i class=\"fas fa-users mr-2\"></i>\n          {{ team && team.members ? team.members.length : 0 }} membres\n        </div>\n      </div>\n    </div>\n\n    <!-- Grille des modules -->\n    <div class=\"p-6\">\n      <div class=\"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6\">\n        <div\n          *ngFor=\"let entity of generatedContent.entities; let i = index\"\n          class=\"group relative\"\n        >\n          <!-- Badge numéro de module -->\n          <div\n            class=\"absolute -top-2 -right-2 z-10 w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-bold shadow-lg\"\n            [ngStyle]=\"{ background: getGradientForIndex(i) }\"\n          >\n            {{ i + 1 }}\n          </div>\n\n          <div\n            class=\"bg-white dark:bg-[#1a1a1a] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] overflow-hidden border border-[#8b5a9f]/20 dark:border-[#00f7ff]/20 hover:shadow-xl transition-all duration-300 hover:-translate-y-1\"\n          >\n            <!-- Header du module -->\n            <div\n              class=\"p-6 text-white relative overflow-hidden\"\n              [ngStyle]=\"{ background: getGradientForIndex(i) }\"\n            >\n              <div\n                class=\"absolute top-0 right-0 w-20 h-20 rounded-full bg-white/10 -mr-10 -mt-10\"\n              ></div>\n              <div class=\"relative z-10\">\n                <div\n                  class=\"w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mb-4\"\n                >\n                  <i\n                    class=\"fas\"\n                    [ngClass]=\"getIconForModule(entity.name)\"\n                    class=\"text-xl\"\n                  ></i>\n                </div>\n                <h4 class=\"text-lg font-bold\">{{ entity.name }}</h4>\n              </div>\n            </div>\n\n            <div class=\"p-6\">\n              <!-- Description -->\n              <div\n                class=\"bg-[#f0f4f8] dark:bg-[#0a0a0a] border-l-4 rounded-lg p-3 mb-4\"\n                [ngStyle]=\"{ 'border-color': getColorForIndex(i) }\"\n              >\n                <p class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0]\">\n                  {{ entity.description }}\n                </p>\n              </div>\n\n              <!-- Assignation -->\n              <div\n                *ngIf=\"entity.assignedTo\"\n                class=\"flex items-center p-3 rounded-lg mb-4\"\n                [ngStyle]=\"{ background: getColorForIndex(i) + '10' }\"\n              >\n                <div\n                  class=\"w-8 h-8 rounded-full flex items-center justify-center text-white mr-3\"\n                  [ngStyle]=\"{ background: getGradientForIndex(i) }\"\n                >\n                  <i class=\"fas fa-user text-sm\"></i>\n                </div>\n                <div>\n                  <div class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0]\">\n                    Responsable\n                  </div>\n                  <div\n                    class=\"font-semibold\"\n                    [ngStyle]=\"{ color: getColorForIndex(i) }\"\n                  >\n                    {{ entity.assignedTo }}\n                  </div>\n                </div>\n              </div>\n\n              <!-- Header des tâches -->\n              <div\n                class=\"flex items-center justify-between mb-4 pb-2 border-b border-[#8b5a9f]/10 dark:border-[#00f7ff]/10\"\n              >\n                <h5\n                  class=\"text-sm font-semibold text-[#6d6870] dark:text-[#a0a0a0] flex items-center\"\n                >\n                  <i\n                    class=\"fas fa-tasks mr-2\"\n                    [ngStyle]=\"{ color: getColorForIndex(i) }\"\n                  ></i>\n                  Tâches à réaliser\n                </h5>\n                <span\n                  class=\"px-2 py-1 rounded-full text-xs font-medium text-white\"\n                  [ngStyle]=\"{ background: getGradientForIndex(i) }\"\n                >\n                  {{ entity.tasks.length }} tâches\n                </span>\n              </div>\n\n              <!-- Liste des tâches -->\n              <div class=\"space-y-3 max-h-64 overflow-y-auto\">\n                <div\n                  *ngFor=\"let task of entity.tasks; let j = index\"\n                  class=\"p-3 rounded-lg border transition-all hover:shadow-md\"\n                  [ngClass]=\"{\n                    'border-[#ff6b69]/20 bg-[#ff6b69]/5':\n                      task.priority === 'high',\n                    'border-[#ffa726]/20 bg-[#ffa726]/5':\n                      task.priority === 'medium',\n                    'border-[#42a5f5]/20 bg-[#42a5f5]/5':\n                      task.priority === 'low'\n                  }\"\n                >\n                  <!-- Titre et priorité -->\n                  <div class=\"flex items-start justify-between mb-2\">\n                    <h6\n                      class=\"text-sm font-semibold text-[#6d6870] dark:text-[#a0a0a0] flex-1 mr-2\"\n                    >\n                      {{ task.title }}\n                    </h6>\n                    <span\n                      class=\"px-2 py-1 rounded-full text-xs font-medium\"\n                      [ngClass]=\"{\n                        'bg-[#ff6b69] text-white': task.priority === 'high',\n                        'bg-[#ffa726] text-white': task.priority === 'medium',\n                        'bg-[#42a5f5] text-white': task.priority === 'low'\n                      }\"\n                    >\n                      {{\n                        task.priority === \"high\"\n                          ? \"Haute\"\n                          : task.priority === \"medium\"\n                          ? \"Moyenne\"\n                          : \"Basse\"\n                      }}\n                    </span>\n                  </div>\n\n                  <!-- Description -->\n                  <p class=\"text-xs text-[#6d6870]/80 dark:text-[#a0a0a0]/80\">\n                    {{ task.description }}\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Section de création de tâches -->\n    <div class=\"p-6 border-t border-[#8b5a9f]/20 dark:border-[#00f7ff]/20\">\n      <div\n        class=\"bg-gradient-to-r from-[#00ff9d]/10 to-[#38ef7d]/10 dark:from-[#00f7ff]/10 dark:to-[#00ff9d]/10 rounded-xl p-6\"\n      >\n        <div class=\"grid grid-cols-1 lg:grid-cols-3 gap-6 items-center\">\n          <!-- Étapes -->\n          <div class=\"lg:col-span-2 space-y-4\">\n            <h4\n              class=\"text-xl font-bold text-[#00ff9d] dark:text-[#00f7ff] mb-4 flex items-center\"\n            >\n              <i class=\"fas fa-check-circle mr-2\"></i>\n              Plan de projet prêt à être implémenté\n            </h4>\n\n            <!-- Étape 1 -->\n            <div class=\"flex items-center\">\n              <div\n                class=\"w-8 h-8 bg-[#00ff9d] dark:bg-[#00f7ff] text-white rounded-full flex items-center justify-center font-bold text-sm mr-4\"\n              >\n                1\n              </div>\n              <div>\n                <h6 class=\"font-semibold text-[#6d6870] dark:text-[#a0a0a0]\">\n                  Création des tâches\n                </h6>\n                <p class=\"text-sm text-[#6d6870]/80 dark:text-[#a0a0a0]/80\">\n                  {{ countTasks(generatedContent) }} tâches seront créées dans\n                  le système\n                </p>\n              </div>\n            </div>\n\n            <!-- Étape 2 -->\n            <div class=\"flex items-center\">\n              <div\n                class=\"w-8 h-8 bg-[#4f5fad] dark:bg-[#8b5a9f] text-white rounded-full flex items-center justify-center font-bold text-sm mr-4\"\n              >\n                2\n              </div>\n              <div>\n                <h6 class=\"font-semibold text-[#6d6870] dark:text-[#a0a0a0]\">\n                  Assignation aux membres\n                </h6>\n                <p class=\"text-sm text-[#6d6870]/80 dark:text-[#a0a0a0]/80\">\n                  Les tâches seront assignées aux\n                  {{ team && team.members ? team.members.length : 0 }} membres\n                  de l'équipe\n                </p>\n              </div>\n            </div>\n\n            <!-- Étape 3 -->\n            <div class=\"flex items-center\">\n              <div\n                class=\"w-8 h-8 bg-[#42a5f5] text-white rounded-full flex items-center justify-center font-bold text-sm mr-4\"\n              >\n                3\n              </div>\n              <div>\n                <h6 class=\"font-semibold text-[#6d6870] dark:text-[#a0a0a0]\">\n                  Suivi du projet\n                </h6>\n                <p class=\"text-sm text-[#6d6870]/80 dark:text-[#a0a0a0]/80\">\n                  Vous pourrez suivre l'avancement dans le tableau de bord des\n                  tâches\n                </p>\n              </div>\n            </div>\n          </div>\n\n          <!-- Bouton de création -->\n          <div class=\"text-center\">\n            <button\n              (click)=\"createTasks()\"\n              class=\"bg-gradient-to-r from-[#00ff9d] to-[#38ef7d] dark:from-[#00f7ff] dark:to-[#00ff9d] text-white px-8 py-4 rounded-xl font-bold text-lg transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-[0_0_25px_rgba(0,255,157,0.4)] dark:hover:shadow-[0_0_25px_rgba(0,247,255,0.4)]\"\n            >\n              <i class=\"fas fa-plus-circle mr-2\"></i>\n              Créer les tâches\n            </button>\n            <p class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0] mt-2\">\n              <i class=\"fas fa-info-circle mr-1\"></i>\n              Cette action est irréversible\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Message d'erreur -->\n  <div\n    *ngIf=\"error\"\n    class=\"p-4 border-t border-[#ff6b69]/20 dark:border-[#ff3b30]/20\"\n  >\n    <div\n      class=\"bg-[#ff6b69]/10 dark:bg-[#ff3b30]/10 border-l-4 border-[#ff6b69] dark:border-[#ff3b30] rounded-lg p-4\"\n    >\n      <div class=\"flex items-center\">\n        <div class=\"text-[#ff6b69] dark:text-[#ff3b30] mr-3 text-lg\">\n          <i class=\"fas fa-exclamation-triangle\"></i>\n        </div>\n        <div class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0]\">\n          {{ error }}\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Zone de saisie -->\n  <div\n    class=\"p-6 border-t border-[#8b5a9f]/20 dark:border-[#00f7ff]/20 bg-[#f0f4f8] dark:bg-[#0a0a0a]\"\n  >\n    <!-- Générateur de tâches -->\n    <div *ngIf=\"!generatedContent\" class=\"mb-6\">\n      <div\n        class=\"bg-white dark:bg-[#1a1a1a] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] p-6 border border-[#8b5a9f]/20 dark:border-[#00f7ff]/20\"\n      >\n        <h4\n          class=\"text-lg font-bold text-[#8b5a9f] dark:text-[#00f7ff] mb-4 flex items-center\"\n        >\n          <i class=\"fas fa-magic mr-2\"></i>\n          Générer des tâches avec l'IA\n        </h4>\n\n        <div class=\"space-y-4\">\n          <div>\n            <label\n              class=\"block text-sm font-medium text-[#8b5a9f] dark:text-[#00f7ff] mb-2\"\n            >\n              Titre de votre projet\n            </label>\n            <div class=\"relative\">\n              <div\n                class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\"\n              >\n                <i\n                  class=\"fas fa-lightbulb text-[#8b5a9f] dark:text-[#00f7ff]\"\n                ></i>\n              </div>\n              <input\n                type=\"text\"\n                id=\"projectTitle\"\n                class=\"w-full pl-10 pr-4 py-3 bg-[#f0f4f8] dark:bg-[#0a0a0a] border border-[#8b5a9f]/20 dark:border-[#00f7ff]/20 rounded-lg text-[#6d6870] dark:text-[#a0a0a0] placeholder-[#6d6870]/50 dark:placeholder-[#a0a0a0]/50 focus:outline-none focus:ring-2 focus:ring-[#8b5a9f] dark:focus:ring-[#00f7ff] focus:border-transparent transition-all\"\n                placeholder=\"Ex: Site e-commerce, Application mobile, Système de gestion...\"\n                [(ngModel)]=\"projectTitle\"\n                [disabled]=\"isGenerating\"\n              />\n            </div>\n            <p\n              class=\"mt-2 text-xs text-[#6d6870] dark:text-[#a0a0a0] flex items-center\"\n            >\n              <i class=\"fas fa-info-circle mr-2\"></i>\n              L'IA générera\n              {{ team && team.members ? team.members.length : 3 }} modules, un\n              pour chaque membre de l'équipe.\n            </p>\n          </div>\n\n          <button\n            (click)=\"generateTasks()\"\n            [disabled]=\"isGenerating || !projectTitle.trim()\"\n            class=\"w-full bg-gradient-to-r from-[#8b5a9f] to-[#4a00e0] dark:from-[#00f7ff] dark:to-[#8b5a9f] text-white px-6 py-3 rounded-lg font-medium transition-all duration-300 hover:scale-105 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100\"\n          >\n            <span\n              *ngIf=\"isGenerating\"\n              class=\"inline-block w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2\"\n            ></span>\n            <i *ngIf=\"!isGenerating\" class=\"fas fa-magic mr-2\"></i>\n            {{ isGenerating ? \"Génération en cours...\" : \"Générer des tâches\" }}\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Chat avec l'IA -->\n    <div\n      class=\"bg-white dark:bg-[#1a1a1a] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] p-4 border border-[#8b5a9f]/20 dark:border-[#00f7ff]/20\"\n    >\n      <div class=\"flex space-x-3\">\n        <div class=\"relative flex-1\">\n          <div\n            class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\"\n          >\n            <i\n              class=\"fas fa-comment-dots text-[#8b5a9f] dark:text-[#00f7ff]\"\n            ></i>\n          </div>\n          <input\n            type=\"text\"\n            class=\"w-full pl-10 pr-4 py-3 bg-[#f0f4f8] dark:bg-[#0a0a0a] border border-[#8b5a9f]/20 dark:border-[#00f7ff]/20 rounded-lg text-[#6d6870] dark:text-[#a0a0a0] placeholder-[#6d6870]/50 dark:placeholder-[#a0a0a0]/50 focus:outline-none focus:ring-2 focus:ring-[#8b5a9f] dark:focus:ring-[#00f7ff] focus:border-transparent transition-all\"\n            placeholder=\"Posez une question sur la gestion de projet...\"\n            [(ngModel)]=\"userQuestion\"\n            (keyup.enter)=\"askQuestion()\"\n            [disabled]=\"isAskingQuestion\"\n          />\n        </div>\n        <button\n          (click)=\"askQuestion()\"\n          [disabled]=\"isAskingQuestion || !userQuestion.trim()\"\n          class=\"bg-gradient-to-r from-[#8b5a9f] to-[#4a00e0] dark:from-[#00f7ff] dark:to-[#8b5a9f] text-white px-4 py-3 rounded-lg font-medium transition-all duration-300 hover:scale-105 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100\"\n        >\n          <span\n            *ngIf=\"isAskingQuestion\"\n            class=\"inline-block w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin\"\n          ></span>\n          <i *ngIf=\"!isAskingQuestion\" class=\"fas fa-paper-plane\"></i>\n        </button>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AAMA,SAASA,QAAQ,QAAQ,gBAAgB;;;;;;;;;ICDnCC,EAAA,CAAAC,cAAA,cAA8D;IAKxDD,EAAA,CAAAE,SAAA,YAAqD;IACrDF,EAAA,CAAAC,cAAA,cAAmD;IACjDD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAERJ,EAAA,CAAAC,cAAA,cAEC;IACCD,EAAA,CAAAE,SAAA,YAA8C;IAChDF,EAAA,CAAAI,YAAA,EAAM;;;;;IATeJ,EAAA,CAAAK,SAAA,GAA6B;IAA7BL,EAAA,CAAAM,UAAA,cAAAC,UAAA,CAAAC,OAAA,EAAAR,EAAA,CAAAS,cAAA,CAA6B;IAE9CT,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAU,kBAAA,kBAAAC,OAAA,CAAAC,cAAA,QACF;;;;;IAWNZ,EAAA,CAAAC,cAAA,cAAqE;IAK/DD,EAAA,CAAAE,SAAA,YAA+C;IACjDF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,cAEC;IACCD,EAAA,CAAAE,SAAA,YAGK;IACLF,EAAA,CAAAC,cAAA,cAAmE;IACjED,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAJJJ,EAAA,CAAAK,SAAA,GAA6B;IAA7BL,EAAA,CAAAM,UAAA,cAAAC,UAAA,CAAAC,OAAA,EAAAR,EAAA,CAAAS,cAAA,CAA6B;IAG7BT,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAU,kBAAA,0BAAAG,OAAA,CAAAD,cAAA,QACF;;;;;IArCRZ,EAAA,CAAAC,cAAA,cAAkE;IAEhED,EAAA,CAAAc,UAAA,IAAAC,oCAAA,kBAgBM;IAGNf,EAAA,CAAAc,UAAA,IAAAE,oCAAA,kBAmBM;IACRhB,EAAA,CAAAI,YAAA,EAAM;;;;IAvCEJ,EAAA,CAAAK,SAAA,GAA6B;IAA7BL,EAAA,CAAAM,UAAA,SAAAC,UAAA,CAAAU,IAAA,YAA6B;IAmB7BjB,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAM,UAAA,SAAAC,UAAA,CAAAU,IAAA,iBAAkC;;;;;IAuB1CjB,EAAA,CAAAC,cAAA,cAGC;IAKKD,EAAA,CAAAE,SAAA,YAA+C;IACjDF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,cAEC;IAEGD,EAAA,CAAAE,SAAA,cAEO;IASTF,EAAA,CAAAI,YAAA,EAAM;;;;;;;;;;;;;;;IA4FFJ,EAAA,CAAAC,cAAA,cAIC;IAKGD,EAAA,CAAAE,SAAA,YAAmC;IACrCF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,UAAK;IAEDD,EAAA,CAAAG,MAAA,oBACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;;;IAjBRJ,EAAA,CAAAM,UAAA,YAAAN,EAAA,CAAAkB,eAAA,IAAAC,GAAA,EAAAC,OAAA,CAAAC,gBAAA,CAAAC,KAAA,UAAsD;IAIpDtB,EAAA,CAAAK,SAAA,GAAkD;IAAlDL,EAAA,CAAAM,UAAA,YAAAN,EAAA,CAAAkB,eAAA,IAAAC,GAAA,EAAAC,OAAA,CAAAG,mBAAA,CAAAD,KAAA,GAAkD;IAUhDtB,EAAA,CAAAK,SAAA,GAA0C;IAA1CL,EAAA,CAAAM,UAAA,YAAAN,EAAA,CAAAkB,eAAA,IAAAM,GAAA,EAAAJ,OAAA,CAAAC,gBAAA,CAAAC,KAAA,GAA0C;IAE1CtB,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAU,kBAAA,MAAAe,UAAA,CAAAC,UAAA,MACF;;;;;;;;;;;;;;;;;;;IA2BF1B,EAAA,CAAAC,cAAA,cAWC;IAMKD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,eAOC;IACCD,EAAA,CAAAG,MAAA,GAOF;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAITJ,EAAA,CAAAC,cAAA,YAA4D;IAC1DD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;IArCJJ,EAAA,CAAAM,UAAA,YAAAN,EAAA,CAAA2B,eAAA,IAAAC,GAAA,EAAAC,QAAA,CAAAC,QAAA,aAAAD,QAAA,CAAAC,QAAA,eAAAD,QAAA,CAAAC,QAAA,YAOE;IAOE9B,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAU,kBAAA,MAAAmB,QAAA,CAAAE,KAAA,MACF;IAGE/B,EAAA,CAAAK,SAAA,GAIE;IAJFL,EAAA,CAAAM,UAAA,YAAAN,EAAA,CAAA2B,eAAA,IAAAK,GAAA,EAAAH,QAAA,CAAAC,QAAA,aAAAD,QAAA,CAAAC,QAAA,eAAAD,QAAA,CAAAC,QAAA,YAIE;IAEF9B,EAAA,CAAAK,SAAA,GAOF;IAPEL,EAAA,CAAAU,kBAAA,MAAAmB,QAAA,CAAAC,QAAA,wBAAAD,QAAA,CAAAC,QAAA,yCAOF;IAKA9B,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAU,kBAAA,MAAAmB,QAAA,CAAAI,WAAA,MACF;;;;;;;;;;IAxIVjC,EAAA,CAAAC,cAAA,cAGC;IAMGD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAENJ,EAAA,CAAAC,cAAA,cAEC;IAMGD,EAAA,CAAAE,SAAA,cAEO;IACPF,EAAA,CAAAC,cAAA,cAA2B;IAIvBD,EAAA,CAAAE,SAAA,YAIK;IACPF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,aAA8B;IAAAD,EAAA,CAAAG,MAAA,IAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAIxDJ,EAAA,CAAAC,cAAA,eAAiB;IAOXD,EAAA,CAAAG,MAAA,IACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAINJ,EAAA,CAAAc,UAAA,KAAAoB,4CAAA,mBAsBM;IAGNlC,EAAA,CAAAC,cAAA,eAEC;IAIGD,EAAA,CAAAE,SAAA,aAGK;IACLF,EAAA,CAAAG,MAAA,0CACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,gBAGC;IACCD,EAAA,CAAAG,MAAA,IACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAITJ,EAAA,CAAAC,cAAA,eAAgD;IAC9CD,EAAA,CAAAc,UAAA,KAAAqB,4CAAA,mBAyCM;IACRnC,EAAA,CAAAI,YAAA,EAAM;;;;;;IAnIRJ,EAAA,CAAAK,SAAA,GAAkD;IAAlDL,EAAA,CAAAM,UAAA,YAAAN,EAAA,CAAAkB,eAAA,KAAAC,GAAA,EAAAiB,OAAA,CAAAb,mBAAA,CAAAD,KAAA,GAAkD;IAElDtB,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAU,kBAAA,MAAAY,KAAA,UACF;IAQItB,EAAA,CAAAK,SAAA,GAAkD;IAAlDL,EAAA,CAAAM,UAAA,YAAAN,EAAA,CAAAkB,eAAA,KAAAC,GAAA,EAAAiB,OAAA,CAAAb,mBAAA,CAAAD,KAAA,GAAkD;IAW5CtB,EAAA,CAAAK,SAAA,GAAyC;IAAzCL,EAAA,CAAAM,UAAA,YAAA8B,OAAA,CAAAC,gBAAA,CAAAZ,UAAA,CAAAa,IAAA,EAAyC;IAIftC,EAAA,CAAAK,SAAA,GAAiB;IAAjBL,EAAA,CAAAuC,iBAAA,CAAAd,UAAA,CAAAa,IAAA,CAAiB;IAQ/CtC,EAAA,CAAAK,SAAA,GAAmD;IAAnDL,EAAA,CAAAM,UAAA,YAAAN,EAAA,CAAAkB,eAAA,KAAAsB,GAAA,EAAAJ,OAAA,CAAAf,gBAAA,CAAAC,KAAA,GAAmD;IAGjDtB,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAU,kBAAA,MAAAe,UAAA,CAAAQ,WAAA,MACF;IAKCjC,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAAM,UAAA,SAAAmB,UAAA,CAAAC,UAAA,CAAuB;IAgCpB1B,EAAA,CAAAK,SAAA,GAA0C;IAA1CL,EAAA,CAAAM,UAAA,YAAAN,EAAA,CAAAkB,eAAA,KAAAM,GAAA,EAAAY,OAAA,CAAAf,gBAAA,CAAAC,KAAA,GAA0C;IAM5CtB,EAAA,CAAAK,SAAA,GAAkD;IAAlDL,EAAA,CAAAM,UAAA,YAAAN,EAAA,CAAAkB,eAAA,KAAAC,GAAA,EAAAiB,OAAA,CAAAb,mBAAA,CAAAD,KAAA,GAAkD;IAElDtB,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAU,kBAAA,MAAAe,UAAA,CAAAgB,KAAA,CAAAC,MAAA,kBACF;IAMmB1C,EAAA,CAAAK,SAAA,GAAiB;IAAjBL,EAAA,CAAAM,UAAA,YAAAmB,UAAA,CAAAgB,KAAA,CAAiB;;;;;;IArIlDzC,EAAA,CAAAC,cAAA,cAGC;IAUSD,EAAA,CAAAE,SAAA,YAA2C;IAC3CF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,YAEC;IACCD,EAAA,CAAAE,SAAA,YAAuC;IACvCF,EAAA,CAAAG,MAAA,GAEF;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAENJ,EAAA,CAAAC,cAAA,eAEC;IACCD,EAAA,CAAAE,SAAA,aAAiC;IACjCF,EAAA,CAAAG,MAAA,IACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAKVJ,EAAA,CAAAC,cAAA,eAAiB;IAEbD,EAAA,CAAAc,UAAA,KAAA6B,qCAAA,oBA6IM;IACR3C,EAAA,CAAAI,YAAA,EAAM;IAIRJ,EAAA,CAAAC,cAAA,eAAuE;IAU7DD,EAAA,CAAAE,SAAA,aAAwC;IACxCF,EAAA,CAAAG,MAAA,wEACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAGLJ,EAAA,CAAAC,cAAA,eAA+B;IAI3BD,EAAA,CAAAG,MAAA,WACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,WAAK;IAEDD,EAAA,CAAAG,MAAA,uCACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,aAA4D;IAC1DD,EAAA,CAAAG,MAAA,IAEF;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAKRJ,EAAA,CAAAC,cAAA,eAA+B;IAI3BD,EAAA,CAAAG,MAAA,WACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,WAAK;IAEDD,EAAA,CAAAG,MAAA,iCACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,aAA4D;IAC1DD,EAAA,CAAAG,MAAA,IAGF;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAKRJ,EAAA,CAAAC,cAAA,eAA+B;IAI3BD,EAAA,CAAAG,MAAA,WACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,WAAK;IAEDD,EAAA,CAAAG,MAAA,yBACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,aAA4D;IAC1DD,EAAA,CAAAG,MAAA,kFAEF;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAMVJ,EAAA,CAAAC,cAAA,eAAyB;IAErBD,EAAA,CAAA4C,UAAA,mBAAAC,wDAAA;MAAA7C,EAAA,CAAA8C,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAhD,EAAA,CAAAiD,aAAA;MAAA,OAASjD,EAAA,CAAAkD,WAAA,CAAAF,OAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IAGvBnD,EAAA,CAAAE,SAAA,aAAuC;IACvCF,EAAA,CAAAG,MAAA,oCACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,aAA2D;IACzDD,EAAA,CAAAE,SAAA,aAAuC;IACvCF,EAAA,CAAAG,MAAA,4CACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;IA1PJJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAU,kBAAA,uBAAA0C,MAAA,CAAAC,gBAAA,CAAAC,YAAA,QACF;IAKEtD,EAAA,CAAAK,SAAA,GAEF;IAFEL,EAAA,CAAAuD,kBAAA,MAAAH,MAAA,CAAAC,gBAAA,CAAAG,QAAA,CAAAd,MAAA,2CAAAU,MAAA,CAAAK,UAAA,CAAAL,MAAA,CAAAC,gBAAA,4BAEF;IAMArD,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAU,kBAAA,MAAA0C,MAAA,CAAAM,IAAA,IAAAN,MAAA,CAAAM,IAAA,CAAAC,OAAA,GAAAP,MAAA,CAAAM,IAAA,CAAAC,OAAA,CAAAjB,MAAA,kBACF;IAQqB1C,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAAM,UAAA,YAAA8C,MAAA,CAAAC,gBAAA,CAAAG,QAAA,CAA8B;IA2KzCxD,EAAA,CAAAK,SAAA,IAEF;IAFEL,EAAA,CAAAU,kBAAA,MAAA0C,MAAA,CAAAK,UAAA,CAAAL,MAAA,CAAAC,gBAAA,gEAEF;IAgBErD,EAAA,CAAAK,SAAA,GAGF;IAHEL,EAAA,CAAAU,kBAAA,gDAAA0C,MAAA,CAAAM,IAAA,IAAAN,MAAA,CAAAM,IAAA,CAAAC,OAAA,GAAAP,MAAA,CAAAM,IAAA,CAAAC,OAAA,CAAAjB,MAAA,mCAGF;;;;;IA2Cd1C,EAAA,CAAAC,cAAA,cAGC;IAMOD,EAAA,CAAAE,SAAA,aAA2C;IAC7CF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,cAAwD;IACtDD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAU,kBAAA,MAAAkD,MAAA,CAAAC,KAAA,MACF;;;;;IA4DI7D,EAAA,CAAAE,SAAA,gBAGQ;;;;;IACRF,EAAA,CAAAE,SAAA,aAAuD;;;;;;IAtD/DF,EAAA,CAAAC,cAAA,eAA4C;IAOtCD,EAAA,CAAAE,SAAA,aAAiC;IACjCF,EAAA,CAAAG,MAAA,oDACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAELJ,EAAA,CAAAC,cAAA,eAAuB;IAKjBD,EAAA,CAAAG,MAAA,8BACF;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACRJ,EAAA,CAAAC,cAAA,eAAsB;IAIlBD,EAAA,CAAAE,SAAA,cAEK;IACPF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,kBAOE;IAFAD,EAAA,CAAA4C,UAAA,2BAAAkB,+DAAAC,MAAA;MAAA/D,EAAA,CAAA8C,aAAA,CAAAkB,IAAA;MAAA,MAAAC,OAAA,GAAAjE,EAAA,CAAAiD,aAAA;MAAA,OAAAjD,EAAA,CAAAkD,WAAA,CAAAe,OAAA,CAAAX,YAAA,GAAAS,MAAA;IAAA,EAA0B;IAL5B/D,EAAA,CAAAI,YAAA,EAOE;IAEJJ,EAAA,CAAAC,cAAA,cAEC;IACCD,EAAA,CAAAE,SAAA,aAAuC;IACvCF,EAAA,CAAAG,MAAA,IAGF;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAGNJ,EAAA,CAAAC,cAAA,mBAIC;IAHCD,EAAA,CAAA4C,UAAA,mBAAAsB,wDAAA;MAAAlE,EAAA,CAAA8C,aAAA,CAAAkB,IAAA;MAAA,MAAAG,OAAA,GAAAnE,EAAA,CAAAiD,aAAA;MAAA,OAASjD,EAAA,CAAAkD,WAAA,CAAAiB,OAAA,CAAAC,aAAA,EAAe;IAAA,EAAC;IAIzBpE,EAAA,CAAAc,UAAA,KAAAuD,sCAAA,oBAGQ;IACRrE,EAAA,CAAAc,UAAA,KAAAwD,mCAAA,iBAAuD;IACvDtE,EAAA,CAAAG,MAAA,IACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;IAzBHJ,EAAA,CAAAK,SAAA,IAA0B;IAA1BL,EAAA,CAAAM,UAAA,YAAAiE,MAAA,CAAAjB,YAAA,CAA0B,aAAAiB,MAAA,CAAAC,YAAA;IAQ5BxE,EAAA,CAAAK,SAAA,GAGF;IAHEL,EAAA,CAAAU,kBAAA,8BAAA6D,MAAA,CAAAb,IAAA,IAAAa,MAAA,CAAAb,IAAA,CAAAC,OAAA,GAAAY,MAAA,CAAAb,IAAA,CAAAC,OAAA,CAAAjB,MAAA,2DAGF;IAKA1C,EAAA,CAAAK,SAAA,GAAiD;IAAjDL,EAAA,CAAAM,UAAA,aAAAiE,MAAA,CAAAC,YAAA,KAAAD,MAAA,CAAAjB,YAAA,CAAAmB,IAAA,GAAiD;IAI9CzE,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAM,UAAA,SAAAiE,MAAA,CAAAC,YAAA,CAAkB;IAGjBxE,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAM,UAAA,UAAAiE,MAAA,CAAAC,YAAA,CAAmB;IACvBxE,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAU,kBAAA,MAAA6D,MAAA,CAAAC,YAAA,iFACF;;;;;IAgCAxE,EAAA,CAAAE,SAAA,gBAGQ;;;;;IACRF,EAAA,CAAAE,SAAA,aAA4D;;;ADtctE,OAAM,MAAOwE,eAAe;EAa1BC,YACUC,SAAoB,EACpBC,WAAwB,EACxBC,mBAAwC;IAFxC,KAAAF,SAAS,GAATA,SAAS;IACT,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,mBAAmB,GAAnBA,mBAAmB;IAb7B,KAAAxB,YAAY,GAAW,EAAE;IACzB,KAAAkB,YAAY,GAAY,KAAK;IAC7B,KAAAnB,gBAAgB,GAAQ,IAAI;IAC5B,KAAAQ,KAAK,GAAkB,IAAI;IAE3B;IACA,KAAAkB,QAAQ,GAAsD,EAAE;IAChE,KAAAC,YAAY,GAAW,EAAE;IACzB,KAAAC,gBAAgB,GAAY,KAAK;EAM9B;EAEHC,QAAQA,CAAA;IACN;IACA,IAAI,CAACH,QAAQ,CAACI,IAAI,CAAC;MACjBlE,IAAI,EAAE,WAAW;MACjBT,OAAO,EACL;KACH,CAAC;EACJ;EAEA4D,aAAaA,CAAA;IACX,IAAI,CAAC,IAAI,CAACd,YAAY,CAACmB,IAAI,EAAE,EAAE;MAC7B,IAAI,CAACK,mBAAmB,CAACM,SAAS,CAAC,oCAAoC,CAAC;MACxE;;IAGF;IACA,IAAIC,WAAW,GACb,IAAI,CAAC3B,IAAI,IAAI,IAAI,CAACA,IAAI,CAACC,OAAO,GAAG,IAAI,CAACD,IAAI,CAACC,OAAO,CAACjB,MAAM,GAAG,CAAC;IAE/D;IACA,MAAM4C,oBAAoB,GAAGC,IAAI,CAACC,GAAG,CAACH,WAAW,EAAE,CAAC,CAAC;IAErD,IAAIA,WAAW,KAAK,CAAC,EAAE;MACrB,IAAI,CAACP,mBAAmB,CAACW,WAAW,CAClC,mEAAmE,CACpE;;IAGH,IAAI,CAACjB,YAAY,GAAG,IAAI;IACxB,IAAI,CAACX,KAAK,GAAG,IAAI;IAEjB6B,OAAO,CAACC,GAAG,CACT,6BAA6BL,oBAAoB,uBAAuBD,WAAW,WAAW,CAC/F;IAED;IACA,IAAI,CAACN,QAAQ,CAACI,IAAI,CAAC;MACjBlE,IAAI,EAAE,MAAM;MACZT,OAAO,EAAE,qCAAqC,IAAI,CAAC8C,YAAY,qBAAqBgC,oBAAoB;KACzG,CAAC;IAEF;IACA,MAAMM,mBAAmB,GAAG,IAAI,CAACb,QAAQ,CAACrC,MAAM;IAChD,IAAI,CAACqC,QAAQ,CAACI,IAAI,CAAC;MACjBlE,IAAI,EAAE,WAAW;MACjBT,OAAO,EAAE;KACV,CAAC;IAEF;IACA,IAAIqF,WAAW,GAAU,EAAE;IAC3B,IAAI,IAAI,CAACnC,IAAI,IAAI,IAAI,CAACA,IAAI,CAACC,OAAO,EAAE;MAClC;MACAkC,WAAW,GAAG,IAAI,CAACnC,IAAI,CAACC,OAAO,CAACmC,GAAG,CAAC,CAACC,QAAgB,EAAEC,KAAa,KAAI;QACtE,OAAO;UAAEC,EAAE,EAAEF,QAAQ;UAAEzD,IAAI,EAAE,UAAU0D,KAAK,GAAG,CAAC,EAAE;UAAE/E,IAAI,EAAE;QAAQ,CAAE;MACtE,CAAC,CAAC;MAEFyE,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEE,WAAW,CAAC;;IAG1E,IAAI,CAACjB,SAAS,CACXsB,oBAAoB,CAAC,IAAI,CAAC5C,YAAY,EAAE+B,WAAW,EAAEQ,WAAW,CAAC,CACjEM,IAAI,CAACpG,QAAQ,CAAC,MAAO,IAAI,CAACyE,YAAY,GAAG,KAAM,CAAC,CAAC,CACjD4B,SAAS,CAAC;MACTC,IAAI,EAAGC,MAAW,IAAI;QACpB,IAAI,CAACA,MAAM,IAAI,CAACA,MAAM,CAAC9C,QAAQ,IAAI8C,MAAM,CAAC9C,QAAQ,CAACd,MAAM,KAAK,CAAC,EAAE;UAC/DgD,OAAO,CAAC7B,KAAK,CAAC,kCAAkC,EAAEyC,MAAM,CAAC;UACzD,IAAI,CAACC,qBAAqB,CACxBX,mBAAmB,EACnB,4BAA4B,CAC7B;UACD;;QAGF,IAAI,CAACvC,gBAAgB,GAAGiD,MAAM;QAE9B;QACA,IAAI,CAACvB,QAAQ,CAACa,mBAAmB,CAAC,GAAG;UACnC3E,IAAI,EAAE,WAAW;UACjBT,OAAO,EAAE,eACP8F,MAAM,CAAC9C,QAAQ,CAACd,MAClB,+BACE4D,MAAM,CAAChD,YACT,sBAAsB,IAAI,CAACG,UAAU,CAAC6C,MAAM,CAAC;SAC9C;QAED,IAAI,CAACxB,mBAAmB,CAAC0B,WAAW,CAAC,6BAA6B,CAAC;MACrE,CAAC;MACD3C,KAAK,EAAGA,KAAU,IAAI;QACpB6B,OAAO,CAAC7B,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;QAChE,IAAI,CAAC0C,qBAAqB,CACxBX,mBAAmB,EACnB/B,KAAK,CAAC4C,OAAO,IAAI,iBAAiB,CACnC;MACH;KACD,CAAC;EACN;EAEA;EACQF,qBAAqBA,CAC3BG,YAAoB,EACpBC,YAAoB;IAEpB,IAAI,CAAC9C,KAAK,GAAG,uDAAuD;IAEpE;IACA,IAAI,CAACkB,QAAQ,CAAC2B,YAAY,CAAC,GAAG;MAC5BzF,IAAI,EAAE,WAAW;MACjBT,OAAO,EACL;KACH;IAED,IAAI,CAACsE,mBAAmB,CAACM,SAAS,CAChC,2CAA2C,GAAGuB,YAAY,CAC3D;EACH;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAAC5B,YAAY,CAACP,IAAI,EAAE,EAAE;MAC7B;;IAGF,MAAMoC,QAAQ,GAAG,IAAI,CAAC7B,YAAY,CAACP,IAAI,EAAE;IACzC,IAAI,CAACO,YAAY,GAAG,EAAE;IACtB,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAE5B;IACA,IAAI,CAACF,QAAQ,CAACI,IAAI,CAAC;MACjBlE,IAAI,EAAE,MAAM;MACZT,OAAO,EAAEqG;KACV,CAAC;IAEF,MAAMC,cAAc,GAAG;MACrB/E,KAAK,EACH,IAAI,CAACuB,YAAY,KAChB,IAAI,CAACD,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAACC,YAAY,GAAG,EAAE,CAAC;MACnErB,WAAW,EACT,2BAA2B,IAAI,IAAI,CAACyB,IAAI,GAAG,IAAI,CAACA,IAAI,CAACpB,IAAI,GAAG,EAAE;KACjE;IAED,IAAI,CAACsC,SAAS,CACXmC,kBAAkB,CAACF,QAAQ,EAAEC,cAAc,CAAC,CAC5CX,IAAI,CAACpG,QAAQ,CAAC,MAAO,IAAI,CAACkF,gBAAgB,GAAG,KAAM,CAAC,CAAC,CACrDmB,SAAS,CAAC;MACTC,IAAI,EAAGW,QAAgB,IAAI;QACzB;QACA,IAAI,CAACjC,QAAQ,CAACI,IAAI,CAAC;UACjBlE,IAAI,EAAE,WAAW;UACjBT,OAAO,EAAEwG;SACV,CAAC;MACJ,CAAC;MACDnD,KAAK,EAAGA,KAAU,IAAI;QACpB6B,OAAO,CAAC7B,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QAEzD;QACA,IAAI,CAACkB,QAAQ,CAACI,IAAI,CAAC;UACjBlE,IAAI,EAAE,WAAW;UACjBT,OAAO,EACL;SACH,CAAC;QAEF,IAAI,CAACsE,mBAAmB,CAACM,SAAS,CAChC,2CAA2C,CAC5C;MACH;KACD,CAAC;EACN;EAEAjC,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACE,gBAAgB,IAAI,CAAC,IAAI,CAACK,IAAI,IAAI,CAAC,IAAI,CAACA,IAAI,CAACuD,GAAG,EAAE;MAC1D,IAAI,CAACnC,mBAAmB,CAACM,SAAS,CAChC,yCAAyC,CAC1C;MACD;;IAGF,IAAI8B,YAAY,GAAG,CAAC;IACpB,MAAMC,UAAU,GAAG,IAAI,CAAC1D,UAAU,CAAC,IAAI,CAACJ,gBAAgB,CAAC;IAEzD;IACA,IAAI,CAAC,IAAI,CAACK,IAAI,CAACC,OAAO,IAAI,IAAI,CAACD,IAAI,CAACC,OAAO,CAACjB,MAAM,KAAK,CAAC,EAAE;MACxD,IAAI,CAACoC,mBAAmB,CAACM,SAAS,CAChC,sDAAsD,CACvD;MACD;;IAGF;IACA,MAAMS,WAAW,GAAG,IAAI,CAACnC,IAAI,CAACC,OAAO,CAACmC,GAAG,CAAEsB,MAAM,IAAI;MACnD,OAAO,OAAOA,MAAM,KAAK,QAAQ,GAAGA,MAAM,GAAIA,MAAc,CAACC,MAAM;IACrE,CAAC,CAAC;IAEF;IACA,MAAMC,iBAAiB,GAA8B,EAAE;IACvDzB,WAAW,CAAC0B,OAAO,CAAC,CAACxB,QAAQ,EAAEC,KAAK,KAAI;MACtCsB,iBAAiB,CAAC,UAAUtB,KAAK,GAAG,CAAC,EAAE,CAAC,GAAGD,QAAQ;IACrD,CAAC,CAAC;IAEFL,OAAO,CAACC,GAAG,CACT,qDAAqD,EACrDE,WAAW,CACZ;IACDH,OAAO,CAACC,GAAG,CACT,6CAA6C,EAC7C2B,iBAAiB,CAClB;IAED;IACA,IAAI,CAACjE,gBAAgB,CAACG,QAAQ,CAAC+D,OAAO,CAAEC,MAAW,IAAI;MACrD;MACA,IAAIC,gBAAoC;MAExC;MACA,IAAID,MAAM,CAAC9F,UAAU,EAAE;QACrB;QACA,MAAMgG,UAAU,GAAGF,MAAM,CAAC9F,UAAU;QACpC,IAAI4F,iBAAiB,CAACI,UAAU,CAAC,EAAE;UACjCD,gBAAgB,GAAGH,iBAAiB,CAACI,UAAU,CAAC;UAChDhC,OAAO,CAACC,GAAG,CACT,0CAA0C6B,MAAM,CAAClF,IAAI,iBAAiBoF,UAAU,UAAUD,gBAAgB,GAAG,CAC9G;SACF,MAAM;UACL;UACA,MAAME,iBAAiB,GAAGpC,IAAI,CAACqC,KAAK,CAClCrC,IAAI,CAACsC,MAAM,EAAE,GAAGhC,WAAW,CAACnD,MAAM,CACnC;UACD+E,gBAAgB,GAAG5B,WAAW,CAAC8B,iBAAiB,CAAC;UACjDjC,OAAO,CAACC,GAAG,CACT,kBAAkB+B,UAAU,iDAAiDC,iBAAiB,EAAE,CACjG;;OAEJ,MAAM;QACL;QACA,MAAMA,iBAAiB,GAAGpC,IAAI,CAACqC,KAAK,CAClCrC,IAAI,CAACsC,MAAM,EAAE,GAAGhC,WAAW,CAACnD,MAAM,CACnC;QACD+E,gBAAgB,GAAG5B,WAAW,CAAC8B,iBAAiB,CAAC;QACjDjC,OAAO,CAACC,GAAG,CACT,+DAA+DgC,iBAAiB,EAAE,CACnF;;MAGH;MACAH,MAAM,CAAC/E,KAAK,CAAC8E,OAAO,CAAEO,QAAa,IAAI;QACrC,MAAMC,IAAI,GAAS;UACjBhG,KAAK,EAAE+F,QAAQ,CAAC/F,KAAK;UACrBE,WAAW,EAAE,IAAIuF,MAAM,CAAClF,IAAI,KAAKwF,QAAQ,CAAC7F,WAAW,EAAE;UACvD+F,MAAM,EAAEF,QAAQ,CAACE,MAAM,IAAI,MAAM;UACjClG,QAAQ,EAAEgG,QAAQ,CAAChG,QAAQ,IAAI,QAAQ;UACvCmG,MAAM,EAAE,IAAI,CAACvE,IAAI,CAACuD,GAAG,IAAI,EAAE;UAC3B;UACAvF,UAAU,EAAE+F;SACb;QAED,IAAI,CAAC5C,WAAW,CAACqD,UAAU,CAACH,IAAI,CAAC,CAAC3B,SAAS,CAAC;UAC1CC,IAAI,EAAEA,CAAA,KAAK;YACTa,YAAY,EAAE;YACd,IAAIA,YAAY,KAAKC,UAAU,EAAE;cAC/B,IAAI,CAACrC,mBAAmB,CAAC0B,WAAW,CAClC,GAAGU,YAAY,iEAAiE,CACjF;cACD;cACA,IAAI,CAAC7D,gBAAgB,GAAG,IAAI;cAC5B,IAAI,CAACC,YAAY,GAAG,EAAE;;UAE1B,CAAC;UACDO,KAAK,EAAGA,KAAK,IAAI;YACf6B,OAAO,CAAC7B,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;YAC/D,IAAI,CAACiB,mBAAmB,CAACM,SAAS,CAChC,uCAAuC,CACxC;UACH;SACD,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEA3B,UAAUA,CAACjD,OAAY;IACrB,IAAI,CAACA,OAAO,IAAI,CAACA,OAAO,CAACgD,QAAQ,EAAE,OAAO,CAAC;IAE3C,OAAOhD,OAAO,CAACgD,QAAQ,CAAC2E,MAAM,CAAC,CAACC,KAAa,EAAEZ,MAAW,KAAI;MAC5D,OAAOY,KAAK,IAAIZ,MAAM,CAAC/E,KAAK,GAAG+E,MAAM,CAAC/E,KAAK,CAACC,MAAM,GAAG,CAAC,CAAC;IACzD,CAAC,EAAE,CAAC,CAAC;EACP;EAEA;EACAnB,mBAAmBA,CAACyE,KAAa;IAC/B;IACA,MAAMqC,SAAS,GAAG,CAChB,0CAA0C,EAC1C,0CAA0C,EAC1C,0CAA0C,EAC1C,0CAA0C,EAC1C,0CAA0C,EAC1C,0CAA0C,EAC1C,0CAA0C,EAC1C,0CAA0C,EAC1C,mDAAmD,CAAE;IAAA,CACtD;IAED;IACA,OAAOA,SAAS,CAACrC,KAAK,GAAGqC,SAAS,CAAC3F,MAAM,CAAC;EAC5C;EAEA;EACArB,gBAAgBA,CAAC2E,KAAa;IAC5B;IACA,MAAMsC,MAAM,GAAG,CACb,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CAAE;IAAA,CACZ;IAED;IACA,OAAOA,MAAM,CAACtC,KAAK,GAAGsC,MAAM,CAAC5F,MAAM,CAAC;EACtC;EAEA;EACAL,gBAAgBA,CAACkG,UAAkB;IACjC;IACA,MAAMjG,IAAI,GAAGiG,UAAU,CAACC,WAAW,EAAE;IAErC;IACA,IACElG,IAAI,CAACmG,QAAQ,CAAC,MAAM,CAAC,IACrBnG,IAAI,CAACmG,QAAQ,CAAC,KAAK,CAAC,IACpBnG,IAAI,CAACmG,QAAQ,CAAC,SAAS,CAAC,IACxBnG,IAAI,CAACmG,QAAQ,CAAC,MAAM,CAAC,EACrB;MACA,OAAO,kBAAkB;KAC1B,MAAM,IACLnG,IAAI,CAACmG,QAAQ,CAAC,WAAW,CAAC,IAC1BnG,IAAI,CAACmG,QAAQ,CAAC,IAAI,CAAC,IACnBnG,IAAI,CAACmG,QAAQ,CAAC,OAAO,CAAC,IACtBnG,IAAI,CAACmG,QAAQ,CAAC,aAAa,CAAC,EAC5B;MACA,OAAO,WAAW;KACnB,MAAM,IACLnG,IAAI,CAACmG,QAAQ,CAAC,aAAa,CAAC,IAC5BnG,IAAI,CAACmG,QAAQ,CAAC,SAAS,CAAC,IACxBnG,IAAI,CAACmG,QAAQ,CAAC,OAAO,CAAC,EACtB;MACA,OAAO,wBAAwB;KAChC,MAAM,IACLnG,IAAI,CAACmG,QAAQ,CAAC,MAAM,CAAC,IACrBnG,IAAI,CAACmG,QAAQ,CAAC,SAAS,CAAC,IACxBnG,IAAI,CAACmG,QAAQ,CAAC,IAAI,CAAC,EACnB;MACA,OAAO,aAAa;KACrB,MAAM,IAAInG,IAAI,CAACmG,QAAQ,CAAC,UAAU,CAAC,IAAInG,IAAI,CAACmG,QAAQ,CAAC,MAAM,CAAC,EAAE;MAC7D,OAAO,qBAAqB;KAC7B,MAAM,IAAInG,IAAI,CAACmG,QAAQ,CAAC,UAAU,CAAC,IAAInG,IAAI,CAACmG,QAAQ,CAAC,aAAa,CAAC,EAAE;MACpE,OAAO,qBAAqB;KAC7B,MAAM,IACLnG,IAAI,CAACmG,QAAQ,CAAC,aAAa,CAAC,IAC5BnG,IAAI,CAACmG,QAAQ,CAAC,MAAM,CAAC,IACrBnG,IAAI,CAACmG,QAAQ,CAAC,QAAQ,CAAC,EACvB;MACA,OAAO,gBAAgB;KACxB,MAAM,IAAInG,IAAI,CAACmG,QAAQ,CAAC,KAAK,CAAC,IAAInG,IAAI,CAACmG,QAAQ,CAAC,eAAe,CAAC,EAAE;MACjE,OAAO,mBAAmB;KAC3B,MAAM,IAAInG,IAAI,CAACmG,QAAQ,CAAC,QAAQ,CAAC,IAAInG,IAAI,CAACmG,QAAQ,CAAC,KAAK,CAAC,EAAE;MAC1D,OAAO,eAAe;KACvB,MAAM,IAAInG,IAAI,CAACmG,QAAQ,CAAC,SAAS,CAAC,IAAInG,IAAI,CAACmG,QAAQ,CAAC,SAAS,CAAC,EAAE;MAC/D,OAAO,WAAW;KACnB,MAAM,IACLnG,IAAI,CAACmG,QAAQ,CAAC,WAAW,CAAC,IAC1BnG,IAAI,CAACmG,QAAQ,CAAC,aAAa,CAAC,IAC5BnG,IAAI,CAACmG,QAAQ,CAAC,KAAK,CAAC,EACpB;MACA,OAAO,aAAa;;IAGtB;IACA,OAAO,gBAAgB;EACzB;EAEA;EACA7H,cAAcA,CAAA;IACZ,MAAM8H,GAAG,GAAG,IAAIC,IAAI,EAAE;IACtB,MAAMC,KAAK,GAAGF,GAAG,CAACG,QAAQ,EAAE,CAACC,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACxD,MAAMC,OAAO,GAAGN,GAAG,CAACO,UAAU,EAAE,CAACH,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC5D,OAAO,GAAGH,KAAK,IAAII,OAAO,EAAE;EAC9B;;;uBAvZWtE,eAAe,EAAA1E,EAAA,CAAAkJ,iBAAA,CAAAC,EAAA,CAAAC,SAAA,GAAApJ,EAAA,CAAAkJ,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAtJ,EAAA,CAAAkJ,iBAAA,CAAAK,EAAA,CAAAC,mBAAA;IAAA;EAAA;;;YAAf9E,eAAe;MAAA+E,SAAA;MAAAC,MAAA;QAAAhG,IAAA;MAAA;MAAAiG,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZ5BhK,EAAA,CAAAC,cAAA,aAAoB;UAGhBD,EAAA,CAAAc,UAAA,IAAAoJ,8BAAA,iBAyCM;UAGNlK,EAAA,CAAAc,UAAA,IAAAqJ,8BAAA,iBA4BM;UACRnK,EAAA,CAAAI,YAAA,EAAM;UAGNJ,EAAA,CAAAc,UAAA,IAAAsJ,8BAAA,kBA6QM;UAGNpK,EAAA,CAAAc,UAAA,IAAAuJ,8BAAA,iBAgBM;UAGNrK,EAAA,CAAAC,cAAA,aAEC;UAECD,EAAA,CAAAc,UAAA,IAAAwJ,8BAAA,kBA2DM;UAGNtK,EAAA,CAAAC,cAAA,aAEC;UAMOD,EAAA,CAAAE,SAAA,aAEK;UACPF,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAC,cAAA,iBAOE;UAHAD,EAAA,CAAA4C,UAAA,2BAAA2H,yDAAAxG,MAAA;YAAA,OAAAkG,GAAA,CAAAjF,YAAA,GAAAjB,MAAA;UAAA,EAA0B,yBAAAyG,uDAAA;YAAA,OACXP,GAAA,CAAArD,WAAA,EAAa;UAAA,EADF;UAJ5B5G,EAAA,CAAAI,YAAA,EAOE;UAEJJ,EAAA,CAAAC,cAAA,kBAIC;UAHCD,EAAA,CAAA4C,UAAA,mBAAA6H,kDAAA;YAAA,OAASR,GAAA,CAAArD,WAAA,EAAa;UAAA,EAAC;UAIvB5G,EAAA,CAAAc,UAAA,KAAA4J,gCAAA,mBAGQ;UACR1K,EAAA,CAAAc,UAAA,KAAA6J,6BAAA,gBAA4D;UAC9D3K,EAAA,CAAAI,YAAA,EAAS;;;UAhdYJ,EAAA,CAAAK,SAAA,GAAa;UAAbL,EAAA,CAAAM,UAAA,YAAA2J,GAAA,CAAAlF,QAAA,CAAa;UA6CnC/E,EAAA,CAAAK,SAAA,GAAsC;UAAtCL,EAAA,CAAAM,UAAA,SAAA2J,GAAA,CAAAzF,YAAA,IAAAyF,GAAA,CAAAhF,gBAAA,CAAsC;UAgCxCjF,EAAA,CAAAK,SAAA,GAAsB;UAAtBL,EAAA,CAAAM,UAAA,SAAA2J,GAAA,CAAA5G,gBAAA,CAAsB;UAgRtBrD,EAAA,CAAAK,SAAA,GAAW;UAAXL,EAAA,CAAAM,UAAA,SAAA2J,GAAA,CAAApG,KAAA,CAAW;UAsBN7D,EAAA,CAAAK,SAAA,GAAuB;UAAvBL,EAAA,CAAAM,UAAA,UAAA2J,GAAA,CAAA5G,gBAAA,CAAuB;UA8ErBrD,EAAA,CAAAK,SAAA,GAA0B;UAA1BL,EAAA,CAAAM,UAAA,YAAA2J,GAAA,CAAAjF,YAAA,CAA0B,aAAAiF,GAAA,CAAAhF,gBAAA;UAO5BjF,EAAA,CAAAK,SAAA,GAAqD;UAArDL,EAAA,CAAAM,UAAA,aAAA2J,GAAA,CAAAhF,gBAAA,KAAAgF,GAAA,CAAAjF,YAAA,CAAAP,IAAA,GAAqD;UAIlDzE,EAAA,CAAAK,SAAA,GAAsB;UAAtBL,EAAA,CAAAM,UAAA,SAAA2J,GAAA,CAAAhF,gBAAA,CAAsB;UAGrBjF,EAAA,CAAAK,SAAA,GAAuB;UAAvBL,EAAA,CAAAM,UAAA,UAAA2J,GAAA,CAAAhF,gBAAA,CAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}