{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@app/services/projets.service\";\nimport * as i3 from \"src/app/services/rendus.service\";\nimport * as i4 from \"src/app/services/authuser.service\";\nimport * as i5 from \"@angular/common\";\nfunction ProjectDetailComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵelement(1, \"div\", 9);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectDetailComponent_div_8_div_18_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 26)(2, \"div\", 27);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 28);\n    i0.ɵɵelement(4, \"path\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(5, \"span\", 30);\n    i0.ɵɵtext(6, \"Document\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"a\", 31);\n    i0.ɵɵtext(8, \" T\\u00E9l\\u00E9charger \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const file_r6 = ctx.$implicit;\n    const ctx_r5 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"href\", ctx_r5.getFileUrl(file_r6), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ProjectDetailComponent_div_8_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"h2\", 22);\n    i0.ɵɵtext(2, \" Fichiers du projet \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 23);\n    i0.ɵɵtemplate(4, ProjectDetailComponent_div_8_div_18_div_4_Template, 9, 1, \"div\", 24);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.projet.fichiers);\n  }\n}\nfunction ProjectDetailComponent_div_8_ng_container_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 32);\n    i0.ɵɵtext(2, \" Projet d\\u00E9j\\u00E0 soumis \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nconst _c0 = function (a1) {\n  return [\"/front/projects/submit\", a1];\n};\nfunction ProjectDetailComponent_div_8_ng_container_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"a\", 33);\n    i0.ɵɵtext(2, \" Soumettre mon projet \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(1, _c0, ctx_r4.projetId));\n  }\n}\nfunction ProjectDetailComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"div\", 11)(2, \"h1\", 12);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 13)(5, \"span\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 14);\n    i0.ɵɵtext(8, \"\\u2022\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\");\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 15)(13, \"div\", 16)(14, \"h2\", 17);\n    i0.ɵɵtext(15, \" Description du projet \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"p\", 18);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(18, ProjectDetailComponent_div_8_div_18_Template, 5, 1, \"div\", 19);\n    i0.ɵɵelementStart(19, \"div\", 20);\n    i0.ɵɵtemplate(20, ProjectDetailComponent_div_8_ng_container_20_Template, 3, 0, \"ng-container\", 21);\n    i0.ɵɵtemplate(21, ProjectDetailComponent_div_8_ng_container_21_Template, 3, 3, \"ng-container\", 21);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.projet == null ? null : ctx_r1.projet.titre);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Groupe: \", ctx_r1.projet == null ? null : ctx_r1.projet.groupe, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"Date limite: \", i0.ɵɵpipeBind2(11, 7, ctx_r1.projet == null ? null : ctx_r1.projet.dateLimite, \"dd/MM/yyyy\"), \"\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r1.projet == null ? null : ctx_r1.projet.description) || \"Aucune description fournie\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.projet == null ? null : ctx_r1.projet.fichiers) && (ctx_r1.projet == null ? null : ctx_r1.projet.fichiers.length) > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hasSubmitted);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.hasSubmitted);\n  }\n}\n// Composant pour afficher les détails d'un projet\nexport class ProjectDetailComponent {\n  constructor(route, router, projetService, rendusService, authService) {\n    this.route = route;\n    this.router = router;\n    this.projetService = projetService;\n    this.rendusService = rendusService;\n    this.authService = authService;\n    this.projetId = '';\n    this.isLoading = true;\n    this.hasSubmitted = false;\n  }\n  ngOnInit() {\n    this.projetId = this.route.snapshot.paramMap.get('id') || '';\n    this.loadProjetDetails();\n    this.checkRenduStatus();\n  }\n  loadProjetDetails() {\n    this.isLoading = true;\n    this.projetService.getProjetById(this.projetId).subscribe({\n      next: projet => {\n        this.projet = projet;\n        this.isLoading = false;\n      },\n      error: err => {\n        console.error('Erreur lors du chargement du projet', err);\n        this.isLoading = false;\n        this.router.navigate(['/projects']);\n      }\n    });\n  }\n  checkRenduStatus() {\n    const etudiantId = this.authService.getCurrentUserId();\n    if (etudiantId) {\n      this.rendusService.checkRenduExists(this.projetId, etudiantId).subscribe({\n        next: exists => {\n          this.hasSubmitted = exists;\n        },\n        error: err => {\n          console.error('Erreur lors de la vérification du rendu', err);\n        }\n      });\n    }\n  }\n  getFileUrl(filePath) {\n    // Extraire uniquement le nom du fichier\n    let fileName = filePath;\n    // Si le chemin contient des slashes ou backslashes, prendre la dernière partie\n    if (filePath.includes('/') || filePath.includes('\\\\')) {\n      const parts = filePath.split(/[\\/\\\\]/);\n      fileName = parts[parts.length - 1];\n    }\n    // Utiliser l'endpoint API spécifique pour le téléchargement\n    return `http://localhost:3000/api/projets/download/${fileName}`;\n  }\n  static {\n    this.ɵfac = function ProjectDetailComponent_Factory(t) {\n      return new (t || ProjectDetailComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.ProjetService), i0.ɵɵdirectiveInject(i3.RendusService), i0.ɵɵdirectiveInject(i4.AuthuserService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProjectDetailComponent,\n      selectors: [[\"app-project-detail\"]],\n      decls: 9,\n      vars: 2,\n      consts: [[1, \"min-h-screen\", \"bg-[#edf1f4]\", \"p-4\", \"md:p-6\"], [1, \"max-w-4xl\", \"mx-auto\"], [1, \"mb-6\"], [\"routerLink\", \"/front/projects\", 1, \"text-[#4f5fad]\", \"hover:text-[#3d4a85]\", \"flex\", \"items-center\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"viewBox\", \"0 0 20 20\", \"fill\", \"currentColor\", 1, \"h-5\", \"w-5\", \"mr-1\"], [\"fill-rule\", \"evenodd\", \"d\", \"M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z\", \"clip-rule\", \"evenodd\"], [\"class\", \"flex justify-center my-8\", 4, \"ngIf\"], [\"class\", \"bg-white rounded-xl shadow-md overflow-hidden\", 4, \"ngIf\"], [1, \"flex\", \"justify-center\", \"my-8\"], [1, \"w-12\", \"h-12\", \"border-4\", \"border-[#4f5fad]/20\", \"border-t-[#4f5fad]\", \"rounded-full\", \"animate-spin\"], [1, \"bg-white\", \"rounded-xl\", \"shadow-md\", \"overflow-hidden\"], [1, \"border-t-4\", \"border-[#4f5fad]\", \"p-6\", \"bg-white\"], [1, \"text-2xl\", \"font-bold\", \"text-[#4f5fad]\"], [1, \"flex\", \"items-center\", \"mt-2\", \"text-sm\", \"text-[#6d6870]\"], [1, \"mx-2\"], [1, \"p-6\"], [1, \"mb-8\"], [1, \"text-lg\", \"font-semibold\", \"text-[#4f5fad]\", \"mb-2\"], [1, \"text-[#6d6870]\"], [\"class\", \"mb-8\", 4, \"ngIf\"], [1, \"flex\", \"justify-end\", \"mt-6\"], [4, \"ngIf\"], [1, \"text-lg\", \"font-semibold\", \"text-[#4f5fad]\", \"mb-4\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"gap-4\"], [\"class\", \"border border-[#bdc6cc] rounded-lg p-4 hover:bg-[#edf1f4] transition-colors\", 4, \"ngFor\", \"ngForOf\"], [1, \"border\", \"border-[#bdc6cc]\", \"rounded-lg\", \"p-4\", \"hover:bg-[#edf1f4]\", \"transition-colors\"], [1, \"flex\", \"items-center\", \"justify-between\"], [1, \"flex\", \"items-center\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-6\", \"w-6\", \"text-[#4f5fad]\", \"mr-2\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"], [1, \"text-[#6d6870]\", \"truncate\"], [\"download\", \"\", 1, \"text-[#4f5fad]\", \"hover:text-[#3d4a85]\", \"text-sm\", \"font-medium\", \"transition-colors\", 3, \"href\"], [1, \"bg-green-100\", \"text-green-800\", \"px-4\", \"py-2\", \"rounded-lg\", \"text-sm\", \"font-medium\"], [1, \"bg-[#4f5fad]\", \"hover:bg-[#3d4a85]\", \"text-white\", \"px-4\", \"py-2\", \"rounded-lg\", \"text-sm\", \"font-medium\", \"transition-colors\", 3, \"routerLink\"]],\n      template: function ProjectDetailComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"a\", 3);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(4, \"svg\", 4);\n          i0.ɵɵelement(5, \"path\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(6, \" Retour aux projets \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(7, ProjectDetailComponent_div_7_Template, 2, 0, \"div\", 6);\n          i0.ɵɵtemplate(8, ProjectDetailComponent_div_8_Template, 22, 10, \"div\", 7);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n        }\n      },\n      dependencies: [i5.NgForOf, i5.NgIf, i1.RouterLink, i5.DatePipe],\n      styles: [\"\\n\\n.project-container[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n  background-color: #fff;\\n  border-radius: 0.5rem;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n\\n.project-header[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n  border-bottom: 1px solid #e5e7eb;\\n  padding-bottom: 1rem;\\n}\\n\\n.project-description[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n}\\n\\n.project-meta[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 1rem;\\n  margin-bottom: 1.5rem;\\n}\\n\\n.project-meta-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInByb2plY3QtZGV0YWlsLmNvbXBvbmVudC5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUEsaURBQWlEO0FBQ2pEO0VBQ0UsZUFBZTtFQUNmLHNCQUFzQjtFQUN0QixxQkFBcUI7RUFDckIsd0NBQXdDO0FBQzFDOztBQUVBO0VBQ0UscUJBQXFCO0VBQ3JCLGdDQUFnQztFQUNoQyxvQkFBb0I7QUFDdEI7O0FBRUE7RUFDRSxxQkFBcUI7QUFDdkI7O0FBRUE7RUFDRSxhQUFhO0VBQ2IsZUFBZTtFQUNmLFNBQVM7RUFDVCxxQkFBcUI7QUFDdkI7O0FBRUE7RUFDRSxhQUFhO0VBQ2IsbUJBQW1CO0VBQ25CLFdBQVc7QUFDYiIsImZpbGUiOiJwcm9qZWN0LWRldGFpbC5jb21wb25lbnQuY3NzIiwic291cmNlc0NvbnRlbnQiOlsiLyogU3R5bGVzIHBvdXIgbGUgY29tcG9zYW50IGRlIGTDqXRhaWwgZGUgcHJvamV0ICovXG4ucHJvamVjdC1jb250YWluZXIge1xuICBwYWRkaW5nOiAxLjVyZW07XG4gIGJhY2tncm91bmQtY29sb3I6ICNmZmY7XG4gIGJvcmRlci1yYWRpdXM6IDAuNXJlbTtcbiAgYm94LXNoYWRvdzogMCAycHggNHB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcbn1cblxuLnByb2plY3QtaGVhZGVyIHtcbiAgbWFyZ2luLWJvdHRvbTogMS41cmVtO1xuICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2U1ZTdlYjtcbiAgcGFkZGluZy1ib3R0b206IDFyZW07XG59XG5cbi5wcm9qZWN0LWRlc2NyaXB0aW9uIHtcbiAgbWFyZ2luLWJvdHRvbTogMS41cmVtO1xufVxuXG4ucHJvamVjdC1tZXRhIHtcbiAgZGlzcGxheTogZmxleDtcbiAgZmxleC13cmFwOiB3cmFwO1xuICBnYXA6IDFyZW07XG4gIG1hcmdpbi1ib3R0b206IDEuNXJlbTtcbn1cblxuLnByb2plY3QtbWV0YS1pdGVtIHtcbiAgZGlzcGxheTogZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgZ2FwOiAwLjVyZW07XG59Il19 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvZnJvbnQvcHJvamVjdHMvcHJvamVjdC1kZXRhaWwvcHJvamVjdC1kZXRhaWwuY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQSxpREFBaUQ7QUFDakQ7RUFDRSxlQUFlO0VBQ2Ysc0JBQXNCO0VBQ3RCLHFCQUFxQjtFQUNyQix3Q0FBd0M7QUFDMUM7O0FBRUE7RUFDRSxxQkFBcUI7RUFDckIsZ0NBQWdDO0VBQ2hDLG9CQUFvQjtBQUN0Qjs7QUFFQTtFQUNFLHFCQUFxQjtBQUN2Qjs7QUFFQTtFQUNFLGFBQWE7RUFDYixlQUFlO0VBQ2YsU0FBUztFQUNULHFCQUFxQjtBQUN2Qjs7QUFFQTtFQUNFLGFBQWE7RUFDYixtQkFBbUI7RUFDbkIsV0FBVztBQUNiO0FBQ0EsNHdDQUE0d0MiLCJzb3VyY2VzQ29udGVudCI6WyIvKiBTdHlsZXMgcG91ciBsZSBjb21wb3NhbnQgZGUgZMODwql0YWlsIGRlIHByb2pldCAqL1xuLnByb2plY3QtY29udGFpbmVyIHtcbiAgcGFkZGluZzogMS41cmVtO1xuICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmO1xuICBib3JkZXItcmFkaXVzOiAwLjVyZW07XG4gIGJveC1zaGFkb3c6IDAgMnB4IDRweCByZ2JhKDAsIDAsIDAsIDAuMSk7XG59XG5cbi5wcm9qZWN0LWhlYWRlciB7XG4gIG1hcmdpbi1ib3R0b206IDEuNXJlbTtcbiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlNWU3ZWI7XG4gIHBhZGRpbmctYm90dG9tOiAxcmVtO1xufVxuXG4ucHJvamVjdC1kZXNjcmlwdGlvbiB7XG4gIG1hcmdpbi1ib3R0b206IDEuNXJlbTtcbn1cblxuLnByb2plY3QtbWV0YSB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGZsZXgtd3JhcDogd3JhcDtcbiAgZ2FwOiAxcmVtO1xuICBtYXJnaW4tYm90dG9tOiAxLjVyZW07XG59XG5cbi5wcm9qZWN0LW1ldGEtaXRlbSB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGdhcDogMC41cmVtO1xufSJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵnamespaceHTML", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵnamespaceSVG", "ɵɵtext", "ɵɵadvance", "ɵɵproperty", "ctx_r5", "getFileUrl", "file_r6", "ɵɵsanitizeUrl", "ɵɵtemplate", "ProjectDetailComponent_div_8_div_18_div_4_Template", "ctx_r2", "projet", "fichiers", "ɵɵelementContainerStart", "ɵɵelementContainerEnd", "ɵɵpureFunction1", "_c0", "ctx_r4", "projetId", "ProjectDetailComponent_div_8_div_18_Template", "ProjectDetailComponent_div_8_ng_container_20_Template", "ProjectDetailComponent_div_8_ng_container_21_Template", "ɵɵtextInterpolate", "ctx_r1", "titre", "ɵɵtextInterpolate1", "groupe", "ɵɵpipeBind2", "dateLimite", "description", "length", "hasSubmitted", "ProjectDetailComponent", "constructor", "route", "router", "projetService", "rendusService", "authService", "isLoading", "ngOnInit", "snapshot", "paramMap", "get", "loadProjetDetails", "checkRenduStatus", "getProjetById", "subscribe", "next", "error", "err", "console", "navigate", "etudiantId", "getCurrentUserId", "checkRenduExists", "exists", "filePath", "fileName", "includes", "parts", "split", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "ProjetService", "i3", "RendusService", "i4", "AuthuserService", "selectors", "decls", "vars", "consts", "template", "ProjectDetailComponent_Template", "rf", "ctx", "ProjectDetailComponent_div_7_Template", "ProjectDetailComponent_div_8_Template"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\projects\\project-detail\\project-detail.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\projects\\project-detail\\project-detail.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { ProjetService } from '@app/services/projets.service';\nimport { RendusService } from 'src/app/services/rendus.service';\nimport { AuthuserService } from 'src/app/services/authuser.service';\n\n// Composant pour afficher les détails d'un projet\n@Component({\n  selector: 'app-project-detail',\n  templateUrl: './project-detail.component.html',\n  styleUrls: ['./project-detail.component.css'],\n})\nexport class ProjectDetailComponent implements OnInit {\n  projetId: string = '';\n  projet: any;\n  rendu: any;\n  isLoading = true;\n  hasSubmitted = false;\n\n  constructor(\n    private route: ActivatedRoute,\n    private router: Router,\n    private projetService: ProjetService,\n    private rendusService: RendusService,\n    private authService: AuthuserService\n  ) {}\n\n  ngOnInit(): void {\n    this.projetId = this.route.snapshot.paramMap.get('id') || '';\n    this.loadProjetDetails();\n    this.checkRenduStatus();\n  }\n\n  loadProjetDetails(): void {\n    this.isLoading = true;\n    this.projetService.getProjetById(this.projetId).subscribe({\n      next: (projet: any) => {\n        this.projet = projet;\n        this.isLoading = false;\n      },\n      error: (err) => {\n        console.error('Erreur lors du chargement du projet', err);\n        this.isLoading = false;\n        this.router.navigate(['/projects']);\n      },\n    });\n  }\n\n  checkRenduStatus(): void {\n    const etudiantId = this.authService.getCurrentUserId();\n    if (etudiantId) {\n      this.rendusService.checkRenduExists(this.projetId, etudiantId).subscribe({\n        next: (exists) => {\n          this.hasSubmitted = exists;\n        },\n        error: (err) => {\n          console.error('Erreur lors de la vérification du rendu', err);\n        },\n      });\n    }\n  }\n\n  getFileUrl(filePath: string): string {\n    // Extraire uniquement le nom du fichier\n    let fileName = filePath;\n\n    // Si le chemin contient des slashes ou backslashes, prendre la dernière partie\n    if (filePath.includes('/') || filePath.includes('\\\\')) {\n      const parts = filePath.split(/[\\/\\\\]/);\n      fileName = parts[parts.length - 1];\n    }\n\n    // Utiliser l'endpoint API spécifique pour le téléchargement\n    return `http://localhost:3000/api/projets/download/${fileName}`;\n  }\n}\n", "<div class=\"min-h-screen bg-[#edf1f4] p-4 md:p-6\">\n  <div class=\"max-w-4xl mx-auto\">\n    <!-- Back Button -->\n    <div class=\"mb-6\">\n      <a\n        routerLink=\"/front/projects\"\n        class=\"text-[#4f5fad] hover:text-[#3d4a85] flex items-center\"\n      >\n        <svg\n          xmlns=\"http://www.w3.org/2000/svg\"\n          class=\"h-5 w-5 mr-1\"\n          viewBox=\"0 0 20 20\"\n          fill=\"currentColor\"\n        >\n          <path\n            fill-rule=\"evenodd\"\n            d=\"M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z\"\n            clip-rule=\"evenodd\"\n          />\n        </svg>\n        Retour aux projets\n      </a>\n    </div>\n\n    <!-- Loading Indicator -->\n    <div *ngIf=\"isLoading\" class=\"flex justify-center my-8\">\n      <div\n        class=\"w-12 h-12 border-4 border-[#4f5fad]/20 border-t-[#4f5fad] rounded-full animate-spin\"\n      ></div>\n    </div>\n\n    <div\n      *ngIf=\"!isLoading\"\n      class=\"bg-white rounded-xl shadow-md overflow-hidden\"\n    >\n      <!-- Header -->\n      <div class=\"border-t-4 border-[#4f5fad] p-6 bg-white\">\n        <h1 class=\"text-2xl font-bold text-[#4f5fad]\">{{ projet?.titre }}</h1>\n        <div class=\"flex items-center mt-2 text-sm text-[#6d6870]\">\n          <span>Groupe: {{ projet?.groupe }}</span>\n          <span class=\"mx-2\">•</span>\n          <span\n            >Date limite: {{ projet?.dateLimite | date : \"dd/MM/yyyy\" }}</span\n          >\n        </div>\n      </div>\n\n      <!-- Project Content -->\n      <div class=\"p-6\">\n        <div class=\"mb-8\">\n          <h2 class=\"text-lg font-semibold text-[#4f5fad] mb-2\">\n            Description du projet\n          </h2>\n          <p class=\"text-[#6d6870]\">\n            {{ projet?.description || \"Aucune description fournie\" }}\n          </p>\n        </div>\n\n        <!-- Files Section -->\n        <div\n          *ngIf=\"projet?.fichiers && projet?.fichiers.length > 0\"\n          class=\"mb-8\"\n        >\n          <h2 class=\"text-lg font-semibold text-[#4f5fad] mb-4\">\n            Fichiers du projet\n          </h2>\n          <div class=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div\n              *ngFor=\"let file of projet.fichiers\"\n              class=\"border border-[#bdc6cc] rounded-lg p-4 hover:bg-[#edf1f4] transition-colors\"\n            >\n              <div class=\"flex items-center justify-between\">\n                <div class=\"flex items-center\">\n                  <svg\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    class=\"h-6 w-6 text-[#4f5fad] mr-2\"\n                    fill=\"none\"\n                    viewBox=\"0 0 24 24\"\n                    stroke=\"currentColor\"\n                  >\n                    <path\n                      stroke-linecap=\"round\"\n                      stroke-linejoin=\"round\"\n                      stroke-width=\"2\"\n                      d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                    />\n                  </svg>\n                  <span class=\"text-[#6d6870] truncate\">Document</span>\n                </div>\n                <a\n                  [href]=\"getFileUrl(file)\"\n                  download\n                  class=\"text-[#4f5fad] hover:text-[#3d4a85] text-sm font-medium transition-colors\"\n                >\n                  Télécharger\n                </a>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Action Button -->\n        <div class=\"flex justify-end mt-6\">\n          <ng-container *ngIf=\"hasSubmitted\">\n            <span\n              class=\"bg-green-100 text-green-800 px-4 py-2 rounded-lg text-sm font-medium\"\n            >\n              Projet déjà soumis\n            </span>\n          </ng-container>\n\n          <ng-container *ngIf=\"!hasSubmitted\">\n            <a\n              [routerLink]=\"['/front/projects/submit', projetId]\"\n              class=\"bg-[#4f5fad] hover:bg-[#3d4a85] text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors\"\n            >\n              Soumettre mon projet\n            </a>\n          </ng-container>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": ";;;;;;;;;ICyBIA,EAAA,CAAAC,eAAA,EAAwD;IAAxDD,EAAA,CAAAE,cAAA,aAAwD;IACtDF,EAAA,CAAAG,SAAA,aAEO;IACTH,EAAA,CAAAI,YAAA,EAAM;;;;;IAsCEJ,EAAA,CAAAE,cAAA,cAGC;IAGKF,EAAA,CAAAK,cAAA,EAMC;IANDL,EAAA,CAAAE,cAAA,cAMC;IACCF,EAAA,CAAAG,SAAA,eAKE;IACJH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,eAAA,EAAsC;IAAtCD,EAAA,CAAAE,cAAA,eAAsC;IAAAF,EAAA,CAAAM,MAAA,eAAQ;IAAAN,EAAA,CAAAI,YAAA,EAAO;IAEvDJ,EAAA,CAAAE,cAAA,YAIC;IACCF,EAAA,CAAAM,MAAA,8BACF;IAAAN,EAAA,CAAAI,YAAA,EAAI;;;;;IALFJ,EAAA,CAAAO,SAAA,GAAyB;IAAzBP,EAAA,CAAAQ,UAAA,SAAAC,MAAA,CAAAC,UAAA,CAAAC,OAAA,GAAAX,EAAA,CAAAY,aAAA,CAAyB;;;;;IA/BnCZ,EAAA,CAAAE,cAAA,cAGC;IAEGF,EAAA,CAAAM,MAAA,2BACF;IAAAN,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAE,cAAA,cAAmD;IACjDF,EAAA,CAAAa,UAAA,IAAAC,kDAAA,kBA8BM;IACRd,EAAA,CAAAI,YAAA,EAAM;;;;IA9BeJ,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAQ,UAAA,YAAAO,MAAA,CAAAC,MAAA,CAAAC,QAAA,CAAkB;;;;;IAmCvCjB,EAAA,CAAAkB,uBAAA,GAAmC;IACjClB,EAAA,CAAAE,cAAA,eAEC;IACCF,EAAA,CAAAM,MAAA,qCACF;IAAAN,EAAA,CAAAI,YAAA,EAAO;IACTJ,EAAA,CAAAmB,qBAAA,EAAe;;;;;;;;IAEfnB,EAAA,CAAAkB,uBAAA,GAAoC;IAClClB,EAAA,CAAAE,cAAA,YAGC;IACCF,EAAA,CAAAM,MAAA,6BACF;IAAAN,EAAA,CAAAI,YAAA,EAAI;IACNJ,EAAA,CAAAmB,qBAAA,EAAe;;;;IALXnB,EAAA,CAAAO,SAAA,GAAmD;IAAnDP,EAAA,CAAAQ,UAAA,eAAAR,EAAA,CAAAoB,eAAA,IAAAC,GAAA,EAAAC,MAAA,CAAAC,QAAA,EAAmD;;;;;;IAlF7DvB,EAAA,CAAAC,eAAA,EAGC;IAHDD,EAAA,CAAAE,cAAA,cAGC;IAGiDF,EAAA,CAAAM,MAAA,GAAmB;IAAAN,EAAA,CAAAI,YAAA,EAAK;IACtEJ,EAAA,CAAAE,cAAA,cAA2D;IACnDF,EAAA,CAAAM,MAAA,GAA4B;IAAAN,EAAA,CAAAI,YAAA,EAAO;IACzCJ,EAAA,CAAAE,cAAA,eAAmB;IAAAF,EAAA,CAAAM,MAAA,aAAC;IAAAN,EAAA,CAAAI,YAAA,EAAO;IAC3BJ,EAAA,CAAAE,cAAA,WACG;IAAAF,EAAA,CAAAM,MAAA,IAA2D;;IAAAN,EAAA,CAAAI,YAAA,EAC7D;IAKLJ,EAAA,CAAAE,cAAA,eAAiB;IAGXF,EAAA,CAAAM,MAAA,+BACF;IAAAN,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAE,cAAA,aAA0B;IACxBF,EAAA,CAAAM,MAAA,IACF;IAAAN,EAAA,CAAAI,YAAA,EAAI;IAINJ,EAAA,CAAAa,UAAA,KAAAW,4CAAA,kBAwCM;IAGNxB,EAAA,CAAAE,cAAA,eAAmC;IACjCF,EAAA,CAAAa,UAAA,KAAAY,qDAAA,2BAMe;IAEfzB,EAAA,CAAAa,UAAA,KAAAa,qDAAA,2BAOe;IACjB1B,EAAA,CAAAI,YAAA,EAAM;;;;IAlFwCJ,EAAA,CAAAO,SAAA,GAAmB;IAAnBP,EAAA,CAAA2B,iBAAA,CAAAC,MAAA,CAAAZ,MAAA,kBAAAY,MAAA,CAAAZ,MAAA,CAAAa,KAAA,CAAmB;IAEzD7B,EAAA,CAAAO,SAAA,GAA4B;IAA5BP,EAAA,CAAA8B,kBAAA,aAAAF,MAAA,CAAAZ,MAAA,kBAAAY,MAAA,CAAAZ,MAAA,CAAAe,MAAA,KAA4B;IAG/B/B,EAAA,CAAAO,SAAA,GAA2D;IAA3DP,EAAA,CAAA8B,kBAAA,kBAAA9B,EAAA,CAAAgC,WAAA,QAAAJ,MAAA,CAAAZ,MAAA,kBAAAY,MAAA,CAAAZ,MAAA,CAAAiB,UAAA,oBAA2D;IAY5DjC,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAA8B,kBAAA,OAAAF,MAAA,CAAAZ,MAAA,kBAAAY,MAAA,CAAAZ,MAAA,CAAAkB,WAAA,uCACF;IAKClC,EAAA,CAAAO,SAAA,GAAqD;IAArDP,EAAA,CAAAQ,UAAA,UAAAoB,MAAA,CAAAZ,MAAA,kBAAAY,MAAA,CAAAZ,MAAA,CAAAC,QAAA,MAAAW,MAAA,CAAAZ,MAAA,kBAAAY,MAAA,CAAAZ,MAAA,CAAAC,QAAA,CAAAkB,MAAA,MAAqD;IA2CvCnC,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAQ,UAAA,SAAAoB,MAAA,CAAAQ,YAAA,CAAkB;IAQlBpC,EAAA,CAAAO,SAAA,GAAmB;IAAnBP,EAAA,CAAAQ,UAAA,UAAAoB,MAAA,CAAAQ,YAAA,CAAmB;;;ADzG5C;AAMA,OAAM,MAAOC,sBAAsB;EAOjCC,YACUC,KAAqB,EACrBC,MAAc,EACdC,aAA4B,EAC5BC,aAA4B,EAC5BC,WAA4B;IAJ5B,KAAAJ,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IAXrB,KAAApB,QAAQ,GAAW,EAAE;IAGrB,KAAAqB,SAAS,GAAG,IAAI;IAChB,KAAAR,YAAY,GAAG,KAAK;EAQjB;EAEHS,QAAQA,CAAA;IACN,IAAI,CAACtB,QAAQ,GAAG,IAAI,CAACgB,KAAK,CAACO,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE;IAC5D,IAAI,CAACC,iBAAiB,EAAE;IACxB,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEAD,iBAAiBA,CAAA;IACf,IAAI,CAACL,SAAS,GAAG,IAAI;IACrB,IAAI,CAACH,aAAa,CAACU,aAAa,CAAC,IAAI,CAAC5B,QAAQ,CAAC,CAAC6B,SAAS,CAAC;MACxDC,IAAI,EAAGrC,MAAW,IAAI;QACpB,IAAI,CAACA,MAAM,GAAGA,MAAM;QACpB,IAAI,CAAC4B,SAAS,GAAG,KAAK;MACxB,CAAC;MACDU,KAAK,EAAGC,GAAG,IAAI;QACbC,OAAO,CAACF,KAAK,CAAC,qCAAqC,EAAEC,GAAG,CAAC;QACzD,IAAI,CAACX,SAAS,GAAG,KAAK;QACtB,IAAI,CAACJ,MAAM,CAACiB,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;MACrC;KACD,CAAC;EACJ;EAEAP,gBAAgBA,CAAA;IACd,MAAMQ,UAAU,GAAG,IAAI,CAACf,WAAW,CAACgB,gBAAgB,EAAE;IACtD,IAAID,UAAU,EAAE;MACd,IAAI,CAAChB,aAAa,CAACkB,gBAAgB,CAAC,IAAI,CAACrC,QAAQ,EAAEmC,UAAU,CAAC,CAACN,SAAS,CAAC;QACvEC,IAAI,EAAGQ,MAAM,IAAI;UACf,IAAI,CAACzB,YAAY,GAAGyB,MAAM;QAC5B,CAAC;QACDP,KAAK,EAAGC,GAAG,IAAI;UACbC,OAAO,CAACF,KAAK,CAAC,yCAAyC,EAAEC,GAAG,CAAC;QAC/D;OACD,CAAC;;EAEN;EAEA7C,UAAUA,CAACoD,QAAgB;IACzB;IACA,IAAIC,QAAQ,GAAGD,QAAQ;IAEvB;IACA,IAAIA,QAAQ,CAACE,QAAQ,CAAC,GAAG,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,IAAI,CAAC,EAAE;MACrD,MAAMC,KAAK,GAAGH,QAAQ,CAACI,KAAK,CAAC,QAAQ,CAAC;MACtCH,QAAQ,GAAGE,KAAK,CAACA,KAAK,CAAC9B,MAAM,GAAG,CAAC,CAAC;;IAGpC;IACA,OAAO,8CAA8C4B,QAAQ,EAAE;EACjE;;;uBA9DW1B,sBAAsB,EAAArC,EAAA,CAAAmE,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAArE,EAAA,CAAAmE,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAAtE,EAAA,CAAAmE,iBAAA,CAAAI,EAAA,CAAAC,aAAA,GAAAxE,EAAA,CAAAmE,iBAAA,CAAAM,EAAA,CAAAC,aAAA,GAAA1E,EAAA,CAAAmE,iBAAA,CAAAQ,EAAA,CAAAC,eAAA;IAAA;EAAA;;;YAAtBvC,sBAAsB;MAAAwC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZnCnF,EAAA,CAAAE,cAAA,aAAkD;UAQ1CF,EAAA,CAAAK,cAAA,EAKC;UALDL,EAAA,CAAAE,cAAA,aAKC;UACCF,EAAA,CAAAG,SAAA,cAIE;UACJH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAM,MAAA,2BACF;UAAAN,EAAA,CAAAI,YAAA,EAAI;UAINJ,EAAA,CAAAa,UAAA,IAAAwE,qCAAA,iBAIM;UAENrF,EAAA,CAAAa,UAAA,IAAAyE,qCAAA,mBA0FM;UACRtF,EAAA,CAAAI,YAAA,EAAM;;;UAjGEJ,EAAA,CAAAO,SAAA,GAAe;UAAfP,EAAA,CAAAQ,UAAA,SAAA4E,GAAA,CAAAxC,SAAA,CAAe;UAOlB5C,EAAA,CAAAO,SAAA,GAAgB;UAAhBP,EAAA,CAAAQ,UAAA,UAAA4E,GAAA,CAAAxC,SAAA,CAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}