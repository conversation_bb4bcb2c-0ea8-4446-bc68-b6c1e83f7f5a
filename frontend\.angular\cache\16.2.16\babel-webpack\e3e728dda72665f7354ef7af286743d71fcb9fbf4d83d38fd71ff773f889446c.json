{"ast": null, "code": "import { throwError } from 'rxjs';\nimport { catchError, tap } from 'rxjs/operators';\nimport { environment } from '../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class MembreService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = `${environment.urlBackend}teammembers`;\n    console.log('Membre API URL:', this.apiUrl);\n  }\n  getMembres() {\n    console.log('Fetching members from:', this.apiUrl);\n    return this.http.get(this.apiUrl).pipe(tap(data => console.log('Members received:', data)), catchError(this.handleError));\n  }\n  getMembre(id) {\n    console.log(`Fetching member with id ${id} from: ${this.apiUrl}/${id}`);\n    return this.http.get(`${this.apiUrl}/${id}`).pipe(tap(data => console.log('Member received:', data)), catchError(this.handleError));\n  }\n  addMembre(membre) {\n    console.log('Adding member:', membre);\n    return this.http.post(this.apiUrl, membre).pipe(tap(data => console.log('Member added, response:', data)), catchError(this.handleError));\n  }\n  deleteMembre(id) {\n    console.log(`Deleting member with id ${id}`);\n    return this.http.delete(`${this.apiUrl}/${id}`).pipe(tap(data => console.log('Member deleted, response:', data)), catchError(this.handleError));\n  }\n  handleError(error) {\n    let errorMessage = '';\n    if (error.error instanceof ErrorEvent) {\n      // Erreur côté client\n      errorMessage = `Erreur client: ${error.error.message}`;\n    } else {\n      // Erreur côté serveur\n      const status = error.status;\n      const message = error.error?.message || error.statusText;\n      errorMessage = `Erreur serveur: Code ${status}, Message: ${message}`;\n      // Log des détails supplémentaires pour le débogage\n      console.error('Error details:', {\n        status: error.status,\n        statusText: error.statusText,\n        url: error.url,\n        error: error.error\n      });\n      if (status === 0) {\n        console.error(\"Le serveur est-il en cours d'exécution? Vérifiez la connexion réseau.\");\n      }\n    }\n    console.error('API Error:', errorMessage);\n    return throwError(() => new Error(errorMessage));\n  }\n  static {\n    this.ɵfac = function MembreService_Factory(t) {\n      return new (t || MembreService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: MembreService,\n      factory: MembreService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["throwError", "catchError", "tap", "environment", "MembreService", "constructor", "http", "apiUrl", "urlBackend", "console", "log", "getMembres", "get", "pipe", "data", "handleError", "getMembre", "id", "addMembre", "membre", "post", "deleteMembre", "delete", "error", "errorMessage", "ErrorEvent", "message", "status", "statusText", "url", "Error", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\services\\membre.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient, HttpErrorResponse } from '@angular/common/http';\r\nimport { Observable, throwError } from 'rxjs';\r\nimport { catchError, tap } from 'rxjs/operators';\r\nimport { Membre } from '../models/membre.model';\r\nimport { environment } from '../../environments/environment';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class MembreService {\r\n  private apiUrl = `${environment.urlBackend}teammembers`;\r\n\r\n  constructor(private http: HttpClient) {\r\n    console.log('Membre API URL:', this.apiUrl);\r\n  }\r\n\r\n  getMembres(): Observable<Membre[]> {\r\n    console.log('Fetching members from:', this.apiUrl);\r\n    return this.http.get<Membre[]>(this.apiUrl).pipe(\r\n      tap((data) => console.log('Members received:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  getMembre(id: string): Observable<Membre> {\r\n    console.log(`Fetching member with id ${id} from: ${this.apiUrl}/${id}`);\r\n    return this.http.get<Membre>(`${this.apiUrl}/${id}`).pipe(\r\n      tap((data) => console.log('Member received:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  addMembre(membre: Membre): Observable<Membre> {\r\n    console.log('Adding member:', membre);\r\n    return this.http.post<Membre>(this.apiUrl, membre).pipe(\r\n      tap((data) => console.log('Member added, response:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  deleteMembre(id: string): Observable<any> {\r\n    console.log(`Deleting member with id ${id}`);\r\n    return this.http.delete(`${this.apiUrl}/${id}`).pipe(\r\n      tap((data) => console.log('Member deleted, response:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  private handleError(error: HttpErrorResponse) {\r\n    let errorMessage = '';\r\n\r\n    if (error.error instanceof ErrorEvent) {\r\n      // Erreur côté client\r\n      errorMessage = `Erreur client: ${error.error.message}`;\r\n    } else {\r\n      // Erreur côté serveur\r\n      const status = error.status;\r\n      const message = error.error?.message || error.statusText;\r\n\r\n      errorMessage = `Erreur serveur: Code ${status}, Message: ${message}`;\r\n\r\n      // Log des détails supplémentaires pour le débogage\r\n      console.error('Error details:', {\r\n        status: error.status,\r\n        statusText: error.statusText,\r\n        url: error.url,\r\n        error: error.error,\r\n      });\r\n\r\n      if (status === 0) {\r\n        console.error(\r\n          \"Le serveur est-il en cours d'exécution? Vérifiez la connexion réseau.\"\r\n        );\r\n      }\r\n    }\r\n\r\n    console.error('API Error:', errorMessage);\r\n    return throwError(() => new Error(errorMessage));\r\n  }\r\n}\r\n"], "mappings": "AAEA,SAAqBA,UAAU,QAAQ,MAAM;AAC7C,SAASC,UAAU,EAAEC,GAAG,QAAQ,gBAAgB;AAEhD,SAASC,WAAW,QAAQ,gCAAgC;;;AAK5D,OAAM,MAAOC,aAAa;EAGxBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,MAAM,GAAG,GAAGJ,WAAW,CAACK,UAAU,aAAa;IAGrDC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAACH,MAAM,CAAC;EAC7C;EAEAI,UAAUA,CAAA;IACRF,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE,IAAI,CAACH,MAAM,CAAC;IAClD,OAAO,IAAI,CAACD,IAAI,CAACM,GAAG,CAAW,IAAI,CAACL,MAAM,CAAC,CAACM,IAAI,CAC9CX,GAAG,CAAEY,IAAI,IAAKL,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEI,IAAI,CAAC,CAAC,EACrDb,UAAU,CAAC,IAAI,CAACc,WAAW,CAAC,CAC7B;EACH;EAEAC,SAASA,CAACC,EAAU;IAClBR,OAAO,CAACC,GAAG,CAAC,2BAA2BO,EAAE,UAAU,IAAI,CAACV,MAAM,IAAIU,EAAE,EAAE,CAAC;IACvE,OAAO,IAAI,CAACX,IAAI,CAACM,GAAG,CAAS,GAAG,IAAI,CAACL,MAAM,IAAIU,EAAE,EAAE,CAAC,CAACJ,IAAI,CACvDX,GAAG,CAAEY,IAAI,IAAKL,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEI,IAAI,CAAC,CAAC,EACpDb,UAAU,CAAC,IAAI,CAACc,WAAW,CAAC,CAC7B;EACH;EAEAG,SAASA,CAACC,MAAc;IACtBV,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAES,MAAM,CAAC;IACrC,OAAO,IAAI,CAACb,IAAI,CAACc,IAAI,CAAS,IAAI,CAACb,MAAM,EAAEY,MAAM,CAAC,CAACN,IAAI,CACrDX,GAAG,CAAEY,IAAI,IAAKL,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEI,IAAI,CAAC,CAAC,EAC3Db,UAAU,CAAC,IAAI,CAACc,WAAW,CAAC,CAC7B;EACH;EAEAM,YAAYA,CAACJ,EAAU;IACrBR,OAAO,CAACC,GAAG,CAAC,2BAA2BO,EAAE,EAAE,CAAC;IAC5C,OAAO,IAAI,CAACX,IAAI,CAACgB,MAAM,CAAC,GAAG,IAAI,CAACf,MAAM,IAAIU,EAAE,EAAE,CAAC,CAACJ,IAAI,CAClDX,GAAG,CAAEY,IAAI,IAAKL,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEI,IAAI,CAAC,CAAC,EAC7Db,UAAU,CAAC,IAAI,CAACc,WAAW,CAAC,CAC7B;EACH;EAEQA,WAAWA,CAACQ,KAAwB;IAC1C,IAAIC,YAAY,GAAG,EAAE;IAErB,IAAID,KAAK,CAACA,KAAK,YAAYE,UAAU,EAAE;MACrC;MACAD,YAAY,GAAG,kBAAkBD,KAAK,CAACA,KAAK,CAACG,OAAO,EAAE;KACvD,MAAM;MACL;MACA,MAAMC,MAAM,GAAGJ,KAAK,CAACI,MAAM;MAC3B,MAAMD,OAAO,GAAGH,KAAK,CAACA,KAAK,EAAEG,OAAO,IAAIH,KAAK,CAACK,UAAU;MAExDJ,YAAY,GAAG,wBAAwBG,MAAM,cAAcD,OAAO,EAAE;MAEpE;MACAjB,OAAO,CAACc,KAAK,CAAC,gBAAgB,EAAE;QAC9BI,MAAM,EAAEJ,KAAK,CAACI,MAAM;QACpBC,UAAU,EAAEL,KAAK,CAACK,UAAU;QAC5BC,GAAG,EAAEN,KAAK,CAACM,GAAG;QACdN,KAAK,EAAEA,KAAK,CAACA;OACd,CAAC;MAEF,IAAII,MAAM,KAAK,CAAC,EAAE;QAChBlB,OAAO,CAACc,KAAK,CACX,uEAAuE,CACxE;;;IAILd,OAAO,CAACc,KAAK,CAAC,YAAY,EAAEC,YAAY,CAAC;IACzC,OAAOxB,UAAU,CAAC,MAAM,IAAI8B,KAAK,CAACN,YAAY,CAAC,CAAC;EAClD;;;uBArEWpB,aAAa,EAAA2B,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAb9B,aAAa;MAAA+B,OAAA,EAAb/B,aAAa,CAAAgC,IAAA;MAAAC,UAAA,EAFZ;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}