{"ast": null, "code": "import { moveItemInArray, transferArrayItem } from '@angular/cdk/drag-drop';\nimport { finalize } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/task.service\";\nimport * as i2 from \"src/app/services/equipe.service\";\nimport * as i3 from \"src/app/services/authuser.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"src/app/services/notification.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/forms\";\nimport * as i8 from \"@angular/cdk/drag-drop\";\nfunction TaskListComponent_h1_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h1\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" T\\u00E2ches: \", ctx_r0.team.name, \" \");\n  }\n}\nfunction TaskListComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 19)(2, \"div\", 20)(3, \"span\", 21);\n    i0.ɵɵtext(4, \"Chargement...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 22)(6, \"span\", 21);\n    i0.ɵɵtext(7, \"Chargement...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 20)(9, \"span\", 21);\n    i0.ɵɵtext(10, \"Chargement...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"p\", 23);\n    i0.ɵɵtext(12, \"Chargement des t\\u00E2ches...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction TaskListComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 24)(2, \"div\", 25);\n    i0.ɵɵelement(3, \"i\", 26);\n    i0.ɵɵelementStart(4, \"div\", 27);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function TaskListComponent_div_18_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.teamId && ctx_r7.loadTasks(ctx_r7.teamId));\n    });\n    i0.ɵɵelement(7, \"i\", 29);\n    i0.ɵɵtext(8, \" R\\u00E9essayer \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.error, \" \");\n  }\n}\nfunction TaskListComponent_div_19_option_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 52);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r10 = ctx.$implicit;\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"value\", user_r10._id || user_r10.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r9.getUserName(user_r10._id || user_r10.id || \"\"), \" \");\n  }\n}\nfunction TaskListComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 30)(3, \"div\", 31)(4, \"h4\", 32);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 33)(7, \"form\", 34);\n    i0.ɵɵlistener(\"ngSubmit\", function TaskListComponent_div_19_Template_form_ngSubmit_7_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.editingTask ? ctx_r11.updateTask() : ctx_r11.createTask());\n    });\n    i0.ɵɵelementStart(8, \"div\", 35)(9, \"div\", 36)(10, \"label\", 37);\n    i0.ɵɵtext(11, \"Titre*\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"input\", 38);\n    i0.ɵɵlistener(\"ngModelChange\", function TaskListComponent_div_19_Template_input_ngModelChange_12_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.editingTask ? ctx_r13.editingTask.title = $event : ctx_r13.newTask.title = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 39)(14, \"label\", 40);\n    i0.ɵɵtext(15, \"Priorit\\u00E9*\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"select\", 41);\n    i0.ɵɵlistener(\"ngModelChange\", function TaskListComponent_div_19_Template_select_ngModelChange_16_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.editingTask ? ctx_r14.editingTask.priority : ctx_r14.newTask.priority = $event);\n    });\n    i0.ɵɵelementStart(17, \"option\", 42);\n    i0.ɵɵtext(18, \"Basse\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"option\", 43);\n    i0.ɵɵtext(20, \"Moyenne\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"option\", 44);\n    i0.ɵɵtext(22, \"Haute\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(23, \"div\", 39)(24, \"label\", 45);\n    i0.ɵɵtext(25, \"Statut*\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"select\", 46);\n    i0.ɵɵlistener(\"ngModelChange\", function TaskListComponent_div_19_Template_select_ngModelChange_26_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.editingTask ? ctx_r15.editingTask.status : ctx_r15.newTask.status = $event);\n    });\n    i0.ɵɵelementStart(27, \"option\", 47);\n    i0.ɵɵtext(28, \"\\u00C0 faire\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"option\", 48);\n    i0.ɵɵtext(30, \"En cours\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"option\", 49);\n    i0.ɵɵtext(32, \"Termin\\u00E9e\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(33, \"div\", 36)(34, \"label\", 50);\n    i0.ɵɵtext(35, \"Assign\\u00E9e \\u00E0\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"select\", 51);\n    i0.ɵɵlistener(\"ngModelChange\", function TaskListComponent_div_19_Template_select_ngModelChange_36_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.editingTask ? ctx_r16.editingTask.assignedTo : ctx_r16.newTask.assignedTo = $event);\n    });\n    i0.ɵɵelementStart(37, \"option\", 52);\n    i0.ɵɵtext(38, \"Non assign\\u00E9e\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(39, TaskListComponent_div_19_option_39_Template, 2, 2, \"option\", 53);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(40, \"div\", 36)(41, \"label\", 54);\n    i0.ɵɵtext(42, \"Date d'\\u00E9ch\\u00E9ance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"input\", 55);\n    i0.ɵɵlistener(\"ngModelChange\", function TaskListComponent_div_19_Template_input_ngModelChange_43_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.editingTask ? ctx_r17.editingTask.dueDate : ctx_r17.newTask.dueDate = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(44, \"div\", 3)(45, \"label\", 56);\n    i0.ɵɵtext(46, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"textarea\", 57);\n    i0.ɵɵlistener(\"ngModelChange\", function TaskListComponent_div_19_Template_textarea_ngModelChange_47_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.editingTask ? ctx_r18.editingTask.description : ctx_r18.newTask.description = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(48, \"div\", 58)(49, \"button\", 59);\n    i0.ɵɵlistener(\"click\", function TaskListComponent_div_19_Template_button_click_49_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r19 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r19.editingTask ? ctx_r19.cancelEdit() : ctx_r19.toggleTaskForm());\n    });\n    i0.ɵɵtext(50, \" Annuler \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(51, \"button\", 60);\n    i0.ɵɵtext(52);\n    i0.ɵɵelementEnd()()()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.editingTask ? \"Modifier la t\\u00E2che\" : \"Nouvelle t\\u00E2che\", \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngModel\", ctx_r3.editingTask ? ctx_r3.editingTask.title : ctx_r3.newTask.title);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r3.editingTask ? ctx_r3.editingTask.priority : ctx_r3.newTask.priority);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngModel\", ctx_r3.editingTask ? ctx_r3.editingTask.status : ctx_r3.newTask.status);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngModel\", ctx_r3.editingTask ? ctx_r3.editingTask.assignedTo : ctx_r3.newTask.assignedTo);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"value\", null);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.users);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r3.editingTask ? ctx_r3.editingTask.dueDate : ctx_r3.newTask.dueDate);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r3.editingTask ? ctx_r3.editingTask.description : ctx_r3.newTask.description);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.editingTask ? \"Mettre \\u00E0 jour\" : \"Cr\\u00E9er\", \" \");\n  }\n}\nfunction TaskListComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 61)(1, \"div\", 3)(2, \"div\", 30)(3, \"div\", 62)(4, \"div\", 35)(5, \"div\", 63)(6, \"div\", 64)(7, \"span\", 65);\n    i0.ɵɵelement(8, \"i\", 66);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"input\", 67);\n    i0.ɵɵlistener(\"ngModelChange\", function TaskListComponent_div_20_Template_input_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.searchTerm = $event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 63)(11, \"select\", 68);\n    i0.ɵɵlistener(\"ngModelChange\", function TaskListComponent_div_20_Template_select_ngModelChange_11_listener($event) {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r22 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r22.statusFilter = $event);\n    });\n    i0.ɵɵelementStart(12, \"option\", 69);\n    i0.ɵɵtext(13, \"Tous les statuts\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"option\", 47);\n    i0.ɵɵtext(15, \"\\u00C0 faire\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"option\", 48);\n    i0.ɵɵtext(17, \"En cours\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"option\", 49);\n    i0.ɵɵtext(19, \"Termin\\u00E9es\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(20, \"div\", 63)(21, \"select\", 68);\n    i0.ɵɵlistener(\"ngModelChange\", function TaskListComponent_div_20_Template_select_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r23 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r23.priorityFilter = $event);\n    });\n    i0.ɵɵelementStart(22, \"option\", 69);\n    i0.ɵɵtext(23, \"Toutes les priorit\\u00E9s\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"option\", 44);\n    i0.ɵɵtext(25, \"Haute\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"option\", 43);\n    i0.ɵɵtext(27, \"Moyenne\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"option\", 42);\n    i0.ɵɵtext(29, \"Basse\");\n    i0.ɵɵelementEnd()()()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngModel\", ctx_r4.searchTerm);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r4.statusFilter);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngModel\", ctx_r4.priorityFilter);\n  }\n}\nfunction TaskListComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 70)(2, \"div\", 71);\n    i0.ɵɵelement(3, \"i\", 72);\n    i0.ɵɵelementStart(4, \"h3\", 73);\n    i0.ɵɵtext(5, \"Aucune t\\u00E2che trouv\\u00E9e\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 74);\n    i0.ɵɵtext(7, \" Commencez par cr\\u00E9er une nouvelle t\\u00E2che pour votre \\u00E9quipe. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 10);\n    i0.ɵɵlistener(\"click\", function TaskListComponent_div_21_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r24 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r24.toggleTaskForm());\n    });\n    i0.ɵɵelement(9, \"i\", 11);\n    i0.ɵɵtext(10, \" Cr\\u00E9er une t\\u00E2che \");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction TaskListComponent_div_22_div_12_small_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 113);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const task_r32 = i0.ɵɵnextContext().$implicit;\n    const ctx_r33 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r33.getUserName(task_r32.assignedTo), \" \");\n  }\n}\nconst _c0 = function (a0, a1, a2) {\n  return {\n    \"bg-danger\": a0,\n    \"bg-warning text-dark\": a1,\n    \"bg-info text-dark\": a2\n  };\n};\nfunction TaskListComponent_div_22_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r36 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 94)(1, \"div\", 95)(2, \"h6\", 96);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 97)(5, \"button\", 98);\n    i0.ɵɵelement(6, \"i\", 99);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"ul\", 100)(8, \"li\")(9, \"button\", 101);\n    i0.ɵɵlistener(\"click\", function TaskListComponent_div_22_div_12_Template_button_click_9_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r36);\n      const task_r32 = restoredCtx.$implicit;\n      const ctx_r35 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r35.editTask(task_r32));\n    });\n    i0.ɵɵelement(10, \"i\", 102);\n    i0.ɵɵtext(11, \" Modifier \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"li\")(13, \"button\", 101);\n    i0.ɵɵlistener(\"click\", function TaskListComponent_div_22_div_12_Template_button_click_13_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r36);\n      const task_r32 = restoredCtx.$implicit;\n      const ctx_r37 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r37.updateTaskStatus(task_r32, \"in-progress\"));\n    });\n    i0.ɵɵelement(14, \"i\", 103);\n    i0.ɵɵtext(15, \" D\\u00E9placer vers \\\"En cours\\\" \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"li\")(17, \"button\", 101);\n    i0.ɵɵlistener(\"click\", function TaskListComponent_div_22_div_12_Template_button_click_17_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r36);\n      const task_r32 = restoredCtx.$implicit;\n      const ctx_r38 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r38.updateTaskStatus(task_r32, \"done\"));\n    });\n    i0.ɵɵelement(18, \"i\", 89);\n    i0.ɵɵtext(19, \" Marquer comme termin\\u00E9e \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"li\");\n    i0.ɵɵelement(21, \"hr\", 104);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"li\")(23, \"button\", 105);\n    i0.ɵɵlistener(\"click\", function TaskListComponent_div_22_div_12_Template_button_click_23_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r36);\n      const task_r32 = restoredCtx.$implicit;\n      const ctx_r39 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(task_r32._id && ctx_r39.deleteTask(task_r32._id));\n    });\n    i0.ɵɵelement(24, \"i\", 106);\n    i0.ɵɵtext(25, \" Supprimer \");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(26, \"p\", 107);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 108)(29, \"span\", 109);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(31, TaskListComponent_div_22_div_12_small_31_Template, 2, 1, \"small\", 110);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"div\", 111);\n    i0.ɵɵelement(33, \"i\", 112);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const task_r32 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngClass\", \"priority-\" + task_r32.priority);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(task_r32.title);\n    i0.ɵɵadvance(24);\n    i0.ɵɵtextInterpolate1(\" \", task_r32.description || \"Aucune description\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(6, _c0, task_r32.priority === \"high\", task_r32.priority === \"medium\", task_r32.priority === \"low\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", task_r32.priority === \"high\" ? \"Haute\" : task_r32.priority === \"medium\" ? \"Moyenne\" : \"Basse\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", task_r32.assignedTo);\n  }\n}\nfunction TaskListComponent_div_22_div_24_small_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 113);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const task_r40 = i0.ɵɵnextContext().$implicit;\n    const ctx_r41 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r41.getUserName(task_r40.assignedTo), \" \");\n  }\n}\nfunction TaskListComponent_div_22_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r44 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 94)(1, \"div\", 95)(2, \"h6\", 96);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 97)(5, \"button\", 98);\n    i0.ɵɵelement(6, \"i\", 99);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"ul\", 100)(8, \"li\")(9, \"button\", 101);\n    i0.ɵɵlistener(\"click\", function TaskListComponent_div_22_div_24_Template_button_click_9_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r44);\n      const task_r40 = restoredCtx.$implicit;\n      const ctx_r43 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r43.editTask(task_r40));\n    });\n    i0.ɵɵelement(10, \"i\", 102);\n    i0.ɵɵtext(11, \" Modifier \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"li\")(13, \"button\", 101);\n    i0.ɵɵlistener(\"click\", function TaskListComponent_div_22_div_24_Template_button_click_13_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r44);\n      const task_r40 = restoredCtx.$implicit;\n      const ctx_r45 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r45.updateTaskStatus(task_r40, \"todo\"));\n    });\n    i0.ɵɵelement(14, \"i\", 9);\n    i0.ɵɵtext(15, \" D\\u00E9placer vers \\\"\\u00C0 faire\\\" \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"li\")(17, \"button\", 101);\n    i0.ɵɵlistener(\"click\", function TaskListComponent_div_22_div_24_Template_button_click_17_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r44);\n      const task_r40 = restoredCtx.$implicit;\n      const ctx_r46 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r46.updateTaskStatus(task_r40, \"done\"));\n    });\n    i0.ɵɵelement(18, \"i\", 89);\n    i0.ɵɵtext(19, \" Marquer comme termin\\u00E9e \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"li\");\n    i0.ɵɵelement(21, \"hr\", 104);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"li\")(23, \"button\", 105);\n    i0.ɵɵlistener(\"click\", function TaskListComponent_div_22_div_24_Template_button_click_23_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r44);\n      const task_r40 = restoredCtx.$implicit;\n      const ctx_r47 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(task_r40._id && ctx_r47.deleteTask(task_r40._id));\n    });\n    i0.ɵɵelement(24, \"i\", 106);\n    i0.ɵɵtext(25, \" Supprimer \");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(26, \"p\", 107);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 108)(29, \"span\", 109);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(31, TaskListComponent_div_22_div_24_small_31_Template, 2, 1, \"small\", 110);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"div\", 111);\n    i0.ɵɵelement(33, \"i\", 112);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const task_r40 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngClass\", \"priority-\" + task_r40.priority);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(task_r40.title);\n    i0.ɵɵadvance(24);\n    i0.ɵɵtextInterpolate1(\" \", task_r40.description || \"Aucune description\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(6, _c0, task_r40.priority === \"high\", task_r40.priority === \"medium\", task_r40.priority === \"low\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", task_r40.priority === \"high\" ? \"Haute\" : task_r40.priority === \"medium\" ? \"Moyenne\" : \"Basse\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", task_r40.assignedTo);\n  }\n}\nfunction TaskListComponent_div_22_div_36_small_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 113);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const task_r48 = i0.ɵɵnextContext().$implicit;\n    const ctx_r49 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r49.getUserName(task_r48.assignedTo), \" \");\n  }\n}\nfunction TaskListComponent_div_22_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r52 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 114)(1, \"div\", 95)(2, \"h6\", 96);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 97)(5, \"button\", 98);\n    i0.ɵɵelement(6, \"i\", 99);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"ul\", 100)(8, \"li\")(9, \"button\", 101);\n    i0.ɵɵlistener(\"click\", function TaskListComponent_div_22_div_36_Template_button_click_9_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r52);\n      const task_r48 = restoredCtx.$implicit;\n      const ctx_r51 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r51.editTask(task_r48));\n    });\n    i0.ɵɵelement(10, \"i\", 102);\n    i0.ɵɵtext(11, \" Modifier \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"li\")(13, \"button\", 101);\n    i0.ɵɵlistener(\"click\", function TaskListComponent_div_22_div_36_Template_button_click_13_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r52);\n      const task_r48 = restoredCtx.$implicit;\n      const ctx_r53 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r53.updateTaskStatus(task_r48, \"todo\"));\n    });\n    i0.ɵɵelement(14, \"i\", 9);\n    i0.ɵɵtext(15, \" D\\u00E9placer vers \\\"\\u00C0 faire\\\" \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"li\")(17, \"button\", 101);\n    i0.ɵɵlistener(\"click\", function TaskListComponent_div_22_div_36_Template_button_click_17_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r52);\n      const task_r48 = restoredCtx.$implicit;\n      const ctx_r54 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r54.updateTaskStatus(task_r48, \"in-progress\"));\n    });\n    i0.ɵɵelement(18, \"i\", 9);\n    i0.ɵɵtext(19, \" D\\u00E9placer vers \\\"En cours\\\" \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"li\");\n    i0.ɵɵelement(21, \"hr\", 104);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"li\")(23, \"button\", 105);\n    i0.ɵɵlistener(\"click\", function TaskListComponent_div_22_div_36_Template_button_click_23_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r52);\n      const task_r48 = restoredCtx.$implicit;\n      const ctx_r55 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(task_r48._id && ctx_r55.deleteTask(task_r48._id));\n    });\n    i0.ɵɵelement(24, \"i\", 106);\n    i0.ɵɵtext(25, \" Supprimer \");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(26, \"p\", 107);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 108)(29, \"span\", 109);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(31, TaskListComponent_div_22_div_36_small_31_Template, 2, 1, \"small\", 110);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"div\", 111);\n    i0.ɵɵelement(33, \"i\", 112);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const task_r48 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngClass\", \"priority-\" + task_r48.priority);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(task_r48.title);\n    i0.ɵɵadvance(24);\n    i0.ɵɵtextInterpolate1(\" \", task_r48.description || \"Aucune description\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(6, _c0, task_r48.priority === \"high\", task_r48.priority === \"medium\", task_r48.priority === \"low\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", task_r48.priority === \"high\" ? \"Haute\" : task_r48.priority === \"medium\" ? \"Moyenne\" : \"Basse\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", task_r48.assignedTo);\n  }\n}\nconst _c1 = function (a0, a1) {\n  return [a0, a1];\n};\nfunction TaskListComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r57 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 75)(1, \"div\", 63)(2, \"div\", 76)(3, \"div\", 31)(4, \"h5\", 77);\n    i0.ɵɵelement(5, \"i\", 78);\n    i0.ɵɵtext(6, \" \\u00C0 faire \");\n    i0.ɵɵelementStart(7, \"span\", 79);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 62)(10, \"div\", 80, 81);\n    i0.ɵɵlistener(\"cdkDropListDropped\", function TaskListComponent_div_22_Template_div_cdkDropListDropped_10_listener($event) {\n      i0.ɵɵrestoreView(_r57);\n      const ctx_r56 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r56.drop($event));\n    });\n    i0.ɵɵtemplate(12, TaskListComponent_div_22_div_12_Template, 34, 10, \"div\", 82);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(13, \"div\", 63)(14, \"div\", 76)(15, \"div\", 83)(16, \"h5\", 77);\n    i0.ɵɵelement(17, \"i\", 84);\n    i0.ɵɵtext(18, \" En cours \");\n    i0.ɵɵelementStart(19, \"span\", 85);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(21, \"div\", 62)(22, \"div\", 86, 87);\n    i0.ɵɵlistener(\"cdkDropListDropped\", function TaskListComponent_div_22_Template_div_cdkDropListDropped_22_listener($event) {\n      i0.ɵɵrestoreView(_r57);\n      const ctx_r58 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r58.drop($event));\n    });\n    i0.ɵɵtemplate(24, TaskListComponent_div_22_div_24_Template, 34, 10, \"div\", 82);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(25, \"div\", 63)(26, \"div\", 76)(27, \"div\", 88)(28, \"h5\", 77);\n    i0.ɵɵelement(29, \"i\", 89);\n    i0.ɵɵtext(30, \" Termin\\u00E9es \");\n    i0.ɵɵelementStart(31, \"span\", 90);\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(33, \"div\", 62)(34, \"div\", 91, 92);\n    i0.ɵɵlistener(\"cdkDropListDropped\", function TaskListComponent_div_22_Template_div_cdkDropListDropped_34_listener($event) {\n      i0.ɵɵrestoreView(_r57);\n      const ctx_r59 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r59.drop($event));\n    });\n    i0.ɵɵtemplate(36, TaskListComponent_div_22_div_36_Template, 34, 10, \"div\", 93);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const _r26 = i0.ɵɵreference(11);\n    const _r28 = i0.ɵɵreference(23);\n    const _r30 = i0.ɵɵreference(35);\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.getTodoTasksCount(), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"cdkDropListData\", ctx_r6.getTodoTasks())(\"cdkDropListConnectedTo\", i0.ɵɵpureFunction2(12, _c1, _r28, _r30));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.getTodoTasks());\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.getInProgressTasksCount(), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"cdkDropListData\", ctx_r6.getInProgressTasks())(\"cdkDropListConnectedTo\", i0.ɵɵpureFunction2(15, _c1, _r26, _r30));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.getInProgressTasks());\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.getDoneTasksCount(), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"cdkDropListData\", ctx_r6.getDoneTasks())(\"cdkDropListConnectedTo\", i0.ɵɵpureFunction2(18, _c1, _r26, _r28));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.getDoneTasks());\n  }\n}\nexport class TaskListComponent {\n  constructor(taskService, equipeService, userService, route, router, notificationService) {\n    this.taskService = taskService;\n    this.equipeService = equipeService;\n    this.userService = userService;\n    this.route = route;\n    this.router = router;\n    this.notificationService = notificationService;\n    this.tasks = [];\n    this.teamId = null;\n    this.team = null;\n    this.loading = false;\n    this.error = null;\n    this.users = [];\n    this.editingTask = null;\n    this.showTaskForm = false;\n    // Filtres\n    this.statusFilter = 'all';\n    this.priorityFilter = 'all';\n    this.searchTerm = '';\n  }\n  ngOnInit() {\n    // Initialiser la nouvelle tâche\n    this.newTask = this.initializeNewTask();\n    this.route.paramMap.subscribe(params => {\n      this.teamId = params.get('id');\n      if (this.teamId) {\n        this.loadTeamDetails(this.teamId);\n        this.loadTasks(this.teamId);\n        this.loadUsers();\n      } else {\n        this.error = \"ID d'équipe manquant\";\n        this.notificationService.showError(\"ID d'équipe manquant\");\n      }\n    });\n  }\n  loadTeamDetails(teamId) {\n    this.loading = true;\n    // Utiliser les données de test si l'API n'est pas disponible\n    const useMockData = false; // Mettre à true pour utiliser les données de test\n    if (useMockData) {\n      // Données de test pour simuler les détails de l'équipe\n      const mockTeam = {\n        _id: teamId,\n        name: 'Équipe ' + teamId,\n        description: \"Description de l'équipe \" + teamId,\n        admin: 'admin123',\n        members: []\n      };\n      setTimeout(() => {\n        this.team = mockTeam;\n        this.loading = false;\n        console.log(\"Détails de l'équipe chargés (mock):\", this.team);\n      }, 300);\n    } else {\n      // Utiliser l'API réelle\n      this.equipeService.getEquipe(teamId).pipe(finalize(() => this.loading = false)).subscribe({\n        next: data => {\n          this.team = data;\n          console.log(\"Détails de l'équipe chargés depuis l'API:\", this.team);\n        },\n        error: error => {\n          console.error(\"Erreur lors du chargement des détails de l'équipe:\", error);\n          this.error = \"Impossible de charger les détails de l'équipe\";\n          this.notificationService.showError(\"Erreur lors du chargement des détails de l'équipe\");\n          // Fallback aux données de test en cas d'erreur\n          const mockTeam = {\n            _id: teamId,\n            name: 'Équipe ' + teamId + ' (fallback)',\n            description: \"Description de l'équipe \" + teamId,\n            admin: 'admin123',\n            members: []\n          };\n          this.team = mockTeam;\n        }\n      });\n    }\n  }\n  loadTasks(teamId) {\n    this.loading = true;\n    // Utiliser les données de test si l'API n'est pas disponible\n    const useMockData = false; // Mettre à true pour utiliser les données de test\n    if (useMockData) {\n      // Données de test pour simuler les tâches\n      const mockTasks = [{\n        _id: '1',\n        title: 'Tâche 1',\n        description: 'Description de la tâche 1',\n        status: 'todo',\n        priority: 'high',\n        teamId: teamId\n      }, {\n        _id: '2',\n        title: 'Tâche 2',\n        description: 'Description de la tâche 2',\n        status: 'todo',\n        priority: 'medium',\n        teamId: teamId\n      }, {\n        _id: '3',\n        title: 'Tâche 3',\n        description: 'Description de la tâche 3',\n        status: 'in-progress',\n        priority: 'high',\n        teamId: teamId\n      }, {\n        _id: '4',\n        title: 'Tâche 4',\n        description: 'Description de la tâche 4',\n        status: 'done',\n        priority: 'low',\n        teamId: teamId\n      }];\n      setTimeout(() => {\n        this.tasks = mockTasks;\n        this.sortTasks();\n        this.loading = false;\n        console.log('Tâches chargées (mock):', this.tasks);\n      }, 500);\n    } else {\n      // Utiliser l'API réelle\n      this.taskService.getTasksByTeam(teamId).pipe(finalize(() => this.loading = false)).subscribe({\n        next: data => {\n          this.tasks = data;\n          this.sortTasks();\n          console.log(\"Tâches chargées depuis l'API:\", this.tasks);\n        },\n        error: error => {\n          console.error('Erreur lors du chargement des tâches:', error);\n          this.error = 'Impossible de charger les tâches';\n          this.notificationService.showError('Erreur lors du chargement des tâches');\n          // Fallback aux données de test en cas d'erreur\n          const mockTasks = [{\n            _id: '1',\n            title: 'Tâche 1 (fallback)',\n            description: 'Description de la tâche 1',\n            status: 'todo',\n            priority: 'high',\n            teamId: teamId\n          }, {\n            _id: '2',\n            title: 'Tâche 2 (fallback)',\n            description: 'Description de la tâche 2',\n            status: 'todo',\n            priority: 'medium',\n            teamId: teamId\n          }];\n          this.tasks = mockTasks;\n          this.sortTasks();\n          console.log('Tâches chargées (fallback):', this.tasks);\n        }\n      });\n    }\n  }\n  // Gestion du glisser-déposer\n  drop(event) {\n    if (event.previousContainer === event.container) {\n      // Déplacement dans la même liste\n      moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);\n    } else {\n      // Déplacement entre listes\n      transferArrayItem(event.previousContainer.data, event.container.data, event.previousIndex, event.currentIndex);\n      // Mettre à jour le statut de la tâche\n      const task = event.container.data[event.currentIndex];\n      let newStatus;\n      if (event.container.id === 'todo-list') {\n        newStatus = 'todo';\n      } else if (event.container.id === 'in-progress-list') {\n        newStatus = 'in-progress';\n      } else {\n        newStatus = 'done';\n      }\n      if (task._id && task.status !== newStatus) {\n        task.status = newStatus;\n        this.updateTaskStatus(task, newStatus);\n      }\n    }\n  }\n  loadUsers() {\n    // Utiliser les données de test si l'API n'est pas disponible\n    const useMockData = false; // Mettre à true pour utiliser les données de test\n    if (useMockData) {\n      // Données de test pour simuler les utilisateurs\n      const mockUsers = [{\n        _id: 'user1',\n        username: 'john_doe',\n        email: '<EMAIL>',\n        role: 'admin',\n        isActive: true\n      }, {\n        _id: 'user2',\n        username: 'jane_smith',\n        email: '<EMAIL>',\n        role: 'student',\n        isActive: true\n      }];\n      setTimeout(() => {\n        this.users = mockUsers;\n        console.log('Utilisateurs chargés (mock):', this.users);\n      }, 400);\n    } else {\n      // TODO: Implémenter l'API réelle pour récupérer les utilisateurs\n      // Pour l'instant, utiliser les données mockées\n      const mockUsers = [{\n        _id: 'user1',\n        username: 'john_doe',\n        email: '<EMAIL>',\n        role: 'admin',\n        isActive: true\n      }, {\n        _id: 'user2',\n        username: 'jane_smith',\n        email: '<EMAIL>',\n        role: 'student',\n        isActive: true\n      }];\n      this.users = mockUsers;\n      console.log('Utilisateurs chargés (mock API):', this.users);\n    }\n  }\n  getUserName(userId) {\n    const user = this.users.find(u => u._id === userId || u.id === userId);\n    if (user) {\n      if (user.firstName && user.lastName) {\n        return `${user.firstName} ${user.lastName}`;\n      } else if (user.name) {\n        return user.name;\n      }\n    }\n    return 'Utilisateur inconnu';\n  }\n  createTask() {\n    if (!this.teamId) {\n      this.notificationService.showError(\"ID d'équipe manquant\");\n      return;\n    }\n    this.newTask.teamId = this.teamId;\n    this.loading = true;\n    this.taskService.createTask(this.newTask).pipe(finalize(() => this.loading = false)).subscribe({\n      next: data => {\n        this.tasks.push(data);\n        this.sortTasks();\n        this.newTask = this.initializeNewTask();\n        this.showTaskForm = false;\n        this.notificationService.showSuccess('Tâche créée avec succès');\n      },\n      error: error => {\n        console.error('Erreur lors de la création de la tâche:', error);\n        this.notificationService.showError('Erreur lors de la création de la tâche');\n      }\n    });\n  }\n  updateTask() {\n    if (!this.editingTask || !this.editingTask._id) {\n      this.notificationService.showError('Tâche invalide');\n      return;\n    }\n    this.loading = true;\n    this.taskService.updateTask(this.editingTask._id, this.editingTask).pipe(finalize(() => this.loading = false)).subscribe({\n      next: data => {\n        const index = this.tasks.findIndex(t => t._id === data._id);\n        if (index !== -1) {\n          this.tasks[index] = data;\n        }\n        this.editingTask = null;\n        this.notificationService.showSuccess('Tâche mise à jour avec succès');\n      },\n      error: error => {\n        console.error('Erreur lors de la mise à jour de la tâche:', error);\n        this.notificationService.showError('Erreur lors de la mise à jour de la tâche');\n      }\n    });\n  }\n  deleteTask(id) {\n    if (confirm('Êtes-vous sûr de vouloir supprimer cette tâche ?')) {\n      this.loading = true;\n      this.taskService.deleteTask(id).pipe(finalize(() => this.loading = false)).subscribe({\n        next: () => {\n          this.tasks = this.tasks.filter(t => t._id !== id);\n          this.notificationService.showSuccess('Tâche supprimée avec succès');\n        },\n        error: error => {\n          console.error('Erreur lors de la suppression de la tâche:', error);\n          this.notificationService.showError('Erreur lors de la suppression de la tâche');\n        }\n      });\n    }\n  }\n  updateTaskStatus(task, status) {\n    if (!task._id) return;\n    this.loading = true;\n    this.taskService.updateTaskStatus(task._id, status).pipe(finalize(() => this.loading = false)).subscribe({\n      next: data => {\n        const index = this.tasks.findIndex(t => t._id === data._id);\n        if (index !== -1) {\n          this.tasks[index] = data;\n        }\n        this.notificationService.showSuccess('Statut de la tâche mis à jour');\n      },\n      error: error => {\n        console.error('Erreur lors de la mise à jour du statut:', error);\n        this.notificationService.showError('Erreur lors de la mise à jour du statut');\n      }\n    });\n  }\n  editTask(task) {\n    this.editingTask = {\n      ...task\n    };\n  }\n  cancelEdit() {\n    this.editingTask = null;\n  }\n  toggleTaskForm() {\n    this.showTaskForm = !this.showTaskForm;\n    if (this.showTaskForm) {\n      this.newTask = this.initializeNewTask();\n    }\n  }\n  initializeNewTask() {\n    return {\n      title: '',\n      description: '',\n      status: 'todo',\n      priority: 'medium',\n      teamId: this.teamId || '',\n      dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // Par défaut, une semaine à partir d'aujourd'hui\n    };\n  }\n\n  sortTasks() {\n    // Trier par priorité (high > medium > low) puis par statut (todo > in-progress > done)\n    this.tasks.sort((a, b) => {\n      const priorityOrder = {\n        high: 0,\n        medium: 1,\n        low: 2\n      };\n      const statusOrder = {\n        todo: 0,\n        'in-progress': 1,\n        done: 2\n      };\n      // D'abord par priorité\n      if (priorityOrder[a.priority] !== priorityOrder[b.priority]) {\n        return priorityOrder[a.priority] - priorityOrder[b.priority];\n      }\n      // Ensuite par statut\n      return statusOrder[a.status] - statusOrder[b.status];\n    });\n  }\n  // Méthodes de filtrage\n  filterTasks() {\n    return this.tasks.filter(task => {\n      // Filtre par statut\n      if (this.statusFilter !== 'all' && task.status !== this.statusFilter) {\n        return false;\n      }\n      // Filtre par priorité\n      if (this.priorityFilter !== 'all' && task.priority !== this.priorityFilter) {\n        return false;\n      }\n      // Filtre par terme de recherche\n      if (this.searchTerm && !task.title.toLowerCase().includes(this.searchTerm.toLowerCase())) {\n        return false;\n      }\n      return true;\n    });\n  }\n  // Méthodes pour obtenir les tâches par statut\n  getTodoTasks() {\n    return this.tasks.filter(task => task.status === 'todo' && (this.priorityFilter === 'all' || task.priority === this.priorityFilter) && (!this.searchTerm || task.title.toLowerCase().includes(this.searchTerm.toLowerCase())));\n  }\n  getInProgressTasks() {\n    return this.tasks.filter(task => task.status === 'in-progress' && (this.priorityFilter === 'all' || task.priority === this.priorityFilter) && (!this.searchTerm || task.title.toLowerCase().includes(this.searchTerm.toLowerCase())));\n  }\n  getDoneTasks() {\n    return this.tasks.filter(task => task.status === 'done' && (this.priorityFilter === 'all' || task.priority === this.priorityFilter) && (!this.searchTerm || task.title.toLowerCase().includes(this.searchTerm.toLowerCase())));\n  }\n  // Méthodes pour compter les tâches par statut\n  getTodoTasksCount() {\n    return this.tasks.filter(task => task.status === 'todo').length;\n  }\n  getInProgressTasksCount() {\n    return this.tasks.filter(task => task.status === 'in-progress').length;\n  }\n  getDoneTasksCount() {\n    return this.tasks.filter(task => task.status === 'done').length;\n  }\n  navigateBack() {\n    this.router.navigate(['/liste']);\n  }\n  static {\n    this.ɵfac = function TaskListComponent_Factory(t) {\n      return new (t || TaskListComponent)(i0.ɵɵdirectiveInject(i1.TaskService), i0.ɵɵdirectiveInject(i2.EquipeService), i0.ɵɵdirectiveInject(i3.AuthuserService), i0.ɵɵdirectiveInject(i4.ActivatedRoute), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i5.NotificationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TaskListComponent,\n      selectors: [[\"app-task-list\"]],\n      decls: 23,\n      vars: 7,\n      consts: [[1, \"container-fluid\", \"py-5\", \"bg-light\"], [1, \"container\"], [1, \"row\", \"mb-5\"], [1, \"col-12\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"flex-wrap\"], [\"class\", \"display-4 fw-bold text-primary\", 4, \"ngIf\"], [1, \"text-muted\", \"lead\"], [1, \"d-flex\", \"gap-2\"], [1, \"btn\", \"btn-outline-secondary\", \"rounded-pill\", \"px-4\", \"py-2\", 3, \"click\"], [1, \"bi\", \"bi-arrow-left\", \"me-2\"], [1, \"btn\", \"btn-primary\", \"rounded-pill\", \"px-4\", \"py-2\", 3, \"click\"], [1, \"bi\", \"bi-plus-circle\", \"me-2\"], [1, \"my-4\"], [\"class\", \"row justify-content-center my-5\", 4, \"ngIf\"], [\"class\", \"row mb-5\", 4, \"ngIf\"], [\"class\", \"row mb-4\", 4, \"ngIf\"], [\"class\", \"row g-4\", 4, \"ngIf\"], [1, \"display-4\", \"fw-bold\", \"text-primary\"], [1, \"row\", \"justify-content-center\", \"my-5\"], [1, \"col-md-6\", \"text-center\"], [\"role\", \"status\", 1, \"spinner-grow\", \"text-primary\", \"mx-1\"], [1, \"visually-hidden\"], [\"role\", \"status\", 1, \"spinner-grow\", \"text-secondary\", \"mx-1\"], [1, \"mt-3\", \"text-muted\"], [1, \"col-md-8\"], [1, \"alert\", \"alert-danger\", \"shadow-sm\", \"border-0\", \"rounded-3\", \"d-flex\", \"align-items-center\"], [1, \"bi\", \"bi-exclamation-triangle-fill\", \"fs-3\", \"me-3\"], [1, \"flex-grow-1\"], [1, \"btn\", \"btn-danger\", \"rounded-pill\", \"ms-3\", 3, \"click\"], [1, \"bi\", \"bi-arrow-clockwise\", \"me-1\"], [1, \"card\", \"border-0\", \"shadow-sm\", \"rounded-3\"], [1, \"card-header\", \"bg-primary\", \"text-white\", \"py-3\"], [1, \"mb-0\"], [1, \"card-body\", \"p-4\"], [3, \"ngSubmit\"], [1, \"row\", \"g-3\"], [1, \"col-md-6\"], [\"for\", \"taskTitle\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"taskTitle\", \"required\", \"\", \"name\", \"title\", \"placeholder\", \"Titre de la t\\u00E2che\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [1, \"col-md-3\"], [\"for\", \"taskPriority\", 1, \"form-label\"], [\"id\", \"taskPriority\", \"required\", \"\", \"name\", \"priority\", 1, \"form-select\", 3, \"ngModel\", \"ngModelChange\"], [\"value\", \"low\"], [\"value\", \"medium\"], [\"value\", \"high\"], [\"for\", \"taskStatus\", 1, \"form-label\"], [\"id\", \"taskStatus\", \"required\", \"\", \"name\", \"status\", 1, \"form-select\", 3, \"ngModel\", \"ngModelChange\"], [\"value\", \"todo\"], [\"value\", \"in-progress\"], [\"value\", \"done\"], [\"for\", \"taskAssignedTo\", 1, \"form-label\"], [\"id\", \"taskAssignedTo\", \"name\", \"assignedTo\", 1, \"form-select\", 3, \"ngModel\", \"ngModelChange\"], [3, \"value\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"taskDueDate\", 1, \"form-label\"], [\"type\", \"date\", \"id\", \"taskDueDate\", \"name\", \"dueDate\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"taskDescription\", 1, \"form-label\"], [\"id\", \"taskDescription\", \"rows\", \"3\", \"name\", \"description\", \"placeholder\", \"Description d\\u00E9taill\\u00E9e de la t\\u00E2che\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [1, \"col-12\", \"d-flex\", \"justify-content-end\", \"gap-2\", \"mt-4\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-secondary\", \"rounded-pill\", \"px-4\", 3, \"click\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"rounded-pill\", \"px-4\"], [1, \"row\", \"mb-4\"], [1, \"card-body\", \"p-3\"], [1, \"col-md-4\"], [1, \"input-group\"], [1, \"input-group-text\", \"bg-white\", \"border-end-0\"], [1, \"bi\", \"bi-search\"], [\"type\", \"text\", \"placeholder\", \"Rechercher une t\\u00E2che...\", 1, \"form-control\", \"border-start-0\", 3, \"ngModel\", \"ngModelChange\"], [1, \"form-select\", 3, \"ngModel\", \"ngModelChange\"], [\"value\", \"all\"], [1, \"col-md-8\", \"text-center\"], [1, \"p-5\", \"bg-white\", \"rounded-3\", \"shadow-sm\"], [1, \"bi\", \"bi-list-check\", \"fs-1\", \"text-muted\", \"mb-3\"], [1, \"mb-3\"], [1, \"text-muted\", \"mb-4\"], [1, \"row\", \"g-4\"], [1, \"card\", \"border-0\", \"shadow-sm\", \"rounded-3\", \"h-100\"], [1, \"mb-0\", \"d-flex\", \"align-items-center\"], [1, \"bi\", \"bi-list-task\", \"me-2\"], [1, \"badge\", \"bg-white\", \"text-primary\", \"rounded-pill\", \"ms-2\"], [\"cdkDropList\", \"\", \"id\", \"todo-list\", 1, \"task-list\", 3, \"cdkDropListData\", \"cdkDropListConnectedTo\", \"cdkDropListDropped\"], [\"todoList\", \"cdkDropList\"], [\"class\", \"task-card mb-3 p-3 rounded-3 shadow-sm\", \"cdkDrag\", \"\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [1, \"card-header\", \"bg-warning\", \"py-3\"], [1, \"bi\", \"bi-hourglass-split\", \"me-2\"], [1, \"badge\", \"bg-white\", \"text-warning\", \"rounded-pill\", \"ms-2\"], [\"cdkDropList\", \"\", \"id\", \"in-progress-list\", 1, \"task-list\", 3, \"cdkDropListData\", \"cdkDropListConnectedTo\", \"cdkDropListDropped\"], [\"inProgressList\", \"cdkDropList\"], [1, \"card-header\", \"bg-success\", \"text-white\", \"py-3\"], [1, \"bi\", \"bi-check2-all\", \"me-2\"], [1, \"badge\", \"bg-white\", \"text-success\", \"rounded-pill\", \"ms-2\"], [\"cdkDropList\", \"\", \"id\", \"done-list\", 1, \"task-list\", 3, \"cdkDropListData\", \"cdkDropListConnectedTo\", \"cdkDropListDropped\"], [\"doneList\", \"cdkDropList\"], [\"class\", \"task-card mb-3 p-3 rounded-3 shadow-sm completed-task\", \"cdkDrag\", \"\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [\"cdkDrag\", \"\", 1, \"task-card\", \"mb-3\", \"p-3\", \"rounded-3\", \"shadow-sm\", 3, \"ngClass\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-start\", \"mb-2\"], [1, \"mb-0\", \"text-truncate\"], [1, \"dropdown\"], [\"type\", \"button\", \"data-bs-toggle\", \"dropdown\", 1, \"btn\", \"btn-sm\", \"btn-link\", \"text-dark\", \"p-0\"], [1, \"bi\", \"bi-three-dots-vertical\"], [1, \"dropdown-menu\", \"dropdown-menu-end\"], [1, \"dropdown-item\", 3, \"click\"], [1, \"bi\", \"bi-pencil\", \"me-2\"], [1, \"bi\", \"bi-arrow-right\", \"me-2\"], [1, \"dropdown-divider\"], [1, \"dropdown-item\", \"text-danger\", 3, \"click\"], [1, \"bi\", \"bi-trash\", \"me-2\"], [1, \"small\", \"text-muted\", \"mb-2\", \"task-description\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"badge\", 3, \"ngClass\"], [\"class\", \"text-muted\", 4, \"ngIf\"], [\"cdkDragHandle\", \"\", 1, \"task-drag-handle\"], [1, \"bi\", \"bi-grip-horizontal\"], [1, \"text-muted\"], [\"cdkDrag\", \"\", 1, \"task-card\", \"mb-3\", \"p-3\", \"rounded-3\", \"shadow-sm\", \"completed-task\", 3, \"ngClass\"]],\n      template: function TaskListComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"div\");\n          i0.ɵɵtemplate(6, TaskListComponent_h1_6_Template, 2, 1, \"h1\", 5);\n          i0.ɵɵelementStart(7, \"p\", 6);\n          i0.ɵɵtext(8, \"G\\u00E9rez les t\\u00E2ches de votre \\u00E9quipe\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"div\", 7)(10, \"button\", 8);\n          i0.ɵɵlistener(\"click\", function TaskListComponent_Template_button_click_10_listener() {\n            return ctx.navigateBack();\n          });\n          i0.ɵɵelement(11, \"i\", 9);\n          i0.ɵɵtext(12, \" Retour \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"button\", 10);\n          i0.ɵɵlistener(\"click\", function TaskListComponent_Template_button_click_13_listener() {\n            return ctx.toggleTaskForm();\n          });\n          i0.ɵɵelement(14, \"i\", 11);\n          i0.ɵɵtext(15, \" Nouvelle t\\u00E2che \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelement(16, \"hr\", 12);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(17, TaskListComponent_div_17_Template, 13, 0, \"div\", 13);\n          i0.ɵɵtemplate(18, TaskListComponent_div_18_Template, 9, 1, \"div\", 13);\n          i0.ɵɵtemplate(19, TaskListComponent_div_19_Template, 53, 10, \"div\", 14);\n          i0.ɵɵtemplate(20, TaskListComponent_div_20_Template, 30, 3, \"div\", 15);\n          i0.ɵɵtemplate(21, TaskListComponent_div_21_Template, 11, 0, \"div\", 13);\n          i0.ɵɵtemplate(22, TaskListComponent_div_22_Template, 37, 21, \"div\", 16);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.team);\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showTaskForm || ctx.editingTask);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.tasks.length > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && !ctx.error && ctx.tasks.length === 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.tasks.length > 0);\n        }\n      },\n      dependencies: [i6.NgClass, i6.NgForOf, i6.NgIf, i7.ɵNgNoValidate, i7.NgSelectOption, i7.ɵNgSelectMultipleOption, i7.DefaultValueAccessor, i7.SelectControlValueAccessor, i7.NgControlStatus, i7.NgControlStatusGroup, i7.RequiredValidator, i7.NgModel, i7.NgForm, i8.CdkDropList, i8.CdkDrag, i8.CdkDragHandle],\n      styles: [\".task-card[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-left: 4px solid transparent;\\n  transition: all 0.2s ease;\\n  position: relative;\\n  cursor: move;\\n  margin-bottom: 12px;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 4px rgba(0,0,0,0.05);\\n}\\n\\n.task-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 8px rgba(0,0,0,0.1) !important;\\n}\\n\\n.task-card.priority-high[_ngcontent-%COMP%] {\\n  border-left-color: #dc3545;\\n}\\n\\n.task-card.priority-medium[_ngcontent-%COMP%] {\\n  border-left-color: #ffc107;\\n}\\n\\n.task-card.priority-low[_ngcontent-%COMP%] {\\n  border-left-color: #0dcaf0;\\n}\\n\\n.completed-task[_ngcontent-%COMP%] {\\n  opacity: 0.7;\\n}\\n\\n.task-description[_ngcontent-%COMP%] {\\n  max-height: 3em;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  display: -webkit-box;\\n  -webkit-line-clamp: 2;\\n  -webkit-box-orient: vertical;\\n  font-size: 0.9rem;\\n  color: #6c757d;\\n  margin-bottom: 10px;\\n}\\n\\n.task-list[_ngcontent-%COMP%] {\\n  min-height: 50px;\\n  max-height: 500px;\\n  overflow-y: auto;\\n  padding: 8px;\\n  border-radius: 4px;\\n}\\n\\n\\n\\n.card-header.bg-primary[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #007bff, #6610f2) !important;\\n}\\n\\n.card-header.bg-warning[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #ffc107, #fd7e14) !important;\\n}\\n\\n.card-header.bg-success[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #28a745, #20c997) !important;\\n}\\n\\n\\n\\n.cdk-drag-preview[_ngcontent-%COMP%] {\\n  box-sizing: border-box;\\n  border-radius: 8px;\\n  box-shadow: 0 5px 15px rgba(0,0,0,0.2) !important;\\n  opacity: 0.8;\\n}\\n\\n.cdk-drag-placeholder[_ngcontent-%COMP%] {\\n  opacity: 0.3;\\n}\\n\\n.cdk-drag-animating[_ngcontent-%COMP%] {\\n  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);\\n}\\n\\n.task-list.cdk-drop-list-dragging[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]:not(.cdk-drag-placeholder) {\\n  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);\\n}\\n\\n\\n\\n.task-drag-handle[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 5px;\\n  right: 5px;\\n  color: #adb5bd;\\n  cursor: move;\\n  font-size: 0.8rem;\\n}\\n\\n\\n\\n.kanban-column[_ngcontent-%COMP%] {\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n  border-radius: 8px;\\n  overflow: hidden;\\n  box-shadow: 0 4px 6px rgba(0,0,0,0.1);\\n}\\n\\n.kanban-column-header[_ngcontent-%COMP%] {\\n  padding: 15px;\\n  border-top-left-radius: 8px;\\n  border-top-right-radius: 8px;\\n}\\n\\n.kanban-column-content[_ngcontent-%COMP%] {\\n  flex-grow: 1;\\n  background-color: #f8f9fa;\\n  padding: 15px;\\n  border-bottom-left-radius: 8px;\\n  border-bottom-right-radius: 8px;\\n  min-height: 300px;\\n  max-height: 600px;\\n  overflow-y: auto;\\n}\\n\\n\\n  .task-card[_ngcontent-%COMP%] {\\n    background-color: white;\\n    border-left: 4px solid transparent;\\n    transition: all 0.2s ease;\\n  }\\n\\n  .task-card[_ngcontent-%COMP%]:hover {\\n    transform: translateY(-2px);\\n    box-shadow: 0 4px 8px rgba(0,0,0,0.1) !important;\\n  }\\n\\n  .task-card.priority-high[_ngcontent-%COMP%] {\\n    border-left-color: #dc3545;\\n  }\\n\\n  .task-card.priority-medium[_ngcontent-%COMP%] {\\n    border-left-color: #ffc107;\\n  }\\n\\n  .task-card.priority-low[_ngcontent-%COMP%] {\\n    border-left-color: #0dcaf0;\\n  }\\n\\n  .completed-task[_ngcontent-%COMP%] {\\n    opacity: 0.7;\\n  }\\n\\n  .task-description[_ngcontent-%COMP%] {\\n    max-height: 3em;\\n    overflow: hidden;\\n    text-overflow: ellipsis;\\n    display: -webkit-box;\\n    -webkit-line-clamp: 2;\\n    -webkit-box-orient: vertical;\\n  }\\n\\n  .task-list[_ngcontent-%COMP%] {\\n    max-height: 500px;\\n    overflow-y: auto;\\n  }\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["moveItemInArray", "transferArrayItem", "finalize", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "team", "name", "ɵɵelement", "ɵɵlistener", "TaskListComponent_div_18_Template_button_click_6_listener", "ɵɵrestoreView", "_r8", "ctx_r7", "ɵɵnextContext", "ɵɵresetView", "teamId", "loadTasks", "ctx_r2", "error", "ɵɵproperty", "user_r10", "_id", "id", "ctx_r9", "getUserName", "TaskListComponent_div_19_Template_form_ngSubmit_7_listener", "_r12", "ctx_r11", "editingTask", "updateTask", "createTask", "TaskListComponent_div_19_Template_input_ngModelChange_12_listener", "$event", "ctx_r13", "title", "newTask", "TaskListComponent_div_19_Template_select_ngModelChange_16_listener", "ctx_r14", "priority", "TaskListComponent_div_19_Template_select_ngModelChange_26_listener", "ctx_r15", "status", "TaskListComponent_div_19_Template_select_ngModelChange_36_listener", "ctx_r16", "assignedTo", "ɵɵtemplate", "TaskListComponent_div_19_option_39_Template", "TaskListComponent_div_19_Template_input_ngModelChange_43_listener", "ctx_r17", "dueDate", "TaskListComponent_div_19_Template_textarea_ngModelChange_47_listener", "ctx_r18", "description", "TaskListComponent_div_19_Template_button_click_49_listener", "ctx_r19", "cancelEdit", "toggleTaskForm", "ctx_r3", "users", "TaskListComponent_div_20_Template_input_ngModelChange_9_listener", "_r21", "ctx_r20", "searchTerm", "TaskListComponent_div_20_Template_select_ngModelChange_11_listener", "ctx_r22", "statusFilter", "TaskListComponent_div_20_Template_select_ngModelChange_21_listener", "ctx_r23", "priorityFilter", "ctx_r4", "TaskListComponent_div_21_Template_button_click_8_listener", "_r25", "ctx_r24", "ctx_r33", "task_r32", "TaskListComponent_div_22_div_12_Template_button_click_9_listener", "restoredCtx", "_r36", "$implicit", "ctx_r35", "editTask", "TaskListComponent_div_22_div_12_Template_button_click_13_listener", "ctx_r37", "updateTaskStatus", "TaskListComponent_div_22_div_12_Template_button_click_17_listener", "ctx_r38", "TaskListComponent_div_22_div_12_Template_button_click_23_listener", "ctx_r39", "deleteTask", "TaskListComponent_div_22_div_12_small_31_Template", "ɵɵtextInterpolate", "ɵɵpureFunction3", "_c0", "ctx_r41", "task_r40", "TaskListComponent_div_22_div_24_Template_button_click_9_listener", "_r44", "ctx_r43", "TaskListComponent_div_22_div_24_Template_button_click_13_listener", "ctx_r45", "TaskListComponent_div_22_div_24_Template_button_click_17_listener", "ctx_r46", "TaskListComponent_div_22_div_24_Template_button_click_23_listener", "ctx_r47", "TaskListComponent_div_22_div_24_small_31_Template", "ctx_r49", "task_r48", "TaskListComponent_div_22_div_36_Template_button_click_9_listener", "_r52", "ctx_r51", "TaskListComponent_div_22_div_36_Template_button_click_13_listener", "ctx_r53", "TaskListComponent_div_22_div_36_Template_button_click_17_listener", "ctx_r54", "TaskListComponent_div_22_div_36_Template_button_click_23_listener", "ctx_r55", "TaskListComponent_div_22_div_36_small_31_Template", "TaskListComponent_div_22_Template_div_cdkDropListDropped_10_listener", "_r57", "ctx_r56", "drop", "TaskListComponent_div_22_div_12_Template", "TaskListComponent_div_22_Template_div_cdkDropListDropped_22_listener", "ctx_r58", "TaskListComponent_div_22_div_24_Template", "TaskListComponent_div_22_Template_div_cdkDropListDropped_34_listener", "ctx_r59", "TaskListComponent_div_22_div_36_Template", "ctx_r6", "getTodoTasksCount", "getTodoTasks", "ɵɵpureFunction2", "_c1", "_r28", "_r30", "getInProgressTasksCount", "getInProgressTasks", "_r26", "getDoneTasksCount", "getDoneTasks", "TaskListComponent", "constructor", "taskService", "equipeService", "userService", "route", "router", "notificationService", "tasks", "loading", "showTaskForm", "ngOnInit", "initializeNewTask", "paramMap", "subscribe", "params", "get", "loadTeamDetails", "loadUsers", "showError", "useMockData", "mockTeam", "admin", "members", "setTimeout", "console", "log", "getEquipe", "pipe", "next", "data", "mockTasks", "sortTasks", "getTasksByTeam", "event", "previousContainer", "container", "previousIndex", "currentIndex", "task", "newStatus", "mockUsers", "username", "email", "role", "isActive", "userId", "user", "find", "u", "firstName", "lastName", "push", "showSuccess", "index", "findIndex", "t", "confirm", "filter", "Date", "now", "sort", "a", "b", "priorityOrder", "high", "medium", "low", "statusOrder", "todo", "done", "filterTasks", "toLowerCase", "includes", "length", "navigateBack", "navigate", "ɵɵdirectiveInject", "i1", "TaskService", "i2", "EquipeService", "i3", "AuthuserService", "i4", "ActivatedRoute", "Router", "i5", "NotificationService", "selectors", "decls", "vars", "consts", "template", "TaskListComponent_Template", "rf", "ctx", "TaskListComponent_h1_6_Template", "TaskListComponent_Template_button_click_10_listener", "TaskListComponent_Template_button_click_13_listener", "TaskListComponent_div_17_Template", "TaskListComponent_div_18_Template", "TaskListComponent_div_19_Template", "TaskListComponent_div_20_Template", "TaskListComponent_div_21_Template", "TaskListComponent_div_22_Template"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\equipes\\task-list\\task-list.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\equipes\\task-list\\task-list.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport {\n  CdkDragDrop,\n  moveItemInArray,\n  transferArrayItem,\n} from '@angular/cdk/drag-drop';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { TaskService } from 'src/app/services/task.service';\nimport { EquipeService } from 'src/app/services/equipe.service';\nimport { AuthuserService } from 'src/app/services/authuser.service';\nimport { NotificationService } from 'src/app/services/notification.service';\nimport { Task } from 'src/app/models/task.model';\nimport { Equipe } from 'src/app//models/equipe.model';\nimport { User } from 'src/app/models/user.model';\nimport { finalize } from 'rxjs/operators';\n\n@Component({\n  selector: 'app-task-list',\n  templateUrl: './task-list.component.html',\n  styleUrls: ['./task-list.component.css'],\n})\nexport class TaskListComponent implements OnInit {\n  tasks: Task[] = [];\n  teamId: string | null = null;\n  team: Equipe | null = null;\n  loading = false;\n  error: string | null = null;\n  users: User[] = [];\n  newTask!: Task;\n  editingTask: Task | null = null;\n  showTaskForm = false;\n\n  // Filtres\n  statusFilter: string = 'all';\n  priorityFilter: string = 'all';\n  searchTerm: string = '';\n\n  constructor(\n    private taskService: TaskService,\n    private equipeService: EquipeService,\n    private userService: AuthuserService,\n    private route: ActivatedRoute,\n    private router: Router,\n    private notificationService: NotificationService\n  ) {}\n\n  ngOnInit(): void {\n    // Initialiser la nouvelle tâche\n    this.newTask = this.initializeNewTask();\n\n    this.route.paramMap.subscribe((params) => {\n      this.teamId = params.get('id');\n      if (this.teamId) {\n        this.loadTeamDetails(this.teamId);\n        this.loadTasks(this.teamId);\n        this.loadUsers();\n      } else {\n        this.error = \"ID d'équipe manquant\";\n        this.notificationService.showError(\"ID d'équipe manquant\");\n      }\n    });\n  }\n\n  loadTeamDetails(teamId: string): void {\n    this.loading = true;\n\n    // Utiliser les données de test si l'API n'est pas disponible\n    const useMockData = false; // Mettre à true pour utiliser les données de test\n\n    if (useMockData) {\n      // Données de test pour simuler les détails de l'équipe\n      const mockTeam: Equipe = {\n        _id: teamId,\n        name: 'Équipe ' + teamId,\n        description: \"Description de l'équipe \" + teamId,\n        admin: 'admin123',\n        members: [],\n      };\n\n      setTimeout(() => {\n        this.team = mockTeam;\n        this.loading = false;\n        console.log(\"Détails de l'équipe chargés (mock):\", this.team);\n      }, 300);\n    } else {\n      // Utiliser l'API réelle\n      this.equipeService\n        .getEquipe(teamId)\n        .pipe(finalize(() => (this.loading = false)))\n        .subscribe({\n          next: (data) => {\n            this.team = data;\n            console.log(\"Détails de l'équipe chargés depuis l'API:\", this.team);\n          },\n          error: (error) => {\n            console.error(\n              \"Erreur lors du chargement des détails de l'équipe:\",\n              error\n            );\n            this.error = \"Impossible de charger les détails de l'équipe\";\n            this.notificationService.showError(\n              \"Erreur lors du chargement des détails de l'équipe\"\n            );\n\n            // Fallback aux données de test en cas d'erreur\n            const mockTeam: Equipe = {\n              _id: teamId,\n              name: 'Équipe ' + teamId + ' (fallback)',\n              description: \"Description de l'équipe \" + teamId,\n              admin: 'admin123',\n              members: [],\n            };\n\n            this.team = mockTeam;\n          },\n        });\n    }\n  }\n\n  loadTasks(teamId: string): void {\n    this.loading = true;\n\n    // Utiliser les données de test si l'API n'est pas disponible\n    const useMockData = false; // Mettre à true pour utiliser les données de test\n\n    if (useMockData) {\n      // Données de test pour simuler les tâches\n      const mockTasks: Task[] = [\n        {\n          _id: '1',\n          title: 'Tâche 1',\n          description: 'Description de la tâche 1',\n          status: 'todo',\n          priority: 'high',\n          teamId: teamId,\n        },\n        {\n          _id: '2',\n          title: 'Tâche 2',\n          description: 'Description de la tâche 2',\n          status: 'todo',\n          priority: 'medium',\n          teamId: teamId,\n        },\n        {\n          _id: '3',\n          title: 'Tâche 3',\n          description: 'Description de la tâche 3',\n          status: 'in-progress',\n          priority: 'high',\n          teamId: teamId,\n        },\n        {\n          _id: '4',\n          title: 'Tâche 4',\n          description: 'Description de la tâche 4',\n          status: 'done',\n          priority: 'low',\n          teamId: teamId,\n        },\n      ];\n\n      setTimeout(() => {\n        this.tasks = mockTasks;\n        this.sortTasks();\n        this.loading = false;\n        console.log('Tâches chargées (mock):', this.tasks);\n      }, 500);\n    } else {\n      // Utiliser l'API réelle\n      this.taskService\n        .getTasksByTeam(teamId)\n        .pipe(finalize(() => (this.loading = false)))\n        .subscribe({\n          next: (data: Task[]) => {\n            this.tasks = data;\n            this.sortTasks();\n            console.log(\"Tâches chargées depuis l'API:\", this.tasks);\n          },\n          error: (error: any) => {\n            console.error('Erreur lors du chargement des tâches:', error);\n            this.error = 'Impossible de charger les tâches';\n            this.notificationService.showError(\n              'Erreur lors du chargement des tâches'\n            );\n\n            // Fallback aux données de test en cas d'erreur\n            const mockTasks: Task[] = [\n              {\n                _id: '1',\n                title: 'Tâche 1 (fallback)',\n                description: 'Description de la tâche 1',\n                status: 'todo',\n                priority: 'high',\n                teamId: teamId,\n              },\n              {\n                _id: '2',\n                title: 'Tâche 2 (fallback)',\n                description: 'Description de la tâche 2',\n                status: 'todo',\n                priority: 'medium',\n                teamId: teamId,\n              },\n            ];\n\n            this.tasks = mockTasks;\n            this.sortTasks();\n            console.log('Tâches chargées (fallback):', this.tasks);\n          },\n        });\n    }\n  }\n\n  // Gestion du glisser-déposer\n  drop(event: CdkDragDrop<Task[]>) {\n    if (event.previousContainer === event.container) {\n      // Déplacement dans la même liste\n      moveItemInArray(\n        event.container.data,\n        event.previousIndex,\n        event.currentIndex\n      );\n    } else {\n      // Déplacement entre listes\n      transferArrayItem(\n        event.previousContainer.data,\n        event.container.data,\n        event.previousIndex,\n        event.currentIndex\n      );\n\n      // Mettre à jour le statut de la tâche\n      const task = event.container.data[event.currentIndex];\n      let newStatus: 'todo' | 'in-progress' | 'done';\n\n      if (event.container.id === 'todo-list') {\n        newStatus = 'todo';\n      } else if (event.container.id === 'in-progress-list') {\n        newStatus = 'in-progress';\n      } else {\n        newStatus = 'done';\n      }\n\n      if (task._id && task.status !== newStatus) {\n        task.status = newStatus;\n        this.updateTaskStatus(task, newStatus);\n      }\n    }\n  }\n\n  loadUsers(): void {\n    // Utiliser les données de test si l'API n'est pas disponible\n    const useMockData = false; // Mettre à true pour utiliser les données de test\n\n    if (useMockData) {\n      // Données de test pour simuler les utilisateurs\n      const mockUsers: User[] = [\n        {\n          _id: 'user1',\n          username: 'john_doe',\n          email: '<EMAIL>',\n          role: 'admin',\n          isActive: true,\n        },\n        {\n          _id: 'user2',\n          username: 'jane_smith',\n          email: '<EMAIL>',\n          role: 'student',\n          isActive: true,\n        },\n      ];\n\n      setTimeout(() => {\n        this.users = mockUsers;\n        console.log('Utilisateurs chargés (mock):', this.users);\n      }, 400);\n    } else {\n      // TODO: Implémenter l'API réelle pour récupérer les utilisateurs\n      // Pour l'instant, utiliser les données mockées\n      const mockUsers: User[] = [\n        {\n          _id: 'user1',\n          username: 'john_doe',\n          email: '<EMAIL>',\n          role: 'admin',\n          isActive: true,\n        },\n        {\n          _id: 'user2',\n          username: 'jane_smith',\n          email: '<EMAIL>',\n          role: 'student',\n          isActive: true,\n        },\n      ];\n\n      this.users = mockUsers;\n      console.log('Utilisateurs chargés (mock API):', this.users);\n    }\n  }\n\n  getUserName(userId: string): string {\n    const user = this.users.find((u) => u._id === userId || u.id === userId);\n    if (user) {\n      if (user.firstName && user.lastName) {\n        return `${user.firstName} ${user.lastName}`;\n      } else if (user.name) {\n        return user.name;\n      }\n    }\n    return 'Utilisateur inconnu';\n  }\n\n  createTask(): void {\n    if (!this.teamId) {\n      this.notificationService.showError(\"ID d'équipe manquant\");\n      return;\n    }\n\n    this.newTask.teamId = this.teamId;\n\n    this.loading = true;\n    this.taskService\n      .createTask(this.newTask)\n      .pipe(finalize(() => (this.loading = false)))\n      .subscribe({\n        next: (data: Task) => {\n          this.tasks.push(data);\n          this.sortTasks();\n          this.newTask = this.initializeNewTask();\n          this.showTaskForm = false;\n          this.notificationService.showSuccess('Tâche créée avec succès');\n        },\n        error: (error: any) => {\n          console.error('Erreur lors de la création de la tâche:', error);\n          this.notificationService.showError(\n            'Erreur lors de la création de la tâche'\n          );\n        },\n      });\n  }\n\n  updateTask(): void {\n    if (!this.editingTask || !this.editingTask._id) {\n      this.notificationService.showError('Tâche invalide');\n      return;\n    }\n\n    this.loading = true;\n    this.taskService\n      .updateTask(this.editingTask._id, this.editingTask)\n      .pipe(finalize(() => (this.loading = false)))\n      .subscribe({\n        next: (data: Task) => {\n          const index = this.tasks.findIndex((t) => t._id === data._id);\n          if (index !== -1) {\n            this.tasks[index] = data;\n          }\n          this.editingTask = null;\n          this.notificationService.showSuccess('Tâche mise à jour avec succès');\n        },\n        error: (error: any) => {\n          console.error('Erreur lors de la mise à jour de la tâche:', error);\n          this.notificationService.showError(\n            'Erreur lors de la mise à jour de la tâche'\n          );\n        },\n      });\n  }\n\n  deleteTask(id: string): void {\n    if (confirm('Êtes-vous sûr de vouloir supprimer cette tâche ?')) {\n      this.loading = true;\n      this.taskService\n        .deleteTask(id)\n        .pipe(finalize(() => (this.loading = false)))\n        .subscribe({\n          next: () => {\n            this.tasks = this.tasks.filter((t) => t._id !== id);\n            this.notificationService.showSuccess('Tâche supprimée avec succès');\n          },\n          error: (error: any) => {\n            console.error('Erreur lors de la suppression de la tâche:', error);\n            this.notificationService.showError(\n              'Erreur lors de la suppression de la tâche'\n            );\n          },\n        });\n    }\n  }\n\n  updateTaskStatus(task: Task, status: 'todo' | 'in-progress' | 'done'): void {\n    if (!task._id) return;\n\n    this.loading = true;\n    this.taskService\n      .updateTaskStatus(task._id, status)\n      .pipe(finalize(() => (this.loading = false)))\n      .subscribe({\n        next: (data: Task) => {\n          const index = this.tasks.findIndex((t) => t._id === data._id);\n          if (index !== -1) {\n            this.tasks[index] = data;\n          }\n          this.notificationService.showSuccess('Statut de la tâche mis à jour');\n        },\n        error: (error: any) => {\n          console.error('Erreur lors de la mise à jour du statut:', error);\n          this.notificationService.showError(\n            'Erreur lors de la mise à jour du statut'\n          );\n        },\n      });\n  }\n\n  editTask(task: Task): void {\n    this.editingTask = { ...task };\n  }\n\n  cancelEdit(): void {\n    this.editingTask = null;\n  }\n\n  toggleTaskForm(): void {\n    this.showTaskForm = !this.showTaskForm;\n    if (this.showTaskForm) {\n      this.newTask = this.initializeNewTask();\n    }\n  }\n\n  initializeNewTask(): Task {\n    return {\n      title: '',\n      description: '',\n      status: 'todo',\n      priority: 'medium',\n      teamId: this.teamId || '',\n      dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // Par défaut, une semaine à partir d'aujourd'hui\n    };\n  }\n\n  sortTasks(): void {\n    // Trier par priorité (high > medium > low) puis par statut (todo > in-progress > done)\n    this.tasks.sort((a, b) => {\n      const priorityOrder: { [key: string]: number } = {\n        high: 0,\n        medium: 1,\n        low: 2,\n      };\n      const statusOrder: { [key: string]: number } = {\n        todo: 0,\n        'in-progress': 1,\n        done: 2,\n      };\n\n      // D'abord par priorité\n      if (priorityOrder[a.priority] !== priorityOrder[b.priority]) {\n        return priorityOrder[a.priority] - priorityOrder[b.priority];\n      }\n\n      // Ensuite par statut\n      return statusOrder[a.status] - statusOrder[b.status];\n    });\n  }\n\n  // Méthodes de filtrage\n  filterTasks(): Task[] {\n    return this.tasks.filter((task) => {\n      // Filtre par statut\n      if (this.statusFilter !== 'all' && task.status !== this.statusFilter) {\n        return false;\n      }\n\n      // Filtre par priorité\n      if (\n        this.priorityFilter !== 'all' &&\n        task.priority !== this.priorityFilter\n      ) {\n        return false;\n      }\n\n      // Filtre par terme de recherche\n      if (\n        this.searchTerm &&\n        !task.title.toLowerCase().includes(this.searchTerm.toLowerCase())\n      ) {\n        return false;\n      }\n\n      return true;\n    });\n  }\n\n  // Méthodes pour obtenir les tâches par statut\n  getTodoTasks(): Task[] {\n    return this.tasks.filter(\n      (task) =>\n        task.status === 'todo' &&\n        (this.priorityFilter === 'all' ||\n          task.priority === this.priorityFilter) &&\n        (!this.searchTerm ||\n          task.title.toLowerCase().includes(this.searchTerm.toLowerCase()))\n    );\n  }\n\n  getInProgressTasks(): Task[] {\n    return this.tasks.filter(\n      (task) =>\n        task.status === 'in-progress' &&\n        (this.priorityFilter === 'all' ||\n          task.priority === this.priorityFilter) &&\n        (!this.searchTerm ||\n          task.title.toLowerCase().includes(this.searchTerm.toLowerCase()))\n    );\n  }\n\n  getDoneTasks(): Task[] {\n    return this.tasks.filter(\n      (task) =>\n        task.status === 'done' &&\n        (this.priorityFilter === 'all' ||\n          task.priority === this.priorityFilter) &&\n        (!this.searchTerm ||\n          task.title.toLowerCase().includes(this.searchTerm.toLowerCase()))\n    );\n  }\n\n  // Méthodes pour compter les tâches par statut\n  getTodoTasksCount(): number {\n    return this.tasks.filter((task) => task.status === 'todo').length;\n  }\n\n  getInProgressTasksCount(): number {\n    return this.tasks.filter((task) => task.status === 'in-progress').length;\n  }\n\n  getDoneTasksCount(): number {\n    return this.tasks.filter((task) => task.status === 'done').length;\n  }\n\n  navigateBack(): void {\n    this.router.navigate(['/liste']);\n  }\n}\n", "<div class=\"container-fluid py-5 bg-light\">\n  <div class=\"container\">\n    <!-- En-tête avec titre et actions -->\n    <div class=\"row mb-5\">\n      <div class=\"col-12\">\n        <div\n          class=\"d-flex justify-content-between align-items-center flex-wrap\"\n        >\n          <div>\n            <h1 class=\"display-4 fw-bold text-primary\" *ngIf=\"team\">\n              Tâches: {{ team.name }}\n            </h1>\n            <p class=\"text-muted lead\">Gérez les tâches de votre équipe</p>\n          </div>\n          <div class=\"d-flex gap-2\">\n            <button\n              class=\"btn btn-outline-secondary rounded-pill px-4 py-2\"\n              (click)=\"navigateBack()\"\n            >\n              <i class=\"bi bi-arrow-left me-2\"></i> Retour\n            </button>\n            <button\n              class=\"btn btn-primary rounded-pill px-4 py-2\"\n              (click)=\"toggleTaskForm()\"\n            >\n              <i class=\"bi bi-plus-circle me-2\"></i> Nouvelle tâche\n            </button>\n          </div>\n        </div>\n        <hr class=\"my-4\" />\n      </div>\n    </div>\n\n    <!-- Message de chargement -->\n    <div *ngIf=\"loading\" class=\"row justify-content-center my-5\">\n      <div class=\"col-md-6 text-center\">\n        <div class=\"spinner-grow text-primary mx-1\" role=\"status\">\n          <span class=\"visually-hidden\">Chargement...</span>\n        </div>\n        <div class=\"spinner-grow text-secondary mx-1\" role=\"status\">\n          <span class=\"visually-hidden\">Chargement...</span>\n        </div>\n        <div class=\"spinner-grow text-primary mx-1\" role=\"status\">\n          <span class=\"visually-hidden\">Chargement...</span>\n        </div>\n        <p class=\"mt-3 text-muted\">Chargement des tâches...</p>\n      </div>\n    </div>\n\n    <!-- Message d'erreur -->\n    <div *ngIf=\"error\" class=\"row justify-content-center my-5\">\n      <div class=\"col-md-8\">\n        <div\n          class=\"alert alert-danger shadow-sm border-0 rounded-3 d-flex align-items-center\"\n        >\n          <i class=\"bi bi-exclamation-triangle-fill fs-3 me-3\"></i>\n          <div class=\"flex-grow-1\">\n            {{ error }}\n          </div>\n          <button\n            class=\"btn btn-danger rounded-pill ms-3\"\n            (click)=\"teamId && loadTasks(teamId)\"\n          >\n            <i class=\"bi bi-arrow-clockwise me-1\"></i> Réessayer\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Formulaire de création/édition de tâche -->\n    <div *ngIf=\"showTaskForm || editingTask\" class=\"row mb-5\">\n      <div class=\"col-12\">\n        <div class=\"card border-0 shadow-sm rounded-3\">\n          <div class=\"card-header bg-primary text-white py-3\">\n            <h4 class=\"mb-0\">\n              {{ editingTask ? \"Modifier la tâche\" : \"Nouvelle tâche\" }}\n            </h4>\n          </div>\n          <div class=\"card-body p-4\">\n            <form (ngSubmit)=\"editingTask ? updateTask() : createTask()\">\n              <div class=\"row g-3\">\n                <div class=\"col-md-6\">\n                  <label for=\"taskTitle\" class=\"form-label\">Titre*</label>\n                  <input\n                    type=\"text\"\n                    class=\"form-control\"\n                    id=\"taskTitle\"\n                    required\n                    [ngModel]=\"editingTask ? editingTask.title : newTask.title\"\n                    (ngModelChange)=\"\n                      editingTask\n                        ? (editingTask.title = $event)\n                        : (newTask.title = $event)\n                    \"\n                    name=\"title\"\n                    placeholder=\"Titre de la tâche\"\n                  />\n                </div>\n                <div class=\"col-md-3\">\n                  <label for=\"taskPriority\" class=\"form-label\">Priorité*</label>\n                  <select\n                    class=\"form-select\"\n                    id=\"taskPriority\"\n                    required\n                    [(ngModel)]=\"\n                      editingTask ? editingTask.priority : newTask.priority\n                    \"\n                    name=\"priority\"\n                  >\n                    <option value=\"low\">Basse</option>\n                    <option value=\"medium\">Moyenne</option>\n                    <option value=\"high\">Haute</option>\n                  </select>\n                </div>\n                <div class=\"col-md-3\">\n                  <label for=\"taskStatus\" class=\"form-label\">Statut*</label>\n                  <select\n                    class=\"form-select\"\n                    id=\"taskStatus\"\n                    required\n                    [(ngModel)]=\"\n                      editingTask ? editingTask.status : newTask.status\n                    \"\n                    name=\"status\"\n                  >\n                    <option value=\"todo\">À faire</option>\n                    <option value=\"in-progress\">En cours</option>\n                    <option value=\"done\">Terminée</option>\n                  </select>\n                </div>\n                <div class=\"col-md-6\">\n                  <label for=\"taskAssignedTo\" class=\"form-label\"\n                    >Assignée à</label\n                  >\n                  <select\n                    class=\"form-select\"\n                    id=\"taskAssignedTo\"\n                    [(ngModel)]=\"\n                      editingTask ? editingTask.assignedTo : newTask.assignedTo\n                    \"\n                    name=\"assignedTo\"\n                  >\n                    <option [value]=\"null\">Non assignée</option>\n                    <option\n                      *ngFor=\"let user of users\"\n                      [value]=\"user._id || user.id\"\n                    >\n                      {{ getUserName(user._id || user.id || \"\") }}\n                    </option>\n                  </select>\n                </div>\n                <div class=\"col-md-6\">\n                  <label for=\"taskDueDate\" class=\"form-label\"\n                    >Date d'échéance</label\n                  >\n                  <input\n                    type=\"date\"\n                    class=\"form-control\"\n                    id=\"taskDueDate\"\n                    [(ngModel)]=\"\n                      editingTask ? editingTask.dueDate : newTask.dueDate\n                    \"\n                    name=\"dueDate\"\n                  />\n                </div>\n                <div class=\"col-12\">\n                  <label for=\"taskDescription\" class=\"form-label\"\n                    >Description</label\n                  >\n                  <textarea\n                    class=\"form-control\"\n                    id=\"taskDescription\"\n                    rows=\"3\"\n                    [(ngModel)]=\"\n                      editingTask\n                        ? editingTask.description\n                        : newTask.description\n                    \"\n                    name=\"description\"\n                    placeholder=\"Description détaillée de la tâche\"\n                  ></textarea>\n                </div>\n                <div class=\"col-12 d-flex justify-content-end gap-2 mt-4\">\n                  <button\n                    type=\"button\"\n                    class=\"btn btn-outline-secondary rounded-pill px-4\"\n                    (click)=\"editingTask ? cancelEdit() : toggleTaskForm()\"\n                  >\n                    Annuler\n                  </button>\n                  <button\n                    type=\"submit\"\n                    class=\"btn btn-primary rounded-pill px-4\"\n                  >\n                    {{ editingTask ? \"Mettre à jour\" : \"Créer\" }}\n                  </button>\n                </div>\n              </div>\n            </form>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Filtres et recherche -->\n    <div class=\"row mb-4\" *ngIf=\"tasks.length > 0\">\n      <div class=\"col-12\">\n        <div class=\"card border-0 shadow-sm rounded-3\">\n          <div class=\"card-body p-3\">\n            <div class=\"row g-3\">\n              <div class=\"col-md-4\">\n                <div class=\"input-group\">\n                  <span class=\"input-group-text bg-white border-end-0\">\n                    <i class=\"bi bi-search\"></i>\n                  </span>\n                  <input\n                    type=\"text\"\n                    class=\"form-control border-start-0\"\n                    placeholder=\"Rechercher une tâche...\"\n                    [(ngModel)]=\"searchTerm\"\n                  />\n                </div>\n              </div>\n              <div class=\"col-md-4\">\n                <select class=\"form-select\" [(ngModel)]=\"statusFilter\">\n                  <option value=\"all\">Tous les statuts</option>\n                  <option value=\"todo\">À faire</option>\n                  <option value=\"in-progress\">En cours</option>\n                  <option value=\"done\">Terminées</option>\n                </select>\n              </div>\n              <div class=\"col-md-4\">\n                <select class=\"form-select\" [(ngModel)]=\"priorityFilter\">\n                  <option value=\"all\">Toutes les priorités</option>\n                  <option value=\"high\">Haute</option>\n                  <option value=\"medium\">Moyenne</option>\n                  <option value=\"low\">Basse</option>\n                </select>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Message quand aucune tâche n'est trouvée -->\n    <div\n      *ngIf=\"!loading && !error && tasks.length === 0\"\n      class=\"row justify-content-center my-5\"\n    >\n      <div class=\"col-md-8 text-center\">\n        <div class=\"p-5 bg-white rounded-3 shadow-sm\">\n          <i class=\"bi bi-list-check fs-1 text-muted mb-3\"></i>\n          <h3 class=\"mb-3\">Aucune tâche trouvée</h3>\n          <p class=\"text-muted mb-4\">\n            Commencez par créer une nouvelle tâche pour votre équipe.\n          </p>\n          <button\n            class=\"btn btn-primary rounded-pill px-4 py-2\"\n            (click)=\"toggleTaskForm()\"\n          >\n            <i class=\"bi bi-plus-circle me-2\"></i> Créer une tâche\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Liste des tâches -->\n    <div class=\"row g-4\" *ngIf=\"tasks.length > 0\">\n      <!-- Colonne \"À faire\" -->\n      <div class=\"col-md-4\">\n        <div class=\"card border-0 shadow-sm rounded-3 h-100\">\n          <div class=\"card-header bg-primary text-white py-3\">\n            <h5 class=\"mb-0 d-flex align-items-center\">\n              <i class=\"bi bi-list-task me-2\"></i>\n              À faire\n              <span class=\"badge bg-white text-primary rounded-pill ms-2\">\n                {{ getTodoTasksCount() }}\n              </span>\n            </h5>\n          </div>\n          <div class=\"card-body p-3\">\n            <div\n              class=\"task-list\"\n              cdkDropList\n              #todoList=\"cdkDropList\"\n              [cdkDropListData]=\"getTodoTasks()\"\n              [cdkDropListConnectedTo]=\"[inProgressList, doneList]\"\n              id=\"todo-list\"\n              (cdkDropListDropped)=\"drop($event)\"\n            >\n              <div\n                *ngFor=\"let task of getTodoTasks()\"\n                class=\"task-card mb-3 p-3 rounded-3 shadow-sm\"\n                [ngClass]=\"'priority-' + task.priority\"\n                cdkDrag\n              >\n                <div\n                  class=\"d-flex justify-content-between align-items-start mb-2\"\n                >\n                  <h6 class=\"mb-0 text-truncate\">{{ task.title }}</h6>\n                  <div class=\"dropdown\">\n                    <button\n                      class=\"btn btn-sm btn-link text-dark p-0\"\n                      type=\"button\"\n                      data-bs-toggle=\"dropdown\"\n                    >\n                      <i class=\"bi bi-three-dots-vertical\"></i>\n                    </button>\n                    <ul class=\"dropdown-menu dropdown-menu-end\">\n                      <li>\n                        <button class=\"dropdown-item\" (click)=\"editTask(task)\">\n                          <i class=\"bi bi-pencil me-2\"></i> Modifier\n                        </button>\n                      </li>\n                      <li>\n                        <button\n                          class=\"dropdown-item\"\n                          (click)=\"updateTaskStatus(task, 'in-progress')\"\n                        >\n                          <i class=\"bi bi-arrow-right me-2\"></i> Déplacer vers\n                          \"En cours\"\n                        </button>\n                      </li>\n                      <li>\n                        <button\n                          class=\"dropdown-item\"\n                          (click)=\"updateTaskStatus(task, 'done')\"\n                        >\n                          <i class=\"bi bi-check2-all me-2\"></i> Marquer comme\n                          terminée\n                        </button>\n                      </li>\n                      <li><hr class=\"dropdown-divider\" /></li>\n                      <li>\n                        <button\n                          class=\"dropdown-item text-danger\"\n                          (click)=\"task._id && deleteTask(task._id)\"\n                        >\n                          <i class=\"bi bi-trash me-2\"></i> Supprimer\n                        </button>\n                      </li>\n                    </ul>\n                  </div>\n                </div>\n                <p class=\"small text-muted mb-2 task-description\">\n                  {{ task.description || \"Aucune description\" }}\n                </p>\n                <div class=\"d-flex justify-content-between align-items-center\">\n                  <span\n                    class=\"badge\"\n                    [ngClass]=\"{\n                      'bg-danger': task.priority === 'high',\n                      'bg-warning text-dark': task.priority === 'medium',\n                      'bg-info text-dark': task.priority === 'low'\n                    }\"\n                  >\n                    {{\n                      task.priority === \"high\"\n                        ? \"Haute\"\n                        : task.priority === \"medium\"\n                        ? \"Moyenne\"\n                        : \"Basse\"\n                    }}\n                  </span>\n                  <small class=\"text-muted\" *ngIf=\"task.assignedTo\">\n                    {{ getUserName(task.assignedTo) }}\n                  </small>\n                </div>\n                <!-- Poignée de glisser-déposer -->\n                <div class=\"task-drag-handle\" cdkDragHandle>\n                  <i class=\"bi bi-grip-horizontal\"></i>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Colonne \"En cours\" -->\n      <div class=\"col-md-4\">\n        <div class=\"card border-0 shadow-sm rounded-3 h-100\">\n          <div class=\"card-header bg-warning py-3\">\n            <h5 class=\"mb-0 d-flex align-items-center\">\n              <i class=\"bi bi-hourglass-split me-2\"></i>\n              En cours\n              <span class=\"badge bg-white text-warning rounded-pill ms-2\">\n                {{ getInProgressTasksCount() }}\n              </span>\n            </h5>\n          </div>\n          <div class=\"card-body p-3\">\n            <div\n              class=\"task-list\"\n              cdkDropList\n              #inProgressList=\"cdkDropList\"\n              [cdkDropListData]=\"getInProgressTasks()\"\n              [cdkDropListConnectedTo]=\"[todoList, doneList]\"\n              id=\"in-progress-list\"\n              (cdkDropListDropped)=\"drop($event)\"\n            >\n              <div\n                *ngFor=\"let task of getInProgressTasks()\"\n                class=\"task-card mb-3 p-3 rounded-3 shadow-sm\"\n                [ngClass]=\"'priority-' + task.priority\"\n                cdkDrag\n              >\n                <div\n                  class=\"d-flex justify-content-between align-items-start mb-2\"\n                >\n                  <h6 class=\"mb-0 text-truncate\">{{ task.title }}</h6>\n                  <div class=\"dropdown\">\n                    <button\n                      class=\"btn btn-sm btn-link text-dark p-0\"\n                      type=\"button\"\n                      data-bs-toggle=\"dropdown\"\n                    >\n                      <i class=\"bi bi-three-dots-vertical\"></i>\n                    </button>\n                    <ul class=\"dropdown-menu dropdown-menu-end\">\n                      <li>\n                        <button class=\"dropdown-item\" (click)=\"editTask(task)\">\n                          <i class=\"bi bi-pencil me-2\"></i> Modifier\n                        </button>\n                      </li>\n                      <li>\n                        <button\n                          class=\"dropdown-item\"\n                          (click)=\"updateTaskStatus(task, 'todo')\"\n                        >\n                          <i class=\"bi bi-arrow-left me-2\"></i> Déplacer vers \"À\n                          faire\"\n                        </button>\n                      </li>\n                      <li>\n                        <button\n                          class=\"dropdown-item\"\n                          (click)=\"updateTaskStatus(task, 'done')\"\n                        >\n                          <i class=\"bi bi-check2-all me-2\"></i> Marquer comme\n                          terminée\n                        </button>\n                      </li>\n                      <li><hr class=\"dropdown-divider\" /></li>\n                      <li>\n                        <button\n                          class=\"dropdown-item text-danger\"\n                          (click)=\"task._id && deleteTask(task._id)\"\n                        >\n                          <i class=\"bi bi-trash me-2\"></i> Supprimer\n                        </button>\n                      </li>\n                    </ul>\n                  </div>\n                </div>\n                <p class=\"small text-muted mb-2 task-description\">\n                  {{ task.description || \"Aucune description\" }}\n                </p>\n                <div class=\"d-flex justify-content-between align-items-center\">\n                  <span\n                    class=\"badge\"\n                    [ngClass]=\"{\n                      'bg-danger': task.priority === 'high',\n                      'bg-warning text-dark': task.priority === 'medium',\n                      'bg-info text-dark': task.priority === 'low'\n                    }\"\n                  >\n                    {{\n                      task.priority === \"high\"\n                        ? \"Haute\"\n                        : task.priority === \"medium\"\n                        ? \"Moyenne\"\n                        : \"Basse\"\n                    }}\n                  </span>\n                  <small class=\"text-muted\" *ngIf=\"task.assignedTo\">\n                    {{ getUserName(task.assignedTo) }}\n                  </small>\n                </div>\n                <!-- Poignée de glisser-déposer -->\n                <div class=\"task-drag-handle\" cdkDragHandle>\n                  <i class=\"bi bi-grip-horizontal\"></i>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Colonne \"Terminées\" -->\n      <div class=\"col-md-4\">\n        <div class=\"card border-0 shadow-sm rounded-3 h-100\">\n          <div class=\"card-header bg-success text-white py-3\">\n            <h5 class=\"mb-0 d-flex align-items-center\">\n              <i class=\"bi bi-check2-all me-2\"></i>\n              Terminées\n              <span class=\"badge bg-white text-success rounded-pill ms-2\">\n                {{ getDoneTasksCount() }}\n              </span>\n            </h5>\n          </div>\n          <div class=\"card-body p-3\">\n            <div\n              class=\"task-list\"\n              cdkDropList\n              #doneList=\"cdkDropList\"\n              [cdkDropListData]=\"getDoneTasks()\"\n              [cdkDropListConnectedTo]=\"[todoList, inProgressList]\"\n              id=\"done-list\"\n              (cdkDropListDropped)=\"drop($event)\"\n            >\n              <div\n                *ngFor=\"let task of getDoneTasks()\"\n                class=\"task-card mb-3 p-3 rounded-3 shadow-sm completed-task\"\n                [ngClass]=\"'priority-' + task.priority\"\n                cdkDrag\n              >\n                <div\n                  class=\"d-flex justify-content-between align-items-start mb-2\"\n                >\n                  <h6 class=\"mb-0 text-truncate\">{{ task.title }}</h6>\n                  <div class=\"dropdown\">\n                    <button\n                      class=\"btn btn-sm btn-link text-dark p-0\"\n                      type=\"button\"\n                      data-bs-toggle=\"dropdown\"\n                    >\n                      <i class=\"bi bi-three-dots-vertical\"></i>\n                    </button>\n                    <ul class=\"dropdown-menu dropdown-menu-end\">\n                      <li>\n                        <button class=\"dropdown-item\" (click)=\"editTask(task)\">\n                          <i class=\"bi bi-pencil me-2\"></i> Modifier\n                        </button>\n                      </li>\n                      <li>\n                        <button\n                          class=\"dropdown-item\"\n                          (click)=\"updateTaskStatus(task, 'todo')\"\n                        >\n                          <i class=\"bi bi-arrow-left me-2\"></i> Déplacer vers \"À\n                          faire\"\n                        </button>\n                      </li>\n                      <li>\n                        <button\n                          class=\"dropdown-item\"\n                          (click)=\"updateTaskStatus(task, 'in-progress')\"\n                        >\n                          <i class=\"bi bi-arrow-left me-2\"></i> Déplacer vers\n                          \"En cours\"\n                        </button>\n                      </li>\n                      <li><hr class=\"dropdown-divider\" /></li>\n                      <li>\n                        <button\n                          class=\"dropdown-item text-danger\"\n                          (click)=\"task._id && deleteTask(task._id)\"\n                        >\n                          <i class=\"bi bi-trash me-2\"></i> Supprimer\n                        </button>\n                      </li>\n                    </ul>\n                  </div>\n                </div>\n                <p class=\"small text-muted mb-2 task-description\">\n                  {{ task.description || \"Aucune description\" }}\n                </p>\n                <div class=\"d-flex justify-content-between align-items-center\">\n                  <span\n                    class=\"badge\"\n                    [ngClass]=\"{\n                      'bg-danger': task.priority === 'high',\n                      'bg-warning text-dark': task.priority === 'medium',\n                      'bg-info text-dark': task.priority === 'low'\n                    }\"\n                  >\n                    {{\n                      task.priority === \"high\"\n                        ? \"Haute\"\n                        : task.priority === \"medium\"\n                        ? \"Moyenne\"\n                        : \"Basse\"\n                    }}\n                  </span>\n                  <small class=\"text-muted\" *ngIf=\"task.assignedTo\">\n                    {{ getUserName(task.assignedTo) }}\n                  </small>\n                </div>\n                <!-- Poignée de glisser-déposer -->\n                <div class=\"task-drag-handle\" cdkDragHandle>\n                  <i class=\"bi bi-grip-horizontal\"></i>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAEEA,eAAe,EACfC,iBAAiB,QACZ,wBAAwB;AAS/B,SAASC,QAAQ,QAAQ,gBAAgB;;;;;;;;;;;;ICL7BC,EAAA,CAAAC,cAAA,aAAwD;IACtDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IADHH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,mBAAAC,MAAA,CAAAC,IAAA,CAAAC,IAAA,MACF;;;;;IAuBRR,EAAA,CAAAC,cAAA,cAA6D;IAGzBD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEpDH,EAAA,CAAAC,cAAA,cAA4D;IAC5BD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEpDH,EAAA,CAAAC,cAAA,cAA0D;IAC1BD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEpDH,EAAA,CAAAC,cAAA,aAA2B;IAAAD,EAAA,CAAAE,MAAA,qCAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;;IAK3DH,EAAA,CAAAC,cAAA,cAA2D;IAKrDD,EAAA,CAAAS,SAAA,YAAyD;IACzDT,EAAA,CAAAC,cAAA,cAAyB;IACvBD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,iBAGC;IADCD,EAAA,CAAAU,UAAA,mBAAAC,0DAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAF,MAAA,CAAAG,MAAA,IAAUH,MAAA,CAAAI,SAAA,CAAAJ,MAAA,CAAAG,MAAA,CAAiB;IAAA,EAAC;IAErCjB,EAAA,CAAAS,SAAA,YAA0C;IAACT,EAAA,CAAAE,MAAA,uBAC7C;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAPPH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAc,MAAA,CAAAC,KAAA,MACF;;;;;IAqFUpB,EAAA,CAAAC,cAAA,iBAGC;IACCD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAHPH,EAAA,CAAAqB,UAAA,UAAAC,QAAA,CAAAC,GAAA,IAAAD,QAAA,CAAAE,EAAA,CAA6B;IAE7BxB,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAoB,MAAA,CAAAC,WAAA,CAAAJ,QAAA,CAAAC,GAAA,IAAAD,QAAA,CAAAE,EAAA,aACF;;;;;;IA9EhBxB,EAAA,CAAAC,cAAA,aAA0D;IAKhDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEPH,EAAA,CAAAC,cAAA,cAA2B;IACnBD,EAAA,CAAAU,UAAA,sBAAAiB,2DAAA;MAAA3B,EAAA,CAAAY,aAAA,CAAAgB,IAAA;MAAA,MAAAC,OAAA,GAAA7B,EAAA,CAAAe,aAAA;MAAA,OAAYf,EAAA,CAAAgB,WAAA,CAAAa,OAAA,CAAAC,WAAA,GAAcD,OAAA,CAAAE,UAAA,EAAY,GAAGF,OAAA,CAAAG,UAAA,EAAY;IAAA,EAAC;IAC1DhC,EAAA,CAAAC,cAAA,cAAqB;IAEyBD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACxDH,EAAA,CAAAC,cAAA,iBAaE;IAPAD,EAAA,CAAAU,UAAA,2BAAAuB,kEAAAC,MAAA;MAAAlC,EAAA,CAAAY,aAAA,CAAAgB,IAAA;MAAA,MAAAO,OAAA,GAAAnC,EAAA,CAAAe,aAAA;MAAA,OAETf,EAAA,CAAAgB,WAAA,CAAAmB,OAAA,CAAAL,WAAA,GAAAK,OAAA,CAAAL,WAAA,CAAAM,KAAA,GAAAF,MAAA,GAAAC,OAAA,CAAAE,OAAA,CAAAD,KAAA,GAAAF,MAAA,CAGX;IAAA,EADqB;IAVHlC,EAAA,CAAAG,YAAA,EAaE;IAEJH,EAAA,CAAAC,cAAA,eAAsB;IACyBD,EAAA,CAAAE,MAAA,sBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC9DH,EAAA,CAAAC,cAAA,kBAQC;IAJCD,EAAA,CAAAU,UAAA,2BAAA4B,mEAAAJ,MAAA;MAAAlC,EAAA,CAAAY,aAAA,CAAAgB,IAAA;MAAA,MAAAW,OAAA,GAAAvC,EAAA,CAAAe,aAAA;MAAA,OACyBf,EAAA,CAAAgB,WAAA,CAAAuB,OAAA,CAAAT,WAAA,GAAAS,OAAA,CAAAT,WAAA,CAAAU,QAAA,GAAAD,OAAA,CAAAF,OAAA,CAAAG,QAAA,GAAAN,MAAA,CAEhB;IAAA,EADR;IAGDlC,EAAA,CAAAC,cAAA,kBAAoB;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAClCH,EAAA,CAAAC,cAAA,kBAAuB;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACvCH,EAAA,CAAAC,cAAA,kBAAqB;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAGvCH,EAAA,CAAAC,cAAA,eAAsB;IACuBD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC1DH,EAAA,CAAAC,cAAA,kBAQC;IAJCD,EAAA,CAAAU,UAAA,2BAAA+B,mEAAAP,MAAA;MAAAlC,EAAA,CAAAY,aAAA,CAAAgB,IAAA;MAAA,MAAAc,OAAA,GAAA1C,EAAA,CAAAe,aAAA;MAAA,OACyBf,EAAA,CAAAgB,WAAA,CAAA0B,OAAA,CAAAZ,WAAA,GAAAY,OAAA,CAAAZ,WAAA,CAAAa,MAAA,GAAAD,OAAA,CAAAL,OAAA,CAAAM,MAAA,GAAAT,MAAA,CAEhB;IAAA,EADR;IAGDlC,EAAA,CAAAC,cAAA,kBAAqB;IAAAD,EAAA,CAAAE,MAAA,oBAAO;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACrCH,EAAA,CAAAC,cAAA,kBAA4B;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC7CH,EAAA,CAAAC,cAAA,kBAAqB;IAAAD,EAAA,CAAAE,MAAA,qBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAG1CH,EAAA,CAAAC,cAAA,eAAsB;IAEjBD,EAAA,CAAAE,MAAA,4BAAU;IAAAF,EAAA,CAAAG,YAAA,EACZ;IACDH,EAAA,CAAAC,cAAA,kBAOC;IAJCD,EAAA,CAAAU,UAAA,2BAAAkC,mEAAAV,MAAA;MAAAlC,EAAA,CAAAY,aAAA,CAAAgB,IAAA;MAAA,MAAAiB,OAAA,GAAA7C,EAAA,CAAAe,aAAA;MAAA,OACyBf,EAAA,CAAAgB,WAAA,CAAA6B,OAAA,CAAAf,WAAA,GAAAe,OAAA,CAAAf,WAAA,CAAAgB,UAAA,GAAAD,OAAA,CAAAR,OAAA,CAAAS,UAAA,GAAAZ,MAAA,CAEhB;IAAA,EADR;IAGDlC,EAAA,CAAAC,cAAA,kBAAuB;IAAAD,EAAA,CAAAE,MAAA,yBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC5CH,EAAA,CAAA+C,UAAA,KAAAC,2CAAA,qBAKS;IACXhD,EAAA,CAAAG,YAAA,EAAS;IAEXH,EAAA,CAAAC,cAAA,eAAsB;IAEjBD,EAAA,CAAAE,MAAA,iCAAe;IAAAF,EAAA,CAAAG,YAAA,EACjB;IACDH,EAAA,CAAAC,cAAA,iBAQE;IAJAD,EAAA,CAAAU,UAAA,2BAAAuC,kEAAAf,MAAA;MAAAlC,EAAA,CAAAY,aAAA,CAAAgB,IAAA;MAAA,MAAAsB,OAAA,GAAAlD,EAAA,CAAAe,aAAA;MAAA,OACyBf,EAAA,CAAAgB,WAAA,CAAAkC,OAAA,CAAApB,WAAA,GAAAoB,OAAA,CAAApB,WAAA,CAAAqB,OAAA,GAAAD,OAAA,CAAAb,OAAA,CAAAc,OAAA,GAAAjB,MAAA,CAEhB;IAAA,EADR;IANHlC,EAAA,CAAAG,YAAA,EAQE;IAEJH,EAAA,CAAAC,cAAA,cAAoB;IAEfD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EACb;IACDH,EAAA,CAAAC,cAAA,oBAWC;IAPCD,EAAA,CAAAU,UAAA,2BAAA0C,qEAAAlB,MAAA;MAAAlC,EAAA,CAAAY,aAAA,CAAAgB,IAAA;MAAA,MAAAyB,OAAA,GAAArD,EAAA,CAAAe,aAAA;MAAA,OAETf,EAAA,CAAAgB,WAAA,CAAAqC,OAAA,CAAAvB,WAAA,GAAAuB,OAAA,CAAAvB,WAAA,CAAAwB,WAAA,GAAAD,OAAA,CAAAhB,OAAA,CAAAiB,WAAA,GAAApB,MAAA,CAGkB;IAAA,EADR;IAGFlC,EAAA,CAAAG,YAAA,EAAW;IAEdH,EAAA,CAAAC,cAAA,eAA0D;IAItDD,EAAA,CAAAU,UAAA,mBAAA6C,2DAAA;MAAAvD,EAAA,CAAAY,aAAA,CAAAgB,IAAA;MAAA,MAAA4B,OAAA,GAAAxD,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAwC,OAAA,CAAA1B,WAAA,GAAc0B,OAAA,CAAAC,UAAA,EAAY,GAAGD,OAAA,CAAAE,cAAA,EAAgB;IAAA,EAAC;IAEvD1D,EAAA,CAAAE,MAAA,iBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAGC;IACCD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAxHbH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAsD,MAAA,CAAA7B,WAAA,yDACF;IAYQ9B,EAAA,CAAAI,SAAA,GAA2D;IAA3DJ,EAAA,CAAAqB,UAAA,YAAAsC,MAAA,CAAA7B,WAAA,GAAA6B,MAAA,CAAA7B,WAAA,CAAAM,KAAA,GAAAuB,MAAA,CAAAtB,OAAA,CAAAD,KAAA,CAA2D;IAgB3DpC,EAAA,CAAAI,SAAA,GAEC;IAFDJ,EAAA,CAAAqB,UAAA,YAAAsC,MAAA,CAAA7B,WAAA,GAAA6B,MAAA,CAAA7B,WAAA,CAAAU,QAAA,GAAAmB,MAAA,CAAAtB,OAAA,CAAAG,QAAA,CAEC;IAcDxC,EAAA,CAAAI,SAAA,IAEC;IAFDJ,EAAA,CAAAqB,UAAA,YAAAsC,MAAA,CAAA7B,WAAA,GAAA6B,MAAA,CAAA7B,WAAA,CAAAa,MAAA,GAAAgB,MAAA,CAAAtB,OAAA,CAAAM,MAAA,CAEC;IAeD3C,EAAA,CAAAI,SAAA,IAEC;IAFDJ,EAAA,CAAAqB,UAAA,YAAAsC,MAAA,CAAA7B,WAAA,GAAA6B,MAAA,CAAA7B,WAAA,CAAAgB,UAAA,GAAAa,MAAA,CAAAtB,OAAA,CAAAS,UAAA,CAEC;IAGO9C,EAAA,CAAAI,SAAA,GAAc;IAAdJ,EAAA,CAAAqB,UAAA,eAAc;IAEHrB,EAAA,CAAAI,SAAA,GAAQ;IAARJ,EAAA,CAAAqB,UAAA,YAAAsC,MAAA,CAAAC,KAAA,CAAQ;IAe3B5D,EAAA,CAAAI,SAAA,GAEC;IAFDJ,EAAA,CAAAqB,UAAA,YAAAsC,MAAA,CAAA7B,WAAA,GAAA6B,MAAA,CAAA7B,WAAA,CAAAqB,OAAA,GAAAQ,MAAA,CAAAtB,OAAA,CAAAc,OAAA,CAEC;IAYDnD,EAAA,CAAAI,SAAA,GAIC;IAJDJ,EAAA,CAAAqB,UAAA,YAAAsC,MAAA,CAAA7B,WAAA,GAAA6B,MAAA,CAAA7B,WAAA,CAAAwB,WAAA,GAAAK,MAAA,CAAAtB,OAAA,CAAAiB,WAAA,CAIC;IAiBDtD,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAsD,MAAA,CAAA7B,WAAA,4CACF;;;;;;IAUd9B,EAAA,CAAAC,cAAA,cAA+C;IAQ/BD,EAAA,CAAAS,SAAA,YAA4B;IAC9BT,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,gBAKE;IADAD,EAAA,CAAAU,UAAA,2BAAAmD,iEAAA3B,MAAA;MAAAlC,EAAA,CAAAY,aAAA,CAAAkD,IAAA;MAAA,MAAAC,OAAA,GAAA/D,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAAA+C,OAAA,CAAAC,UAAA,GAAA9B,MAAA;IAAA,EAAwB;IAJ1BlC,EAAA,CAAAG,YAAA,EAKE;IAGNH,EAAA,CAAAC,cAAA,eAAsB;IACQD,EAAA,CAAAU,UAAA,2BAAAuD,mEAAA/B,MAAA;MAAAlC,EAAA,CAAAY,aAAA,CAAAkD,IAAA;MAAA,MAAAI,OAAA,GAAAlE,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAAAkD,OAAA,CAAAC,YAAA,GAAAjC,MAAA;IAAA,EAA0B;IACpDlC,EAAA,CAAAC,cAAA,kBAAoB;IAAAD,EAAA,CAAAE,MAAA,wBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC7CH,EAAA,CAAAC,cAAA,kBAAqB;IAAAD,EAAA,CAAAE,MAAA,oBAAO;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACrCH,EAAA,CAAAC,cAAA,kBAA4B;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC7CH,EAAA,CAAAC,cAAA,kBAAqB;IAAAD,EAAA,CAAAE,MAAA,sBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAG3CH,EAAA,CAAAC,cAAA,eAAsB;IACQD,EAAA,CAAAU,UAAA,2BAAA0D,mEAAAlC,MAAA;MAAAlC,EAAA,CAAAY,aAAA,CAAAkD,IAAA;MAAA,MAAAO,OAAA,GAAArE,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAAAqD,OAAA,CAAAC,cAAA,GAAApC,MAAA;IAAA,EAA4B;IACtDlC,EAAA,CAAAC,cAAA,kBAAoB;IAAAD,EAAA,CAAAE,MAAA,iCAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACjDH,EAAA,CAAAC,cAAA,kBAAqB;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACnCH,EAAA,CAAAC,cAAA,kBAAuB;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACvCH,EAAA,CAAAC,cAAA,kBAAoB;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAjBhCH,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAqB,UAAA,YAAAkD,MAAA,CAAAP,UAAA,CAAwB;IAKAhE,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAqB,UAAA,YAAAkD,MAAA,CAAAJ,YAAA,CAA0B;IAQ1BnE,EAAA,CAAAI,SAAA,IAA4B;IAA5BJ,EAAA,CAAAqB,UAAA,YAAAkD,MAAA,CAAAD,cAAA,CAA4B;;;;;;IAcpEtE,EAAA,CAAAC,cAAA,cAGC;IAGKD,EAAA,CAAAS,SAAA,YAAqD;IACrDT,EAAA,CAAAC,cAAA,aAAiB;IAAAD,EAAA,CAAAE,MAAA,qCAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1CH,EAAA,CAAAC,cAAA,YAA2B;IACzBD,EAAA,CAAAE,MAAA,iFACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAC,cAAA,iBAGC;IADCD,EAAA,CAAAU,UAAA,mBAAA8D,0DAAA;MAAAxE,EAAA,CAAAY,aAAA,CAAA6D,IAAA;MAAA,MAAAC,OAAA,GAAA1E,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAA0D,OAAA,CAAAhB,cAAA,EAAgB;IAAA,EAAC;IAE1B1D,EAAA,CAAAS,SAAA,YAAsC;IAACT,EAAA,CAAAE,MAAA,mCACzC;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAuGDH,EAAA,CAAAC,cAAA,iBAAkD;IAChDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IADNH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAsE,OAAA,CAAAjD,WAAA,CAAAkD,QAAA,CAAA9B,UAAA,OACF;;;;;;;;;;;;;IA5EJ9C,EAAA,CAAAC,cAAA,cAKC;IAIkCD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpDH,EAAA,CAAAC,cAAA,cAAsB;IAMlBD,EAAA,CAAAS,SAAA,YAAyC;IAC3CT,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,cAA4C;IAEVD,EAAA,CAAAU,UAAA,mBAAAmE,iEAAA;MAAA,MAAAC,WAAA,GAAA9E,EAAA,CAAAY,aAAA,CAAAmE,IAAA;MAAA,MAAAH,QAAA,GAAAE,WAAA,CAAAE,SAAA;MAAA,MAAAC,OAAA,GAAAjF,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAiE,OAAA,CAAAC,QAAA,CAAAN,QAAA,CAAc;IAAA,EAAC;IACpD5E,EAAA,CAAAS,SAAA,cAAiC;IAACT,EAAA,CAAAE,MAAA,kBACpC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAEXH,EAAA,CAAAC,cAAA,UAAI;IAGAD,EAAA,CAAAU,UAAA,mBAAAyE,kEAAA;MAAA,MAAAL,WAAA,GAAA9E,EAAA,CAAAY,aAAA,CAAAmE,IAAA;MAAA,MAAAH,QAAA,GAAAE,WAAA,CAAAE,SAAA;MAAA,MAAAI,OAAA,GAAApF,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAoE,OAAA,CAAAC,gBAAA,CAAAT,QAAA,EAAuB,aAAa,CAAC;IAAA,EAAC;IAE/C5E,EAAA,CAAAS,SAAA,cAAsC;IAACT,EAAA,CAAAE,MAAA,yCAEzC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAEXH,EAAA,CAAAC,cAAA,UAAI;IAGAD,EAAA,CAAAU,UAAA,mBAAA4E,kEAAA;MAAA,MAAAR,WAAA,GAAA9E,EAAA,CAAAY,aAAA,CAAAmE,IAAA;MAAA,MAAAH,QAAA,GAAAE,WAAA,CAAAE,SAAA;MAAA,MAAAO,OAAA,GAAAvF,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAuE,OAAA,CAAAF,gBAAA,CAAAT,QAAA,EAAuB,MAAM,CAAC;IAAA,EAAC;IAExC5E,EAAA,CAAAS,SAAA,aAAqC;IAACT,EAAA,CAAAE,MAAA,qCAExC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAEXH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAS,SAAA,eAA+B;IAAAT,EAAA,CAAAG,YAAA,EAAK;IACxCH,EAAA,CAAAC,cAAA,UAAI;IAGAD,EAAA,CAAAU,UAAA,mBAAA8E,kEAAA;MAAA,MAAAV,WAAA,GAAA9E,EAAA,CAAAY,aAAA,CAAAmE,IAAA;MAAA,MAAAH,QAAA,GAAAE,WAAA,CAAAE,SAAA;MAAA,MAAAS,OAAA,GAAAzF,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAA4D,QAAA,CAAArD,GAAA,IAAYkE,OAAA,CAAAC,UAAA,CAAAd,QAAA,CAAArD,GAAA,CAAoB;IAAA,EAAC;IAE1CvB,EAAA,CAAAS,SAAA,cAAgC;IAACT,EAAA,CAAAE,MAAA,mBACnC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAKjBH,EAAA,CAAAC,cAAA,cAAkD;IAChDD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAC,cAAA,gBAA+D;IAS3DD,EAAA,CAAAE,MAAA,IAOF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAA+C,UAAA,KAAA4C,iDAAA,qBAEQ;IACV3F,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,gBAA4C;IAC1CD,EAAA,CAAAS,SAAA,cAAqC;IACvCT,EAAA,CAAAG,YAAA,EAAM;;;;IA9ENH,EAAA,CAAAqB,UAAA,0BAAAuD,QAAA,CAAApC,QAAA,CAAuC;IAMNxC,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAA4F,iBAAA,CAAAhB,QAAA,CAAAxC,KAAA,CAAgB;IA8C/CpC,EAAA,CAAAI,SAAA,IACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAuE,QAAA,CAAAtB,WAAA,8BACF;IAIItD,EAAA,CAAAI,SAAA,GAIE;IAJFJ,EAAA,CAAAqB,UAAA,YAAArB,EAAA,CAAA6F,eAAA,IAAAC,GAAA,EAAAlB,QAAA,CAAApC,QAAA,aAAAoC,QAAA,CAAApC,QAAA,eAAAoC,QAAA,CAAApC,QAAA,YAIE;IAEFxC,EAAA,CAAAI,SAAA,GAOF;IAPEJ,EAAA,CAAAK,kBAAA,MAAAuE,QAAA,CAAApC,QAAA,wBAAAoC,QAAA,CAAApC,QAAA,yCAOF;IAC2BxC,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAqB,UAAA,SAAAuD,QAAA,CAAA9B,UAAA,CAAqB;;;;;IA8GhD9C,EAAA,CAAAC,cAAA,iBAAkD;IAChDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IADNH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAA0F,OAAA,CAAArE,WAAA,CAAAsE,QAAA,CAAAlD,UAAA,OACF;;;;;;IA5EJ9C,EAAA,CAAAC,cAAA,cAKC;IAIkCD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpDH,EAAA,CAAAC,cAAA,cAAsB;IAMlBD,EAAA,CAAAS,SAAA,YAAyC;IAC3CT,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,cAA4C;IAEVD,EAAA,CAAAU,UAAA,mBAAAuF,iEAAA;MAAA,MAAAnB,WAAA,GAAA9E,EAAA,CAAAY,aAAA,CAAAsF,IAAA;MAAA,MAAAF,QAAA,GAAAlB,WAAA,CAAAE,SAAA;MAAA,MAAAmB,OAAA,GAAAnG,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAmF,OAAA,CAAAjB,QAAA,CAAAc,QAAA,CAAc;IAAA,EAAC;IACpDhG,EAAA,CAAAS,SAAA,cAAiC;IAACT,EAAA,CAAAE,MAAA,kBACpC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAEXH,EAAA,CAAAC,cAAA,UAAI;IAGAD,EAAA,CAAAU,UAAA,mBAAA0F,kEAAA;MAAA,MAAAtB,WAAA,GAAA9E,EAAA,CAAAY,aAAA,CAAAsF,IAAA;MAAA,MAAAF,QAAA,GAAAlB,WAAA,CAAAE,SAAA;MAAA,MAAAqB,OAAA,GAAArG,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAqF,OAAA,CAAAhB,gBAAA,CAAAW,QAAA,EAAuB,MAAM,CAAC;IAAA,EAAC;IAExChG,EAAA,CAAAS,SAAA,YAAqC;IAACT,EAAA,CAAAE,MAAA,6CAExC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAEXH,EAAA,CAAAC,cAAA,UAAI;IAGAD,EAAA,CAAAU,UAAA,mBAAA4F,kEAAA;MAAA,MAAAxB,WAAA,GAAA9E,EAAA,CAAAY,aAAA,CAAAsF,IAAA;MAAA,MAAAF,QAAA,GAAAlB,WAAA,CAAAE,SAAA;MAAA,MAAAuB,OAAA,GAAAvG,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAuF,OAAA,CAAAlB,gBAAA,CAAAW,QAAA,EAAuB,MAAM,CAAC;IAAA,EAAC;IAExChG,EAAA,CAAAS,SAAA,aAAqC;IAACT,EAAA,CAAAE,MAAA,qCAExC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAEXH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAS,SAAA,eAA+B;IAAAT,EAAA,CAAAG,YAAA,EAAK;IACxCH,EAAA,CAAAC,cAAA,UAAI;IAGAD,EAAA,CAAAU,UAAA,mBAAA8F,kEAAA;MAAA,MAAA1B,WAAA,GAAA9E,EAAA,CAAAY,aAAA,CAAAsF,IAAA;MAAA,MAAAF,QAAA,GAAAlB,WAAA,CAAAE,SAAA;MAAA,MAAAyB,OAAA,GAAAzG,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAgF,QAAA,CAAAzE,GAAA,IAAYkF,OAAA,CAAAf,UAAA,CAAAM,QAAA,CAAAzE,GAAA,CAAoB;IAAA,EAAC;IAE1CvB,EAAA,CAAAS,SAAA,cAAgC;IAACT,EAAA,CAAAE,MAAA,mBACnC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAKjBH,EAAA,CAAAC,cAAA,cAAkD;IAChDD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAC,cAAA,gBAA+D;IAS3DD,EAAA,CAAAE,MAAA,IAOF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAA+C,UAAA,KAAA2D,iDAAA,qBAEQ;IACV1G,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,gBAA4C;IAC1CD,EAAA,CAAAS,SAAA,cAAqC;IACvCT,EAAA,CAAAG,YAAA,EAAM;;;;IA9ENH,EAAA,CAAAqB,UAAA,0BAAA2E,QAAA,CAAAxD,QAAA,CAAuC;IAMNxC,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAA4F,iBAAA,CAAAI,QAAA,CAAA5D,KAAA,CAAgB;IA8C/CpC,EAAA,CAAAI,SAAA,IACF;IADEJ,EAAA,CAAAK,kBAAA,MAAA2F,QAAA,CAAA1C,WAAA,8BACF;IAIItD,EAAA,CAAAI,SAAA,GAIE;IAJFJ,EAAA,CAAAqB,UAAA,YAAArB,EAAA,CAAA6F,eAAA,IAAAC,GAAA,EAAAE,QAAA,CAAAxD,QAAA,aAAAwD,QAAA,CAAAxD,QAAA,eAAAwD,QAAA,CAAAxD,QAAA,YAIE;IAEFxC,EAAA,CAAAI,SAAA,GAOF;IAPEJ,EAAA,CAAAK,kBAAA,MAAA2F,QAAA,CAAAxD,QAAA,wBAAAwD,QAAA,CAAAxD,QAAA,yCAOF;IAC2BxC,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAqB,UAAA,SAAA2E,QAAA,CAAAlD,UAAA,CAAqB;;;;;IA8GhD9C,EAAA,CAAAC,cAAA,iBAAkD;IAChDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IADNH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAsG,OAAA,CAAAjF,WAAA,CAAAkF,QAAA,CAAA9D,UAAA,OACF;;;;;;IA5EJ9C,EAAA,CAAAC,cAAA,eAKC;IAIkCD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpDH,EAAA,CAAAC,cAAA,cAAsB;IAMlBD,EAAA,CAAAS,SAAA,YAAyC;IAC3CT,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,cAA4C;IAEVD,EAAA,CAAAU,UAAA,mBAAAmG,iEAAA;MAAA,MAAA/B,WAAA,GAAA9E,EAAA,CAAAY,aAAA,CAAAkG,IAAA;MAAA,MAAAF,QAAA,GAAA9B,WAAA,CAAAE,SAAA;MAAA,MAAA+B,OAAA,GAAA/G,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAA+F,OAAA,CAAA7B,QAAA,CAAA0B,QAAA,CAAc;IAAA,EAAC;IACpD5G,EAAA,CAAAS,SAAA,cAAiC;IAACT,EAAA,CAAAE,MAAA,kBACpC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAEXH,EAAA,CAAAC,cAAA,UAAI;IAGAD,EAAA,CAAAU,UAAA,mBAAAsG,kEAAA;MAAA,MAAAlC,WAAA,GAAA9E,EAAA,CAAAY,aAAA,CAAAkG,IAAA;MAAA,MAAAF,QAAA,GAAA9B,WAAA,CAAAE,SAAA;MAAA,MAAAiC,OAAA,GAAAjH,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAiG,OAAA,CAAA5B,gBAAA,CAAAuB,QAAA,EAAuB,MAAM,CAAC;IAAA,EAAC;IAExC5G,EAAA,CAAAS,SAAA,YAAqC;IAACT,EAAA,CAAAE,MAAA,6CAExC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAEXH,EAAA,CAAAC,cAAA,UAAI;IAGAD,EAAA,CAAAU,UAAA,mBAAAwG,kEAAA;MAAA,MAAApC,WAAA,GAAA9E,EAAA,CAAAY,aAAA,CAAAkG,IAAA;MAAA,MAAAF,QAAA,GAAA9B,WAAA,CAAAE,SAAA;MAAA,MAAAmC,OAAA,GAAAnH,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAmG,OAAA,CAAA9B,gBAAA,CAAAuB,QAAA,EAAuB,aAAa,CAAC;IAAA,EAAC;IAE/C5G,EAAA,CAAAS,SAAA,YAAqC;IAACT,EAAA,CAAAE,MAAA,yCAExC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAEXH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAS,SAAA,eAA+B;IAAAT,EAAA,CAAAG,YAAA,EAAK;IACxCH,EAAA,CAAAC,cAAA,UAAI;IAGAD,EAAA,CAAAU,UAAA,mBAAA0G,kEAAA;MAAA,MAAAtC,WAAA,GAAA9E,EAAA,CAAAY,aAAA,CAAAkG,IAAA;MAAA,MAAAF,QAAA,GAAA9B,WAAA,CAAAE,SAAA;MAAA,MAAAqC,OAAA,GAAArH,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAA4F,QAAA,CAAArF,GAAA,IAAY8F,OAAA,CAAA3B,UAAA,CAAAkB,QAAA,CAAArF,GAAA,CAAoB;IAAA,EAAC;IAE1CvB,EAAA,CAAAS,SAAA,cAAgC;IAACT,EAAA,CAAAE,MAAA,mBACnC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAKjBH,EAAA,CAAAC,cAAA,cAAkD;IAChDD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAC,cAAA,gBAA+D;IAS3DD,EAAA,CAAAE,MAAA,IAOF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAA+C,UAAA,KAAAuE,iDAAA,qBAEQ;IACVtH,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,gBAA4C;IAC1CD,EAAA,CAAAS,SAAA,cAAqC;IACvCT,EAAA,CAAAG,YAAA,EAAM;;;;IA9ENH,EAAA,CAAAqB,UAAA,0BAAAuF,QAAA,CAAApE,QAAA,CAAuC;IAMNxC,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAA4F,iBAAA,CAAAgB,QAAA,CAAAxE,KAAA,CAAgB;IA8C/CpC,EAAA,CAAAI,SAAA,IACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAuG,QAAA,CAAAtD,WAAA,8BACF;IAIItD,EAAA,CAAAI,SAAA,GAIE;IAJFJ,EAAA,CAAAqB,UAAA,YAAArB,EAAA,CAAA6F,eAAA,IAAAC,GAAA,EAAAc,QAAA,CAAApE,QAAA,aAAAoE,QAAA,CAAApE,QAAA,eAAAoE,QAAA,CAAApE,QAAA,YAIE;IAEFxC,EAAA,CAAAI,SAAA,GAOF;IAPEJ,EAAA,CAAAK,kBAAA,MAAAuG,QAAA,CAAApE,QAAA,wBAAAoE,QAAA,CAAApE,QAAA,yCAOF;IAC2BxC,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAqB,UAAA,SAAAuF,QAAA,CAAA9D,UAAA,CAAqB;;;;;;;;;IA7T9D9C,EAAA,CAAAC,cAAA,cAA8C;IAMpCD,EAAA,CAAAS,SAAA,YAAoC;IACpCT,EAAA,CAAAE,MAAA,qBACA;IAAAF,EAAA,CAAAC,cAAA,eAA4D;IAC1DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAGXH,EAAA,CAAAC,cAAA,cAA2B;IAQvBD,EAAA,CAAAU,UAAA,gCAAA6G,qEAAArF,MAAA;MAAAlC,EAAA,CAAAY,aAAA,CAAA4G,IAAA;MAAA,MAAAC,OAAA,GAAAzH,EAAA,CAAAe,aAAA;MAAA,OAAsBf,EAAA,CAAAgB,WAAA,CAAAyG,OAAA,CAAAC,IAAA,CAAAxF,MAAA,CAAY;IAAA,EAAC;IAEnClC,EAAA,CAAA+C,UAAA,KAAA4E,wCAAA,oBAkFM;IACR3H,EAAA,CAAAG,YAAA,EAAM;IAMZH,EAAA,CAAAC,cAAA,eAAsB;IAIdD,EAAA,CAAAS,SAAA,aAA0C;IAC1CT,EAAA,CAAAE,MAAA,kBACA;IAAAF,EAAA,CAAAC,cAAA,gBAA4D;IAC1DD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAGXH,EAAA,CAAAC,cAAA,eAA2B;IAQvBD,EAAA,CAAAU,UAAA,gCAAAkH,qEAAA1F,MAAA;MAAAlC,EAAA,CAAAY,aAAA,CAAA4G,IAAA;MAAA,MAAAK,OAAA,GAAA7H,EAAA,CAAAe,aAAA;MAAA,OAAsBf,EAAA,CAAAgB,WAAA,CAAA6G,OAAA,CAAAH,IAAA,CAAAxF,MAAA,CAAY;IAAA,EAAC;IAEnClC,EAAA,CAAA+C,UAAA,KAAA+E,wCAAA,oBAkFM;IACR9H,EAAA,CAAAG,YAAA,EAAM;IAMZH,EAAA,CAAAC,cAAA,eAAsB;IAIdD,EAAA,CAAAS,SAAA,aAAqC;IACrCT,EAAA,CAAAE,MAAA,wBACA;IAAAF,EAAA,CAAAC,cAAA,gBAA4D;IAC1DD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAGXH,EAAA,CAAAC,cAAA,eAA2B;IAQvBD,EAAA,CAAAU,UAAA,gCAAAqH,qEAAA7F,MAAA;MAAAlC,EAAA,CAAAY,aAAA,CAAA4G,IAAA;MAAA,MAAAQ,OAAA,GAAAhI,EAAA,CAAAe,aAAA;MAAA,OAAsBf,EAAA,CAAAgB,WAAA,CAAAgH,OAAA,CAAAN,IAAA,CAAAxF,MAAA,CAAY;IAAA,EAAC;IAEnClC,EAAA,CAAA+C,UAAA,KAAAkF,wCAAA,oBAkFM;IACRjI,EAAA,CAAAG,YAAA,EAAM;;;;;;;IA7TFH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAA6H,MAAA,CAAAC,iBAAA,QACF;IAQAnI,EAAA,CAAAI,SAAA,GAAkC;IAAlCJ,EAAA,CAAAqB,UAAA,oBAAA6G,MAAA,CAAAE,YAAA,GAAkC,2BAAApI,EAAA,CAAAqI,eAAA,KAAAC,GAAA,EAAAC,IAAA,EAAAC,IAAA;IAMfxI,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAqB,UAAA,YAAA6G,MAAA,CAAAE,YAAA,GAAiB;IA+FlCpI,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAA6H,MAAA,CAAAO,uBAAA,QACF;IAQAzI,EAAA,CAAAI,SAAA,GAAwC;IAAxCJ,EAAA,CAAAqB,UAAA,oBAAA6G,MAAA,CAAAQ,kBAAA,GAAwC,2BAAA1I,EAAA,CAAAqI,eAAA,KAAAC,GAAA,EAAAK,IAAA,EAAAH,IAAA;IAMrBxI,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAqB,UAAA,YAAA6G,MAAA,CAAAQ,kBAAA,GAAuB;IA+FxC1I,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAA6H,MAAA,CAAAU,iBAAA,QACF;IAQA5I,EAAA,CAAAI,SAAA,GAAkC;IAAlCJ,EAAA,CAAAqB,UAAA,oBAAA6G,MAAA,CAAAW,YAAA,GAAkC,2BAAA7I,EAAA,CAAAqI,eAAA,KAAAC,GAAA,EAAAK,IAAA,EAAAJ,IAAA;IAMfvI,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAqB,UAAA,YAAA6G,MAAA,CAAAW,YAAA,GAAiB;;;AD3elD,OAAM,MAAOC,iBAAiB;EAgB5BC,YACUC,WAAwB,EACxBC,aAA4B,EAC5BC,WAA4B,EAC5BC,KAAqB,EACrBC,MAAc,EACdC,mBAAwC;IALxC,KAAAL,WAAW,GAAXA,WAAW;IACX,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,mBAAmB,GAAnBA,mBAAmB;IArB7B,KAAAC,KAAK,GAAW,EAAE;IAClB,KAAArI,MAAM,GAAkB,IAAI;IAC5B,KAAAV,IAAI,GAAkB,IAAI;IAC1B,KAAAgJ,OAAO,GAAG,KAAK;IACf,KAAAnI,KAAK,GAAkB,IAAI;IAC3B,KAAAwC,KAAK,GAAW,EAAE;IAElB,KAAA9B,WAAW,GAAgB,IAAI;IAC/B,KAAA0H,YAAY,GAAG,KAAK;IAEpB;IACA,KAAArF,YAAY,GAAW,KAAK;IAC5B,KAAAG,cAAc,GAAW,KAAK;IAC9B,KAAAN,UAAU,GAAW,EAAE;EASpB;EAEHyF,QAAQA,CAAA;IACN;IACA,IAAI,CAACpH,OAAO,GAAG,IAAI,CAACqH,iBAAiB,EAAE;IAEvC,IAAI,CAACP,KAAK,CAACQ,QAAQ,CAACC,SAAS,CAAEC,MAAM,IAAI;MACvC,IAAI,CAAC5I,MAAM,GAAG4I,MAAM,CAACC,GAAG,CAAC,IAAI,CAAC;MAC9B,IAAI,IAAI,CAAC7I,MAAM,EAAE;QACf,IAAI,CAAC8I,eAAe,CAAC,IAAI,CAAC9I,MAAM,CAAC;QACjC,IAAI,CAACC,SAAS,CAAC,IAAI,CAACD,MAAM,CAAC;QAC3B,IAAI,CAAC+I,SAAS,EAAE;OACjB,MAAM;QACL,IAAI,CAAC5I,KAAK,GAAG,sBAAsB;QACnC,IAAI,CAACiI,mBAAmB,CAACY,SAAS,CAAC,sBAAsB,CAAC;;IAE9D,CAAC,CAAC;EACJ;EAEAF,eAAeA,CAAC9I,MAAc;IAC5B,IAAI,CAACsI,OAAO,GAAG,IAAI;IAEnB;IACA,MAAMW,WAAW,GAAG,KAAK,CAAC,CAAC;IAE3B,IAAIA,WAAW,EAAE;MACf;MACA,MAAMC,QAAQ,GAAW;QACvB5I,GAAG,EAAEN,MAAM;QACXT,IAAI,EAAE,SAAS,GAAGS,MAAM;QACxBqC,WAAW,EAAE,0BAA0B,GAAGrC,MAAM;QAChDmJ,KAAK,EAAE,UAAU;QACjBC,OAAO,EAAE;OACV;MAEDC,UAAU,CAAC,MAAK;QACd,IAAI,CAAC/J,IAAI,GAAG4J,QAAQ;QACpB,IAAI,CAACZ,OAAO,GAAG,KAAK;QACpBgB,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE,IAAI,CAACjK,IAAI,CAAC;MAC/D,CAAC,EAAE,GAAG,CAAC;KACR,MAAM;MACL;MACA,IAAI,CAAC0I,aAAa,CACfwB,SAAS,CAACxJ,MAAM,CAAC,CACjByJ,IAAI,CAAC3K,QAAQ,CAAC,MAAO,IAAI,CAACwJ,OAAO,GAAG,KAAM,CAAC,CAAC,CAC5CK,SAAS,CAAC;QACTe,IAAI,EAAGC,IAAI,IAAI;UACb,IAAI,CAACrK,IAAI,GAAGqK,IAAI;UAChBL,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAE,IAAI,CAACjK,IAAI,CAAC;QACrE,CAAC;QACDa,KAAK,EAAGA,KAAK,IAAI;UACfmJ,OAAO,CAACnJ,KAAK,CACX,oDAAoD,EACpDA,KAAK,CACN;UACD,IAAI,CAACA,KAAK,GAAG,+CAA+C;UAC5D,IAAI,CAACiI,mBAAmB,CAACY,SAAS,CAChC,mDAAmD,CACpD;UAED;UACA,MAAME,QAAQ,GAAW;YACvB5I,GAAG,EAAEN,MAAM;YACXT,IAAI,EAAE,SAAS,GAAGS,MAAM,GAAG,aAAa;YACxCqC,WAAW,EAAE,0BAA0B,GAAGrC,MAAM;YAChDmJ,KAAK,EAAE,UAAU;YACjBC,OAAO,EAAE;WACV;UAED,IAAI,CAAC9J,IAAI,GAAG4J,QAAQ;QACtB;OACD,CAAC;;EAER;EAEAjJ,SAASA,CAACD,MAAc;IACtB,IAAI,CAACsI,OAAO,GAAG,IAAI;IAEnB;IACA,MAAMW,WAAW,GAAG,KAAK,CAAC,CAAC;IAE3B,IAAIA,WAAW,EAAE;MACf;MACA,MAAMW,SAAS,GAAW,CACxB;QACEtJ,GAAG,EAAE,GAAG;QACRa,KAAK,EAAE,SAAS;QAChBkB,WAAW,EAAE,2BAA2B;QACxCX,MAAM,EAAE,MAAM;QACdH,QAAQ,EAAE,MAAM;QAChBvB,MAAM,EAAEA;OACT,EACD;QACEM,GAAG,EAAE,GAAG;QACRa,KAAK,EAAE,SAAS;QAChBkB,WAAW,EAAE,2BAA2B;QACxCX,MAAM,EAAE,MAAM;QACdH,QAAQ,EAAE,QAAQ;QAClBvB,MAAM,EAAEA;OACT,EACD;QACEM,GAAG,EAAE,GAAG;QACRa,KAAK,EAAE,SAAS;QAChBkB,WAAW,EAAE,2BAA2B;QACxCX,MAAM,EAAE,aAAa;QACrBH,QAAQ,EAAE,MAAM;QAChBvB,MAAM,EAAEA;OACT,EACD;QACEM,GAAG,EAAE,GAAG;QACRa,KAAK,EAAE,SAAS;QAChBkB,WAAW,EAAE,2BAA2B;QACxCX,MAAM,EAAE,MAAM;QACdH,QAAQ,EAAE,KAAK;QACfvB,MAAM,EAAEA;OACT,CACF;MAEDqJ,UAAU,CAAC,MAAK;QACd,IAAI,CAAChB,KAAK,GAAGuB,SAAS;QACtB,IAAI,CAACC,SAAS,EAAE;QAChB,IAAI,CAACvB,OAAO,GAAG,KAAK;QACpBgB,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE,IAAI,CAAClB,KAAK,CAAC;MACpD,CAAC,EAAE,GAAG,CAAC;KACR,MAAM;MACL;MACA,IAAI,CAACN,WAAW,CACb+B,cAAc,CAAC9J,MAAM,CAAC,CACtByJ,IAAI,CAAC3K,QAAQ,CAAC,MAAO,IAAI,CAACwJ,OAAO,GAAG,KAAM,CAAC,CAAC,CAC5CK,SAAS,CAAC;QACTe,IAAI,EAAGC,IAAY,IAAI;UACrB,IAAI,CAACtB,KAAK,GAAGsB,IAAI;UACjB,IAAI,CAACE,SAAS,EAAE;UAChBP,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE,IAAI,CAAClB,KAAK,CAAC;QAC1D,CAAC;QACDlI,KAAK,EAAGA,KAAU,IAAI;UACpBmJ,OAAO,CAACnJ,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;UAC7D,IAAI,CAACA,KAAK,GAAG,kCAAkC;UAC/C,IAAI,CAACiI,mBAAmB,CAACY,SAAS,CAChC,sCAAsC,CACvC;UAED;UACA,MAAMY,SAAS,GAAW,CACxB;YACEtJ,GAAG,EAAE,GAAG;YACRa,KAAK,EAAE,oBAAoB;YAC3BkB,WAAW,EAAE,2BAA2B;YACxCX,MAAM,EAAE,MAAM;YACdH,QAAQ,EAAE,MAAM;YAChBvB,MAAM,EAAEA;WACT,EACD;YACEM,GAAG,EAAE,GAAG;YACRa,KAAK,EAAE,oBAAoB;YAC3BkB,WAAW,EAAE,2BAA2B;YACxCX,MAAM,EAAE,MAAM;YACdH,QAAQ,EAAE,QAAQ;YAClBvB,MAAM,EAAEA;WACT,CACF;UAED,IAAI,CAACqI,KAAK,GAAGuB,SAAS;UACtB,IAAI,CAACC,SAAS,EAAE;UAChBP,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE,IAAI,CAAClB,KAAK,CAAC;QACxD;OACD,CAAC;;EAER;EAEA;EACA5B,IAAIA,CAACsD,KAA0B;IAC7B,IAAIA,KAAK,CAACC,iBAAiB,KAAKD,KAAK,CAACE,SAAS,EAAE;MAC/C;MACArL,eAAe,CACbmL,KAAK,CAACE,SAAS,CAACN,IAAI,EACpBI,KAAK,CAACG,aAAa,EACnBH,KAAK,CAACI,YAAY,CACnB;KACF,MAAM;MACL;MACAtL,iBAAiB,CACfkL,KAAK,CAACC,iBAAiB,CAACL,IAAI,EAC5BI,KAAK,CAACE,SAAS,CAACN,IAAI,EACpBI,KAAK,CAACG,aAAa,EACnBH,KAAK,CAACI,YAAY,CACnB;MAED;MACA,MAAMC,IAAI,GAAGL,KAAK,CAACE,SAAS,CAACN,IAAI,CAACI,KAAK,CAACI,YAAY,CAAC;MACrD,IAAIE,SAA0C;MAE9C,IAAIN,KAAK,CAACE,SAAS,CAAC1J,EAAE,KAAK,WAAW,EAAE;QACtC8J,SAAS,GAAG,MAAM;OACnB,MAAM,IAAIN,KAAK,CAACE,SAAS,CAAC1J,EAAE,KAAK,kBAAkB,EAAE;QACpD8J,SAAS,GAAG,aAAa;OAC1B,MAAM;QACLA,SAAS,GAAG,MAAM;;MAGpB,IAAID,IAAI,CAAC9J,GAAG,IAAI8J,IAAI,CAAC1I,MAAM,KAAK2I,SAAS,EAAE;QACzCD,IAAI,CAAC1I,MAAM,GAAG2I,SAAS;QACvB,IAAI,CAACjG,gBAAgB,CAACgG,IAAI,EAAEC,SAAS,CAAC;;;EAG5C;EAEAtB,SAASA,CAAA;IACP;IACA,MAAME,WAAW,GAAG,KAAK,CAAC,CAAC;IAE3B,IAAIA,WAAW,EAAE;MACf;MACA,MAAMqB,SAAS,GAAW,CACxB;QACEhK,GAAG,EAAE,OAAO;QACZiK,QAAQ,EAAE,UAAU;QACpBC,KAAK,EAAE,kBAAkB;QACzBC,IAAI,EAAE,OAAO;QACbC,QAAQ,EAAE;OACX,EACD;QACEpK,GAAG,EAAE,OAAO;QACZiK,QAAQ,EAAE,YAAY;QACtBC,KAAK,EAAE,kBAAkB;QACzBC,IAAI,EAAE,SAAS;QACfC,QAAQ,EAAE;OACX,CACF;MAEDrB,UAAU,CAAC,MAAK;QACd,IAAI,CAAC1G,KAAK,GAAG2H,SAAS;QACtBhB,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAAC5G,KAAK,CAAC;MACzD,CAAC,EAAE,GAAG,CAAC;KACR,MAAM;MACL;MACA;MACA,MAAM2H,SAAS,GAAW,CACxB;QACEhK,GAAG,EAAE,OAAO;QACZiK,QAAQ,EAAE,UAAU;QACpBC,KAAK,EAAE,kBAAkB;QACzBC,IAAI,EAAE,OAAO;QACbC,QAAQ,EAAE;OACX,EACD;QACEpK,GAAG,EAAE,OAAO;QACZiK,QAAQ,EAAE,YAAY;QACtBC,KAAK,EAAE,kBAAkB;QACzBC,IAAI,EAAE,SAAS;QACfC,QAAQ,EAAE;OACX,CACF;MAED,IAAI,CAAC/H,KAAK,GAAG2H,SAAS;MACtBhB,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE,IAAI,CAAC5G,KAAK,CAAC;;EAE/D;EAEAlC,WAAWA,CAACkK,MAAc;IACxB,MAAMC,IAAI,GAAG,IAAI,CAACjI,KAAK,CAACkI,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACxK,GAAG,KAAKqK,MAAM,IAAIG,CAAC,CAACvK,EAAE,KAAKoK,MAAM,CAAC;IACxE,IAAIC,IAAI,EAAE;MACR,IAAIA,IAAI,CAACG,SAAS,IAAIH,IAAI,CAACI,QAAQ,EAAE;QACnC,OAAO,GAAGJ,IAAI,CAACG,SAAS,IAAIH,IAAI,CAACI,QAAQ,EAAE;OAC5C,MAAM,IAAIJ,IAAI,CAACrL,IAAI,EAAE;QACpB,OAAOqL,IAAI,CAACrL,IAAI;;;IAGpB,OAAO,qBAAqB;EAC9B;EAEAwB,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAACf,MAAM,EAAE;MAChB,IAAI,CAACoI,mBAAmB,CAACY,SAAS,CAAC,sBAAsB,CAAC;MAC1D;;IAGF,IAAI,CAAC5H,OAAO,CAACpB,MAAM,GAAG,IAAI,CAACA,MAAM;IAEjC,IAAI,CAACsI,OAAO,GAAG,IAAI;IACnB,IAAI,CAACP,WAAW,CACbhH,UAAU,CAAC,IAAI,CAACK,OAAO,CAAC,CACxBqI,IAAI,CAAC3K,QAAQ,CAAC,MAAO,IAAI,CAACwJ,OAAO,GAAG,KAAM,CAAC,CAAC,CAC5CK,SAAS,CAAC;MACTe,IAAI,EAAGC,IAAU,IAAI;QACnB,IAAI,CAACtB,KAAK,CAAC4C,IAAI,CAACtB,IAAI,CAAC;QACrB,IAAI,CAACE,SAAS,EAAE;QAChB,IAAI,CAACzI,OAAO,GAAG,IAAI,CAACqH,iBAAiB,EAAE;QACvC,IAAI,CAACF,YAAY,GAAG,KAAK;QACzB,IAAI,CAACH,mBAAmB,CAAC8C,WAAW,CAAC,yBAAyB,CAAC;MACjE,CAAC;MACD/K,KAAK,EAAGA,KAAU,IAAI;QACpBmJ,OAAO,CAACnJ,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;QAC/D,IAAI,CAACiI,mBAAmB,CAACY,SAAS,CAChC,wCAAwC,CACzC;MACH;KACD,CAAC;EACN;EAEAlI,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAACD,WAAW,IAAI,CAAC,IAAI,CAACA,WAAW,CAACP,GAAG,EAAE;MAC9C,IAAI,CAAC8H,mBAAmB,CAACY,SAAS,CAAC,gBAAgB,CAAC;MACpD;;IAGF,IAAI,CAACV,OAAO,GAAG,IAAI;IACnB,IAAI,CAACP,WAAW,CACbjH,UAAU,CAAC,IAAI,CAACD,WAAW,CAACP,GAAG,EAAE,IAAI,CAACO,WAAW,CAAC,CAClD4I,IAAI,CAAC3K,QAAQ,CAAC,MAAO,IAAI,CAACwJ,OAAO,GAAG,KAAM,CAAC,CAAC,CAC5CK,SAAS,CAAC;MACTe,IAAI,EAAGC,IAAU,IAAI;QACnB,MAAMwB,KAAK,GAAG,IAAI,CAAC9C,KAAK,CAAC+C,SAAS,CAAEC,CAAC,IAAKA,CAAC,CAAC/K,GAAG,KAAKqJ,IAAI,CAACrJ,GAAG,CAAC;QAC7D,IAAI6K,KAAK,KAAK,CAAC,CAAC,EAAE;UAChB,IAAI,CAAC9C,KAAK,CAAC8C,KAAK,CAAC,GAAGxB,IAAI;;QAE1B,IAAI,CAAC9I,WAAW,GAAG,IAAI;QACvB,IAAI,CAACuH,mBAAmB,CAAC8C,WAAW,CAAC,+BAA+B,CAAC;MACvE,CAAC;MACD/K,KAAK,EAAGA,KAAU,IAAI;QACpBmJ,OAAO,CAACnJ,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;QAClE,IAAI,CAACiI,mBAAmB,CAACY,SAAS,CAChC,2CAA2C,CAC5C;MACH;KACD,CAAC;EACN;EAEAvE,UAAUA,CAAClE,EAAU;IACnB,IAAI+K,OAAO,CAAC,kDAAkD,CAAC,EAAE;MAC/D,IAAI,CAAChD,OAAO,GAAG,IAAI;MACnB,IAAI,CAACP,WAAW,CACbtD,UAAU,CAAClE,EAAE,CAAC,CACdkJ,IAAI,CAAC3K,QAAQ,CAAC,MAAO,IAAI,CAACwJ,OAAO,GAAG,KAAM,CAAC,CAAC,CAC5CK,SAAS,CAAC;QACTe,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAACrB,KAAK,GAAG,IAAI,CAACA,KAAK,CAACkD,MAAM,CAAEF,CAAC,IAAKA,CAAC,CAAC/K,GAAG,KAAKC,EAAE,CAAC;UACnD,IAAI,CAAC6H,mBAAmB,CAAC8C,WAAW,CAAC,6BAA6B,CAAC;QACrE,CAAC;QACD/K,KAAK,EAAGA,KAAU,IAAI;UACpBmJ,OAAO,CAACnJ,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;UAClE,IAAI,CAACiI,mBAAmB,CAACY,SAAS,CAChC,2CAA2C,CAC5C;QACH;OACD,CAAC;;EAER;EAEA5E,gBAAgBA,CAACgG,IAAU,EAAE1I,MAAuC;IAClE,IAAI,CAAC0I,IAAI,CAAC9J,GAAG,EAAE;IAEf,IAAI,CAACgI,OAAO,GAAG,IAAI;IACnB,IAAI,CAACP,WAAW,CACb3D,gBAAgB,CAACgG,IAAI,CAAC9J,GAAG,EAAEoB,MAAM,CAAC,CAClC+H,IAAI,CAAC3K,QAAQ,CAAC,MAAO,IAAI,CAACwJ,OAAO,GAAG,KAAM,CAAC,CAAC,CAC5CK,SAAS,CAAC;MACTe,IAAI,EAAGC,IAAU,IAAI;QACnB,MAAMwB,KAAK,GAAG,IAAI,CAAC9C,KAAK,CAAC+C,SAAS,CAAEC,CAAC,IAAKA,CAAC,CAAC/K,GAAG,KAAKqJ,IAAI,CAACrJ,GAAG,CAAC;QAC7D,IAAI6K,KAAK,KAAK,CAAC,CAAC,EAAE;UAChB,IAAI,CAAC9C,KAAK,CAAC8C,KAAK,CAAC,GAAGxB,IAAI;;QAE1B,IAAI,CAACvB,mBAAmB,CAAC8C,WAAW,CAAC,+BAA+B,CAAC;MACvE,CAAC;MACD/K,KAAK,EAAGA,KAAU,IAAI;QACpBmJ,OAAO,CAACnJ,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;QAChE,IAAI,CAACiI,mBAAmB,CAACY,SAAS,CAChC,yCAAyC,CAC1C;MACH;KACD,CAAC;EACN;EAEA/E,QAAQA,CAACmG,IAAU;IACjB,IAAI,CAACvJ,WAAW,GAAG;MAAE,GAAGuJ;IAAI,CAAE;EAChC;EAEA5H,UAAUA,CAAA;IACR,IAAI,CAAC3B,WAAW,GAAG,IAAI;EACzB;EAEA4B,cAAcA,CAAA;IACZ,IAAI,CAAC8F,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;IACtC,IAAI,IAAI,CAACA,YAAY,EAAE;MACrB,IAAI,CAACnH,OAAO,GAAG,IAAI,CAACqH,iBAAiB,EAAE;;EAE3C;EAEAA,iBAAiBA,CAAA;IACf,OAAO;MACLtH,KAAK,EAAE,EAAE;MACTkB,WAAW,EAAE,EAAE;MACfX,MAAM,EAAE,MAAM;MACdH,QAAQ,EAAE,QAAQ;MAClBvB,MAAM,EAAE,IAAI,CAACA,MAAM,IAAI,EAAE;MACzBkC,OAAO,EAAE,IAAIsJ,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAE;KAC1D;EACH;;EAEA5B,SAASA,CAAA;IACP;IACA,IAAI,CAACxB,KAAK,CAACqD,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MACvB,MAAMC,aAAa,GAA8B;QAC/CC,IAAI,EAAE,CAAC;QACPC,MAAM,EAAE,CAAC;QACTC,GAAG,EAAE;OACN;MACD,MAAMC,WAAW,GAA8B;QAC7CC,IAAI,EAAE,CAAC;QACP,aAAa,EAAE,CAAC;QAChBC,IAAI,EAAE;OACP;MAED;MACA,IAAIN,aAAa,CAACF,CAAC,CAACpK,QAAQ,CAAC,KAAKsK,aAAa,CAACD,CAAC,CAACrK,QAAQ,CAAC,EAAE;QAC3D,OAAOsK,aAAa,CAACF,CAAC,CAACpK,QAAQ,CAAC,GAAGsK,aAAa,CAACD,CAAC,CAACrK,QAAQ,CAAC;;MAG9D;MACA,OAAO0K,WAAW,CAACN,CAAC,CAACjK,MAAM,CAAC,GAAGuK,WAAW,CAACL,CAAC,CAAClK,MAAM,CAAC;IACtD,CAAC,CAAC;EACJ;EAEA;EACA0K,WAAWA,CAAA;IACT,OAAO,IAAI,CAAC/D,KAAK,CAACkD,MAAM,CAAEnB,IAAI,IAAI;MAChC;MACA,IAAI,IAAI,CAAClH,YAAY,KAAK,KAAK,IAAIkH,IAAI,CAAC1I,MAAM,KAAK,IAAI,CAACwB,YAAY,EAAE;QACpE,OAAO,KAAK;;MAGd;MACA,IACE,IAAI,CAACG,cAAc,KAAK,KAAK,IAC7B+G,IAAI,CAAC7I,QAAQ,KAAK,IAAI,CAAC8B,cAAc,EACrC;QACA,OAAO,KAAK;;MAGd;MACA,IACE,IAAI,CAACN,UAAU,IACf,CAACqH,IAAI,CAACjJ,KAAK,CAACkL,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACvJ,UAAU,CAACsJ,WAAW,EAAE,CAAC,EACjE;QACA,OAAO,KAAK;;MAGd,OAAO,IAAI;IACb,CAAC,CAAC;EACJ;EAEA;EACAlF,YAAYA,CAAA;IACV,OAAO,IAAI,CAACkB,KAAK,CAACkD,MAAM,CACrBnB,IAAI,IACHA,IAAI,CAAC1I,MAAM,KAAK,MAAM,KACrB,IAAI,CAAC2B,cAAc,KAAK,KAAK,IAC5B+G,IAAI,CAAC7I,QAAQ,KAAK,IAAI,CAAC8B,cAAc,CAAC,KACvC,CAAC,IAAI,CAACN,UAAU,IACfqH,IAAI,CAACjJ,KAAK,CAACkL,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACvJ,UAAU,CAACsJ,WAAW,EAAE,CAAC,CAAC,CACtE;EACH;EAEA5E,kBAAkBA,CAAA;IAChB,OAAO,IAAI,CAACY,KAAK,CAACkD,MAAM,CACrBnB,IAAI,IACHA,IAAI,CAAC1I,MAAM,KAAK,aAAa,KAC5B,IAAI,CAAC2B,cAAc,KAAK,KAAK,IAC5B+G,IAAI,CAAC7I,QAAQ,KAAK,IAAI,CAAC8B,cAAc,CAAC,KACvC,CAAC,IAAI,CAACN,UAAU,IACfqH,IAAI,CAACjJ,KAAK,CAACkL,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACvJ,UAAU,CAACsJ,WAAW,EAAE,CAAC,CAAC,CACtE;EACH;EAEAzE,YAAYA,CAAA;IACV,OAAO,IAAI,CAACS,KAAK,CAACkD,MAAM,CACrBnB,IAAI,IACHA,IAAI,CAAC1I,MAAM,KAAK,MAAM,KACrB,IAAI,CAAC2B,cAAc,KAAK,KAAK,IAC5B+G,IAAI,CAAC7I,QAAQ,KAAK,IAAI,CAAC8B,cAAc,CAAC,KACvC,CAAC,IAAI,CAACN,UAAU,IACfqH,IAAI,CAACjJ,KAAK,CAACkL,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACvJ,UAAU,CAACsJ,WAAW,EAAE,CAAC,CAAC,CACtE;EACH;EAEA;EACAnF,iBAAiBA,CAAA;IACf,OAAO,IAAI,CAACmB,KAAK,CAACkD,MAAM,CAAEnB,IAAI,IAAKA,IAAI,CAAC1I,MAAM,KAAK,MAAM,CAAC,CAAC6K,MAAM;EACnE;EAEA/E,uBAAuBA,CAAA;IACrB,OAAO,IAAI,CAACa,KAAK,CAACkD,MAAM,CAAEnB,IAAI,IAAKA,IAAI,CAAC1I,MAAM,KAAK,aAAa,CAAC,CAAC6K,MAAM;EAC1E;EAEA5E,iBAAiBA,CAAA;IACf,OAAO,IAAI,CAACU,KAAK,CAACkD,MAAM,CAAEnB,IAAI,IAAKA,IAAI,CAAC1I,MAAM,KAAK,MAAM,CAAC,CAAC6K,MAAM;EACnE;EAEAC,YAAYA,CAAA;IACV,IAAI,CAACrE,MAAM,CAACsE,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;;;uBA3gBW5E,iBAAiB,EAAA9I,EAAA,CAAA2N,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA7N,EAAA,CAAA2N,iBAAA,CAAAG,EAAA,CAAAC,aAAA,GAAA/N,EAAA,CAAA2N,iBAAA,CAAAK,EAAA,CAAAC,eAAA,GAAAjO,EAAA,CAAA2N,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAAnO,EAAA,CAAA2N,iBAAA,CAAAO,EAAA,CAAAE,MAAA,GAAApO,EAAA,CAAA2N,iBAAA,CAAAU,EAAA,CAAAC,mBAAA;IAAA;EAAA;;;YAAjBxF,iBAAiB;MAAAyF,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCrB9B7O,EAAA,CAAAC,cAAA,aAA2C;UAS/BD,EAAA,CAAA+C,UAAA,IAAAgM,+BAAA,gBAEK;UACL/O,EAAA,CAAAC,cAAA,WAA2B;UAAAD,EAAA,CAAAE,MAAA,sDAAgC;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAEjEH,EAAA,CAAAC,cAAA,aAA0B;UAGtBD,EAAA,CAAAU,UAAA,mBAAAsO,oDAAA;YAAA,OAASF,GAAA,CAAArB,YAAA,EAAc;UAAA,EAAC;UAExBzN,EAAA,CAAAS,SAAA,YAAqC;UAACT,EAAA,CAAAE,MAAA,gBACxC;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAGC;UADCD,EAAA,CAAAU,UAAA,mBAAAuO,oDAAA;YAAA,OAASH,GAAA,CAAApL,cAAA,EAAgB;UAAA,EAAC;UAE1B1D,EAAA,CAAAS,SAAA,aAAsC;UAACT,EAAA,CAAAE,MAAA,6BACzC;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAGbH,EAAA,CAAAS,SAAA,cAAmB;UACrBT,EAAA,CAAAG,YAAA,EAAM;UAIRH,EAAA,CAAA+C,UAAA,KAAAmM,iCAAA,mBAaM;UAGNlP,EAAA,CAAA+C,UAAA,KAAAoM,iCAAA,kBAiBM;UAGNnP,EAAA,CAAA+C,UAAA,KAAAqM,iCAAA,oBAoIM;UAGNpP,EAAA,CAAA+C,UAAA,KAAAsM,iCAAA,mBAsCM;UAGNrP,EAAA,CAAA+C,UAAA,KAAAuM,iCAAA,mBAmBM;UAGNtP,EAAA,CAAA+C,UAAA,KAAAwM,iCAAA,oBA0UM;UACRvP,EAAA,CAAAG,YAAA,EAAM;;;UA9kBgDH,EAAA,CAAAI,SAAA,GAAU;UAAVJ,EAAA,CAAAqB,UAAA,SAAAyN,GAAA,CAAAvO,IAAA,CAAU;UAyBxDP,EAAA,CAAAI,SAAA,IAAa;UAAbJ,EAAA,CAAAqB,UAAA,SAAAyN,GAAA,CAAAvF,OAAA,CAAa;UAgBbvJ,EAAA,CAAAI,SAAA,GAAW;UAAXJ,EAAA,CAAAqB,UAAA,SAAAyN,GAAA,CAAA1N,KAAA,CAAW;UAoBXpB,EAAA,CAAAI,SAAA,GAAiC;UAAjCJ,EAAA,CAAAqB,UAAA,SAAAyN,GAAA,CAAAtF,YAAA,IAAAsF,GAAA,CAAAhN,WAAA,CAAiC;UAuIhB9B,EAAA,CAAAI,SAAA,GAAsB;UAAtBJ,EAAA,CAAAqB,UAAA,SAAAyN,GAAA,CAAAxF,KAAA,CAAAkE,MAAA,KAAsB;UA0C1CxN,EAAA,CAAAI,SAAA,GAA8C;UAA9CJ,EAAA,CAAAqB,UAAA,UAAAyN,GAAA,CAAAvF,OAAA,KAAAuF,GAAA,CAAA1N,KAAA,IAAA0N,GAAA,CAAAxF,KAAA,CAAAkE,MAAA,OAA8C;UAqB3BxN,EAAA,CAAAI,SAAA,GAAsB;UAAtBJ,EAAA,CAAAqB,UAAA,SAAAyN,GAAA,CAAAxF,KAAA,CAAAkE,MAAA,KAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}