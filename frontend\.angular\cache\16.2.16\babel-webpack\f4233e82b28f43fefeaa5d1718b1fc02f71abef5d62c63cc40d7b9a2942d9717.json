{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/OneDrive/Bureau/Project PI/devBridge/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/**\n * Contains the list of OpenAPI data types\n * as defined by https://swagger.io/docs/specification/data-models/data-types/\n * @public\n */\nvar SchemaType;\n(function (SchemaType) {\n  /** String type. */\n  SchemaType[\"STRING\"] = \"string\";\n  /** Number type. */\n  SchemaType[\"NUMBER\"] = \"number\";\n  /** Integer type. */\n  SchemaType[\"INTEGER\"] = \"integer\";\n  /** Boolean type. */\n  SchemaType[\"BOOLEAN\"] = \"boolean\";\n  /** Array type. */\n  SchemaType[\"ARRAY\"] = \"array\";\n  /** Object type. */\n  SchemaType[\"OBJECT\"] = \"object\";\n})(SchemaType || (SchemaType = {}));\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @public\n */\nvar ExecutableCodeLanguage;\n(function (ExecutableCodeLanguage) {\n  ExecutableCodeLanguage[\"LANGUAGE_UNSPECIFIED\"] = \"language_unspecified\";\n  ExecutableCodeLanguage[\"PYTHON\"] = \"python\";\n})(ExecutableCodeLanguage || (ExecutableCodeLanguage = {}));\n/**\n * Possible outcomes of code execution.\n * @public\n */\nvar Outcome;\n(function (Outcome) {\n  /**\n   * Unspecified status. This value should not be used.\n   */\n  Outcome[\"OUTCOME_UNSPECIFIED\"] = \"outcome_unspecified\";\n  /**\n   * Code execution completed successfully.\n   */\n  Outcome[\"OUTCOME_OK\"] = \"outcome_ok\";\n  /**\n   * Code execution finished but with a failure. `stderr` should contain the\n   * reason.\n   */\n  Outcome[\"OUTCOME_FAILED\"] = \"outcome_failed\";\n  /**\n   * Code execution ran for too long, and was cancelled. There may or may not\n   * be a partial output present.\n   */\n  Outcome[\"OUTCOME_DEADLINE_EXCEEDED\"] = \"outcome_deadline_exceeded\";\n})(Outcome || (Outcome = {}));\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Possible roles.\n * @public\n */\nconst POSSIBLE_ROLES = [\"user\", \"model\", \"function\", \"system\"];\n/**\n * Harm categories that would cause prompts or candidates to be blocked.\n * @public\n */\nvar HarmCategory;\n(function (HarmCategory) {\n  HarmCategory[\"HARM_CATEGORY_UNSPECIFIED\"] = \"HARM_CATEGORY_UNSPECIFIED\";\n  HarmCategory[\"HARM_CATEGORY_HATE_SPEECH\"] = \"HARM_CATEGORY_HATE_SPEECH\";\n  HarmCategory[\"HARM_CATEGORY_SEXUALLY_EXPLICIT\"] = \"HARM_CATEGORY_SEXUALLY_EXPLICIT\";\n  HarmCategory[\"HARM_CATEGORY_HARASSMENT\"] = \"HARM_CATEGORY_HARASSMENT\";\n  HarmCategory[\"HARM_CATEGORY_DANGEROUS_CONTENT\"] = \"HARM_CATEGORY_DANGEROUS_CONTENT\";\n  HarmCategory[\"HARM_CATEGORY_CIVIC_INTEGRITY\"] = \"HARM_CATEGORY_CIVIC_INTEGRITY\";\n})(HarmCategory || (HarmCategory = {}));\n/**\n * Threshold above which a prompt or candidate will be blocked.\n * @public\n */\nvar HarmBlockThreshold;\n(function (HarmBlockThreshold) {\n  /** Threshold is unspecified. */\n  HarmBlockThreshold[\"HARM_BLOCK_THRESHOLD_UNSPECIFIED\"] = \"HARM_BLOCK_THRESHOLD_UNSPECIFIED\";\n  /** Content with NEGLIGIBLE will be allowed. */\n  HarmBlockThreshold[\"BLOCK_LOW_AND_ABOVE\"] = \"BLOCK_LOW_AND_ABOVE\";\n  /** Content with NEGLIGIBLE and LOW will be allowed. */\n  HarmBlockThreshold[\"BLOCK_MEDIUM_AND_ABOVE\"] = \"BLOCK_MEDIUM_AND_ABOVE\";\n  /** Content with NEGLIGIBLE, LOW, and MEDIUM will be allowed. */\n  HarmBlockThreshold[\"BLOCK_ONLY_HIGH\"] = \"BLOCK_ONLY_HIGH\";\n  /** All content will be allowed. */\n  HarmBlockThreshold[\"BLOCK_NONE\"] = \"BLOCK_NONE\";\n})(HarmBlockThreshold || (HarmBlockThreshold = {}));\n/**\n * Probability that a prompt or candidate matches a harm category.\n * @public\n */\nvar HarmProbability;\n(function (HarmProbability) {\n  /** Probability is unspecified. */\n  HarmProbability[\"HARM_PROBABILITY_UNSPECIFIED\"] = \"HARM_PROBABILITY_UNSPECIFIED\";\n  /** Content has a negligible chance of being unsafe. */\n  HarmProbability[\"NEGLIGIBLE\"] = \"NEGLIGIBLE\";\n  /** Content has a low chance of being unsafe. */\n  HarmProbability[\"LOW\"] = \"LOW\";\n  /** Content has a medium chance of being unsafe. */\n  HarmProbability[\"MEDIUM\"] = \"MEDIUM\";\n  /** Content has a high chance of being unsafe. */\n  HarmProbability[\"HIGH\"] = \"HIGH\";\n})(HarmProbability || (HarmProbability = {}));\n/**\n * Reason that a prompt was blocked.\n * @public\n */\nvar BlockReason;\n(function (BlockReason) {\n  // A blocked reason was not specified.\n  BlockReason[\"BLOCKED_REASON_UNSPECIFIED\"] = \"BLOCKED_REASON_UNSPECIFIED\";\n  // Content was blocked by safety settings.\n  BlockReason[\"SAFETY\"] = \"SAFETY\";\n  // Content was blocked, but the reason is uncategorized.\n  BlockReason[\"OTHER\"] = \"OTHER\";\n})(BlockReason || (BlockReason = {}));\n/**\n * Reason that a candidate finished.\n * @public\n */\nvar FinishReason;\n(function (FinishReason) {\n  // Default value. This value is unused.\n  FinishReason[\"FINISH_REASON_UNSPECIFIED\"] = \"FINISH_REASON_UNSPECIFIED\";\n  // Natural stop point of the model or provided stop sequence.\n  FinishReason[\"STOP\"] = \"STOP\";\n  // The maximum number of tokens as specified in the request was reached.\n  FinishReason[\"MAX_TOKENS\"] = \"MAX_TOKENS\";\n  // The candidate content was flagged for safety reasons.\n  FinishReason[\"SAFETY\"] = \"SAFETY\";\n  // The candidate content was flagged for recitation reasons.\n  FinishReason[\"RECITATION\"] = \"RECITATION\";\n  // The candidate content was flagged for using an unsupported language.\n  FinishReason[\"LANGUAGE\"] = \"LANGUAGE\";\n  // Token generation stopped because the content contains forbidden terms.\n  FinishReason[\"BLOCKLIST\"] = \"BLOCKLIST\";\n  // Token generation stopped for potentially containing prohibited content.\n  FinishReason[\"PROHIBITED_CONTENT\"] = \"PROHIBITED_CONTENT\";\n  // Token generation stopped because the content potentially contains Sensitive Personally Identifiable Information (SPII).\n  FinishReason[\"SPII\"] = \"SPII\";\n  // The function call generated by the model is invalid.\n  FinishReason[\"MALFORMED_FUNCTION_CALL\"] = \"MALFORMED_FUNCTION_CALL\";\n  // Unknown reason.\n  FinishReason[\"OTHER\"] = \"OTHER\";\n})(FinishReason || (FinishReason = {}));\n/**\n * Task type for embedding content.\n * @public\n */\nvar TaskType;\n(function (TaskType) {\n  TaskType[\"TASK_TYPE_UNSPECIFIED\"] = \"TASK_TYPE_UNSPECIFIED\";\n  TaskType[\"RETRIEVAL_QUERY\"] = \"RETRIEVAL_QUERY\";\n  TaskType[\"RETRIEVAL_DOCUMENT\"] = \"RETRIEVAL_DOCUMENT\";\n  TaskType[\"SEMANTIC_SIMILARITY\"] = \"SEMANTIC_SIMILARITY\";\n  TaskType[\"CLASSIFICATION\"] = \"CLASSIFICATION\";\n  TaskType[\"CLUSTERING\"] = \"CLUSTERING\";\n})(TaskType || (TaskType = {}));\n/**\n * @public\n */\nvar FunctionCallingMode;\n(function (FunctionCallingMode) {\n  // Unspecified function calling mode. This value should not be used.\n  FunctionCallingMode[\"MODE_UNSPECIFIED\"] = \"MODE_UNSPECIFIED\";\n  // Default model behavior, model decides to predict either a function call\n  // or a natural language repspose.\n  FunctionCallingMode[\"AUTO\"] = \"AUTO\";\n  // Model is constrained to always predicting a function call only.\n  // If \"allowed_function_names\" are set, the predicted function call will be\n  // limited to any one of \"allowed_function_names\", else the predicted\n  // function call will be any one of the provided \"function_declarations\".\n  FunctionCallingMode[\"ANY\"] = \"ANY\";\n  // Model will not predict any function call. Model behavior is same as when\n  // not passing any function declarations.\n  FunctionCallingMode[\"NONE\"] = \"NONE\";\n})(FunctionCallingMode || (FunctionCallingMode = {}));\n/**\n * The mode of the predictor to be used in dynamic retrieval.\n * @public\n */\nvar DynamicRetrievalMode;\n(function (DynamicRetrievalMode) {\n  // Unspecified function calling mode. This value should not be used.\n  DynamicRetrievalMode[\"MODE_UNSPECIFIED\"] = \"MODE_UNSPECIFIED\";\n  // Run retrieval only when system decides it is necessary.\n  DynamicRetrievalMode[\"MODE_DYNAMIC\"] = \"MODE_DYNAMIC\";\n})(DynamicRetrievalMode || (DynamicRetrievalMode = {}));\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Basic error type for this SDK.\n * @public\n */\nclass GoogleGenerativeAIError extends Error {\n  constructor(message) {\n    super(`[GoogleGenerativeAI Error]: ${message}`);\n  }\n}\n/**\n * Errors in the contents of a response from the model. This includes parsing\n * errors, or responses including a safety block reason.\n * @public\n */\nclass GoogleGenerativeAIResponseError extends GoogleGenerativeAIError {\n  constructor(message, response) {\n    super(message);\n    this.response = response;\n  }\n}\n/**\n * Error class covering HTTP errors when calling the server. Includes HTTP\n * status, statusText, and optional details, if provided in the server response.\n * @public\n */\nclass GoogleGenerativeAIFetchError extends GoogleGenerativeAIError {\n  constructor(message, status, statusText, errorDetails) {\n    super(message);\n    this.status = status;\n    this.statusText = statusText;\n    this.errorDetails = errorDetails;\n  }\n}\n/**\n * Errors in the contents of a request originating from user input.\n * @public\n */\nclass GoogleGenerativeAIRequestInputError extends GoogleGenerativeAIError {}\n/**\n * Error thrown when a request is aborted, either due to a timeout or\n * intentional cancellation by the user.\n * @public\n */\nclass GoogleGenerativeAIAbortError extends GoogleGenerativeAIError {}\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst DEFAULT_BASE_URL = \"https://generativelanguage.googleapis.com\";\nconst DEFAULT_API_VERSION = \"v1beta\";\n/**\n * We can't `require` package.json if this runs on web. We will use rollup to\n * swap in the version number here at build time.\n */\nconst PACKAGE_VERSION = \"0.24.1\";\nconst PACKAGE_LOG_HEADER = \"genai-js\";\nvar Task;\n(function (Task) {\n  Task[\"GENERATE_CONTENT\"] = \"generateContent\";\n  Task[\"STREAM_GENERATE_CONTENT\"] = \"streamGenerateContent\";\n  Task[\"COUNT_TOKENS\"] = \"countTokens\";\n  Task[\"EMBED_CONTENT\"] = \"embedContent\";\n  Task[\"BATCH_EMBED_CONTENTS\"] = \"batchEmbedContents\";\n})(Task || (Task = {}));\nclass RequestUrl {\n  constructor(model, task, apiKey, stream, requestOptions) {\n    this.model = model;\n    this.task = task;\n    this.apiKey = apiKey;\n    this.stream = stream;\n    this.requestOptions = requestOptions;\n  }\n  toString() {\n    var _a, _b;\n    const apiVersion = ((_a = this.requestOptions) === null || _a === void 0 ? void 0 : _a.apiVersion) || DEFAULT_API_VERSION;\n    const baseUrl = ((_b = this.requestOptions) === null || _b === void 0 ? void 0 : _b.baseUrl) || DEFAULT_BASE_URL;\n    let url = `${baseUrl}/${apiVersion}/${this.model}:${this.task}`;\n    if (this.stream) {\n      url += \"?alt=sse\";\n    }\n    return url;\n  }\n}\n/**\n * Simple, but may become more complex if we add more versions to log.\n */\nfunction getClientHeaders(requestOptions) {\n  const clientHeaders = [];\n  if (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.apiClient) {\n    clientHeaders.push(requestOptions.apiClient);\n  }\n  clientHeaders.push(`${PACKAGE_LOG_HEADER}/${PACKAGE_VERSION}`);\n  return clientHeaders.join(\" \");\n}\nfunction getHeaders(_x) {\n  return _getHeaders.apply(this, arguments);\n}\nfunction _getHeaders() {\n  _getHeaders = _asyncToGenerator(function* (url) {\n    var _a;\n    const headers = new Headers();\n    headers.append(\"Content-Type\", \"application/json\");\n    headers.append(\"x-goog-api-client\", getClientHeaders(url.requestOptions));\n    headers.append(\"x-goog-api-key\", url.apiKey);\n    let customHeaders = (_a = url.requestOptions) === null || _a === void 0 ? void 0 : _a.customHeaders;\n    if (customHeaders) {\n      if (!(customHeaders instanceof Headers)) {\n        try {\n          customHeaders = new Headers(customHeaders);\n        } catch (e) {\n          throw new GoogleGenerativeAIRequestInputError(`unable to convert customHeaders value ${JSON.stringify(customHeaders)} to Headers: ${e.message}`);\n        }\n      }\n      for (const [headerName, headerValue] of customHeaders.entries()) {\n        if (headerName === \"x-goog-api-key\") {\n          throw new GoogleGenerativeAIRequestInputError(`Cannot set reserved header name ${headerName}`);\n        } else if (headerName === \"x-goog-api-client\") {\n          throw new GoogleGenerativeAIRequestInputError(`Header name ${headerName} can only be set using the apiClient field`);\n        }\n        headers.append(headerName, headerValue);\n      }\n    }\n    return headers;\n  });\n  return _getHeaders.apply(this, arguments);\n}\nfunction constructModelRequest(_x2, _x3, _x4, _x5, _x6, _x7) {\n  return _constructModelRequest.apply(this, arguments);\n}\nfunction _constructModelRequest() {\n  _constructModelRequest = _asyncToGenerator(function* (model, task, apiKey, stream, body, requestOptions) {\n    const url = new RequestUrl(model, task, apiKey, stream, requestOptions);\n    return {\n      url: url.toString(),\n      fetchOptions: Object.assign(Object.assign({}, buildFetchOptions(requestOptions)), {\n        method: \"POST\",\n        headers: yield getHeaders(url),\n        body\n      })\n    };\n  });\n  return _constructModelRequest.apply(this, arguments);\n}\nfunction makeModelRequest(_x8, _x9, _x0, _x1, _x10) {\n  return _makeModelRequest.apply(this, arguments);\n}\nfunction _makeModelRequest() {\n  _makeModelRequest = _asyncToGenerator(function* (model, task, apiKey, stream, body, requestOptions = {},\n  // Allows this to be stubbed for tests\n  fetchFn = fetch) {\n    const {\n      url,\n      fetchOptions\n    } = yield constructModelRequest(model, task, apiKey, stream, body, requestOptions);\n    return makeRequest(url, fetchOptions, fetchFn);\n  });\n  return _makeModelRequest.apply(this, arguments);\n}\nfunction makeRequest(_x11, _x12) {\n  return _makeRequest.apply(this, arguments);\n}\nfunction _makeRequest() {\n  _makeRequest = _asyncToGenerator(function* (url, fetchOptions, fetchFn = fetch) {\n    let response;\n    try {\n      response = yield fetchFn(url, fetchOptions);\n    } catch (e) {\n      handleResponseError(e, url);\n    }\n    if (!response.ok) {\n      yield handleResponseNotOk(response, url);\n    }\n    return response;\n  });\n  return _makeRequest.apply(this, arguments);\n}\nfunction handleResponseError(e, url) {\n  let err = e;\n  if (err.name === \"AbortError\") {\n    err = new GoogleGenerativeAIAbortError(`Request aborted when fetching ${url.toString()}: ${e.message}`);\n    err.stack = e.stack;\n  } else if (!(e instanceof GoogleGenerativeAIFetchError || e instanceof GoogleGenerativeAIRequestInputError)) {\n    err = new GoogleGenerativeAIError(`Error fetching from ${url.toString()}: ${e.message}`);\n    err.stack = e.stack;\n  }\n  throw err;\n}\nfunction handleResponseNotOk(_x13, _x14) {\n  return _handleResponseNotOk.apply(this, arguments);\n}\n/**\n * Generates the request options to be passed to the fetch API.\n * @param requestOptions - The user-defined request options.\n * @returns The generated request options.\n */\nfunction _handleResponseNotOk() {\n  _handleResponseNotOk = _asyncToGenerator(function* (response, url) {\n    let message = \"\";\n    let errorDetails;\n    try {\n      const json = yield response.json();\n      message = json.error.message;\n      if (json.error.details) {\n        message += ` ${JSON.stringify(json.error.details)}`;\n        errorDetails = json.error.details;\n      }\n    } catch (e) {\n      // ignored\n    }\n    throw new GoogleGenerativeAIFetchError(`Error fetching from ${url.toString()}: [${response.status} ${response.statusText}] ${message}`, response.status, response.statusText, errorDetails);\n  });\n  return _handleResponseNotOk.apply(this, arguments);\n}\nfunction buildFetchOptions(requestOptions) {\n  const fetchOptions = {};\n  if ((requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.signal) !== undefined || (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.timeout) >= 0) {\n    const controller = new AbortController();\n    if ((requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.timeout) >= 0) {\n      setTimeout(() => controller.abort(), requestOptions.timeout);\n    }\n    if (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.signal) {\n      requestOptions.signal.addEventListener(\"abort\", () => {\n        controller.abort();\n      });\n    }\n    fetchOptions.signal = controller.signal;\n  }\n  return fetchOptions;\n}\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Adds convenience helper methods to a response object, including stream\n * chunks (as long as each chunk is a complete GenerateContentResponse JSON).\n */\nfunction addHelpers(response) {\n  response.text = () => {\n    if (response.candidates && response.candidates.length > 0) {\n      if (response.candidates.length > 1) {\n        console.warn(`This response had ${response.candidates.length} ` + `candidates. Returning text from the first candidate only. ` + `Access response.candidates directly to use the other candidates.`);\n      }\n      if (hadBadFinishReason(response.candidates[0])) {\n        throw new GoogleGenerativeAIResponseError(`${formatBlockErrorMessage(response)}`, response);\n      }\n      return getText(response);\n    } else if (response.promptFeedback) {\n      throw new GoogleGenerativeAIResponseError(`Text not available. ${formatBlockErrorMessage(response)}`, response);\n    }\n    return \"\";\n  };\n  /**\n   * TODO: remove at next major version\n   */\n  response.functionCall = () => {\n    if (response.candidates && response.candidates.length > 0) {\n      if (response.candidates.length > 1) {\n        console.warn(`This response had ${response.candidates.length} ` + `candidates. Returning function calls from the first candidate only. ` + `Access response.candidates directly to use the other candidates.`);\n      }\n      if (hadBadFinishReason(response.candidates[0])) {\n        throw new GoogleGenerativeAIResponseError(`${formatBlockErrorMessage(response)}`, response);\n      }\n      console.warn(`response.functionCall() is deprecated. ` + `Use response.functionCalls() instead.`);\n      return getFunctionCalls(response)[0];\n    } else if (response.promptFeedback) {\n      throw new GoogleGenerativeAIResponseError(`Function call not available. ${formatBlockErrorMessage(response)}`, response);\n    }\n    return undefined;\n  };\n  response.functionCalls = () => {\n    if (response.candidates && response.candidates.length > 0) {\n      if (response.candidates.length > 1) {\n        console.warn(`This response had ${response.candidates.length} ` + `candidates. Returning function calls from the first candidate only. ` + `Access response.candidates directly to use the other candidates.`);\n      }\n      if (hadBadFinishReason(response.candidates[0])) {\n        throw new GoogleGenerativeAIResponseError(`${formatBlockErrorMessage(response)}`, response);\n      }\n      return getFunctionCalls(response);\n    } else if (response.promptFeedback) {\n      throw new GoogleGenerativeAIResponseError(`Function call not available. ${formatBlockErrorMessage(response)}`, response);\n    }\n    return undefined;\n  };\n  return response;\n}\n/**\n * Returns all text found in all parts of first candidate.\n */\nfunction getText(response) {\n  var _a, _b, _c, _d;\n  const textStrings = [];\n  if ((_b = (_a = response.candidates) === null || _a === void 0 ? void 0 : _a[0].content) === null || _b === void 0 ? void 0 : _b.parts) {\n    for (const part of (_d = (_c = response.candidates) === null || _c === void 0 ? void 0 : _c[0].content) === null || _d === void 0 ? void 0 : _d.parts) {\n      if (part.text) {\n        textStrings.push(part.text);\n      }\n      if (part.executableCode) {\n        textStrings.push(\"\\n```\" + part.executableCode.language + \"\\n\" + part.executableCode.code + \"\\n```\\n\");\n      }\n      if (part.codeExecutionResult) {\n        textStrings.push(\"\\n```\\n\" + part.codeExecutionResult.output + \"\\n```\\n\");\n      }\n    }\n  }\n  if (textStrings.length > 0) {\n    return textStrings.join(\"\");\n  } else {\n    return \"\";\n  }\n}\n/**\n * Returns functionCall of first candidate.\n */\nfunction getFunctionCalls(response) {\n  var _a, _b, _c, _d;\n  const functionCalls = [];\n  if ((_b = (_a = response.candidates) === null || _a === void 0 ? void 0 : _a[0].content) === null || _b === void 0 ? void 0 : _b.parts) {\n    for (const part of (_d = (_c = response.candidates) === null || _c === void 0 ? void 0 : _c[0].content) === null || _d === void 0 ? void 0 : _d.parts) {\n      if (part.functionCall) {\n        functionCalls.push(part.functionCall);\n      }\n    }\n  }\n  if (functionCalls.length > 0) {\n    return functionCalls;\n  } else {\n    return undefined;\n  }\n}\nconst badFinishReasons = [FinishReason.RECITATION, FinishReason.SAFETY, FinishReason.LANGUAGE];\nfunction hadBadFinishReason(candidate) {\n  return !!candidate.finishReason && badFinishReasons.includes(candidate.finishReason);\n}\nfunction formatBlockErrorMessage(response) {\n  var _a, _b, _c;\n  let message = \"\";\n  if ((!response.candidates || response.candidates.length === 0) && response.promptFeedback) {\n    message += \"Response was blocked\";\n    if ((_a = response.promptFeedback) === null || _a === void 0 ? void 0 : _a.blockReason) {\n      message += ` due to ${response.promptFeedback.blockReason}`;\n    }\n    if ((_b = response.promptFeedback) === null || _b === void 0 ? void 0 : _b.blockReasonMessage) {\n      message += `: ${response.promptFeedback.blockReasonMessage}`;\n    }\n  } else if ((_c = response.candidates) === null || _c === void 0 ? void 0 : _c[0]) {\n    const firstCandidate = response.candidates[0];\n    if (hadBadFinishReason(firstCandidate)) {\n      message += `Candidate was blocked due to ${firstCandidate.finishReason}`;\n      if (firstCandidate.finishMessage) {\n        message += `: ${firstCandidate.finishMessage}`;\n      }\n    }\n  }\n  return message;\n}\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\n/* global Reflect, Promise, SuppressedError, Symbol */\n\nfunction __await(v) {\n  return this instanceof __await ? (this.v = v, this) : new __await(v);\n}\nfunction __asyncGenerator(thisArg, _arguments, generator) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var g = generator.apply(thisArg, _arguments || []),\n    i,\n    q = [];\n  return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () {\n    return this;\n  }, i;\n  function verb(n) {\n    if (g[n]) i[n] = function (v) {\n      return new Promise(function (a, b) {\n        q.push([n, v, a, b]) > 1 || resume(n, v);\n      });\n    };\n  }\n  function resume(n, v) {\n    try {\n      step(g[n](v));\n    } catch (e) {\n      settle(q[0][3], e);\n    }\n  }\n  function step(r) {\n    r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r);\n  }\n  function fulfill(value) {\n    resume(\"next\", value);\n  }\n  function reject(value) {\n    resume(\"throw\", value);\n  }\n  function settle(f, v) {\n    if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]);\n  }\n}\ntypeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n  var e = new Error(message);\n  return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n};\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst responseLineRE = /^data\\: (.*)(?:\\n\\n|\\r\\r|\\r\\n\\r\\n)/;\n/**\n * Process a response.body stream from the backend and return an\n * iterator that provides one complete GenerateContentResponse at a time\n * and a promise that resolves with a single aggregated\n * GenerateContentResponse.\n *\n * @param response - Response from a fetch call\n */\nfunction processStream(response) {\n  const inputStream = response.body.pipeThrough(new TextDecoderStream(\"utf8\", {\n    fatal: true\n  }));\n  const responseStream = getResponseStream(inputStream);\n  const [stream1, stream2] = responseStream.tee();\n  return {\n    stream: generateResponseSequence(stream1),\n    response: getResponsePromise(stream2)\n  };\n}\nfunction getResponsePromise(_x15) {\n  return _getResponsePromise.apply(this, arguments);\n}\nfunction _getResponsePromise() {\n  _getResponsePromise = _asyncToGenerator(function* (stream) {\n    const allResponses = [];\n    const reader = stream.getReader();\n    while (true) {\n      const {\n        done,\n        value\n      } = yield reader.read();\n      if (done) {\n        return addHelpers(aggregateResponses(allResponses));\n      }\n      allResponses.push(value);\n    }\n  });\n  return _getResponsePromise.apply(this, arguments);\n}\nfunction generateResponseSequence(stream) {\n  return __asyncGenerator(this, arguments, function* generateResponseSequence_1() {\n    const reader = stream.getReader();\n    while (true) {\n      const {\n        value,\n        done\n      } = yield __await(reader.read());\n      if (done) {\n        break;\n      }\n      yield yield __await(addHelpers(value));\n    }\n  });\n}\n/**\n * Reads a raw stream from the fetch response and join incomplete\n * chunks, returning a new stream that provides a single complete\n * GenerateContentResponse in each iteration.\n */\nfunction getResponseStream(inputStream) {\n  const reader = inputStream.getReader();\n  const stream = new ReadableStream({\n    start(controller) {\n      let currentText = \"\";\n      return pump();\n      function pump() {\n        return reader.read().then(({\n          value,\n          done\n        }) => {\n          if (done) {\n            if (currentText.trim()) {\n              controller.error(new GoogleGenerativeAIError(\"Failed to parse stream\"));\n              return;\n            }\n            controller.close();\n            return;\n          }\n          currentText += value;\n          let match = currentText.match(responseLineRE);\n          let parsedResponse;\n          while (match) {\n            try {\n              parsedResponse = JSON.parse(match[1]);\n            } catch (e) {\n              controller.error(new GoogleGenerativeAIError(`Error parsing JSON response: \"${match[1]}\"`));\n              return;\n            }\n            controller.enqueue(parsedResponse);\n            currentText = currentText.substring(match[0].length);\n            match = currentText.match(responseLineRE);\n          }\n          return pump();\n        }).catch(e => {\n          let err = e;\n          err.stack = e.stack;\n          if (err.name === \"AbortError\") {\n            err = new GoogleGenerativeAIAbortError(\"Request aborted when reading from the stream\");\n          } else {\n            err = new GoogleGenerativeAIError(\"Error reading from the stream\");\n          }\n          throw err;\n        });\n      }\n    }\n  });\n  return stream;\n}\n/**\n * Aggregates an array of `GenerateContentResponse`s into a single\n * GenerateContentResponse.\n */\nfunction aggregateResponses(responses) {\n  const lastResponse = responses[responses.length - 1];\n  const aggregatedResponse = {\n    promptFeedback: lastResponse === null || lastResponse === void 0 ? void 0 : lastResponse.promptFeedback\n  };\n  for (const response of responses) {\n    if (response.candidates) {\n      let candidateIndex = 0;\n      for (const candidate of response.candidates) {\n        if (!aggregatedResponse.candidates) {\n          aggregatedResponse.candidates = [];\n        }\n        if (!aggregatedResponse.candidates[candidateIndex]) {\n          aggregatedResponse.candidates[candidateIndex] = {\n            index: candidateIndex\n          };\n        }\n        // Keep overwriting, the last one will be final\n        aggregatedResponse.candidates[candidateIndex].citationMetadata = candidate.citationMetadata;\n        aggregatedResponse.candidates[candidateIndex].groundingMetadata = candidate.groundingMetadata;\n        aggregatedResponse.candidates[candidateIndex].finishReason = candidate.finishReason;\n        aggregatedResponse.candidates[candidateIndex].finishMessage = candidate.finishMessage;\n        aggregatedResponse.candidates[candidateIndex].safetyRatings = candidate.safetyRatings;\n        /**\n         * Candidates should always have content and parts, but this handles\n         * possible malformed responses.\n         */\n        if (candidate.content && candidate.content.parts) {\n          if (!aggregatedResponse.candidates[candidateIndex].content) {\n            aggregatedResponse.candidates[candidateIndex].content = {\n              role: candidate.content.role || \"user\",\n              parts: []\n            };\n          }\n          const newPart = {};\n          for (const part of candidate.content.parts) {\n            if (part.text) {\n              newPart.text = part.text;\n            }\n            if (part.functionCall) {\n              newPart.functionCall = part.functionCall;\n            }\n            if (part.executableCode) {\n              newPart.executableCode = part.executableCode;\n            }\n            if (part.codeExecutionResult) {\n              newPart.codeExecutionResult = part.codeExecutionResult;\n            }\n            if (Object.keys(newPart).length === 0) {\n              newPart.text = \"\";\n            }\n            aggregatedResponse.candidates[candidateIndex].content.parts.push(newPart);\n          }\n        }\n      }\n      candidateIndex++;\n    }\n    if (response.usageMetadata) {\n      aggregatedResponse.usageMetadata = response.usageMetadata;\n    }\n  }\n  return aggregatedResponse;\n}\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction generateContentStream(_x16, _x17, _x18, _x19) {\n  return _generateContentStream.apply(this, arguments);\n}\nfunction _generateContentStream() {\n  _generateContentStream = _asyncToGenerator(function* (apiKey, model, params, requestOptions) {\n    const response = yield makeModelRequest(model, Task.STREAM_GENERATE_CONTENT, apiKey, /* stream */true, JSON.stringify(params), requestOptions);\n    return processStream(response);\n  });\n  return _generateContentStream.apply(this, arguments);\n}\nfunction generateContent(_x20, _x21, _x22, _x23) {\n  return _generateContent.apply(this, arguments);\n}\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction _generateContent() {\n  _generateContent = _asyncToGenerator(function* (apiKey, model, params, requestOptions) {\n    const response = yield makeModelRequest(model, Task.GENERATE_CONTENT, apiKey, /* stream */false, JSON.stringify(params), requestOptions);\n    const responseJson = yield response.json();\n    const enhancedResponse = addHelpers(responseJson);\n    return {\n      response: enhancedResponse\n    };\n  });\n  return _generateContent.apply(this, arguments);\n}\nfunction formatSystemInstruction(input) {\n  // null or undefined\n  if (input == null) {\n    return undefined;\n  } else if (typeof input === \"string\") {\n    return {\n      role: \"system\",\n      parts: [{\n        text: input\n      }]\n    };\n  } else if (input.text) {\n    return {\n      role: \"system\",\n      parts: [input]\n    };\n  } else if (input.parts) {\n    if (!input.role) {\n      return {\n        role: \"system\",\n        parts: input.parts\n      };\n    } else {\n      return input;\n    }\n  }\n}\nfunction formatNewContent(request) {\n  let newParts = [];\n  if (typeof request === \"string\") {\n    newParts = [{\n      text: request\n    }];\n  } else {\n    for (const partOrString of request) {\n      if (typeof partOrString === \"string\") {\n        newParts.push({\n          text: partOrString\n        });\n      } else {\n        newParts.push(partOrString);\n      }\n    }\n  }\n  return assignRoleToPartsAndValidateSendMessageRequest(newParts);\n}\n/**\n * When multiple Part types (i.e. FunctionResponsePart and TextPart) are\n * passed in a single Part array, we may need to assign different roles to each\n * part. Currently only FunctionResponsePart requires a role other than 'user'.\n * @private\n * @param parts Array of parts to pass to the model\n * @returns Array of content items\n */\nfunction assignRoleToPartsAndValidateSendMessageRequest(parts) {\n  const userContent = {\n    role: \"user\",\n    parts: []\n  };\n  const functionContent = {\n    role: \"function\",\n    parts: []\n  };\n  let hasUserContent = false;\n  let hasFunctionContent = false;\n  for (const part of parts) {\n    if (\"functionResponse\" in part) {\n      functionContent.parts.push(part);\n      hasFunctionContent = true;\n    } else {\n      userContent.parts.push(part);\n      hasUserContent = true;\n    }\n  }\n  if (hasUserContent && hasFunctionContent) {\n    throw new GoogleGenerativeAIError(\"Within a single message, FunctionResponse cannot be mixed with other type of part in the request for sending chat message.\");\n  }\n  if (!hasUserContent && !hasFunctionContent) {\n    throw new GoogleGenerativeAIError(\"No content is provided for sending chat message.\");\n  }\n  if (hasUserContent) {\n    return userContent;\n  }\n  return functionContent;\n}\nfunction formatCountTokensInput(params, modelParams) {\n  var _a;\n  let formattedGenerateContentRequest = {\n    model: modelParams === null || modelParams === void 0 ? void 0 : modelParams.model,\n    generationConfig: modelParams === null || modelParams === void 0 ? void 0 : modelParams.generationConfig,\n    safetySettings: modelParams === null || modelParams === void 0 ? void 0 : modelParams.safetySettings,\n    tools: modelParams === null || modelParams === void 0 ? void 0 : modelParams.tools,\n    toolConfig: modelParams === null || modelParams === void 0 ? void 0 : modelParams.toolConfig,\n    systemInstruction: modelParams === null || modelParams === void 0 ? void 0 : modelParams.systemInstruction,\n    cachedContent: (_a = modelParams === null || modelParams === void 0 ? void 0 : modelParams.cachedContent) === null || _a === void 0 ? void 0 : _a.name,\n    contents: []\n  };\n  const containsGenerateContentRequest = params.generateContentRequest != null;\n  if (params.contents) {\n    if (containsGenerateContentRequest) {\n      throw new GoogleGenerativeAIRequestInputError(\"CountTokensRequest must have one of contents or generateContentRequest, not both.\");\n    }\n    formattedGenerateContentRequest.contents = params.contents;\n  } else if (containsGenerateContentRequest) {\n    formattedGenerateContentRequest = Object.assign(Object.assign({}, formattedGenerateContentRequest), params.generateContentRequest);\n  } else {\n    // Array or string\n    const content = formatNewContent(params);\n    formattedGenerateContentRequest.contents = [content];\n  }\n  return {\n    generateContentRequest: formattedGenerateContentRequest\n  };\n}\nfunction formatGenerateContentInput(params) {\n  let formattedRequest;\n  if (params.contents) {\n    formattedRequest = params;\n  } else {\n    // Array or string\n    const content = formatNewContent(params);\n    formattedRequest = {\n      contents: [content]\n    };\n  }\n  if (params.systemInstruction) {\n    formattedRequest.systemInstruction = formatSystemInstruction(params.systemInstruction);\n  }\n  return formattedRequest;\n}\nfunction formatEmbedContentInput(params) {\n  if (typeof params === \"string\" || Array.isArray(params)) {\n    const content = formatNewContent(params);\n    return {\n      content\n    };\n  }\n  return params;\n}\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// https://ai.google.dev/api/rest/v1beta/Content#part\nconst VALID_PART_FIELDS = [\"text\", \"inlineData\", \"functionCall\", \"functionResponse\", \"executableCode\", \"codeExecutionResult\"];\nconst VALID_PARTS_PER_ROLE = {\n  user: [\"text\", \"inlineData\"],\n  function: [\"functionResponse\"],\n  model: [\"text\", \"functionCall\", \"executableCode\", \"codeExecutionResult\"],\n  // System instructions shouldn't be in history anyway.\n  system: [\"text\"]\n};\nfunction validateChatHistory(history) {\n  let prevContent = false;\n  for (const currContent of history) {\n    const {\n      role,\n      parts\n    } = currContent;\n    if (!prevContent && role !== \"user\") {\n      throw new GoogleGenerativeAIError(`First content should be with role 'user', got ${role}`);\n    }\n    if (!POSSIBLE_ROLES.includes(role)) {\n      throw new GoogleGenerativeAIError(`Each item should include role field. Got ${role} but valid roles are: ${JSON.stringify(POSSIBLE_ROLES)}`);\n    }\n    if (!Array.isArray(parts)) {\n      throw new GoogleGenerativeAIError(\"Content should have 'parts' property with an array of Parts\");\n    }\n    if (parts.length === 0) {\n      throw new GoogleGenerativeAIError(\"Each Content should have at least one part\");\n    }\n    const countFields = {\n      text: 0,\n      inlineData: 0,\n      functionCall: 0,\n      functionResponse: 0,\n      fileData: 0,\n      executableCode: 0,\n      codeExecutionResult: 0\n    };\n    for (const part of parts) {\n      for (const key of VALID_PART_FIELDS) {\n        if (key in part) {\n          countFields[key] += 1;\n        }\n      }\n    }\n    const validParts = VALID_PARTS_PER_ROLE[role];\n    for (const key of VALID_PART_FIELDS) {\n      if (!validParts.includes(key) && countFields[key] > 0) {\n        throw new GoogleGenerativeAIError(`Content with role '${role}' can't contain '${key}' part`);\n      }\n    }\n    prevContent = true;\n  }\n}\n/**\n * Returns true if the response is valid (could be appended to the history), flase otherwise.\n */\nfunction isValidResponse(response) {\n  var _a;\n  if (response.candidates === undefined || response.candidates.length === 0) {\n    return false;\n  }\n  const content = (_a = response.candidates[0]) === null || _a === void 0 ? void 0 : _a.content;\n  if (content === undefined) {\n    return false;\n  }\n  if (content.parts === undefined || content.parts.length === 0) {\n    return false;\n  }\n  for (const part of content.parts) {\n    if (part === undefined || Object.keys(part).length === 0) {\n      return false;\n    }\n    if (part.text !== undefined && part.text === \"\") {\n      return false;\n    }\n  }\n  return true;\n}\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Do not log a message for this error.\n */\nconst SILENT_ERROR = \"SILENT_ERROR\";\n/**\n * ChatSession class that enables sending chat messages and stores\n * history of sent and received messages so far.\n *\n * @public\n */\nclass ChatSession {\n  constructor(apiKey, model, params, _requestOptions = {}) {\n    this.model = model;\n    this.params = params;\n    this._requestOptions = _requestOptions;\n    this._history = [];\n    this._sendPromise = Promise.resolve();\n    this._apiKey = apiKey;\n    if (params === null || params === void 0 ? void 0 : params.history) {\n      validateChatHistory(params.history);\n      this._history = params.history;\n    }\n  }\n  /**\n   * Gets the chat history so far. Blocked prompts are not added to history.\n   * Blocked candidates are not added to history, nor are the prompts that\n   * generated them.\n   */\n  getHistory() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      yield _this._sendPromise;\n      return _this._history;\n    })();\n  }\n  /**\n   * Sends a chat message and receives a non-streaming\n   * {@link GenerateContentResult}.\n   *\n   * Fields set in the optional {@link SingleRequestOptions} parameter will\n   * take precedence over the {@link RequestOptions} values provided to\n   * {@link GoogleGenerativeAI.getGenerativeModel }.\n   */\n  sendMessage(_x24) {\n    var _this2 = this;\n    return _asyncToGenerator(function* (request, requestOptions = {}) {\n      var _a, _b, _c, _d, _e, _f;\n      yield _this2._sendPromise;\n      const newContent = formatNewContent(request);\n      const generateContentRequest = {\n        safetySettings: (_a = _this2.params) === null || _a === void 0 ? void 0 : _a.safetySettings,\n        generationConfig: (_b = _this2.params) === null || _b === void 0 ? void 0 : _b.generationConfig,\n        tools: (_c = _this2.params) === null || _c === void 0 ? void 0 : _c.tools,\n        toolConfig: (_d = _this2.params) === null || _d === void 0 ? void 0 : _d.toolConfig,\n        systemInstruction: (_e = _this2.params) === null || _e === void 0 ? void 0 : _e.systemInstruction,\n        cachedContent: (_f = _this2.params) === null || _f === void 0 ? void 0 : _f.cachedContent,\n        contents: [..._this2._history, newContent]\n      };\n      const chatSessionRequestOptions = Object.assign(Object.assign({}, _this2._requestOptions), requestOptions);\n      let finalResult;\n      // Add onto the chain.\n      _this2._sendPromise = _this2._sendPromise.then(() => generateContent(_this2._apiKey, _this2.model, generateContentRequest, chatSessionRequestOptions)).then(result => {\n        var _a;\n        if (isValidResponse(result.response)) {\n          _this2._history.push(newContent);\n          const responseContent = Object.assign({\n            parts: [],\n            // Response seems to come back without a role set.\n            role: \"model\"\n          }, (_a = result.response.candidates) === null || _a === void 0 ? void 0 : _a[0].content);\n          _this2._history.push(responseContent);\n        } else {\n          const blockErrorMessage = formatBlockErrorMessage(result.response);\n          if (blockErrorMessage) {\n            console.warn(`sendMessage() was unsuccessful. ${blockErrorMessage}. Inspect response object for details.`);\n          }\n        }\n        finalResult = result;\n      }).catch(e => {\n        // Resets _sendPromise to avoid subsequent calls failing and throw error.\n        _this2._sendPromise = Promise.resolve();\n        throw e;\n      });\n      yield _this2._sendPromise;\n      return finalResult;\n    }).apply(this, arguments);\n  }\n  /**\n   * Sends a chat message and receives the response as a\n   * {@link GenerateContentStreamResult} containing an iterable stream\n   * and a response promise.\n   *\n   * Fields set in the optional {@link SingleRequestOptions} parameter will\n   * take precedence over the {@link RequestOptions} values provided to\n   * {@link GoogleGenerativeAI.getGenerativeModel }.\n   */\n  sendMessageStream(_x25) {\n    var _this3 = this;\n    return _asyncToGenerator(function* (request, requestOptions = {}) {\n      var _a, _b, _c, _d, _e, _f;\n      yield _this3._sendPromise;\n      const newContent = formatNewContent(request);\n      const generateContentRequest = {\n        safetySettings: (_a = _this3.params) === null || _a === void 0 ? void 0 : _a.safetySettings,\n        generationConfig: (_b = _this3.params) === null || _b === void 0 ? void 0 : _b.generationConfig,\n        tools: (_c = _this3.params) === null || _c === void 0 ? void 0 : _c.tools,\n        toolConfig: (_d = _this3.params) === null || _d === void 0 ? void 0 : _d.toolConfig,\n        systemInstruction: (_e = _this3.params) === null || _e === void 0 ? void 0 : _e.systemInstruction,\n        cachedContent: (_f = _this3.params) === null || _f === void 0 ? void 0 : _f.cachedContent,\n        contents: [..._this3._history, newContent]\n      };\n      const chatSessionRequestOptions = Object.assign(Object.assign({}, _this3._requestOptions), requestOptions);\n      const streamPromise = generateContentStream(_this3._apiKey, _this3.model, generateContentRequest, chatSessionRequestOptions);\n      // Add onto the chain.\n      _this3._sendPromise = _this3._sendPromise.then(() => streamPromise)\n      // This must be handled to avoid unhandled rejection, but jump\n      // to the final catch block with a label to not log this error.\n      .catch(_ignored => {\n        throw new Error(SILENT_ERROR);\n      }).then(streamResult => streamResult.response).then(response => {\n        if (isValidResponse(response)) {\n          _this3._history.push(newContent);\n          const responseContent = Object.assign({}, response.candidates[0].content);\n          // Response seems to come back without a role set.\n          if (!responseContent.role) {\n            responseContent.role = \"model\";\n          }\n          _this3._history.push(responseContent);\n        } else {\n          const blockErrorMessage = formatBlockErrorMessage(response);\n          if (blockErrorMessage) {\n            console.warn(`sendMessageStream() was unsuccessful. ${blockErrorMessage}. Inspect response object for details.`);\n          }\n        }\n      }).catch(e => {\n        // Errors in streamPromise are already catchable by the user as\n        // streamPromise is returned.\n        // Avoid duplicating the error message in logs.\n        if (e.message !== SILENT_ERROR) {\n          // Users do not have access to _sendPromise to catch errors\n          // downstream from streamPromise, so they should not throw.\n          console.error(e);\n        }\n      });\n      return streamPromise;\n    }).apply(this, arguments);\n  }\n}\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction countTokens(_x26, _x27, _x28, _x29) {\n  return _countTokens.apply(this, arguments);\n}\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction _countTokens() {\n  _countTokens = _asyncToGenerator(function* (apiKey, model, params, singleRequestOptions) {\n    const response = yield makeModelRequest(model, Task.COUNT_TOKENS, apiKey, false, JSON.stringify(params), singleRequestOptions);\n    return response.json();\n  });\n  return _countTokens.apply(this, arguments);\n}\nfunction embedContent(_x30, _x31, _x32, _x33) {\n  return _embedContent.apply(this, arguments);\n}\nfunction _embedContent() {\n  _embedContent = _asyncToGenerator(function* (apiKey, model, params, requestOptions) {\n    const response = yield makeModelRequest(model, Task.EMBED_CONTENT, apiKey, false, JSON.stringify(params), requestOptions);\n    return response.json();\n  });\n  return _embedContent.apply(this, arguments);\n}\nfunction batchEmbedContents(_x34, _x35, _x36, _x37) {\n  return _batchEmbedContents.apply(this, arguments);\n}\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Class for generative model APIs.\n * @public\n */\nfunction _batchEmbedContents() {\n  _batchEmbedContents = _asyncToGenerator(function* (apiKey, model, params, requestOptions) {\n    const requestsWithModel = params.requests.map(request => {\n      return Object.assign(Object.assign({}, request), {\n        model\n      });\n    });\n    const response = yield makeModelRequest(model, Task.BATCH_EMBED_CONTENTS, apiKey, false, JSON.stringify({\n      requests: requestsWithModel\n    }), requestOptions);\n    return response.json();\n  });\n  return _batchEmbedContents.apply(this, arguments);\n}\nclass GenerativeModel {\n  constructor(apiKey, modelParams, _requestOptions = {}) {\n    this.apiKey = apiKey;\n    this._requestOptions = _requestOptions;\n    if (modelParams.model.includes(\"/\")) {\n      // Models may be named \"models/model-name\" or \"tunedModels/model-name\"\n      this.model = modelParams.model;\n    } else {\n      // If path is not included, assume it's a non-tuned model.\n      this.model = `models/${modelParams.model}`;\n    }\n    this.generationConfig = modelParams.generationConfig || {};\n    this.safetySettings = modelParams.safetySettings || [];\n    this.tools = modelParams.tools;\n    this.toolConfig = modelParams.toolConfig;\n    this.systemInstruction = formatSystemInstruction(modelParams.systemInstruction);\n    this.cachedContent = modelParams.cachedContent;\n  }\n  /**\n   * Makes a single non-streaming call to the model\n   * and returns an object containing a single {@link GenerateContentResponse}.\n   *\n   * Fields set in the optional {@link SingleRequestOptions} parameter will\n   * take precedence over the {@link RequestOptions} values provided to\n   * {@link GoogleGenerativeAI.getGenerativeModel }.\n   */\n  generateContent(_x38) {\n    var _this4 = this;\n    return _asyncToGenerator(function* (request, requestOptions = {}) {\n      var _a;\n      const formattedParams = formatGenerateContentInput(request);\n      const generativeModelRequestOptions = Object.assign(Object.assign({}, _this4._requestOptions), requestOptions);\n      return generateContent(_this4.apiKey, _this4.model, Object.assign({\n        generationConfig: _this4.generationConfig,\n        safetySettings: _this4.safetySettings,\n        tools: _this4.tools,\n        toolConfig: _this4.toolConfig,\n        systemInstruction: _this4.systemInstruction,\n        cachedContent: (_a = _this4.cachedContent) === null || _a === void 0 ? void 0 : _a.name\n      }, formattedParams), generativeModelRequestOptions);\n    }).apply(this, arguments);\n  }\n  /**\n   * Makes a single streaming call to the model and returns an object\n   * containing an iterable stream that iterates over all chunks in the\n   * streaming response as well as a promise that returns the final\n   * aggregated response.\n   *\n   * Fields set in the optional {@link SingleRequestOptions} parameter will\n   * take precedence over the {@link RequestOptions} values provided to\n   * {@link GoogleGenerativeAI.getGenerativeModel }.\n   */\n  generateContentStream(_x39) {\n    var _this5 = this;\n    return _asyncToGenerator(function* (request, requestOptions = {}) {\n      var _a;\n      const formattedParams = formatGenerateContentInput(request);\n      const generativeModelRequestOptions = Object.assign(Object.assign({}, _this5._requestOptions), requestOptions);\n      return generateContentStream(_this5.apiKey, _this5.model, Object.assign({\n        generationConfig: _this5.generationConfig,\n        safetySettings: _this5.safetySettings,\n        tools: _this5.tools,\n        toolConfig: _this5.toolConfig,\n        systemInstruction: _this5.systemInstruction,\n        cachedContent: (_a = _this5.cachedContent) === null || _a === void 0 ? void 0 : _a.name\n      }, formattedParams), generativeModelRequestOptions);\n    }).apply(this, arguments);\n  }\n  /**\n   * Gets a new {@link ChatSession} instance which can be used for\n   * multi-turn chats.\n   */\n  startChat(startChatParams) {\n    var _a;\n    return new ChatSession(this.apiKey, this.model, Object.assign({\n      generationConfig: this.generationConfig,\n      safetySettings: this.safetySettings,\n      tools: this.tools,\n      toolConfig: this.toolConfig,\n      systemInstruction: this.systemInstruction,\n      cachedContent: (_a = this.cachedContent) === null || _a === void 0 ? void 0 : _a.name\n    }, startChatParams), this._requestOptions);\n  }\n  /**\n   * Counts the tokens in the provided request.\n   *\n   * Fields set in the optional {@link SingleRequestOptions} parameter will\n   * take precedence over the {@link RequestOptions} values provided to\n   * {@link GoogleGenerativeAI.getGenerativeModel }.\n   */\n  countTokens(_x40) {\n    var _this6 = this;\n    return _asyncToGenerator(function* (request, requestOptions = {}) {\n      const formattedParams = formatCountTokensInput(request, {\n        model: _this6.model,\n        generationConfig: _this6.generationConfig,\n        safetySettings: _this6.safetySettings,\n        tools: _this6.tools,\n        toolConfig: _this6.toolConfig,\n        systemInstruction: _this6.systemInstruction,\n        cachedContent: _this6.cachedContent\n      });\n      const generativeModelRequestOptions = Object.assign(Object.assign({}, _this6._requestOptions), requestOptions);\n      return countTokens(_this6.apiKey, _this6.model, formattedParams, generativeModelRequestOptions);\n    }).apply(this, arguments);\n  }\n  /**\n   * Embeds the provided content.\n   *\n   * Fields set in the optional {@link SingleRequestOptions} parameter will\n   * take precedence over the {@link RequestOptions} values provided to\n   * {@link GoogleGenerativeAI.getGenerativeModel }.\n   */\n  embedContent(_x41) {\n    var _this7 = this;\n    return _asyncToGenerator(function* (request, requestOptions = {}) {\n      const formattedParams = formatEmbedContentInput(request);\n      const generativeModelRequestOptions = Object.assign(Object.assign({}, _this7._requestOptions), requestOptions);\n      return embedContent(_this7.apiKey, _this7.model, formattedParams, generativeModelRequestOptions);\n    }).apply(this, arguments);\n  }\n  /**\n   * Embeds an array of {@link EmbedContentRequest}s.\n   *\n   * Fields set in the optional {@link SingleRequestOptions} parameter will\n   * take precedence over the {@link RequestOptions} values provided to\n   * {@link GoogleGenerativeAI.getGenerativeModel }.\n   */\n  batchEmbedContents(_x42) {\n    var _this8 = this;\n    return _asyncToGenerator(function* (batchEmbedContentRequest, requestOptions = {}) {\n      const generativeModelRequestOptions = Object.assign(Object.assign({}, _this8._requestOptions), requestOptions);\n      return batchEmbedContents(_this8.apiKey, _this8.model, batchEmbedContentRequest, generativeModelRequestOptions);\n    }).apply(this, arguments);\n  }\n}\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Top-level class for this SDK\n * @public\n */\nclass GoogleGenerativeAI {\n  constructor(apiKey) {\n    this.apiKey = apiKey;\n  }\n  /**\n   * Gets a {@link GenerativeModel} instance for the provided model name.\n   */\n  getGenerativeModel(modelParams, requestOptions) {\n    if (!modelParams.model) {\n      throw new GoogleGenerativeAIError(`Must provide a model name. ` + `Example: genai.getGenerativeModel({ model: 'my-model-name' })`);\n    }\n    return new GenerativeModel(this.apiKey, modelParams, requestOptions);\n  }\n  /**\n   * Creates a {@link GenerativeModel} instance from provided content cache.\n   */\n  getGenerativeModelFromCachedContent(cachedContent, modelParams, requestOptions) {\n    if (!cachedContent.name) {\n      throw new GoogleGenerativeAIRequestInputError(\"Cached content must contain a `name` field.\");\n    }\n    if (!cachedContent.model) {\n      throw new GoogleGenerativeAIRequestInputError(\"Cached content must contain a `model` field.\");\n    }\n    /**\n     * Not checking tools and toolConfig for now as it would require a deep\n     * equality comparison and isn't likely to be a common case.\n     */\n    const disallowedDuplicates = [\"model\", \"systemInstruction\"];\n    for (const key of disallowedDuplicates) {\n      if ((modelParams === null || modelParams === void 0 ? void 0 : modelParams[key]) && cachedContent[key] && (modelParams === null || modelParams === void 0 ? void 0 : modelParams[key]) !== cachedContent[key]) {\n        if (key === \"model\") {\n          const modelParamsComp = modelParams.model.startsWith(\"models/\") ? modelParams.model.replace(\"models/\", \"\") : modelParams.model;\n          const cachedContentComp = cachedContent.model.startsWith(\"models/\") ? cachedContent.model.replace(\"models/\", \"\") : cachedContent.model;\n          if (modelParamsComp === cachedContentComp) {\n            continue;\n          }\n        }\n        throw new GoogleGenerativeAIRequestInputError(`Different value for \"${key}\" specified in modelParams` + ` (${modelParams[key]}) and cachedContent (${cachedContent[key]})`);\n      }\n    }\n    const modelParamsFromCache = Object.assign(Object.assign({}, modelParams), {\n      model: cachedContent.model,\n      tools: cachedContent.tools,\n      toolConfig: cachedContent.toolConfig,\n      systemInstruction: cachedContent.systemInstruction,\n      cachedContent\n    });\n    return new GenerativeModel(this.apiKey, modelParamsFromCache, requestOptions);\n  }\n}\nexport { BlockReason, ChatSession, DynamicRetrievalMode, ExecutableCodeLanguage, FinishReason, FunctionCallingMode, GenerativeModel, GoogleGenerativeAI, GoogleGenerativeAIAbortError, GoogleGenerativeAIError, GoogleGenerativeAIFetchError, GoogleGenerativeAIRequestInputError, GoogleGenerativeAIResponseError, HarmBlockThreshold, HarmCategory, HarmProbability, Outcome, POSSIBLE_ROLES, SchemaType, TaskType };", "map": {"version": 3, "names": ["SchemaType", "ExecutableCodeLanguage", "Outcome", "POSSIBLE_ROLES", "HarmCategory", "HarmBlockThreshold", "HarmProbability", "BlockReason", "FinishReason", "TaskType", "FunctionCallingMode", "DynamicRetrievalMode", "GoogleGenerativeAIError", "Error", "constructor", "message", "GoogleGenerativeAIResponseError", "response", "GoogleGenerativeAIFetchError", "status", "statusText", "errorDetails", "GoogleGenerativeAIRequestInputError", "GoogleGenerativeAIAbortError", "DEFAULT_BASE_URL", "DEFAULT_API_VERSION", "PACKAGE_VERSION", "PACKAGE_LOG_HEADER", "Task", "RequestUrl", "model", "task", "<PERSON><PERSON><PERSON><PERSON>", "stream", "requestOptions", "toString", "_a", "_b", "apiVersion", "baseUrl", "url", "getClientHeaders", "clientHeaders", "apiClient", "push", "join", "getHeaders", "_x", "_getHeaders", "apply", "arguments", "_asyncToGenerator", "headers", "Headers", "append", "customHeaders", "e", "JSON", "stringify", "headerName", "headerValue", "entries", "constructModelRequest", "_x2", "_x3", "_x4", "_x5", "_x6", "_x7", "_constructModelRequest", "body", "fetchOptions", "Object", "assign", "buildFetchOptions", "method", "makeModelRequest", "_x8", "_x9", "_x0", "_x1", "_x10", "_makeModelRequest", "fetchFn", "fetch", "makeRequest", "_x11", "_x12", "_makeRequest", "handleResponseError", "ok", "handleResponseNotOk", "err", "name", "stack", "_x13", "_x14", "_handleResponseNotOk", "json", "error", "details", "signal", "undefined", "timeout", "controller", "AbortController", "setTimeout", "abort", "addEventListener", "addHelpers", "text", "candidates", "length", "console", "warn", "hadBadFinishReason", "formatBlockErrorMessage", "getText", "promptFeedback", "functionCall", "getFunctionCalls", "functionCalls", "_c", "_d", "textStrings", "content", "parts", "part", "executableCode", "language", "code", "codeExecutionResult", "output", "badFinishReasons", "RECITATION", "SAFETY", "LANGUAGE", "candidate", "finishReason", "includes", "blockReason", "blockReasonMessage", "firstCandidate", "finishMessage", "__await", "v", "__asyncGenerator", "thisArg", "_arguments", "generator", "Symbol", "asyncIterator", "TypeError", "g", "i", "q", "verb", "n", "Promise", "a", "b", "resume", "step", "settle", "r", "value", "resolve", "then", "fulfill", "reject", "f", "shift", "SuppressedError", "suppressed", "responseLineRE", "processStream", "inputStream", "pipeThrough", "TextDecoderStream", "fatal", "responseStream", "getResponseStream", "stream1", "stream2", "tee", "generateResponseSequence", "getResponsePromise", "_x15", "_getResponsePromise", "allResponses", "reader", "<PERSON><PERSON><PERSON><PERSON>", "done", "read", "aggregateResponses", "generateResponseSequence_1", "ReadableStream", "start", "currentText", "pump", "trim", "close", "match", "parsedResponse", "parse", "enqueue", "substring", "catch", "responses", "lastResponse", "aggregatedResponse", "candidateIndex", "index", "citationMetadata", "groundingMetadata", "safetyRatings", "role", "newPart", "keys", "usageMetadata", "generateContentStream", "_x16", "_x17", "_x18", "_x19", "_generateContentStream", "params", "STREAM_GENERATE_CONTENT", "generateContent", "_x20", "_x21", "_x22", "_x23", "_generateContent", "GENERATE_CONTENT", "responseJson", "enhancedResponse", "formatSystemInstruction", "input", "formatNewContent", "request", "newParts", "partOrString", "assignRoleToPartsAndValidateSendMessageRequest", "userContent", "functionContent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hasFunctionContent", "formatCountTokensInput", "modelParams", "formattedGenerateContentRequest", "generationConfig", "safetySettings", "tools", "toolConfig", "systemInstruction", "cachedContent", "contents", "containsGenerateContentRequest", "generateContentRequest", "formatGenerateContentInput", "formattedRequest", "formatEmbedContentInput", "Array", "isArray", "VALID_PART_FIELDS", "VALID_PARTS_PER_ROLE", "user", "function", "system", "validateChatHistory", "history", "prevContent", "currContent", "countFields", "inlineData", "functionResponse", "fileData", "key", "validParts", "isValidResponse", "SILENT_ERROR", "ChatSession", "_requestOptions", "_history", "_sendPromise", "_api<PERSON><PERSON>", "getHistory", "_this", "sendMessage", "_x24", "_this2", "_e", "_f", "newContent", "chatSessionRequestOptions", "finalResult", "result", "responseContent", "blockErrorMessage", "sendMessageStream", "_x25", "_this3", "streamPromise", "_ignored", "streamResult", "countTokens", "_x26", "_x27", "_x28", "_x29", "_countTokens", "singleRequestOptions", "COUNT_TOKENS", "embedContent", "_x30", "_x31", "_x32", "_x33", "_embedContent", "EMBED_CONTENT", "batchEmbedContents", "_x34", "_x35", "_x36", "_x37", "_batchEmbedContents", "requestsWithModel", "requests", "map", "BATCH_EMBED_CONTENTS", "GenerativeModel", "_x38", "_this4", "formattedParams", "generativeModelRequestOptions", "_x39", "_this5", "startChat", "startChatParams", "_x40", "_this6", "_x41", "_this7", "_x42", "_this8", "batchEmbedContentRequest", "GoogleGenerativeAI", "getGenerativeModel", "getGenerativeModelFromCachedContent", "disallowedDuplicates", "modelParamsComp", "startsWith", "replace", "cachedContentComp", "modelParamsFromCache"], "sources": ["C:/Users/<USER>/OneDrive/Bureau/Project PI/devBridge/frontend/node_modules/@google/generative-ai/dist/index.mjs"], "sourcesContent": ["/**\n * Contains the list of OpenAPI data types\n * as defined by https://swagger.io/docs/specification/data-models/data-types/\n * @public\n */\nvar SchemaType;\n(function (SchemaType) {\n    /** String type. */\n    SchemaType[\"STRING\"] = \"string\";\n    /** Number type. */\n    SchemaType[\"NUMBER\"] = \"number\";\n    /** Integer type. */\n    SchemaType[\"INTEGER\"] = \"integer\";\n    /** Boolean type. */\n    SchemaType[\"BOOLEAN\"] = \"boolean\";\n    /** Array type. */\n    SchemaType[\"ARRAY\"] = \"array\";\n    /** Object type. */\n    SchemaType[\"OBJECT\"] = \"object\";\n})(SchemaType || (SchemaType = {}));\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @public\n */\nvar ExecutableCodeLanguage;\n(function (ExecutableCodeLanguage) {\n    ExecutableCodeLanguage[\"LANGUAGE_UNSPECIFIED\"] = \"language_unspecified\";\n    ExecutableCodeLanguage[\"PYTHON\"] = \"python\";\n})(ExecutableCodeLanguage || (ExecutableCodeLanguage = {}));\n/**\n * Possible outcomes of code execution.\n * @public\n */\nvar Outcome;\n(function (Outcome) {\n    /**\n     * Unspecified status. This value should not be used.\n     */\n    Outcome[\"OUTCOME_UNSPECIFIED\"] = \"outcome_unspecified\";\n    /**\n     * Code execution completed successfully.\n     */\n    Outcome[\"OUTCOME_OK\"] = \"outcome_ok\";\n    /**\n     * Code execution finished but with a failure. `stderr` should contain the\n     * reason.\n     */\n    Outcome[\"OUTCOME_FAILED\"] = \"outcome_failed\";\n    /**\n     * Code execution ran for too long, and was cancelled. There may or may not\n     * be a partial output present.\n     */\n    Outcome[\"OUTCOME_DEADLINE_EXCEEDED\"] = \"outcome_deadline_exceeded\";\n})(Outcome || (Outcome = {}));\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Possible roles.\n * @public\n */\nconst POSSIBLE_ROLES = [\"user\", \"model\", \"function\", \"system\"];\n/**\n * Harm categories that would cause prompts or candidates to be blocked.\n * @public\n */\nvar HarmCategory;\n(function (HarmCategory) {\n    HarmCategory[\"HARM_CATEGORY_UNSPECIFIED\"] = \"HARM_CATEGORY_UNSPECIFIED\";\n    HarmCategory[\"HARM_CATEGORY_HATE_SPEECH\"] = \"HARM_CATEGORY_HATE_SPEECH\";\n    HarmCategory[\"HARM_CATEGORY_SEXUALLY_EXPLICIT\"] = \"HARM_CATEGORY_SEXUALLY_EXPLICIT\";\n    HarmCategory[\"HARM_CATEGORY_HARASSMENT\"] = \"HARM_CATEGORY_HARASSMENT\";\n    HarmCategory[\"HARM_CATEGORY_DANGEROUS_CONTENT\"] = \"HARM_CATEGORY_DANGEROUS_CONTENT\";\n    HarmCategory[\"HARM_CATEGORY_CIVIC_INTEGRITY\"] = \"HARM_CATEGORY_CIVIC_INTEGRITY\";\n})(HarmCategory || (HarmCategory = {}));\n/**\n * Threshold above which a prompt or candidate will be blocked.\n * @public\n */\nvar HarmBlockThreshold;\n(function (HarmBlockThreshold) {\n    /** Threshold is unspecified. */\n    HarmBlockThreshold[\"HARM_BLOCK_THRESHOLD_UNSPECIFIED\"] = \"HARM_BLOCK_THRESHOLD_UNSPECIFIED\";\n    /** Content with NEGLIGIBLE will be allowed. */\n    HarmBlockThreshold[\"BLOCK_LOW_AND_ABOVE\"] = \"BLOCK_LOW_AND_ABOVE\";\n    /** Content with NEGLIGIBLE and LOW will be allowed. */\n    HarmBlockThreshold[\"BLOCK_MEDIUM_AND_ABOVE\"] = \"BLOCK_MEDIUM_AND_ABOVE\";\n    /** Content with NEGLIGIBLE, LOW, and MEDIUM will be allowed. */\n    HarmBlockThreshold[\"BLOCK_ONLY_HIGH\"] = \"BLOCK_ONLY_HIGH\";\n    /** All content will be allowed. */\n    HarmBlockThreshold[\"BLOCK_NONE\"] = \"BLOCK_NONE\";\n})(HarmBlockThreshold || (HarmBlockThreshold = {}));\n/**\n * Probability that a prompt or candidate matches a harm category.\n * @public\n */\nvar HarmProbability;\n(function (HarmProbability) {\n    /** Probability is unspecified. */\n    HarmProbability[\"HARM_PROBABILITY_UNSPECIFIED\"] = \"HARM_PROBABILITY_UNSPECIFIED\";\n    /** Content has a negligible chance of being unsafe. */\n    HarmProbability[\"NEGLIGIBLE\"] = \"NEGLIGIBLE\";\n    /** Content has a low chance of being unsafe. */\n    HarmProbability[\"LOW\"] = \"LOW\";\n    /** Content has a medium chance of being unsafe. */\n    HarmProbability[\"MEDIUM\"] = \"MEDIUM\";\n    /** Content has a high chance of being unsafe. */\n    HarmProbability[\"HIGH\"] = \"HIGH\";\n})(HarmProbability || (HarmProbability = {}));\n/**\n * Reason that a prompt was blocked.\n * @public\n */\nvar BlockReason;\n(function (BlockReason) {\n    // A blocked reason was not specified.\n    BlockReason[\"BLOCKED_REASON_UNSPECIFIED\"] = \"BLOCKED_REASON_UNSPECIFIED\";\n    // Content was blocked by safety settings.\n    BlockReason[\"SAFETY\"] = \"SAFETY\";\n    // Content was blocked, but the reason is uncategorized.\n    BlockReason[\"OTHER\"] = \"OTHER\";\n})(BlockReason || (BlockReason = {}));\n/**\n * Reason that a candidate finished.\n * @public\n */\nvar FinishReason;\n(function (FinishReason) {\n    // Default value. This value is unused.\n    FinishReason[\"FINISH_REASON_UNSPECIFIED\"] = \"FINISH_REASON_UNSPECIFIED\";\n    // Natural stop point of the model or provided stop sequence.\n    FinishReason[\"STOP\"] = \"STOP\";\n    // The maximum number of tokens as specified in the request was reached.\n    FinishReason[\"MAX_TOKENS\"] = \"MAX_TOKENS\";\n    // The candidate content was flagged for safety reasons.\n    FinishReason[\"SAFETY\"] = \"SAFETY\";\n    // The candidate content was flagged for recitation reasons.\n    FinishReason[\"RECITATION\"] = \"RECITATION\";\n    // The candidate content was flagged for using an unsupported language.\n    FinishReason[\"LANGUAGE\"] = \"LANGUAGE\";\n    // Token generation stopped because the content contains forbidden terms.\n    FinishReason[\"BLOCKLIST\"] = \"BLOCKLIST\";\n    // Token generation stopped for potentially containing prohibited content.\n    FinishReason[\"PROHIBITED_CONTENT\"] = \"PROHIBITED_CONTENT\";\n    // Token generation stopped because the content potentially contains Sensitive Personally Identifiable Information (SPII).\n    FinishReason[\"SPII\"] = \"SPII\";\n    // The function call generated by the model is invalid.\n    FinishReason[\"MALFORMED_FUNCTION_CALL\"] = \"MALFORMED_FUNCTION_CALL\";\n    // Unknown reason.\n    FinishReason[\"OTHER\"] = \"OTHER\";\n})(FinishReason || (FinishReason = {}));\n/**\n * Task type for embedding content.\n * @public\n */\nvar TaskType;\n(function (TaskType) {\n    TaskType[\"TASK_TYPE_UNSPECIFIED\"] = \"TASK_TYPE_UNSPECIFIED\";\n    TaskType[\"RETRIEVAL_QUERY\"] = \"RETRIEVAL_QUERY\";\n    TaskType[\"RETRIEVAL_DOCUMENT\"] = \"RETRIEVAL_DOCUMENT\";\n    TaskType[\"SEMANTIC_SIMILARITY\"] = \"SEMANTIC_SIMILARITY\";\n    TaskType[\"CLASSIFICATION\"] = \"CLASSIFICATION\";\n    TaskType[\"CLUSTERING\"] = \"CLUSTERING\";\n})(TaskType || (TaskType = {}));\n/**\n * @public\n */\nvar FunctionCallingMode;\n(function (FunctionCallingMode) {\n    // Unspecified function calling mode. This value should not be used.\n    FunctionCallingMode[\"MODE_UNSPECIFIED\"] = \"MODE_UNSPECIFIED\";\n    // Default model behavior, model decides to predict either a function call\n    // or a natural language repspose.\n    FunctionCallingMode[\"AUTO\"] = \"AUTO\";\n    // Model is constrained to always predicting a function call only.\n    // If \"allowed_function_names\" are set, the predicted function call will be\n    // limited to any one of \"allowed_function_names\", else the predicted\n    // function call will be any one of the provided \"function_declarations\".\n    FunctionCallingMode[\"ANY\"] = \"ANY\";\n    // Model will not predict any function call. Model behavior is same as when\n    // not passing any function declarations.\n    FunctionCallingMode[\"NONE\"] = \"NONE\";\n})(FunctionCallingMode || (FunctionCallingMode = {}));\n/**\n * The mode of the predictor to be used in dynamic retrieval.\n * @public\n */\nvar DynamicRetrievalMode;\n(function (DynamicRetrievalMode) {\n    // Unspecified function calling mode. This value should not be used.\n    DynamicRetrievalMode[\"MODE_UNSPECIFIED\"] = \"MODE_UNSPECIFIED\";\n    // Run retrieval only when system decides it is necessary.\n    DynamicRetrievalMode[\"MODE_DYNAMIC\"] = \"MODE_DYNAMIC\";\n})(DynamicRetrievalMode || (DynamicRetrievalMode = {}));\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Basic error type for this SDK.\n * @public\n */\nclass GoogleGenerativeAIError extends Error {\n    constructor(message) {\n        super(`[GoogleGenerativeAI Error]: ${message}`);\n    }\n}\n/**\n * Errors in the contents of a response from the model. This includes parsing\n * errors, or responses including a safety block reason.\n * @public\n */\nclass GoogleGenerativeAIResponseError extends GoogleGenerativeAIError {\n    constructor(message, response) {\n        super(message);\n        this.response = response;\n    }\n}\n/**\n * Error class covering HTTP errors when calling the server. Includes HTTP\n * status, statusText, and optional details, if provided in the server response.\n * @public\n */\nclass GoogleGenerativeAIFetchError extends GoogleGenerativeAIError {\n    constructor(message, status, statusText, errorDetails) {\n        super(message);\n        this.status = status;\n        this.statusText = statusText;\n        this.errorDetails = errorDetails;\n    }\n}\n/**\n * Errors in the contents of a request originating from user input.\n * @public\n */\nclass GoogleGenerativeAIRequestInputError extends GoogleGenerativeAIError {\n}\n/**\n * Error thrown when a request is aborted, either due to a timeout or\n * intentional cancellation by the user.\n * @public\n */\nclass GoogleGenerativeAIAbortError extends GoogleGenerativeAIError {\n}\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst DEFAULT_BASE_URL = \"https://generativelanguage.googleapis.com\";\nconst DEFAULT_API_VERSION = \"v1beta\";\n/**\n * We can't `require` package.json if this runs on web. We will use rollup to\n * swap in the version number here at build time.\n */\nconst PACKAGE_VERSION = \"0.24.1\";\nconst PACKAGE_LOG_HEADER = \"genai-js\";\nvar Task;\n(function (Task) {\n    Task[\"GENERATE_CONTENT\"] = \"generateContent\";\n    Task[\"STREAM_GENERATE_CONTENT\"] = \"streamGenerateContent\";\n    Task[\"COUNT_TOKENS\"] = \"countTokens\";\n    Task[\"EMBED_CONTENT\"] = \"embedContent\";\n    Task[\"BATCH_EMBED_CONTENTS\"] = \"batchEmbedContents\";\n})(Task || (Task = {}));\nclass RequestUrl {\n    constructor(model, task, apiKey, stream, requestOptions) {\n        this.model = model;\n        this.task = task;\n        this.apiKey = apiKey;\n        this.stream = stream;\n        this.requestOptions = requestOptions;\n    }\n    toString() {\n        var _a, _b;\n        const apiVersion = ((_a = this.requestOptions) === null || _a === void 0 ? void 0 : _a.apiVersion) || DEFAULT_API_VERSION;\n        const baseUrl = ((_b = this.requestOptions) === null || _b === void 0 ? void 0 : _b.baseUrl) || DEFAULT_BASE_URL;\n        let url = `${baseUrl}/${apiVersion}/${this.model}:${this.task}`;\n        if (this.stream) {\n            url += \"?alt=sse\";\n        }\n        return url;\n    }\n}\n/**\n * Simple, but may become more complex if we add more versions to log.\n */\nfunction getClientHeaders(requestOptions) {\n    const clientHeaders = [];\n    if (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.apiClient) {\n        clientHeaders.push(requestOptions.apiClient);\n    }\n    clientHeaders.push(`${PACKAGE_LOG_HEADER}/${PACKAGE_VERSION}`);\n    return clientHeaders.join(\" \");\n}\nasync function getHeaders(url) {\n    var _a;\n    const headers = new Headers();\n    headers.append(\"Content-Type\", \"application/json\");\n    headers.append(\"x-goog-api-client\", getClientHeaders(url.requestOptions));\n    headers.append(\"x-goog-api-key\", url.apiKey);\n    let customHeaders = (_a = url.requestOptions) === null || _a === void 0 ? void 0 : _a.customHeaders;\n    if (customHeaders) {\n        if (!(customHeaders instanceof Headers)) {\n            try {\n                customHeaders = new Headers(customHeaders);\n            }\n            catch (e) {\n                throw new GoogleGenerativeAIRequestInputError(`unable to convert customHeaders value ${JSON.stringify(customHeaders)} to Headers: ${e.message}`);\n            }\n        }\n        for (const [headerName, headerValue] of customHeaders.entries()) {\n            if (headerName === \"x-goog-api-key\") {\n                throw new GoogleGenerativeAIRequestInputError(`Cannot set reserved header name ${headerName}`);\n            }\n            else if (headerName === \"x-goog-api-client\") {\n                throw new GoogleGenerativeAIRequestInputError(`Header name ${headerName} can only be set using the apiClient field`);\n            }\n            headers.append(headerName, headerValue);\n        }\n    }\n    return headers;\n}\nasync function constructModelRequest(model, task, apiKey, stream, body, requestOptions) {\n    const url = new RequestUrl(model, task, apiKey, stream, requestOptions);\n    return {\n        url: url.toString(),\n        fetchOptions: Object.assign(Object.assign({}, buildFetchOptions(requestOptions)), { method: \"POST\", headers: await getHeaders(url), body }),\n    };\n}\nasync function makeModelRequest(model, task, apiKey, stream, body, requestOptions = {}, \n// Allows this to be stubbed for tests\nfetchFn = fetch) {\n    const { url, fetchOptions } = await constructModelRequest(model, task, apiKey, stream, body, requestOptions);\n    return makeRequest(url, fetchOptions, fetchFn);\n}\nasync function makeRequest(url, fetchOptions, fetchFn = fetch) {\n    let response;\n    try {\n        response = await fetchFn(url, fetchOptions);\n    }\n    catch (e) {\n        handleResponseError(e, url);\n    }\n    if (!response.ok) {\n        await handleResponseNotOk(response, url);\n    }\n    return response;\n}\nfunction handleResponseError(e, url) {\n    let err = e;\n    if (err.name === \"AbortError\") {\n        err = new GoogleGenerativeAIAbortError(`Request aborted when fetching ${url.toString()}: ${e.message}`);\n        err.stack = e.stack;\n    }\n    else if (!(e instanceof GoogleGenerativeAIFetchError ||\n        e instanceof GoogleGenerativeAIRequestInputError)) {\n        err = new GoogleGenerativeAIError(`Error fetching from ${url.toString()}: ${e.message}`);\n        err.stack = e.stack;\n    }\n    throw err;\n}\nasync function handleResponseNotOk(response, url) {\n    let message = \"\";\n    let errorDetails;\n    try {\n        const json = await response.json();\n        message = json.error.message;\n        if (json.error.details) {\n            message += ` ${JSON.stringify(json.error.details)}`;\n            errorDetails = json.error.details;\n        }\n    }\n    catch (e) {\n        // ignored\n    }\n    throw new GoogleGenerativeAIFetchError(`Error fetching from ${url.toString()}: [${response.status} ${response.statusText}] ${message}`, response.status, response.statusText, errorDetails);\n}\n/**\n * Generates the request options to be passed to the fetch API.\n * @param requestOptions - The user-defined request options.\n * @returns The generated request options.\n */\nfunction buildFetchOptions(requestOptions) {\n    const fetchOptions = {};\n    if ((requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.signal) !== undefined || (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.timeout) >= 0) {\n        const controller = new AbortController();\n        if ((requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.timeout) >= 0) {\n            setTimeout(() => controller.abort(), requestOptions.timeout);\n        }\n        if (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.signal) {\n            requestOptions.signal.addEventListener(\"abort\", () => {\n                controller.abort();\n            });\n        }\n        fetchOptions.signal = controller.signal;\n    }\n    return fetchOptions;\n}\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Adds convenience helper methods to a response object, including stream\n * chunks (as long as each chunk is a complete GenerateContentResponse JSON).\n */\nfunction addHelpers(response) {\n    response.text = () => {\n        if (response.candidates && response.candidates.length > 0) {\n            if (response.candidates.length > 1) {\n                console.warn(`This response had ${response.candidates.length} ` +\n                    `candidates. Returning text from the first candidate only. ` +\n                    `Access response.candidates directly to use the other candidates.`);\n            }\n            if (hadBadFinishReason(response.candidates[0])) {\n                throw new GoogleGenerativeAIResponseError(`${formatBlockErrorMessage(response)}`, response);\n            }\n            return getText(response);\n        }\n        else if (response.promptFeedback) {\n            throw new GoogleGenerativeAIResponseError(`Text not available. ${formatBlockErrorMessage(response)}`, response);\n        }\n        return \"\";\n    };\n    /**\n     * TODO: remove at next major version\n     */\n    response.functionCall = () => {\n        if (response.candidates && response.candidates.length > 0) {\n            if (response.candidates.length > 1) {\n                console.warn(`This response had ${response.candidates.length} ` +\n                    `candidates. Returning function calls from the first candidate only. ` +\n                    `Access response.candidates directly to use the other candidates.`);\n            }\n            if (hadBadFinishReason(response.candidates[0])) {\n                throw new GoogleGenerativeAIResponseError(`${formatBlockErrorMessage(response)}`, response);\n            }\n            console.warn(`response.functionCall() is deprecated. ` +\n                `Use response.functionCalls() instead.`);\n            return getFunctionCalls(response)[0];\n        }\n        else if (response.promptFeedback) {\n            throw new GoogleGenerativeAIResponseError(`Function call not available. ${formatBlockErrorMessage(response)}`, response);\n        }\n        return undefined;\n    };\n    response.functionCalls = () => {\n        if (response.candidates && response.candidates.length > 0) {\n            if (response.candidates.length > 1) {\n                console.warn(`This response had ${response.candidates.length} ` +\n                    `candidates. Returning function calls from the first candidate only. ` +\n                    `Access response.candidates directly to use the other candidates.`);\n            }\n            if (hadBadFinishReason(response.candidates[0])) {\n                throw new GoogleGenerativeAIResponseError(`${formatBlockErrorMessage(response)}`, response);\n            }\n            return getFunctionCalls(response);\n        }\n        else if (response.promptFeedback) {\n            throw new GoogleGenerativeAIResponseError(`Function call not available. ${formatBlockErrorMessage(response)}`, response);\n        }\n        return undefined;\n    };\n    return response;\n}\n/**\n * Returns all text found in all parts of first candidate.\n */\nfunction getText(response) {\n    var _a, _b, _c, _d;\n    const textStrings = [];\n    if ((_b = (_a = response.candidates) === null || _a === void 0 ? void 0 : _a[0].content) === null || _b === void 0 ? void 0 : _b.parts) {\n        for (const part of (_d = (_c = response.candidates) === null || _c === void 0 ? void 0 : _c[0].content) === null || _d === void 0 ? void 0 : _d.parts) {\n            if (part.text) {\n                textStrings.push(part.text);\n            }\n            if (part.executableCode) {\n                textStrings.push(\"\\n```\" +\n                    part.executableCode.language +\n                    \"\\n\" +\n                    part.executableCode.code +\n                    \"\\n```\\n\");\n            }\n            if (part.codeExecutionResult) {\n                textStrings.push(\"\\n```\\n\" + part.codeExecutionResult.output + \"\\n```\\n\");\n            }\n        }\n    }\n    if (textStrings.length > 0) {\n        return textStrings.join(\"\");\n    }\n    else {\n        return \"\";\n    }\n}\n/**\n * Returns functionCall of first candidate.\n */\nfunction getFunctionCalls(response) {\n    var _a, _b, _c, _d;\n    const functionCalls = [];\n    if ((_b = (_a = response.candidates) === null || _a === void 0 ? void 0 : _a[0].content) === null || _b === void 0 ? void 0 : _b.parts) {\n        for (const part of (_d = (_c = response.candidates) === null || _c === void 0 ? void 0 : _c[0].content) === null || _d === void 0 ? void 0 : _d.parts) {\n            if (part.functionCall) {\n                functionCalls.push(part.functionCall);\n            }\n        }\n    }\n    if (functionCalls.length > 0) {\n        return functionCalls;\n    }\n    else {\n        return undefined;\n    }\n}\nconst badFinishReasons = [\n    FinishReason.RECITATION,\n    FinishReason.SAFETY,\n    FinishReason.LANGUAGE,\n];\nfunction hadBadFinishReason(candidate) {\n    return (!!candidate.finishReason &&\n        badFinishReasons.includes(candidate.finishReason));\n}\nfunction formatBlockErrorMessage(response) {\n    var _a, _b, _c;\n    let message = \"\";\n    if ((!response.candidates || response.candidates.length === 0) &&\n        response.promptFeedback) {\n        message += \"Response was blocked\";\n        if ((_a = response.promptFeedback) === null || _a === void 0 ? void 0 : _a.blockReason) {\n            message += ` due to ${response.promptFeedback.blockReason}`;\n        }\n        if ((_b = response.promptFeedback) === null || _b === void 0 ? void 0 : _b.blockReasonMessage) {\n            message += `: ${response.promptFeedback.blockReasonMessage}`;\n        }\n    }\n    else if ((_c = response.candidates) === null || _c === void 0 ? void 0 : _c[0]) {\n        const firstCandidate = response.candidates[0];\n        if (hadBadFinishReason(firstCandidate)) {\n            message += `Candidate was blocked due to ${firstCandidate.finishReason}`;\n            if (firstCandidate.finishMessage) {\n                message += `: ${firstCandidate.finishMessage}`;\n            }\n        }\n    }\n    return message;\n}\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise, SuppressedError, Symbol */\r\n\r\n\r\nfunction __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nfunction __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\ntypeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\r\n    var e = new Error(message);\r\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\r\n};\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst responseLineRE = /^data\\: (.*)(?:\\n\\n|\\r\\r|\\r\\n\\r\\n)/;\n/**\n * Process a response.body stream from the backend and return an\n * iterator that provides one complete GenerateContentResponse at a time\n * and a promise that resolves with a single aggregated\n * GenerateContentResponse.\n *\n * @param response - Response from a fetch call\n */\nfunction processStream(response) {\n    const inputStream = response.body.pipeThrough(new TextDecoderStream(\"utf8\", { fatal: true }));\n    const responseStream = getResponseStream(inputStream);\n    const [stream1, stream2] = responseStream.tee();\n    return {\n        stream: generateResponseSequence(stream1),\n        response: getResponsePromise(stream2),\n    };\n}\nasync function getResponsePromise(stream) {\n    const allResponses = [];\n    const reader = stream.getReader();\n    while (true) {\n        const { done, value } = await reader.read();\n        if (done) {\n            return addHelpers(aggregateResponses(allResponses));\n        }\n        allResponses.push(value);\n    }\n}\nfunction generateResponseSequence(stream) {\n    return __asyncGenerator(this, arguments, function* generateResponseSequence_1() {\n        const reader = stream.getReader();\n        while (true) {\n            const { value, done } = yield __await(reader.read());\n            if (done) {\n                break;\n            }\n            yield yield __await(addHelpers(value));\n        }\n    });\n}\n/**\n * Reads a raw stream from the fetch response and join incomplete\n * chunks, returning a new stream that provides a single complete\n * GenerateContentResponse in each iteration.\n */\nfunction getResponseStream(inputStream) {\n    const reader = inputStream.getReader();\n    const stream = new ReadableStream({\n        start(controller) {\n            let currentText = \"\";\n            return pump();\n            function pump() {\n                return reader\n                    .read()\n                    .then(({ value, done }) => {\n                    if (done) {\n                        if (currentText.trim()) {\n                            controller.error(new GoogleGenerativeAIError(\"Failed to parse stream\"));\n                            return;\n                        }\n                        controller.close();\n                        return;\n                    }\n                    currentText += value;\n                    let match = currentText.match(responseLineRE);\n                    let parsedResponse;\n                    while (match) {\n                        try {\n                            parsedResponse = JSON.parse(match[1]);\n                        }\n                        catch (e) {\n                            controller.error(new GoogleGenerativeAIError(`Error parsing JSON response: \"${match[1]}\"`));\n                            return;\n                        }\n                        controller.enqueue(parsedResponse);\n                        currentText = currentText.substring(match[0].length);\n                        match = currentText.match(responseLineRE);\n                    }\n                    return pump();\n                })\n                    .catch((e) => {\n                    let err = e;\n                    err.stack = e.stack;\n                    if (err.name === \"AbortError\") {\n                        err = new GoogleGenerativeAIAbortError(\"Request aborted when reading from the stream\");\n                    }\n                    else {\n                        err = new GoogleGenerativeAIError(\"Error reading from the stream\");\n                    }\n                    throw err;\n                });\n            }\n        },\n    });\n    return stream;\n}\n/**\n * Aggregates an array of `GenerateContentResponse`s into a single\n * GenerateContentResponse.\n */\nfunction aggregateResponses(responses) {\n    const lastResponse = responses[responses.length - 1];\n    const aggregatedResponse = {\n        promptFeedback: lastResponse === null || lastResponse === void 0 ? void 0 : lastResponse.promptFeedback,\n    };\n    for (const response of responses) {\n        if (response.candidates) {\n            let candidateIndex = 0;\n            for (const candidate of response.candidates) {\n                if (!aggregatedResponse.candidates) {\n                    aggregatedResponse.candidates = [];\n                }\n                if (!aggregatedResponse.candidates[candidateIndex]) {\n                    aggregatedResponse.candidates[candidateIndex] = {\n                        index: candidateIndex,\n                    };\n                }\n                // Keep overwriting, the last one will be final\n                aggregatedResponse.candidates[candidateIndex].citationMetadata =\n                    candidate.citationMetadata;\n                aggregatedResponse.candidates[candidateIndex].groundingMetadata =\n                    candidate.groundingMetadata;\n                aggregatedResponse.candidates[candidateIndex].finishReason =\n                    candidate.finishReason;\n                aggregatedResponse.candidates[candidateIndex].finishMessage =\n                    candidate.finishMessage;\n                aggregatedResponse.candidates[candidateIndex].safetyRatings =\n                    candidate.safetyRatings;\n                /**\n                 * Candidates should always have content and parts, but this handles\n                 * possible malformed responses.\n                 */\n                if (candidate.content && candidate.content.parts) {\n                    if (!aggregatedResponse.candidates[candidateIndex].content) {\n                        aggregatedResponse.candidates[candidateIndex].content = {\n                            role: candidate.content.role || \"user\",\n                            parts: [],\n                        };\n                    }\n                    const newPart = {};\n                    for (const part of candidate.content.parts) {\n                        if (part.text) {\n                            newPart.text = part.text;\n                        }\n                        if (part.functionCall) {\n                            newPart.functionCall = part.functionCall;\n                        }\n                        if (part.executableCode) {\n                            newPart.executableCode = part.executableCode;\n                        }\n                        if (part.codeExecutionResult) {\n                            newPart.codeExecutionResult = part.codeExecutionResult;\n                        }\n                        if (Object.keys(newPart).length === 0) {\n                            newPart.text = \"\";\n                        }\n                        aggregatedResponse.candidates[candidateIndex].content.parts.push(newPart);\n                    }\n                }\n            }\n            candidateIndex++;\n        }\n        if (response.usageMetadata) {\n            aggregatedResponse.usageMetadata = response.usageMetadata;\n        }\n    }\n    return aggregatedResponse;\n}\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nasync function generateContentStream(apiKey, model, params, requestOptions) {\n    const response = await makeModelRequest(model, Task.STREAM_GENERATE_CONTENT, apiKey, \n    /* stream */ true, JSON.stringify(params), requestOptions);\n    return processStream(response);\n}\nasync function generateContent(apiKey, model, params, requestOptions) {\n    const response = await makeModelRequest(model, Task.GENERATE_CONTENT, apiKey, \n    /* stream */ false, JSON.stringify(params), requestOptions);\n    const responseJson = await response.json();\n    const enhancedResponse = addHelpers(responseJson);\n    return {\n        response: enhancedResponse,\n    };\n}\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction formatSystemInstruction(input) {\n    // null or undefined\n    if (input == null) {\n        return undefined;\n    }\n    else if (typeof input === \"string\") {\n        return { role: \"system\", parts: [{ text: input }] };\n    }\n    else if (input.text) {\n        return { role: \"system\", parts: [input] };\n    }\n    else if (input.parts) {\n        if (!input.role) {\n            return { role: \"system\", parts: input.parts };\n        }\n        else {\n            return input;\n        }\n    }\n}\nfunction formatNewContent(request) {\n    let newParts = [];\n    if (typeof request === \"string\") {\n        newParts = [{ text: request }];\n    }\n    else {\n        for (const partOrString of request) {\n            if (typeof partOrString === \"string\") {\n                newParts.push({ text: partOrString });\n            }\n            else {\n                newParts.push(partOrString);\n            }\n        }\n    }\n    return assignRoleToPartsAndValidateSendMessageRequest(newParts);\n}\n/**\n * When multiple Part types (i.e. FunctionResponsePart and TextPart) are\n * passed in a single Part array, we may need to assign different roles to each\n * part. Currently only FunctionResponsePart requires a role other than 'user'.\n * @private\n * @param parts Array of parts to pass to the model\n * @returns Array of content items\n */\nfunction assignRoleToPartsAndValidateSendMessageRequest(parts) {\n    const userContent = { role: \"user\", parts: [] };\n    const functionContent = { role: \"function\", parts: [] };\n    let hasUserContent = false;\n    let hasFunctionContent = false;\n    for (const part of parts) {\n        if (\"functionResponse\" in part) {\n            functionContent.parts.push(part);\n            hasFunctionContent = true;\n        }\n        else {\n            userContent.parts.push(part);\n            hasUserContent = true;\n        }\n    }\n    if (hasUserContent && hasFunctionContent) {\n        throw new GoogleGenerativeAIError(\"Within a single message, FunctionResponse cannot be mixed with other type of part in the request for sending chat message.\");\n    }\n    if (!hasUserContent && !hasFunctionContent) {\n        throw new GoogleGenerativeAIError(\"No content is provided for sending chat message.\");\n    }\n    if (hasUserContent) {\n        return userContent;\n    }\n    return functionContent;\n}\nfunction formatCountTokensInput(params, modelParams) {\n    var _a;\n    let formattedGenerateContentRequest = {\n        model: modelParams === null || modelParams === void 0 ? void 0 : modelParams.model,\n        generationConfig: modelParams === null || modelParams === void 0 ? void 0 : modelParams.generationConfig,\n        safetySettings: modelParams === null || modelParams === void 0 ? void 0 : modelParams.safetySettings,\n        tools: modelParams === null || modelParams === void 0 ? void 0 : modelParams.tools,\n        toolConfig: modelParams === null || modelParams === void 0 ? void 0 : modelParams.toolConfig,\n        systemInstruction: modelParams === null || modelParams === void 0 ? void 0 : modelParams.systemInstruction,\n        cachedContent: (_a = modelParams === null || modelParams === void 0 ? void 0 : modelParams.cachedContent) === null || _a === void 0 ? void 0 : _a.name,\n        contents: [],\n    };\n    const containsGenerateContentRequest = params.generateContentRequest != null;\n    if (params.contents) {\n        if (containsGenerateContentRequest) {\n            throw new GoogleGenerativeAIRequestInputError(\"CountTokensRequest must have one of contents or generateContentRequest, not both.\");\n        }\n        formattedGenerateContentRequest.contents = params.contents;\n    }\n    else if (containsGenerateContentRequest) {\n        formattedGenerateContentRequest = Object.assign(Object.assign({}, formattedGenerateContentRequest), params.generateContentRequest);\n    }\n    else {\n        // Array or string\n        const content = formatNewContent(params);\n        formattedGenerateContentRequest.contents = [content];\n    }\n    return { generateContentRequest: formattedGenerateContentRequest };\n}\nfunction formatGenerateContentInput(params) {\n    let formattedRequest;\n    if (params.contents) {\n        formattedRequest = params;\n    }\n    else {\n        // Array or string\n        const content = formatNewContent(params);\n        formattedRequest = { contents: [content] };\n    }\n    if (params.systemInstruction) {\n        formattedRequest.systemInstruction = formatSystemInstruction(params.systemInstruction);\n    }\n    return formattedRequest;\n}\nfunction formatEmbedContentInput(params) {\n    if (typeof params === \"string\" || Array.isArray(params)) {\n        const content = formatNewContent(params);\n        return { content };\n    }\n    return params;\n}\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// https://ai.google.dev/api/rest/v1beta/Content#part\nconst VALID_PART_FIELDS = [\n    \"text\",\n    \"inlineData\",\n    \"functionCall\",\n    \"functionResponse\",\n    \"executableCode\",\n    \"codeExecutionResult\",\n];\nconst VALID_PARTS_PER_ROLE = {\n    user: [\"text\", \"inlineData\"],\n    function: [\"functionResponse\"],\n    model: [\"text\", \"functionCall\", \"executableCode\", \"codeExecutionResult\"],\n    // System instructions shouldn't be in history anyway.\n    system: [\"text\"],\n};\nfunction validateChatHistory(history) {\n    let prevContent = false;\n    for (const currContent of history) {\n        const { role, parts } = currContent;\n        if (!prevContent && role !== \"user\") {\n            throw new GoogleGenerativeAIError(`First content should be with role 'user', got ${role}`);\n        }\n        if (!POSSIBLE_ROLES.includes(role)) {\n            throw new GoogleGenerativeAIError(`Each item should include role field. Got ${role} but valid roles are: ${JSON.stringify(POSSIBLE_ROLES)}`);\n        }\n        if (!Array.isArray(parts)) {\n            throw new GoogleGenerativeAIError(\"Content should have 'parts' property with an array of Parts\");\n        }\n        if (parts.length === 0) {\n            throw new GoogleGenerativeAIError(\"Each Content should have at least one part\");\n        }\n        const countFields = {\n            text: 0,\n            inlineData: 0,\n            functionCall: 0,\n            functionResponse: 0,\n            fileData: 0,\n            executableCode: 0,\n            codeExecutionResult: 0,\n        };\n        for (const part of parts) {\n            for (const key of VALID_PART_FIELDS) {\n                if (key in part) {\n                    countFields[key] += 1;\n                }\n            }\n        }\n        const validParts = VALID_PARTS_PER_ROLE[role];\n        for (const key of VALID_PART_FIELDS) {\n            if (!validParts.includes(key) && countFields[key] > 0) {\n                throw new GoogleGenerativeAIError(`Content with role '${role}' can't contain '${key}' part`);\n            }\n        }\n        prevContent = true;\n    }\n}\n/**\n * Returns true if the response is valid (could be appended to the history), flase otherwise.\n */\nfunction isValidResponse(response) {\n    var _a;\n    if (response.candidates === undefined || response.candidates.length === 0) {\n        return false;\n    }\n    const content = (_a = response.candidates[0]) === null || _a === void 0 ? void 0 : _a.content;\n    if (content === undefined) {\n        return false;\n    }\n    if (content.parts === undefined || content.parts.length === 0) {\n        return false;\n    }\n    for (const part of content.parts) {\n        if (part === undefined || Object.keys(part).length === 0) {\n            return false;\n        }\n        if (part.text !== undefined && part.text === \"\") {\n            return false;\n        }\n    }\n    return true;\n}\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Do not log a message for this error.\n */\nconst SILENT_ERROR = \"SILENT_ERROR\";\n/**\n * ChatSession class that enables sending chat messages and stores\n * history of sent and received messages so far.\n *\n * @public\n */\nclass ChatSession {\n    constructor(apiKey, model, params, _requestOptions = {}) {\n        this.model = model;\n        this.params = params;\n        this._requestOptions = _requestOptions;\n        this._history = [];\n        this._sendPromise = Promise.resolve();\n        this._apiKey = apiKey;\n        if (params === null || params === void 0 ? void 0 : params.history) {\n            validateChatHistory(params.history);\n            this._history = params.history;\n        }\n    }\n    /**\n     * Gets the chat history so far. Blocked prompts are not added to history.\n     * Blocked candidates are not added to history, nor are the prompts that\n     * generated them.\n     */\n    async getHistory() {\n        await this._sendPromise;\n        return this._history;\n    }\n    /**\n     * Sends a chat message and receives a non-streaming\n     * {@link GenerateContentResult}.\n     *\n     * Fields set in the optional {@link SingleRequestOptions} parameter will\n     * take precedence over the {@link RequestOptions} values provided to\n     * {@link GoogleGenerativeAI.getGenerativeModel }.\n     */\n    async sendMessage(request, requestOptions = {}) {\n        var _a, _b, _c, _d, _e, _f;\n        await this._sendPromise;\n        const newContent = formatNewContent(request);\n        const generateContentRequest = {\n            safetySettings: (_a = this.params) === null || _a === void 0 ? void 0 : _a.safetySettings,\n            generationConfig: (_b = this.params) === null || _b === void 0 ? void 0 : _b.generationConfig,\n            tools: (_c = this.params) === null || _c === void 0 ? void 0 : _c.tools,\n            toolConfig: (_d = this.params) === null || _d === void 0 ? void 0 : _d.toolConfig,\n            systemInstruction: (_e = this.params) === null || _e === void 0 ? void 0 : _e.systemInstruction,\n            cachedContent: (_f = this.params) === null || _f === void 0 ? void 0 : _f.cachedContent,\n            contents: [...this._history, newContent],\n        };\n        const chatSessionRequestOptions = Object.assign(Object.assign({}, this._requestOptions), requestOptions);\n        let finalResult;\n        // Add onto the chain.\n        this._sendPromise = this._sendPromise\n            .then(() => generateContent(this._apiKey, this.model, generateContentRequest, chatSessionRequestOptions))\n            .then((result) => {\n            var _a;\n            if (isValidResponse(result.response)) {\n                this._history.push(newContent);\n                const responseContent = Object.assign({ parts: [], \n                    // Response seems to come back without a role set.\n                    role: \"model\" }, (_a = result.response.candidates) === null || _a === void 0 ? void 0 : _a[0].content);\n                this._history.push(responseContent);\n            }\n            else {\n                const blockErrorMessage = formatBlockErrorMessage(result.response);\n                if (blockErrorMessage) {\n                    console.warn(`sendMessage() was unsuccessful. ${blockErrorMessage}. Inspect response object for details.`);\n                }\n            }\n            finalResult = result;\n        })\n            .catch((e) => {\n            // Resets _sendPromise to avoid subsequent calls failing and throw error.\n            this._sendPromise = Promise.resolve();\n            throw e;\n        });\n        await this._sendPromise;\n        return finalResult;\n    }\n    /**\n     * Sends a chat message and receives the response as a\n     * {@link GenerateContentStreamResult} containing an iterable stream\n     * and a response promise.\n     *\n     * Fields set in the optional {@link SingleRequestOptions} parameter will\n     * take precedence over the {@link RequestOptions} values provided to\n     * {@link GoogleGenerativeAI.getGenerativeModel }.\n     */\n    async sendMessageStream(request, requestOptions = {}) {\n        var _a, _b, _c, _d, _e, _f;\n        await this._sendPromise;\n        const newContent = formatNewContent(request);\n        const generateContentRequest = {\n            safetySettings: (_a = this.params) === null || _a === void 0 ? void 0 : _a.safetySettings,\n            generationConfig: (_b = this.params) === null || _b === void 0 ? void 0 : _b.generationConfig,\n            tools: (_c = this.params) === null || _c === void 0 ? void 0 : _c.tools,\n            toolConfig: (_d = this.params) === null || _d === void 0 ? void 0 : _d.toolConfig,\n            systemInstruction: (_e = this.params) === null || _e === void 0 ? void 0 : _e.systemInstruction,\n            cachedContent: (_f = this.params) === null || _f === void 0 ? void 0 : _f.cachedContent,\n            contents: [...this._history, newContent],\n        };\n        const chatSessionRequestOptions = Object.assign(Object.assign({}, this._requestOptions), requestOptions);\n        const streamPromise = generateContentStream(this._apiKey, this.model, generateContentRequest, chatSessionRequestOptions);\n        // Add onto the chain.\n        this._sendPromise = this._sendPromise\n            .then(() => streamPromise)\n            // This must be handled to avoid unhandled rejection, but jump\n            // to the final catch block with a label to not log this error.\n            .catch((_ignored) => {\n            throw new Error(SILENT_ERROR);\n        })\n            .then((streamResult) => streamResult.response)\n            .then((response) => {\n            if (isValidResponse(response)) {\n                this._history.push(newContent);\n                const responseContent = Object.assign({}, response.candidates[0].content);\n                // Response seems to come back without a role set.\n                if (!responseContent.role) {\n                    responseContent.role = \"model\";\n                }\n                this._history.push(responseContent);\n            }\n            else {\n                const blockErrorMessage = formatBlockErrorMessage(response);\n                if (blockErrorMessage) {\n                    console.warn(`sendMessageStream() was unsuccessful. ${blockErrorMessage}. Inspect response object for details.`);\n                }\n            }\n        })\n            .catch((e) => {\n            // Errors in streamPromise are already catchable by the user as\n            // streamPromise is returned.\n            // Avoid duplicating the error message in logs.\n            if (e.message !== SILENT_ERROR) {\n                // Users do not have access to _sendPromise to catch errors\n                // downstream from streamPromise, so they should not throw.\n                console.error(e);\n            }\n        });\n        return streamPromise;\n    }\n}\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nasync function countTokens(apiKey, model, params, singleRequestOptions) {\n    const response = await makeModelRequest(model, Task.COUNT_TOKENS, apiKey, false, JSON.stringify(params), singleRequestOptions);\n    return response.json();\n}\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nasync function embedContent(apiKey, model, params, requestOptions) {\n    const response = await makeModelRequest(model, Task.EMBED_CONTENT, apiKey, false, JSON.stringify(params), requestOptions);\n    return response.json();\n}\nasync function batchEmbedContents(apiKey, model, params, requestOptions) {\n    const requestsWithModel = params.requests.map((request) => {\n        return Object.assign(Object.assign({}, request), { model });\n    });\n    const response = await makeModelRequest(model, Task.BATCH_EMBED_CONTENTS, apiKey, false, JSON.stringify({ requests: requestsWithModel }), requestOptions);\n    return response.json();\n}\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Class for generative model APIs.\n * @public\n */\nclass GenerativeModel {\n    constructor(apiKey, modelParams, _requestOptions = {}) {\n        this.apiKey = apiKey;\n        this._requestOptions = _requestOptions;\n        if (modelParams.model.includes(\"/\")) {\n            // Models may be named \"models/model-name\" or \"tunedModels/model-name\"\n            this.model = modelParams.model;\n        }\n        else {\n            // If path is not included, assume it's a non-tuned model.\n            this.model = `models/${modelParams.model}`;\n        }\n        this.generationConfig = modelParams.generationConfig || {};\n        this.safetySettings = modelParams.safetySettings || [];\n        this.tools = modelParams.tools;\n        this.toolConfig = modelParams.toolConfig;\n        this.systemInstruction = formatSystemInstruction(modelParams.systemInstruction);\n        this.cachedContent = modelParams.cachedContent;\n    }\n    /**\n     * Makes a single non-streaming call to the model\n     * and returns an object containing a single {@link GenerateContentResponse}.\n     *\n     * Fields set in the optional {@link SingleRequestOptions} parameter will\n     * take precedence over the {@link RequestOptions} values provided to\n     * {@link GoogleGenerativeAI.getGenerativeModel }.\n     */\n    async generateContent(request, requestOptions = {}) {\n        var _a;\n        const formattedParams = formatGenerateContentInput(request);\n        const generativeModelRequestOptions = Object.assign(Object.assign({}, this._requestOptions), requestOptions);\n        return generateContent(this.apiKey, this.model, Object.assign({ generationConfig: this.generationConfig, safetySettings: this.safetySettings, tools: this.tools, toolConfig: this.toolConfig, systemInstruction: this.systemInstruction, cachedContent: (_a = this.cachedContent) === null || _a === void 0 ? void 0 : _a.name }, formattedParams), generativeModelRequestOptions);\n    }\n    /**\n     * Makes a single streaming call to the model and returns an object\n     * containing an iterable stream that iterates over all chunks in the\n     * streaming response as well as a promise that returns the final\n     * aggregated response.\n     *\n     * Fields set in the optional {@link SingleRequestOptions} parameter will\n     * take precedence over the {@link RequestOptions} values provided to\n     * {@link GoogleGenerativeAI.getGenerativeModel }.\n     */\n    async generateContentStream(request, requestOptions = {}) {\n        var _a;\n        const formattedParams = formatGenerateContentInput(request);\n        const generativeModelRequestOptions = Object.assign(Object.assign({}, this._requestOptions), requestOptions);\n        return generateContentStream(this.apiKey, this.model, Object.assign({ generationConfig: this.generationConfig, safetySettings: this.safetySettings, tools: this.tools, toolConfig: this.toolConfig, systemInstruction: this.systemInstruction, cachedContent: (_a = this.cachedContent) === null || _a === void 0 ? void 0 : _a.name }, formattedParams), generativeModelRequestOptions);\n    }\n    /**\n     * Gets a new {@link ChatSession} instance which can be used for\n     * multi-turn chats.\n     */\n    startChat(startChatParams) {\n        var _a;\n        return new ChatSession(this.apiKey, this.model, Object.assign({ generationConfig: this.generationConfig, safetySettings: this.safetySettings, tools: this.tools, toolConfig: this.toolConfig, systemInstruction: this.systemInstruction, cachedContent: (_a = this.cachedContent) === null || _a === void 0 ? void 0 : _a.name }, startChatParams), this._requestOptions);\n    }\n    /**\n     * Counts the tokens in the provided request.\n     *\n     * Fields set in the optional {@link SingleRequestOptions} parameter will\n     * take precedence over the {@link RequestOptions} values provided to\n     * {@link GoogleGenerativeAI.getGenerativeModel }.\n     */\n    async countTokens(request, requestOptions = {}) {\n        const formattedParams = formatCountTokensInput(request, {\n            model: this.model,\n            generationConfig: this.generationConfig,\n            safetySettings: this.safetySettings,\n            tools: this.tools,\n            toolConfig: this.toolConfig,\n            systemInstruction: this.systemInstruction,\n            cachedContent: this.cachedContent,\n        });\n        const generativeModelRequestOptions = Object.assign(Object.assign({}, this._requestOptions), requestOptions);\n        return countTokens(this.apiKey, this.model, formattedParams, generativeModelRequestOptions);\n    }\n    /**\n     * Embeds the provided content.\n     *\n     * Fields set in the optional {@link SingleRequestOptions} parameter will\n     * take precedence over the {@link RequestOptions} values provided to\n     * {@link GoogleGenerativeAI.getGenerativeModel }.\n     */\n    async embedContent(request, requestOptions = {}) {\n        const formattedParams = formatEmbedContentInput(request);\n        const generativeModelRequestOptions = Object.assign(Object.assign({}, this._requestOptions), requestOptions);\n        return embedContent(this.apiKey, this.model, formattedParams, generativeModelRequestOptions);\n    }\n    /**\n     * Embeds an array of {@link EmbedContentRequest}s.\n     *\n     * Fields set in the optional {@link SingleRequestOptions} parameter will\n     * take precedence over the {@link RequestOptions} values provided to\n     * {@link GoogleGenerativeAI.getGenerativeModel }.\n     */\n    async batchEmbedContents(batchEmbedContentRequest, requestOptions = {}) {\n        const generativeModelRequestOptions = Object.assign(Object.assign({}, this._requestOptions), requestOptions);\n        return batchEmbedContents(this.apiKey, this.model, batchEmbedContentRequest, generativeModelRequestOptions);\n    }\n}\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Top-level class for this SDK\n * @public\n */\nclass GoogleGenerativeAI {\n    constructor(apiKey) {\n        this.apiKey = apiKey;\n    }\n    /**\n     * Gets a {@link GenerativeModel} instance for the provided model name.\n     */\n    getGenerativeModel(modelParams, requestOptions) {\n        if (!modelParams.model) {\n            throw new GoogleGenerativeAIError(`Must provide a model name. ` +\n                `Example: genai.getGenerativeModel({ model: 'my-model-name' })`);\n        }\n        return new GenerativeModel(this.apiKey, modelParams, requestOptions);\n    }\n    /**\n     * Creates a {@link GenerativeModel} instance from provided content cache.\n     */\n    getGenerativeModelFromCachedContent(cachedContent, modelParams, requestOptions) {\n        if (!cachedContent.name) {\n            throw new GoogleGenerativeAIRequestInputError(\"Cached content must contain a `name` field.\");\n        }\n        if (!cachedContent.model) {\n            throw new GoogleGenerativeAIRequestInputError(\"Cached content must contain a `model` field.\");\n        }\n        /**\n         * Not checking tools and toolConfig for now as it would require a deep\n         * equality comparison and isn't likely to be a common case.\n         */\n        const disallowedDuplicates = [\"model\", \"systemInstruction\"];\n        for (const key of disallowedDuplicates) {\n            if ((modelParams === null || modelParams === void 0 ? void 0 : modelParams[key]) &&\n                cachedContent[key] &&\n                (modelParams === null || modelParams === void 0 ? void 0 : modelParams[key]) !== cachedContent[key]) {\n                if (key === \"model\") {\n                    const modelParamsComp = modelParams.model.startsWith(\"models/\")\n                        ? modelParams.model.replace(\"models/\", \"\")\n                        : modelParams.model;\n                    const cachedContentComp = cachedContent.model.startsWith(\"models/\")\n                        ? cachedContent.model.replace(\"models/\", \"\")\n                        : cachedContent.model;\n                    if (modelParamsComp === cachedContentComp) {\n                        continue;\n                    }\n                }\n                throw new GoogleGenerativeAIRequestInputError(`Different value for \"${key}\" specified in modelParams` +\n                    ` (${modelParams[key]}) and cachedContent (${cachedContent[key]})`);\n            }\n        }\n        const modelParamsFromCache = Object.assign(Object.assign({}, modelParams), { model: cachedContent.model, tools: cachedContent.tools, toolConfig: cachedContent.toolConfig, systemInstruction: cachedContent.systemInstruction, cachedContent });\n        return new GenerativeModel(this.apiKey, modelParamsFromCache, requestOptions);\n    }\n}\n\nexport { BlockReason, ChatSession, DynamicRetrievalMode, ExecutableCodeLanguage, FinishReason, FunctionCallingMode, GenerativeModel, GoogleGenerativeAI, GoogleGenerativeAIAbortError, GoogleGenerativeAIError, GoogleGenerativeAIFetchError, GoogleGenerativeAIRequestInputError, GoogleGenerativeAIResponseError, HarmBlockThreshold, HarmCategory, HarmProbability, Outcome, POSSIBLE_ROLES, SchemaType, TaskType };\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA,IAAIA,UAAU;AACd,CAAC,UAAUA,UAAU,EAAE;EACnB;EACAA,UAAU,CAAC,QAAQ,CAAC,GAAG,QAAQ;EAC/B;EACAA,UAAU,CAAC,QAAQ,CAAC,GAAG,QAAQ;EAC/B;EACAA,UAAU,CAAC,SAAS,CAAC,GAAG,SAAS;EACjC;EACAA,UAAU,CAAC,SAAS,CAAC,GAAG,SAAS;EACjC;EACAA,UAAU,CAAC,OAAO,CAAC,GAAG,OAAO;EAC7B;EACAA,UAAU,CAAC,QAAQ,CAAC,GAAG,QAAQ;AACnC,CAAC,EAAEA,UAAU,KAAKA,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;;AAEnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,sBAAsB;AAC1B,CAAC,UAAUA,sBAAsB,EAAE;EAC/BA,sBAAsB,CAAC,sBAAsB,CAAC,GAAG,sBAAsB;EACvEA,sBAAsB,CAAC,QAAQ,CAAC,GAAG,QAAQ;AAC/C,CAAC,EAAEA,sBAAsB,KAAKA,sBAAsB,GAAG,CAAC,CAAC,CAAC,CAAC;AAC3D;AACA;AACA;AACA;AACA,IAAIC,OAAO;AACX,CAAC,UAAUA,OAAO,EAAE;EAChB;AACJ;AACA;EACIA,OAAO,CAAC,qBAAqB,CAAC,GAAG,qBAAqB;EACtD;AACJ;AACA;EACIA,OAAO,CAAC,YAAY,CAAC,GAAG,YAAY;EACpC;AACJ;AACA;AACA;EACIA,OAAO,CAAC,gBAAgB,CAAC,GAAG,gBAAgB;EAC5C;AACJ;AACA;AACA;EACIA,OAAO,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;AACtE,CAAC,EAAEA,OAAO,KAAKA,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC;;AAE7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,cAAc,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,QAAQ,CAAC;AAC9D;AACA;AACA;AACA;AACA,IAAIC,YAAY;AAChB,CAAC,UAAUA,YAAY,EAAE;EACrBA,YAAY,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EACvEA,YAAY,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EACvEA,YAAY,CAAC,iCAAiC,CAAC,GAAG,iCAAiC;EACnFA,YAAY,CAAC,0BAA0B,CAAC,GAAG,0BAA0B;EACrEA,YAAY,CAAC,iCAAiC,CAAC,GAAG,iCAAiC;EACnFA,YAAY,CAAC,+BAA+B,CAAC,GAAG,+BAA+B;AACnF,CAAC,EAAEA,YAAY,KAAKA,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC;AACvC;AACA;AACA;AACA;AACA,IAAIC,kBAAkB;AACtB,CAAC,UAAUA,kBAAkB,EAAE;EAC3B;EACAA,kBAAkB,CAAC,kCAAkC,CAAC,GAAG,kCAAkC;EAC3F;EACAA,kBAAkB,CAAC,qBAAqB,CAAC,GAAG,qBAAqB;EACjE;EACAA,kBAAkB,CAAC,wBAAwB,CAAC,GAAG,wBAAwB;EACvE;EACAA,kBAAkB,CAAC,iBAAiB,CAAC,GAAG,iBAAiB;EACzD;EACAA,kBAAkB,CAAC,YAAY,CAAC,GAAG,YAAY;AACnD,CAAC,EAAEA,kBAAkB,KAAKA,kBAAkB,GAAG,CAAC,CAAC,CAAC,CAAC;AACnD;AACA;AACA;AACA;AACA,IAAIC,eAAe;AACnB,CAAC,UAAUA,eAAe,EAAE;EACxB;EACAA,eAAe,CAAC,8BAA8B,CAAC,GAAG,8BAA8B;EAChF;EACAA,eAAe,CAAC,YAAY,CAAC,GAAG,YAAY;EAC5C;EACAA,eAAe,CAAC,KAAK,CAAC,GAAG,KAAK;EAC9B;EACAA,eAAe,CAAC,QAAQ,CAAC,GAAG,QAAQ;EACpC;EACAA,eAAe,CAAC,MAAM,CAAC,GAAG,MAAM;AACpC,CAAC,EAAEA,eAAe,KAAKA,eAAe,GAAG,CAAC,CAAC,CAAC,CAAC;AAC7C;AACA;AACA;AACA;AACA,IAAIC,WAAW;AACf,CAAC,UAAUA,WAAW,EAAE;EACpB;EACAA,WAAW,CAAC,4BAA4B,CAAC,GAAG,4BAA4B;EACxE;EACAA,WAAW,CAAC,QAAQ,CAAC,GAAG,QAAQ;EAChC;EACAA,WAAW,CAAC,OAAO,CAAC,GAAG,OAAO;AAClC,CAAC,EAAEA,WAAW,KAAKA,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC;AACrC;AACA;AACA;AACA;AACA,IAAIC,YAAY;AAChB,CAAC,UAAUA,YAAY,EAAE;EACrB;EACAA,YAAY,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EACvE;EACAA,YAAY,CAAC,MAAM,CAAC,GAAG,MAAM;EAC7B;EACAA,YAAY,CAAC,YAAY,CAAC,GAAG,YAAY;EACzC;EACAA,YAAY,CAAC,QAAQ,CAAC,GAAG,QAAQ;EACjC;EACAA,YAAY,CAAC,YAAY,CAAC,GAAG,YAAY;EACzC;EACAA,YAAY,CAAC,UAAU,CAAC,GAAG,UAAU;EACrC;EACAA,YAAY,CAAC,WAAW,CAAC,GAAG,WAAW;EACvC;EACAA,YAAY,CAAC,oBAAoB,CAAC,GAAG,oBAAoB;EACzD;EACAA,YAAY,CAAC,MAAM,CAAC,GAAG,MAAM;EAC7B;EACAA,YAAY,CAAC,yBAAyB,CAAC,GAAG,yBAAyB;EACnE;EACAA,YAAY,CAAC,OAAO,CAAC,GAAG,OAAO;AACnC,CAAC,EAAEA,YAAY,KAAKA,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC;AACvC;AACA;AACA;AACA;AACA,IAAIC,QAAQ;AACZ,CAAC,UAAUA,QAAQ,EAAE;EACjBA,QAAQ,CAAC,uBAAuB,CAAC,GAAG,uBAAuB;EAC3DA,QAAQ,CAAC,iBAAiB,CAAC,GAAG,iBAAiB;EAC/CA,QAAQ,CAAC,oBAAoB,CAAC,GAAG,oBAAoB;EACrDA,QAAQ,CAAC,qBAAqB,CAAC,GAAG,qBAAqB;EACvDA,QAAQ,CAAC,gBAAgB,CAAC,GAAG,gBAAgB;EAC7CA,QAAQ,CAAC,YAAY,CAAC,GAAG,YAAY;AACzC,CAAC,EAAEA,QAAQ,KAAKA,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/B;AACA;AACA;AACA,IAAIC,mBAAmB;AACvB,CAAC,UAAUA,mBAAmB,EAAE;EAC5B;EACAA,mBAAmB,CAAC,kBAAkB,CAAC,GAAG,kBAAkB;EAC5D;EACA;EACAA,mBAAmB,CAAC,MAAM,CAAC,GAAG,MAAM;EACpC;EACA;EACA;EACA;EACAA,mBAAmB,CAAC,KAAK,CAAC,GAAG,KAAK;EAClC;EACA;EACAA,mBAAmB,CAAC,MAAM,CAAC,GAAG,MAAM;AACxC,CAAC,EAAEA,mBAAmB,KAAKA,mBAAmB,GAAG,CAAC,CAAC,CAAC,CAAC;AACrD;AACA;AACA;AACA;AACA,IAAIC,oBAAoB;AACxB,CAAC,UAAUA,oBAAoB,EAAE;EAC7B;EACAA,oBAAoB,CAAC,kBAAkB,CAAC,GAAG,kBAAkB;EAC7D;EACAA,oBAAoB,CAAC,cAAc,CAAC,GAAG,cAAc;AACzD,CAAC,EAAEA,oBAAoB,KAAKA,oBAAoB,GAAG,CAAC,CAAC,CAAC,CAAC;;AAEvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,uBAAuB,SAASC,KAAK,CAAC;EACxCC,WAAWA,CAACC,OAAO,EAAE;IACjB,KAAK,CAAE,+BAA8BA,OAAQ,EAAC,CAAC;EACnD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,+BAA+B,SAASJ,uBAAuB,CAAC;EAClEE,WAAWA,CAACC,OAAO,EAAEE,QAAQ,EAAE;IAC3B,KAAK,CAACF,OAAO,CAAC;IACd,IAAI,CAACE,QAAQ,GAAGA,QAAQ;EAC5B;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,4BAA4B,SAASN,uBAAuB,CAAC;EAC/DE,WAAWA,CAACC,OAAO,EAAEI,MAAM,EAAEC,UAAU,EAAEC,YAAY,EAAE;IACnD,KAAK,CAACN,OAAO,CAAC;IACd,IAAI,CAACI,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,YAAY,GAAGA,YAAY;EACpC;AACJ;AACA;AACA;AACA;AACA;AACA,MAAMC,mCAAmC,SAASV,uBAAuB,CAAC;AAE1E;AACA;AACA;AACA;AACA;AACA,MAAMW,4BAA4B,SAASX,uBAAuB,CAAC;;AAGnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMY,gBAAgB,GAAG,2CAA2C;AACpE,MAAMC,mBAAmB,GAAG,QAAQ;AACpC;AACA;AACA;AACA;AACA,MAAMC,eAAe,GAAG,QAAQ;AAChC,MAAMC,kBAAkB,GAAG,UAAU;AACrC,IAAIC,IAAI;AACR,CAAC,UAAUA,IAAI,EAAE;EACbA,IAAI,CAAC,kBAAkB,CAAC,GAAG,iBAAiB;EAC5CA,IAAI,CAAC,yBAAyB,CAAC,GAAG,uBAAuB;EACzDA,IAAI,CAAC,cAAc,CAAC,GAAG,aAAa;EACpCA,IAAI,CAAC,eAAe,CAAC,GAAG,cAAc;EACtCA,IAAI,CAAC,sBAAsB,CAAC,GAAG,oBAAoB;AACvD,CAAC,EAAEA,IAAI,KAAKA,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;AACvB,MAAMC,UAAU,CAAC;EACbf,WAAWA,CAACgB,KAAK,EAAEC,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAEC,cAAc,EAAE;IACrD,IAAI,CAACJ,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,cAAc,GAAGA,cAAc;EACxC;EACAC,QAAQA,CAAA,EAAG;IACP,IAAIC,EAAE,EAAEC,EAAE;IACV,MAAMC,UAAU,GAAG,CAAC,CAACF,EAAE,GAAG,IAAI,CAACF,cAAc,MAAM,IAAI,IAAIE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,UAAU,KAAKb,mBAAmB;IACzH,MAAMc,OAAO,GAAG,CAAC,CAACF,EAAE,GAAG,IAAI,CAACH,cAAc,MAAM,IAAI,IAAIG,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,OAAO,KAAKf,gBAAgB;IAChH,IAAIgB,GAAG,GAAI,GAAED,OAAQ,IAAGD,UAAW,IAAG,IAAI,CAACR,KAAM,IAAG,IAAI,CAACC,IAAK,EAAC;IAC/D,IAAI,IAAI,CAACE,MAAM,EAAE;MACbO,GAAG,IAAI,UAAU;IACrB;IACA,OAAOA,GAAG;EACd;AACJ;AACA;AACA;AACA;AACA,SAASC,gBAAgBA,CAACP,cAAc,EAAE;EACtC,MAAMQ,aAAa,GAAG,EAAE;EACxB,IAAIR,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACS,SAAS,EAAE;IAC1FD,aAAa,CAACE,IAAI,CAACV,cAAc,CAACS,SAAS,CAAC;EAChD;EACAD,aAAa,CAACE,IAAI,CAAE,GAAEjB,kBAAmB,IAAGD,eAAgB,EAAC,CAAC;EAC9D,OAAOgB,aAAa,CAACG,IAAI,CAAC,GAAG,CAAC;AAClC;AAAC,SACcC,UAAUA,CAAAC,EAAA;EAAA,OAAAC,WAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAF,YAAA;EAAAA,WAAA,GAAAG,iBAAA,CAAzB,WAA0BX,GAAG,EAAE;IAC3B,IAAIJ,EAAE;IACN,MAAMgB,OAAO,GAAG,IAAIC,OAAO,CAAC,CAAC;IAC7BD,OAAO,CAACE,MAAM,CAAC,cAAc,EAAE,kBAAkB,CAAC;IAClDF,OAAO,CAACE,MAAM,CAAC,mBAAmB,EAAEb,gBAAgB,CAACD,GAAG,CAACN,cAAc,CAAC,CAAC;IACzEkB,OAAO,CAACE,MAAM,CAAC,gBAAgB,EAAEd,GAAG,CAACR,MAAM,CAAC;IAC5C,IAAIuB,aAAa,GAAG,CAACnB,EAAE,GAAGI,GAAG,CAACN,cAAc,MAAM,IAAI,IAAIE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACmB,aAAa;IACnG,IAAIA,aAAa,EAAE;MACf,IAAI,EAAEA,aAAa,YAAYF,OAAO,CAAC,EAAE;QACrC,IAAI;UACAE,aAAa,GAAG,IAAIF,OAAO,CAACE,aAAa,CAAC;QAC9C,CAAC,CACD,OAAOC,CAAC,EAAE;UACN,MAAM,IAAIlC,mCAAmC,CAAE,yCAAwCmC,IAAI,CAACC,SAAS,CAACH,aAAa,CAAE,gBAAeC,CAAC,CAACzC,OAAQ,EAAC,CAAC;QACpJ;MACJ;MACA,KAAK,MAAM,CAAC4C,UAAU,EAAEC,WAAW,CAAC,IAAIL,aAAa,CAACM,OAAO,CAAC,CAAC,EAAE;QAC7D,IAAIF,UAAU,KAAK,gBAAgB,EAAE;UACjC,MAAM,IAAIrC,mCAAmC,CAAE,mCAAkCqC,UAAW,EAAC,CAAC;QAClG,CAAC,MACI,IAAIA,UAAU,KAAK,mBAAmB,EAAE;UACzC,MAAM,IAAIrC,mCAAmC,CAAE,eAAcqC,UAAW,4CAA2C,CAAC;QACxH;QACAP,OAAO,CAACE,MAAM,CAACK,UAAU,EAAEC,WAAW,CAAC;MAC3C;IACJ;IACA,OAAOR,OAAO;EAClB,CAAC;EAAA,OAAAJ,WAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SACcY,qBAAqBA,CAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;EAAA,OAAAC,sBAAA,CAAApB,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAmB,uBAAA;EAAAA,sBAAA,GAAAlB,iBAAA,CAApC,WAAqCrB,KAAK,EAAEC,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAEqC,IAAI,EAAEpC,cAAc,EAAE;IACpF,MAAMM,GAAG,GAAG,IAAIX,UAAU,CAACC,KAAK,EAAEC,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAEC,cAAc,CAAC;IACvE,OAAO;MACHM,GAAG,EAAEA,GAAG,CAACL,QAAQ,CAAC,CAAC;MACnBoC,YAAY,EAAEC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEC,iBAAiB,CAACxC,cAAc,CAAC,CAAC,EAAE;QAAEyC,MAAM,EAAE,MAAM;QAAEvB,OAAO,QAAQN,UAAU,CAACN,GAAG,CAAC;QAAE8B;MAAK,CAAC;IAC9I,CAAC;EACL,CAAC;EAAA,OAAAD,sBAAA,CAAApB,KAAA,OAAAC,SAAA;AAAA;AAAA,SACc0B,gBAAgBA,CAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,IAAA;EAAA,OAAAC,iBAAA,CAAAjC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAgC,kBAAA;EAAAA,iBAAA,GAAA/B,iBAAA,CAA/B,WAAgCrB,KAAK,EAAEC,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAEqC,IAAI,EAAEpC,cAAc,GAAG,CAAC,CAAC;EACtF;EACAiD,OAAO,GAAGC,KAAK,EAAE;IACb,MAAM;MAAE5C,GAAG;MAAE+B;IAAa,CAAC,SAAST,qBAAqB,CAAChC,KAAK,EAAEC,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAEqC,IAAI,EAAEpC,cAAc,CAAC;IAC5G,OAAOmD,WAAW,CAAC7C,GAAG,EAAE+B,YAAY,EAAEY,OAAO,CAAC;EAClD,CAAC;EAAA,OAAAD,iBAAA,CAAAjC,KAAA,OAAAC,SAAA;AAAA;AAAA,SACcmC,WAAWA,CAAAC,IAAA,EAAAC,IAAA;EAAA,OAAAC,YAAA,CAAAvC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAsC,aAAA;EAAAA,YAAA,GAAArC,iBAAA,CAA1B,WAA2BX,GAAG,EAAE+B,YAAY,EAAEY,OAAO,GAAGC,KAAK,EAAE;IAC3D,IAAInE,QAAQ;IACZ,IAAI;MACAA,QAAQ,SAASkE,OAAO,CAAC3C,GAAG,EAAE+B,YAAY,CAAC;IAC/C,CAAC,CACD,OAAOf,CAAC,EAAE;MACNiC,mBAAmB,CAACjC,CAAC,EAAEhB,GAAG,CAAC;IAC/B;IACA,IAAI,CAACvB,QAAQ,CAACyE,EAAE,EAAE;MACd,MAAMC,mBAAmB,CAAC1E,QAAQ,EAAEuB,GAAG,CAAC;IAC5C;IACA,OAAOvB,QAAQ;EACnB,CAAC;EAAA,OAAAuE,YAAA,CAAAvC,KAAA,OAAAC,SAAA;AAAA;AACD,SAASuC,mBAAmBA,CAACjC,CAAC,EAAEhB,GAAG,EAAE;EACjC,IAAIoD,GAAG,GAAGpC,CAAC;EACX,IAAIoC,GAAG,CAACC,IAAI,KAAK,YAAY,EAAE;IAC3BD,GAAG,GAAG,IAAIrE,4BAA4B,CAAE,iCAAgCiB,GAAG,CAACL,QAAQ,CAAC,CAAE,KAAIqB,CAAC,CAACzC,OAAQ,EAAC,CAAC;IACvG6E,GAAG,CAACE,KAAK,GAAGtC,CAAC,CAACsC,KAAK;EACvB,CAAC,MACI,IAAI,EAAEtC,CAAC,YAAYtC,4BAA4B,IAChDsC,CAAC,YAAYlC,mCAAmC,CAAC,EAAE;IACnDsE,GAAG,GAAG,IAAIhF,uBAAuB,CAAE,uBAAsB4B,GAAG,CAACL,QAAQ,CAAC,CAAE,KAAIqB,CAAC,CAACzC,OAAQ,EAAC,CAAC;IACxF6E,GAAG,CAACE,KAAK,GAAGtC,CAAC,CAACsC,KAAK;EACvB;EACA,MAAMF,GAAG;AACb;AAAC,SACcD,mBAAmBA,CAAAI,IAAA,EAAAC,IAAA;EAAA,OAAAC,oBAAA,CAAAhD,KAAA,OAAAC,SAAA;AAAA;AAgBlC;AACA;AACA;AACA;AACA;AAJA,SAAA+C,qBAAA;EAAAA,oBAAA,GAAA9C,iBAAA,CAhBA,WAAmClC,QAAQ,EAAEuB,GAAG,EAAE;IAC9C,IAAIzB,OAAO,GAAG,EAAE;IAChB,IAAIM,YAAY;IAChB,IAAI;MACA,MAAM6E,IAAI,SAASjF,QAAQ,CAACiF,IAAI,CAAC,CAAC;MAClCnF,OAAO,GAAGmF,IAAI,CAACC,KAAK,CAACpF,OAAO;MAC5B,IAAImF,IAAI,CAACC,KAAK,CAACC,OAAO,EAAE;QACpBrF,OAAO,IAAK,IAAG0C,IAAI,CAACC,SAAS,CAACwC,IAAI,CAACC,KAAK,CAACC,OAAO,CAAE,EAAC;QACnD/E,YAAY,GAAG6E,IAAI,CAACC,KAAK,CAACC,OAAO;MACrC;IACJ,CAAC,CACD,OAAO5C,CAAC,EAAE;MACN;IAAA;IAEJ,MAAM,IAAItC,4BAA4B,CAAE,uBAAsBsB,GAAG,CAACL,QAAQ,CAAC,CAAE,MAAKlB,QAAQ,CAACE,MAAO,IAAGF,QAAQ,CAACG,UAAW,KAAIL,OAAQ,EAAC,EAAEE,QAAQ,CAACE,MAAM,EAAEF,QAAQ,CAACG,UAAU,EAAEC,YAAY,CAAC;EAC/L,CAAC;EAAA,OAAA4E,oBAAA,CAAAhD,KAAA,OAAAC,SAAA;AAAA;AAMD,SAASwB,iBAAiBA,CAACxC,cAAc,EAAE;EACvC,MAAMqC,YAAY,GAAG,CAAC,CAAC;EACvB,IAAI,CAACrC,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACmE,MAAM,MAAMC,SAAS,IAAI,CAACpE,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACqE,OAAO,KAAK,CAAC,EAAE;IACxM,MAAMC,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;IACxC,IAAI,CAACvE,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACqE,OAAO,KAAK,CAAC,EAAE;MAC/FG,UAAU,CAAC,MAAMF,UAAU,CAACG,KAAK,CAAC,CAAC,EAAEzE,cAAc,CAACqE,OAAO,CAAC;IAChE;IACA,IAAIrE,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACmE,MAAM,EAAE;MACvFnE,cAAc,CAACmE,MAAM,CAACO,gBAAgB,CAAC,OAAO,EAAE,MAAM;QAClDJ,UAAU,CAACG,KAAK,CAAC,CAAC;MACtB,CAAC,CAAC;IACN;IACApC,YAAY,CAAC8B,MAAM,GAAGG,UAAU,CAACH,MAAM;EAC3C;EACA,OAAO9B,YAAY;AACvB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASsC,UAAUA,CAAC5F,QAAQ,EAAE;EAC1BA,QAAQ,CAAC6F,IAAI,GAAG,MAAM;IAClB,IAAI7F,QAAQ,CAAC8F,UAAU,IAAI9F,QAAQ,CAAC8F,UAAU,CAACC,MAAM,GAAG,CAAC,EAAE;MACvD,IAAI/F,QAAQ,CAAC8F,UAAU,CAACC,MAAM,GAAG,CAAC,EAAE;QAChCC,OAAO,CAACC,IAAI,CAAE,qBAAoBjG,QAAQ,CAAC8F,UAAU,CAACC,MAAO,GAAE,GAC1D,4DAA2D,GAC3D,kEAAiE,CAAC;MAC3E;MACA,IAAIG,kBAAkB,CAAClG,QAAQ,CAAC8F,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE;QAC5C,MAAM,IAAI/F,+BAA+B,CAAE,GAAEoG,uBAAuB,CAACnG,QAAQ,CAAE,EAAC,EAAEA,QAAQ,CAAC;MAC/F;MACA,OAAOoG,OAAO,CAACpG,QAAQ,CAAC;IAC5B,CAAC,MACI,IAAIA,QAAQ,CAACqG,cAAc,EAAE;MAC9B,MAAM,IAAItG,+BAA+B,CAAE,uBAAsBoG,uBAAuB,CAACnG,QAAQ,CAAE,EAAC,EAAEA,QAAQ,CAAC;IACnH;IACA,OAAO,EAAE;EACb,CAAC;EACD;AACJ;AACA;EACIA,QAAQ,CAACsG,YAAY,GAAG,MAAM;IAC1B,IAAItG,QAAQ,CAAC8F,UAAU,IAAI9F,QAAQ,CAAC8F,UAAU,CAACC,MAAM,GAAG,CAAC,EAAE;MACvD,IAAI/F,QAAQ,CAAC8F,UAAU,CAACC,MAAM,GAAG,CAAC,EAAE;QAChCC,OAAO,CAACC,IAAI,CAAE,qBAAoBjG,QAAQ,CAAC8F,UAAU,CAACC,MAAO,GAAE,GAC1D,sEAAqE,GACrE,kEAAiE,CAAC;MAC3E;MACA,IAAIG,kBAAkB,CAAClG,QAAQ,CAAC8F,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE;QAC5C,MAAM,IAAI/F,+BAA+B,CAAE,GAAEoG,uBAAuB,CAACnG,QAAQ,CAAE,EAAC,EAAEA,QAAQ,CAAC;MAC/F;MACAgG,OAAO,CAACC,IAAI,CAAE,yCAAwC,GACjD,uCAAsC,CAAC;MAC5C,OAAOM,gBAAgB,CAACvG,QAAQ,CAAC,CAAC,CAAC,CAAC;IACxC,CAAC,MACI,IAAIA,QAAQ,CAACqG,cAAc,EAAE;MAC9B,MAAM,IAAItG,+BAA+B,CAAE,gCAA+BoG,uBAAuB,CAACnG,QAAQ,CAAE,EAAC,EAAEA,QAAQ,CAAC;IAC5H;IACA,OAAOqF,SAAS;EACpB,CAAC;EACDrF,QAAQ,CAACwG,aAAa,GAAG,MAAM;IAC3B,IAAIxG,QAAQ,CAAC8F,UAAU,IAAI9F,QAAQ,CAAC8F,UAAU,CAACC,MAAM,GAAG,CAAC,EAAE;MACvD,IAAI/F,QAAQ,CAAC8F,UAAU,CAACC,MAAM,GAAG,CAAC,EAAE;QAChCC,OAAO,CAACC,IAAI,CAAE,qBAAoBjG,QAAQ,CAAC8F,UAAU,CAACC,MAAO,GAAE,GAC1D,sEAAqE,GACrE,kEAAiE,CAAC;MAC3E;MACA,IAAIG,kBAAkB,CAAClG,QAAQ,CAAC8F,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE;QAC5C,MAAM,IAAI/F,+BAA+B,CAAE,GAAEoG,uBAAuB,CAACnG,QAAQ,CAAE,EAAC,EAAEA,QAAQ,CAAC;MAC/F;MACA,OAAOuG,gBAAgB,CAACvG,QAAQ,CAAC;IACrC,CAAC,MACI,IAAIA,QAAQ,CAACqG,cAAc,EAAE;MAC9B,MAAM,IAAItG,+BAA+B,CAAE,gCAA+BoG,uBAAuB,CAACnG,QAAQ,CAAE,EAAC,EAAEA,QAAQ,CAAC;IAC5H;IACA,OAAOqF,SAAS;EACpB,CAAC;EACD,OAAOrF,QAAQ;AACnB;AACA;AACA;AACA;AACA,SAASoG,OAAOA,CAACpG,QAAQ,EAAE;EACvB,IAAImB,EAAE,EAAEC,EAAE,EAAEqF,EAAE,EAAEC,EAAE;EAClB,MAAMC,WAAW,GAAG,EAAE;EACtB,IAAI,CAACvF,EAAE,GAAG,CAACD,EAAE,GAAGnB,QAAQ,CAAC8F,UAAU,MAAM,IAAI,IAAI3E,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC,CAAC,CAAC,CAACyF,OAAO,MAAM,IAAI,IAAIxF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACyF,KAAK,EAAE;IACpI,KAAK,MAAMC,IAAI,IAAI,CAACJ,EAAE,GAAG,CAACD,EAAE,GAAGzG,QAAQ,CAAC8F,UAAU,MAAM,IAAI,IAAIW,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC,CAAC,CAAC,CAACG,OAAO,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,KAAK,EAAE;MACnJ,IAAIC,IAAI,CAACjB,IAAI,EAAE;QACXc,WAAW,CAAChF,IAAI,CAACmF,IAAI,CAACjB,IAAI,CAAC;MAC/B;MACA,IAAIiB,IAAI,CAACC,cAAc,EAAE;QACrBJ,WAAW,CAAChF,IAAI,CAAC,OAAO,GACpBmF,IAAI,CAACC,cAAc,CAACC,QAAQ,GAC5B,IAAI,GACJF,IAAI,CAACC,cAAc,CAACE,IAAI,GACxB,SAAS,CAAC;MAClB;MACA,IAAIH,IAAI,CAACI,mBAAmB,EAAE;QAC1BP,WAAW,CAAChF,IAAI,CAAC,SAAS,GAAGmF,IAAI,CAACI,mBAAmB,CAACC,MAAM,GAAG,SAAS,CAAC;MAC7E;IACJ;EACJ;EACA,IAAIR,WAAW,CAACZ,MAAM,GAAG,CAAC,EAAE;IACxB,OAAOY,WAAW,CAAC/E,IAAI,CAAC,EAAE,CAAC;EAC/B,CAAC,MACI;IACD,OAAO,EAAE;EACb;AACJ;AACA;AACA;AACA;AACA,SAAS2E,gBAAgBA,CAACvG,QAAQ,EAAE;EAChC,IAAImB,EAAE,EAAEC,EAAE,EAAEqF,EAAE,EAAEC,EAAE;EAClB,MAAMF,aAAa,GAAG,EAAE;EACxB,IAAI,CAACpF,EAAE,GAAG,CAACD,EAAE,GAAGnB,QAAQ,CAAC8F,UAAU,MAAM,IAAI,IAAI3E,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC,CAAC,CAAC,CAACyF,OAAO,MAAM,IAAI,IAAIxF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACyF,KAAK,EAAE;IACpI,KAAK,MAAMC,IAAI,IAAI,CAACJ,EAAE,GAAG,CAACD,EAAE,GAAGzG,QAAQ,CAAC8F,UAAU,MAAM,IAAI,IAAIW,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC,CAAC,CAAC,CAACG,OAAO,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,KAAK,EAAE;MACnJ,IAAIC,IAAI,CAACR,YAAY,EAAE;QACnBE,aAAa,CAAC7E,IAAI,CAACmF,IAAI,CAACR,YAAY,CAAC;MACzC;IACJ;EACJ;EACA,IAAIE,aAAa,CAACT,MAAM,GAAG,CAAC,EAAE;IAC1B,OAAOS,aAAa;EACxB,CAAC,MACI;IACD,OAAOnB,SAAS;EACpB;AACJ;AACA,MAAM+B,gBAAgB,GAAG,CACrB7H,YAAY,CAAC8H,UAAU,EACvB9H,YAAY,CAAC+H,MAAM,EACnB/H,YAAY,CAACgI,QAAQ,CACxB;AACD,SAASrB,kBAAkBA,CAACsB,SAAS,EAAE;EACnC,OAAQ,CAAC,CAACA,SAAS,CAACC,YAAY,IAC5BL,gBAAgB,CAACM,QAAQ,CAACF,SAAS,CAACC,YAAY,CAAC;AACzD;AACA,SAAStB,uBAAuBA,CAACnG,QAAQ,EAAE;EACvC,IAAImB,EAAE,EAAEC,EAAE,EAAEqF,EAAE;EACd,IAAI3G,OAAO,GAAG,EAAE;EAChB,IAAI,CAAC,CAACE,QAAQ,CAAC8F,UAAU,IAAI9F,QAAQ,CAAC8F,UAAU,CAACC,MAAM,KAAK,CAAC,KACzD/F,QAAQ,CAACqG,cAAc,EAAE;IACzBvG,OAAO,IAAI,sBAAsB;IACjC,IAAI,CAACqB,EAAE,GAAGnB,QAAQ,CAACqG,cAAc,MAAM,IAAI,IAAIlF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACwG,WAAW,EAAE;MACpF7H,OAAO,IAAK,WAAUE,QAAQ,CAACqG,cAAc,CAACsB,WAAY,EAAC;IAC/D;IACA,IAAI,CAACvG,EAAE,GAAGpB,QAAQ,CAACqG,cAAc,MAAM,IAAI,IAAIjF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACwG,kBAAkB,EAAE;MAC3F9H,OAAO,IAAK,KAAIE,QAAQ,CAACqG,cAAc,CAACuB,kBAAmB,EAAC;IAChE;EACJ,CAAC,MACI,IAAI,CAACnB,EAAE,GAAGzG,QAAQ,CAAC8F,UAAU,MAAM,IAAI,IAAIW,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC,CAAC,CAAC,EAAE;IAC5E,MAAMoB,cAAc,GAAG7H,QAAQ,CAAC8F,UAAU,CAAC,CAAC,CAAC;IAC7C,IAAII,kBAAkB,CAAC2B,cAAc,CAAC,EAAE;MACpC/H,OAAO,IAAK,gCAA+B+H,cAAc,CAACJ,YAAa,EAAC;MACxE,IAAII,cAAc,CAACC,aAAa,EAAE;QAC9BhI,OAAO,IAAK,KAAI+H,cAAc,CAACC,aAAc,EAAC;MAClD;IACJ;EACJ;EACA,OAAOhI,OAAO;AAClB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA,SAASiI,OAAOA,CAACC,CAAC,EAAE;EAChB,OAAO,IAAI,YAAYD,OAAO,IAAI,IAAI,CAACC,CAAC,GAAGA,CAAC,EAAE,IAAI,IAAI,IAAID,OAAO,CAACC,CAAC,CAAC;AACxE;AAEA,SAASC,gBAAgBA,CAACC,OAAO,EAAEC,UAAU,EAAEC,SAAS,EAAE;EACtD,IAAI,CAACC,MAAM,CAACC,aAAa,EAAE,MAAM,IAAIC,SAAS,CAAC,sCAAsC,CAAC;EACtF,IAAIC,CAAC,GAAGJ,SAAS,CAACpG,KAAK,CAACkG,OAAO,EAAEC,UAAU,IAAI,EAAE,CAAC;IAAEM,CAAC;IAAEC,CAAC,GAAG,EAAE;EAC7D,OAAOD,CAAC,GAAG,CAAC,CAAC,EAAEE,IAAI,CAAC,MAAM,CAAC,EAAEA,IAAI,CAAC,OAAO,CAAC,EAAEA,IAAI,CAAC,QAAQ,CAAC,EAAEF,CAAC,CAACJ,MAAM,CAACC,aAAa,CAAC,GAAG,YAAY;IAAE,OAAO,IAAI;EAAE,CAAC,EAAEG,CAAC;EACrH,SAASE,IAAIA,CAACC,CAAC,EAAE;IAAE,IAAIJ,CAAC,CAACI,CAAC,CAAC,EAAEH,CAAC,CAACG,CAAC,CAAC,GAAG,UAAUZ,CAAC,EAAE;MAAE,OAAO,IAAIa,OAAO,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;QAAEL,CAAC,CAAC/G,IAAI,CAAC,CAACiH,CAAC,EAAEZ,CAAC,EAAEc,CAAC,EAAEC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIC,MAAM,CAACJ,CAAC,EAAEZ,CAAC,CAAC;MAAE,CAAC,CAAC;IAAE,CAAC;EAAE;EACzI,SAASgB,MAAMA,CAACJ,CAAC,EAAEZ,CAAC,EAAE;IAAE,IAAI;MAAEiB,IAAI,CAACT,CAAC,CAACI,CAAC,CAAC,CAACZ,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,OAAOzF,CAAC,EAAE;MAAE2G,MAAM,CAACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEnG,CAAC,CAAC;IAAE;EAAE;EACjF,SAAS0G,IAAIA,CAACE,CAAC,EAAE;IAAEA,CAAC,CAACC,KAAK,YAAYrB,OAAO,GAAGc,OAAO,CAACQ,OAAO,CAACF,CAAC,CAACC,KAAK,CAACpB,CAAC,CAAC,CAACsB,IAAI,CAACC,OAAO,EAAEC,MAAM,CAAC,GAAGN,MAAM,CAACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAES,CAAC,CAAC;EAAE;EACvH,SAASI,OAAOA,CAACH,KAAK,EAAE;IAAEJ,MAAM,CAAC,MAAM,EAAEI,KAAK,CAAC;EAAE;EACjD,SAASI,MAAMA,CAACJ,KAAK,EAAE;IAAEJ,MAAM,CAAC,OAAO,EAAEI,KAAK,CAAC;EAAE;EACjD,SAASF,MAAMA,CAACO,CAAC,EAAEzB,CAAC,EAAE;IAAE,IAAIyB,CAAC,CAACzB,CAAC,CAAC,EAAEU,CAAC,CAACgB,KAAK,CAAC,CAAC,EAAEhB,CAAC,CAAC3C,MAAM,EAAEiD,MAAM,CAACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAAE;AACrF;AAEA,OAAOiB,eAAe,KAAK,UAAU,GAAGA,eAAe,GAAG,UAAUzE,KAAK,EAAE0E,UAAU,EAAE9J,OAAO,EAAE;EAC5F,IAAIyC,CAAC,GAAG,IAAI3C,KAAK,CAACE,OAAO,CAAC;EAC1B,OAAOyC,CAAC,CAACqC,IAAI,GAAG,iBAAiB,EAAErC,CAAC,CAAC2C,KAAK,GAAGA,KAAK,EAAE3C,CAAC,CAACqH,UAAU,GAAGA,UAAU,EAAErH,CAAC;AACpF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMsH,cAAc,GAAG,oCAAoC;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAAC9J,QAAQ,EAAE;EAC7B,MAAM+J,WAAW,GAAG/J,QAAQ,CAACqD,IAAI,CAAC2G,WAAW,CAAC,IAAIC,iBAAiB,CAAC,MAAM,EAAE;IAAEC,KAAK,EAAE;EAAK,CAAC,CAAC,CAAC;EAC7F,MAAMC,cAAc,GAAGC,iBAAiB,CAACL,WAAW,CAAC;EACrD,MAAM,CAACM,OAAO,EAAEC,OAAO,CAAC,GAAGH,cAAc,CAACI,GAAG,CAAC,CAAC;EAC/C,OAAO;IACHvJ,MAAM,EAAEwJ,wBAAwB,CAACH,OAAO,CAAC;IACzCrK,QAAQ,EAAEyK,kBAAkB,CAACH,OAAO;EACxC,CAAC;AACL;AAAC,SACcG,kBAAkBA,CAAAC,IAAA;EAAA,OAAAC,mBAAA,CAAA3I,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAA0I,oBAAA;EAAAA,mBAAA,GAAAzI,iBAAA,CAAjC,WAAkClB,MAAM,EAAE;IACtC,MAAM4J,YAAY,GAAG,EAAE;IACvB,MAAMC,MAAM,GAAG7J,MAAM,CAAC8J,SAAS,CAAC,CAAC;IACjC,OAAO,IAAI,EAAE;MACT,MAAM;QAAEC,IAAI;QAAE3B;MAAM,CAAC,SAASyB,MAAM,CAACG,IAAI,CAAC,CAAC;MAC3C,IAAID,IAAI,EAAE;QACN,OAAOnF,UAAU,CAACqF,kBAAkB,CAACL,YAAY,CAAC,CAAC;MACvD;MACAA,YAAY,CAACjJ,IAAI,CAACyH,KAAK,CAAC;IAC5B;EACJ,CAAC;EAAA,OAAAuB,mBAAA,CAAA3I,KAAA,OAAAC,SAAA;AAAA;AACD,SAASuI,wBAAwBA,CAACxJ,MAAM,EAAE;EACtC,OAAOiH,gBAAgB,CAAC,IAAI,EAAEhG,SAAS,EAAE,UAAUiJ,0BAA0BA,CAAA,EAAG;IAC5E,MAAML,MAAM,GAAG7J,MAAM,CAAC8J,SAAS,CAAC,CAAC;IACjC,OAAO,IAAI,EAAE;MACT,MAAM;QAAE1B,KAAK;QAAE2B;MAAK,CAAC,GAAG,MAAMhD,OAAO,CAAC8C,MAAM,CAACG,IAAI,CAAC,CAAC,CAAC;MACpD,IAAID,IAAI,EAAE;QACN;MACJ;MACA,MAAM,MAAMhD,OAAO,CAACnC,UAAU,CAACwD,KAAK,CAAC,CAAC;IAC1C;EACJ,CAAC,CAAC;AACN;AACA;AACA;AACA;AACA;AACA;AACA,SAASgB,iBAAiBA,CAACL,WAAW,EAAE;EACpC,MAAMc,MAAM,GAAGd,WAAW,CAACe,SAAS,CAAC,CAAC;EACtC,MAAM9J,MAAM,GAAG,IAAImK,cAAc,CAAC;IAC9BC,KAAKA,CAAC7F,UAAU,EAAE;MACd,IAAI8F,WAAW,GAAG,EAAE;MACpB,OAAOC,IAAI,CAAC,CAAC;MACb,SAASA,IAAIA,CAAA,EAAG;QACZ,OAAOT,MAAM,CACRG,IAAI,CAAC,CAAC,CACN1B,IAAI,CAAC,CAAC;UAAEF,KAAK;UAAE2B;QAAK,CAAC,KAAK;UAC3B,IAAIA,IAAI,EAAE;YACN,IAAIM,WAAW,CAACE,IAAI,CAAC,CAAC,EAAE;cACpBhG,UAAU,CAACL,KAAK,CAAC,IAAIvF,uBAAuB,CAAC,wBAAwB,CAAC,CAAC;cACvE;YACJ;YACA4F,UAAU,CAACiG,KAAK,CAAC,CAAC;YAClB;UACJ;UACAH,WAAW,IAAIjC,KAAK;UACpB,IAAIqC,KAAK,GAAGJ,WAAW,CAACI,KAAK,CAAC5B,cAAc,CAAC;UAC7C,IAAI6B,cAAc;UAClB,OAAOD,KAAK,EAAE;YACV,IAAI;cACAC,cAAc,GAAGlJ,IAAI,CAACmJ,KAAK,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC;YACzC,CAAC,CACD,OAAOlJ,CAAC,EAAE;cACNgD,UAAU,CAACL,KAAK,CAAC,IAAIvF,uBAAuB,CAAE,iCAAgC8L,KAAK,CAAC,CAAC,CAAE,GAAE,CAAC,CAAC;cAC3F;YACJ;YACAlG,UAAU,CAACqG,OAAO,CAACF,cAAc,CAAC;YAClCL,WAAW,GAAGA,WAAW,CAACQ,SAAS,CAACJ,KAAK,CAAC,CAAC,CAAC,CAAC1F,MAAM,CAAC;YACpD0F,KAAK,GAAGJ,WAAW,CAACI,KAAK,CAAC5B,cAAc,CAAC;UAC7C;UACA,OAAOyB,IAAI,CAAC,CAAC;QACjB,CAAC,CAAC,CACGQ,KAAK,CAAEvJ,CAAC,IAAK;UACd,IAAIoC,GAAG,GAAGpC,CAAC;UACXoC,GAAG,CAACE,KAAK,GAAGtC,CAAC,CAACsC,KAAK;UACnB,IAAIF,GAAG,CAACC,IAAI,KAAK,YAAY,EAAE;YAC3BD,GAAG,GAAG,IAAIrE,4BAA4B,CAAC,8CAA8C,CAAC;UAC1F,CAAC,MACI;YACDqE,GAAG,GAAG,IAAIhF,uBAAuB,CAAC,+BAA+B,CAAC;UACtE;UACA,MAAMgF,GAAG;QACb,CAAC,CAAC;MACN;IACJ;EACJ,CAAC,CAAC;EACF,OAAO3D,MAAM;AACjB;AACA;AACA;AACA;AACA;AACA,SAASiK,kBAAkBA,CAACc,SAAS,EAAE;EACnC,MAAMC,YAAY,GAAGD,SAAS,CAACA,SAAS,CAAChG,MAAM,GAAG,CAAC,CAAC;EACpD,MAAMkG,kBAAkB,GAAG;IACvB5F,cAAc,EAAE2F,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAAC3F;EAC7F,CAAC;EACD,KAAK,MAAMrG,QAAQ,IAAI+L,SAAS,EAAE;IAC9B,IAAI/L,QAAQ,CAAC8F,UAAU,EAAE;MACrB,IAAIoG,cAAc,GAAG,CAAC;MACtB,KAAK,MAAM1E,SAAS,IAAIxH,QAAQ,CAAC8F,UAAU,EAAE;QACzC,IAAI,CAACmG,kBAAkB,CAACnG,UAAU,EAAE;UAChCmG,kBAAkB,CAACnG,UAAU,GAAG,EAAE;QACtC;QACA,IAAI,CAACmG,kBAAkB,CAACnG,UAAU,CAACoG,cAAc,CAAC,EAAE;UAChDD,kBAAkB,CAACnG,UAAU,CAACoG,cAAc,CAAC,GAAG;YAC5CC,KAAK,EAAED;UACX,CAAC;QACL;QACA;QACAD,kBAAkB,CAACnG,UAAU,CAACoG,cAAc,CAAC,CAACE,gBAAgB,GAC1D5E,SAAS,CAAC4E,gBAAgB;QAC9BH,kBAAkB,CAACnG,UAAU,CAACoG,cAAc,CAAC,CAACG,iBAAiB,GAC3D7E,SAAS,CAAC6E,iBAAiB;QAC/BJ,kBAAkB,CAACnG,UAAU,CAACoG,cAAc,CAAC,CAACzE,YAAY,GACtDD,SAAS,CAACC,YAAY;QAC1BwE,kBAAkB,CAACnG,UAAU,CAACoG,cAAc,CAAC,CAACpE,aAAa,GACvDN,SAAS,CAACM,aAAa;QAC3BmE,kBAAkB,CAACnG,UAAU,CAACoG,cAAc,CAAC,CAACI,aAAa,GACvD9E,SAAS,CAAC8E,aAAa;QAC3B;AAChB;AACA;AACA;QACgB,IAAI9E,SAAS,CAACZ,OAAO,IAAIY,SAAS,CAACZ,OAAO,CAACC,KAAK,EAAE;UAC9C,IAAI,CAACoF,kBAAkB,CAACnG,UAAU,CAACoG,cAAc,CAAC,CAACtF,OAAO,EAAE;YACxDqF,kBAAkB,CAACnG,UAAU,CAACoG,cAAc,CAAC,CAACtF,OAAO,GAAG;cACpD2F,IAAI,EAAE/E,SAAS,CAACZ,OAAO,CAAC2F,IAAI,IAAI,MAAM;cACtC1F,KAAK,EAAE;YACX,CAAC;UACL;UACA,MAAM2F,OAAO,GAAG,CAAC,CAAC;UAClB,KAAK,MAAM1F,IAAI,IAAIU,SAAS,CAACZ,OAAO,CAACC,KAAK,EAAE;YACxC,IAAIC,IAAI,CAACjB,IAAI,EAAE;cACX2G,OAAO,CAAC3G,IAAI,GAAGiB,IAAI,CAACjB,IAAI;YAC5B;YACA,IAAIiB,IAAI,CAACR,YAAY,EAAE;cACnBkG,OAAO,CAAClG,YAAY,GAAGQ,IAAI,CAACR,YAAY;YAC5C;YACA,IAAIQ,IAAI,CAACC,cAAc,EAAE;cACrByF,OAAO,CAACzF,cAAc,GAAGD,IAAI,CAACC,cAAc;YAChD;YACA,IAAID,IAAI,CAACI,mBAAmB,EAAE;cAC1BsF,OAAO,CAACtF,mBAAmB,GAAGJ,IAAI,CAACI,mBAAmB;YAC1D;YACA,IAAI3D,MAAM,CAACkJ,IAAI,CAACD,OAAO,CAAC,CAACzG,MAAM,KAAK,CAAC,EAAE;cACnCyG,OAAO,CAAC3G,IAAI,GAAG,EAAE;YACrB;YACAoG,kBAAkB,CAACnG,UAAU,CAACoG,cAAc,CAAC,CAACtF,OAAO,CAACC,KAAK,CAAClF,IAAI,CAAC6K,OAAO,CAAC;UAC7E;QACJ;MACJ;MACAN,cAAc,EAAE;IACpB;IACA,IAAIlM,QAAQ,CAAC0M,aAAa,EAAE;MACxBT,kBAAkB,CAACS,aAAa,GAAG1M,QAAQ,CAAC0M,aAAa;IAC7D;EACJ;EACA,OAAOT,kBAAkB;AAC7B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAfA,SAgBeU,qBAAqBA,CAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA;EAAA,OAAAC,sBAAA,CAAAhL,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAA+K,uBAAA;EAAAA,sBAAA,GAAA9K,iBAAA,CAApC,WAAqCnB,MAAM,EAAEF,KAAK,EAAEoM,MAAM,EAAEhM,cAAc,EAAE;IACxE,MAAMjB,QAAQ,SAAS2D,gBAAgB,CAAC9C,KAAK,EAAEF,IAAI,CAACuM,uBAAuB,EAAEnM,MAAM,EACnF,YAAa,IAAI,EAAEyB,IAAI,CAACC,SAAS,CAACwK,MAAM,CAAC,EAAEhM,cAAc,CAAC;IAC1D,OAAO6I,aAAa,CAAC9J,QAAQ,CAAC;EAClC,CAAC;EAAA,OAAAgN,sBAAA,CAAAhL,KAAA,OAAAC,SAAA;AAAA;AAAA,SACckL,eAAeA,CAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA;EAAA,OAAAC,gBAAA,CAAAxL,KAAA,OAAAC,SAAA;AAAA;AAU9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAfA,SAAAuL,iBAAA;EAAAA,gBAAA,GAAAtL,iBAAA,CAVA,WAA+BnB,MAAM,EAAEF,KAAK,EAAEoM,MAAM,EAAEhM,cAAc,EAAE;IAClE,MAAMjB,QAAQ,SAAS2D,gBAAgB,CAAC9C,KAAK,EAAEF,IAAI,CAAC8M,gBAAgB,EAAE1M,MAAM,EAC5E,YAAa,KAAK,EAAEyB,IAAI,CAACC,SAAS,CAACwK,MAAM,CAAC,EAAEhM,cAAc,CAAC;IAC3D,MAAMyM,YAAY,SAAS1N,QAAQ,CAACiF,IAAI,CAAC,CAAC;IAC1C,MAAM0I,gBAAgB,GAAG/H,UAAU,CAAC8H,YAAY,CAAC;IACjD,OAAO;MACH1N,QAAQ,EAAE2N;IACd,CAAC;EACL,CAAC;EAAA,OAAAH,gBAAA,CAAAxL,KAAA,OAAAC,SAAA;AAAA;AAkBD,SAAS2L,uBAAuBA,CAACC,KAAK,EAAE;EACpC;EACA,IAAIA,KAAK,IAAI,IAAI,EAAE;IACf,OAAOxI,SAAS;EACpB,CAAC,MACI,IAAI,OAAOwI,KAAK,KAAK,QAAQ,EAAE;IAChC,OAAO;MAAEtB,IAAI,EAAE,QAAQ;MAAE1F,KAAK,EAAE,CAAC;QAAEhB,IAAI,EAAEgI;MAAM,CAAC;IAAE,CAAC;EACvD,CAAC,MACI,IAAIA,KAAK,CAAChI,IAAI,EAAE;IACjB,OAAO;MAAE0G,IAAI,EAAE,QAAQ;MAAE1F,KAAK,EAAE,CAACgH,KAAK;IAAE,CAAC;EAC7C,CAAC,MACI,IAAIA,KAAK,CAAChH,KAAK,EAAE;IAClB,IAAI,CAACgH,KAAK,CAACtB,IAAI,EAAE;MACb,OAAO;QAAEA,IAAI,EAAE,QAAQ;QAAE1F,KAAK,EAAEgH,KAAK,CAAChH;MAAM,CAAC;IACjD,CAAC,MACI;MACD,OAAOgH,KAAK;IAChB;EACJ;AACJ;AACA,SAASC,gBAAgBA,CAACC,OAAO,EAAE;EAC/B,IAAIC,QAAQ,GAAG,EAAE;EACjB,IAAI,OAAOD,OAAO,KAAK,QAAQ,EAAE;IAC7BC,QAAQ,GAAG,CAAC;MAAEnI,IAAI,EAAEkI;IAAQ,CAAC,CAAC;EAClC,CAAC,MACI;IACD,KAAK,MAAME,YAAY,IAAIF,OAAO,EAAE;MAChC,IAAI,OAAOE,YAAY,KAAK,QAAQ,EAAE;QAClCD,QAAQ,CAACrM,IAAI,CAAC;UAAEkE,IAAI,EAAEoI;QAAa,CAAC,CAAC;MACzC,CAAC,MACI;QACDD,QAAQ,CAACrM,IAAI,CAACsM,YAAY,CAAC;MAC/B;IACJ;EACJ;EACA,OAAOC,8CAA8C,CAACF,QAAQ,CAAC;AACnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,8CAA8CA,CAACrH,KAAK,EAAE;EAC3D,MAAMsH,WAAW,GAAG;IAAE5B,IAAI,EAAE,MAAM;IAAE1F,KAAK,EAAE;EAAG,CAAC;EAC/C,MAAMuH,eAAe,GAAG;IAAE7B,IAAI,EAAE,UAAU;IAAE1F,KAAK,EAAE;EAAG,CAAC;EACvD,IAAIwH,cAAc,GAAG,KAAK;EAC1B,IAAIC,kBAAkB,GAAG,KAAK;EAC9B,KAAK,MAAMxH,IAAI,IAAID,KAAK,EAAE;IACtB,IAAI,kBAAkB,IAAIC,IAAI,EAAE;MAC5BsH,eAAe,CAACvH,KAAK,CAAClF,IAAI,CAACmF,IAAI,CAAC;MAChCwH,kBAAkB,GAAG,IAAI;IAC7B,CAAC,MACI;MACDH,WAAW,CAACtH,KAAK,CAAClF,IAAI,CAACmF,IAAI,CAAC;MAC5BuH,cAAc,GAAG,IAAI;IACzB;EACJ;EACA,IAAIA,cAAc,IAAIC,kBAAkB,EAAE;IACtC,MAAM,IAAI3O,uBAAuB,CAAC,4HAA4H,CAAC;EACnK;EACA,IAAI,CAAC0O,cAAc,IAAI,CAACC,kBAAkB,EAAE;IACxC,MAAM,IAAI3O,uBAAuB,CAAC,kDAAkD,CAAC;EACzF;EACA,IAAI0O,cAAc,EAAE;IAChB,OAAOF,WAAW;EACtB;EACA,OAAOC,eAAe;AAC1B;AACA,SAASG,sBAAsBA,CAACtB,MAAM,EAAEuB,WAAW,EAAE;EACjD,IAAIrN,EAAE;EACN,IAAIsN,+BAA+B,GAAG;IAClC5N,KAAK,EAAE2N,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAAC3N,KAAK;IAClF6N,gBAAgB,EAAEF,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACE,gBAAgB;IACxGC,cAAc,EAAEH,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACG,cAAc;IACpGC,KAAK,EAAEJ,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACI,KAAK;IAClFC,UAAU,EAAEL,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACK,UAAU;IAC5FC,iBAAiB,EAAEN,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACM,iBAAiB;IAC1GC,aAAa,EAAE,CAAC5N,EAAE,GAAGqN,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACO,aAAa,MAAM,IAAI,IAAI5N,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACyD,IAAI;IACtJoK,QAAQ,EAAE;EACd,CAAC;EACD,MAAMC,8BAA8B,GAAGhC,MAAM,CAACiC,sBAAsB,IAAI,IAAI;EAC5E,IAAIjC,MAAM,CAAC+B,QAAQ,EAAE;IACjB,IAAIC,8BAA8B,EAAE;MAChC,MAAM,IAAI5O,mCAAmC,CAAC,mFAAmF,CAAC;IACtI;IACAoO,+BAA+B,CAACO,QAAQ,GAAG/B,MAAM,CAAC+B,QAAQ;EAC9D,CAAC,MACI,IAAIC,8BAA8B,EAAE;IACrCR,+BAA+B,GAAGlL,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEiL,+BAA+B,CAAC,EAAExB,MAAM,CAACiC,sBAAsB,CAAC;EACtI,CAAC,MACI;IACD;IACA,MAAMtI,OAAO,GAAGkH,gBAAgB,CAACb,MAAM,CAAC;IACxCwB,+BAA+B,CAACO,QAAQ,GAAG,CAACpI,OAAO,CAAC;EACxD;EACA,OAAO;IAAEsI,sBAAsB,EAAET;EAAgC,CAAC;AACtE;AACA,SAASU,0BAA0BA,CAAClC,MAAM,EAAE;EACxC,IAAImC,gBAAgB;EACpB,IAAInC,MAAM,CAAC+B,QAAQ,EAAE;IACjBI,gBAAgB,GAAGnC,MAAM;EAC7B,CAAC,MACI;IACD;IACA,MAAMrG,OAAO,GAAGkH,gBAAgB,CAACb,MAAM,CAAC;IACxCmC,gBAAgB,GAAG;MAAEJ,QAAQ,EAAE,CAACpI,OAAO;IAAE,CAAC;EAC9C;EACA,IAAIqG,MAAM,CAAC6B,iBAAiB,EAAE;IAC1BM,gBAAgB,CAACN,iBAAiB,GAAGlB,uBAAuB,CAACX,MAAM,CAAC6B,iBAAiB,CAAC;EAC1F;EACA,OAAOM,gBAAgB;AAC3B;AACA,SAASC,uBAAuBA,CAACpC,MAAM,EAAE;EACrC,IAAI,OAAOA,MAAM,KAAK,QAAQ,IAAIqC,KAAK,CAACC,OAAO,CAACtC,MAAM,CAAC,EAAE;IACrD,MAAMrG,OAAO,GAAGkH,gBAAgB,CAACb,MAAM,CAAC;IACxC,OAAO;MAAErG;IAAQ,CAAC;EACtB;EACA,OAAOqG,MAAM;AACjB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMuC,iBAAiB,GAAG,CACtB,MAAM,EACN,YAAY,EACZ,cAAc,EACd,kBAAkB,EAClB,gBAAgB,EAChB,qBAAqB,CACxB;AACD,MAAMC,oBAAoB,GAAG;EACzBC,IAAI,EAAE,CAAC,MAAM,EAAE,YAAY,CAAC;EAC5BC,QAAQ,EAAE,CAAC,kBAAkB,CAAC;EAC9B9O,KAAK,EAAE,CAAC,MAAM,EAAE,cAAc,EAAE,gBAAgB,EAAE,qBAAqB,CAAC;EACxE;EACA+O,MAAM,EAAE,CAAC,MAAM;AACnB,CAAC;AACD,SAASC,mBAAmBA,CAACC,OAAO,EAAE;EAClC,IAAIC,WAAW,GAAG,KAAK;EACvB,KAAK,MAAMC,WAAW,IAAIF,OAAO,EAAE;IAC/B,MAAM;MAAEvD,IAAI;MAAE1F;IAAM,CAAC,GAAGmJ,WAAW;IACnC,IAAI,CAACD,WAAW,IAAIxD,IAAI,KAAK,MAAM,EAAE;MACjC,MAAM,IAAI5M,uBAAuB,CAAE,iDAAgD4M,IAAK,EAAC,CAAC;IAC9F;IACA,IAAI,CAACrN,cAAc,CAACwI,QAAQ,CAAC6E,IAAI,CAAC,EAAE;MAChC,MAAM,IAAI5M,uBAAuB,CAAE,4CAA2C4M,IAAK,yBAAwB/J,IAAI,CAACC,SAAS,CAACvD,cAAc,CAAE,EAAC,CAAC;IAChJ;IACA,IAAI,CAACoQ,KAAK,CAACC,OAAO,CAAC1I,KAAK,CAAC,EAAE;MACvB,MAAM,IAAIlH,uBAAuB,CAAC,6DAA6D,CAAC;IACpG;IACA,IAAIkH,KAAK,CAACd,MAAM,KAAK,CAAC,EAAE;MACpB,MAAM,IAAIpG,uBAAuB,CAAC,4CAA4C,CAAC;IACnF;IACA,MAAMsQ,WAAW,GAAG;MAChBpK,IAAI,EAAE,CAAC;MACPqK,UAAU,EAAE,CAAC;MACb5J,YAAY,EAAE,CAAC;MACf6J,gBAAgB,EAAE,CAAC;MACnBC,QAAQ,EAAE,CAAC;MACXrJ,cAAc,EAAE,CAAC;MACjBG,mBAAmB,EAAE;IACzB,CAAC;IACD,KAAK,MAAMJ,IAAI,IAAID,KAAK,EAAE;MACtB,KAAK,MAAMwJ,GAAG,IAAIb,iBAAiB,EAAE;QACjC,IAAIa,GAAG,IAAIvJ,IAAI,EAAE;UACbmJ,WAAW,CAACI,GAAG,CAAC,IAAI,CAAC;QACzB;MACJ;IACJ;IACA,MAAMC,UAAU,GAAGb,oBAAoB,CAAClD,IAAI,CAAC;IAC7C,KAAK,MAAM8D,GAAG,IAAIb,iBAAiB,EAAE;MACjC,IAAI,CAACc,UAAU,CAAC5I,QAAQ,CAAC2I,GAAG,CAAC,IAAIJ,WAAW,CAACI,GAAG,CAAC,GAAG,CAAC,EAAE;QACnD,MAAM,IAAI1Q,uBAAuB,CAAE,sBAAqB4M,IAAK,oBAAmB8D,GAAI,QAAO,CAAC;MAChG;IACJ;IACAN,WAAW,GAAG,IAAI;EACtB;AACJ;AACA;AACA;AACA;AACA,SAASQ,eAAeA,CAACvQ,QAAQ,EAAE;EAC/B,IAAImB,EAAE;EACN,IAAInB,QAAQ,CAAC8F,UAAU,KAAKT,SAAS,IAAIrF,QAAQ,CAAC8F,UAAU,CAACC,MAAM,KAAK,CAAC,EAAE;IACvE,OAAO,KAAK;EAChB;EACA,MAAMa,OAAO,GAAG,CAACzF,EAAE,GAAGnB,QAAQ,CAAC8F,UAAU,CAAC,CAAC,CAAC,MAAM,IAAI,IAAI3E,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACyF,OAAO;EAC7F,IAAIA,OAAO,KAAKvB,SAAS,EAAE;IACvB,OAAO,KAAK;EAChB;EACA,IAAIuB,OAAO,CAACC,KAAK,KAAKxB,SAAS,IAAIuB,OAAO,CAACC,KAAK,CAACd,MAAM,KAAK,CAAC,EAAE;IAC3D,OAAO,KAAK;EAChB;EACA,KAAK,MAAMe,IAAI,IAAIF,OAAO,CAACC,KAAK,EAAE;IAC9B,IAAIC,IAAI,KAAKzB,SAAS,IAAI9B,MAAM,CAACkJ,IAAI,CAAC3F,IAAI,CAAC,CAACf,MAAM,KAAK,CAAC,EAAE;MACtD,OAAO,KAAK;IAChB;IACA,IAAIe,IAAI,CAACjB,IAAI,KAAKR,SAAS,IAAIyB,IAAI,CAACjB,IAAI,KAAK,EAAE,EAAE;MAC7C,OAAO,KAAK;IAChB;EACJ;EACA,OAAO,IAAI;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM2K,YAAY,GAAG,cAAc;AACnC;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,WAAW,CAAC;EACd5Q,WAAWA,CAACkB,MAAM,EAAEF,KAAK,EAAEoM,MAAM,EAAEyD,eAAe,GAAG,CAAC,CAAC,EAAE;IACrD,IAAI,CAAC7P,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACoM,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACyD,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,YAAY,GAAG/H,OAAO,CAACQ,OAAO,CAAC,CAAC;IACrC,IAAI,CAACwH,OAAO,GAAG9P,MAAM;IACrB,IAAIkM,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC6C,OAAO,EAAE;MAChED,mBAAmB,CAAC5C,MAAM,CAAC6C,OAAO,CAAC;MACnC,IAAI,CAACa,QAAQ,GAAG1D,MAAM,CAAC6C,OAAO;IAClC;EACJ;EACA;AACJ;AACA;AACA;AACA;EACUgB,UAAUA,CAAA,EAAG;IAAA,IAAAC,KAAA;IAAA,OAAA7O,iBAAA;MACf,MAAM6O,KAAI,CAACH,YAAY;MACvB,OAAOG,KAAI,CAACJ,QAAQ;IAAC;EACzB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACUK,WAAWA,CAAAC,IAAA,EAA+B;IAAA,IAAAC,MAAA;IAAA,OAAAhP,iBAAA,YAA9B6L,OAAO,EAAE9M,cAAc,GAAG,CAAC,CAAC;MAC1C,IAAIE,EAAE,EAAEC,EAAE,EAAEqF,EAAE,EAAEC,EAAE,EAAEyK,EAAE,EAAEC,EAAE;MAC1B,MAAMF,MAAI,CAACN,YAAY;MACvB,MAAMS,UAAU,GAAGvD,gBAAgB,CAACC,OAAO,CAAC;MAC5C,MAAMmB,sBAAsB,GAAG;QAC3BP,cAAc,EAAE,CAACxN,EAAE,GAAG+P,MAAI,CAACjE,MAAM,MAAM,IAAI,IAAI9L,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACwN,cAAc;QACzFD,gBAAgB,EAAE,CAACtN,EAAE,GAAG8P,MAAI,CAACjE,MAAM,MAAM,IAAI,IAAI7L,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACsN,gBAAgB;QAC7FE,KAAK,EAAE,CAACnI,EAAE,GAAGyK,MAAI,CAACjE,MAAM,MAAM,IAAI,IAAIxG,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACmI,KAAK;QACvEC,UAAU,EAAE,CAACnI,EAAE,GAAGwK,MAAI,CAACjE,MAAM,MAAM,IAAI,IAAIvG,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACmI,UAAU;QACjFC,iBAAiB,EAAE,CAACqC,EAAE,GAAGD,MAAI,CAACjE,MAAM,MAAM,IAAI,IAAIkE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACrC,iBAAiB;QAC/FC,aAAa,EAAE,CAACqC,EAAE,GAAGF,MAAI,CAACjE,MAAM,MAAM,IAAI,IAAImE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACrC,aAAa;QACvFC,QAAQ,EAAE,CAAC,GAAGkC,MAAI,CAACP,QAAQ,EAAEU,UAAU;MAC3C,CAAC;MACD,MAAMC,yBAAyB,GAAG/N,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE0N,MAAI,CAACR,eAAe,CAAC,EAAEzP,cAAc,CAAC;MACxG,IAAIsQ,WAAW;MACf;MACAL,MAAI,CAACN,YAAY,GAAGM,MAAI,CAACN,YAAY,CAChCtH,IAAI,CAAC,MAAM6D,eAAe,CAAC+D,MAAI,CAACL,OAAO,EAAEK,MAAI,CAACrQ,KAAK,EAAEqO,sBAAsB,EAAEoC,yBAAyB,CAAC,CAAC,CACxGhI,IAAI,CAAEkI,MAAM,IAAK;QAClB,IAAIrQ,EAAE;QACN,IAAIoP,eAAe,CAACiB,MAAM,CAACxR,QAAQ,CAAC,EAAE;UAClCkR,MAAI,CAACP,QAAQ,CAAChP,IAAI,CAAC0P,UAAU,CAAC;UAC9B,MAAMI,eAAe,GAAGlO,MAAM,CAACC,MAAM,CAAC;YAAEqD,KAAK,EAAE,EAAE;YAC7C;YACA0F,IAAI,EAAE;UAAQ,CAAC,EAAE,CAACpL,EAAE,GAAGqQ,MAAM,CAACxR,QAAQ,CAAC8F,UAAU,MAAM,IAAI,IAAI3E,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC,CAAC,CAAC,CAACyF,OAAO,CAAC;UAC1GsK,MAAI,CAACP,QAAQ,CAAChP,IAAI,CAAC8P,eAAe,CAAC;QACvC,CAAC,MACI;UACD,MAAMC,iBAAiB,GAAGvL,uBAAuB,CAACqL,MAAM,CAACxR,QAAQ,CAAC;UAClE,IAAI0R,iBAAiB,EAAE;YACnB1L,OAAO,CAACC,IAAI,CAAE,mCAAkCyL,iBAAkB,wCAAuC,CAAC;UAC9G;QACJ;QACAH,WAAW,GAAGC,MAAM;MACxB,CAAC,CAAC,CACG1F,KAAK,CAAEvJ,CAAC,IAAK;QACd;QACA2O,MAAI,CAACN,YAAY,GAAG/H,OAAO,CAACQ,OAAO,CAAC,CAAC;QACrC,MAAM9G,CAAC;MACX,CAAC,CAAC;MACF,MAAM2O,MAAI,CAACN,YAAY;MACvB,OAAOW,WAAW;IAAC,GAAAvP,KAAA,OAAAC,SAAA;EACvB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACU0P,iBAAiBA,CAAAC,IAAA,EAA+B;IAAA,IAAAC,MAAA;IAAA,OAAA3P,iBAAA,YAA9B6L,OAAO,EAAE9M,cAAc,GAAG,CAAC,CAAC;MAChD,IAAIE,EAAE,EAAEC,EAAE,EAAEqF,EAAE,EAAEC,EAAE,EAAEyK,EAAE,EAAEC,EAAE;MAC1B,MAAMS,MAAI,CAACjB,YAAY;MACvB,MAAMS,UAAU,GAAGvD,gBAAgB,CAACC,OAAO,CAAC;MAC5C,MAAMmB,sBAAsB,GAAG;QAC3BP,cAAc,EAAE,CAACxN,EAAE,GAAG0Q,MAAI,CAAC5E,MAAM,MAAM,IAAI,IAAI9L,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACwN,cAAc;QACzFD,gBAAgB,EAAE,CAACtN,EAAE,GAAGyQ,MAAI,CAAC5E,MAAM,MAAM,IAAI,IAAI7L,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACsN,gBAAgB;QAC7FE,KAAK,EAAE,CAACnI,EAAE,GAAGoL,MAAI,CAAC5E,MAAM,MAAM,IAAI,IAAIxG,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACmI,KAAK;QACvEC,UAAU,EAAE,CAACnI,EAAE,GAAGmL,MAAI,CAAC5E,MAAM,MAAM,IAAI,IAAIvG,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACmI,UAAU;QACjFC,iBAAiB,EAAE,CAACqC,EAAE,GAAGU,MAAI,CAAC5E,MAAM,MAAM,IAAI,IAAIkE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACrC,iBAAiB;QAC/FC,aAAa,EAAE,CAACqC,EAAE,GAAGS,MAAI,CAAC5E,MAAM,MAAM,IAAI,IAAImE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACrC,aAAa;QACvFC,QAAQ,EAAE,CAAC,GAAG6C,MAAI,CAAClB,QAAQ,EAAEU,UAAU;MAC3C,CAAC;MACD,MAAMC,yBAAyB,GAAG/N,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEqO,MAAI,CAACnB,eAAe,CAAC,EAAEzP,cAAc,CAAC;MACxG,MAAM6Q,aAAa,GAAGnF,qBAAqB,CAACkF,MAAI,CAAChB,OAAO,EAAEgB,MAAI,CAAChR,KAAK,EAAEqO,sBAAsB,EAAEoC,yBAAyB,CAAC;MACxH;MACAO,MAAI,CAACjB,YAAY,GAAGiB,MAAI,CAACjB,YAAY,CAChCtH,IAAI,CAAC,MAAMwI,aAAa;MACzB;MACA;MAAA,CACChG,KAAK,CAAEiG,QAAQ,IAAK;QACrB,MAAM,IAAInS,KAAK,CAAC4Q,YAAY,CAAC;MACjC,CAAC,CAAC,CACGlH,IAAI,CAAE0I,YAAY,IAAKA,YAAY,CAAChS,QAAQ,CAAC,CAC7CsJ,IAAI,CAAEtJ,QAAQ,IAAK;QACpB,IAAIuQ,eAAe,CAACvQ,QAAQ,CAAC,EAAE;UAC3B6R,MAAI,CAAClB,QAAQ,CAAChP,IAAI,CAAC0P,UAAU,CAAC;UAC9B,MAAMI,eAAe,GAAGlO,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAExD,QAAQ,CAAC8F,UAAU,CAAC,CAAC,CAAC,CAACc,OAAO,CAAC;UACzE;UACA,IAAI,CAAC6K,eAAe,CAAClF,IAAI,EAAE;YACvBkF,eAAe,CAAClF,IAAI,GAAG,OAAO;UAClC;UACAsF,MAAI,CAAClB,QAAQ,CAAChP,IAAI,CAAC8P,eAAe,CAAC;QACvC,CAAC,MACI;UACD,MAAMC,iBAAiB,GAAGvL,uBAAuB,CAACnG,QAAQ,CAAC;UAC3D,IAAI0R,iBAAiB,EAAE;YACnB1L,OAAO,CAACC,IAAI,CAAE,yCAAwCyL,iBAAkB,wCAAuC,CAAC;UACpH;QACJ;MACJ,CAAC,CAAC,CACG5F,KAAK,CAAEvJ,CAAC,IAAK;QACd;QACA;QACA;QACA,IAAIA,CAAC,CAACzC,OAAO,KAAK0Q,YAAY,EAAE;UAC5B;UACA;UACAxK,OAAO,CAACd,KAAK,CAAC3C,CAAC,CAAC;QACpB;MACJ,CAAC,CAAC;MACF,OAAOuP,aAAa;IAAC,GAAA9P,KAAA,OAAAC,SAAA;EACzB;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAfA,SAgBegQ,WAAWA,CAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA;EAAA,OAAAC,YAAA,CAAAtQ,KAAA,OAAAC,SAAA;AAAA;AAK1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAfA,SAAAqQ,aAAA;EAAAA,YAAA,GAAApQ,iBAAA,CALA,WAA2BnB,MAAM,EAAEF,KAAK,EAAEoM,MAAM,EAAEsF,oBAAoB,EAAE;IACpE,MAAMvS,QAAQ,SAAS2D,gBAAgB,CAAC9C,KAAK,EAAEF,IAAI,CAAC6R,YAAY,EAAEzR,MAAM,EAAE,KAAK,EAAEyB,IAAI,CAACC,SAAS,CAACwK,MAAM,CAAC,EAAEsF,oBAAoB,CAAC;IAC9H,OAAOvS,QAAQ,CAACiF,IAAI,CAAC,CAAC;EAC1B,CAAC;EAAA,OAAAqN,YAAA,CAAAtQ,KAAA,OAAAC,SAAA;AAAA;AAAA,SAkBcwQ,YAAYA,CAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA;EAAA,OAAAC,aAAA,CAAA9Q,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAA6Q,cAAA;EAAAA,aAAA,GAAA5Q,iBAAA,CAA3B,WAA4BnB,MAAM,EAAEF,KAAK,EAAEoM,MAAM,EAAEhM,cAAc,EAAE;IAC/D,MAAMjB,QAAQ,SAAS2D,gBAAgB,CAAC9C,KAAK,EAAEF,IAAI,CAACoS,aAAa,EAAEhS,MAAM,EAAE,KAAK,EAAEyB,IAAI,CAACC,SAAS,CAACwK,MAAM,CAAC,EAAEhM,cAAc,CAAC;IACzH,OAAOjB,QAAQ,CAACiF,IAAI,CAAC,CAAC;EAC1B,CAAC;EAAA,OAAA6N,aAAA,CAAA9Q,KAAA,OAAAC,SAAA;AAAA;AAAA,SACc+Q,kBAAkBA,CAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA;EAAA,OAAAC,mBAAA,CAAArR,KAAA,OAAAC,SAAA;AAAA;AAQjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAHA,SAAAoR,oBAAA;EAAAA,mBAAA,GAAAnR,iBAAA,CAxBA,WAAkCnB,MAAM,EAAEF,KAAK,EAAEoM,MAAM,EAAEhM,cAAc,EAAE;IACrE,MAAMqS,iBAAiB,GAAGrG,MAAM,CAACsG,QAAQ,CAACC,GAAG,CAAEzF,OAAO,IAAK;MACvD,OAAOxK,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEuK,OAAO,CAAC,EAAE;QAAElN;MAAM,CAAC,CAAC;IAC/D,CAAC,CAAC;IACF,MAAMb,QAAQ,SAAS2D,gBAAgB,CAAC9C,KAAK,EAAEF,IAAI,CAAC8S,oBAAoB,EAAE1S,MAAM,EAAE,KAAK,EAAEyB,IAAI,CAACC,SAAS,CAAC;MAAE8Q,QAAQ,EAAED;IAAkB,CAAC,CAAC,EAAErS,cAAc,CAAC;IACzJ,OAAOjB,QAAQ,CAACiF,IAAI,CAAC,CAAC;EAC1B,CAAC;EAAA,OAAAoO,mBAAA,CAAArR,KAAA,OAAAC,SAAA;AAAA;AAsBD,MAAMyR,eAAe,CAAC;EAClB7T,WAAWA,CAACkB,MAAM,EAAEyN,WAAW,EAAEkC,eAAe,GAAG,CAAC,CAAC,EAAE;IACnD,IAAI,CAAC3P,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC2P,eAAe,GAAGA,eAAe;IACtC,IAAIlC,WAAW,CAAC3N,KAAK,CAAC6G,QAAQ,CAAC,GAAG,CAAC,EAAE;MACjC;MACA,IAAI,CAAC7G,KAAK,GAAG2N,WAAW,CAAC3N,KAAK;IAClC,CAAC,MACI;MACD;MACA,IAAI,CAACA,KAAK,GAAI,UAAS2N,WAAW,CAAC3N,KAAM,EAAC;IAC9C;IACA,IAAI,CAAC6N,gBAAgB,GAAGF,WAAW,CAACE,gBAAgB,IAAI,CAAC,CAAC;IAC1D,IAAI,CAACC,cAAc,GAAGH,WAAW,CAACG,cAAc,IAAI,EAAE;IACtD,IAAI,CAACC,KAAK,GAAGJ,WAAW,CAACI,KAAK;IAC9B,IAAI,CAACC,UAAU,GAAGL,WAAW,CAACK,UAAU;IACxC,IAAI,CAACC,iBAAiB,GAAGlB,uBAAuB,CAACY,WAAW,CAACM,iBAAiB,CAAC;IAC/E,IAAI,CAACC,aAAa,GAAGP,WAAW,CAACO,aAAa;EAClD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACU5B,eAAeA,CAAAwG,IAAA,EAA+B;IAAA,IAAAC,MAAA;IAAA,OAAA1R,iBAAA,YAA9B6L,OAAO,EAAE9M,cAAc,GAAG,CAAC,CAAC;MAC9C,IAAIE,EAAE;MACN,MAAM0S,eAAe,GAAG1E,0BAA0B,CAACpB,OAAO,CAAC;MAC3D,MAAM+F,6BAA6B,GAAGvQ,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEoQ,MAAI,CAAClD,eAAe,CAAC,EAAEzP,cAAc,CAAC;MAC5G,OAAOkM,eAAe,CAACyG,MAAI,CAAC7S,MAAM,EAAE6S,MAAI,CAAC/S,KAAK,EAAE0C,MAAM,CAACC,MAAM,CAAC;QAAEkL,gBAAgB,EAAEkF,MAAI,CAAClF,gBAAgB;QAAEC,cAAc,EAAEiF,MAAI,CAACjF,cAAc;QAAEC,KAAK,EAAEgF,MAAI,CAAChF,KAAK;QAAEC,UAAU,EAAE+E,MAAI,CAAC/E,UAAU;QAAEC,iBAAiB,EAAE8E,MAAI,CAAC9E,iBAAiB;QAAEC,aAAa,EAAE,CAAC5N,EAAE,GAAGyS,MAAI,CAAC7E,aAAa,MAAM,IAAI,IAAI5N,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACyD;MAAK,CAAC,EAAEiP,eAAe,CAAC,EAAEC,6BAA6B,CAAC;IAAC,GAAA9R,KAAA,OAAAC,SAAA;EACvX;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACU0K,qBAAqBA,CAAAoH,IAAA,EAA+B;IAAA,IAAAC,MAAA;IAAA,OAAA9R,iBAAA,YAA9B6L,OAAO,EAAE9M,cAAc,GAAG,CAAC,CAAC;MACpD,IAAIE,EAAE;MACN,MAAM0S,eAAe,GAAG1E,0BAA0B,CAACpB,OAAO,CAAC;MAC3D,MAAM+F,6BAA6B,GAAGvQ,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEwQ,MAAI,CAACtD,eAAe,CAAC,EAAEzP,cAAc,CAAC;MAC5G,OAAO0L,qBAAqB,CAACqH,MAAI,CAACjT,MAAM,EAAEiT,MAAI,CAACnT,KAAK,EAAE0C,MAAM,CAACC,MAAM,CAAC;QAAEkL,gBAAgB,EAAEsF,MAAI,CAACtF,gBAAgB;QAAEC,cAAc,EAAEqF,MAAI,CAACrF,cAAc;QAAEC,KAAK,EAAEoF,MAAI,CAACpF,KAAK;QAAEC,UAAU,EAAEmF,MAAI,CAACnF,UAAU;QAAEC,iBAAiB,EAAEkF,MAAI,CAAClF,iBAAiB;QAAEC,aAAa,EAAE,CAAC5N,EAAE,GAAG6S,MAAI,CAACjF,aAAa,MAAM,IAAI,IAAI5N,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACyD;MAAK,CAAC,EAAEiP,eAAe,CAAC,EAAEC,6BAA6B,CAAC;IAAC,GAAA9R,KAAA,OAAAC,SAAA;EAC7X;EACA;AACJ;AACA;AACA;EACIgS,SAASA,CAACC,eAAe,EAAE;IACvB,IAAI/S,EAAE;IACN,OAAO,IAAIsP,WAAW,CAAC,IAAI,CAAC1P,MAAM,EAAE,IAAI,CAACF,KAAK,EAAE0C,MAAM,CAACC,MAAM,CAAC;MAAEkL,gBAAgB,EAAE,IAAI,CAACA,gBAAgB;MAAEC,cAAc,EAAE,IAAI,CAACA,cAAc;MAAEC,KAAK,EAAE,IAAI,CAACA,KAAK;MAAEC,UAAU,EAAE,IAAI,CAACA,UAAU;MAAEC,iBAAiB,EAAE,IAAI,CAACA,iBAAiB;MAAEC,aAAa,EAAE,CAAC5N,EAAE,GAAG,IAAI,CAAC4N,aAAa,MAAM,IAAI,IAAI5N,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACyD;IAAK,CAAC,EAAEsP,eAAe,CAAC,EAAE,IAAI,CAACxD,eAAe,CAAC;EAC7W;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACUuB,WAAWA,CAAAkC,IAAA,EAA+B;IAAA,IAAAC,MAAA;IAAA,OAAAlS,iBAAA,YAA9B6L,OAAO,EAAE9M,cAAc,GAAG,CAAC,CAAC;MAC1C,MAAM4S,eAAe,GAAGtF,sBAAsB,CAACR,OAAO,EAAE;QACpDlN,KAAK,EAAEuT,MAAI,CAACvT,KAAK;QACjB6N,gBAAgB,EAAE0F,MAAI,CAAC1F,gBAAgB;QACvCC,cAAc,EAAEyF,MAAI,CAACzF,cAAc;QACnCC,KAAK,EAAEwF,MAAI,CAACxF,KAAK;QACjBC,UAAU,EAAEuF,MAAI,CAACvF,UAAU;QAC3BC,iBAAiB,EAAEsF,MAAI,CAACtF,iBAAiB;QACzCC,aAAa,EAAEqF,MAAI,CAACrF;MACxB,CAAC,CAAC;MACF,MAAM+E,6BAA6B,GAAGvQ,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE4Q,MAAI,CAAC1D,eAAe,CAAC,EAAEzP,cAAc,CAAC;MAC5G,OAAOgR,WAAW,CAACmC,MAAI,CAACrT,MAAM,EAAEqT,MAAI,CAACvT,KAAK,EAAEgT,eAAe,EAAEC,6BAA6B,CAAC;IAAC,GAAA9R,KAAA,OAAAC,SAAA;EAChG;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACUwQ,YAAYA,CAAA4B,IAAA,EAA+B;IAAA,IAAAC,MAAA;IAAA,OAAApS,iBAAA,YAA9B6L,OAAO,EAAE9M,cAAc,GAAG,CAAC,CAAC;MAC3C,MAAM4S,eAAe,GAAGxE,uBAAuB,CAACtB,OAAO,CAAC;MACxD,MAAM+F,6BAA6B,GAAGvQ,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE8Q,MAAI,CAAC5D,eAAe,CAAC,EAAEzP,cAAc,CAAC;MAC5G,OAAOwR,YAAY,CAAC6B,MAAI,CAACvT,MAAM,EAAEuT,MAAI,CAACzT,KAAK,EAAEgT,eAAe,EAAEC,6BAA6B,CAAC;IAAC,GAAA9R,KAAA,OAAAC,SAAA;EACjG;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACU+Q,kBAAkBA,CAAAuB,IAAA,EAAgD;IAAA,IAAAC,MAAA;IAAA,OAAAtS,iBAAA,YAA/CuS,wBAAwB,EAAExT,cAAc,GAAG,CAAC,CAAC;MAClE,MAAM6S,6BAA6B,GAAGvQ,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEgR,MAAI,CAAC9D,eAAe,CAAC,EAAEzP,cAAc,CAAC;MAC5G,OAAO+R,kBAAkB,CAACwB,MAAI,CAACzT,MAAM,EAAEyT,MAAI,CAAC3T,KAAK,EAAE4T,wBAAwB,EAAEX,6BAA6B,CAAC;IAAC,GAAA9R,KAAA,OAAAC,SAAA;EAChH;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMyS,kBAAkB,CAAC;EACrB7U,WAAWA,CAACkB,MAAM,EAAE;IAChB,IAAI,CAACA,MAAM,GAAGA,MAAM;EACxB;EACA;AACJ;AACA;EACI4T,kBAAkBA,CAACnG,WAAW,EAAEvN,cAAc,EAAE;IAC5C,IAAI,CAACuN,WAAW,CAAC3N,KAAK,EAAE;MACpB,MAAM,IAAIlB,uBAAuB,CAAE,6BAA4B,GAC1D,+DAA8D,CAAC;IACxE;IACA,OAAO,IAAI+T,eAAe,CAAC,IAAI,CAAC3S,MAAM,EAAEyN,WAAW,EAAEvN,cAAc,CAAC;EACxE;EACA;AACJ;AACA;EACI2T,mCAAmCA,CAAC7F,aAAa,EAAEP,WAAW,EAAEvN,cAAc,EAAE;IAC5E,IAAI,CAAC8N,aAAa,CAACnK,IAAI,EAAE;MACrB,MAAM,IAAIvE,mCAAmC,CAAC,6CAA6C,CAAC;IAChG;IACA,IAAI,CAAC0O,aAAa,CAAClO,KAAK,EAAE;MACtB,MAAM,IAAIR,mCAAmC,CAAC,8CAA8C,CAAC;IACjG;IACA;AACR;AACA;AACA;IACQ,MAAMwU,oBAAoB,GAAG,CAAC,OAAO,EAAE,mBAAmB,CAAC;IAC3D,KAAK,MAAMxE,GAAG,IAAIwE,oBAAoB,EAAE;MACpC,IAAI,CAACrG,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAAC6B,GAAG,CAAC,KAC3EtB,aAAa,CAACsB,GAAG,CAAC,IAClB,CAAC7B,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAAC6B,GAAG,CAAC,MAAMtB,aAAa,CAACsB,GAAG,CAAC,EAAE;QACrG,IAAIA,GAAG,KAAK,OAAO,EAAE;UACjB,MAAMyE,eAAe,GAAGtG,WAAW,CAAC3N,KAAK,CAACkU,UAAU,CAAC,SAAS,CAAC,GACzDvG,WAAW,CAAC3N,KAAK,CAACmU,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,GACxCxG,WAAW,CAAC3N,KAAK;UACvB,MAAMoU,iBAAiB,GAAGlG,aAAa,CAAClO,KAAK,CAACkU,UAAU,CAAC,SAAS,CAAC,GAC7DhG,aAAa,CAAClO,KAAK,CAACmU,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,GAC1CjG,aAAa,CAAClO,KAAK;UACzB,IAAIiU,eAAe,KAAKG,iBAAiB,EAAE;YACvC;UACJ;QACJ;QACA,MAAM,IAAI5U,mCAAmC,CAAE,wBAAuBgQ,GAAI,4BAA2B,GAChG,KAAI7B,WAAW,CAAC6B,GAAG,CAAE,wBAAuBtB,aAAa,CAACsB,GAAG,CAAE,GAAE,CAAC;MAC3E;IACJ;IACA,MAAM6E,oBAAoB,GAAG3R,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEgL,WAAW,CAAC,EAAE;MAAE3N,KAAK,EAAEkO,aAAa,CAAClO,KAAK;MAAE+N,KAAK,EAAEG,aAAa,CAACH,KAAK;MAAEC,UAAU,EAAEE,aAAa,CAACF,UAAU;MAAEC,iBAAiB,EAAEC,aAAa,CAACD,iBAAiB;MAAEC;IAAc,CAAC,CAAC;IAC/O,OAAO,IAAI2E,eAAe,CAAC,IAAI,CAAC3S,MAAM,EAAEmU,oBAAoB,EAAEjU,cAAc,CAAC;EACjF;AACJ;AAEA,SAAS3B,WAAW,EAAEmR,WAAW,EAAE/Q,oBAAoB,EAAEV,sBAAsB,EAAEO,YAAY,EAAEE,mBAAmB,EAAEiU,eAAe,EAAEgB,kBAAkB,EAAEpU,4BAA4B,EAAEX,uBAAuB,EAAEM,4BAA4B,EAAEI,mCAAmC,EAAEN,+BAA+B,EAAEX,kBAAkB,EAAED,YAAY,EAAEE,eAAe,EAAEJ,OAAO,EAAEC,cAAc,EAAEH,UAAU,EAAES,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}