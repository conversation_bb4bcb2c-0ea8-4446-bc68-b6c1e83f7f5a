import { Component } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { ProjetService } from '@app/services/projets.service';

@Component({
  selector: 'app-add-project',
  templateUrl: './add-project.component.html',
  styleUrls: ['./add-project.component.css'],
})
export class AddProjectComponent {
  projetForm: FormGroup;
  selectedFiles: File[] = [];
  isSubmitting = false;

  constructor(
    private fb: FormBuilder,
    private projetService: ProjetService,
    private router: Router
  ) {
    this.projetForm = this.fb.group({
      titre: ['', Validators.required],
      description: [''],
      dateLimite: ['', Validators.required],
      fichiers: [null],
      groupe: ['', Validators.required], // ← champ pour l'ID du groupe
    });
  }

  onFileChange(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files) {
      this.selectedFiles = Array.from(input.files);
    }
  }

  onSubmit(): void {
    if (this.projetForm.invalid) return;

    this.isSubmitting = true;
    console.log('Soumission du formulaire de projet');

    const formData = new FormData();
    formData.append('titre', this.projetForm.value.titre);
    formData.append('description', this.projetForm.value.description || '');
    formData.append('dateLimite', this.projetForm.value.dateLimite);
    formData.append('groupe', this.projetForm.value.groupe);

    // Ajouter l'ID du professeur (utilisateur connecté)
   
add-project.component.ts:67 
            
            
           POST http://localhost:3000/api/projets/create 404 (Not Found)
scheduleTask @ zone.js:2645
scheduleTask @ zone.js:389
onScheduleTask @ core.mjs:10752
scheduleTask @ zone.js:382
onScheduleTask @ zone.js:279
scheduleTask @ zone.js:382
scheduleTask @ zone.js:216
scheduleMacroTask @ zone.js:239
scheduleMacroTaskWithCurrentZone @ zone.js:672
(anonymous) @ zone.js:2678
proto.<computed> @ zone.js:962
(anonymous) @ http.mjs:2190
_trySubscribe @ Observable.js:37
(anonymous) @ Observable.js:31
errorContext @ errorContext.js:19
subscribe @ Observable.js:22
source.subscribe.isComplete @ switchMap.js:14
OperatorSubscriber._next @ OperatorSubscriber.js:13
next @ Subscriber.js:31
(anonymous) @ innerFrom.js:51
_trySubscribe @ Observable.js:37
(anonymous) @ Observable.js:31
errorContext @ errorContext.js:19
subscribe @ Observable.js:22
(anonymous) @ switchMap.js:10
(anonymous) @ lift.js:10
(anonymous) @ Observable.js:26
errorContext @ errorContext.js:19
subscribe @ Observable.js:22
(anonymous) @ finalize.js:5
(anonymous) @ lift.js:10
(anonymous) @ Observable.js:26
errorContext @ errorContext.js:19
subscribe @ Observable.js:22
(anonymous) @ finalize.js:5
(anonymous) @ lift.js:10
(anonymous) @ Observable.js:26
errorContext @ errorContext.js:19
subscribe @ Observable.js:22
doInnerSub @ mergeInternals.js:19
outerNext @ mergeInternals.js:14
OperatorSubscriber._next @ OperatorSubscriber.js:13
next @ Subscriber.js:31
(anonymous) @ innerFrom.js:51
_trySubscribe @ Observable.js:37
(anonymous) @ Observable.js:31
errorContext @ errorContext.js:19
subscribe @ Observable.js:22
mergeInternals @ mergeInternals.js:50
(anonymous) @ mergeMap.js:13
(anonymous) @ lift.js:10
(anonymous) @ Observable.js:26
errorContext @ errorContext.js:19
subscribe @ Observable.js:22
(anonymous) @ filter.js:6
(anonymous) @ lift.js:10
(anonymous) @ Observable.js:26
errorContext @ errorContext.js:19
subscribe @ Observable.js:22
(anonymous) @ map.js:6
(anonymous) @ lift.js:10
(anonymous) @ Observable.js:26
errorContext @ errorContext.js:19
subscribe @ Observable.js:22
(anonymous) @ tap.js:15
(anonymous) @ lift.js:10
(anonymous) @ Observable.js:26
errorContext @ errorContext.js:19
subscribe @ Observable.js:22
(anonymous) @ catchError.js:9
(anonymous) @ lift.js:10
(anonymous) @ Observable.js:26
errorContext @ errorContext.js:19
subscribe @ Observable.js:22
onSubmit @ add-project.component.ts:67
AddProjectComponent_Template_form_ngSubmit_6_listener @ add-project.component.html:15
executeListenerWithErrorHandling @ core.mjs:16795
wrapListenerIn_markDirtyAndPreventDefault @ core.mjs:16828
next @ Subscriber.js:91
_next @ Subscriber.js:60
next @ Subscriber.js:31
(anonymous) @ Subject.js:34
errorContext @ errorContext.js:19
next @ Subject.js:27
emit @ core.mjs:10670
onSubmit @ forms.mjs:4878
FormGroupDirective_submit_HostBindingHandler @ forms.mjs:4962
executeListenerWithErrorHandling @ core.mjs:16795
wrapListenerIn_markDirtyAndPreventDefault @ core.mjs:16828
(anonymous) @ platform-browser.mjs:665
invokeTask @ zone.js:402
(anonymous) @ core.mjs:10757
onInvokeTask @ core.mjs:10757
invokeTask @ zone.js:401
onInvokeTask @ core.mjs:11070
invokeTask @ zone.js:401
runTask @ zone.js:173
invokeTask @ zone.js:483
invokeTask @ zone.js:1631
globalCallback @ zone.js:1662
globalZoneAwareCallback @ zone.js:1695
Zone - HTMLFormElement.addEventListener:submit
onScheduleTask @ core.mjs:10751
scheduleTask @ zone.js:382
onScheduleTask @ zone.js:279
scheduleTask @ zone.js:382
scheduleTask @ zone.js:216
scheduleEventTask @ zone.js:242
(anonymous) @ zone.js:1934
addEventListener @ platform-browser.mjs:765
addEventListener @ platform-browser.mjs:207
listen @ platform-browser.mjs:645
listen @ animations.mjs:305
listenerInternal @ core.mjs:16757
ɵɵlistener @ core.mjs:16638
FormGroupDirective_HostBindings @ forms.mjs:65
invokeHostBindingsInCreationMode @ core.mjs:12669
invokeDirectivesHostBindings @ core.mjs:12652
createDirectivesInstances @ core.mjs:12047
ɵɵelementStart @ core.mjs:16336
AddProjectComponent_Template @ add-project.component.html:12
executeTemplate @ core.mjs:12003
renderView @ core.mjs:13201
renderComponent @ core.mjs:13148
renderChildComponents @ core.mjs:13246
renderView @ core.mjs:13226
create @ core.mjs:14138
createComponent @ core.mjs:24450
activateWith @ router.mjs:2481
initializeOutletWithName @ router.mjs:2411
ngOnInit @ router.mjs:2394
callHookInternal @ core.mjs:4024
callHook @ core.mjs:4051
callHooks @ core.mjs:4006
executeInitAndCheckHooks @ core.mjs:3956
selectIndexInternal @ core.mjs:11780
ɵɵadvance @ core.mjs:11763
AdminLayoutComponent_Template @ admin-layout.component.html:761
executeTemplate @ core.mjs:12003
refreshView @ core.mjs:13498
detectChangesInView @ core.mjs:13663
detectChangesInComponent @ core.mjs:13638
detectChangesInChildComponents @ core.mjs:13676
refreshView @ core.mjs:13548
detectChangesInView @ core.mjs:13663
detectChangesInEmbeddedViews @ core.mjs:13606
refreshView @ core.mjs:13522
detectChangesInView @ core.mjs:13663
detectChangesInComponent @ core.mjs:13638
detectChangesInChildComponents @ core.mjs:13676
refreshView @ core.mjs:13548
detectChangesInternal @ core.mjs:13437
detectChanges @ core.mjs:13954
tick @ core.mjs:28735
(anonymous) @ core.mjs:28895
invoke @ zone.js:368
onInvoke @ core.mjs:11083
invoke @ zone.js:367
run @ zone.js:129
run @ core.mjs:10934
next @ core.mjs:28894
next @ Subscriber.js:91
_next @ Subscriber.js:60
next @ Subscriber.js:31
(anonymous) @ Subject.js:34
errorContext @ errorContext.js:19
next @ Subject.js:27
emit @ core.mjs:10670
checkStable @ core.mjs:11002
onHasTask @ core.mjs:11100
hasTask @ zone.js:422
_updateTaskCount @ zone.js:443
_updateTaskCount @ zone.js:272
runTask @ zone.js:190
drainMicroTaskQueue @ zone.js:581
Promise.then
nativeScheduleMicroTask @ zone.js:557
scheduleMicroTask @ zone.js:568
scheduleTask @ zone.js:392
onScheduleTask @ core.mjs:10752
scheduleTask @ zone.js:382
onScheduleTask @ zone.js:279
scheduleTask @ zone.js:382
scheduleTask @ zone.js:216
scheduleMicroTask @ zone.js:236
scheduleResolveOrReject @ zone.js:1247
resolvePromise @ zone.js:1184
(anonymous) @ zone.js:1100
(anonymous) @ zone.js:1116
webpackJsonpCallback @ jsonp chunk loading:77
(anonymous) @ src_app_views_admin_projects_projects_module_ts.js:2Understand this error
projets.service.ts:54 Erreur lors de l'ajout du projet: HttpErrorResponse {headers: HttpHeaders, status: 404, statusText: 'Not Found', url: 'http://localhost:3000/api/projets/create', ok: false, …}
(anonymous) @ projets.service.ts:54
(anonymous) @ catchError.js:10
OperatorSubscriber._error @ OperatorSubscriber.js:23
error @ Subscriber.js:40
source.subscribe._a @ tap.js:28
OperatorSubscriber._error @ OperatorSubscriber.js:23
error @ Subscriber.js:40
_error @ Subscriber.js:64
error @ Subscriber.js:40
_error @ Subscriber.js:64
error @ Subscriber.js:40
_error @ Subscriber.js:64
error @ Subscriber.js:40
_error @ Subscriber.js:64
error @ Subscriber.js:40
onLoad @ http.mjs:2103
invokeTask @ zone.js:402
(anonymous) @ core.mjs:10757
onInvokeTask @ core.mjs:10757
invokeTask @ zone.js:401
onInvokeTask @ core.mjs:11070
invokeTask @ zone.js:401
runTask @ zone.js:173
invokeTask @ zone.js:483
invokeTask @ zone.js:1631
globalCallback @ zone.js:1674
globalZoneAwareCallback @ zone.js:1695
Zone - XMLHttpRequest.addEventListener:load
onScheduleTask @ core.mjs:10751
scheduleTask @ zone.js:382
onScheduleTask @ zone.js:279
scheduleTask @ zone.js:382
scheduleTask @ zone.js:216
scheduleEventTask @ zone.js:242
(anonymous) @ zone.js:1934
(anonymous) @ http.mjs:2176
_trySubscribe @ Observable.js:37
(anonymous) @ Observable.js:31
errorContext @ errorContext.js:19
subscribe @ Observable.js:22
source.subscribe.isComplete @ switchMap.js:14
OperatorSubscriber._next @ OperatorSubscriber.js:13
next @ Subscriber.js:31
(anonymous) @ innerFrom.js:51
_trySubscribe @ Observable.js:37
(anonymous) @ Observable.js:31
errorContext @ errorContext.js:19
subscribe @ Observable.js:22
(anonymous) @ switchMap.js:10
(anonymous) @ lift.js:10
(anonymous) @ Observable.js:26
errorContext @ errorContext.js:19
subscribe @ Observable.js:22
(anonymous) @ finalize.js:5
(anonymous) @ lift.js:10
(anonymous) @ Observable.js:26
errorContext @ errorContext.js:19
subscribe @ Observable.js:22
(anonymous) @ finalize.js:5
(anonymous) @ lift.js:10
(anonymous) @ Observable.js:26
errorContext @ errorContext.js:19
subscribe @ Observable.js:22
doInnerSub @ mergeInternals.js:19
outerNext @ mergeInternals.js:14
OperatorSubscriber._next @ OperatorSubscriber.js:13
next @ Subscriber.js:31
(anonymous) @ innerFrom.js:51
_trySubscribe @ Observable.js:37
(anonymous) @ Observable.js:31
errorContext @ errorContext.js:19
subscribe @ Observable.js:22
mergeInternals @ mergeInternals.js:50
(anonymous) @ mergeMap.js:13
(anonymous) @ lift.js:10
(anonymous) @ Observable.js:26
errorContext @ errorContext.js:19
subscribe @ Observable.js:22
(anonymous) @ filter.js:6
(anonymous) @ lift.js:10
(anonymous) @ Observable.js:26
errorContext @ errorContext.js:19
subscribe @ Observable.js:22
(anonymous) @ map.js:6
(anonymous) @ lift.js:10
(anonymous) @ Observable.js:26
errorContext @ errorContext.js:19
subscribe @ Observable.js:22
(anonymous) @ tap.js:15
(anonymous) @ lift.js:10
(anonymous) @ Observable.js:26
errorContext @ errorContext.js:19
subscribe @ Observable.js:22
(anonymous) @ catchError.js:9
(anonymous) @ lift.js:10
(anonymous) @ Observable.js:26
errorContext @ errorContext.js:19
subscribe @ Observable.js:22
onSubmit @ add-project.component.ts:67
AddProjectComponent_Template_form_ngSubmit_6_listener @ add-project.component.html:15
executeListenerWithErrorHandling @ core.mjs:16795
wrapListenerIn_markDirtyAndPreventDefault @ core.mjs:16828
next @ Subscriber.js:91
_next @ Subscriber.js:60
next @ Subscriber.js:31
(anonymous) @ Subject.js:34
errorContext @ errorContext.js:19
next @ Subject.js:27
emit @ core.mjs:10670
onSubmit @ forms.mjs:4878
FormGroupDirective_submit_HostBindingHandler @ forms.mjs:4962
executeListenerWithErrorHandling @ core.mjs:16795
wrapListenerIn_markDirtyAndPreventDefault @ core.mjs:16828
(anonymous) @ platform-browser.mjs:665
invokeTask @ zone.js:402
(anonymous) @ core.mjs:10757
onInvokeTask @ core.mjs:10757
invokeTask @ zone.js:401
onInvokeTask @ core.mjs:11070
invokeTask @ zone.js:401
runTask @ zone.js:173
invokeTask @ zone.js:483
invokeTask @ zone.js:1631
globalCallback @ zone.js:1662
globalZoneAwareCallback @ zone.js:1695
Zone - HTMLFormElement.addEventListener:submit
onScheduleTask @ core.mjs:10751
scheduleTask @ zone.js:382
onScheduleTask @ zone.js:279
scheduleTask @ zone.js:382
scheduleTask @ zone.js:216
scheduleEventTask @ zone.js:242
(anonymous) @ zone.js:1934
addEventListener @ platform-browser.mjs:765
addEventListener @ platform-browser.mjs:207
listen @ platform-browser.mjs:645
listen @ animations.mjs:305
listenerInternal @ core.mjs:16757
ɵɵlistener @ core.mjs:16638
FormGroupDirective_HostBindings @ forms.mjs:65
invokeHostBindingsInCreationMode @ core.mjs:12669
invokeDirectivesHostBindings @ core.mjs:12652
createDirectivesInstances @ core.mjs:12047
ɵɵelementStart @ core.mjs:16336
AddProjectComponent_Template @ add-project.component.html:12
executeTemplate @ core.mjs:12003
renderView @ core.mjs:13201
renderComponent @ core.mjs:13148
renderChildComponents @ core.mjs:13246
renderView @ core.mjs:13226
create @ core.mjs:14138
createComponent @ core.mjs:24450
activateWith @ router.mjs:2481
initializeOutletWithName @ router.mjs:2411
ngOnInit @ router.mjs:2394
callHookInternal @ core.mjs:4024
callHook @ core.mjs:4051
callHooks @ core.mjs:4006
executeInitAndCheckHooks @ core.mjs:3956
selectIndexInternal @ core.mjs:11780
ɵɵadvance @ core.mjs:11763
AdminLayoutComponent_Template @ admin-layout.component.html:761
executeTemplate @ core.mjs:12003
refreshView @ core.mjs:13498
detectChangesInView @ core.mjs:13663
detectChangesInComponent @ core.mjs:13638
detectChangesInChildComponents @ core.mjs:13676
refreshView @ core.mjs:13548
detectChangesInView @ core.mjs:13663
detectChangesInEmbeddedViews @ core.mjs:13606
refreshView @ core.mjs:13522
detectChangesInView @ core.mjs:13663
detectChangesInComponent @ core.mjs:13638
detectChangesInChildComponents @ core.mjs:13676
refreshView @ core.mjs:13548
detectChangesInternal @ core.mjs:13437
detectChanges @ core.mjs:13954
tick @ core.mjs:28735
(anonymous) @ core.mjs:28895
invoke @ zone.js:368
onInvoke @ core.mjs:11083
invoke @ zone.js:367
run @ zone.js:129
run @ core.mjs:10934
next @ core.mjs:28894
next @ Subscriber.js:91
_next @ Subscriber.js:60
next @ Subscriber.js:31
(anonymous) @ Subject.js:34
errorContext @ errorContext.js:19
next @ Subject.js:27
emit @ core.mjs:10670
checkStable @ core.mjs:11002
onHasTask @ core.mjs:11100
hasTask @ zone.js:422
_updateTaskCount @ zone.js:443
_updateTaskCount @ zone.js:272
runTask @ zone.js:190
drainMicroTaskQueue @ zone.js:581
Promise.then
nativeScheduleMicroTask @ zone.js:557
scheduleMicroTask @ zone.js:568
scheduleTask @ zone.js:392
onScheduleTask @ core.mjs:10752
scheduleTask @ zone.js:382
onScheduleTask @ zone.js:279
scheduleTask @ zone.js:382
scheduleTask @ zone.js:216
scheduleMicroTask @ zone.js:236
scheduleResolveOrReject @ zone.js:1247
resolvePromise @ zone.js:1184
(anonymous) @ zone.js:1100
(anonymous) @ zone.js:1116
webpackJsonpCallback @ jsonp chunk loading:77
(anonymous) @ src_app_views_admin_projects_projects_module_ts.js:2Understand this error
add-project.component.ts:74 Erreur lors de l'ajout du projet: HttpErrorResponse {headers: HttpHeaders, status: 404, statusText: 'Not Found', url: 'http://localhost:3000/api/projets/create', ok: false, …}
        console.error("Erreur lors de l'ajout du projet:", err);
        alert(
          "Erreur lors de l'ajout du projet: " +
            (err.error?.message || err.message || 'Erreur inconnue')
        );
        this.isSubmitting = false;
      },
      complete: () => {
        this.isSubmitting = false;
      },
    });
  }
}
