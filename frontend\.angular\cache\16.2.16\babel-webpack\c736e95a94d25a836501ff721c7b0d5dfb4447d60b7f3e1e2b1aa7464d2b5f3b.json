{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@app/services/projects.service\";\nimport * as i4 from \"src/app/services/rendus.service\";\nimport * as i5 from \"src/app/services/authuser.service\";\nimport * as i6 from \"@angular/common\";\nfunction ProjectSubmissionComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5);\n    i0.ɵɵelement(1, \"div\", 6);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectSubmissionComponent_div_5_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵtext(1, \" La description est requise et doit contenir au moins 10 caract\\u00E8res. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectSubmissionComponent_div_5_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵtext(1, \" Veuillez s\\u00E9lectionner au moins un fichier. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectSubmissionComponent_div_5_div_28_li_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const file_r8 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", file_r8.name, \" (\", (file_r8.size / 1024).toFixed(2), \" KB) \");\n  }\n}\nfunction ProjectSubmissionComponent_div_5_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"p\", 9);\n    i0.ɵɵtext(2, \"Fichiers s\\u00E9lectionn\\u00E9s:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"ul\", 23);\n    i0.ɵɵtemplate(4, ProjectSubmissionComponent_div_5_div_28_li_4_Template, 2, 2, \"li\", 24);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.selectedFiles);\n  }\n}\nfunction ProjectSubmissionComponent_div_5_span_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Soumettre le projet\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectSubmissionComponent_div_5_span_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Soumission en cours...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectSubmissionComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 7)(2, \"h2\", 8);\n    i0.ɵɵtext(3, \" Informations sur le projet \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\")(5, \"span\", 9);\n    i0.ɵɵtext(6, \"Titre:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\")(9, \"span\", 9);\n    i0.ɵɵtext(10, \"Description:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"p\")(13, \"span\", 9);\n    i0.ɵɵtext(14, \"Date limite:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(15);\n    i0.ɵɵpipe(16, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"form\", 10);\n    i0.ɵɵlistener(\"ngSubmit\", function ProjectSubmissionComponent_div_5_Template_form_ngSubmit_17_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.onSubmit());\n    });\n    i0.ɵɵelementStart(18, \"div\", 11)(19, \"label\", 12);\n    i0.ɵɵtext(20, \"Description de votre travail\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(21, \"textarea\", 13);\n    i0.ɵɵtemplate(22, ProjectSubmissionComponent_div_5_div_22_Template, 2, 0, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 15)(24, \"label\", 16);\n    i0.ɵɵtext(25, \"Fichiers du projet\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"input\", 17);\n    i0.ɵɵlistener(\"change\", function ProjectSubmissionComponent_div_5_Template_input_change_26_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.onFileChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(27, ProjectSubmissionComponent_div_5_div_27_Template, 2, 0, \"div\", 14);\n    i0.ɵɵtemplate(28, ProjectSubmissionComponent_div_5_div_28_Template, 5, 1, \"div\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\", 19)(30, \"button\", 20);\n    i0.ɵɵtemplate(31, ProjectSubmissionComponent_div_5_span_31_Template, 2, 0, \"span\", 4);\n    i0.ɵɵtemplate(32, ProjectSubmissionComponent_div_5_span_32_Template, 2, 0, \"span\", 4);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    let tmp_4_0;\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.projet.titre, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.projet.description, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(16, 10, ctx_r1.projet.dateLimite, \"dd/MM/yyyy\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.submissionForm);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx_r1.submissionForm.get(\"description\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx_r1.submissionForm.get(\"description\")) == null ? null : tmp_4_0.touched));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedFiles.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedFiles.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.submissionForm.invalid || ctx_r1.selectedFiles.length === 0 || ctx_r1.isSubmitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isSubmitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isSubmitting);\n  }\n}\n// Composant pour soumettre un projet\nexport class ProjectSubmissionComponent {\n  constructor(fb, route, router, projetService, rendusService, authService) {\n    this.fb = fb;\n    this.route = route;\n    this.router = router;\n    this.projetService = projetService;\n    this.rendusService = rendusService;\n    this.authService = authService;\n    this.projetId = '';\n    this.selectedFiles = [];\n    this.isLoading = true;\n    this.isSubmitting = false;\n    this.submissionForm = this.fb.group({\n      description: ['', [Validators.required, Validators.minLength(10)]]\n    });\n  }\n  ngOnInit() {\n    this.projetId = this.route.snapshot.paramMap.get('id') || '';\n    this.loadProjetDetails();\n  }\n  loadProjetDetails() {\n    this.isLoading = true;\n    this.projetService.getProjetById(this.projetId).subscribe({\n      next: projet => {\n        this.projet = projet;\n        this.isLoading = false;\n      },\n      error: err => {\n        console.error('Erreur lors du chargement du projet', err);\n        this.isLoading = false;\n        this.router.navigate(['/projects']);\n      }\n    });\n  }\n  onFileChange(event) {\n    const input = event.target;\n    if (input.files) {\n      this.selectedFiles = Array.from(input.files);\n    }\n  }\n  onSubmit() {\n    if (this.submissionForm.invalid || this.selectedFiles.length === 0) {\n      return;\n    }\n    this.isSubmitting = true;\n    const formData = new FormData();\n    formData.append('projet', this.projetId);\n    formData.append('etudiant', this.authService.getCurrentUserId() || '');\n    formData.append('description', this.submissionForm.value.description);\n    this.selectedFiles.forEach(file => {\n      formData.append('fichiers', file);\n    });\n    this.rendusService.submitRendu(formData).subscribe({\n      next: response => {\n        alert('Votre projet a été soumis avec succès');\n        this.router.navigate(['/projects']);\n      },\n      error: err => {\n        console.error('Erreur lors de la soumission du projet', err);\n        alert('Une erreur est survenue lors de la soumission du projet');\n        this.isSubmitting = false;\n      }\n    });\n  }\n  static {\n    this.ɵfac = function ProjectSubmissionComponent_Factory(t) {\n      return new (t || ProjectSubmissionComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.ProjetService), i0.ɵɵdirectiveInject(i4.RendusService), i0.ɵɵdirectiveInject(i5.AuthuserService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProjectSubmissionComponent,\n      selectors: [[\"app-project-submission\"]],\n      decls: 6,\n      vars: 2,\n      consts: [[1, \"min-h-screen\", \"bg-[#edf1f4]\", \"p-4\", \"md:p-6\"], [1, \"max-w-4xl\", \"mx-auto\", \"bg-white\", \"rounded-xl\", \"shadow-md\", \"p-6\"], [1, \"text-2xl\", \"font-bold\", \"mb-6\", \"text-[#4f5fad]\"], [\"class\", \"flex justify-center my-8\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"flex\", \"justify-center\", \"my-8\"], [1, \"animate-spin\", \"rounded-full\", \"h-12\", \"w-12\", \"border-t-2\", \"border-b-2\", \"border-[#4f5fad]\"], [1, \"mb-6\", \"p-4\", \"bg-[#edf1f4]\", \"rounded-lg\"], [1, \"text-xl\", \"font-semibold\", \"mb-2\", \"text-[#4f5fad]\"], [1, \"font-medium\", \"text-[#6d6870]\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"mb-4\"], [\"for\", \"description\", 1, \"block\", \"text-[#6d6870]\", \"font-medium\", \"mb-2\"], [\"id\", \"description\", \"formControlName\", \"description\", \"rows\", \"5\", \"placeholder\", \"D\\u00E9crivez votre travail, les fonctionnalit\\u00E9s impl\\u00E9ment\\u00E9es, les difficult\\u00E9s rencontr\\u00E9es, etc.\", 1, \"w-full\", \"px-3\", \"py-2\", \"border\", \"border-[#bdc6cc]\", \"rounded\", \"focus:outline-none\", \"focus:ring-1\", \"focus:ring-[#4f5fad]/20\", \"focus:border-[#4f5fad]\"], [\"class\", \"text-[#ff6b69] mt-1\", 4, \"ngIf\"], [1, \"mb-6\"], [\"for\", \"fichiers\", 1, \"block\", \"text-[#6d6870]\", \"font-medium\", \"mb-2\"], [\"type\", \"file\", \"id\", \"fichiers\", \"multiple\", \"\", 1, \"w-full\", \"px-3\", \"py-2\", \"border\", \"border-[#bdc6cc]\", \"rounded\", \"focus:outline-none\", \"focus:ring-1\", \"focus:ring-[#4f5fad]/20\", \"focus:border-[#4f5fad]\", 3, \"change\"], [\"class\", \"mt-2\", 4, \"ngIf\"], [1, \"flex\", \"justify-end\"], [\"type\", \"submit\", 1, \"px-6\", \"py-2\", \"bg-[#4f5fad]\", \"text-white\", \"rounded\", \"hover:bg-[#3d4a85]\", \"transition-colors\", \"disabled:opacity-50\", 3, \"disabled\"], [1, \"text-[#ff6b69]\", \"mt-1\"], [1, \"mt-2\"], [1, \"list-disc\", \"pl-5\"], [4, \"ngFor\", \"ngForOf\"]],\n      template: function ProjectSubmissionComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\", 2);\n          i0.ɵɵtext(3, \"Soumettre un projet\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(4, ProjectSubmissionComponent_div_4_Template, 2, 0, \"div\", 3);\n          i0.ɵɵtemplate(5, ProjectSubmissionComponent_div_5_Template, 33, 13, \"div\", 4);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.projet && !ctx.isLoading);\n        }\n      },\n      dependencies: [i6.NgForOf, i6.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i6.DatePipe],\n      styles: [\"\\n\\n.submission-form[_ngcontent-%COMP%] {\\n  max-width: 800px;\\n  margin: 0 auto;\\n}\\n\\n.form-section[_ngcontent-%COMP%] {\\n  margin-bottom: 2rem;\\n}\\n\\n.file-upload[_ngcontent-%COMP%] {\\n  border: 2px dashed #ccc;\\n  padding: 1.5rem;\\n  text-align: center;\\n  border-radius: 0.5rem;\\n  margin-bottom: 1rem;\\n}\\n\\n.file-upload[_ngcontent-%COMP%]:hover {\\n  border-color: #6366f1;\\n}\\n\\n.file-list[_ngcontent-%COMP%] {\\n  margin-top: 1rem;\\n}\\n\\n.file-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 0.5rem;\\n  background-color: #f9fafb;\\n  border-radius: 0.25rem;\\n  margin-bottom: 0.5rem;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInByb2plY3Qtc3VibWlzc2lvbi5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLHFEQUFxRDtBQUNyRDtFQUNFLGdCQUFnQjtFQUNoQixjQUFjO0FBQ2hCOztBQUVBO0VBQ0UsbUJBQW1CO0FBQ3JCOztBQUVBO0VBQ0UsdUJBQXVCO0VBQ3ZCLGVBQWU7RUFDZixrQkFBa0I7RUFDbEIscUJBQXFCO0VBQ3JCLG1CQUFtQjtBQUNyQjs7QUFFQTtFQUNFLHFCQUFxQjtBQUN2Qjs7QUFFQTtFQUNFLGdCQUFnQjtBQUNsQjs7QUFFQTtFQUNFLGFBQWE7RUFDYixtQkFBbUI7RUFDbkIsOEJBQThCO0VBQzlCLGVBQWU7RUFDZix5QkFBeUI7RUFDekIsc0JBQXNCO0VBQ3RCLHFCQUFxQjtBQUN2QiIsImZpbGUiOiJwcm9qZWN0LXN1Ym1pc3Npb24uY29tcG9uZW50LmNzcyIsInNvdXJjZXNDb250ZW50IjpbIi8qIFN0eWxlcyBwb3VyIGxlIGNvbXBvc2FudCBkZSBzb3VtaXNzaW9uIGRlIHByb2pldCAqL1xuLnN1Ym1pc3Npb24tZm9ybSB7XG4gIG1heC13aWR0aDogODAwcHg7XG4gIG1hcmdpbjogMCBhdXRvO1xufVxuXG4uZm9ybS1zZWN0aW9uIHtcbiAgbWFyZ2luLWJvdHRvbTogMnJlbTtcbn1cblxuLmZpbGUtdXBsb2FkIHtcbiAgYm9yZGVyOiAycHggZGFzaGVkICNjY2M7XG4gIHBhZGRpbmc6IDEuNXJlbTtcbiAgdGV4dC1hbGlnbjogY2VudGVyO1xuICBib3JkZXItcmFkaXVzOiAwLjVyZW07XG4gIG1hcmdpbi1ib3R0b206IDFyZW07XG59XG5cbi5maWxlLXVwbG9hZDpob3ZlciB7XG4gIGJvcmRlci1jb2xvcjogIzYzNjZmMTtcbn1cblxuLmZpbGUtbGlzdCB7XG4gIG1hcmdpbi10b3A6IDFyZW07XG59XG5cbi5maWxlLWl0ZW0ge1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XG4gIHBhZGRpbmc6IDAuNXJlbTtcbiAgYmFja2dyb3VuZC1jb2xvcjogI2Y5ZmFmYjtcbiAgYm9yZGVyLXJhZGl1czogMC4yNXJlbTtcbiAgbWFyZ2luLWJvdHRvbTogMC41cmVtO1xufSJdfQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvZnJvbnQvcHJvamVjdHMvcHJvamVjdC1zdWJtaXNzaW9uL3Byb2plY3Qtc3VibWlzc2lvbi5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLHFEQUFxRDtBQUNyRDtFQUNFLGdCQUFnQjtFQUNoQixjQUFjO0FBQ2hCOztBQUVBO0VBQ0UsbUJBQW1CO0FBQ3JCOztBQUVBO0VBQ0UsdUJBQXVCO0VBQ3ZCLGVBQWU7RUFDZixrQkFBa0I7RUFDbEIscUJBQXFCO0VBQ3JCLG1CQUFtQjtBQUNyQjs7QUFFQTtFQUNFLHFCQUFxQjtBQUN2Qjs7QUFFQTtFQUNFLGdCQUFnQjtBQUNsQjs7QUFFQTtFQUNFLGFBQWE7RUFDYixtQkFBbUI7RUFDbkIsOEJBQThCO0VBQzlCLGVBQWU7RUFDZix5QkFBeUI7RUFDekIsc0JBQXNCO0VBQ3RCLHFCQUFxQjtBQUN2QjtBQUNBLHc1Q0FBdzVDIiwic291cmNlc0NvbnRlbnQiOlsiLyogU3R5bGVzIHBvdXIgbGUgY29tcG9zYW50IGRlIHNvdW1pc3Npb24gZGUgcHJvamV0ICovXG4uc3VibWlzc2lvbi1mb3JtIHtcbiAgbWF4LXdpZHRoOiA4MDBweDtcbiAgbWFyZ2luOiAwIGF1dG87XG59XG5cbi5mb3JtLXNlY3Rpb24ge1xuICBtYXJnaW4tYm90dG9tOiAycmVtO1xufVxuXG4uZmlsZS11cGxvYWQge1xuICBib3JkZXI6IDJweCBkYXNoZWQgI2NjYztcbiAgcGFkZGluZzogMS41cmVtO1xuICB0ZXh0LWFsaWduOiBjZW50ZXI7XG4gIGJvcmRlci1yYWRpdXM6IDAuNXJlbTtcbiAgbWFyZ2luLWJvdHRvbTogMXJlbTtcbn1cblxuLmZpbGUtdXBsb2FkOmhvdmVyIHtcbiAgYm9yZGVyLWNvbG9yOiAjNjM2NmYxO1xufVxuXG4uZmlsZS1saXN0IHtcbiAgbWFyZ2luLXRvcDogMXJlbTtcbn1cblxuLmZpbGUtaXRlbSB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcbiAgcGFkZGluZzogMC41cmVtO1xuICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjlmYWZiO1xuICBib3JkZXItcmFkaXVzOiAwLjI1cmVtO1xuICBtYXJnaW4tYm90dG9tOiAwLjVyZW07XG59Il0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate2", "file_r8", "name", "size", "toFixed", "ɵɵtemplate", "ProjectSubmissionComponent_div_5_div_28_li_4_Template", "ɵɵproperty", "ctx_r4", "selectedFiles", "ɵɵlistener", "ProjectSubmissionComponent_div_5_Template_form_ngSubmit_17_listener", "ɵɵrestoreView", "_r10", "ctx_r9", "ɵɵnextContext", "ɵɵresetView", "onSubmit", "ProjectSubmissionComponent_div_5_div_22_Template", "ProjectSubmissionComponent_div_5_Template_input_change_26_listener", "$event", "ctx_r11", "onFileChange", "ProjectSubmissionComponent_div_5_div_27_Template", "ProjectSubmissionComponent_div_5_div_28_Template", "ProjectSubmissionComponent_div_5_span_31_Template", "ProjectSubmissionComponent_div_5_span_32_Template", "ɵɵtextInterpolate1", "ctx_r1", "projet", "titre", "description", "ɵɵpipeBind2", "dateLimite", "submissionForm", "tmp_4_0", "get", "invalid", "touched", "length", "isSubmitting", "ProjectSubmissionComponent", "constructor", "fb", "route", "router", "projetService", "rendusService", "authService", "projetId", "isLoading", "group", "required", "<PERSON><PERSON><PERSON><PERSON>", "ngOnInit", "snapshot", "paramMap", "loadProjetDetails", "getProjetById", "subscribe", "next", "error", "err", "console", "navigate", "event", "input", "target", "files", "Array", "from", "formData", "FormData", "append", "getCurrentUserId", "value", "for<PERSON>ach", "file", "submitRendu", "response", "alert", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "ActivatedRoute", "Router", "i3", "ProjetService", "i4", "RendusService", "i5", "AuthuserService", "selectors", "decls", "vars", "consts", "template", "ProjectSubmissionComponent_Template", "rf", "ctx", "ProjectSubmissionComponent_div_4_Template", "ProjectSubmissionComponent_div_5_Template"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\projects\\project-submission\\project-submission.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\projects\\project-submission\\project-submission.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { ProjetService } from '@app/services/projects.service';\nimport { RendusService } from 'src/app/services/rendus.service';\nimport { AuthuserService } from 'src/app/services/authuser.service';\n\n// Composant pour soumettre un projet\n@Component({\n  selector: 'app-project-submission',\n  templateUrl: './project-submission.component.html',\n  styleUrls: ['./project-submission.component.css'],\n})\nexport class ProjectSubmissionComponent implements OnInit {\n  projetId: string = '';\n  projet: any;\n  submissionForm: FormGroup;\n  selectedFiles: File[] = [];\n  isLoading = true;\n  isSubmitting = false;\n\n  constructor(\n    private fb: FormBuilder,\n    private route: ActivatedRoute,\n    private router: Router,\n    private projetService: ProjetService,\n    private rendusService: RendusService,\n    private authService: AuthuserService\n  ) {\n    this.submissionForm = this.fb.group({\n      description: ['', [Validators.required, Validators.minLength(10)]],\n    });\n  }\n\n  ngOnInit(): void {\n    this.projetId = this.route.snapshot.paramMap.get('id') || '';\n    this.loadProjetDetails();\n  }\n\n  loadProjetDetails(): void {\n    this.isLoading = true;\n    this.projetService.getProjetById(this.projetId).subscribe({\n      next: (projet: any) => {\n        this.projet = projet;\n        this.isLoading = false;\n      },\n      error: (err: Error) => {\n        console.error('Erreur lors du chargement du projet', err);\n        this.isLoading = false;\n        this.router.navigate(['/projects']);\n      },\n    });\n  }\n\n  onFileChange(event: Event): void {\n    const input = event.target as HTMLInputElement;\n    if (input.files) {\n      this.selectedFiles = Array.from(input.files);\n    }\n  }\n\n  onSubmit(): void {\n    if (this.submissionForm.invalid || this.selectedFiles.length === 0) {\n      return;\n    }\n\n    this.isSubmitting = true;\n    const formData = new FormData();\n    formData.append('projet', this.projetId);\n    formData.append('etudiant', this.authService.getCurrentUserId() || '');\n    formData.append('description', this.submissionForm.value.description);\n\n    this.selectedFiles.forEach((file) => {\n      formData.append('fichiers', file);\n    });\n\n    this.rendusService.submitRendu(formData).subscribe({\n      next: (response: any) => {\n        alert('Votre projet a été soumis avec succès');\n        this.router.navigate(['/projects']);\n      },\n      error: (err: Error) => {\n        console.error('Erreur lors de la soumission du projet', err);\n        alert('Une erreur est survenue lors de la soumission du projet');\n        this.isSubmitting = false;\n      },\n    });\n  }\n}\n", "<div class=\"min-h-screen bg-[#edf1f4] p-4 md:p-6\">\n  <div class=\"max-w-4xl mx-auto bg-white rounded-xl shadow-md p-6\">\n    <h1 class=\"text-2xl font-bold mb-6 text-[#4f5fad]\">Soumettre un projet</h1>\n\n    <div *ngIf=\"isLoading\" class=\"flex justify-center my-8\">\n      <div\n        class=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#4f5fad]\"\n      ></div>\n    </div>\n\n    <div *ngIf=\"projet && !isLoading\">\n      <div class=\"mb-6 p-4 bg-[#edf1f4] rounded-lg\">\n        <h2 class=\"text-xl font-semibold mb-2 text-[#4f5fad]\">\n          Informations sur le projet\n        </h2>\n        <p>\n          <span class=\"font-medium text-[#6d6870]\">Titre:</span>\n          {{ projet.titre }}\n        </p>\n        <p>\n          <span class=\"font-medium text-[#6d6870]\">Description:</span>\n          {{ projet.description }}\n        </p>\n        <p>\n          <span class=\"font-medium text-[#6d6870]\">Date limite:</span>\n          {{ projet.dateLimite | date : \"dd/MM/yyyy\" }}\n        </p>\n      </div>\n\n      <form [formGroup]=\"submissionForm\" (ngSubmit)=\"onSubmit()\">\n        <div class=\"mb-4\">\n          <label for=\"description\" class=\"block text-[#6d6870] font-medium mb-2\"\n            >Description de votre travail</label\n          >\n          <textarea\n            id=\"description\"\n            formControlName=\"description\"\n            rows=\"5\"\n            class=\"w-full px-3 py-2 border border-[#bdc6cc] rounded focus:outline-none focus:ring-1 focus:ring-[#4f5fad]/20 focus:border-[#4f5fad]\"\n            placeholder=\"Décrivez votre travail, les fonctionnalités implémentées, les difficultés rencontrées, etc.\"\n          ></textarea>\n          <div\n            *ngIf=\"\n              submissionForm.get('description')?.invalid &&\n              submissionForm.get('description')?.touched\n            \"\n            class=\"text-[#ff6b69] mt-1\"\n          >\n            La description est requise et doit contenir au moins 10 caractères.\n          </div>\n        </div>\n\n        <div class=\"mb-6\">\n          <label for=\"fichiers\" class=\"block text-[#6d6870] font-medium mb-2\"\n            >Fichiers du projet</label\n          >\n          <input\n            type=\"file\"\n            id=\"fichiers\"\n            multiple\n            (change)=\"onFileChange($event)\"\n            class=\"w-full px-3 py-2 border border-[#bdc6cc] rounded focus:outline-none focus:ring-1 focus:ring-[#4f5fad]/20 focus:border-[#4f5fad]\"\n          />\n          <div *ngIf=\"selectedFiles.length === 0\" class=\"text-[#ff6b69] mt-1\">\n            Veuillez sélectionner au moins un fichier.\n          </div>\n          <div *ngIf=\"selectedFiles.length > 0\" class=\"mt-2\">\n            <p class=\"font-medium text-[#6d6870]\">Fichiers sélectionnés:</p>\n            <ul class=\"list-disc pl-5\">\n              <li *ngFor=\"let file of selectedFiles\">\n                {{ file.name }} ({{ (file.size / 1024).toFixed(2) }} KB)\n              </li>\n            </ul>\n          </div>\n        </div>\n\n        <div class=\"flex justify-end\">\n          <button\n            type=\"submit\"\n            [disabled]=\"\n              submissionForm.invalid ||\n              selectedFiles.length === 0 ||\n              isSubmitting\n            \"\n            class=\"px-6 py-2 bg-[#4f5fad] text-white rounded hover:bg-[#3d4a85] transition-colors disabled:opacity-50\"\n          >\n            <span *ngIf=\"!isSubmitting\">Soumettre le projet</span>\n            <span *ngIf=\"isSubmitting\">Soumission en cours...</span>\n          </button>\n        </div>\n      </form>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;;;ICG/DC,EAAA,CAAAC,cAAA,aAAwD;IACtDD,EAAA,CAAAE,SAAA,aAEO;IACTF,EAAA,CAAAG,YAAA,EAAM;;;;;IAiCAH,EAAA,CAAAC,cAAA,cAMC;IACCD,EAAA,CAAAI,MAAA,iFACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;;IAcNH,EAAA,CAAAC,cAAA,cAAoE;IAClED,EAAA,CAAAI,MAAA,wDACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;;IAIFH,EAAA,CAAAC,cAAA,SAAuC;IACrCD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;;;;IADHH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,OAAA,CAAAC,IAAA,SAAAD,OAAA,CAAAE,IAAA,SAAAC,OAAA,aACF;;;;;IALJV,EAAA,CAAAC,cAAA,cAAmD;IACXD,EAAA,CAAAI,MAAA,uCAAsB;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAChEH,EAAA,CAAAC,cAAA,aAA2B;IACzBD,EAAA,CAAAW,UAAA,IAAAC,qDAAA,iBAEK;IACPZ,EAAA,CAAAG,YAAA,EAAK;;;;IAHkBH,EAAA,CAAAK,SAAA,GAAgB;IAAhBL,EAAA,CAAAa,UAAA,YAAAC,MAAA,CAAAC,aAAA,CAAgB;;;;;IAiBvCf,EAAA,CAAAC,cAAA,WAA4B;IAAAD,EAAA,CAAAI,MAAA,0BAAmB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;IACtDH,EAAA,CAAAC,cAAA,WAA2B;IAAAD,EAAA,CAAAI,MAAA,6BAAsB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;;IA7EhEH,EAAA,CAAAC,cAAA,UAAkC;IAG5BD,EAAA,CAAAI,MAAA,mCACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,QAAG;IACwCD,EAAA,CAAAI,MAAA,aAAM;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACtDH,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAC,cAAA,QAAG;IACwCD,EAAA,CAAAI,MAAA,oBAAY;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAC5DH,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAC,cAAA,SAAG;IACwCD,EAAA,CAAAI,MAAA,oBAAY;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAC5DH,EAAA,CAAAI,MAAA,IACF;;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAGNH,EAAA,CAAAC,cAAA,gBAA2D;IAAxBD,EAAA,CAAAgB,UAAA,sBAAAC,oEAAA;MAAAjB,EAAA,CAAAkB,aAAA,CAAAC,IAAA;MAAA,MAAAC,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAA,OAAYrB,EAAA,CAAAsB,WAAA,CAAAF,MAAA,CAAAG,QAAA,EAAU;IAAA,EAAC;IACxDvB,EAAA,CAAAC,cAAA,eAAkB;IAEbD,EAAA,CAAAI,MAAA,oCAA4B;IAAAJ,EAAA,CAAAG,YAAA,EAC9B;IACDH,EAAA,CAAAE,SAAA,oBAMY;IACZF,EAAA,CAAAW,UAAA,KAAAa,gDAAA,kBAQM;IACRxB,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,eAAkB;IAEbD,EAAA,CAAAI,MAAA,0BAAkB;IAAAJ,EAAA,CAAAG,YAAA,EACpB;IACDH,EAAA,CAAAC,cAAA,iBAME;IAFAD,EAAA,CAAAgB,UAAA,oBAAAS,mEAAAC,MAAA;MAAA1B,EAAA,CAAAkB,aAAA,CAAAC,IAAA;MAAA,MAAAQ,OAAA,GAAA3B,EAAA,CAAAqB,aAAA;MAAA,OAAUrB,EAAA,CAAAsB,WAAA,CAAAK,OAAA,CAAAC,YAAA,CAAAF,MAAA,CAAoB;IAAA,EAAC;IAJjC1B,EAAA,CAAAG,YAAA,EAME;IACFH,EAAA,CAAAW,UAAA,KAAAkB,gDAAA,kBAEM;IACN7B,EAAA,CAAAW,UAAA,KAAAmB,gDAAA,kBAOM;IACR9B,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,eAA8B;IAU1BD,EAAA,CAAAW,UAAA,KAAAoB,iDAAA,kBAAsD;IACtD/B,EAAA,CAAAW,UAAA,KAAAqB,iDAAA,kBAAwD;IAC1DhC,EAAA,CAAAG,YAAA,EAAS;;;;;IAvETH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAiC,kBAAA,MAAAC,MAAA,CAAAC,MAAA,CAAAC,KAAA,MACF;IAGEpC,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAiC,kBAAA,MAAAC,MAAA,CAAAC,MAAA,CAAAE,WAAA,MACF;IAGErC,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAiC,kBAAA,MAAAjC,EAAA,CAAAsC,WAAA,SAAAJ,MAAA,CAAAC,MAAA,CAAAI,UAAA,qBACF;IAGIvC,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAAa,UAAA,cAAAqB,MAAA,CAAAM,cAAA,CAA4B;IAa3BxC,EAAA,CAAAK,SAAA,GAIb;IAJaL,EAAA,CAAAa,UAAA,WAAA4B,OAAA,GAAAP,MAAA,CAAAM,cAAA,CAAAE,GAAA,kCAAAD,OAAA,CAAAE,OAAA,OAAAF,OAAA,GAAAP,MAAA,CAAAM,cAAA,CAAAE,GAAA,kCAAAD,OAAA,CAAAG,OAAA,EAIb;IAiBgB5C,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAa,UAAA,SAAAqB,MAAA,CAAAnB,aAAA,CAAA8B,MAAA,OAAgC;IAGhC7C,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAAa,UAAA,SAAAqB,MAAA,CAAAnB,aAAA,CAAA8B,MAAA,KAA8B;IAalC7C,EAAA,CAAAK,SAAA,GAIC;IAJDL,EAAA,CAAAa,UAAA,aAAAqB,MAAA,CAAAM,cAAA,CAAAG,OAAA,IAAAT,MAAA,CAAAnB,aAAA,CAAA8B,MAAA,UAAAX,MAAA,CAAAY,YAAA,CAIC;IAGM9C,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAa,UAAA,UAAAqB,MAAA,CAAAY,YAAA,CAAmB;IACnB9C,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAa,UAAA,SAAAqB,MAAA,CAAAY,YAAA,CAAkB;;;ADhFrC;AAMA,OAAM,MAAOC,0BAA0B;EAQrCC,YACUC,EAAe,EACfC,KAAqB,EACrBC,MAAc,EACdC,aAA4B,EAC5BC,aAA4B,EAC5BC,WAA4B;IAL5B,KAAAL,EAAE,GAAFA,EAAE;IACF,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IAbrB,KAAAC,QAAQ,GAAW,EAAE;IAGrB,KAAAxC,aAAa,GAAW,EAAE;IAC1B,KAAAyC,SAAS,GAAG,IAAI;IAChB,KAAAV,YAAY,GAAG,KAAK;IAUlB,IAAI,CAACN,cAAc,GAAG,IAAI,CAACS,EAAE,CAACQ,KAAK,CAAC;MAClCpB,WAAW,EAAE,CAAC,EAAE,EAAE,CAACtC,UAAU,CAAC2D,QAAQ,EAAE3D,UAAU,CAAC4D,SAAS,CAAC,EAAE,CAAC,CAAC;KAClE,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACL,QAAQ,GAAG,IAAI,CAACL,KAAK,CAACW,QAAQ,CAACC,QAAQ,CAACpB,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE;IAC5D,IAAI,CAACqB,iBAAiB,EAAE;EAC1B;EAEAA,iBAAiBA,CAAA;IACf,IAAI,CAACP,SAAS,GAAG,IAAI;IACrB,IAAI,CAACJ,aAAa,CAACY,aAAa,CAAC,IAAI,CAACT,QAAQ,CAAC,CAACU,SAAS,CAAC;MACxDC,IAAI,EAAG/B,MAAW,IAAI;QACpB,IAAI,CAACA,MAAM,GAAGA,MAAM;QACpB,IAAI,CAACqB,SAAS,GAAG,KAAK;MACxB,CAAC;MACDW,KAAK,EAAGC,GAAU,IAAI;QACpBC,OAAO,CAACF,KAAK,CAAC,qCAAqC,EAAEC,GAAG,CAAC;QACzD,IAAI,CAACZ,SAAS,GAAG,KAAK;QACtB,IAAI,CAACL,MAAM,CAACmB,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;MACrC;KACD,CAAC;EACJ;EAEA1C,YAAYA,CAAC2C,KAAY;IACvB,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAA0B;IAC9C,IAAID,KAAK,CAACE,KAAK,EAAE;MACf,IAAI,CAAC3D,aAAa,GAAG4D,KAAK,CAACC,IAAI,CAACJ,KAAK,CAACE,KAAK,CAAC;;EAEhD;EAEAnD,QAAQA,CAAA;IACN,IAAI,IAAI,CAACiB,cAAc,CAACG,OAAO,IAAI,IAAI,CAAC5B,aAAa,CAAC8B,MAAM,KAAK,CAAC,EAAE;MAClE;;IAGF,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,MAAM+B,QAAQ,GAAG,IAAIC,QAAQ,EAAE;IAC/BD,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAE,IAAI,CAACxB,QAAQ,CAAC;IACxCsB,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAE,IAAI,CAACzB,WAAW,CAAC0B,gBAAgB,EAAE,IAAI,EAAE,CAAC;IACtEH,QAAQ,CAACE,MAAM,CAAC,aAAa,EAAE,IAAI,CAACvC,cAAc,CAACyC,KAAK,CAAC5C,WAAW,CAAC;IAErE,IAAI,CAACtB,aAAa,CAACmE,OAAO,CAAEC,IAAI,IAAI;MAClCN,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEI,IAAI,CAAC;IACnC,CAAC,CAAC;IAEF,IAAI,CAAC9B,aAAa,CAAC+B,WAAW,CAACP,QAAQ,CAAC,CAACZ,SAAS,CAAC;MACjDC,IAAI,EAAGmB,QAAa,IAAI;QACtBC,KAAK,CAAC,uCAAuC,CAAC;QAC9C,IAAI,CAACnC,MAAM,CAACmB,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;MACrC,CAAC;MACDH,KAAK,EAAGC,GAAU,IAAI;QACpBC,OAAO,CAACF,KAAK,CAAC,wCAAwC,EAAEC,GAAG,CAAC;QAC5DkB,KAAK,CAAC,yDAAyD,CAAC;QAChE,IAAI,CAACxC,YAAY,GAAG,KAAK;MAC3B;KACD,CAAC;EACJ;;;uBA1EWC,0BAA0B,EAAA/C,EAAA,CAAAuF,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAzF,EAAA,CAAAuF,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAA3F,EAAA,CAAAuF,iBAAA,CAAAG,EAAA,CAAAE,MAAA,GAAA5F,EAAA,CAAAuF,iBAAA,CAAAM,EAAA,CAAAC,aAAA,GAAA9F,EAAA,CAAAuF,iBAAA,CAAAQ,EAAA,CAAAC,aAAA,GAAAhG,EAAA,CAAAuF,iBAAA,CAAAU,EAAA,CAAAC,eAAA;IAAA;EAAA;;;YAA1BnD,0BAA0B;MAAAoD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCbvCzG,EAAA,CAAAC,cAAA,aAAkD;UAEKD,EAAA,CAAAI,MAAA,0BAAmB;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UAE3EH,EAAA,CAAAW,UAAA,IAAAgG,yCAAA,iBAIM;UAEN3G,EAAA,CAAAW,UAAA,IAAAiG,yCAAA,mBAiFM;UACR5G,EAAA,CAAAG,YAAA,EAAM;;;UAxFEH,EAAA,CAAAK,SAAA,GAAe;UAAfL,EAAA,CAAAa,UAAA,SAAA6F,GAAA,CAAAlD,SAAA,CAAe;UAMfxD,EAAA,CAAAK,SAAA,GAA0B;UAA1BL,EAAA,CAAAa,UAAA,SAAA6F,GAAA,CAAAvE,MAAA,KAAAuE,GAAA,CAAAlD,SAAA,CAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}