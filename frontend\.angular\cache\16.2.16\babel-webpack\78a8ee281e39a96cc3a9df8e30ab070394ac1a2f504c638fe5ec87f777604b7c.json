{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { EquipesRoutingModule } from './equipes-routing.module';\nimport { EquipeListComponent } from './equipe-list/equipe-list.component';\nimport { EquipeFormComponent } from './equipe-form/equipe-form.component';\nimport { EquipeDetailComponent } from './equipe-detail/equipe-detail.component';\nimport { TaskListComponent } from './task-list/task-list.component';\nimport { AiChatComponent } from './ai-chat/ai-chat.component';\nimport { EquipeComponent } from './equipe/equipe.component';\nimport { NotificationComponent } from './notification/notification.component';\nimport { HttpClientModule } from '@angular/common/http';\nimport { FormsModule } from '@angular/forms';\nimport { DragDropModule } from '@angular/cdk/drag-drop';\nimport * as i0 from \"@angular/core\";\nexport class EquipesModule {\n  static {\n    this.ɵfac = function EquipesModule_Factory(t) {\n      return new (t || EquipesModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: EquipesModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, EquipesRoutingModule, FormsModule, DragDropModule, HttpClientModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(EquipesModule, {\n    declarations: [EquipeListComponent, EquipeFormComponent, EquipeDetailComponent, TaskListComponent, AiChatComponent, EquipeComponent, NotificationComponent],\n    imports: [CommonModule, EquipesRoutingModule, FormsModule, DragDropModule, HttpClientModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "EquipesRoutingModule", "EquipeListComponent", "EquipeFormComponent", "EquipeDetailComponent", "TaskListComponent", "AiChatComponent", "EquipeComponent", "NotificationComponent", "HttpClientModule", "FormsModule", "DragDropModule", "EquipesModule", "declarations", "imports"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\equipes\\equipes.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\n\nimport { EquipesRoutingModule } from './equipes-routing.module';\nimport { EquipeListComponent } from './equipe-list/equipe-list.component';\nimport { EquipeFormComponent } from './equipe-form/equipe-form.component';\nimport { EquipeDetailComponent } from './equipe-detail/equipe-detail.component';\nimport { TaskListComponent } from './task-list/task-list.component';\nimport { AiChatComponent } from './ai-chat/ai-chat.component';\nimport { EquipeComponent } from './equipe/equipe.component';\nimport { NotificationComponent } from './notification/notification.component';\nimport { HttpClientModule } from '@angular/common/http';\nimport { FormsModule } from '@angular/forms';\nimport { DragDropModule } from '@angular/cdk/drag-drop';\n\n@NgModule({\n  declarations: [\n    EquipeListComponent,\n    EquipeFormComponent,\n    EquipeDetailComponent,\n    TaskListComponent,\n    AiChatComponent,\n    EquipeComponent,\n    NotificationComponent,\n  ],\n  imports: [\n    CommonModule,\n    EquipesRoutingModule,\n    FormsModule,\n    DragDropModule,\n    HttpClientModule,\n  ],\n  providers: [],\n})\nexport class EquipesModule {}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,oBAAoB,QAAQ,0BAA0B;AAC/D,SAASC,mBAAmB,QAAQ,qCAAqC;AACzE,SAASC,mBAAmB,QAAQ,qCAAqC;AACzE,SAASC,qBAAqB,QAAQ,yCAAyC;AAC/E,SAASC,iBAAiB,QAAQ,iCAAiC;AACnE,SAASC,eAAe,QAAQ,6BAA6B;AAC7D,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,qBAAqB,QAAQ,uCAAuC;AAC7E,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,cAAc,QAAQ,wBAAwB;;AAqBvD,OAAM,MAAOC,aAAa;;;uBAAbA,aAAa;IAAA;EAAA;;;YAAbA;IAAa;EAAA;;;gBARtBZ,YAAY,EACZC,oBAAoB,EACpBS,WAAW,EACXC,cAAc,EACdF,gBAAgB;IAAA;EAAA;;;2EAIPG,aAAa;IAAAC,YAAA,GAjBtBX,mBAAmB,EACnBC,mBAAmB,EACnBC,qBAAqB,EACrBC,iBAAiB,EACjBC,eAAe,EACfC,eAAe,EACfC,qBAAqB;IAAAM,OAAA,GAGrBd,YAAY,EACZC,oBAAoB,EACpBS,WAAW,EACXC,cAAc,EACdF,gBAAgB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}