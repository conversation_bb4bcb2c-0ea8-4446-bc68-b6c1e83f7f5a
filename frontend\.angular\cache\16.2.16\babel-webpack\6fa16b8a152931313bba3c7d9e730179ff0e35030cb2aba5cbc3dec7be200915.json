{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/equipe.service\";\nimport * as i2 from \"src/app/services/membre.service\";\nexport class EquipeComponent {\n  constructor(equipeService, membreService) {\n    this.equipeService = equipeService;\n    this.membreService = membreService;\n    this.equipes = [];\n    this.newEquipe = {\n      name: '',\n      description: ''\n    };\n    this.selectedEquipe = null;\n    this.isEditing = false;\n    this.membres = [];\n    this.loading = false;\n    this.error = '';\n  }\n  ngOnInit() {\n    this.loadEquipes();\n    this.loadMembres();\n  }\n  loadEquipes() {\n    this.loading = true;\n    this.equipeService.getEquipes().subscribe({\n      next: data => {\n        console.log('Loaded equipes:', data);\n        this.equipes = data;\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Error loading equipes:', error);\n        this.error = 'Erreur lors du chargement des équipes: ' + error.message;\n        this.loading = false;\n      }\n    });\n  }\n  loadMembres() {\n    this.loading = true;\n    this.membreService.getMembres().subscribe({\n      next: data => {\n        console.log('Loaded membres:', data);\n        this.membres = data;\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Error loading membres:', error);\n        this.error = 'Erreur lors du chargement des membres: ' + error.message;\n        this.loading = false;\n      }\n    });\n  }\n  addEquipe() {\n    console.log('Adding equipe:', this.newEquipe);\n    if (!this.newEquipe.name) {\n      console.error('Team name is required');\n      this.error = 'Le nom de l\\'équipe est requis';\n      return;\n    }\n    this.loading = true;\n    this.error = '';\n    this.equipeService.addEquipe(this.newEquipe).subscribe({\n      next: response => {\n        console.log('Equipe added successfully:', response);\n        this.loadEquipes();\n        this.newEquipe = {\n          name: '',\n          description: ''\n        }; // Clear input\n        this.loading = false;\n        // Afficher un message de succès temporaire\n        const successMessage = 'Équipe créée avec succès!';\n        this.error = ''; // Effacer les erreurs précédentes\n        alert(successMessage);\n      },\n      error: error => {\n        console.error('Error adding equipe:', error);\n        this.error = 'Erreur lors de la création de l\\'équipe: ' + (error.error?.message || error.message || 'Unknown error');\n        this.loading = false;\n      }\n    });\n  }\n  editEquipe(equipe) {\n    this.isEditing = true;\n    // Créer une copie profonde pour éviter de modifier l'objet original\n    this.newEquipe = {\n      _id: equipe._id,\n      name: equipe.name || '',\n      description: equipe.description || '',\n      admin: equipe.admin,\n      members: equipe.members ? [...equipe.members] : []\n    };\n  }\n  cancelEdit() {\n    this.isEditing = false;\n    this.newEquipe = {\n      name: '',\n      description: ''\n    };\n    this.error = ''; // Effacer les erreurs\n  }\n\n  updateSelectedEquipe() {\n    if (!this.newEquipe.name) {\n      console.error('Team name is required');\n      this.error = 'Le nom de l\\'équipe est requis';\n      return;\n    }\n    if (this.newEquipe._id) {\n      this.loading = true;\n      this.error = '';\n      this.equipeService.updateEquipe(this.newEquipe._id, this.newEquipe).subscribe({\n        next: updatedEquipe => {\n          console.log('Team updated successfully:', updatedEquipe);\n          this.loadEquipes();\n          this.isEditing = false;\n          this.newEquipe = {\n            name: '',\n            description: ''\n          };\n          this.loading = false;\n          // Afficher un message de succès temporaire\n          const successMessage = 'Équipe mise à jour avec succès!';\n          alert(successMessage);\n        },\n        error: error => {\n          console.error('Error updating team:', error);\n          this.error = 'Erreur lors de la mise à jour de l\\'équipe: ' + (error.error?.message || error.message || 'Unknown error');\n          this.loading = false;\n        }\n      });\n    } else {\n      this.error = 'ID de l\\'équipe manquant pour la mise à jour';\n    }\n  }\n  deleteEquipe(id) {\n    if (!id) {\n      console.error('ID is undefined');\n      this.error = 'ID de l\\'équipe non défini';\n      return;\n    }\n    if (confirm('Êtes-vous sûr de vouloir supprimer cette équipe? Cette action est irréversible.')) {\n      this.loading = true;\n      this.error = '';\n      this.equipeService.deleteEquipe(id).subscribe({\n        next: response => {\n          console.log('Team deleted successfully:', response);\n          // Si l'équipe en cours d'édition est celle qui vient d'être supprimée, réinitialiser le formulaire\n          if (this.isEditing && this.newEquipe._id === id) {\n            this.isEditing = false;\n            this.newEquipe = {\n              name: '',\n              description: ''\n            };\n          }\n          this.loadEquipes();\n          this.loading = false;\n          // Afficher un message de succès\n          alert('Équipe supprimée avec succès');\n        },\n        error: error => {\n          console.error('Error deleting team:', error);\n          this.error = 'Erreur lors de la suppression de l\\'équipe: ' + (error.error?.message || error.message || 'Unknown error');\n          this.loading = false;\n        }\n      });\n    }\n  }\n  showMembreModal(equipe) {\n    this.selectedEquipe = equipe;\n    // Ouvrir le modal avec Bootstrap 5\n    const modalRef = document.getElementById('membreModal');\n    if (modalRef) {\n      try {\n        // Ensure Bootstrap is properly loaded\n        if (typeof window !== 'undefined' && window.bootstrap) {\n          const modal = new window.bootstrap.Modal(modalRef);\n          modal.show();\n        } else {\n          console.error('Bootstrap is not loaded properly');\n          alert('Erreur: Bootstrap n\\'est pas chargé correctement');\n        }\n      } catch (error) {\n        console.error('Error showing modal:', error);\n      }\n    } else {\n      console.error('Modal element not found');\n    }\n  }\n  addMembreToEquipe(teamId, membreId) {\n    if (!teamId) {\n      console.error('Team ID is undefined');\n      alert('ID de l\\'équipe non défini');\n      return;\n    }\n    if (!membreId || membreId.trim() === '') {\n      console.error('Member ID is empty');\n      alert('L\\'ID du membre est requis');\n      return;\n    }\n    this.loading = true;\n    // Create a proper Membre object that matches what the API expects\n    const membre = {\n      id: membreId\n    };\n    this.equipeService.addMembreToEquipe(teamId, membre).subscribe({\n      next: response => {\n        console.log('Member added successfully:', response);\n        this.loadEquipes();\n        this.loading = false;\n        // Afficher un message de succès\n        alert('Membre ajouté avec succès à l\\'équipe');\n      },\n      error: error => {\n        console.error('Error adding member:', error);\n        this.error = 'Erreur lors de l\\'ajout du membre: ' + (error.error?.message || error.message || 'Unknown error');\n        alert(this.error);\n        this.loading = false;\n      }\n    });\n  }\n  removeMembreFromEquipe(teamId, membreId) {\n    if (!teamId) {\n      console.error('Team ID is undefined');\n      alert('ID de l\\'équipe non défini');\n      return;\n    }\n    if (!membreId) {\n      console.error('Member ID is undefined');\n      alert('ID du membre non défini');\n      return;\n    }\n    if (confirm('Êtes-vous sûr de vouloir retirer ce membre de l\\'équipe?')) {\n      this.loading = true;\n      this.equipeService.removeMembreFromEquipe(teamId, membreId).subscribe({\n        next: response => {\n          console.log('Member removed successfully:', response);\n          this.loadEquipes();\n          this.loading = false;\n          // Si l'équipe sélectionnée est celle dont on vient de retirer un membre, mettre à jour l'équipe sélectionnée\n          if (this.selectedEquipe && this.selectedEquipe._id === teamId) {\n            const updatedEquipe = this.equipes.find(e => e._id === teamId);\n            if (updatedEquipe) {\n              this.selectedEquipe = updatedEquipe;\n            }\n          }\n        },\n        error: error => {\n          console.error('Error removing member:', error);\n          this.error = 'Erreur lors de la suppression du membre: ' + (error.error?.message || error.message || 'Unknown error');\n          alert(this.error);\n          this.loading = false;\n        }\n      });\n    }\n  }\n  static {\n    this.ɵfac = function EquipeComponent_Factory(t) {\n      return new (t || EquipeComponent)(i0.ɵɵdirectiveInject(i1.EquipeService), i0.ɵɵdirectiveInject(i2.MembreService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: EquipeComponent,\n      selectors: [[\"app-equipe\"]],\n      decls: 2,\n      vars: 0,\n      template: function EquipeComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"p\");\n          i0.ɵɵtext(1, \"equipe works!\");\n          i0.ɵɵelementEnd();\n        }\n      },\n      styles: [\"var[_ngcontent-%COMP%]   resource[_ngcontent-%COMP%];\\n\\n (()[_ngcontent-%COMP%]   =[_ngcontent-%COMP%] >  { // webpackBootstrap\\n\\n \\tvar __webpack_modules__ = ({\\n\\n\\n 39:\\n\\n\\n\\n\\n\\n (() => {\\n\\nthrow new Error(\\\"Module build failed (from ./node_modules/postcss-loader/dist/cjs.js):\\\\n\\\\nSyntaxError\\\\n\\\\n(64:9) C:\\\\\\\\Users\\\\\\\\<USER>\\\\\\\\OneDrive\\\\\\\\Bureau\\\\\\\\Project PI\\\\\\\\devBridge\\\\\\\\frontend\\\\\\\\src\\\\\\\\app\\\\\\\\views\\\\\\\\front\\\\\\\\equipes\\\\\\\\equipe\\\\\\\\equipe.component.css Unknown word\\\\n\\\\n \\\\u001b[90m 62 | \\\\u001b[39m        <div class=\\\\u001b[32m\\\\\\\"card-header bg-primary text-white\\\\\\\"\\\\u001b[39m>\\\\n \\\\u001b[90m 63 | \\\\u001b[39m          <h3 class=\\\\u001b[32m\\\\\\\"mb-0\\\\\\\"\\\\u001b[39m>\\\\u001b[33m{\\\\u001b[39m\\\\u001b[33m{\\\\u001b[39m isEditing ? \\\\u001b[32m'Modifier une \\u00E9quipe'\\\\u001b[39m \\\\u001b[33m:\\\\u001b[39m \\\\u001b[32m'Cr\\u00E9er une \\u00E9quipe'\\\\u001b[39m \\\\u001b[33m}\\\\u001b[39m\\\\u001b[33m}\\\\u001b[39m</h3>\\\\n\\\\u001b[1m\\\\u001b[31m>\\\\u001b[39m\\\\u001b[22m\\\\u001b[90m 64 | \\\\u001b[39m        </div>\\\\n \\\\u001b[90m    | \\\\u001b[39m        \\\\u001b[1m\\\\u001b[31m^\\\\u001b[39m\\\\u001b[22m\\\\n \\\\u001b[90m 65 | \\\\u001b[39m        <div class=\\\\u001b[32m\\\\\\\"card-body\\\\\\\"\\\\u001b[39m>\\\\n \\\\u001b[90m 66 | \\\\u001b[39m          <form>\\\\n\\\");\\n\\n\\n })\\n\\n\\n \\t});\\n\\n\\n\\n \\t\\n\\n \\t// startup\\n\\n \\t// Load entry module and return exports\\n\\n \\t// This entry module doesn't tell about it's top-level declarations so it can't be inlined\\n\\n \\tvar __webpack_exports__ = {};\\n\\n \\t__webpack_modules__[39]();\\n\\n \\tresource = __webpack_exports__;\\n\\n \\t\\n\\n })()\\n;\\n/*# sourceMappingURL=equipe.component.css.map*/\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EquipeComponent", "constructor", "equipeService", "membreService", "equipes", "newEquipe", "name", "description", "selectedEquipe", "isEditing", "membres", "loading", "error", "ngOnInit", "loadEquipes", "loadMembres", "getEquipes", "subscribe", "next", "data", "console", "log", "message", "getMembres", "addEquipe", "response", "successMessage", "alert", "editEquipe", "equipe", "_id", "admin", "members", "cancelEdit", "updateSelectedEquipe", "updateEquipe", "updatedEquipe", "deleteEquipe", "id", "confirm", "showMembreModal", "modalRef", "document", "getElementById", "window", "bootstrap", "modal", "Modal", "show", "addMembreToEquipe", "teamId", "membreId", "trim", "membre", "removeMembreFromEquipe", "find", "e", "i0", "ɵɵdirectiveInject", "i1", "EquipeService", "i2", "MembreService", "selectors", "decls", "vars", "template", "EquipeComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\equipes\\equipe\\equipe.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\equipes\\equipe\\equipe.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { EquipeService } from 'src/app/services/equipe.service';\nimport { MembreService } from 'src/app/services/membre.service';\nimport { Equipe } from 'src/app/models/equipe.model';\nimport { Membre } from 'src/app/models/membre.model';\nimport { forkJoin } from 'rxjs';\n\n// Add Bootstrap type declaration\ndeclare global {\n  interface Window {\n    bootstrap: any;\n  }\n}\n\n@Component({\n  selector: 'app-equipe',\n  templateUrl: './equipe.component.html',\n  styleUrls: ['./equipe.component.css'],\n})\nexport class EquipeComponent implements OnInit {\n  equipes: Equipe[] = [];\n  newEquipe: Equipe = { name: '', description: '' };\n  selectedEquipe: Equipe | null = null;\n  isEditing = false;\n  membres: Membre[] = [];\n  loading = false;\n  error = '';\n\n  constructor(\n    private equipeService: EquipeService,\n    private membreService: MembreService\n  ) {}\n\n  ngOnInit(): void {\n    this.loadEquipes();\n    this.loadMembres();\n  }\n\n  loadEquipes() {\n    this.loading = true;\n    this.equipeService.getEquipes().subscribe({\n      next: (data) => {\n        console.log('Loaded equipes:', data);\n        this.equipes = data;\n        this.loading = false;\n      },\n      error: (error) => {\n        console.error('Error loading equipes:', error);\n        this.error = 'Erreur lors du chargement des équipes: ' + error.message;\n        this.loading = false;\n      }\n    });\n  }\n\n  loadMembres() {\n    this.loading = true;\n    this.membreService.getMembres().subscribe({\n      next: (data) => {\n        console.log('Loaded membres:', data);\n        this.membres = data;\n        this.loading = false;\n      },\n      error: (error) => {\n        console.error('Error loading membres:', error);\n        this.error = 'Erreur lors du chargement des membres: ' + error.message;\n        this.loading = false;\n      }\n    });\n  }\n\n  addEquipe() {\n    console.log('Adding equipe:', this.newEquipe);\n\n    if (!this.newEquipe.name) {\n      console.error('Team name is required');\n      this.error = 'Le nom de l\\'équipe est requis';\n      return;\n    }\n\n    this.loading = true;\n    this.error = '';\n\n    this.equipeService.addEquipe(this.newEquipe).subscribe({\n      next: (response) => {\n        console.log('Equipe added successfully:', response);\n        this.loadEquipes();\n        this.newEquipe = { name: '', description: '' }; // Clear input\n        this.loading = false;\n\n        // Afficher un message de succès temporaire\n        const successMessage = 'Équipe créée avec succès!';\n        this.error = ''; // Effacer les erreurs précédentes\n        alert(successMessage);\n      },\n      error: (error) => {\n        console.error('Error adding equipe:', error);\n        this.error = 'Erreur lors de la création de l\\'équipe: ' + (error.error?.message || error.message || 'Unknown error');\n        this.loading = false;\n      }\n    });\n  }\n\n  editEquipe(equipe: Equipe) {\n    this.isEditing = true;\n    // Créer une copie profonde pour éviter de modifier l'objet original\n    this.newEquipe = {\n      _id: equipe._id,\n      name: equipe.name || '',\n      description: equipe.description || '',\n      admin: equipe.admin,\n      members: equipe.members ? [...equipe.members] : []\n    };\n  }\n\n  cancelEdit() {\n    this.isEditing = false;\n    this.newEquipe = { name: '', description: '' };\n    this.error = ''; // Effacer les erreurs\n  }\n\n  updateSelectedEquipe() {\n    if (!this.newEquipe.name) {\n      console.error('Team name is required');\n      this.error = 'Le nom de l\\'équipe est requis';\n      return;\n    }\n\n    if (this.newEquipe._id) {\n      this.loading = true;\n      this.error = '';\n\n      this.equipeService.updateEquipe(this.newEquipe._id, this.newEquipe).subscribe({\n        next: (updatedEquipe) => {\n          console.log('Team updated successfully:', updatedEquipe);\n          this.loadEquipes();\n          this.isEditing = false;\n          this.newEquipe = { name: '', description: '' };\n          this.loading = false;\n\n          // Afficher un message de succès temporaire\n          const successMessage = 'Équipe mise à jour avec succès!';\n          alert(successMessage);\n        },\n        error: (error) => {\n          console.error('Error updating team:', error);\n          this.error = 'Erreur lors de la mise à jour de l\\'équipe: ' + (error.error?.message || error.message || 'Unknown error');\n          this.loading = false;\n        }\n      });\n    } else {\n      this.error = 'ID de l\\'équipe manquant pour la mise à jour';\n    }\n  }\n\n  deleteEquipe(id: string) {\n    if (!id) {\n      console.error('ID is undefined');\n      this.error = 'ID de l\\'équipe non défini';\n      return;\n    }\n\n    if (confirm('Êtes-vous sûr de vouloir supprimer cette équipe? Cette action est irréversible.')) {\n      this.loading = true;\n      this.error = '';\n\n      this.equipeService.deleteEquipe(id).subscribe({\n        next: (response) => {\n          console.log('Team deleted successfully:', response);\n\n          // Si l'équipe en cours d'édition est celle qui vient d'être supprimée, réinitialiser le formulaire\n          if (this.isEditing && this.newEquipe._id === id) {\n            this.isEditing = false;\n            this.newEquipe = { name: '', description: '' };\n          }\n\n          this.loadEquipes();\n          this.loading = false;\n\n          // Afficher un message de succès\n          alert('Équipe supprimée avec succès');\n        },\n        error: (error) => {\n          console.error('Error deleting team:', error);\n          this.error = 'Erreur lors de la suppression de l\\'équipe: ' + (error.error?.message || error.message || 'Unknown error');\n          this.loading = false;\n        }\n      });\n    }\n  }\n\n  showMembreModal(equipe: Equipe) {\n    this.selectedEquipe = equipe;\n    // Ouvrir le modal avec Bootstrap 5\n    const modalRef = document.getElementById('membreModal');\n    if (modalRef) {\n      try {\n        // Ensure Bootstrap is properly loaded\n        if (typeof window !== 'undefined' && window.bootstrap) {\n          const modal = new window.bootstrap.Modal(modalRef);\n          modal.show();\n        } else {\n          console.error('Bootstrap is not loaded properly');\n          alert('Erreur: Bootstrap n\\'est pas chargé correctement');\n        }\n      } catch (error) {\n        console.error('Error showing modal:', error);\n      }\n    } else {\n      console.error('Modal element not found');\n    }\n  }\n\n  addMembreToEquipe(teamId: string | undefined, membreId: string) {\n    if (!teamId) {\n      console.error('Team ID is undefined');\n      alert('ID de l\\'équipe non défini');\n      return;\n    }\n\n    if (!membreId || membreId.trim() === '') {\n      console.error('Member ID is empty');\n      alert('L\\'ID du membre est requis');\n      return;\n    }\n\n    this.loading = true;\n\n    // Create a proper Membre object that matches what the API expects\n    const membre: Membre = { id: membreId };\n\n    this.equipeService.addMembreToEquipe(teamId, membre).subscribe({\n      next: (response) => {\n        console.log('Member added successfully:', response);\n        this.loadEquipes();\n        this.loading = false;\n\n        // Afficher un message de succès\n        alert('Membre ajouté avec succès à l\\'équipe');\n      },\n      error: (error) => {\n        console.error('Error adding member:', error);\n        this.error = 'Erreur lors de l\\'ajout du membre: ' + (error.error?.message || error.message || 'Unknown error');\n        alert(this.error);\n        this.loading = false;\n      }\n    });\n  }\n\n  removeMembreFromEquipe(teamId: string | undefined, membreId: string) {\n    if (!teamId) {\n      console.error('Team ID is undefined');\n      alert('ID de l\\'équipe non défini');\n      return;\n    }\n\n    if (!membreId) {\n      console.error('Member ID is undefined');\n      alert('ID du membre non défini');\n      return;\n    }\n\n    if (confirm('Êtes-vous sûr de vouloir retirer ce membre de l\\'équipe?')) {\n      this.loading = true;\n\n      this.equipeService.removeMembreFromEquipe(teamId, membreId).subscribe({\n        next: (response) => {\n          console.log('Member removed successfully:', response);\n          this.loadEquipes();\n          this.loading = false;\n\n          // Si l'équipe sélectionnée est celle dont on vient de retirer un membre, mettre à jour l'équipe sélectionnée\n          if (this.selectedEquipe && this.selectedEquipe._id === teamId) {\n            const updatedEquipe = this.equipes.find(e => e._id === teamId);\n            if (updatedEquipe) {\n              this.selectedEquipe = updatedEquipe;\n            }\n          }\n        },\n        error: (error) => {\n          console.error('Error removing member:', error);\n          this.error = 'Erreur lors de la suppression du membre: ' + (error.error?.message || error.message || 'Unknown error');\n          alert(this.error);\n          this.loading = false;\n        }\n      });\n    }\n  }\n}\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n", "<p>equipe works!</p>\n"], "mappings": ";;;AAmBA,OAAM,MAAOA,eAAe;EAS1BC,YACUC,aAA4B,EAC5BC,aAA4B;IAD5B,KAAAD,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;IAVvB,KAAAC,OAAO,GAAa,EAAE;IACtB,KAAAC,SAAS,GAAW;MAAEC,IAAI,EAAE,EAAE;MAAEC,WAAW,EAAE;IAAE,CAAE;IACjD,KAAAC,cAAc,GAAkB,IAAI;IACpC,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,OAAO,GAAa,EAAE;IACtB,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAC,KAAK,GAAG,EAAE;EAKP;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,EAAE;IAClB,IAAI,CAACC,WAAW,EAAE;EACpB;EAEAD,WAAWA,CAAA;IACT,IAAI,CAACH,OAAO,GAAG,IAAI;IACnB,IAAI,CAACT,aAAa,CAACc,UAAU,EAAE,CAACC,SAAS,CAAC;MACxCC,IAAI,EAAGC,IAAI,IAAI;QACbC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEF,IAAI,CAAC;QACpC,IAAI,CAACf,OAAO,GAAGe,IAAI;QACnB,IAAI,CAACR,OAAO,GAAG,KAAK;MACtB,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfQ,OAAO,CAACR,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAACA,KAAK,GAAG,yCAAyC,GAAGA,KAAK,CAACU,OAAO;QACtE,IAAI,CAACX,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAI,WAAWA,CAAA;IACT,IAAI,CAACJ,OAAO,GAAG,IAAI;IACnB,IAAI,CAACR,aAAa,CAACoB,UAAU,EAAE,CAACN,SAAS,CAAC;MACxCC,IAAI,EAAGC,IAAI,IAAI;QACbC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEF,IAAI,CAAC;QACpC,IAAI,CAACT,OAAO,GAAGS,IAAI;QACnB,IAAI,CAACR,OAAO,GAAG,KAAK;MACtB,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfQ,OAAO,CAACR,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAACA,KAAK,GAAG,yCAAyC,GAAGA,KAAK,CAACU,OAAO;QACtE,IAAI,CAACX,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAa,SAASA,CAAA;IACPJ,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAAChB,SAAS,CAAC;IAE7C,IAAI,CAAC,IAAI,CAACA,SAAS,CAACC,IAAI,EAAE;MACxBc,OAAO,CAACR,KAAK,CAAC,uBAAuB,CAAC;MACtC,IAAI,CAACA,KAAK,GAAG,gCAAgC;MAC7C;;IAGF,IAAI,CAACD,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,KAAK,GAAG,EAAE;IAEf,IAAI,CAACV,aAAa,CAACsB,SAAS,CAAC,IAAI,CAACnB,SAAS,CAAC,CAACY,SAAS,CAAC;MACrDC,IAAI,EAAGO,QAAQ,IAAI;QACjBL,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEI,QAAQ,CAAC;QACnD,IAAI,CAACX,WAAW,EAAE;QAClB,IAAI,CAACT,SAAS,GAAG;UAAEC,IAAI,EAAE,EAAE;UAAEC,WAAW,EAAE;QAAE,CAAE,CAAC,CAAC;QAChD,IAAI,CAACI,OAAO,GAAG,KAAK;QAEpB;QACA,MAAMe,cAAc,GAAG,2BAA2B;QAClD,IAAI,CAACd,KAAK,GAAG,EAAE,CAAC,CAAC;QACjBe,KAAK,CAACD,cAAc,CAAC;MACvB,CAAC;MACDd,KAAK,EAAGA,KAAK,IAAI;QACfQ,OAAO,CAACR,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C,IAAI,CAACA,KAAK,GAAG,2CAA2C,IAAIA,KAAK,CAACA,KAAK,EAAEU,OAAO,IAAIV,KAAK,CAACU,OAAO,IAAI,eAAe,CAAC;QACrH,IAAI,CAACX,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAiB,UAAUA,CAACC,MAAc;IACvB,IAAI,CAACpB,SAAS,GAAG,IAAI;IACrB;IACA,IAAI,CAACJ,SAAS,GAAG;MACfyB,GAAG,EAAED,MAAM,CAACC,GAAG;MACfxB,IAAI,EAAEuB,MAAM,CAACvB,IAAI,IAAI,EAAE;MACvBC,WAAW,EAAEsB,MAAM,CAACtB,WAAW,IAAI,EAAE;MACrCwB,KAAK,EAAEF,MAAM,CAACE,KAAK;MACnBC,OAAO,EAAEH,MAAM,CAACG,OAAO,GAAG,CAAC,GAAGH,MAAM,CAACG,OAAO,CAAC,GAAG;KACjD;EACH;EAEAC,UAAUA,CAAA;IACR,IAAI,CAACxB,SAAS,GAAG,KAAK;IACtB,IAAI,CAACJ,SAAS,GAAG;MAAEC,IAAI,EAAE,EAAE;MAAEC,WAAW,EAAE;IAAE,CAAE;IAC9C,IAAI,CAACK,KAAK,GAAG,EAAE,CAAC,CAAC;EACnB;;EAEAsB,oBAAoBA,CAAA;IAClB,IAAI,CAAC,IAAI,CAAC7B,SAAS,CAACC,IAAI,EAAE;MACxBc,OAAO,CAACR,KAAK,CAAC,uBAAuB,CAAC;MACtC,IAAI,CAACA,KAAK,GAAG,gCAAgC;MAC7C;;IAGF,IAAI,IAAI,CAACP,SAAS,CAACyB,GAAG,EAAE;MACtB,IAAI,CAACnB,OAAO,GAAG,IAAI;MACnB,IAAI,CAACC,KAAK,GAAG,EAAE;MAEf,IAAI,CAACV,aAAa,CAACiC,YAAY,CAAC,IAAI,CAAC9B,SAAS,CAACyB,GAAG,EAAE,IAAI,CAACzB,SAAS,CAAC,CAACY,SAAS,CAAC;QAC5EC,IAAI,EAAGkB,aAAa,IAAI;UACtBhB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEe,aAAa,CAAC;UACxD,IAAI,CAACtB,WAAW,EAAE;UAClB,IAAI,CAACL,SAAS,GAAG,KAAK;UACtB,IAAI,CAACJ,SAAS,GAAG;YAAEC,IAAI,EAAE,EAAE;YAAEC,WAAW,EAAE;UAAE,CAAE;UAC9C,IAAI,CAACI,OAAO,GAAG,KAAK;UAEpB;UACA,MAAMe,cAAc,GAAG,iCAAiC;UACxDC,KAAK,CAACD,cAAc,CAAC;QACvB,CAAC;QACDd,KAAK,EAAGA,KAAK,IAAI;UACfQ,OAAO,CAACR,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;UAC5C,IAAI,CAACA,KAAK,GAAG,8CAA8C,IAAIA,KAAK,CAACA,KAAK,EAAEU,OAAO,IAAIV,KAAK,CAACU,OAAO,IAAI,eAAe,CAAC;UACxH,IAAI,CAACX,OAAO,GAAG,KAAK;QACtB;OACD,CAAC;KACH,MAAM;MACL,IAAI,CAACC,KAAK,GAAG,8CAA8C;;EAE/D;EAEAyB,YAAYA,CAACC,EAAU;IACrB,IAAI,CAACA,EAAE,EAAE;MACPlB,OAAO,CAACR,KAAK,CAAC,iBAAiB,CAAC;MAChC,IAAI,CAACA,KAAK,GAAG,4BAA4B;MACzC;;IAGF,IAAI2B,OAAO,CAAC,iFAAiF,CAAC,EAAE;MAC9F,IAAI,CAAC5B,OAAO,GAAG,IAAI;MACnB,IAAI,CAACC,KAAK,GAAG,EAAE;MAEf,IAAI,CAACV,aAAa,CAACmC,YAAY,CAACC,EAAE,CAAC,CAACrB,SAAS,CAAC;QAC5CC,IAAI,EAAGO,QAAQ,IAAI;UACjBL,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEI,QAAQ,CAAC;UAEnD;UACA,IAAI,IAAI,CAAChB,SAAS,IAAI,IAAI,CAACJ,SAAS,CAACyB,GAAG,KAAKQ,EAAE,EAAE;YAC/C,IAAI,CAAC7B,SAAS,GAAG,KAAK;YACtB,IAAI,CAACJ,SAAS,GAAG;cAAEC,IAAI,EAAE,EAAE;cAAEC,WAAW,EAAE;YAAE,CAAE;;UAGhD,IAAI,CAACO,WAAW,EAAE;UAClB,IAAI,CAACH,OAAO,GAAG,KAAK;UAEpB;UACAgB,KAAK,CAAC,8BAA8B,CAAC;QACvC,CAAC;QACDf,KAAK,EAAGA,KAAK,IAAI;UACfQ,OAAO,CAACR,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;UAC5C,IAAI,CAACA,KAAK,GAAG,8CAA8C,IAAIA,KAAK,CAACA,KAAK,EAAEU,OAAO,IAAIV,KAAK,CAACU,OAAO,IAAI,eAAe,CAAC;UACxH,IAAI,CAACX,OAAO,GAAG,KAAK;QACtB;OACD,CAAC;;EAEN;EAEA6B,eAAeA,CAACX,MAAc;IAC5B,IAAI,CAACrB,cAAc,GAAGqB,MAAM;IAC5B;IACA,MAAMY,QAAQ,GAAGC,QAAQ,CAACC,cAAc,CAAC,aAAa,CAAC;IACvD,IAAIF,QAAQ,EAAE;MACZ,IAAI;QACF;QACA,IAAI,OAAOG,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,SAAS,EAAE;UACrD,MAAMC,KAAK,GAAG,IAAIF,MAAM,CAACC,SAAS,CAACE,KAAK,CAACN,QAAQ,CAAC;UAClDK,KAAK,CAACE,IAAI,EAAE;SACb,MAAM;UACL5B,OAAO,CAACR,KAAK,CAAC,kCAAkC,CAAC;UACjDe,KAAK,CAAC,kDAAkD,CAAC;;OAE5D,CAAC,OAAOf,KAAK,EAAE;QACdQ,OAAO,CAACR,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;;KAE/C,MAAM;MACLQ,OAAO,CAACR,KAAK,CAAC,yBAAyB,CAAC;;EAE5C;EAEAqC,iBAAiBA,CAACC,MAA0B,EAAEC,QAAgB;IAC5D,IAAI,CAACD,MAAM,EAAE;MACX9B,OAAO,CAACR,KAAK,CAAC,sBAAsB,CAAC;MACrCe,KAAK,CAAC,4BAA4B,CAAC;MACnC;;IAGF,IAAI,CAACwB,QAAQ,IAAIA,QAAQ,CAACC,IAAI,EAAE,KAAK,EAAE,EAAE;MACvChC,OAAO,CAACR,KAAK,CAAC,oBAAoB,CAAC;MACnCe,KAAK,CAAC,4BAA4B,CAAC;MACnC;;IAGF,IAAI,CAAChB,OAAO,GAAG,IAAI;IAEnB;IACA,MAAM0C,MAAM,GAAW;MAAEf,EAAE,EAAEa;IAAQ,CAAE;IAEvC,IAAI,CAACjD,aAAa,CAAC+C,iBAAiB,CAACC,MAAM,EAAEG,MAAM,CAAC,CAACpC,SAAS,CAAC;MAC7DC,IAAI,EAAGO,QAAQ,IAAI;QACjBL,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEI,QAAQ,CAAC;QACnD,IAAI,CAACX,WAAW,EAAE;QAClB,IAAI,CAACH,OAAO,GAAG,KAAK;QAEpB;QACAgB,KAAK,CAAC,uCAAuC,CAAC;MAChD,CAAC;MACDf,KAAK,EAAGA,KAAK,IAAI;QACfQ,OAAO,CAACR,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C,IAAI,CAACA,KAAK,GAAG,qCAAqC,IAAIA,KAAK,CAACA,KAAK,EAAEU,OAAO,IAAIV,KAAK,CAACU,OAAO,IAAI,eAAe,CAAC;QAC/GK,KAAK,CAAC,IAAI,CAACf,KAAK,CAAC;QACjB,IAAI,CAACD,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEA2C,sBAAsBA,CAACJ,MAA0B,EAAEC,QAAgB;IACjE,IAAI,CAACD,MAAM,EAAE;MACX9B,OAAO,CAACR,KAAK,CAAC,sBAAsB,CAAC;MACrCe,KAAK,CAAC,4BAA4B,CAAC;MACnC;;IAGF,IAAI,CAACwB,QAAQ,EAAE;MACb/B,OAAO,CAACR,KAAK,CAAC,wBAAwB,CAAC;MACvCe,KAAK,CAAC,yBAAyB,CAAC;MAChC;;IAGF,IAAIY,OAAO,CAAC,0DAA0D,CAAC,EAAE;MACvE,IAAI,CAAC5B,OAAO,GAAG,IAAI;MAEnB,IAAI,CAACT,aAAa,CAACoD,sBAAsB,CAACJ,MAAM,EAAEC,QAAQ,CAAC,CAAClC,SAAS,CAAC;QACpEC,IAAI,EAAGO,QAAQ,IAAI;UACjBL,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEI,QAAQ,CAAC;UACrD,IAAI,CAACX,WAAW,EAAE;UAClB,IAAI,CAACH,OAAO,GAAG,KAAK;UAEpB;UACA,IAAI,IAAI,CAACH,cAAc,IAAI,IAAI,CAACA,cAAc,CAACsB,GAAG,KAAKoB,MAAM,EAAE;YAC7D,MAAMd,aAAa,GAAG,IAAI,CAAChC,OAAO,CAACmD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC1B,GAAG,KAAKoB,MAAM,CAAC;YAC9D,IAAId,aAAa,EAAE;cACjB,IAAI,CAAC5B,cAAc,GAAG4B,aAAa;;;QAGzC,CAAC;QACDxB,KAAK,EAAGA,KAAK,IAAI;UACfQ,OAAO,CAACR,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;UAC9C,IAAI,CAACA,KAAK,GAAG,2CAA2C,IAAIA,KAAK,CAACA,KAAK,EAAEU,OAAO,IAAIV,KAAK,CAACU,OAAO,IAAI,eAAe,CAAC;UACrHK,KAAK,CAAC,IAAI,CAACf,KAAK,CAAC;UACjB,IAAI,CAACD,OAAO,GAAG,KAAK;QACtB;OACD,CAAC;;EAEN;;;uBA3QWX,eAAe,EAAAyD,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,aAAA;IAAA;EAAA;;;YAAf9D,eAAe;MAAA+D,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCnB5BX,EAAA,CAAAa,cAAA,QAAG;UAAAb,EAAA,CAAAc,MAAA,oBAAa;UAAAd,EAAA,CAAAe,YAAA,EAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}