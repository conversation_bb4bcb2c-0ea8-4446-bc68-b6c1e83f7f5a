{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/equipe.service\";\nimport * as i2 from \"src/app/services/membre.service\";\nimport * as i3 from \"src/app/services/auth.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"src/app/services/notification.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/forms\";\nfunction EquipeFormComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"div\", 22);\n    i0.ɵɵelement(2, \"div\", 23)(3, \"div\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 25);\n    i0.ɵɵtext(5, \" Chargement des donn\\u00E9es... \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction EquipeFormComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"div\", 27)(2, \"div\", 28)(3, \"div\", 29);\n    i0.ɵɵelement(4, \"i\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 31)(6, \"h3\", 32);\n    i0.ɵɵtext(7, \" Erreur \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 33);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.error, \" \");\n  }\n}\nfunction EquipeFormComponent_div_33_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 69);\n    i0.ɵɵelement(1, \"i\", 70);\n    i0.ɵɵtext(2, \" Ce nom d'\\u00E9quipe existe d\\u00E9j\\u00E0. Veuillez en choisir un autre. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipeFormComponent_div_33_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 69);\n    i0.ɵɵelement(1, \"i\", 70);\n    i0.ɵɵtext(2, \" Le nom de l'\\u00E9quipe doit contenir au moins 3 caract\\u00E8res. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipeFormComponent_div_33_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71);\n    i0.ɵɵelement(1, \"i\", 72);\n    i0.ɵɵtext(2, \" Le nom de l'\\u00E9quipe est requis. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipeFormComponent_div_33_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 69);\n    i0.ɵɵelement(1, \"i\", 70);\n    i0.ɵɵtext(2, \" La description doit contenir au moins 10 caract\\u00E8res. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipeFormComponent_div_33_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71);\n    i0.ɵɵelement(1, \"i\", 72);\n    i0.ɵɵtext(2, \" La description de l'\\u00E9quipe est requise. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipeFormComponent_div_33_div_40_div_7_tr_20_small_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 101);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const membreId_r20 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"ID: \", membreId_r20, \"\");\n  }\n}\nfunction EquipeFormComponent_div_33_div_40_div_7_tr_20_a_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 102);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const membreId_r20 = i0.ɵɵnextContext().$implicit;\n    const ctx_r22 = i0.ɵɵnextContext(4);\n    i0.ɵɵpropertyInterpolate1(\"href\", \"mailto:\", ctx_r22.getMembreEmail(membreId_r20), \"\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r22.getMembreEmail(membreId_r20), \" \");\n  }\n}\nfunction EquipeFormComponent_div_33_div_40_div_7_tr_20_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 103);\n    i0.ɵɵtext(1, \"Non renseign\\u00E9\");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c0 = function (a0, a1, a2) {\n  return {\n    \"bg-primary\": a0,\n    \"bg-success\": a1,\n    \"bg-secondary\": a2\n  };\n};\nconst _c1 = function (a0, a1, a2) {\n  return {\n    \"bi-mortarboard-fill\": a0,\n    \"bi-briefcase-fill\": a1,\n    \"bi-question-circle-fill\": a2\n  };\n};\nfunction EquipeFormComponent_div_33_div_40_div_7_tr_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 93)(1, \"td\")(2, \"span\", 94);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, EquipeFormComponent_div_33_div_40_div_7_tr_20_small_4_Template, 2, 1, \"small\", 95);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtemplate(6, EquipeFormComponent_div_33_div_40_div_7_tr_20_a_6_Template, 2, 2, \"a\", 96);\n    i0.ɵɵtemplate(7, EquipeFormComponent_div_33_div_40_div_7_tr_20_span_7_Template, 2, 0, \"span\", 97);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\")(9, \"span\", 98);\n    i0.ɵɵelement(10, \"i\", 39);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"td\", 91)(13, \"button\", 99);\n    i0.ɵɵlistener(\"click\", function EquipeFormComponent_div_33_div_40_div_7_tr_20_Template_button_click_13_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r27);\n      const membreId_r20 = restoredCtx.$implicit;\n      const ctx_r26 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r26.removeMembreFromEquipe(membreId_r20));\n    });\n    i0.ɵɵelement(14, \"i\", 100);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const membreId_r20 = ctx.$implicit;\n    const ctx_r19 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r19.getMembreName(membreId_r20));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r19.getMembreName(membreId_r20) !== membreId_r20);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r19.getMembreEmail(membreId_r20) !== \"Non renseign\\u00E9\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r19.getMembreEmail(membreId_r20) === \"Non renseign\\u00E9\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(7, _c0, ctx_r19.getMembreProfession(membreId_r20) === \"\\u00C9tudiant\", ctx_r19.getMembreProfession(membreId_r20) === \"Professeur\", ctx_r19.getMembreProfession(membreId_r20) === \"Non sp\\u00E9cifi\\u00E9\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(11, _c1, ctx_r19.getMembreProfession(membreId_r20) === \"\\u00C9tudiant\", ctx_r19.getMembreProfession(membreId_r20) === \"Professeur\", ctx_r19.getMembreProfession(membreId_r20) === \"Non sp\\u00E9cifi\\u00E9\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r19.getMembreProfession(membreId_r20), \" \");\n  }\n}\nfunction EquipeFormComponent_div_33_div_40_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 84)(2, \"table\", 85)(3, \"thead\", 86)(4, \"tr\")(5, \"th\")(6, \"div\", 87);\n    i0.ɵɵelement(7, \"i\", 88);\n    i0.ɵɵtext(8, \" Nom et Pr\\u00E9nom \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"th\")(10, \"div\", 87);\n    i0.ɵɵelement(11, \"i\", 89);\n    i0.ɵɵtext(12, \" Email \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"th\")(14, \"div\", 87);\n    i0.ɵɵelement(15, \"i\", 90);\n    i0.ɵɵtext(16, \" Statut \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"th\", 91);\n    i0.ɵɵtext(18, \"Actions\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(19, \"tbody\");\n    i0.ɵɵtemplate(20, EquipeFormComponent_div_33_div_40_div_7_tr_20_Template, 15, 15, \"tr\", 92);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(20);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r14.equipe.members);\n  }\n}\nfunction EquipeFormComponent_div_33_div_40_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 104);\n    i0.ɵɵelement(1, \"i\", 105);\n    i0.ɵɵelementStart(2, \"h5\", 106);\n    i0.ɵɵtext(3, \" Aucun membre dans cette \\u00E9quipe \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 106);\n    i0.ɵɵtext(5, \" Ajoutez des membres \\u00E0 l'\\u00E9quipe en utilisant le formulaire ci-dessous. \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction EquipeFormComponent_div_33_div_40_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58);\n    i0.ɵɵelement(1, \"i\", 59);\n    i0.ɵɵelementStart(2, \"div\");\n    i0.ɵɵtext(3, \" Aucun utilisateur disponible. Veuillez d'abord cr\\u00E9er des utilisateurs. \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction EquipeFormComponent_div_33_div_40_div_15_option_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 124);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r31 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", user_r31._id || user_r31.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate4(\" \", user_r31.firstName || \"\", \" \", user_r31.lastName || user_r31.name || user_r31.id, \" \", user_r31.email ? \"- \" + user_r31.email : \"\", \" \", user_r31.profession ? \"(\" + (user_r31.profession === \"etudiant\" ? \"\\u00C9tudiant\" : \"Professeur\") + \")\" : user_r31.role ? \"(\" + (user_r31.role === \"etudiant\" ? \"\\u00C9tudiant\" : \"Professeur\") + \")\" : \"\", \" \");\n  }\n}\nfunction EquipeFormComponent_div_33_div_40_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r33 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 107)(1, \"div\", 40)(2, \"div\", 108)(3, \"div\", 109)(4, \"label\", 110);\n    i0.ɵɵtext(5, \"Utilisateur\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"select\", 111, 112)(8, \"option\", 113);\n    i0.ɵɵtext(9, \" S\\u00E9lectionnez un utilisateur \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, EquipeFormComponent_div_33_div_40_div_15_option_10_Template, 2, 5, \"option\", 114);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 115)(12, \"label\", 116);\n    i0.ɵɵtext(13, \"R\\u00F4le dans l'\\u00E9quipe\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"select\", 117, 118)(16, \"option\", 119);\n    i0.ɵɵtext(17, \"Membre\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"option\", 120);\n    i0.ɵɵtext(19, \"Administrateur\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(20, \"div\", 121)(21, \"button\", 122);\n    i0.ɵɵlistener(\"click\", function EquipeFormComponent_div_33_div_40_div_15_Template_button_click_21_listener() {\n      i0.ɵɵrestoreView(_r33);\n      const _r28 = i0.ɵɵreference(7);\n      const _r30 = i0.ɵɵreference(15);\n      const ctx_r32 = i0.ɵɵnextContext(3);\n      ctx_r32.addMembreToEquipe(_r28.value, _r30.value);\n      return i0.ɵɵresetView(_r28.value = \"\");\n    });\n    i0.ɵɵelement(22, \"i\", 123);\n    i0.ɵɵtext(23, \" Ajouter \");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const _r28 = i0.ɵɵreference(7);\n    const ctx_r18 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r18.availableUsers);\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"disabled\", !_r28.value);\n  }\n}\nfunction EquipeFormComponent_div_33_div_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61)(1, \"div\", 73)(2, \"div\", 74)(3, \"h4\", 75);\n    i0.ɵɵelement(4, \"i\", 76);\n    i0.ɵɵtext(5, \" Membres de l'\\u00E9quipe \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 40);\n    i0.ɵɵtemplate(7, EquipeFormComponent_div_33_div_40_div_7_Template, 21, 1, \"div\", 77);\n    i0.ɵɵtemplate(8, EquipeFormComponent_div_33_div_40_ng_template_8_Template, 6, 0, \"ng-template\", null, 78, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementStart(10, \"div\", 79)(11, \"h5\", 80);\n    i0.ɵɵelement(12, \"i\", 81);\n    i0.ɵɵtext(13, \" Ajouter un membre \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, EquipeFormComponent_div_33_div_40_div_14_Template, 4, 0, \"div\", 82);\n    i0.ɵɵtemplate(15, EquipeFormComponent_div_33_div_40_div_15_Template, 24, 2, \"div\", 83);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const _r15 = i0.ɵɵreference(9);\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.equipe.members && ctx_r10.equipe.members.length > 0)(\"ngIfElse\", _r15);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.availableUsers.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.availableUsers.length > 0);\n  }\n}\nfunction EquipeFormComponent_div_33_button_47_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r35 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 125);\n    i0.ɵɵlistener(\"click\", function EquipeFormComponent_div_33_button_47_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r34 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r34.deleteEquipe());\n    });\n    i0.ɵɵelement(1, \"i\", 126);\n    i0.ɵɵtext(2, \" Supprimer \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipeFormComponent_div_33_span_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 127);\n  }\n}\nconst _c2 = function (a0, a1) {\n  return {\n    \"bi-save\": a0,\n    \"bi-plus-circle\": a1\n  };\n};\nfunction EquipeFormComponent_div_33_i_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 39);\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(1, _c2, ctx_r13.isEditMode, !ctx_r13.isEditMode));\n  }\n}\nconst _c3 = function (a0, a1) {\n  return {\n    \"bi-pencil-square\": a0,\n    \"bi-plus-circle\": a1\n  };\n};\nfunction EquipeFormComponent_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r37 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35)(2, \"div\", 36)(3, \"div\", 37)(4, \"h3\", 38);\n    i0.ɵɵelement(5, \"i\", 39);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 40)(8, \"form\", 41);\n    i0.ɵɵlistener(\"ngSubmit\", function EquipeFormComponent_div_33_Template_form_ngSubmit_8_listener() {\n      i0.ɵɵrestoreView(_r37);\n      const ctx_r36 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r36.onSubmit());\n    });\n    i0.ɵɵelementStart(9, \"div\", 42)(10, \"label\", 43);\n    i0.ɵɵtext(11, \"Nom de l'\\u00E9quipe \");\n    i0.ɵɵelementStart(12, \"span\", 44);\n    i0.ɵɵtext(13, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 45)(15, \"span\", 46);\n    i0.ɵɵelement(16, \"i\", 47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"input\", 48, 49);\n    i0.ɵɵlistener(\"input\", function EquipeFormComponent_div_33_Template_input_input_17_listener() {\n      i0.ɵɵrestoreView(_r37);\n      const _r3 = i0.ɵɵreference(18);\n      const ctx_r38 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r38.updateName(_r3.value));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(19, EquipeFormComponent_div_33_div_19_Template, 3, 0, \"div\", 50);\n    i0.ɵɵtemplate(20, EquipeFormComponent_div_33_div_20_Template, 3, 0, \"div\", 50);\n    i0.ɵɵtemplate(21, EquipeFormComponent_div_33_div_21_Template, 3, 0, \"div\", 51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 42)(23, \"label\", 52);\n    i0.ɵɵtext(24, \"Description \");\n    i0.ɵɵelementStart(25, \"span\", 44);\n    i0.ɵɵtext(26, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 45)(28, \"span\", 53);\n    i0.ɵɵelement(29, \"i\", 54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"textarea\", 55, 56);\n    i0.ɵɵlistener(\"input\", function EquipeFormComponent_div_33_Template_textarea_input_30_listener() {\n      i0.ɵɵrestoreView(_r37);\n      const _r7 = i0.ɵɵreference(31);\n      const ctx_r39 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r39.updateDescription(_r7.value));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(32, EquipeFormComponent_div_33_div_32_Template, 3, 0, \"div\", 50);\n    i0.ɵɵtemplate(33, EquipeFormComponent_div_33_div_33_Template, 3, 0, \"div\", 51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(34, \"input\", 57);\n    i0.ɵɵelementStart(35, \"div\", 42)(36, \"div\", 58);\n    i0.ɵɵelement(37, \"i\", 59);\n    i0.ɵɵelementStart(38, \"div\");\n    i0.ɵɵtext(39, \" Un administrateur par d\\u00E9faut sera assign\\u00E9 \\u00E0 cette \\u00E9quipe. \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(40, EquipeFormComponent_div_33_div_40_Template, 16, 4, \"div\", 60);\n    i0.ɵɵelementStart(41, \"div\", 61)(42, \"div\", 62)(43, \"div\")(44, \"button\", 63);\n    i0.ɵɵlistener(\"click\", function EquipeFormComponent_div_33_Template_button_click_44_listener() {\n      i0.ɵɵrestoreView(_r37);\n      const ctx_r40 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r40.cancel());\n    });\n    i0.ɵɵelement(45, \"i\", 64);\n    i0.ɵɵtext(46, \" Retour \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(47, EquipeFormComponent_div_33_button_47_Template, 3, 0, \"button\", 65);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"button\", 66);\n    i0.ɵɵtemplate(49, EquipeFormComponent_div_33_span_49_Template, 1, 0, \"span\", 67);\n    i0.ɵɵtemplate(50, EquipeFormComponent_div_33_i_50_Template, 1, 4, \"i\", 68);\n    i0.ɵɵtext(51);\n    i0.ɵɵelementEnd()()()()()()()();\n  }\n  if (rf & 2) {\n    const _r3 = i0.ɵɵreference(18);\n    const _r7 = i0.ɵɵreference(31);\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(20, _c3, ctx_r2.isEditMode, !ctx_r2.isEditMode));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.isEditMode ? \"Informations de l'\\u00E9quipe\" : \"D\\u00E9tails de la nouvelle \\u00E9quipe\", \" \");\n    i0.ɵɵadvance(11);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r2.nameExists || ctx_r2.nameError && _r3.value.length > 0);\n    i0.ɵɵproperty(\"value\", ctx_r2.equipe.name || \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.nameExists);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.nameError && _r3.value.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.error && !ctx_r2.equipe.name);\n    i0.ɵɵadvance(9);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r2.descriptionError && _r7.value.length > 0);\n    i0.ɵɵproperty(\"value\", ctx_r2.equipe.description || \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.descriptionError && _r7.value.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.error && !ctx_r2.equipe.description);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"value\", ctx_r2.equipe.admin);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isEditMode && ctx_r2.equipe._id);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isEditMode && ctx_r2.equipeId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.submitting || !ctx_r2.equipe.name || !ctx_r2.equipe.description || ctx_r2.nameExists || ctx_r2.nameError || ctx_r2.descriptionError);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.submitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.submitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.isEditMode ? \"Mettre \\u00E0 jour\" : \"Cr\\u00E9er l'\\u00E9quipe\", \" \");\n  }\n}\nexport class EquipeFormComponent {\n  constructor(equipeService, membreService, userService, route, router, notificationService) {\n    this.equipeService = equipeService;\n    this.membreService = membreService;\n    this.userService = userService;\n    this.route = route;\n    this.router = router;\n    this.notificationService = notificationService;\n    this.equipe = {\n      name: '',\n      description: '',\n      admin: '65f1e5b3a1d8f3c8c0f9e8d7' // ID temporaire\n    };\n\n    this.isEditMode = false;\n    this.loading = false;\n    this.submitting = false;\n    this.error = null;\n    this.equipeId = null;\n    this.nameExists = false;\n    this.nameError = false;\n    this.descriptionError = false;\n    this.checkingName = false;\n    this.existingEquipes = [];\n    this.availableMembers = []; // Liste des membres disponibles\n    this.availableUsers = []; // Liste des utilisateurs disponibles\n  }\n\n  ngOnInit() {\n    console.log('EquipeFormComponent initialized');\n    // Ensure equipe is always defined with admin\n    if (!this.equipe) {\n      this.equipe = {\n        name: '',\n        description: '',\n        admin: '65f1e5b3a1d8f3c8c0f9e8d7' // ID temporaire\n      };\n    }\n    // Charger toutes les équipes pour vérifier les noms existants\n    this.loadAllEquipes();\n    // Charger tous les membres disponibles\n    this.loadAllMembers();\n    // Charger tous les utilisateurs disponibles\n    this.loadAllUsers();\n    try {\n      // Vérifier si nous sommes en mode édition (si un ID est présent dans l'URL)\n      this.equipeId = this.route.snapshot.paramMap.get('id');\n      this.isEditMode = !!this.equipeId;\n      console.log('Edit mode:', this.isEditMode, 'ID:', this.equipeId);\n      if (this.isEditMode && this.equipeId) {\n        this.loadEquipe(this.equipeId);\n        // Ajouter un délai pour s'assurer que l'équipe est chargée\n        setTimeout(() => {\n          console.log('Après délai - this.equipeId:', this.equipeId);\n          console.log('Après délai - this.equipe:', this.equipe);\n        }, 1000);\n      }\n    } catch (error) {\n      console.error('Error in ngOnInit:', error);\n      this.error = \"Erreur d'initialisation\";\n    }\n    // Ajouter un gestionnaire d'événements pour le bouton d'ajout de membre\n    setTimeout(() => {\n      const addButton = document.getElementById('addMembreButton');\n      if (addButton) {\n        console.log(\"Bouton d'ajout de membre trouvé\");\n        addButton.addEventListener('click', () => {\n          console.log(\"Bouton d'ajout de membre cliqué\");\n        });\n      } else {\n        console.log(\"Bouton d'ajout de membre non trouvé\");\n      }\n    }, 2000);\n  }\n  loadAllMembers() {\n    this.membreService.getMembres().subscribe({\n      next: membres => {\n        this.availableMembers = membres;\n        console.log('Membres disponibles chargés:', membres);\n      },\n      error: error => {\n        console.error('Erreur lors du chargement des membres:', error);\n        this.error = 'Impossible de charger la liste des membres. Veuillez réessayer plus tard.';\n      }\n    });\n  }\n  loadAllUsers() {\n    const token = localStorage.getItem('token');\n    if (token) {\n      this.userService.getAllUsers(token).subscribe({\n        next: users => {\n          this.availableUsers = users;\n          console.log('Utilisateurs disponibles chargés:', users);\n        },\n        error: error => {\n          console.error('Erreur lors du chargement des utilisateurs:', error);\n          this.error = 'Impossible de charger la liste des utilisateurs. Veuillez réessayer plus tard.';\n        }\n      });\n    }\n  }\n  loadAllEquipes() {\n    this.equipeService.getEquipes().subscribe({\n      next: equipes => {\n        this.existingEquipes = equipes;\n        console.log('Équipes existantes chargées:', equipes);\n      },\n      error: error => {\n        console.error('Erreur lors du chargement des équipes:', error);\n      }\n    });\n  }\n  loadEquipe(id) {\n    console.log('Loading equipe with ID:', id);\n    this.loading = true;\n    this.error = null;\n    this.equipeService.getEquipe(id).subscribe({\n      next: data => {\n        console.log('Équipe chargée:', data);\n        this.equipe = data;\n        // Vérifier que l'ID est correctement défini\n        console.log(\"ID de l'équipe après chargement:\", this.equipe._id);\n        console.log('this.equipeId:', this.equipeId);\n        // Si l'équipe a des membres, récupérer les informations de chaque membre\n        if (this.equipe.members && this.equipe.members.length > 0) {\n          this.loadMembersDetails();\n        }\n        this.loading = false;\n      },\n      error: error => {\n        console.error(\"Erreur lors du chargement de l'équipe:\", error);\n        this.error = \"Impossible de charger les détails de l'équipe. Veuillez réessayer plus tard.\";\n        this.loading = false;\n      }\n    });\n  }\n  // Méthode pour récupérer les détails des membres de l'équipe\n  loadMembersDetails() {\n    if (!this.equipe.members || this.equipe.members.length === 0) {\n      return;\n    }\n    console.log(\"Chargement des détails des membres de l'équipe...\");\n    // Pour chaque membre de l'équipe, essayer de trouver ses informations dans la liste des utilisateurs\n    this.equipe.members.forEach(membreId => {\n      // Chercher d'abord dans la liste des utilisateurs\n      const user = this.availableUsers.find(u => u._id === membreId || u.id === membreId);\n      if (user) {\n        console.log(`Membre ${membreId} trouvé dans la liste des utilisateurs:`, user);\n        // Vérifier si toutes les informations nécessaires sont présentes\n        if (!user.email || !user.profession && !user.role) {\n          // Si des informations manquent, essayer de les récupérer depuis l'API\n          const token = localStorage.getItem('token');\n          if (token) {\n            this.userService.getUserById(membreId, token).subscribe({\n              next: userData => {\n                console.log(`Détails supplémentaires de l'utilisateur ${membreId} récupérés:`, userData);\n                // Mettre à jour l'utilisateur dans la liste avec les nouvelles informations\n                const index = this.availableUsers.findIndex(u => u._id === membreId || u.id === membreId);\n                if (index !== -1) {\n                  this.availableUsers[index] = {\n                    ...this.availableUsers[index],\n                    ...userData\n                  };\n                }\n              },\n              error: error => {\n                console.error(`Erreur lors de la récupération des détails supplémentaires de l'utilisateur ${membreId}:`, error);\n              }\n            });\n          }\n        }\n      } else {\n        // Si non trouvé, essayer de récupérer l'utilisateur depuis l'API\n        const token = localStorage.getItem('token');\n        if (token) {\n          this.userService.getUserById(membreId, token).subscribe({\n            next: userData => {\n              console.log(`Détails de l'utilisateur ${membreId} récupérés:`, userData);\n              // Ajouter l'utilisateur à la liste des utilisateurs disponibles s'il n'y est pas déjà\n              if (!this.availableUsers.some(u => u._id === userData._id || u.id === userData.id)) {\n                this.availableUsers.push(userData);\n              }\n            },\n            error: error => {\n              console.error(`Erreur lors de la récupération des détails de l'utilisateur ${membreId}:`, error);\n            }\n          });\n        }\n      }\n    });\n  }\n  checkNameExists(name) {\n    // En mode édition, ignorer l'équipe actuelle\n    if (this.isEditMode && this.equipeId) {\n      return this.existingEquipes.some(e => e.name === name && e._id !== this.equipeId);\n    }\n    // En mode création, vérifier tous les noms\n    return this.existingEquipes.some(e => e.name === name);\n  }\n  updateName(value) {\n    console.log('Name updated:', value);\n    this.equipe.name = value;\n    // Vérifier si le nom existe déjà\n    this.nameExists = this.checkNameExists(value);\n    if (this.nameExists) {\n      console.warn(\"Ce nom d'équipe existe déjà\");\n    }\n    // Vérifier si le nom a au moins 3 caractères\n    this.nameError = value.length > 0 && value.length < 3;\n    if (this.nameError) {\n      console.warn('Le nom doit contenir au moins 3 caractères');\n    }\n  }\n  updateDescription(value) {\n    console.log('Description updated:', value);\n    this.equipe.description = value;\n    // Vérifier si la description a au moins 10 caractères\n    this.descriptionError = value.length > 0 && value.length < 10;\n    if (this.descriptionError) {\n      console.warn('La description doit contenir au moins 10 caractères');\n    }\n  }\n  onSubmit() {\n    console.log('Form submitted with:', this.equipe);\n    // Vérifier si le nom est présent et valide\n    if (!this.equipe.name) {\n      this.error = \"Le nom de l'équipe est requis.\";\n      return;\n    }\n    if (this.equipe.name.length < 3) {\n      this.nameError = true;\n      this.error = \"Le nom de l'équipe doit contenir au moins 3 caractères.\";\n      return;\n    }\n    // Vérifier si la description est présente et valide\n    if (!this.equipe.description) {\n      this.error = \"La description de l'équipe est requise.\";\n      return;\n    }\n    if (this.equipe.description.length < 10) {\n      this.descriptionError = true;\n      this.error = \"La description de l'équipe doit contenir au moins 10 caractères.\";\n      return;\n    }\n    // Vérifier si le nom existe déjà avant de soumettre\n    if (this.checkNameExists(this.equipe.name)) {\n      this.nameExists = true;\n      this.error = 'Une équipe avec ce nom existe déjà. Veuillez choisir un autre nom.';\n      return;\n    }\n    this.submitting = true;\n    this.error = null;\n    // Créer une copie de l'objet équipe pour éviter les problèmes de référence\n    const equipeToSave = {\n      name: this.equipe.name,\n      description: this.equipe.description || '',\n      admin: this.equipe.admin\n    };\n    // Ajouter l'ID si nous sommes en mode édition\n    if (this.isEditMode && this.equipeId) {\n      equipeToSave._id = this.equipeId;\n    }\n    console.log('Données à envoyer:', equipeToSave);\n    if (this.isEditMode && this.equipeId) {\n      // Mode édition\n      this.equipeService.updateEquipe(this.equipeId, equipeToSave).subscribe({\n        next: response => {\n          console.log('Équipe mise à jour avec succès:', response);\n          this.submitting = false;\n          this.notificationService.showSuccess(`L'équipe \"${response.name}\" a été mise à jour avec succès.`);\n          this.router.navigate(['/equipes/liste']);\n        },\n        error: error => {\n          console.error(\"Erreur lors de la mise à jour de l'équipe:\", error);\n          this.error = `Impossible de mettre à jour l'équipe: ${error.message}`;\n          this.submitting = false;\n          this.notificationService.showError(`Erreur: ${error.message}`);\n        }\n      });\n    } else {\n      // Mode ajout\n      this.equipeService.addEquipe(equipeToSave).subscribe({\n        next: response => {\n          console.log('Équipe ajoutée avec succès:', response);\n          this.submitting = false;\n          this.notificationService.showSuccess(`L'équipe \"${response.name}\" a été créée avec succès.`);\n          this.router.navigate(['/equipes/liste']);\n        },\n        error: error => {\n          console.error(\"Erreur lors de l'ajout de l'équipe:\", error);\n          this.error = `Impossible d'ajouter l'équipe: ${error.message}`;\n          this.submitting = false;\n          this.notificationService.showError(`Erreur: ${error.message}`);\n        }\n      });\n    }\n  }\n  cancel() {\n    console.log('Form cancelled');\n    this.router.navigate(['/equipes/liste']);\n  }\n  // Méthodes pour gérer les membres\n  addMembreToEquipe(membreId, role = 'membre') {\n    console.log('Début de addMembreToEquipe avec membreId:', membreId, 'et rôle:', role);\n    console.log('État actuel - this.equipeId:', this.equipeId);\n    console.log('État actuel - this.equipe:', this.equipe);\n    // Utiliser this.equipe._id si this.equipeId n'est pas défini\n    const equipeId = this.equipeId || this.equipe && this.equipe._id;\n    console.log('equipeId calculé:', equipeId);\n    if (!equipeId || !membreId) {\n      console.error(\"ID d'équipe ou ID de membre manquant\");\n      this.error = \"ID d'équipe ou ID de membre manquant\";\n      console.log('equipeId:', equipeId, 'membreId:', membreId);\n      // Afficher un message à l'utilisateur\n      this.notificationService.showError(\"Impossible d'ajouter le membre: ID d'équipe ou ID de membre manquant\");\n      return;\n    }\n    // Vérifier si le membre est déjà dans l'équipe\n    if (this.equipe.members && this.equipe.members.includes(membreId)) {\n      this.notificationService.showError(\"Ce membre fait déjà partie de l'équipe\");\n      return;\n    }\n    // Récupérer les informations de l'utilisateur pour afficher un message plus informatif\n    const user = this.availableUsers.find(u => u._id === membreId || u.id === membreId);\n    const userName = user ? user.firstName && user.lastName ? `${user.firstName} ${user.lastName}` : user.name || membreId : membreId;\n    // Créer l'objet membre avec le rôle spécifié\n    const membre = {\n      id: membreId,\n      role: role\n    };\n    this.loading = true;\n    console.log(`Ajout de l'utilisateur \"${userName}\" comme ${role} à l'équipe ${equipeId}`);\n    this.equipeService.addMembreToEquipe(equipeId, membre).subscribe({\n      next: response => {\n        console.log('Membre ajouté avec succès:', response);\n        this.notificationService.showSuccess(`${userName} a été ajouté comme ${role === 'admin' ? 'administrateur' : 'membre'} à l'équipe`);\n        // Recharger l'équipe pour mettre à jour la liste des membres\n        this.loadEquipe(equipeId);\n        this.loading = false;\n      },\n      error: error => {\n        console.error(\"Erreur lors de l'ajout du membre:\", error);\n        this.error = \"Impossible d'ajouter le membre. Veuillez réessayer plus tard.\";\n        this.notificationService.showError(\"Erreur lors de l'ajout du membre: \" + error.message);\n        this.loading = false;\n      }\n    });\n  }\n  // Méthode pour obtenir le nom complet d'un membre à partir de son ID\n  getMembreName(membreId) {\n    // Chercher d'abord dans la liste des utilisateurs\n    const user = this.availableUsers.find(u => u._id === membreId || u.id === membreId);\n    if (user) {\n      if (user.firstName && user.lastName) {\n        return `${user.firstName} ${user.lastName}`;\n      } else if (user.name) {\n        return user.name;\n      }\n    }\n    // Chercher ensuite dans la liste des membres\n    const membre = this.availableMembers.find(m => m._id === membreId || m.id === membreId);\n    if (membre && membre.name) {\n      return membre.name;\n    }\n    // Si aucun nom n'est trouvé, retourner l'ID\n    return membreId;\n  }\n  // Méthode pour obtenir l'email d'un membre\n  getMembreEmail(membreId) {\n    const user = this.availableUsers.find(u => u._id === membreId || u.id === membreId);\n    if (user && user.email) {\n      return user.email;\n    }\n    return 'Non renseigné';\n  }\n  // Méthode pour obtenir la profession d'un membre\n  getMembreProfession(membreId) {\n    const user = this.availableUsers.find(u => u._id === membreId || u.id === membreId);\n    if (user) {\n      if (user.profession) {\n        return user.profession === 'etudiant' ? 'Étudiant' : 'Professeur';\n      } else if (user.role) {\n        return user.role === 'etudiant' ? 'Étudiant' : 'Professeur';\n      }\n    }\n    return 'Non spécifié';\n  }\n  // Méthode pour obtenir le rôle d'un membre dans l'équipe\n  getMembreRole(_membreId) {\n    // Cette méthode nécessiterait d'avoir accès aux rôles des membres dans l'équipe\n    // Pour l'instant, nous retournons une valeur par défaut\n    return 'Membre';\n  }\n  removeMembreFromEquipe(membreId) {\n    console.log('Méthode removeMembreFromEquipe appelée avec ID:', membreId);\n    console.log('État actuel - this.equipeId:', this.equipeId);\n    console.log('État actuel - this.equipe:', this.equipe);\n    // Utiliser this.equipe._id si this.equipeId n'est pas défini\n    const equipeId = this.equipeId || this.equipe && this.equipe._id;\n    if (!equipeId) {\n      console.error(\"ID d'équipe manquant\");\n      this.error = \"ID d'équipe manquant. Impossible de retirer le membre.\";\n      this.notificationService.showError(\"ID d'équipe manquant. Impossible de retirer le membre.\");\n      return;\n    }\n    if (!membreId) {\n      console.error('ID de membre manquant');\n      this.error = 'ID de membre manquant. Impossible de retirer le membre.';\n      this.notificationService.showError('ID de membre manquant. Impossible de retirer le membre.');\n      return;\n    }\n    // Obtenir le nom du membre pour l'afficher dans le message de confirmation\n    const membreName = this.getMembreName(membreId);\n    console.log(`Tentative de retrait de l'utilisateur ${membreId} (${membreName}) de l'équipe ${equipeId}`);\n    try {\n      if (confirm(`Êtes-vous sûr de vouloir retirer ${membreName} de l'équipe?`)) {\n        console.log('Confirmation acceptée, suppression en cours...');\n        this.loading = true;\n        this.error = null;\n        // Ajouter un délai pour s'assurer que l'utilisateur voit le chargement\n        setTimeout(() => {\n          this.equipeService.removeMembreFromEquipe(equipeId, membreId).subscribe({\n            next: response => {\n              console.log(`Utilisateur \"${membreName}\" retiré avec succès de l'équipe:`, response);\n              this.loading = false;\n              this.notificationService.showSuccess(`${membreName} a été retiré avec succès de l'équipe`);\n              // Recharger l'équipe pour mettre à jour la liste des membres\n              this.loadEquipe(equipeId);\n            },\n            error: error => {\n              console.error(`Erreur lors du retrait de l'utilisateur \"${membreName}\":`, error);\n              console.error(\"Détails de l'erreur:\", {\n                status: error.status,\n                message: error.message,\n                error: error\n              });\n              this.loading = false;\n              this.error = `Impossible de retirer l'utilisateur \"${membreName}\" de l'équipe: ${error.message || 'Erreur inconnue'}`;\n              this.notificationService.showError(`Erreur lors du retrait du membre: ${this.error}`);\n            }\n          });\n        }, 500);\n      } else {\n        console.log(\"Suppression annulée par l'utilisateur\");\n      }\n    } catch (error) {\n      console.error('Exception lors du retrait du membre:', error);\n      this.error = `Exception: ${error?.message || 'Erreur inconnue'}`;\n      this.notificationService.showError(`Exception: ${this.error}`);\n    }\n  }\n  // Méthode pour supprimer l'équipe\n  deleteEquipe() {\n    console.log('Méthode deleteEquipe appelée dans equipe-form.component.ts');\n    console.log('État actuel - this.equipeId:', this.equipeId);\n    console.log('État actuel - this.equipe:', this.equipe);\n    // Utiliser this.equipe._id si this.equipeId n'est pas défini\n    const equipeId = this.equipeId || this.equipe && this.equipe._id;\n    if (!equipeId) {\n      console.error(\"ID d'équipe manquant\");\n      this.error = \"ID d'équipe manquant. Impossible de supprimer l'équipe.\";\n      this.notificationService.showError(\"ID d'équipe manquant. Impossible de supprimer l'équipe.\");\n      return;\n    }\n    console.log(\"ID de l'équipe à supprimer (final):\", equipeId);\n    try {\n      if (confirm(`Êtes-vous sûr de vouloir supprimer l'équipe \"${this.equipe.name}\"? Cette action est irréversible.`)) {\n        console.log('Confirmation acceptée, suppression en cours...');\n        this.loading = true;\n        this.error = null;\n        // Ajouter un délai pour s'assurer que l'utilisateur voit le chargement\n        setTimeout(() => {\n          this.equipeService.deleteEquipe(equipeId).subscribe({\n            next: response => {\n              console.log('Équipe supprimée avec succès, réponse:', response);\n              this.loading = false;\n              this.notificationService.showSuccess(`L'équipe \"${this.equipe.name}\" a été supprimée avec succès.`);\n              // Ajouter un délai avant la redirection\n              setTimeout(() => {\n                this.router.navigate(['/equipes/liste']);\n              }, 500);\n            },\n            error: error => {\n              console.error(\"Erreur lors de la suppression de l'équipe:\", error);\n              console.error(\"Détails de l'erreur:\", {\n                status: error.status,\n                message: error.message,\n                error: error\n              });\n              this.loading = false;\n              this.error = `Impossible de supprimer l'équipe: ${error.message || 'Erreur inconnue'}`;\n              this.notificationService.showError(`Erreur lors de la suppression: ${this.error}`);\n            }\n          });\n        }, 500);\n      } else {\n        console.log(\"Suppression annulée par l'utilisateur\");\n      }\n    } catch (error) {\n      console.error('Exception lors de la suppression:', error);\n      this.error = `Exception: ${error?.message || 'Erreur inconnue'}`;\n      this.notificationService.showError(`Exception: ${this.error}`);\n    }\n  }\n  static {\n    this.ɵfac = function EquipeFormComponent_Factory(t) {\n      return new (t || EquipeFormComponent)(i0.ɵɵdirectiveInject(i1.EquipeService), i0.ɵɵdirectiveInject(i2.MembreService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.ActivatedRoute), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i5.NotificationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: EquipeFormComponent,\n      selectors: [[\"app-equipe-form\"]],\n      decls: 34,\n      vars: 5,\n      consts: [[1, \"min-h-screen\", \"bg-[#f0f4f8]\", \"dark:bg-[#0a0a0a]\", \"relative\", \"overflow-hidden\"], [1, \"absolute\", \"inset-0\", \"overflow-hidden\", \"pointer-events-none\"], [1, \"absolute\", \"top-[15%]\", \"left-[10%]\", \"w-64\", \"h-64\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#00f7ff]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"bottom-[20%]\", \"right-[10%]\", \"w-80\", \"h-80\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#00f7ff]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"inset-0\", \"opacity-5\", \"dark:opacity-[0.03]\"], [1, \"h-full\", \"grid\", \"grid-cols-12\"], [1, \"border-r\", \"border-[#4f5fad]\", \"dark:border-[#00f7ff]\"], [1, \"max-w-4xl\", \"mx-auto\", \"p-6\", \"relative\", \"z-10\"], [1, \"mb-8\", \"relative\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"dark:from-[#00f7ff]\", \"dark:to-[#4f5fad]\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"dark:from-[#00f7ff]\", \"dark:to-[#4f5fad]\", \"blur-md\"], [1, \"bg-white\", \"dark:bg-[#1a1a1a]\", \"rounded-xl\", \"shadow-lg\", \"dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)]\", \"p-6\", \"backdrop-blur-sm\", \"border\", \"border-[#4f5fad]/20\", \"dark:border-[#00f7ff]/20\"], [1, \"flex\", \"flex-col\", \"lg:flex-row\", \"lg:items-center\", \"lg:justify-between\"], [1, \"mb-4\", \"lg:mb-0\"], [1, \"text-3xl\", \"font-bold\", \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\", \"mb-2\", \"tracking-wide\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"text-sm\"], [1, \"bg-[#4f5fad]/20\", \"dark:bg-[#00f7ff]/20\", \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\", \"px-6\", \"py-3\", \"rounded-xl\", \"font-medium\", \"transition-all\", \"duration-300\", \"hover:scale-105\", \"hover:bg-[#4f5fad]/30\", \"dark:hover:bg-[#00f7ff]/30\", 3, \"click\"], [1, \"fas\", \"fa-arrow-left\", \"mr-2\"], [\"class\", \"flex flex-col items-center justify-center py-16\", 4, \"ngIf\"], [\"class\", \"mb-6\", 4, \"ngIf\"], [\"class\", \"row justify-content-center\", 4, \"ngIf\"], [1, \"flex\", \"flex-col\", \"items-center\", \"justify-center\", \"py-16\"], [1, \"relative\"], [1, \"w-12\", \"h-12\", \"border-3\", \"border-[#4f5fad]/20\", \"dark:border-[#00f7ff]/20\", \"border-t-[#4f5fad]\", \"dark:border-t-[#00f7ff]\", \"rounded-full\", \"animate-spin\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/20\", \"dark:bg-[#00f7ff]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"mt-4\", \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\", \"text-sm\", \"font-medium\", \"tracking-wide\"], [1, \"mb-6\"], [1, \"bg-[#ff6b69]/10\", \"dark:bg-[#ff3b30]/10\", \"border-l-4\", \"border-[#ff6b69]\", \"dark:border-[#ff3b30]\", \"rounded-lg\", \"p-4\", \"backdrop-blur-sm\"], [1, \"flex\", \"items-start\"], [1, \"text-[#ff6b69]\", \"dark:text-[#ff3b30]\", \"mr-3\", \"text-xl\"], [1, \"fas\", \"fa-exclamation-triangle\"], [1, \"flex-1\"], [1, \"font-semibold\", \"text-[#ff6b69]\", \"dark:text-[#ff3b30]\", \"mb-1\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\"], [1, \"row\", \"justify-content-center\"], [1, \"col-lg-8\"], [1, \"card\", \"border-0\", \"shadow-sm\", \"rounded-3\", \"overflow-hidden\"], [1, \"card-header\", \"bg-gradient-primary\", \"text-white\", \"border-0\", \"py-4\"], [1, \"mb-0\"], [1, \"bi\", 3, \"ngClass\"], [1, \"card-body\", \"p-4\"], [1, \"row\", \"g-3\", 3, \"ngSubmit\"], [1, \"col-12\", \"mb-3\"], [\"for\", \"name\", 1, \"form-label\", \"fw-medium\"], [1, \"text-danger\"], [1, \"input-group\"], [1, \"input-group-text\", \"bg-light\", \"border-0\"], [1, \"bi\", \"bi-people-fill\", \"text-primary\"], [\"type\", \"text\", \"id\", \"name\", \"required\", \"\", \"minlength\", \"3\", \"placeholder\", \"Entrez le nom de l'\\u00E9quipe\", 1, \"form-control\", \"bg-light\", \"border-0\", 3, \"value\", \"input\"], [\"nameInput\", \"\"], [\"class\", \"invalid-feedback d-block small mt-1\", 4, \"ngIf\"], [\"class\", \"text-danger small mt-1\", 4, \"ngIf\"], [\"for\", \"description\", 1, \"form-label\", \"fw-medium\"], [1, \"input-group-text\", \"bg-light\", \"border-0\", \"align-self-start\"], [1, \"bi\", \"bi-card-text\", \"text-primary\"], [\"id\", \"description\", \"rows\", \"4\", \"required\", \"\", \"minlength\", \"10\", \"placeholder\", \"D\\u00E9crivez l'objectif et les activit\\u00E9s de cette \\u00E9quipe\", 1, \"form-control\", \"bg-light\", \"border-0\", 3, \"value\", \"input\"], [\"descInput\", \"\"], [\"type\", \"hidden\", 3, \"value\"], [1, \"alert\", \"alert-info\", \"border-0\", \"rounded-3\", \"shadow-sm\", \"d-flex\", \"align-items-center\"], [1, \"bi\", \"bi-info-circle-fill\", \"fs-4\", \"me-3\", \"text-primary\"], [\"class\", \"col-12 mt-4\", 4, \"ngIf\"], [1, \"col-12\", \"mt-4\"], [1, \"d-flex\", \"gap-3\", \"justify-content-between\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-secondary\", \"rounded-pill\", \"px-4\", \"py-2\", 3, \"click\"], [1, \"bi\", \"bi-arrow-left\", \"me-2\"], [\"type\", \"button\", \"class\", \"btn btn-outline-danger rounded-pill px-4 py-2 ms-2\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"rounded-pill\", \"px-4\", \"py-2\", \"shadow-sm\", 3, \"disabled\"], [\"class\", \"spinner-border spinner-border-sm me-2\", 4, \"ngIf\"], [\"class\", \"bi\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"invalid-feedback\", \"d-block\", \"small\", \"mt-1\"], [1, \"bi\", \"bi-exclamation-triangle-fill\", \"me-1\"], [1, \"text-danger\", \"small\", \"mt-1\"], [1, \"bi\", \"bi-exclamation-circle-fill\", \"me-1\"], [1, \"card\", \"border-0\", \"shadow-sm\", \"rounded-3\", \"mb-4\"], [1, \"card-header\", \"bg-light\", \"border-0\", \"py-3\"], [1, \"mb-0\", \"d-flex\", \"align-items-center\"], [1, \"bi\", \"bi-people-fill\", \"text-primary\", \"me-2\"], [4, \"ngIf\", \"ngIfElse\"], [\"noMembers\", \"\"], [1, \"mt-4\"], [1, \"d-flex\", \"align-items-center\", \"mb-3\"], [1, \"bi\", \"bi-person-plus-fill\", \"text-primary\", \"me-2\"], [\"class\", \"alert alert-info border-0 rounded-3 shadow-sm d-flex align-items-center\", 4, \"ngIf\"], [\"class\", \"card border-0 bg-light rounded-3 mb-3\", 4, \"ngIf\"], [1, \"table-responsive\"], [1, \"table\", \"table-hover\", \"align-middle\"], [1, \"table-light\"], [1, \"d-flex\", \"align-items-center\"], [1, \"bi\", \"bi-person\", \"text-primary\", \"me-2\"], [1, \"bi\", \"bi-envelope\", \"text-primary\", \"me-2\"], [1, \"bi\", \"bi-briefcase\", \"text-primary\", \"me-2\"], [1, \"text-center\"], [\"class\", \"transition hover-row\", 4, \"ngFor\", \"ngForOf\"], [1, \"transition\", \"hover-row\"], [1, \"fw-medium\"], [\"class\", \"text-muted d-block\", 4, \"ngIf\"], [\"class\", \"text-decoration-none\", 3, \"href\", 4, \"ngIf\"], [\"class\", \"text-muted fst-italic\", 4, \"ngIf\"], [1, \"badge\", \"rounded-pill\", \"px-3\", \"py-2\", \"shadow-sm\", 3, \"ngClass\"], [\"type\", \"button\", \"title\", \"Retirer de l'\\u00E9quipe\", 1, \"btn\", \"btn-sm\", \"btn-outline-danger\", \"rounded-circle\", 3, \"click\"], [1, \"bi\", \"bi-trash\"], [1, \"text-muted\", \"d-block\"], [1, \"text-decoration-none\", 3, \"href\"], [1, \"text-muted\", \"fst-italic\"], [1, \"text-center\", \"py-4\"], [1, \"bi\", \"bi-people\", \"fs-1\", \"text-muted\", \"mb-3\", \"d-block\"], [1, \"text-muted\"], [1, \"card\", \"border-0\", \"bg-light\", \"rounded-3\", \"mb-3\"], [1, \"row\", \"g-3\"], [1, \"col-md-6\"], [\"for\", \"userSelect\", 1, \"form-label\", \"fw-medium\"], [\"id\", \"userSelect\", 1, \"form-select\", \"border-0\", \"shadow-sm\"], [\"userSelect\", \"\"], [\"value\", \"\", \"selected\", \"\", \"disabled\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-md-4\"], [\"for\", \"roleSelect\", 1, \"form-label\", \"fw-medium\"], [\"id\", \"roleSelect\", 1, \"form-select\", \"border-0\", \"shadow-sm\"], [\"roleSelect\", \"\"], [\"value\", \"membre\", \"selected\", \"\"], [\"value\", \"admin\"], [1, \"col-md-2\", \"d-flex\", \"align-items-end\"], [\"type\", \"button\", \"id\", \"addMembreButton\", 1, \"btn\", \"btn-primary\", \"rounded-pill\", \"w-100\", \"shadow-sm\", 3, \"disabled\", \"click\"], [1, \"bi\", \"bi-plus-circle\", \"me-1\"], [3, \"value\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-danger\", \"rounded-pill\", \"px-4\", \"py-2\", \"ms-2\", 3, \"click\"], [1, \"bi\", \"bi-trash\", \"me-2\"], [1, \"spinner-border\", \"spinner-border-sm\", \"me-2\"]],\n      template: function EquipeFormComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵelement(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5);\n          i0.ɵɵelement(6, \"div\", 6)(7, \"div\", 6)(8, \"div\", 6)(9, \"div\", 6)(10, \"div\", 6)(11, \"div\", 6)(12, \"div\", 6)(13, \"div\", 6)(14, \"div\", 6)(15, \"div\", 6)(16, \"div\", 6);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(17, \"div\", 7)(18, \"div\", 8);\n          i0.ɵɵelement(19, \"div\", 9)(20, \"div\", 10);\n          i0.ɵɵelementStart(21, \"div\", 11)(22, \"div\", 12)(23, \"div\", 13)(24, \"h1\", 14);\n          i0.ɵɵtext(25);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"p\", 15);\n          i0.ɵɵtext(27);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(28, \"button\", 16);\n          i0.ɵɵlistener(\"click\", function EquipeFormComponent_Template_button_click_28_listener() {\n            return ctx.cancel();\n          });\n          i0.ɵɵelement(29, \"i\", 17);\n          i0.ɵɵtext(30, \" Retour \\u00E0 la liste \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(31, EquipeFormComponent_div_31_Template, 6, 0, \"div\", 18);\n          i0.ɵɵtemplate(32, EquipeFormComponent_div_32_Template, 10, 1, \"div\", 19);\n          i0.ɵɵtemplate(33, EquipeFormComponent_div_33_Template, 52, 23, \"div\", 20);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(25);\n          i0.ɵɵtextInterpolate1(\" \", ctx.isEditMode ? \"Modifier l'\\u00E9quipe\" : \"Nouvelle \\u00E9quipe\", \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.isEditMode ? \"Modifiez les informations et les membres de votre \\u00E9quipe\" : \"Cr\\u00E9ez une nouvelle \\u00E9quipe pour organiser vos projets et membres\", \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n        }\n      },\n      dependencies: [i6.NgClass, i6.NgForOf, i6.NgIf, i7.ɵNgNoValidate, i7.NgSelectOption, i7.ɵNgSelectMultipleOption, i7.NgControlStatusGroup, i7.NgForm],\n      styles: [\".cursor-pointer[_ngcontent-%COMP%] {\\n      cursor: pointer;\\n    }\\n    summary[_ngcontent-%COMP%]:hover {\\n      text-decoration: underline;\\n    }\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImVxdWlwZS1mb3JtLmNvbXBvbmVudC5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkdBQUc7TUFDRyxlQUFlO0lBQ2pCO0lBQ0E7TUFDRSwwQkFBMEI7SUFDNUIiLCJmaWxlIjoiZXF1aXBlLWZvcm0uY29tcG9uZW50LmNzcyIsInNvdXJjZXNDb250ZW50IjpbIiAgIC5jdXJzb3ItcG9pbnRlciB7XHJcbiAgICAgIGN1cnNvcjogcG9pbnRlcjtcclxuICAgIH1cclxuICAgIHN1bW1hcnk6aG92ZXIge1xyXG4gICAgICB0ZXh0LWRlY29yYXRpb246IHVuZGVybGluZTtcclxuICAgIH0iXX0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvZnJvbnQvZXF1aXBlcy9lcXVpcGUtZm9ybS9lcXVpcGUtZm9ybS5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJHQUFHO01BQ0csZUFBZTtJQUNqQjtJQUNBO01BQ0UsMEJBQTBCO0lBQzVCO0FBQ0osd2NBQXdjIiwic291cmNlc0NvbnRlbnQiOlsiICAgLmN1cnNvci1wb2ludGVyIHtcclxuICAgICAgY3Vyc29yOiBwb2ludGVyO1xyXG4gICAgfVxyXG4gICAgc3VtbWFyeTpob3ZlciB7XHJcbiAgICAgIHRleHQtZGVjb3JhdGlvbjogdW5kZXJsaW5lO1xyXG4gICAgfSJdLCJzb3VyY2VSb290IjoiIn0= */\", \"\\n\\n  .bg-gradient-primary[_ngcontent-%COMP%] {\\n    background: linear-gradient(45deg, #007bff, #6610f2) !important;\\n  }\\n\\n  \\n\\n  .transition[_ngcontent-%COMP%] {\\n    transition: all 0.2s ease;\\n  }\\n\\n  .hover-row[_ngcontent-%COMP%]:hover {\\n    background-color: rgba(13, 110, 253, 0.05) !important;\\n    transform: translateY(-2px);\\n    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);\\n  }\\n\\n  \\n\\n  .form-control[_ngcontent-%COMP%]:focus, .form-select[_ngcontent-%COMP%]:focus {\\n    border-color: #86b7fe;\\n    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);\\n  }\\n\\n  \\n\\n  .btn[_ngcontent-%COMP%] {\\n    transition: all 0.3s ease;\\n  }\\n\\n  .btn[_ngcontent-%COMP%]:hover {\\n    transform: translateY(-2px);\\n  }\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r1", "error", "membreId_r20", "ɵɵpropertyInterpolate1", "ctx_r22", "getMembreEmail", "ɵɵsanitizeUrl", "ɵɵtemplate", "EquipeFormComponent_div_33_div_40_div_7_tr_20_small_4_Template", "EquipeFormComponent_div_33_div_40_div_7_tr_20_a_6_Template", "EquipeFormComponent_div_33_div_40_div_7_tr_20_span_7_Template", "ɵɵlistener", "EquipeFormComponent_div_33_div_40_div_7_tr_20_Template_button_click_13_listener", "restoredCtx", "ɵɵrestoreView", "_r27", "$implicit", "ctx_r26", "ɵɵnextContext", "ɵɵresetView", "removeMembreFromEquipe", "ɵɵtextInterpolate", "ctx_r19", "getMembreName", "ɵɵproperty", "ɵɵpureFunction3", "_c0", "getMembreProfession", "_c1", "EquipeFormComponent_div_33_div_40_div_7_tr_20_Template", "ctx_r14", "equipe", "members", "user_r31", "_id", "id", "ɵɵtextInterpolate4", "firstName", "lastName", "name", "email", "profession", "role", "EquipeFormComponent_div_33_div_40_div_15_option_10_Template", "EquipeFormComponent_div_33_div_40_div_15_Template_button_click_21_listener", "_r33", "_r28", "ɵɵreference", "_r30", "ctx_r32", "addMembreToEquipe", "value", "ctx_r18", "availableUsers", "EquipeFormComponent_div_33_div_40_div_7_Template", "EquipeFormComponent_div_33_div_40_ng_template_8_Template", "ɵɵtemplateRefExtractor", "EquipeFormComponent_div_33_div_40_div_14_Template", "EquipeFormComponent_div_33_div_40_div_15_Template", "ctx_r10", "length", "_r15", "EquipeFormComponent_div_33_button_47_Template_button_click_0_listener", "_r35", "ctx_r34", "deleteEquipe", "ɵɵpureFunction2", "_c2", "ctx_r13", "isEditMode", "EquipeFormComponent_div_33_Template_form_ngSubmit_8_listener", "_r37", "ctx_r36", "onSubmit", "EquipeFormComponent_div_33_Template_input_input_17_listener", "_r3", "ctx_r38", "updateName", "EquipeFormComponent_div_33_div_19_Template", "EquipeFormComponent_div_33_div_20_Template", "EquipeFormComponent_div_33_div_21_Template", "EquipeFormComponent_div_33_Template_textarea_input_30_listener", "_r7", "ctx_r39", "updateDescription", "EquipeFormComponent_div_33_div_32_Template", "EquipeFormComponent_div_33_div_33_Template", "EquipeFormComponent_div_33_div_40_Template", "EquipeFormComponent_div_33_Template_button_click_44_listener", "ctx_r40", "cancel", "EquipeFormComponent_div_33_button_47_Template", "EquipeFormComponent_div_33_span_49_Template", "EquipeFormComponent_div_33_i_50_Template", "_c3", "ctx_r2", "ɵɵclassProp", "nameExists", "nameError", "descriptionError", "description", "admin", "equipeId", "submitting", "EquipeFormComponent", "constructor", "equipeService", "membreService", "userService", "route", "router", "notificationService", "loading", "checkingName", "existingEquipes", "availableMembers", "ngOnInit", "console", "log", "loadAllEquipes", "loadAllMembers", "loadAllUsers", "snapshot", "paramMap", "get", "loadEquipe", "setTimeout", "addButton", "document", "getElementById", "addEventListener", "getMembres", "subscribe", "next", "membres", "token", "localStorage", "getItem", "getAllUsers", "users", "getEquipes", "equipes", "getEquipe", "data", "loadMembersDetails", "for<PERSON>ach", "membreId", "user", "find", "u", "getUserById", "userData", "index", "findIndex", "some", "push", "checkNameExists", "e", "warn", "equipeToSave", "updateEquipe", "response", "showSuccess", "navigate", "message", "showError", "addEquipe", "includes", "userName", "membre", "m", "getMembreRole", "_membreId", "membreName", "confirm", "status", "ɵɵdirectiveInject", "i1", "EquipeService", "i2", "MembreService", "i3", "AuthService", "i4", "ActivatedRoute", "Router", "i5", "NotificationService", "selectors", "decls", "vars", "consts", "template", "EquipeFormComponent_Template", "rf", "ctx", "EquipeFormComponent_Template_button_click_28_listener", "EquipeFormComponent_div_31_Template", "EquipeFormComponent_div_32_Template", "EquipeFormComponent_div_33_Template"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\equipes\\equipe-form\\equipe-form.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\equipes\\equipe-form\\equipe-form.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { EquipeService } from 'src/app/services/equipe.service';\nimport { MembreService } from 'src/app/services/membre.service';\nimport { AuthService } from 'src/app/services/auth.service';\nimport { NotificationService } from 'src/app/services/notification.service';\nimport { Equipe } from 'src/app/models/equipe.model';\nimport { Membre } from 'src/app/models/membre.model';\nimport { User } from 'src/app/models/user.model';\n\n@Component({\n  selector: 'app-equipe-form',\n  templateUrl: './equipe-form.component.html',\n  styleUrls: ['./equipe-form.component.css'],\n})\nexport class EquipeFormComponent implements OnInit {\n  equipe: Equipe = {\n    name: '',\n    description: '',\n    admin: '65f1e5b3a1d8f3c8c0f9e8d7', // ID temporaire\n  };\n  isEditMode = false;\n  loading = false;\n  submitting = false;\n  error: string | null = null;\n  equipeId: string | null = null;\n  nameExists = false;\n  nameError = false;\n  descriptionError = false;\n  checkingName = false;\n  existingEquipes: Equipe[] = [];\n  availableMembers: Membre[] = []; // Liste des membres disponibles\n  availableUsers: User[] = []; // Liste des utilisateurs disponibles\n\n  constructor(\n    private equipeService: EquipeService,\n    private membreService: MembreService,\n    private userService: AuthService,\n    private route: ActivatedRoute,\n    private router: Router,\n    private notificationService: NotificationService\n  ) {}\n\n  ngOnInit(): void {\n    console.log('EquipeFormComponent initialized');\n\n    // Ensure equipe is always defined with admin\n    if (!this.equipe) {\n      this.equipe = {\n        name: '',\n        description: '',\n        admin: '65f1e5b3a1d8f3c8c0f9e8d7', // ID temporaire\n      };\n    }\n\n    // Charger toutes les équipes pour vérifier les noms existants\n    this.loadAllEquipes();\n\n    // Charger tous les membres disponibles\n    this.loadAllMembers();\n\n    // Charger tous les utilisateurs disponibles\n    this.loadAllUsers();\n\n    try {\n      // Vérifier si nous sommes en mode édition (si un ID est présent dans l'URL)\n      this.equipeId = this.route.snapshot.paramMap.get('id');\n      this.isEditMode = !!this.equipeId;\n      console.log('Edit mode:', this.isEditMode, 'ID:', this.equipeId);\n\n      if (this.isEditMode && this.equipeId) {\n        this.loadEquipe(this.equipeId);\n\n        // Ajouter un délai pour s'assurer que l'équipe est chargée\n        setTimeout(() => {\n          console.log('Après délai - this.equipeId:', this.equipeId);\n          console.log('Après délai - this.equipe:', this.equipe);\n        }, 1000);\n      }\n    } catch (error) {\n      console.error('Error in ngOnInit:', error);\n      this.error = \"Erreur d'initialisation\";\n    }\n\n    // Ajouter un gestionnaire d'événements pour le bouton d'ajout de membre\n    setTimeout(() => {\n      const addButton = document.getElementById('addMembreButton');\n      if (addButton) {\n        console.log(\"Bouton d'ajout de membre trouvé\");\n        addButton.addEventListener('click', () => {\n          console.log(\"Bouton d'ajout de membre cliqué\");\n        });\n      } else {\n        console.log(\"Bouton d'ajout de membre non trouvé\");\n      }\n    }, 2000);\n  }\n\n  loadAllMembers(): void {\n    this.membreService.getMembres().subscribe({\n      next: (membres) => {\n        this.availableMembers = membres;\n        console.log('Membres disponibles chargés:', membres);\n      },\n      error: (error) => {\n        console.error('Erreur lors du chargement des membres:', error);\n        this.error =\n          'Impossible de charger la liste des membres. Veuillez réessayer plus tard.';\n      },\n    });\n  }\n\n  loadAllUsers(): void {\n    const token = localStorage.getItem('token');\n    if (token) {\n      this.userService.getAllUsers(token).subscribe({\n        next: (users: any) => {\n          this.availableUsers = users;\n          console.log('Utilisateurs disponibles chargés:', users);\n        },\n        error: (error) => {\n          console.error('Erreur lors du chargement des utilisateurs:', error);\n          this.error =\n            'Impossible de charger la liste des utilisateurs. Veuillez réessayer plus tard.';\n        },\n      });\n    }\n  }\n\n  loadAllEquipes(): void {\n    this.equipeService.getEquipes().subscribe({\n      next: (equipes) => {\n        this.existingEquipes = equipes;\n        console.log('Équipes existantes chargées:', equipes);\n      },\n      error: (error) => {\n        console.error('Erreur lors du chargement des équipes:', error);\n      },\n    });\n  }\n\n  loadEquipe(id: string): void {\n    console.log('Loading equipe with ID:', id);\n    this.loading = true;\n    this.error = null;\n\n    this.equipeService.getEquipe(id).subscribe({\n      next: (data) => {\n        console.log('Équipe chargée:', data);\n        this.equipe = data;\n\n        // Vérifier que l'ID est correctement défini\n        console.log(\"ID de l'équipe après chargement:\", this.equipe._id);\n        console.log('this.equipeId:', this.equipeId);\n\n        // Si l'équipe a des membres, récupérer les informations de chaque membre\n        if (this.equipe.members && this.equipe.members.length > 0) {\n          this.loadMembersDetails();\n        }\n\n        this.loading = false;\n      },\n      error: (error) => {\n        console.error(\"Erreur lors du chargement de l'équipe:\", error);\n        this.error =\n          \"Impossible de charger les détails de l'équipe. Veuillez réessayer plus tard.\";\n        this.loading = false;\n      },\n    });\n  }\n\n  // Méthode pour récupérer les détails des membres de l'équipe\n  loadMembersDetails(): void {\n    if (!this.equipe.members || this.equipe.members.length === 0) {\n      return;\n    }\n\n    console.log(\"Chargement des détails des membres de l'équipe...\");\n\n    // Pour chaque membre de l'équipe, essayer de trouver ses informations dans la liste des utilisateurs\n    this.equipe.members.forEach((membreId) => {\n      // Chercher d'abord dans la liste des utilisateurs\n      const user = this.availableUsers.find(\n        (u) => u._id === membreId || u.id === membreId\n      );\n      if (user) {\n        console.log(\n          `Membre ${membreId} trouvé dans la liste des utilisateurs:`,\n          user\n        );\n\n        // Vérifier si toutes les informations nécessaires sont présentes\n        if (!user.email || (!user.profession && !user.role)) {\n          // Si des informations manquent, essayer de les récupérer depuis l'API\n          const token = localStorage.getItem('token');\n          if (token) {\n            this.userService.getUserById(membreId, token).subscribe({\n              next: (userData: any) => {\n                console.log(\n                  `Détails supplémentaires de l'utilisateur ${membreId} récupérés:`,\n                  userData\n                );\n\n                // Mettre à jour l'utilisateur dans la liste avec les nouvelles informations\n                const index = this.availableUsers.findIndex(\n                  (u) => u._id === membreId || u.id === membreId\n                );\n                if (index !== -1) {\n                  this.availableUsers[index] = {\n                    ...this.availableUsers[index],\n                    ...userData,\n                  };\n                }\n              },\n              error: (error) => {\n                console.error(\n                  `Erreur lors de la récupération des détails supplémentaires de l'utilisateur ${membreId}:`,\n                  error\n                );\n              },\n            });\n          }\n        }\n      } else {\n        // Si non trouvé, essayer de récupérer l'utilisateur depuis l'API\n        const token = localStorage.getItem('token');\n        if (token) {\n          this.userService.getUserById(membreId, token).subscribe({\n            next: (userData: any) => {\n              console.log(\n                `Détails de l'utilisateur ${membreId} récupérés:`,\n                userData\n              );\n              // Ajouter l'utilisateur à la liste des utilisateurs disponibles s'il n'y est pas déjà\n              if (\n                !this.availableUsers.some(\n                  (u) => u._id === userData._id || u.id === userData.id\n                )\n              ) {\n                this.availableUsers.push(userData);\n              }\n            },\n            error: (error) => {\n              console.error(\n                `Erreur lors de la récupération des détails de l'utilisateur ${membreId}:`,\n                error\n              );\n            },\n          });\n        }\n      }\n    });\n  }\n\n  checkNameExists(name: string): boolean {\n    // En mode édition, ignorer l'équipe actuelle\n    if (this.isEditMode && this.equipeId) {\n      return this.existingEquipes.some(\n        (e) => e.name === name && e._id !== this.equipeId\n      );\n    }\n    // En mode création, vérifier tous les noms\n    return this.existingEquipes.some((e) => e.name === name);\n  }\n\n  updateName(value: string): void {\n    console.log('Name updated:', value);\n    this.equipe.name = value;\n\n    // Vérifier si le nom existe déjà\n    this.nameExists = this.checkNameExists(value);\n    if (this.nameExists) {\n      console.warn(\"Ce nom d'équipe existe déjà\");\n    }\n\n    // Vérifier si le nom a au moins 3 caractères\n    this.nameError = value.length > 0 && value.length < 3;\n    if (this.nameError) {\n      console.warn('Le nom doit contenir au moins 3 caractères');\n    }\n  }\n\n  updateDescription(value: string): void {\n    console.log('Description updated:', value);\n    this.equipe.description = value;\n\n    // Vérifier si la description a au moins 10 caractères\n    this.descriptionError = value.length > 0 && value.length < 10;\n    if (this.descriptionError) {\n      console.warn('La description doit contenir au moins 10 caractères');\n    }\n  }\n\n  onSubmit(): void {\n    console.log('Form submitted with:', this.equipe);\n\n    // Vérifier si le nom est présent et valide\n    if (!this.equipe.name) {\n      this.error = \"Le nom de l'équipe est requis.\";\n      return;\n    }\n\n    if (this.equipe.name.length < 3) {\n      this.nameError = true;\n      this.error = \"Le nom de l'équipe doit contenir au moins 3 caractères.\";\n      return;\n    }\n\n    // Vérifier si la description est présente et valide\n    if (!this.equipe.description) {\n      this.error = \"La description de l'équipe est requise.\";\n      return;\n    }\n\n    if (this.equipe.description.length < 10) {\n      this.descriptionError = true;\n      this.error =\n        \"La description de l'équipe doit contenir au moins 10 caractères.\";\n      return;\n    }\n\n    // Vérifier si le nom existe déjà avant de soumettre\n    if (this.checkNameExists(this.equipe.name)) {\n      this.nameExists = true;\n      this.error =\n        'Une équipe avec ce nom existe déjà. Veuillez choisir un autre nom.';\n      return;\n    }\n\n    this.submitting = true;\n    this.error = null;\n\n    // Créer une copie de l'objet équipe pour éviter les problèmes de référence\n    const equipeToSave: Equipe = {\n      name: this.equipe.name,\n      description: this.equipe.description || '',\n      admin: this.equipe.admin,\n    };\n\n    // Ajouter l'ID si nous sommes en mode édition\n    if (this.isEditMode && this.equipeId) {\n      equipeToSave._id = this.equipeId;\n    }\n\n    console.log('Données à envoyer:', equipeToSave);\n\n    if (this.isEditMode && this.equipeId) {\n      // Mode édition\n      this.equipeService.updateEquipe(this.equipeId, equipeToSave).subscribe({\n        next: (response) => {\n          console.log('Équipe mise à jour avec succès:', response);\n          this.submitting = false;\n          this.notificationService.showSuccess(\n            `L'équipe \"${response.name}\" a été mise à jour avec succès.`\n          );\n          this.router.navigate(['/equipes/liste']);\n        },\n        error: (error) => {\n          console.error(\"Erreur lors de la mise à jour de l'équipe:\", error);\n          this.error = `Impossible de mettre à jour l'équipe: ${error.message}`;\n          this.submitting = false;\n          this.notificationService.showError(`Erreur: ${error.message}`);\n        },\n      });\n    } else {\n      // Mode ajout\n      this.equipeService.addEquipe(equipeToSave).subscribe({\n        next: (response) => {\n          console.log('Équipe ajoutée avec succès:', response);\n          this.submitting = false;\n          this.notificationService.showSuccess(\n            `L'équipe \"${response.name}\" a été créée avec succès.`\n          );\n          this.router.navigate(['/equipes/liste']);\n        },\n        error: (error) => {\n          console.error(\"Erreur lors de l'ajout de l'équipe:\", error);\n          this.error = `Impossible d'ajouter l'équipe: ${error.message}`;\n          this.submitting = false;\n          this.notificationService.showError(`Erreur: ${error.message}`);\n        },\n      });\n    }\n  }\n\n  cancel(): void {\n    console.log('Form cancelled');\n    this.router.navigate(['/equipes/liste']);\n  }\n\n  // Méthodes pour gérer les membres\n  addMembreToEquipe(membreId: string, role: string = 'membre'): void {\n    console.log(\n      'Début de addMembreToEquipe avec membreId:',\n      membreId,\n      'et rôle:',\n      role\n    );\n    console.log('État actuel - this.equipeId:', this.equipeId);\n    console.log('État actuel - this.equipe:', this.equipe);\n\n    // Utiliser this.equipe._id si this.equipeId n'est pas défini\n    const equipeId = this.equipeId || (this.equipe && this.equipe._id);\n\n    console.log('equipeId calculé:', equipeId);\n\n    if (!equipeId || !membreId) {\n      console.error(\"ID d'équipe ou ID de membre manquant\");\n      this.error = \"ID d'équipe ou ID de membre manquant\";\n      console.log('equipeId:', equipeId, 'membreId:', membreId);\n\n      // Afficher un message à l'utilisateur\n      this.notificationService.showError(\n        \"Impossible d'ajouter le membre: ID d'équipe ou ID de membre manquant\"\n      );\n      return;\n    }\n\n    // Vérifier si le membre est déjà dans l'équipe\n    if (this.equipe.members && this.equipe.members.includes(membreId)) {\n      this.notificationService.showError(\n        \"Ce membre fait déjà partie de l'équipe\"\n      );\n      return;\n    }\n\n    // Récupérer les informations de l'utilisateur pour afficher un message plus informatif\n    const user = this.availableUsers.find(\n      (u) => u._id === membreId || u.id === membreId\n    );\n    const userName = user\n      ? user.firstName && user.lastName\n        ? `${user.firstName} ${user.lastName}`\n        : user.name || membreId\n      : membreId;\n\n    // Créer l'objet membre avec le rôle spécifié\n    const membre: Membre = {\n      id: membreId,\n      role: role,\n    };\n\n    this.loading = true;\n\n    console.log(\n      `Ajout de l'utilisateur \"${userName}\" comme ${role} à l'équipe ${equipeId}`\n    );\n\n    this.equipeService.addMembreToEquipe(equipeId, membre).subscribe({\n      next: (response) => {\n        console.log('Membre ajouté avec succès:', response);\n        this.notificationService.showSuccess(\n          `${userName} a été ajouté comme ${\n            role === 'admin' ? 'administrateur' : 'membre'\n          } à l'équipe`\n        );\n        // Recharger l'équipe pour mettre à jour la liste des membres\n        this.loadEquipe(equipeId);\n        this.loading = false;\n      },\n      error: (error) => {\n        console.error(\"Erreur lors de l'ajout du membre:\", error);\n        this.error =\n          \"Impossible d'ajouter le membre. Veuillez réessayer plus tard.\";\n        this.notificationService.showError(\n          \"Erreur lors de l'ajout du membre: \" + error.message\n        );\n        this.loading = false;\n      },\n    });\n  }\n\n  // Méthode pour obtenir le nom complet d'un membre à partir de son ID\n  getMembreName(membreId: string): string {\n    // Chercher d'abord dans la liste des utilisateurs\n    const user = this.availableUsers.find(\n      (u) => u._id === membreId || u.id === membreId\n    );\n    if (user) {\n      if (user.firstName && user.lastName) {\n        return `${user.firstName} ${user.lastName}`;\n      } else if (user.name) {\n        return user.name;\n      }\n    }\n\n    // Chercher ensuite dans la liste des membres\n    const membre = this.availableMembers.find(\n      (m) => m._id === membreId || m.id === membreId\n    );\n    if (membre && membre.name) {\n      return membre.name;\n    }\n\n    // Si aucun nom n'est trouvé, retourner l'ID\n    return membreId;\n  }\n\n  // Méthode pour obtenir l'email d'un membre\n  getMembreEmail(membreId: string): string {\n    const user = this.availableUsers.find(\n      (u) => u._id === membreId || u.id === membreId\n    );\n    if (user && user.email) {\n      return user.email;\n    }\n    return 'Non renseigné';\n  }\n\n  // Méthode pour obtenir la profession d'un membre\n  getMembreProfession(membreId: string): string {\n    const user = this.availableUsers.find(\n      (u) => u._id === membreId || u.id === membreId\n    );\n    if (user) {\n      if (user.profession) {\n        return user.profession === 'etudiant' ? 'Étudiant' : 'Professeur';\n      } else if (user.role) {\n        return user.role === 'etudiant' ? 'Étudiant' : 'Professeur';\n      }\n    }\n    return 'Non spécifié';\n  }\n\n  // Méthode pour obtenir le rôle d'un membre dans l'équipe\n  getMembreRole(_membreId: string): string {\n    // Cette méthode nécessiterait d'avoir accès aux rôles des membres dans l'équipe\n    // Pour l'instant, nous retournons une valeur par défaut\n    return 'Membre';\n  }\n\n  removeMembreFromEquipe(membreId: string): void {\n    console.log('Méthode removeMembreFromEquipe appelée avec ID:', membreId);\n    console.log('État actuel - this.equipeId:', this.equipeId);\n    console.log('État actuel - this.equipe:', this.equipe);\n\n    // Utiliser this.equipe._id si this.equipeId n'est pas défini\n    const equipeId = this.equipeId || (this.equipe && this.equipe._id);\n\n    if (!equipeId) {\n      console.error(\"ID d'équipe manquant\");\n      this.error = \"ID d'équipe manquant. Impossible de retirer le membre.\";\n      this.notificationService.showError(\n        \"ID d'équipe manquant. Impossible de retirer le membre.\"\n      );\n      return;\n    }\n\n    if (!membreId) {\n      console.error('ID de membre manquant');\n      this.error = 'ID de membre manquant. Impossible de retirer le membre.';\n      this.notificationService.showError(\n        'ID de membre manquant. Impossible de retirer le membre.'\n      );\n      return;\n    }\n\n    // Obtenir le nom du membre pour l'afficher dans le message de confirmation\n    const membreName = this.getMembreName(membreId);\n\n    console.log(\n      `Tentative de retrait de l'utilisateur ${membreId} (${membreName}) de l'équipe ${equipeId}`\n    );\n\n    try {\n      if (\n        confirm(`Êtes-vous sûr de vouloir retirer ${membreName} de l'équipe?`)\n      ) {\n        console.log('Confirmation acceptée, suppression en cours...');\n\n        this.loading = true;\n        this.error = null;\n\n        // Ajouter un délai pour s'assurer que l'utilisateur voit le chargement\n        setTimeout(() => {\n          this.equipeService\n            .removeMembreFromEquipe(equipeId, membreId)\n            .subscribe({\n              next: (response) => {\n                console.log(\n                  `Utilisateur \"${membreName}\" retiré avec succès de l'équipe:`,\n                  response\n                );\n                this.loading = false;\n                this.notificationService.showSuccess(\n                  `${membreName} a été retiré avec succès de l'équipe`\n                );\n\n                // Recharger l'équipe pour mettre à jour la liste des membres\n                this.loadEquipe(equipeId);\n              },\n              error: (error) => {\n                console.error(\n                  `Erreur lors du retrait de l'utilisateur \"${membreName}\":`,\n                  error\n                );\n                console.error(\"Détails de l'erreur:\", {\n                  status: error.status,\n                  message: error.message,\n                  error: error,\n                });\n\n                this.loading = false;\n                this.error = `Impossible de retirer l'utilisateur \"${membreName}\" de l'équipe: ${\n                  error.message || 'Erreur inconnue'\n                }`;\n                this.notificationService.showError(\n                  `Erreur lors du retrait du membre: ${this.error}`\n                );\n              },\n            });\n        }, 500);\n      } else {\n        console.log(\"Suppression annulée par l'utilisateur\");\n      }\n    } catch (error: any) {\n      console.error('Exception lors du retrait du membre:', error);\n      this.error = `Exception: ${error?.message || 'Erreur inconnue'}`;\n      this.notificationService.showError(`Exception: ${this.error}`);\n    }\n  }\n\n  // Méthode pour supprimer l'équipe\n  deleteEquipe(): void {\n    console.log('Méthode deleteEquipe appelée dans equipe-form.component.ts');\n    console.log('État actuel - this.equipeId:', this.equipeId);\n    console.log('État actuel - this.equipe:', this.equipe);\n\n    // Utiliser this.equipe._id si this.equipeId n'est pas défini\n    const equipeId = this.equipeId || (this.equipe && this.equipe._id);\n\n    if (!equipeId) {\n      console.error(\"ID d'équipe manquant\");\n      this.error = \"ID d'équipe manquant. Impossible de supprimer l'équipe.\";\n      this.notificationService.showError(\n        \"ID d'équipe manquant. Impossible de supprimer l'équipe.\"\n      );\n      return;\n    }\n\n    console.log(\"ID de l'équipe à supprimer (final):\", equipeId);\n\n    try {\n      if (\n        confirm(\n          `Êtes-vous sûr de vouloir supprimer l'équipe \"${this.equipe.name}\"? Cette action est irréversible.`\n        )\n      ) {\n        console.log('Confirmation acceptée, suppression en cours...');\n\n        this.loading = true;\n        this.error = null;\n\n        // Ajouter un délai pour s'assurer que l'utilisateur voit le chargement\n        setTimeout(() => {\n          this.equipeService.deleteEquipe(equipeId).subscribe({\n            next: (response) => {\n              console.log('Équipe supprimée avec succès, réponse:', response);\n              this.loading = false;\n              this.notificationService.showSuccess(\n                `L'équipe \"${this.equipe.name}\" a été supprimée avec succès.`\n              );\n\n              // Ajouter un délai avant la redirection\n              setTimeout(() => {\n                this.router.navigate(['/equipes/liste']);\n              }, 500);\n            },\n            error: (error) => {\n              console.error(\n                \"Erreur lors de la suppression de l'équipe:\",\n                error\n              );\n              console.error(\"Détails de l'erreur:\", {\n                status: error.status,\n                message: error.message,\n                error: error,\n              });\n\n              this.loading = false;\n              this.error = `Impossible de supprimer l'équipe: ${\n                error.message || 'Erreur inconnue'\n              }`;\n              this.notificationService.showError(\n                `Erreur lors de la suppression: ${this.error}`\n              );\n            },\n          });\n        }, 500);\n      } else {\n        console.log(\"Suppression annulée par l'utilisateur\");\n      }\n    } catch (error: any) {\n      console.error('Exception lors de la suppression:', error);\n      this.error = `Exception: ${error?.message || 'Erreur inconnue'}`;\n      this.notificationService.showError(`Exception: ${this.error}`);\n    }\n  }\n}\n", "<div\n  class=\"min-h-screen bg-[#f0f4f8] dark:bg-[#0a0a0a] relative overflow-hidden\"\n>\n  <!-- Background decorative elements -->\n  <div class=\"absolute inset-0 overflow-hidden pointer-events-none\">\n    <div\n      class=\"absolute top-[15%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-br from-[#4f5fad]/5 to-transparent dark:from-[#00f7ff]/3 dark:to-transparent blur-3xl\"\n    ></div>\n    <div\n      class=\"absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#4f5fad]/5 to-transparent dark:from-[#00f7ff]/3 dark:to-transparent blur-3xl\"\n    ></div>\n\n    <!-- Grid pattern -->\n    <div class=\"absolute inset-0 opacity-5 dark:opacity-[0.03]\">\n      <div class=\"h-full grid grid-cols-12\">\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n      </div>\n    </div>\n  </div>\n\n  <div class=\"max-w-4xl mx-auto p-6 relative z-10\">\n    <!-- Header -->\n    <div class=\"mb-8 relative\">\n      <!-- Decorative top border -->\n      <div\n        class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad]\"\n      ></div>\n      <div\n        class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad] blur-md\"\n      ></div>\n\n      <div\n        class=\"bg-white dark:bg-[#1a1a1a] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] p-6 backdrop-blur-sm border border-[#4f5fad]/20 dark:border-[#00f7ff]/20\"\n      >\n        <div\n          class=\"flex flex-col lg:flex-row lg:items-center lg:justify-between\"\n        >\n          <div class=\"mb-4 lg:mb-0\">\n            <h1\n              class=\"text-3xl font-bold text-[#4f5fad] dark:text-[#00f7ff] mb-2 tracking-wide\"\n            >\n              {{ isEditMode ? \"Modifier l'équipe\" : \"Nouvelle équipe\" }}\n            </h1>\n            <p class=\"text-[#6d6870] dark:text-[#a0a0a0] text-sm\">\n              {{\n                isEditMode\n                  ? \"Modifiez les informations et les membres de votre équipe\"\n                  : \"Créez une nouvelle équipe pour organiser vos projets et membres\"\n              }}\n            </p>\n          </div>\n\n          <button\n            (click)=\"cancel()\"\n            class=\"bg-[#4f5fad]/20 dark:bg-[#00f7ff]/20 text-[#4f5fad] dark:text-[#00f7ff] px-6 py-3 rounded-xl font-medium transition-all duration-300 hover:scale-105 hover:bg-[#4f5fad]/30 dark:hover:bg-[#00f7ff]/30\"\n          >\n            <i class=\"fas fa-arrow-left mr-2\"></i>\n            Retour à la liste\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Loading Indicator -->\n    <div\n      *ngIf=\"loading\"\n      class=\"flex flex-col items-center justify-center py-16\"\n    >\n      <div class=\"relative\">\n        <div\n          class=\"w-12 h-12 border-3 border-[#4f5fad]/20 dark:border-[#00f7ff]/20 border-t-[#4f5fad] dark:border-t-[#00f7ff] rounded-full animate-spin\"\n        ></div>\n        <div\n          class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#00f7ff]/20 blur-xl rounded-full transform scale-150 -z-10\"\n        ></div>\n      </div>\n      <p\n        class=\"mt-4 text-[#4f5fad] dark:text-[#00f7ff] text-sm font-medium tracking-wide\"\n      >\n        Chargement des données...\n      </p>\n    </div>\n\n    <!-- Error Alert -->\n    <div *ngIf=\"error\" class=\"mb-6\">\n      <div\n        class=\"bg-[#ff6b69]/10 dark:bg-[#ff3b30]/10 border-l-4 border-[#ff6b69] dark:border-[#ff3b30] rounded-lg p-4 backdrop-blur-sm\"\n      >\n        <div class=\"flex items-start\">\n          <div class=\"text-[#ff6b69] dark:text-[#ff3b30] mr-3 text-xl\">\n            <i class=\"fas fa-exclamation-triangle\"></i>\n          </div>\n          <div class=\"flex-1\">\n            <h3 class=\"font-semibold text-[#ff6b69] dark:text-[#ff3b30] mb-1\">\n              Erreur\n            </h3>\n            <p class=\"text-sm text-[#6d6870] dark:text-[#e0e0e0]\">\n              {{ error }}\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Formulaire avec design moderne -->\n    <div class=\"row justify-content-center\" *ngIf=\"!loading\">\n      <div class=\"col-lg-8\">\n        <div class=\"card border-0 shadow-sm rounded-3 overflow-hidden\">\n          <div class=\"card-header bg-gradient-primary text-white border-0 py-4\">\n            <h3 class=\"mb-0\">\n              <i\n                class=\"bi\"\n                [ngClass]=\"{\n                  'bi-pencil-square': isEditMode,\n                  'bi-plus-circle': !isEditMode\n                }\"\n              ></i>\n              {{\n                isEditMode\n                  ? \"Informations de l'équipe\"\n                  : \"Détails de la nouvelle équipe\"\n              }}\n            </h3>\n          </div>\n\n          <div class=\"card-body p-4\">\n            <form (ngSubmit)=\"onSubmit()\" class=\"row g-3\">\n              <!-- Nom de l'équipe -->\n              <div class=\"col-12 mb-3\">\n                <label for=\"name\" class=\"form-label fw-medium\"\n                  >Nom de l'équipe <span class=\"text-danger\">*</span></label\n                >\n                <div class=\"input-group\">\n                  <span class=\"input-group-text bg-light border-0\">\n                    <i class=\"bi bi-people-fill text-primary\"></i>\n                  </span>\n                  <input\n                    #nameInput\n                    type=\"text\"\n                    class=\"form-control bg-light border-0\"\n                    [class.is-invalid]=\"\n                      nameExists || (nameError && nameInput.value.length > 0)\n                    \"\n                    id=\"name\"\n                    [value]=\"equipe.name || ''\"\n                    (input)=\"updateName(nameInput.value)\"\n                    required\n                    minlength=\"3\"\n                    placeholder=\"Entrez le nom de l'équipe\"\n                  />\n                </div>\n                <div\n                  *ngIf=\"nameExists\"\n                  class=\"invalid-feedback d-block small mt-1\"\n                >\n                  <i class=\"bi bi-exclamation-triangle-fill me-1\"></i>\n                  Ce nom d'équipe existe déjà. Veuillez en choisir un autre.\n                </div>\n                <div\n                  *ngIf=\"nameError && nameInput.value.length > 0\"\n                  class=\"invalid-feedback d-block small mt-1\"\n                >\n                  <i class=\"bi bi-exclamation-triangle-fill me-1\"></i>\n                  Le nom de l'équipe doit contenir au moins 3 caractères.\n                </div>\n                <div\n                  *ngIf=\"error && !equipe.name\"\n                  class=\"text-danger small mt-1\"\n                >\n                  <i class=\"bi bi-exclamation-circle-fill me-1\"></i>\n                  Le nom de l'équipe est requis.\n                </div>\n              </div>\n\n              <!-- Description de l'équipe -->\n              <div class=\"col-12 mb-3\">\n                <label for=\"description\" class=\"form-label fw-medium\"\n                  >Description <span class=\"text-danger\">*</span></label\n                >\n                <div class=\"input-group\">\n                  <span\n                    class=\"input-group-text bg-light border-0 align-self-start\"\n                  >\n                    <i class=\"bi bi-card-text text-primary\"></i>\n                  </span>\n                  <textarea\n                    #descInput\n                    class=\"form-control bg-light border-0\"\n                    id=\"description\"\n                    rows=\"4\"\n                    [class.is-invalid]=\"\n                      descriptionError && descInput.value.length > 0\n                    \"\n                    [value]=\"equipe.description || ''\"\n                    (input)=\"updateDescription(descInput.value)\"\n                    required\n                    minlength=\"10\"\n                    placeholder=\"Décrivez l'objectif et les activités de cette équipe\"\n                  ></textarea>\n                </div>\n                <div\n                  *ngIf=\"descriptionError && descInput.value.length > 0\"\n                  class=\"invalid-feedback d-block small mt-1\"\n                >\n                  <i class=\"bi bi-exclamation-triangle-fill me-1\"></i>\n                  La description doit contenir au moins 10 caractères.\n                </div>\n                <div\n                  *ngIf=\"error && !equipe.description\"\n                  class=\"text-danger small mt-1\"\n                >\n                  <i class=\"bi bi-exclamation-circle-fill me-1\"></i>\n                  La description de l'équipe est requise.\n                </div>\n              </div>\n\n              <!-- Admin field - hidden for now, using default value -->\n              <input type=\"hidden\" [value]=\"equipe.admin\" />\n              <div class=\"col-12 mb-3\">\n                <div\n                  class=\"alert alert-info border-0 rounded-3 shadow-sm d-flex align-items-center\"\n                >\n                  <i class=\"bi bi-info-circle-fill fs-4 me-3 text-primary\"></i>\n                  <div>\n                    Un administrateur par défaut sera assigné à cette équipe.\n                  </div>\n                </div>\n              </div>\n\n              <!-- Utilisateurs membres de l'équipe (visible uniquement en mode édition) -->\n              <div *ngIf=\"isEditMode && equipe._id\" class=\"col-12 mt-4\">\n                <div class=\"card border-0 shadow-sm rounded-3 mb-4\">\n                  <div class=\"card-header bg-light border-0 py-3\">\n                    <h4 class=\"mb-0 d-flex align-items-center\">\n                      <i class=\"bi bi-people-fill text-primary me-2\"></i>\n                      Membres de l'équipe\n                    </h4>\n                  </div>\n\n                  <div class=\"card-body p-4\">\n                    <!-- Liste des membres actuels -->\n                    <div\n                      *ngIf=\"\n                        equipe.members && equipe.members.length > 0;\n                        else noMembers\n                      \"\n                    >\n                      <div class=\"table-responsive\">\n                        <table class=\"table table-hover align-middle\">\n                          <thead class=\"table-light\">\n                            <tr>\n                              <th>\n                                <div class=\"d-flex align-items-center\">\n                                  <i class=\"bi bi-person text-primary me-2\"></i>\n                                  Nom et Prénom\n                                </div>\n                              </th>\n                              <th>\n                                <div class=\"d-flex align-items-center\">\n                                  <i\n                                    class=\"bi bi-envelope text-primary me-2\"\n                                  ></i>\n                                  Email\n                                </div>\n                              </th>\n                              <th>\n                                <div class=\"d-flex align-items-center\">\n                                  <i\n                                    class=\"bi bi-briefcase text-primary me-2\"\n                                  ></i>\n                                  Statut\n                                </div>\n                              </th>\n                              <th class=\"text-center\">Actions</th>\n                            </tr>\n                          </thead>\n                          <tbody>\n                            <tr\n                              *ngFor=\"let membreId of equipe.members\"\n                              class=\"transition hover-row\"\n                            >\n                              <td>\n                                <span class=\"fw-medium\">{{\n                                  getMembreName(membreId)\n                                }}</span>\n                                <small\n                                  class=\"text-muted d-block\"\n                                  *ngIf=\"getMembreName(membreId) !== membreId\"\n                                  >ID: {{ membreId }}</small\n                                >\n                              </td>\n                              <td>\n                                <a\n                                  *ngIf=\"\n                                    getMembreEmail(membreId) !== 'Non renseigné'\n                                  \"\n                                  href=\"mailto:{{ getMembreEmail(membreId) }}\"\n                                  class=\"text-decoration-none\"\n                                >\n                                  {{ getMembreEmail(membreId) }}\n                                </a>\n                                <span\n                                  *ngIf=\"\n                                    getMembreEmail(membreId) === 'Non renseigné'\n                                  \"\n                                  class=\"text-muted fst-italic\"\n                                  >Non renseigné</span\n                                >\n                              </td>\n                              <td>\n                                <span\n                                  class=\"badge rounded-pill px-3 py-2 shadow-sm\"\n                                  [ngClass]=\"{\n                                    'bg-primary':\n                                      getMembreProfession(membreId) ===\n                                      'Étudiant',\n                                    'bg-success':\n                                      getMembreProfession(membreId) ===\n                                      'Professeur',\n                                    'bg-secondary':\n                                      getMembreProfession(membreId) ===\n                                      'Non spécifié'\n                                  }\"\n                                >\n                                  <i\n                                    class=\"bi\"\n                                    [ngClass]=\"{\n                                      'bi-mortarboard-fill':\n                                        getMembreProfession(membreId) ===\n                                        'Étudiant',\n                                      'bi-briefcase-fill':\n                                        getMembreProfession(membreId) ===\n                                        'Professeur',\n                                      'bi-question-circle-fill':\n                                        getMembreProfession(membreId) ===\n                                        'Non spécifié'\n                                    }\"\n                                  ></i>\n                                  {{ getMembreProfession(membreId) }}\n                                </span>\n                              </td>\n                              <td class=\"text-center\">\n                                <button\n                                  type=\"button\"\n                                  class=\"btn btn-sm btn-outline-danger rounded-circle\"\n                                  title=\"Retirer de l'équipe\"\n                                  (click)=\"removeMembreFromEquipe(membreId)\"\n                                >\n                                  <i class=\"bi bi-trash\"></i>\n                                </button>\n                              </td>\n                            </tr>\n                          </tbody>\n                        </table>\n                      </div>\n                    </div>\n\n                    <ng-template #noMembers>\n                      <div class=\"text-center py-4\">\n                        <i\n                          class=\"bi bi-people fs-1 text-muted mb-3 d-block\"\n                        ></i>\n                        <h5 class=\"text-muted\">\n                          Aucun membre dans cette équipe\n                        </h5>\n                        <p class=\"text-muted\">\n                          Ajoutez des membres à l'équipe en utilisant le\n                          formulaire ci-dessous.\n                        </p>\n                      </div>\n                    </ng-template>\n\n                    <!-- Formulaire pour ajouter un utilisateur comme membre -->\n                    <div class=\"mt-4\">\n                      <h5 class=\"d-flex align-items-center mb-3\">\n                        <i class=\"bi bi-person-plus-fill text-primary me-2\"></i>\n                        Ajouter un membre\n                      </h5>\n\n                      <!-- Afficher un message si aucun utilisateur n'est disponible -->\n                      <div\n                        *ngIf=\"availableUsers.length === 0\"\n                        class=\"alert alert-info border-0 rounded-3 shadow-sm d-flex align-items-center\"\n                      >\n                        <i\n                          class=\"bi bi-info-circle-fill fs-4 me-3 text-primary\"\n                        ></i>\n                        <div>\n                          Aucun utilisateur disponible. Veuillez d'abord créer\n                          des utilisateurs.\n                        </div>\n                      </div>\n\n                      <!-- Formulaire d'ajout d'utilisateur avec rôle -->\n                      <div\n                        *ngIf=\"availableUsers.length > 0\"\n                        class=\"card border-0 bg-light rounded-3 mb-3\"\n                      >\n                        <div class=\"card-body p-4\">\n                          <div class=\"row g-3\">\n                            <!-- Sélection de l'utilisateur -->\n                            <div class=\"col-md-6\">\n                              <label\n                                for=\"userSelect\"\n                                class=\"form-label fw-medium\"\n                                >Utilisateur</label\n                              >\n                              <select\n                                #userSelect\n                                id=\"userSelect\"\n                                class=\"form-select border-0 shadow-sm\"\n                              >\n                                <option value=\"\" selected disabled>\n                                  Sélectionnez un utilisateur\n                                </option>\n                                <option\n                                  *ngFor=\"let user of availableUsers\"\n                                  [value]=\"user._id || user.id\"\n                                >\n                                  {{ user.firstName || \"\" }}\n                                  {{ user.lastName || user.name || user.id }}\n                                  {{ user.email ? \"- \" + user.email : \"\" }}\n                                  {{\n                                    user.profession\n                                      ? \"(\" +\n                                        (user.profession === \"etudiant\"\n                                          ? \"Étudiant\"\n                                          : \"Professeur\") +\n                                        \")\"\n                                      : user.role\n                                      ? \"(\" +\n                                        (user.role === \"etudiant\"\n                                          ? \"Étudiant\"\n                                          : \"Professeur\") +\n                                        \")\"\n                                      : \"\"\n                                  }}\n                                </option>\n                              </select>\n                            </div>\n\n                            <!-- Sélection du rôle dans l'équipe -->\n                            <div class=\"col-md-4\">\n                              <label\n                                for=\"roleSelect\"\n                                class=\"form-label fw-medium\"\n                                >Rôle dans l'équipe</label\n                              >\n                              <select\n                                #roleSelect\n                                id=\"roleSelect\"\n                                class=\"form-select border-0 shadow-sm\"\n                              >\n                                <option value=\"membre\" selected>Membre</option>\n                                <option value=\"admin\">Administrateur</option>\n                              </select>\n                            </div>\n\n                            <!-- Bouton d'ajout -->\n                            <div class=\"col-md-2 d-flex align-items-end\">\n                              <button\n                                type=\"button\"\n                                class=\"btn btn-primary rounded-pill w-100 shadow-sm\"\n                                [disabled]=\"!userSelect.value\"\n                                (click)=\"\n                                  addMembreToEquipe(\n                                    userSelect.value,\n                                    roleSelect.value\n                                  );\n                                  userSelect.value = ''\n                                \"\n                                id=\"addMembreButton\"\n                              >\n                                <i class=\"bi bi-plus-circle me-1\"></i> Ajouter\n                              </button>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <!-- Boutons d'action -->\n              <div class=\"col-12 mt-4\">\n                <div class=\"d-flex gap-3 justify-content-between\">\n                  <div>\n                    <button\n                      type=\"button\"\n                      class=\"btn btn-outline-secondary rounded-pill px-4 py-2\"\n                      (click)=\"cancel()\"\n                    >\n                      <i class=\"bi bi-arrow-left me-2\"></i>\n                      Retour\n                    </button>\n\n                    <!-- Bouton de suppression (visible uniquement en mode édition) -->\n                    <button\n                      *ngIf=\"isEditMode && equipeId\"\n                      type=\"button\"\n                      class=\"btn btn-outline-danger rounded-pill px-4 py-2 ms-2\"\n                      (click)=\"deleteEquipe()\"\n                    >\n                      <i class=\"bi bi-trash me-2\"></i>\n                      Supprimer\n                    </button>\n                  </div>\n\n                  <button\n                    type=\"submit\"\n                    class=\"btn btn-primary rounded-pill px-4 py-2 shadow-sm\"\n                    [disabled]=\"\n                      submitting ||\n                      !equipe.name ||\n                      !equipe.description ||\n                      nameExists ||\n                      nameError ||\n                      descriptionError\n                    \"\n                  >\n                    <span\n                      *ngIf=\"submitting\"\n                      class=\"spinner-border spinner-border-sm me-2\"\n                    ></span>\n                    <i\n                      *ngIf=\"!submitting\"\n                      class=\"bi\"\n                      [ngClass]=\"{\n                        'bi-save': isEditMode,\n                        'bi-plus-circle': !isEditMode\n                      }\"\n                    ></i>\n                    {{ isEditMode ? \"Mettre à jour\" : \"Créer l'équipe\" }}\n                  </button>\n                </div>\n              </div>\n            </form>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n\n<!-- Styles spécifiques pour cette page -->\n<style>\n  /* Fond dégradé pour l'en-tête du formulaire */\n  .bg-gradient-primary {\n    background: linear-gradient(45deg, #007bff, #6610f2) !important;\n  }\n\n  /* Animation au survol des lignes du tableau */\n  .transition {\n    transition: all 0.2s ease;\n  }\n\n  .hover-row:hover {\n    background-color: rgba(13, 110, 253, 0.05) !important;\n    transform: translateY(-2px);\n    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);\n  }\n\n  /* Style pour les inputs */\n  .form-control:focus,\n  .form-select:focus {\n    border-color: #86b7fe;\n    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);\n  }\n\n  /* Animation pour les boutons */\n  .btn {\n    transition: all 0.3s ease;\n  }\n\n  .btn:hover {\n    transform: translateY(-2px);\n  }\n</style>\n"], "mappings": ";;;;;;;;;;IC0EIA,EAAA,CAAAC,cAAA,cAGC;IAEGD,EAAA,CAAAE,SAAA,cAEO;IAITF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,YAEC;IACCD,EAAA,CAAAI,MAAA,uCACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;;IAINH,EAAA,CAAAC,cAAA,cAAgC;IAMxBD,EAAA,CAAAE,SAAA,YAA2C;IAC7CF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAoB;IAEhBD,EAAA,CAAAI,MAAA,eACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,YAAsD;IACpDD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,KAAA,MACF;;;;;IAqDIR,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAE,SAAA,YAAoD;IACpDF,EAAA,CAAAI,MAAA,kFACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;;IACNH,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAE,SAAA,YAAoD;IACpDF,EAAA,CAAAI,MAAA,0EACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;;IACNH,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAE,SAAA,YAAkD;IAClDF,EAAA,CAAAI,MAAA,4CACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;;IA6BNH,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAE,SAAA,YAAoD;IACpDF,EAAA,CAAAI,MAAA,kEACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;;IACNH,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAE,SAAA,YAAkD;IAClDF,EAAA,CAAAI,MAAA,qDACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;;IAwEUH,EAAA,CAAAC,cAAA,iBAGG;IAAAD,EAAA,CAAAI,MAAA,GAAkB;IAAAJ,EAAA,CAAAG,YAAA,EACpB;;;;IADEH,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAM,kBAAA,SAAAG,YAAA,KAAkB;;;;;IAIrBT,EAAA,CAAAC,cAAA,aAMC;IACCD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;;IAJFH,EAAA,CAAAU,sBAAA,oBAAAC,OAAA,CAAAC,cAAA,CAAAH,YAAA,OAAAT,EAAA,CAAAa,aAAA,CAA4C;IAG5Cb,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAK,OAAA,CAAAC,cAAA,CAAAH,YAAA,OACF;;;;;IACAT,EAAA,CAAAC,cAAA,gBAKG;IAAAD,EAAA,CAAAI,MAAA,yBAAa;IAAAJ,EAAA,CAAAG,YAAA,EACf;;;;;;;;;;;;;;;;;;;;IA9BLH,EAAA,CAAAC,cAAA,aAGC;IAE2BD,EAAA,CAAAI,MAAA,GAEtB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACTH,EAAA,CAAAc,UAAA,IAAAC,8DAAA,oBAIC;IACHf,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAc,UAAA,IAAAE,0DAAA,gBAQI;IACJhB,EAAA,CAAAc,UAAA,IAAAG,6DAAA,mBAMC;IACHjB,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IAeAD,EAAA,CAAAE,SAAA,aAaK;IACLF,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAETH,EAAA,CAAAC,cAAA,cAAwB;IAKpBD,EAAA,CAAAkB,UAAA,mBAAAC,gFAAA;MAAA,MAAAC,WAAA,GAAApB,EAAA,CAAAqB,aAAA,CAAAC,IAAA;MAAA,MAAAb,YAAA,GAAAW,WAAA,CAAAG,SAAA;MAAA,MAAAC,OAAA,GAAAxB,EAAA,CAAAyB,aAAA;MAAA,OAASzB,EAAA,CAAA0B,WAAA,CAAAF,OAAA,CAAAG,sBAAA,CAAAlB,YAAA,CAAgC;IAAA,EAAC;IAE1CT,EAAA,CAAAE,SAAA,cAA2B;IAC7BF,EAAA,CAAAG,YAAA,EAAS;;;;;IAnEeH,EAAA,CAAAK,SAAA,GAEtB;IAFsBL,EAAA,CAAA4B,iBAAA,CAAAC,OAAA,CAAAC,aAAA,CAAArB,YAAA,EAEtB;IAGCT,EAAA,CAAAK,SAAA,GAA0C;IAA1CL,EAAA,CAAA+B,UAAA,SAAAF,OAAA,CAAAC,aAAA,CAAArB,YAAA,MAAAA,YAAA,CAA0C;IAM1CT,EAAA,CAAAK,SAAA,GAGnC;IAHmCL,EAAA,CAAA+B,UAAA,SAAAF,OAAA,CAAAjB,cAAA,CAAAH,YAAA,2BAGnC;IAMmCT,EAAA,CAAAK,SAAA,GAGnC;IAHmCL,EAAA,CAAA+B,UAAA,SAAAF,OAAA,CAAAjB,cAAA,CAAAH,YAAA,2BAGnC;IAOkCT,EAAA,CAAAK,SAAA,GAUE;IAVFL,EAAA,CAAA+B,UAAA,YAAA/B,EAAA,CAAAgC,eAAA,IAAAC,GAAA,EAAAJ,OAAA,CAAAK,mBAAA,CAAAzB,YAAA,uBAAAoB,OAAA,CAAAK,mBAAA,CAAAzB,YAAA,oBAAAoB,OAAA,CAAAK,mBAAA,CAAAzB,YAAA,gCAUE;IAIAT,EAAA,CAAAK,SAAA,GAUE;IAVFL,EAAA,CAAA+B,UAAA,YAAA/B,EAAA,CAAAgC,eAAA,KAAAG,GAAA,EAAAN,OAAA,CAAAK,mBAAA,CAAAzB,YAAA,uBAAAoB,OAAA,CAAAK,mBAAA,CAAAzB,YAAA,oBAAAoB,OAAA,CAAAK,mBAAA,CAAAzB,YAAA,gCAUE;IAEJT,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAuB,OAAA,CAAAK,mBAAA,CAAAzB,YAAA,OACF;;;;;IAlGZT,EAAA,CAAAC,cAAA,UAKC;IAOaD,EAAA,CAAAE,SAAA,YAA8C;IAC9CF,EAAA,CAAAI,MAAA,2BACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,SAAI;IAEAD,EAAA,CAAAE,SAAA,aAEK;IACLF,EAAA,CAAAI,MAAA,eACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,UAAI;IAEAD,EAAA,CAAAE,SAAA,aAEK;IACLF,EAAA,CAAAI,MAAA,gBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,cAAwB;IAAAD,EAAA,CAAAI,MAAA,eAAO;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAGxCH,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAAc,UAAA,KAAAsB,sDAAA,mBA0EK;IACPpC,EAAA,CAAAG,YAAA,EAAQ;;;;IA1EiBH,EAAA,CAAAK,SAAA,IAAiB;IAAjBL,EAAA,CAAA+B,UAAA,YAAAM,OAAA,CAAAC,MAAA,CAAAC,OAAA,CAAiB;;;;;IAgF9CvC,EAAA,CAAAC,cAAA,eAA8B;IAC5BD,EAAA,CAAAE,SAAA,aAEK;IACLF,EAAA,CAAAC,cAAA,cAAuB;IACrBD,EAAA,CAAAI,MAAA,4CACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,aAAsB;IACpBD,EAAA,CAAAI,MAAA,wFAEF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;;IAYNH,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAE,SAAA,YAEK;IACLF,EAAA,CAAAC,cAAA,UAAK;IACHD,EAAA,CAAAI,MAAA,oFAEF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;;IAyBEH,EAAA,CAAAC,cAAA,kBAGC;IACCD,EAAA,CAAAI,MAAA,GAkBF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;IApBPH,EAAA,CAAA+B,UAAA,UAAAS,QAAA,CAAAC,GAAA,IAAAD,QAAA,CAAAE,EAAA,CAA6B;IAE7B1C,EAAA,CAAAK,SAAA,GAkBF;IAlBEL,EAAA,CAAA2C,kBAAA,MAAAH,QAAA,CAAAI,SAAA,aAAAJ,QAAA,CAAAK,QAAA,IAAAL,QAAA,CAAAM,IAAA,IAAAN,QAAA,CAAAE,EAAA,OAAAF,QAAA,CAAAO,KAAA,UAAAP,QAAA,CAAAO,KAAA,YAAAP,QAAA,CAAAQ,UAAA,UAAAR,QAAA,CAAAQ,UAAA,0DAAAR,QAAA,CAAAS,IAAA,UAAAT,QAAA,CAAAS,IAAA,kEAkBF;;;;;;IA3CVjD,EAAA,CAAAC,cAAA,eAGC;IAQUD,EAAA,CAAAI,MAAA,kBAAW;IAAAJ,EAAA,CAAAG,YAAA,EACb;IACDH,EAAA,CAAAC,cAAA,uBAIC;IAEGD,EAAA,CAAAI,MAAA,yCACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAc,UAAA,KAAAoC,2DAAA,sBAsBS;IACXlD,EAAA,CAAAG,YAAA,EAAS;IAIXH,EAAA,CAAAC,cAAA,gBAAsB;IAIjBD,EAAA,CAAAI,MAAA,oCAAkB;IAAAJ,EAAA,CAAAG,YAAA,EACpB;IACDH,EAAA,CAAAC,cAAA,wBAIC;IACiCD,EAAA,CAAAI,MAAA,cAAM;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IAC/CH,EAAA,CAAAC,cAAA,mBAAsB;IAAAD,EAAA,CAAAI,MAAA,sBAAc;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IAKjDH,EAAA,CAAAC,cAAA,gBAA6C;IAKzCD,EAAA,CAAAkB,UAAA,mBAAAiC,2EAAA;MAAAnD,EAAA,CAAAqB,aAAA,CAAA+B,IAAA;MAAA,MAAAC,IAAA,GAAArD,EAAA,CAAAsD,WAAA;MAAA,MAAAC,IAAA,GAAAvD,EAAA,CAAAsD,WAAA;MAAA,MAAAE,OAAA,GAAAxD,EAAA,CAAAyB,aAAA;MAEhB+B,OAAA,CAAAC,iBAAA,CAAAJ,IAAA,CAAAK,KAAA,EAAAH,IAAA,CAAAG,KAAA,CAGiB;MAAA,OACpB1D,EAAA,CAAA0B,WAAA,CAAA2B,IAAA,CAAAK,KAAA,GAAmB,EAChC;IAAA,EADiC;IAGD1D,EAAA,CAAAE,SAAA,cAAsC;IAACF,EAAA,CAAAI,MAAA,iBACzC;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;;IA1DYH,EAAA,CAAAK,SAAA,IAAiB;IAAjBL,EAAA,CAAA+B,UAAA,YAAA4B,OAAA,CAAAC,cAAA,CAAiB;IA+CpC5D,EAAA,CAAAK,SAAA,IAA8B;IAA9BL,EAAA,CAAA+B,UAAA,cAAAsB,IAAA,CAAAK,KAAA,CAA8B;;;;;IAzOhD1D,EAAA,CAAAC,cAAA,cAA0D;IAIlDD,EAAA,CAAAE,SAAA,YAAmD;IACnDF,EAAA,CAAAI,MAAA,iCACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAGPH,EAAA,CAAAC,cAAA,cAA2B;IAEzBD,EAAA,CAAAc,UAAA,IAAA+C,gDAAA,mBAkHM;IAEN7D,EAAA,CAAAc,UAAA,IAAAgD,wDAAA,iCAAA9D,EAAA,CAAA+D,sBAAA,CAac;IAGd/D,EAAA,CAAAC,cAAA,eAAkB;IAEdD,EAAA,CAAAE,SAAA,aAAwD;IACxDF,EAAA,CAAAI,MAAA,2BACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAGLH,EAAA,CAAAc,UAAA,KAAAkD,iDAAA,kBAWM;IAGNhE,EAAA,CAAAc,UAAA,KAAAmD,iDAAA,mBAoFM;IACRjE,EAAA,CAAAG,YAAA,EAAM;;;;;IA7OHH,EAAA,CAAAK,SAAA,GAGb;IAHaL,EAAA,CAAA+B,UAAA,SAAAmC,OAAA,CAAA5B,MAAA,CAAAC,OAAA,IAAA2B,OAAA,CAAA5B,MAAA,CAAAC,OAAA,CAAA4B,MAAA,KAGb,aAAAC,IAAA;IAwIepE,EAAA,CAAAK,SAAA,GAAiC;IAAjCL,EAAA,CAAA+B,UAAA,SAAAmC,OAAA,CAAAN,cAAA,CAAAO,MAAA,OAAiC;IAcjCnE,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAA+B,UAAA,SAAAmC,OAAA,CAAAN,cAAA,CAAAO,MAAA,KAA+B;;;;;;IAuGpCnE,EAAA,CAAAC,cAAA,kBAKC;IADCD,EAAA,CAAAkB,UAAA,mBAAAmD,sEAAA;MAAArE,EAAA,CAAAqB,aAAA,CAAAiD,IAAA;MAAA,MAAAC,OAAA,GAAAvE,EAAA,CAAAyB,aAAA;MAAA,OAASzB,EAAA,CAAA0B,WAAA,CAAA6C,OAAA,CAAAC,YAAA,EAAc;IAAA,EAAC;IAExBxE,EAAA,CAAAE,SAAA,aAAgC;IAChCF,EAAA,CAAAI,MAAA,kBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;;IAeTH,EAAA,CAAAE,SAAA,gBAGQ;;;;;;;;;;;IACRF,EAAA,CAAAE,SAAA,YAOK;;;;IAJHF,EAAA,CAAA+B,UAAA,YAAA/B,EAAA,CAAAyE,eAAA,IAAAC,GAAA,EAAAC,OAAA,CAAAC,UAAA,GAAAD,OAAA,CAAAC,UAAA,EAGE;;;;;;;;;;;;IA1apB5E,EAAA,CAAAC,cAAA,cAAyD;IAK/CD,EAAA,CAAAE,SAAA,YAMK;IACLF,EAAA,CAAAI,MAAA,GAKF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAGPH,EAAA,CAAAC,cAAA,cAA2B;IACnBD,EAAA,CAAAkB,UAAA,sBAAA2D,6DAAA;MAAA7E,EAAA,CAAAqB,aAAA,CAAAyD,IAAA;MAAA,MAAAC,OAAA,GAAA/E,EAAA,CAAAyB,aAAA;MAAA,OAAYzB,EAAA,CAAA0B,WAAA,CAAAqD,OAAA,CAAAC,QAAA,EAAU;IAAA,EAAC;IAE3BhF,EAAA,CAAAC,cAAA,cAAyB;IAEpBD,EAAA,CAAAI,MAAA,6BAAgB;IAAAJ,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAI,MAAA,SAAC;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAErDH,EAAA,CAAAC,cAAA,eAAyB;IAErBD,EAAA,CAAAE,SAAA,aAA8C;IAChDF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,qBAaE;IAJAD,EAAA,CAAAkB,UAAA,mBAAA+D,4DAAA;MAAAjF,EAAA,CAAAqB,aAAA,CAAAyD,IAAA;MAAA,MAAAI,GAAA,GAAAlF,EAAA,CAAAsD,WAAA;MAAA,MAAA6B,OAAA,GAAAnF,EAAA,CAAAyB,aAAA;MAAA,OAASzB,EAAA,CAAA0B,WAAA,CAAAyD,OAAA,CAAAC,UAAA,CAAAF,GAAA,CAAAxB,KAAA,CAA2B;IAAA,EAAC;IATvC1D,EAAA,CAAAG,YAAA,EAaE;IAEJH,EAAA,CAAAc,UAAA,KAAAuE,0CAAA,kBAMM;IACNrF,EAAA,CAAAc,UAAA,KAAAwE,0CAAA,kBAMM;IACNtF,EAAA,CAAAc,UAAA,KAAAyE,0CAAA,kBAMM;IACRvF,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAAyB;IAEpBD,EAAA,CAAAI,MAAA,oBAAY;IAAAJ,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAI,MAAA,SAAC;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAEjDH,EAAA,CAAAC,cAAA,eAAyB;IAIrBD,EAAA,CAAAE,SAAA,aAA4C;IAC9CF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,wBAaC;IAJCD,EAAA,CAAAkB,UAAA,mBAAAsE,+DAAA;MAAAxF,EAAA,CAAAqB,aAAA,CAAAyD,IAAA;MAAA,MAAAW,GAAA,GAAAzF,EAAA,CAAAsD,WAAA;MAAA,MAAAoC,OAAA,GAAA1F,EAAA,CAAAyB,aAAA;MAAA,OAASzB,EAAA,CAAA0B,WAAA,CAAAgE,OAAA,CAAAC,iBAAA,CAAAF,GAAA,CAAA/B,KAAA,CAAkC;IAAA,EAAC;IAI7C1D,EAAA,CAAAG,YAAA,EAAW;IAEdH,EAAA,CAAAc,UAAA,KAAA8E,0CAAA,kBAMM;IACN5F,EAAA,CAAAc,UAAA,KAAA+E,0CAAA,kBAMM;IACR7F,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAE,SAAA,iBAA8C;IAC9CF,EAAA,CAAAC,cAAA,eAAyB;IAIrBD,EAAA,CAAAE,SAAA,aAA6D;IAC7DF,EAAA,CAAAC,cAAA,WAAK;IACHD,EAAA,CAAAI,MAAA,uFACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAKVH,EAAA,CAAAc,UAAA,KAAAgF,0CAAA,mBA4PM;IAGN9F,EAAA,CAAAC,cAAA,eAAyB;IAMjBD,EAAA,CAAAkB,UAAA,mBAAA6E,6DAAA;MAAA/F,EAAA,CAAAqB,aAAA,CAAAyD,IAAA;MAAA,MAAAkB,OAAA,GAAAhG,EAAA,CAAAyB,aAAA;MAAA,OAASzB,EAAA,CAAA0B,WAAA,CAAAsE,OAAA,CAAAC,MAAA,EAAQ;IAAA,EAAC;IAElBjG,EAAA,CAAAE,SAAA,aAAqC;IACrCF,EAAA,CAAAI,MAAA,gBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IAGTH,EAAA,CAAAc,UAAA,KAAAoF,6CAAA,qBAQS;IACXlG,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,kBAWC;IACCD,EAAA,CAAAc,UAAA,KAAAqF,2CAAA,mBAGQ;IACRnG,EAAA,CAAAc,UAAA,KAAAsF,wCAAA,gBAOK;IACLpG,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;;;IAtaXH,EAAA,CAAAK,SAAA,GAGE;IAHFL,EAAA,CAAA+B,UAAA,YAAA/B,EAAA,CAAAyE,eAAA,KAAA4B,GAAA,EAAAC,MAAA,CAAA1B,UAAA,GAAA0B,MAAA,CAAA1B,UAAA,EAGE;IAEJ5E,EAAA,CAAAK,SAAA,GAKF;IALEL,EAAA,CAAAM,kBAAA,MAAAgG,MAAA,CAAA1B,UAAA,oFAKF;IAkBQ5E,EAAA,CAAAK,SAAA,IAEC;IAFDL,EAAA,CAAAuG,WAAA,eAAAD,MAAA,CAAAE,UAAA,IAAAF,MAAA,CAAAG,SAAA,IAAAvB,GAAA,CAAAxB,KAAA,CAAAS,MAAA,KAEC;IAEDnE,EAAA,CAAA+B,UAAA,UAAAuE,MAAA,CAAAhE,MAAA,CAAAQ,IAAA,OAA2B;IAQ5B9C,EAAA,CAAAK,SAAA,GAAgB;IAAhBL,EAAA,CAAA+B,UAAA,SAAAuE,MAAA,CAAAE,UAAA,CAAgB;IAOhBxG,EAAA,CAAAK,SAAA,GAA6C;IAA7CL,EAAA,CAAA+B,UAAA,SAAAuE,MAAA,CAAAG,SAAA,IAAAvB,GAAA,CAAAxB,KAAA,CAAAS,MAAA,KAA6C;IAO7CnE,EAAA,CAAAK,SAAA,GAA2B;IAA3BL,EAAA,CAAA+B,UAAA,SAAAuE,MAAA,CAAA9F,KAAA,KAAA8F,MAAA,CAAAhE,MAAA,CAAAQ,IAAA,CAA2B;IAwB1B9C,EAAA,CAAAK,SAAA,GAEC;IAFDL,EAAA,CAAAuG,WAAA,eAAAD,MAAA,CAAAI,gBAAA,IAAAjB,GAAA,CAAA/B,KAAA,CAAAS,MAAA,KAEC;IACDnE,EAAA,CAAA+B,UAAA,UAAAuE,MAAA,CAAAhE,MAAA,CAAAqE,WAAA,OAAkC;IAQnC3G,EAAA,CAAAK,SAAA,GAAoD;IAApDL,EAAA,CAAA+B,UAAA,SAAAuE,MAAA,CAAAI,gBAAA,IAAAjB,GAAA,CAAA/B,KAAA,CAAAS,MAAA,KAAoD;IAOpDnE,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAA+B,UAAA,SAAAuE,MAAA,CAAA9F,KAAA,KAAA8F,MAAA,CAAAhE,MAAA,CAAAqE,WAAA,CAAkC;IASlB3G,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAA+B,UAAA,UAAAuE,MAAA,CAAAhE,MAAA,CAAAsE,KAAA,CAAsB;IAarC5G,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAA+B,UAAA,SAAAuE,MAAA,CAAA1B,UAAA,IAAA0B,MAAA,CAAAhE,MAAA,CAAAG,GAAA,CAA8B;IA6Q3BzC,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAA+B,UAAA,SAAAuE,MAAA,CAAA1B,UAAA,IAAA0B,MAAA,CAAAO,QAAA,CAA4B;IAa/B7G,EAAA,CAAAK,SAAA,GAOC;IAPDL,EAAA,CAAA+B,UAAA,aAAAuE,MAAA,CAAAQ,UAAA,KAAAR,MAAA,CAAAhE,MAAA,CAAAQ,IAAA,KAAAwD,MAAA,CAAAhE,MAAA,CAAAqE,WAAA,IAAAL,MAAA,CAAAE,UAAA,IAAAF,MAAA,CAAAG,SAAA,IAAAH,MAAA,CAAAI,gBAAA,CAOC;IAGE1G,EAAA,CAAAK,SAAA,GAAgB;IAAhBL,EAAA,CAAA+B,UAAA,SAAAuE,MAAA,CAAAQ,UAAA,CAAgB;IAIhB9G,EAAA,CAAAK,SAAA,GAAiB;IAAjBL,EAAA,CAAA+B,UAAA,UAAAuE,MAAA,CAAAQ,UAAA,CAAiB;IAOpB9G,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAgG,MAAA,CAAA1B,UAAA,0DACF;;;ADjhBlB,OAAM,MAAOmC,mBAAmB;EAmB9BC,YACUC,aAA4B,EAC5BC,aAA4B,EAC5BC,WAAwB,EACxBC,KAAqB,EACrBC,MAAc,EACdC,mBAAwC;IALxC,KAAAL,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,mBAAmB,GAAnBA,mBAAmB;IAxB7B,KAAAhF,MAAM,GAAW;MACfQ,IAAI,EAAE,EAAE;MACR6D,WAAW,EAAE,EAAE;MACfC,KAAK,EAAE,0BAA0B,CAAE;KACpC;;IACD,KAAAhC,UAAU,GAAG,KAAK;IAClB,KAAA2C,OAAO,GAAG,KAAK;IACf,KAAAT,UAAU,GAAG,KAAK;IAClB,KAAAtG,KAAK,GAAkB,IAAI;IAC3B,KAAAqG,QAAQ,GAAkB,IAAI;IAC9B,KAAAL,UAAU,GAAG,KAAK;IAClB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAc,YAAY,GAAG,KAAK;IACpB,KAAAC,eAAe,GAAa,EAAE;IAC9B,KAAAC,gBAAgB,GAAa,EAAE,CAAC,CAAC;IACjC,KAAA9D,cAAc,GAAW,EAAE,CAAC,CAAC;EAS1B;;EAEH+D,QAAQA,CAAA;IACNC,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAE9C;IACA,IAAI,CAAC,IAAI,CAACvF,MAAM,EAAE;MAChB,IAAI,CAACA,MAAM,GAAG;QACZQ,IAAI,EAAE,EAAE;QACR6D,WAAW,EAAE,EAAE;QACfC,KAAK,EAAE,0BAA0B,CAAE;OACpC;;IAGH;IACA,IAAI,CAACkB,cAAc,EAAE;IAErB;IACA,IAAI,CAACC,cAAc,EAAE;IAErB;IACA,IAAI,CAACC,YAAY,EAAE;IAEnB,IAAI;MACF;MACA,IAAI,CAACnB,QAAQ,GAAG,IAAI,CAACO,KAAK,CAACa,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC;MACtD,IAAI,CAACvD,UAAU,GAAG,CAAC,CAAC,IAAI,CAACiC,QAAQ;MACjCe,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE,IAAI,CAACjD,UAAU,EAAE,KAAK,EAAE,IAAI,CAACiC,QAAQ,CAAC;MAEhE,IAAI,IAAI,CAACjC,UAAU,IAAI,IAAI,CAACiC,QAAQ,EAAE;QACpC,IAAI,CAACuB,UAAU,CAAC,IAAI,CAACvB,QAAQ,CAAC;QAE9B;QACAwB,UAAU,CAAC,MAAK;UACdT,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAAChB,QAAQ,CAAC;UAC1De,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAACvF,MAAM,CAAC;QACxD,CAAC,EAAE,IAAI,CAAC;;KAEX,CAAC,OAAO9B,KAAK,EAAE;MACdoH,OAAO,CAACpH,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,IAAI,CAACA,KAAK,GAAG,yBAAyB;;IAGxC;IACA6H,UAAU,CAAC,MAAK;MACd,MAAMC,SAAS,GAAGC,QAAQ,CAACC,cAAc,CAAC,iBAAiB,CAAC;MAC5D,IAAIF,SAAS,EAAE;QACbV,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAC9CS,SAAS,CAACG,gBAAgB,CAAC,OAAO,EAAE,MAAK;UACvCb,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAChD,CAAC,CAAC;OACH,MAAM;QACLD,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;;IAEtD,CAAC,EAAE,IAAI,CAAC;EACV;EAEAE,cAAcA,CAAA;IACZ,IAAI,CAACb,aAAa,CAACwB,UAAU,EAAE,CAACC,SAAS,CAAC;MACxCC,IAAI,EAAGC,OAAO,IAAI;QAChB,IAAI,CAACnB,gBAAgB,GAAGmB,OAAO;QAC/BjB,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEgB,OAAO,CAAC;MACtD,CAAC;MACDrI,KAAK,EAAGA,KAAK,IAAI;QACfoH,OAAO,CAACpH,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;QAC9D,IAAI,CAACA,KAAK,GACR,2EAA2E;MAC/E;KACD,CAAC;EACJ;EAEAwH,YAAYA,CAAA;IACV,MAAMc,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAIF,KAAK,EAAE;MACT,IAAI,CAAC3B,WAAW,CAAC8B,WAAW,CAACH,KAAK,CAAC,CAACH,SAAS,CAAC;QAC5CC,IAAI,EAAGM,KAAU,IAAI;UACnB,IAAI,CAACtF,cAAc,GAAGsF,KAAK;UAC3BtB,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEqB,KAAK,CAAC;QACzD,CAAC;QACD1I,KAAK,EAAGA,KAAK,IAAI;UACfoH,OAAO,CAACpH,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;UACnE,IAAI,CAACA,KAAK,GACR,gFAAgF;QACpF;OACD,CAAC;;EAEN;EAEAsH,cAAcA,CAAA;IACZ,IAAI,CAACb,aAAa,CAACkC,UAAU,EAAE,CAACR,SAAS,CAAC;MACxCC,IAAI,EAAGQ,OAAO,IAAI;QAChB,IAAI,CAAC3B,eAAe,GAAG2B,OAAO;QAC9BxB,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEuB,OAAO,CAAC;MACtD,CAAC;MACD5I,KAAK,EAAGA,KAAK,IAAI;QACfoH,OAAO,CAACpH,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAChE;KACD,CAAC;EACJ;EAEA4H,UAAUA,CAAC1F,EAAU;IACnBkF,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEnF,EAAE,CAAC;IAC1C,IAAI,CAAC6E,OAAO,GAAG,IAAI;IACnB,IAAI,CAAC/G,KAAK,GAAG,IAAI;IAEjB,IAAI,CAACyG,aAAa,CAACoC,SAAS,CAAC3G,EAAE,CAAC,CAACiG,SAAS,CAAC;MACzCC,IAAI,EAAGU,IAAI,IAAI;QACb1B,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEyB,IAAI,CAAC;QACpC,IAAI,CAAChH,MAAM,GAAGgH,IAAI;QAElB;QACA1B,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE,IAAI,CAACvF,MAAM,CAACG,GAAG,CAAC;QAChEmF,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAAChB,QAAQ,CAAC;QAE5C;QACA,IAAI,IAAI,CAACvE,MAAM,CAACC,OAAO,IAAI,IAAI,CAACD,MAAM,CAACC,OAAO,CAAC4B,MAAM,GAAG,CAAC,EAAE;UACzD,IAAI,CAACoF,kBAAkB,EAAE;;QAG3B,IAAI,CAAChC,OAAO,GAAG,KAAK;MACtB,CAAC;MACD/G,KAAK,EAAGA,KAAK,IAAI;QACfoH,OAAO,CAACpH,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;QAC9D,IAAI,CAACA,KAAK,GACR,8EAA8E;QAChF,IAAI,CAAC+G,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEA;EACAgC,kBAAkBA,CAAA;IAChB,IAAI,CAAC,IAAI,CAACjH,MAAM,CAACC,OAAO,IAAI,IAAI,CAACD,MAAM,CAACC,OAAO,CAAC4B,MAAM,KAAK,CAAC,EAAE;MAC5D;;IAGFyD,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;IAEhE;IACA,IAAI,CAACvF,MAAM,CAACC,OAAO,CAACiH,OAAO,CAAEC,QAAQ,IAAI;MACvC;MACA,MAAMC,IAAI,GAAG,IAAI,CAAC9F,cAAc,CAAC+F,IAAI,CAClCC,CAAC,IAAKA,CAAC,CAACnH,GAAG,KAAKgH,QAAQ,IAAIG,CAAC,CAAClH,EAAE,KAAK+G,QAAQ,CAC/C;MACD,IAAIC,IAAI,EAAE;QACR9B,OAAO,CAACC,GAAG,CACT,UAAU4B,QAAQ,yCAAyC,EAC3DC,IAAI,CACL;QAED;QACA,IAAI,CAACA,IAAI,CAAC3G,KAAK,IAAK,CAAC2G,IAAI,CAAC1G,UAAU,IAAI,CAAC0G,IAAI,CAACzG,IAAK,EAAE;UACnD;UACA,MAAM6F,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;UAC3C,IAAIF,KAAK,EAAE;YACT,IAAI,CAAC3B,WAAW,CAAC0C,WAAW,CAACJ,QAAQ,EAAEX,KAAK,CAAC,CAACH,SAAS,CAAC;cACtDC,IAAI,EAAGkB,QAAa,IAAI;gBACtBlC,OAAO,CAACC,GAAG,CACT,4CAA4C4B,QAAQ,aAAa,EACjEK,QAAQ,CACT;gBAED;gBACA,MAAMC,KAAK,GAAG,IAAI,CAACnG,cAAc,CAACoG,SAAS,CACxCJ,CAAC,IAAKA,CAAC,CAACnH,GAAG,KAAKgH,QAAQ,IAAIG,CAAC,CAAClH,EAAE,KAAK+G,QAAQ,CAC/C;gBACD,IAAIM,KAAK,KAAK,CAAC,CAAC,EAAE;kBAChB,IAAI,CAACnG,cAAc,CAACmG,KAAK,CAAC,GAAG;oBAC3B,GAAG,IAAI,CAACnG,cAAc,CAACmG,KAAK,CAAC;oBAC7B,GAAGD;mBACJ;;cAEL,CAAC;cACDtJ,KAAK,EAAGA,KAAK,IAAI;gBACfoH,OAAO,CAACpH,KAAK,CACX,+EAA+EiJ,QAAQ,GAAG,EAC1FjJ,KAAK,CACN;cACH;aACD,CAAC;;;OAGP,MAAM;QACL;QACA,MAAMsI,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC3C,IAAIF,KAAK,EAAE;UACT,IAAI,CAAC3B,WAAW,CAAC0C,WAAW,CAACJ,QAAQ,EAAEX,KAAK,CAAC,CAACH,SAAS,CAAC;YACtDC,IAAI,EAAGkB,QAAa,IAAI;cACtBlC,OAAO,CAACC,GAAG,CACT,4BAA4B4B,QAAQ,aAAa,EACjDK,QAAQ,CACT;cACD;cACA,IACE,CAAC,IAAI,CAAClG,cAAc,CAACqG,IAAI,CACtBL,CAAC,IAAKA,CAAC,CAACnH,GAAG,KAAKqH,QAAQ,CAACrH,GAAG,IAAImH,CAAC,CAAClH,EAAE,KAAKoH,QAAQ,CAACpH,EAAE,CACtD,EACD;gBACA,IAAI,CAACkB,cAAc,CAACsG,IAAI,CAACJ,QAAQ,CAAC;;YAEtC,CAAC;YACDtJ,KAAK,EAAGA,KAAK,IAAI;cACfoH,OAAO,CAACpH,KAAK,CACX,+DAA+DiJ,QAAQ,GAAG,EAC1EjJ,KAAK,CACN;YACH;WACD,CAAC;;;IAGR,CAAC,CAAC;EACJ;EAEA2J,eAAeA,CAACrH,IAAY;IAC1B;IACA,IAAI,IAAI,CAAC8B,UAAU,IAAI,IAAI,CAACiC,QAAQ,EAAE;MACpC,OAAO,IAAI,CAACY,eAAe,CAACwC,IAAI,CAC7BG,CAAC,IAAKA,CAAC,CAACtH,IAAI,KAAKA,IAAI,IAAIsH,CAAC,CAAC3H,GAAG,KAAK,IAAI,CAACoE,QAAQ,CAClD;;IAEH;IACA,OAAO,IAAI,CAACY,eAAe,CAACwC,IAAI,CAAEG,CAAC,IAAKA,CAAC,CAACtH,IAAI,KAAKA,IAAI,CAAC;EAC1D;EAEAsC,UAAUA,CAAC1B,KAAa;IACtBkE,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEnE,KAAK,CAAC;IACnC,IAAI,CAACpB,MAAM,CAACQ,IAAI,GAAGY,KAAK;IAExB;IACA,IAAI,CAAC8C,UAAU,GAAG,IAAI,CAAC2D,eAAe,CAACzG,KAAK,CAAC;IAC7C,IAAI,IAAI,CAAC8C,UAAU,EAAE;MACnBoB,OAAO,CAACyC,IAAI,CAAC,6BAA6B,CAAC;;IAG7C;IACA,IAAI,CAAC5D,SAAS,GAAG/C,KAAK,CAACS,MAAM,GAAG,CAAC,IAAIT,KAAK,CAACS,MAAM,GAAG,CAAC;IACrD,IAAI,IAAI,CAACsC,SAAS,EAAE;MAClBmB,OAAO,CAACyC,IAAI,CAAC,4CAA4C,CAAC;;EAE9D;EAEA1E,iBAAiBA,CAACjC,KAAa;IAC7BkE,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEnE,KAAK,CAAC;IAC1C,IAAI,CAACpB,MAAM,CAACqE,WAAW,GAAGjD,KAAK;IAE/B;IACA,IAAI,CAACgD,gBAAgB,GAAGhD,KAAK,CAACS,MAAM,GAAG,CAAC,IAAIT,KAAK,CAACS,MAAM,GAAG,EAAE;IAC7D,IAAI,IAAI,CAACuC,gBAAgB,EAAE;MACzBkB,OAAO,CAACyC,IAAI,CAAC,qDAAqD,CAAC;;EAEvE;EAEArF,QAAQA,CAAA;IACN4C,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAACvF,MAAM,CAAC;IAEhD;IACA,IAAI,CAAC,IAAI,CAACA,MAAM,CAACQ,IAAI,EAAE;MACrB,IAAI,CAACtC,KAAK,GAAG,gCAAgC;MAC7C;;IAGF,IAAI,IAAI,CAAC8B,MAAM,CAACQ,IAAI,CAACqB,MAAM,GAAG,CAAC,EAAE;MAC/B,IAAI,CAACsC,SAAS,GAAG,IAAI;MACrB,IAAI,CAACjG,KAAK,GAAG,yDAAyD;MACtE;;IAGF;IACA,IAAI,CAAC,IAAI,CAAC8B,MAAM,CAACqE,WAAW,EAAE;MAC5B,IAAI,CAACnG,KAAK,GAAG,yCAAyC;MACtD;;IAGF,IAAI,IAAI,CAAC8B,MAAM,CAACqE,WAAW,CAACxC,MAAM,GAAG,EAAE,EAAE;MACvC,IAAI,CAACuC,gBAAgB,GAAG,IAAI;MAC5B,IAAI,CAAClG,KAAK,GACR,kEAAkE;MACpE;;IAGF;IACA,IAAI,IAAI,CAAC2J,eAAe,CAAC,IAAI,CAAC7H,MAAM,CAACQ,IAAI,CAAC,EAAE;MAC1C,IAAI,CAAC0D,UAAU,GAAG,IAAI;MACtB,IAAI,CAAChG,KAAK,GACR,oEAAoE;MACtE;;IAGF,IAAI,CAACsG,UAAU,GAAG,IAAI;IACtB,IAAI,CAACtG,KAAK,GAAG,IAAI;IAEjB;IACA,MAAM8J,YAAY,GAAW;MAC3BxH,IAAI,EAAE,IAAI,CAACR,MAAM,CAACQ,IAAI;MACtB6D,WAAW,EAAE,IAAI,CAACrE,MAAM,CAACqE,WAAW,IAAI,EAAE;MAC1CC,KAAK,EAAE,IAAI,CAACtE,MAAM,CAACsE;KACpB;IAED;IACA,IAAI,IAAI,CAAChC,UAAU,IAAI,IAAI,CAACiC,QAAQ,EAAE;MACpCyD,YAAY,CAAC7H,GAAG,GAAG,IAAI,CAACoE,QAAQ;;IAGlCe,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEyC,YAAY,CAAC;IAE/C,IAAI,IAAI,CAAC1F,UAAU,IAAI,IAAI,CAACiC,QAAQ,EAAE;MACpC;MACA,IAAI,CAACI,aAAa,CAACsD,YAAY,CAAC,IAAI,CAAC1D,QAAQ,EAAEyD,YAAY,CAAC,CAAC3B,SAAS,CAAC;QACrEC,IAAI,EAAG4B,QAAQ,IAAI;UACjB5C,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE2C,QAAQ,CAAC;UACxD,IAAI,CAAC1D,UAAU,GAAG,KAAK;UACvB,IAAI,CAACQ,mBAAmB,CAACmD,WAAW,CAClC,aAAaD,QAAQ,CAAC1H,IAAI,kCAAkC,CAC7D;UACD,IAAI,CAACuE,MAAM,CAACqD,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;QAC1C,CAAC;QACDlK,KAAK,EAAGA,KAAK,IAAI;UACfoH,OAAO,CAACpH,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;UAClE,IAAI,CAACA,KAAK,GAAG,yCAAyCA,KAAK,CAACmK,OAAO,EAAE;UACrE,IAAI,CAAC7D,UAAU,GAAG,KAAK;UACvB,IAAI,CAACQ,mBAAmB,CAACsD,SAAS,CAAC,WAAWpK,KAAK,CAACmK,OAAO,EAAE,CAAC;QAChE;OACD,CAAC;KACH,MAAM;MACL;MACA,IAAI,CAAC1D,aAAa,CAAC4D,SAAS,CAACP,YAAY,CAAC,CAAC3B,SAAS,CAAC;QACnDC,IAAI,EAAG4B,QAAQ,IAAI;UACjB5C,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE2C,QAAQ,CAAC;UACpD,IAAI,CAAC1D,UAAU,GAAG,KAAK;UACvB,IAAI,CAACQ,mBAAmB,CAACmD,WAAW,CAClC,aAAaD,QAAQ,CAAC1H,IAAI,4BAA4B,CACvD;UACD,IAAI,CAACuE,MAAM,CAACqD,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;QAC1C,CAAC;QACDlK,KAAK,EAAGA,KAAK,IAAI;UACfoH,OAAO,CAACpH,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;UAC3D,IAAI,CAACA,KAAK,GAAG,kCAAkCA,KAAK,CAACmK,OAAO,EAAE;UAC9D,IAAI,CAAC7D,UAAU,GAAG,KAAK;UACvB,IAAI,CAACQ,mBAAmB,CAACsD,SAAS,CAAC,WAAWpK,KAAK,CAACmK,OAAO,EAAE,CAAC;QAChE;OACD,CAAC;;EAEN;EAEA1E,MAAMA,CAAA;IACJ2B,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAC7B,IAAI,CAACR,MAAM,CAACqD,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;EAC1C;EAEA;EACAjH,iBAAiBA,CAACgG,QAAgB,EAAExG,IAAA,GAAe,QAAQ;IACzD2E,OAAO,CAACC,GAAG,CACT,2CAA2C,EAC3C4B,QAAQ,EACR,UAAU,EACVxG,IAAI,CACL;IACD2E,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAAChB,QAAQ,CAAC;IAC1De,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAACvF,MAAM,CAAC;IAEtD;IACA,MAAMuE,QAAQ,GAAG,IAAI,CAACA,QAAQ,IAAK,IAAI,CAACvE,MAAM,IAAI,IAAI,CAACA,MAAM,CAACG,GAAI;IAElEmF,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEhB,QAAQ,CAAC;IAE1C,IAAI,CAACA,QAAQ,IAAI,CAAC4C,QAAQ,EAAE;MAC1B7B,OAAO,CAACpH,KAAK,CAAC,sCAAsC,CAAC;MACrD,IAAI,CAACA,KAAK,GAAG,sCAAsC;MACnDoH,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEhB,QAAQ,EAAE,WAAW,EAAE4C,QAAQ,CAAC;MAEzD;MACA,IAAI,CAACnC,mBAAmB,CAACsD,SAAS,CAChC,sEAAsE,CACvE;MACD;;IAGF;IACA,IAAI,IAAI,CAACtI,MAAM,CAACC,OAAO,IAAI,IAAI,CAACD,MAAM,CAACC,OAAO,CAACuI,QAAQ,CAACrB,QAAQ,CAAC,EAAE;MACjE,IAAI,CAACnC,mBAAmB,CAACsD,SAAS,CAChC,wCAAwC,CACzC;MACD;;IAGF;IACA,MAAMlB,IAAI,GAAG,IAAI,CAAC9F,cAAc,CAAC+F,IAAI,CAClCC,CAAC,IAAKA,CAAC,CAACnH,GAAG,KAAKgH,QAAQ,IAAIG,CAAC,CAAClH,EAAE,KAAK+G,QAAQ,CAC/C;IACD,MAAMsB,QAAQ,GAAGrB,IAAI,GACjBA,IAAI,CAAC9G,SAAS,IAAI8G,IAAI,CAAC7G,QAAQ,GAC7B,GAAG6G,IAAI,CAAC9G,SAAS,IAAI8G,IAAI,CAAC7G,QAAQ,EAAE,GACpC6G,IAAI,CAAC5G,IAAI,IAAI2G,QAAQ,GACvBA,QAAQ;IAEZ;IACA,MAAMuB,MAAM,GAAW;MACrBtI,EAAE,EAAE+G,QAAQ;MACZxG,IAAI,EAAEA;KACP;IAED,IAAI,CAACsE,OAAO,GAAG,IAAI;IAEnBK,OAAO,CAACC,GAAG,CACT,2BAA2BkD,QAAQ,WAAW9H,IAAI,eAAe4D,QAAQ,EAAE,CAC5E;IAED,IAAI,CAACI,aAAa,CAACxD,iBAAiB,CAACoD,QAAQ,EAAEmE,MAAM,CAAC,CAACrC,SAAS,CAAC;MAC/DC,IAAI,EAAG4B,QAAQ,IAAI;QACjB5C,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE2C,QAAQ,CAAC;QACnD,IAAI,CAAClD,mBAAmB,CAACmD,WAAW,CAClC,GAAGM,QAAQ,uBACT9H,IAAI,KAAK,OAAO,GAAG,gBAAgB,GAAG,QACxC,aAAa,CACd;QACD;QACA,IAAI,CAACmF,UAAU,CAACvB,QAAQ,CAAC;QACzB,IAAI,CAACU,OAAO,GAAG,KAAK;MACtB,CAAC;MACD/G,KAAK,EAAGA,KAAK,IAAI;QACfoH,OAAO,CAACpH,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QACzD,IAAI,CAACA,KAAK,GACR,+DAA+D;QACjE,IAAI,CAAC8G,mBAAmB,CAACsD,SAAS,CAChC,oCAAoC,GAAGpK,KAAK,CAACmK,OAAO,CACrD;QACD,IAAI,CAACpD,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEA;EACAzF,aAAaA,CAAC2H,QAAgB;IAC5B;IACA,MAAMC,IAAI,GAAG,IAAI,CAAC9F,cAAc,CAAC+F,IAAI,CAClCC,CAAC,IAAKA,CAAC,CAACnH,GAAG,KAAKgH,QAAQ,IAAIG,CAAC,CAAClH,EAAE,KAAK+G,QAAQ,CAC/C;IACD,IAAIC,IAAI,EAAE;MACR,IAAIA,IAAI,CAAC9G,SAAS,IAAI8G,IAAI,CAAC7G,QAAQ,EAAE;QACnC,OAAO,GAAG6G,IAAI,CAAC9G,SAAS,IAAI8G,IAAI,CAAC7G,QAAQ,EAAE;OAC5C,MAAM,IAAI6G,IAAI,CAAC5G,IAAI,EAAE;QACpB,OAAO4G,IAAI,CAAC5G,IAAI;;;IAIpB;IACA,MAAMkI,MAAM,GAAG,IAAI,CAACtD,gBAAgB,CAACiC,IAAI,CACtCsB,CAAC,IAAKA,CAAC,CAACxI,GAAG,KAAKgH,QAAQ,IAAIwB,CAAC,CAACvI,EAAE,KAAK+G,QAAQ,CAC/C;IACD,IAAIuB,MAAM,IAAIA,MAAM,CAAClI,IAAI,EAAE;MACzB,OAAOkI,MAAM,CAAClI,IAAI;;IAGpB;IACA,OAAO2G,QAAQ;EACjB;EAEA;EACA7I,cAAcA,CAAC6I,QAAgB;IAC7B,MAAMC,IAAI,GAAG,IAAI,CAAC9F,cAAc,CAAC+F,IAAI,CAClCC,CAAC,IAAKA,CAAC,CAACnH,GAAG,KAAKgH,QAAQ,IAAIG,CAAC,CAAClH,EAAE,KAAK+G,QAAQ,CAC/C;IACD,IAAIC,IAAI,IAAIA,IAAI,CAAC3G,KAAK,EAAE;MACtB,OAAO2G,IAAI,CAAC3G,KAAK;;IAEnB,OAAO,eAAe;EACxB;EAEA;EACAb,mBAAmBA,CAACuH,QAAgB;IAClC,MAAMC,IAAI,GAAG,IAAI,CAAC9F,cAAc,CAAC+F,IAAI,CAClCC,CAAC,IAAKA,CAAC,CAACnH,GAAG,KAAKgH,QAAQ,IAAIG,CAAC,CAAClH,EAAE,KAAK+G,QAAQ,CAC/C;IACD,IAAIC,IAAI,EAAE;MACR,IAAIA,IAAI,CAAC1G,UAAU,EAAE;QACnB,OAAO0G,IAAI,CAAC1G,UAAU,KAAK,UAAU,GAAG,UAAU,GAAG,YAAY;OAClE,MAAM,IAAI0G,IAAI,CAACzG,IAAI,EAAE;QACpB,OAAOyG,IAAI,CAACzG,IAAI,KAAK,UAAU,GAAG,UAAU,GAAG,YAAY;;;IAG/D,OAAO,cAAc;EACvB;EAEA;EACAiI,aAAaA,CAACC,SAAiB;IAC7B;IACA;IACA,OAAO,QAAQ;EACjB;EAEAxJ,sBAAsBA,CAAC8H,QAAgB;IACrC7B,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAE4B,QAAQ,CAAC;IACxE7B,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAAChB,QAAQ,CAAC;IAC1De,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAACvF,MAAM,CAAC;IAEtD;IACA,MAAMuE,QAAQ,GAAG,IAAI,CAACA,QAAQ,IAAK,IAAI,CAACvE,MAAM,IAAI,IAAI,CAACA,MAAM,CAACG,GAAI;IAElE,IAAI,CAACoE,QAAQ,EAAE;MACbe,OAAO,CAACpH,KAAK,CAAC,sBAAsB,CAAC;MACrC,IAAI,CAACA,KAAK,GAAG,wDAAwD;MACrE,IAAI,CAAC8G,mBAAmB,CAACsD,SAAS,CAChC,wDAAwD,CACzD;MACD;;IAGF,IAAI,CAACnB,QAAQ,EAAE;MACb7B,OAAO,CAACpH,KAAK,CAAC,uBAAuB,CAAC;MACtC,IAAI,CAACA,KAAK,GAAG,yDAAyD;MACtE,IAAI,CAAC8G,mBAAmB,CAACsD,SAAS,CAChC,yDAAyD,CAC1D;MACD;;IAGF;IACA,MAAMQ,UAAU,GAAG,IAAI,CAACtJ,aAAa,CAAC2H,QAAQ,CAAC;IAE/C7B,OAAO,CAACC,GAAG,CACT,yCAAyC4B,QAAQ,KAAK2B,UAAU,iBAAiBvE,QAAQ,EAAE,CAC5F;IAED,IAAI;MACF,IACEwE,OAAO,CAAC,oCAAoCD,UAAU,eAAe,CAAC,EACtE;QACAxD,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;QAE7D,IAAI,CAACN,OAAO,GAAG,IAAI;QACnB,IAAI,CAAC/G,KAAK,GAAG,IAAI;QAEjB;QACA6H,UAAU,CAAC,MAAK;UACd,IAAI,CAACpB,aAAa,CACftF,sBAAsB,CAACkF,QAAQ,EAAE4C,QAAQ,CAAC,CAC1Cd,SAAS,CAAC;YACTC,IAAI,EAAG4B,QAAQ,IAAI;cACjB5C,OAAO,CAACC,GAAG,CACT,gBAAgBuD,UAAU,mCAAmC,EAC7DZ,QAAQ,CACT;cACD,IAAI,CAACjD,OAAO,GAAG,KAAK;cACpB,IAAI,CAACD,mBAAmB,CAACmD,WAAW,CAClC,GAAGW,UAAU,uCAAuC,CACrD;cAED;cACA,IAAI,CAAChD,UAAU,CAACvB,QAAQ,CAAC;YAC3B,CAAC;YACDrG,KAAK,EAAGA,KAAK,IAAI;cACfoH,OAAO,CAACpH,KAAK,CACX,4CAA4C4K,UAAU,IAAI,EAC1D5K,KAAK,CACN;cACDoH,OAAO,CAACpH,KAAK,CAAC,sBAAsB,EAAE;gBACpC8K,MAAM,EAAE9K,KAAK,CAAC8K,MAAM;gBACpBX,OAAO,EAAEnK,KAAK,CAACmK,OAAO;gBACtBnK,KAAK,EAAEA;eACR,CAAC;cAEF,IAAI,CAAC+G,OAAO,GAAG,KAAK;cACpB,IAAI,CAAC/G,KAAK,GAAG,wCAAwC4K,UAAU,kBAC7D5K,KAAK,CAACmK,OAAO,IAAI,iBACnB,EAAE;cACF,IAAI,CAACrD,mBAAmB,CAACsD,SAAS,CAChC,qCAAqC,IAAI,CAACpK,KAAK,EAAE,CAClD;YACH;WACD,CAAC;QACN,CAAC,EAAE,GAAG,CAAC;OACR,MAAM;QACLoH,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;;KAEvD,CAAC,OAAOrH,KAAU,EAAE;MACnBoH,OAAO,CAACpH,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D,IAAI,CAACA,KAAK,GAAG,cAAcA,KAAK,EAAEmK,OAAO,IAAI,iBAAiB,EAAE;MAChE,IAAI,CAACrD,mBAAmB,CAACsD,SAAS,CAAC,cAAc,IAAI,CAACpK,KAAK,EAAE,CAAC;;EAElE;EAEA;EACAgE,YAAYA,CAAA;IACVoD,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;IACzED,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAAChB,QAAQ,CAAC;IAC1De,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAACvF,MAAM,CAAC;IAEtD;IACA,MAAMuE,QAAQ,GAAG,IAAI,CAACA,QAAQ,IAAK,IAAI,CAACvE,MAAM,IAAI,IAAI,CAACA,MAAM,CAACG,GAAI;IAElE,IAAI,CAACoE,QAAQ,EAAE;MACbe,OAAO,CAACpH,KAAK,CAAC,sBAAsB,CAAC;MACrC,IAAI,CAACA,KAAK,GAAG,yDAAyD;MACtE,IAAI,CAAC8G,mBAAmB,CAACsD,SAAS,CAChC,yDAAyD,CAC1D;MACD;;IAGFhD,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEhB,QAAQ,CAAC;IAE5D,IAAI;MACF,IACEwE,OAAO,CACL,gDAAgD,IAAI,CAAC/I,MAAM,CAACQ,IAAI,mCAAmC,CACpG,EACD;QACA8E,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;QAE7D,IAAI,CAACN,OAAO,GAAG,IAAI;QACnB,IAAI,CAAC/G,KAAK,GAAG,IAAI;QAEjB;QACA6H,UAAU,CAAC,MAAK;UACd,IAAI,CAACpB,aAAa,CAACzC,YAAY,CAACqC,QAAQ,CAAC,CAAC8B,SAAS,CAAC;YAClDC,IAAI,EAAG4B,QAAQ,IAAI;cACjB5C,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE2C,QAAQ,CAAC;cAC/D,IAAI,CAACjD,OAAO,GAAG,KAAK;cACpB,IAAI,CAACD,mBAAmB,CAACmD,WAAW,CAClC,aAAa,IAAI,CAACnI,MAAM,CAACQ,IAAI,gCAAgC,CAC9D;cAED;cACAuF,UAAU,CAAC,MAAK;gBACd,IAAI,CAAChB,MAAM,CAACqD,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;cAC1C,CAAC,EAAE,GAAG,CAAC;YACT,CAAC;YACDlK,KAAK,EAAGA,KAAK,IAAI;cACfoH,OAAO,CAACpH,KAAK,CACX,4CAA4C,EAC5CA,KAAK,CACN;cACDoH,OAAO,CAACpH,KAAK,CAAC,sBAAsB,EAAE;gBACpC8K,MAAM,EAAE9K,KAAK,CAAC8K,MAAM;gBACpBX,OAAO,EAAEnK,KAAK,CAACmK,OAAO;gBACtBnK,KAAK,EAAEA;eACR,CAAC;cAEF,IAAI,CAAC+G,OAAO,GAAG,KAAK;cACpB,IAAI,CAAC/G,KAAK,GAAG,qCACXA,KAAK,CAACmK,OAAO,IAAI,iBACnB,EAAE;cACF,IAAI,CAACrD,mBAAmB,CAACsD,SAAS,CAChC,kCAAkC,IAAI,CAACpK,KAAK,EAAE,CAC/C;YACH;WACD,CAAC;QACJ,CAAC,EAAE,GAAG,CAAC;OACR,MAAM;QACLoH,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;;KAEvD,CAAC,OAAOrH,KAAU,EAAE;MACnBoH,OAAO,CAACpH,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,IAAI,CAACA,KAAK,GAAG,cAAcA,KAAK,EAAEmK,OAAO,IAAI,iBAAiB,EAAE;MAChE,IAAI,CAACrD,mBAAmB,CAACsD,SAAS,CAAC,cAAc,IAAI,CAACpK,KAAK,EAAE,CAAC;;EAElE;;;uBA1qBWuG,mBAAmB,EAAA/G,EAAA,CAAAuL,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAAzL,EAAA,CAAAuL,iBAAA,CAAAG,EAAA,CAAAC,aAAA,GAAA3L,EAAA,CAAAuL,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAA7L,EAAA,CAAAuL,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAA/L,EAAA,CAAAuL,iBAAA,CAAAO,EAAA,CAAAE,MAAA,GAAAhM,EAAA,CAAAuL,iBAAA,CAAAU,EAAA,CAAAC,mBAAA;IAAA;EAAA;;;YAAnBnF,mBAAmB;MAAAoF,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCfhCzM,EAAA,CAAAC,cAAA,aAEC;UAGGD,EAAA,CAAAE,SAAA,aAEO;UAMPF,EAAA,CAAAC,cAAA,aAA4D;UAExDD,EAAA,CAAAE,SAAA,aAAmE;UAWrEF,EAAA,CAAAG,YAAA,EAAM;UAIVH,EAAA,CAAAC,cAAA,cAAiD;UAI7CD,EAAA,CAAAE,SAAA,cAEO;UAKPF,EAAA,CAAAC,cAAA,eAEC;UAQOD,EAAA,CAAAI,MAAA,IACF;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,aAAsD;UACpDD,EAAA,CAAAI,MAAA,IAKF;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAGNH,EAAA,CAAAC,cAAA,kBAGC;UAFCD,EAAA,CAAAkB,UAAA,mBAAAyL,sDAAA;YAAA,OAASD,GAAA,CAAAzG,MAAA,EAAQ;UAAA,EAAC;UAGlBjG,EAAA,CAAAE,SAAA,aAAsC;UACtCF,EAAA,CAAAI,MAAA,gCACF;UAAAJ,EAAA,CAAAG,YAAA,EAAS;UAMfH,EAAA,CAAAc,UAAA,KAAA8L,mCAAA,kBAiBM;UAGN5M,EAAA,CAAAc,UAAA,KAAA+L,mCAAA,mBAkBM;UAGN7M,EAAA,CAAAc,UAAA,KAAAgM,mCAAA,oBAobM;UACR9M,EAAA,CAAAG,YAAA,EAAM;;;UArfMH,EAAA,CAAAK,SAAA,IACF;UADEL,EAAA,CAAAM,kBAAA,MAAAoM,GAAA,CAAA9H,UAAA,0DACF;UAEE5E,EAAA,CAAAK,SAAA,GAKF;UALEL,EAAA,CAAAM,kBAAA,MAAAoM,GAAA,CAAA9H,UAAA,sJAKF;UAgBL5E,EAAA,CAAAK,SAAA,GAAa;UAAbL,EAAA,CAAA+B,UAAA,SAAA2K,GAAA,CAAAnF,OAAA,CAAa;UAmBVvH,EAAA,CAAAK,SAAA,GAAW;UAAXL,EAAA,CAAA+B,UAAA,SAAA2K,GAAA,CAAAlM,KAAA,CAAW;UAqBwBR,EAAA,CAAAK,SAAA,GAAc;UAAdL,EAAA,CAAA+B,UAAA,UAAA2K,GAAA,CAAAnF,OAAA,CAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}