{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { EquipesRoutingModule } from './equipes-routing.module';\nimport { EquipeListComponent } from './equipe-list/equipe-list.component';\nimport { EquipeFormComponent } from './equipe-form/equipe-form.component';\nimport { EquipeDetailComponent } from './equipe-detail/equipe-detail.component';\nimport { TaskListComponent } from './task-list/task-list.component';\nimport { AiChatComponent } from './ai-chat/ai-chat.component';\nimport { EquipeComponent } from './equipe/equipe.component';\nimport { NotificationComponent } from './notification/notification.component';\nimport { HttpClientModule } from '@angular/common/http';\nimport { FormsModule } from '@angular/forms';\nimport { DragDropModule } from '@angular/cdk/drag-drop';\nexport let EquipesModule = class EquipesModule {};\nEquipesModule = __decorate([NgModule({\n  declarations: [EquipeListComponent, EquipeFormComponent, EquipeDetailComponent, TaskListComponent, AiChatComponent, EquipeComponent, NotificationComponent],\n  imports: [CommonModule, EquipesRoutingModule, FormsModule, DragDropModule, HttpClientModule],\n  providers: []\n})], EquipesModule);", "map": {"version": 3, "names": ["NgModule", "CommonModule", "EquipesRoutingModule", "EquipeListComponent", "EquipeFormComponent", "EquipeDetailComponent", "TaskListComponent", "AiChatComponent", "EquipeComponent", "NotificationComponent", "HttpClientModule", "FormsModule", "DragDropModule", "EquipesModule", "__decorate", "declarations", "imports", "providers"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\equipes\\equipes.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\n\nimport { EquipesRoutingModule } from './equipes-routing.module';\nimport { EquipeListComponent } from './equipe-list/equipe-list.component';\nimport { EquipeFormComponent } from './equipe-form/equipe-form.component';\nimport { EquipeDetailComponent } from './equipe-detail/equipe-detail.component';\nimport { TaskListComponent } from './task-list/task-list.component';\nimport { AiChatComponent } from './ai-chat/ai-chat.component';\nimport { EquipeComponent } from './equipe/equipe.component';\nimport { NotificationComponent } from './notification/notification.component';\nimport { HttpClientModule } from '@angular/common/http';\nimport { FormsModule } from '@angular/forms';\nimport { DragDropModule } from '@angular/cdk/drag-drop';\n\n@NgModule({\n  declarations: [\n    EquipeListComponent,\n    EquipeFormComponent,\n    EquipeDetailComponent,\n    TaskListComponent,\n    AiChatComponent,\n    EquipeComponent,\n    NotificationComponent,\n  ],\n  imports: [\n    CommonModule,\n    EquipesRoutingModule,\n    FormsModule,\n    DragDropModule,\n    HttpClientModule,\n  ],\n  providers: [],\n})\nexport class EquipesModule {}\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,oBAAoB,QAAQ,0BAA0B;AAC/D,SAASC,mBAAmB,QAAQ,qCAAqC;AACzE,SAASC,mBAAmB,QAAQ,qCAAqC;AACzE,SAASC,qBAAqB,QAAQ,yCAAyC;AAC/E,SAASC,iBAAiB,QAAQ,iCAAiC;AACnE,SAASC,eAAe,QAAQ,6BAA6B;AAC7D,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,qBAAqB,QAAQ,uCAAuC;AAC7E,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,cAAc,QAAQ,wBAAwB;AAqBhD,WAAMC,aAAa,GAAnB,MAAMA,aAAa,GAAG;AAAhBA,aAAa,GAAAC,UAAA,EAnBzBd,QAAQ,CAAC;EACRe,YAAY,EAAE,CACZZ,mBAAmB,EACnBC,mBAAmB,EACnBC,qBAAqB,EACrBC,iBAAiB,EACjBC,eAAe,EACfC,eAAe,EACfC,qBAAqB,CACtB;EACDO,OAAO,EAAE,CACPf,YAAY,EACZC,oBAAoB,EACpBS,WAAW,EACXC,cAAc,EACdF,gBAAgB,CACjB;EACDO,SAAS,EAAE;CACZ,CAAC,C,EACWJ,aAAa,CAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}