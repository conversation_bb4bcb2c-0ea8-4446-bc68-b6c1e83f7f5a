{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/equipe.service\";\nimport * as i2 from \"src/app/services/membre.service\";\nimport * as i3 from \"src/app/services/authuser.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"src/app/services/notification.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/forms\";\nfunction EquipeFormComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"div\", 14)(2, \"div\", 15)(3, \"span\", 16);\n    i0.ɵɵtext(4, \"Chargement...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 17)(6, \"span\", 16);\n    i0.ɵɵtext(7, \"Chargement...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 15)(9, \"span\", 16);\n    i0.ɵɵtext(10, \"Chargement...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"p\", 18);\n    i0.ɵɵtext(12, \"Chargement des donn\\u00E9es...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction EquipeFormComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"div\", 3)(2, \"div\", 20);\n    i0.ɵɵelement(3, \"i\", 21);\n    i0.ɵɵelementStart(4, \"div\", 22);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction EquipeFormComponent_div_16_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57);\n    i0.ɵɵelement(1, \"i\", 58);\n    i0.ɵɵtext(2, \" Ce nom d'\\u00E9quipe existe d\\u00E9j\\u00E0. Veuillez en choisir un autre. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipeFormComponent_div_16_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57);\n    i0.ɵɵelement(1, \"i\", 58);\n    i0.ɵɵtext(2, \" Le nom de l'\\u00E9quipe doit contenir au moins 3 caract\\u00E8res. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipeFormComponent_div_16_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵelement(1, \"i\", 60);\n    i0.ɵɵtext(2, \" Le nom de l'\\u00E9quipe est requis. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipeFormComponent_div_16_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57);\n    i0.ɵɵelement(1, \"i\", 58);\n    i0.ɵɵtext(2, \" La description doit contenir au moins 10 caract\\u00E8res. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipeFormComponent_div_16_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵelement(1, \"i\", 60);\n    i0.ɵɵtext(2, \" La description de l'\\u00E9quipe est requise. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipeFormComponent_div_16_div_40_div_7_tr_20_small_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 89);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const membreId_r20 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"ID: \", membreId_r20, \"\");\n  }\n}\nfunction EquipeFormComponent_div_16_div_40_div_7_tr_20_a_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 90);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const membreId_r20 = i0.ɵɵnextContext().$implicit;\n    const ctx_r22 = i0.ɵɵnextContext(4);\n    i0.ɵɵpropertyInterpolate1(\"href\", \"mailto:\", ctx_r22.getMembreEmail(membreId_r20), \"\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r22.getMembreEmail(membreId_r20), \" \");\n  }\n}\nfunction EquipeFormComponent_div_16_div_40_div_7_tr_20_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 91);\n    i0.ɵɵtext(1, \"Non renseign\\u00E9\");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c0 = function (a0, a1, a2) {\n  return {\n    \"bg-primary\": a0,\n    \"bg-success\": a1,\n    \"bg-secondary\": a2\n  };\n};\nconst _c1 = function (a0, a1, a2) {\n  return {\n    \"bi-mortarboard-fill\": a0,\n    \"bi-briefcase-fill\": a1,\n    \"bi-question-circle-fill\": a2\n  };\n};\nfunction EquipeFormComponent_div_16_div_40_div_7_tr_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 81)(1, \"td\")(2, \"span\", 82);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, EquipeFormComponent_div_16_div_40_div_7_tr_20_small_4_Template, 2, 1, \"small\", 83);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtemplate(6, EquipeFormComponent_div_16_div_40_div_7_tr_20_a_6_Template, 2, 2, \"a\", 84);\n    i0.ɵɵtemplate(7, EquipeFormComponent_div_16_div_40_div_7_tr_20_span_7_Template, 2, 0, \"span\", 85);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\")(9, \"span\", 86);\n    i0.ɵɵelement(10, \"i\", 28);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"td\", 79)(13, \"button\", 87);\n    i0.ɵɵlistener(\"click\", function EquipeFormComponent_div_16_div_40_div_7_tr_20_Template_button_click_13_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r27);\n      const membreId_r20 = restoredCtx.$implicit;\n      const ctx_r26 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r26.removeMembreFromEquipe(membreId_r20));\n    });\n    i0.ɵɵelement(14, \"i\", 88);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const membreId_r20 = ctx.$implicit;\n    const ctx_r19 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r19.getMembreName(membreId_r20));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r19.getMembreName(membreId_r20) !== membreId_r20);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r19.getMembreEmail(membreId_r20) !== \"Non renseign\\u00E9\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r19.getMembreEmail(membreId_r20) === \"Non renseign\\u00E9\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(7, _c0, ctx_r19.getMembreProfession(membreId_r20) === \"\\u00C9tudiant\", ctx_r19.getMembreProfession(membreId_r20) === \"Professeur\", ctx_r19.getMembreProfession(membreId_r20) === \"Non sp\\u00E9cifi\\u00E9\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(11, _c1, ctx_r19.getMembreProfession(membreId_r20) === \"\\u00C9tudiant\", ctx_r19.getMembreProfession(membreId_r20) === \"Professeur\", ctx_r19.getMembreProfession(membreId_r20) === \"Non sp\\u00E9cifi\\u00E9\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r19.getMembreProfession(membreId_r20), \" \");\n  }\n}\nfunction EquipeFormComponent_div_16_div_40_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 72)(2, \"table\", 73)(3, \"thead\", 74)(4, \"tr\")(5, \"th\")(6, \"div\", 75);\n    i0.ɵɵelement(7, \"i\", 76);\n    i0.ɵɵtext(8, \" Nom et Pr\\u00E9nom \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"th\")(10, \"div\", 75);\n    i0.ɵɵelement(11, \"i\", 77);\n    i0.ɵɵtext(12, \" Email \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"th\")(14, \"div\", 75);\n    i0.ɵɵelement(15, \"i\", 78);\n    i0.ɵɵtext(16, \" Statut \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"th\", 79);\n    i0.ɵɵtext(18, \"Actions\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(19, \"tbody\");\n    i0.ɵɵtemplate(20, EquipeFormComponent_div_16_div_40_div_7_tr_20_Template, 15, 15, \"tr\", 80);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(20);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r14.equipe.members);\n  }\n}\nfunction EquipeFormComponent_div_16_div_40_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 92);\n    i0.ɵɵelement(1, \"i\", 93);\n    i0.ɵɵelementStart(2, \"h5\", 94);\n    i0.ɵɵtext(3, \"Aucun membre dans cette \\u00E9quipe\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 94);\n    i0.ɵɵtext(5, \"Ajoutez des membres \\u00E0 l'\\u00E9quipe en utilisant le formulaire ci-dessous.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction EquipeFormComponent_div_16_div_40_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵelement(1, \"i\", 48);\n    i0.ɵɵelementStart(2, \"div\");\n    i0.ɵɵtext(3, \"Aucun utilisateur disponible. Veuillez d'abord cr\\u00E9er des utilisateurs.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction EquipeFormComponent_div_16_div_40_div_15_option_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 112);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r31 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", user_r31._id || user_r31.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate4(\" \", user_r31.firstName || \"\", \" \", user_r31.lastName || user_r31.name || user_r31.id, \" \", user_r31.email ? \"- \" + user_r31.email : \"\", \" \", user_r31.profession ? \"(\" + (user_r31.profession === \"etudiant\" ? \"\\u00C9tudiant\" : \"Professeur\") + \")\" : user_r31.role ? \"(\" + (user_r31.role === \"etudiant\" ? \"\\u00C9tudiant\" : \"Professeur\") + \")\" : \"\", \" \");\n  }\n}\nfunction EquipeFormComponent_div_16_div_40_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r33 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 95)(1, \"div\", 29)(2, \"div\", 96)(3, \"div\", 97)(4, \"label\", 98);\n    i0.ɵɵtext(5, \"Utilisateur\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"select\", 99, 100)(8, \"option\", 101);\n    i0.ɵɵtext(9, \"S\\u00E9lectionnez un utilisateur\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, EquipeFormComponent_div_16_div_40_div_15_option_10_Template, 2, 5, \"option\", 102);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 103)(12, \"label\", 104);\n    i0.ɵɵtext(13, \"R\\u00F4le dans l'\\u00E9quipe\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"select\", 105, 106)(16, \"option\", 107);\n    i0.ɵɵtext(17, \"Membre\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"option\", 108);\n    i0.ɵɵtext(19, \"Administrateur\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(20, \"div\", 109)(21, \"button\", 110);\n    i0.ɵɵlistener(\"click\", function EquipeFormComponent_div_16_div_40_div_15_Template_button_click_21_listener() {\n      i0.ɵɵrestoreView(_r33);\n      const _r28 = i0.ɵɵreference(7);\n      const _r30 = i0.ɵɵreference(15);\n      const ctx_r32 = i0.ɵɵnextContext(3);\n      ctx_r32.addMembreToEquipe(_r28.value, _r30.value);\n      return i0.ɵɵresetView(_r28.value = \"\");\n    });\n    i0.ɵɵelement(22, \"i\", 111);\n    i0.ɵɵtext(23, \" Ajouter \");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const _r28 = i0.ɵɵreference(7);\n    const ctx_r18 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r18.availableUsers);\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"disabled\", !_r28.value);\n  }\n}\nfunction EquipeFormComponent_div_16_div_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"div\", 61)(2, \"div\", 62)(3, \"h4\", 63);\n    i0.ɵɵelement(4, \"i\", 64);\n    i0.ɵɵtext(5, \" Membres de l'\\u00E9quipe \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 29);\n    i0.ɵɵtemplate(7, EquipeFormComponent_div_16_div_40_div_7_Template, 21, 1, \"div\", 65);\n    i0.ɵɵtemplate(8, EquipeFormComponent_div_16_div_40_ng_template_8_Template, 6, 0, \"ng-template\", null, 66, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementStart(10, \"div\", 67)(11, \"h5\", 68);\n    i0.ɵɵelement(12, \"i\", 69);\n    i0.ɵɵtext(13, \" Ajouter un membre \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, EquipeFormComponent_div_16_div_40_div_14_Template, 4, 0, \"div\", 70);\n    i0.ɵɵtemplate(15, EquipeFormComponent_div_16_div_40_div_15_Template, 24, 2, \"div\", 71);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const _r15 = i0.ɵɵreference(9);\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.equipe.members && ctx_r10.equipe.members.length > 0)(\"ngIfElse\", _r15);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.availableUsers.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.availableUsers.length > 0);\n  }\n}\nfunction EquipeFormComponent_div_16_button_47_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r35 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 113);\n    i0.ɵɵlistener(\"click\", function EquipeFormComponent_div_16_button_47_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r34 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r34.deleteEquipe());\n    });\n    i0.ɵɵelement(1, \"i\", 114);\n    i0.ɵɵtext(2, \" Supprimer \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipeFormComponent_div_16_span_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 115);\n  }\n}\nconst _c2 = function (a0, a1) {\n  return {\n    \"bi-save\": a0,\n    \"bi-plus-circle\": a1\n  };\n};\nfunction EquipeFormComponent_div_16_i_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 28);\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(1, _c2, ctx_r13.isEditMode, !ctx_r13.isEditMode));\n  }\n}\nconst _c3 = function (a0, a1) {\n  return {\n    \"bi-pencil-square\": a0,\n    \"bi-plus-circle\": a1\n  };\n};\nfunction EquipeFormComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r37 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"div\", 24)(2, \"div\", 25)(3, \"div\", 26)(4, \"h3\", 27);\n    i0.ɵɵelement(5, \"i\", 28);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 29)(8, \"form\", 30);\n    i0.ɵɵlistener(\"ngSubmit\", function EquipeFormComponent_div_16_Template_form_ngSubmit_8_listener() {\n      i0.ɵɵrestoreView(_r37);\n      const ctx_r36 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r36.onSubmit());\n    });\n    i0.ɵɵelementStart(9, \"div\", 31)(10, \"label\", 32);\n    i0.ɵɵtext(11, \"Nom de l'\\u00E9quipe \");\n    i0.ɵɵelementStart(12, \"span\", 33);\n    i0.ɵɵtext(13, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 34)(15, \"span\", 35);\n    i0.ɵɵelement(16, \"i\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"input\", 37, 38);\n    i0.ɵɵlistener(\"input\", function EquipeFormComponent_div_16_Template_input_input_17_listener() {\n      i0.ɵɵrestoreView(_r37);\n      const _r3 = i0.ɵɵreference(18);\n      const ctx_r38 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r38.updateName(_r3.value));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(19, EquipeFormComponent_div_16_div_19_Template, 3, 0, \"div\", 39);\n    i0.ɵɵtemplate(20, EquipeFormComponent_div_16_div_20_Template, 3, 0, \"div\", 39);\n    i0.ɵɵtemplate(21, EquipeFormComponent_div_16_div_21_Template, 3, 0, \"div\", 40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 31)(23, \"label\", 41);\n    i0.ɵɵtext(24, \"Description \");\n    i0.ɵɵelementStart(25, \"span\", 33);\n    i0.ɵɵtext(26, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 34)(28, \"span\", 42);\n    i0.ɵɵelement(29, \"i\", 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"textarea\", 44, 45);\n    i0.ɵɵlistener(\"input\", function EquipeFormComponent_div_16_Template_textarea_input_30_listener() {\n      i0.ɵɵrestoreView(_r37);\n      const _r7 = i0.ɵɵreference(31);\n      const ctx_r39 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r39.updateDescription(_r7.value));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(32, EquipeFormComponent_div_16_div_32_Template, 3, 0, \"div\", 39);\n    i0.ɵɵtemplate(33, EquipeFormComponent_div_16_div_33_Template, 3, 0, \"div\", 40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(34, \"input\", 46);\n    i0.ɵɵelementStart(35, \"div\", 31)(36, \"div\", 47);\n    i0.ɵɵelement(37, \"i\", 48);\n    i0.ɵɵelementStart(38, \"div\");\n    i0.ɵɵtext(39, \"Un administrateur par d\\u00E9faut sera assign\\u00E9 \\u00E0 cette \\u00E9quipe.\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(40, EquipeFormComponent_div_16_div_40_Template, 16, 4, \"div\", 49);\n    i0.ɵɵelementStart(41, \"div\", 50)(42, \"div\", 51)(43, \"div\")(44, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function EquipeFormComponent_div_16_Template_button_click_44_listener() {\n      i0.ɵɵrestoreView(_r37);\n      const ctx_r40 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r40.cancel());\n    });\n    i0.ɵɵelement(45, \"i\", 8);\n    i0.ɵɵtext(46, \" Retour \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(47, EquipeFormComponent_div_16_button_47_Template, 3, 0, \"button\", 53);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"button\", 54);\n    i0.ɵɵtemplate(49, EquipeFormComponent_div_16_span_49_Template, 1, 0, \"span\", 55);\n    i0.ɵɵtemplate(50, EquipeFormComponent_div_16_i_50_Template, 1, 4, \"i\", 56);\n    i0.ɵɵtext(51);\n    i0.ɵɵelementEnd()()()()()()()();\n  }\n  if (rf & 2) {\n    const _r3 = i0.ɵɵreference(18);\n    const _r7 = i0.ɵɵreference(31);\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(20, _c3, ctx_r2.isEditMode, !ctx_r2.isEditMode));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.isEditMode ? \"Informations de l'\\u00E9quipe\" : \"D\\u00E9tails de la nouvelle \\u00E9quipe\", \" \");\n    i0.ɵɵadvance(11);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r2.nameExists || ctx_r2.nameError && _r3.value.length > 0);\n    i0.ɵɵproperty(\"value\", ctx_r2.equipe.name || \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.nameExists);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.nameError && _r3.value.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.error && !ctx_r2.equipe.name);\n    i0.ɵɵadvance(9);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r2.descriptionError && _r7.value.length > 0);\n    i0.ɵɵproperty(\"value\", ctx_r2.equipe.description || \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.descriptionError && _r7.value.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.error && !ctx_r2.equipe.description);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"value\", ctx_r2.equipe.admin);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isEditMode && ctx_r2.equipe._id);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isEditMode && ctx_r2.equipeId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.submitting || !ctx_r2.equipe.name || !ctx_r2.equipe.description || ctx_r2.nameExists || ctx_r2.nameError || ctx_r2.descriptionError);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.submitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.submitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.isEditMode ? \"Mettre \\u00E0 jour\" : \"Cr\\u00E9er l'\\u00E9quipe\", \" \");\n  }\n}\nexport let EquipeFormComponent = /*#__PURE__*/(() => {\n  class EquipeFormComponent {\n    constructor(equipeService, membreService, userService, route, router, notificationService) {\n      this.equipeService = equipeService;\n      this.membreService = membreService;\n      this.userService = userService;\n      this.route = route;\n      this.router = router;\n      this.notificationService = notificationService;\n      this.equipe = {\n        name: '',\n        description: '',\n        admin: '65f1e5b3a1d8f3c8c0f9e8d7' // ID temporaire\n      };\n\n      this.isEditMode = false;\n      this.loading = false;\n      this.submitting = false;\n      this.error = null;\n      this.equipeId = null;\n      this.nameExists = false;\n      this.nameError = false;\n      this.descriptionError = false;\n      this.checkingName = false;\n      this.existingEquipes = [];\n      this.availableMembers = []; // Liste des membres disponibles\n      this.availableUsers = []; // Liste des utilisateurs disponibles\n    }\n\n    ngOnInit() {\n      console.log('EquipeFormComponent initialized');\n      // Ensure equipe is always defined with admin\n      if (!this.equipe) {\n        this.equipe = {\n          name: '',\n          description: '',\n          admin: '65f1e5b3a1d8f3c8c0f9e8d7' // ID temporaire\n        };\n      }\n      // Charger toutes les équipes pour vérifier les noms existants\n      this.loadAllEquipes();\n      // Charger tous les membres disponibles\n      this.loadAllMembers();\n      // Charger tous les utilisateurs disponibles\n      this.loadAllUsers();\n      try {\n        // Vérifier si nous sommes en mode édition (si un ID est présent dans l'URL)\n        this.equipeId = this.route.snapshot.paramMap.get('id');\n        this.isEditMode = !!this.equipeId;\n        console.log('Edit mode:', this.isEditMode, 'ID:', this.equipeId);\n        if (this.isEditMode && this.equipeId) {\n          this.loadEquipe(this.equipeId);\n          // Ajouter un délai pour s'assurer que l'équipe est chargée\n          setTimeout(() => {\n            console.log('Après délai - this.equipeId:', this.equipeId);\n            console.log('Après délai - this.equipe:', this.equipe);\n          }, 1000);\n        }\n      } catch (error) {\n        console.error('Error in ngOnInit:', error);\n        this.error = \"Erreur d'initialisation\";\n      }\n      // Ajouter un gestionnaire d'événements pour le bouton d'ajout de membre\n      setTimeout(() => {\n        const addButton = document.getElementById('addMembreButton');\n        if (addButton) {\n          console.log(\"Bouton d'ajout de membre trouvé\");\n          addButton.addEventListener('click', () => {\n            console.log(\"Bouton d'ajout de membre cliqué\");\n          });\n        } else {\n          console.log(\"Bouton d'ajout de membre non trouvé\");\n        }\n      }, 2000);\n    }\n    loadAllMembers() {\n      this.membreService.getMembres().subscribe({\n        next: membres => {\n          this.availableMembers = membres;\n          console.log('Membres disponibles chargés:', membres);\n        },\n        error: error => {\n          console.error('Erreur lors du chargement des membres:', error);\n          this.error = 'Impossible de charger la liste des membres. Veuillez réessayer plus tard.';\n        }\n      });\n    }\n    loadAllUsers() {\n      this.userService.getUsers().subscribe({\n        next: users => {\n          this.availableUsers = users;\n          console.log('Utilisateurs disponibles chargés:', users);\n        },\n        error: error => {\n          console.error('Erreur lors du chargement des utilisateurs:', error);\n          this.error = 'Impossible de charger la liste des utilisateurs. Veuillez réessayer plus tard.';\n        }\n      });\n    }\n    loadAllEquipes() {\n      this.equipeService.getEquipes().subscribe({\n        next: equipes => {\n          this.existingEquipes = equipes;\n          console.log('Équipes existantes chargées:', equipes);\n        },\n        error: error => {\n          console.error('Erreur lors du chargement des équipes:', error);\n        }\n      });\n    }\n    loadEquipe(id) {\n      console.log('Loading equipe with ID:', id);\n      this.loading = true;\n      this.error = null;\n      this.equipeService.getEquipe(id).subscribe({\n        next: data => {\n          console.log('Équipe chargée:', data);\n          this.equipe = data;\n          // Vérifier que l'ID est correctement défini\n          console.log(\"ID de l'équipe après chargement:\", this.equipe._id);\n          console.log('this.equipeId:', this.equipeId);\n          // Si l'équipe a des membres, récupérer les informations de chaque membre\n          if (this.equipe.members && this.equipe.members.length > 0) {\n            this.loadMembersDetails();\n          }\n          this.loading = false;\n        },\n        error: error => {\n          console.error(\"Erreur lors du chargement de l'équipe:\", error);\n          this.error = \"Impossible de charger les détails de l'équipe. Veuillez réessayer plus tard.\";\n          this.loading = false;\n        }\n      });\n    }\n    // Méthode pour récupérer les détails des membres de l'équipe\n    loadMembersDetails() {\n      if (!this.equipe.members || this.equipe.members.length === 0) {\n        return;\n      }\n      console.log(\"Chargement des détails des membres de l'équipe...\");\n      // Pour chaque membre de l'équipe, essayer de trouver ses informations dans la liste des utilisateurs\n      this.equipe.members.forEach(membreId => {\n        // Chercher d'abord dans la liste des utilisateurs\n        const user = this.availableUsers.find(u => u._id === membreId || u.id === membreId);\n        if (user) {\n          console.log(`Membre ${membreId} trouvé dans la liste des utilisateurs:`, user);\n          // Vérifier si toutes les informations nécessaires sont présentes\n          if (!user.email || !user.profession && !user.role) {\n            // Si des informations manquent, essayer de les récupérer depuis l'API\n            this.userService.getUser(membreId).subscribe({\n              next: userData => {\n                console.log(`Détails supplémentaires de l'utilisateur ${membreId} récupérés:`, userData);\n                // Mettre à jour l'utilisateur dans la liste avec les nouvelles informations\n                const index = this.availableUsers.findIndex(u => u._id === membreId || u.id === membreId);\n                if (index !== -1) {\n                  this.availableUsers[index] = {\n                    ...this.availableUsers[index],\n                    ...userData\n                  };\n                }\n              },\n              error: error => {\n                console.error(`Erreur lors de la récupération des détails supplémentaires de l'utilisateur ${membreId}:`, error);\n              }\n            });\n          }\n        } else {\n          // Si non trouvé, essayer de récupérer l'utilisateur depuis l'API\n          this.userService.getUser(membreId).subscribe({\n            next: userData => {\n              console.log(`Détails de l'utilisateur ${membreId} récupérés:`, userData);\n              // Ajouter l'utilisateur à la liste des utilisateurs disponibles s'il n'y est pas déjà\n              if (!this.availableUsers.some(u => u._id === userData._id || u.id === userData.id)) {\n                this.availableUsers.push(userData);\n              }\n            },\n            error: error => {\n              console.error(`Erreur lors de la récupération des détails de l'utilisateur ${membreId}:`, error);\n            }\n          });\n        }\n      });\n    }\n    checkNameExists(name) {\n      // En mode édition, ignorer l'équipe actuelle\n      if (this.isEditMode && this.equipeId) {\n        return this.existingEquipes.some(e => e.name === name && e._id !== this.equipeId);\n      }\n      // En mode création, vérifier tous les noms\n      return this.existingEquipes.some(e => e.name === name);\n    }\n    updateName(value) {\n      console.log('Name updated:', value);\n      this.equipe.name = value;\n      // Vérifier si le nom existe déjà\n      this.nameExists = this.checkNameExists(value);\n      if (this.nameExists) {\n        console.warn(\"Ce nom d'équipe existe déjà\");\n      }\n      // Vérifier si le nom a au moins 3 caractères\n      this.nameError = value.length > 0 && value.length < 3;\n      if (this.nameError) {\n        console.warn('Le nom doit contenir au moins 3 caractères');\n      }\n    }\n    updateDescription(value) {\n      console.log('Description updated:', value);\n      this.equipe.description = value;\n      // Vérifier si la description a au moins 10 caractères\n      this.descriptionError = value.length > 0 && value.length < 10;\n      if (this.descriptionError) {\n        console.warn('La description doit contenir au moins 10 caractères');\n      }\n    }\n    onSubmit() {\n      console.log('Form submitted with:', this.equipe);\n      // Vérifier si le nom est présent et valide\n      if (!this.equipe.name) {\n        this.error = \"Le nom de l'équipe est requis.\";\n        return;\n      }\n      if (this.equipe.name.length < 3) {\n        this.nameError = true;\n        this.error = \"Le nom de l'équipe doit contenir au moins 3 caractères.\";\n        return;\n      }\n      // Vérifier si la description est présente et valide\n      if (!this.equipe.description) {\n        this.error = \"La description de l'équipe est requise.\";\n        return;\n      }\n      if (this.equipe.description.length < 10) {\n        this.descriptionError = true;\n        this.error = \"La description de l'équipe doit contenir au moins 10 caractères.\";\n        return;\n      }\n      // Vérifier si le nom existe déjà avant de soumettre\n      if (this.checkNameExists(this.equipe.name)) {\n        this.nameExists = true;\n        this.error = 'Une équipe avec ce nom existe déjà. Veuillez choisir un autre nom.';\n        return;\n      }\n      this.submitting = true;\n      this.error = null;\n      // Créer une copie de l'objet équipe pour éviter les problèmes de référence\n      const equipeToSave = {\n        name: this.equipe.name,\n        description: this.equipe.description || '',\n        admin: this.equipe.admin\n      };\n      // Ajouter l'ID si nous sommes en mode édition\n      if (this.isEditMode && this.equipeId) {\n        equipeToSave._id = this.equipeId;\n      }\n      console.log('Données à envoyer:', equipeToSave);\n      if (this.isEditMode && this.equipeId) {\n        // Mode édition\n        this.equipeService.updateEquipe(this.equipeId, equipeToSave).subscribe({\n          next: response => {\n            console.log('Équipe mise à jour avec succès:', response);\n            this.submitting = false;\n            this.notificationService.showSuccess(`L'équipe \"${response.name}\" a été mise à jour avec succès.`);\n            this.router.navigate(['/equipes/liste']);\n          },\n          error: error => {\n            console.error(\"Erreur lors de la mise à jour de l'équipe:\", error);\n            this.error = `Impossible de mettre à jour l'équipe: ${error.message}`;\n            this.submitting = false;\n            this.notificationService.showError(`Erreur: ${error.message}`);\n          }\n        });\n      } else {\n        // Mode ajout\n        this.equipeService.addEquipe(equipeToSave).subscribe({\n          next: response => {\n            console.log('Équipe ajoutée avec succès:', response);\n            this.submitting = false;\n            this.notificationService.showSuccess(`L'équipe \"${response.name}\" a été créée avec succès.`);\n            this.router.navigate(['/equipes/liste']);\n          },\n          error: error => {\n            console.error(\"Erreur lors de l'ajout de l'équipe:\", error);\n            this.error = `Impossible d'ajouter l'équipe: ${error.message}`;\n            this.submitting = false;\n            this.notificationService.showError(`Erreur: ${error.message}`);\n          }\n        });\n      }\n    }\n    cancel() {\n      console.log('Form cancelled');\n      this.router.navigate(['/equipes/liste']);\n    }\n    // Méthodes pour gérer les membres\n    addMembreToEquipe(membreId, role = 'membre') {\n      console.log('Début de addMembreToEquipe avec membreId:', membreId, 'et rôle:', role);\n      console.log('État actuel - this.equipeId:', this.equipeId);\n      console.log('État actuel - this.equipe:', this.equipe);\n      // Utiliser this.equipe._id si this.equipeId n'est pas défini\n      const equipeId = this.equipeId || this.equipe && this.equipe._id;\n      console.log('equipeId calculé:', equipeId);\n      if (!equipeId || !membreId) {\n        console.error(\"ID d'équipe ou ID de membre manquant\");\n        this.error = \"ID d'équipe ou ID de membre manquant\";\n        console.log('equipeId:', equipeId, 'membreId:', membreId);\n        // Afficher un message à l'utilisateur\n        this.notificationService.showError(\"Impossible d'ajouter le membre: ID d'équipe ou ID de membre manquant\");\n        return;\n      }\n      // Vérifier si le membre est déjà dans l'équipe\n      if (this.equipe.members && this.equipe.members.includes(membreId)) {\n        this.notificationService.showError(\"Ce membre fait déjà partie de l'équipe\");\n        return;\n      }\n      // Récupérer les informations de l'utilisateur pour afficher un message plus informatif\n      const user = this.availableUsers.find(u => u._id === membreId || u.id === membreId);\n      const userName = user ? user.firstName && user.lastName ? `${user.firstName} ${user.lastName}` : user.name || membreId : membreId;\n      // Créer l'objet membre avec le rôle spécifié\n      const membre = {\n        id: membreId,\n        role: role\n      };\n      this.loading = true;\n      console.log(`Ajout de l'utilisateur \"${userName}\" comme ${role} à l'équipe ${equipeId}`);\n      this.equipeService.addMembreToEquipe(equipeId, membre).subscribe({\n        next: response => {\n          console.log('Membre ajouté avec succès:', response);\n          this.notificationService.showSuccess(`${userName} a été ajouté comme ${role === 'admin' ? 'administrateur' : 'membre'} à l'équipe`);\n          // Recharger l'équipe pour mettre à jour la liste des membres\n          this.loadEquipe(equipeId);\n          this.loading = false;\n        },\n        error: error => {\n          console.error(\"Erreur lors de l'ajout du membre:\", error);\n          this.error = \"Impossible d'ajouter le membre. Veuillez réessayer plus tard.\";\n          this.notificationService.showError(\"Erreur lors de l'ajout du membre: \" + error.message);\n          this.loading = false;\n        }\n      });\n    }\n    // Méthode pour obtenir le nom complet d'un membre à partir de son ID\n    getMembreName(membreId) {\n      // Chercher d'abord dans la liste des utilisateurs\n      const user = this.availableUsers.find(u => u._id === membreId || u.id === membreId);\n      if (user) {\n        if (user.firstName && user.lastName) {\n          return `${user.firstName} ${user.lastName}`;\n        } else if (user.name) {\n          return user.name;\n        }\n      }\n      // Chercher ensuite dans la liste des membres\n      const membre = this.availableMembers.find(m => m._id === membreId || m.id === membreId);\n      if (membre && membre.name) {\n        return membre.name;\n      }\n      // Si aucun nom n'est trouvé, retourner l'ID\n      return membreId;\n    }\n    // Méthode pour obtenir l'email d'un membre\n    getMembreEmail(membreId) {\n      const user = this.availableUsers.find(u => u._id === membreId || u.id === membreId);\n      if (user && user.email) {\n        return user.email;\n      }\n      return 'Non renseigné';\n    }\n    // Méthode pour obtenir la profession d'un membre\n    getMembreProfession(membreId) {\n      const user = this.availableUsers.find(u => u._id === membreId || u.id === membreId);\n      if (user) {\n        if (user.profession) {\n          return user.profession === 'etudiant' ? 'Étudiant' : 'Professeur';\n        } else if (user.role) {\n          return user.role === 'etudiant' ? 'Étudiant' : 'Professeur';\n        }\n      }\n      return 'Non spécifié';\n    }\n    // Méthode pour obtenir le rôle d'un membre dans l'équipe\n    getMembreRole(membreId) {\n      // Cette méthode nécessiterait d'avoir accès aux rôles des membres dans l'équipe\n      // Pour l'instant, nous retournons une valeur par défaut\n      return 'Membre';\n    }\n    removeMembreFromEquipe(membreId) {\n      console.log('Méthode removeMembreFromEquipe appelée avec ID:', membreId);\n      console.log('État actuel - this.equipeId:', this.equipeId);\n      console.log('État actuel - this.equipe:', this.equipe);\n      // Utiliser this.equipe._id si this.equipeId n'est pas défini\n      const equipeId = this.equipeId || this.equipe && this.equipe._id;\n      if (!equipeId) {\n        console.error(\"ID d'équipe manquant\");\n        this.error = \"ID d'équipe manquant. Impossible de retirer le membre.\";\n        this.notificationService.showError(\"ID d'équipe manquant. Impossible de retirer le membre.\");\n        return;\n      }\n      if (!membreId) {\n        console.error('ID de membre manquant');\n        this.error = 'ID de membre manquant. Impossible de retirer le membre.';\n        this.notificationService.showError('ID de membre manquant. Impossible de retirer le membre.');\n        return;\n      }\n      // Obtenir le nom du membre pour l'afficher dans le message de confirmation\n      const membreName = this.getMembreName(membreId);\n      console.log(`Tentative de retrait de l'utilisateur ${membreId} (${membreName}) de l'équipe ${equipeId}`);\n      try {\n        if (confirm(`Êtes-vous sûr de vouloir retirer ${membreName} de l'équipe?`)) {\n          console.log('Confirmation acceptée, suppression en cours...');\n          this.loading = true;\n          this.error = null;\n          // Ajouter un délai pour s'assurer que l'utilisateur voit le chargement\n          setTimeout(() => {\n            this.equipeService.removeMembreFromEquipe(equipeId, membreId).subscribe({\n              next: response => {\n                console.log(`Utilisateur \"${membreName}\" retiré avec succès de l'équipe:`, response);\n                this.loading = false;\n                this.notificationService.showSuccess(`${membreName} a été retiré avec succès de l'équipe`);\n                // Recharger l'équipe pour mettre à jour la liste des membres\n                this.loadEquipe(equipeId);\n              },\n              error: error => {\n                console.error(`Erreur lors du retrait de l'utilisateur \"${membreName}\":`, error);\n                console.error(\"Détails de l'erreur:\", {\n                  status: error.status,\n                  message: error.message,\n                  error: error\n                });\n                this.loading = false;\n                this.error = `Impossible de retirer l'utilisateur \"${membreName}\" de l'équipe: ${error.message || 'Erreur inconnue'}`;\n                this.notificationService.showError(`Erreur lors du retrait du membre: ${this.error}`);\n              }\n            });\n          }, 500);\n        } else {\n          console.log(\"Suppression annulée par l'utilisateur\");\n        }\n      } catch (error) {\n        console.error('Exception lors du retrait du membre:', error);\n        this.error = `Exception: ${error?.message || 'Erreur inconnue'}`;\n        this.notificationService.showError(`Exception: ${this.error}`);\n      }\n    }\n    // Méthode pour supprimer l'équipe\n    deleteEquipe() {\n      console.log('Méthode deleteEquipe appelée dans equipe-form.component.ts');\n      console.log('État actuel - this.equipeId:', this.equipeId);\n      console.log('État actuel - this.equipe:', this.equipe);\n      // Utiliser this.equipe._id si this.equipeId n'est pas défini\n      const equipeId = this.equipeId || this.equipe && this.equipe._id;\n      if (!equipeId) {\n        console.error(\"ID d'équipe manquant\");\n        this.error = \"ID d'équipe manquant. Impossible de supprimer l'équipe.\";\n        this.notificationService.showError(\"ID d'équipe manquant. Impossible de supprimer l'équipe.\");\n        return;\n      }\n      console.log(\"ID de l'équipe à supprimer (final):\", equipeId);\n      try {\n        if (confirm(`Êtes-vous sûr de vouloir supprimer l'équipe \"${this.equipe.name}\"? Cette action est irréversible.`)) {\n          console.log('Confirmation acceptée, suppression en cours...');\n          this.loading = true;\n          this.error = null;\n          // Ajouter un délai pour s'assurer que l'utilisateur voit le chargement\n          setTimeout(() => {\n            this.equipeService.deleteEquipe(equipeId).subscribe({\n              next: response => {\n                console.log('Équipe supprimée avec succès, réponse:', response);\n                this.loading = false;\n                this.notificationService.showSuccess(`L'équipe \"${this.equipe.name}\" a été supprimée avec succès.`);\n                // Ajouter un délai avant la redirection\n                setTimeout(() => {\n                  this.router.navigate(['/equipes/liste']);\n                }, 500);\n              },\n              error: error => {\n                console.error(\"Erreur lors de la suppression de l'équipe:\", error);\n                console.error(\"Détails de l'erreur:\", {\n                  status: error.status,\n                  message: error.message,\n                  error: error\n                });\n                this.loading = false;\n                this.error = `Impossible de supprimer l'équipe: ${error.message || 'Erreur inconnue'}`;\n                this.notificationService.showError(`Erreur lors de la suppression: ${this.error}`);\n              }\n            });\n          }, 500);\n        } else {\n          console.log(\"Suppression annulée par l'utilisateur\");\n        }\n      } catch (error) {\n        console.error('Exception lors de la suppression:', error);\n        this.error = `Exception: ${error?.message || 'Erreur inconnue'}`;\n        this.notificationService.showError(`Exception: ${this.error}`);\n      }\n    }\n    static {\n      this.ɵfac = function EquipeFormComponent_Factory(t) {\n        return new (t || EquipeFormComponent)(i0.ɵɵdirectiveInject(i1.EquipeService), i0.ɵɵdirectiveInject(i2.MembreService), i0.ɵɵdirectiveInject(i3.AuthuserService), i0.ɵɵdirectiveInject(i4.ActivatedRoute), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i5.NotificationService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: EquipeFormComponent,\n        selectors: [[\"app-equipe-form\"]],\n        decls: 17,\n        vars: 5,\n        consts: [[1, \"container-fluid\", \"py-5\", \"bg-light\"], [1, \"container\"], [1, \"row\", \"mb-5\"], [1, \"col-12\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"display-4\", \"fw-bold\", \"text-primary\"], [1, \"text-muted\", \"lead\"], [1, \"btn\", \"btn-outline-primary\", \"rounded-pill\", \"px-4\", \"py-2\", \"shadow-sm\", 3, \"click\"], [1, \"bi\", \"bi-arrow-left\", \"me-2\"], [1, \"my-4\"], [\"class\", \"row justify-content-center my-5\", 4, \"ngIf\"], [\"class\", \"row mb-4\", 4, \"ngIf\"], [\"class\", \"row justify-content-center\", 4, \"ngIf\"], [1, \"row\", \"justify-content-center\", \"my-5\"], [1, \"col-md-6\", \"text-center\"], [\"role\", \"status\", 1, \"spinner-grow\", \"text-primary\", \"mx-1\"], [1, \"visually-hidden\"], [\"role\", \"status\", 1, \"spinner-grow\", \"text-secondary\", \"mx-1\"], [1, \"mt-3\", \"text-muted\"], [1, \"row\", \"mb-4\"], [1, \"alert\", \"alert-danger\", \"shadow-sm\", \"border-0\", \"rounded-3\", \"d-flex\", \"align-items-center\"], [1, \"bi\", \"bi-exclamation-triangle-fill\", \"fs-3\", \"me-3\"], [1, \"flex-grow-1\"], [1, \"row\", \"justify-content-center\"], [1, \"col-lg-8\"], [1, \"card\", \"border-0\", \"shadow-sm\", \"rounded-3\", \"overflow-hidden\"], [1, \"card-header\", \"bg-gradient-primary\", \"text-white\", \"border-0\", \"py-4\"], [1, \"mb-0\"], [1, \"bi\", 3, \"ngClass\"], [1, \"card-body\", \"p-4\"], [1, \"row\", \"g-3\", 3, \"ngSubmit\"], [1, \"col-12\", \"mb-3\"], [\"for\", \"name\", 1, \"form-label\", \"fw-medium\"], [1, \"text-danger\"], [1, \"input-group\"], [1, \"input-group-text\", \"bg-light\", \"border-0\"], [1, \"bi\", \"bi-people-fill\", \"text-primary\"], [\"type\", \"text\", \"id\", \"name\", \"required\", \"\", \"minlength\", \"3\", \"placeholder\", \"Entrez le nom de l'\\u00E9quipe\", 1, \"form-control\", \"bg-light\", \"border-0\", 3, \"value\", \"input\"], [\"nameInput\", \"\"], [\"class\", \"invalid-feedback d-block small mt-1\", 4, \"ngIf\"], [\"class\", \"text-danger small mt-1\", 4, \"ngIf\"], [\"for\", \"description\", 1, \"form-label\", \"fw-medium\"], [1, \"input-group-text\", \"bg-light\", \"border-0\", \"align-self-start\"], [1, \"bi\", \"bi-card-text\", \"text-primary\"], [\"id\", \"description\", \"rows\", \"4\", \"required\", \"\", \"minlength\", \"10\", \"placeholder\", \"D\\u00E9crivez l'objectif et les activit\\u00E9s de cette \\u00E9quipe\", 1, \"form-control\", \"bg-light\", \"border-0\", 3, \"value\", \"input\"], [\"descInput\", \"\"], [\"type\", \"hidden\", 3, \"value\"], [1, \"alert\", \"alert-info\", \"border-0\", \"rounded-3\", \"shadow-sm\", \"d-flex\", \"align-items-center\"], [1, \"bi\", \"bi-info-circle-fill\", \"fs-4\", \"me-3\", \"text-primary\"], [\"class\", \"col-12 mt-4\", 4, \"ngIf\"], [1, \"col-12\", \"mt-4\"], [1, \"d-flex\", \"gap-3\", \"justify-content-between\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-secondary\", \"rounded-pill\", \"px-4\", \"py-2\", 3, \"click\"], [\"type\", \"button\", \"class\", \"btn btn-outline-danger rounded-pill px-4 py-2 ms-2\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"rounded-pill\", \"px-4\", \"py-2\", \"shadow-sm\", 3, \"disabled\"], [\"class\", \"spinner-border spinner-border-sm me-2\", 4, \"ngIf\"], [\"class\", \"bi\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"invalid-feedback\", \"d-block\", \"small\", \"mt-1\"], [1, \"bi\", \"bi-exclamation-triangle-fill\", \"me-1\"], [1, \"text-danger\", \"small\", \"mt-1\"], [1, \"bi\", \"bi-exclamation-circle-fill\", \"me-1\"], [1, \"card\", \"border-0\", \"shadow-sm\", \"rounded-3\", \"mb-4\"], [1, \"card-header\", \"bg-light\", \"border-0\", \"py-3\"], [1, \"mb-0\", \"d-flex\", \"align-items-center\"], [1, \"bi\", \"bi-people-fill\", \"text-primary\", \"me-2\"], [4, \"ngIf\", \"ngIfElse\"], [\"noMembers\", \"\"], [1, \"mt-4\"], [1, \"d-flex\", \"align-items-center\", \"mb-3\"], [1, \"bi\", \"bi-person-plus-fill\", \"text-primary\", \"me-2\"], [\"class\", \"alert alert-info border-0 rounded-3 shadow-sm d-flex align-items-center\", 4, \"ngIf\"], [\"class\", \"card border-0 bg-light rounded-3 mb-3\", 4, \"ngIf\"], [1, \"table-responsive\"], [1, \"table\", \"table-hover\", \"align-middle\"], [1, \"table-light\"], [1, \"d-flex\", \"align-items-center\"], [1, \"bi\", \"bi-person\", \"text-primary\", \"me-2\"], [1, \"bi\", \"bi-envelope\", \"text-primary\", \"me-2\"], [1, \"bi\", \"bi-briefcase\", \"text-primary\", \"me-2\"], [1, \"text-center\"], [\"class\", \"transition hover-row\", 4, \"ngFor\", \"ngForOf\"], [1, \"transition\", \"hover-row\"], [1, \"fw-medium\"], [\"class\", \"text-muted d-block\", 4, \"ngIf\"], [\"class\", \"text-decoration-none\", 3, \"href\", 4, \"ngIf\"], [\"class\", \"text-muted fst-italic\", 4, \"ngIf\"], [1, \"badge\", \"rounded-pill\", \"px-3\", \"py-2\", \"shadow-sm\", 3, \"ngClass\"], [\"type\", \"button\", \"title\", \"Retirer de l'\\u00E9quipe\", 1, \"btn\", \"btn-sm\", \"btn-outline-danger\", \"rounded-circle\", 3, \"click\"], [1, \"bi\", \"bi-trash\"], [1, \"text-muted\", \"d-block\"], [1, \"text-decoration-none\", 3, \"href\"], [1, \"text-muted\", \"fst-italic\"], [1, \"text-center\", \"py-4\"], [1, \"bi\", \"bi-people\", \"fs-1\", \"text-muted\", \"mb-3\", \"d-block\"], [1, \"text-muted\"], [1, \"card\", \"border-0\", \"bg-light\", \"rounded-3\", \"mb-3\"], [1, \"row\", \"g-3\"], [1, \"col-md-6\"], [\"for\", \"userSelect\", 1, \"form-label\", \"fw-medium\"], [\"id\", \"userSelect\", 1, \"form-select\", \"border-0\", \"shadow-sm\"], [\"userSelect\", \"\"], [\"value\", \"\", \"selected\", \"\", \"disabled\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-md-4\"], [\"for\", \"roleSelect\", 1, \"form-label\", \"fw-medium\"], [\"id\", \"roleSelect\", 1, \"form-select\", \"border-0\", \"shadow-sm\"], [\"roleSelect\", \"\"], [\"value\", \"membre\", \"selected\", \"\"], [\"value\", \"admin\"], [1, \"col-md-2\", \"d-flex\", \"align-items-end\"], [\"type\", \"button\", \"id\", \"addMembreButton\", 1, \"btn\", \"btn-primary\", \"rounded-pill\", \"w-100\", \"shadow-sm\", 3, \"disabled\", \"click\"], [1, \"bi\", \"bi-plus-circle\", \"me-1\"], [3, \"value\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-danger\", \"rounded-pill\", \"px-4\", \"py-2\", \"ms-2\", 3, \"click\"], [1, \"bi\", \"bi-trash\", \"me-2\"], [1, \"spinner-border\", \"spinner-border-sm\", \"me-2\"]],\n        template: function EquipeFormComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"div\")(6, \"h1\", 5);\n            i0.ɵɵtext(7);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(8, \"p\", 6);\n            i0.ɵɵtext(9);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(10, \"button\", 7);\n            i0.ɵɵlistener(\"click\", function EquipeFormComponent_Template_button_click_10_listener() {\n              return ctx.cancel();\n            });\n            i0.ɵɵelement(11, \"i\", 8);\n            i0.ɵɵtext(12, \" Retour \\u00E0 la liste \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(13, \"hr\", 9);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(14, EquipeFormComponent_div_14_Template, 13, 0, \"div\", 10);\n            i0.ɵɵtemplate(15, EquipeFormComponent_div_15_Template, 6, 1, \"div\", 11);\n            i0.ɵɵtemplate(16, EquipeFormComponent_div_16_Template, 52, 23, \"div\", 12);\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(7);\n            i0.ɵɵtextInterpolate(ctx.isEditMode ? \"Modifier l'\\u00E9quipe\" : \"Nouvelle \\u00E9quipe\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate1(\" \", ctx.isEditMode ? \"Modifiez les informations et les membres de votre \\u00E9quipe\" : \"Cr\\u00E9ez une nouvelle \\u00E9quipe pour organiser vos projets et membres\", \" \");\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n          }\n        },\n        dependencies: [i6.NgClass, i6.NgForOf, i6.NgIf, i7.ɵNgNoValidate, i7.NgSelectOption, i7.ɵNgSelectMultipleOption, i7.NgControlStatusGroup, i7.NgForm],\n        styles: [\".cursor-pointer[_ngcontent-%COMP%]{cursor:pointer}summary[_ngcontent-%COMP%]:hover{text-decoration:underline}\", \"\\n\\n  .bg-gradient-primary[_ngcontent-%COMP%] {\\n    background: linear-gradient(45deg, #007bff, #6610f2) !important;\\n  }\\n\\n  \\n\\n  .transition[_ngcontent-%COMP%] {\\n    transition: all 0.2s ease;\\n  }\\n\\n  .hover-row[_ngcontent-%COMP%]:hover {\\n    background-color: rgba(13, 110, 253, 0.05) !important;\\n    transform: translateY(-2px);\\n    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);\\n  }\\n\\n  \\n\\n  .form-control[_ngcontent-%COMP%]:focus, .form-select[_ngcontent-%COMP%]:focus {\\n    border-color: #86b7fe;\\n    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);\\n  }\\n\\n  \\n\\n  .btn[_ngcontent-%COMP%] {\\n    transition: all 0.3s ease;\\n  }\\n\\n  .btn[_ngcontent-%COMP%]:hover {\\n    transform: translateY(-2px);\\n  }\"]\n      });\n    }\n  }\n  return EquipeFormComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}