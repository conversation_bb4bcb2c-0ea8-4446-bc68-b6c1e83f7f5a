<div class="container-fluid py-5 bg-light">
  <div class="container">
    <!-- En-tête avec titre et description -->
    <div class="row mb-5">
      <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
          <div>
            <h1 class="display-4 fw-bold text-primary">{{ isEditMode ? 'Modifier l\'équipe' : 'Nouvelle équipe' }}</h1>
            <p class="text-muted lead">
              {{ isEditMode ? 'Modifiez les informations et les membres de votre équipe' : 'Créez une nouvelle équipe pour organiser vos projets et membres' }}
            </p>
          </div>
          <button class="btn btn-outline-primary rounded-pill px-4 py-2 shadow-sm" (click)="cancel()">
            <i class="bi bi-arrow-left me-2"></i> Retour à la liste
          </button>
        </div>
        <hr class="my-4">
      </div>
    </div>

    <!-- Loading message avec animation moderne -->
    <div *ngIf="loading" class="row justify-content-center my-5">
      <div class="col-md-6 text-center">
        <div class="spinner-grow text-primary mx-1" role="status">
          <span class="visually-hidden">Chargement...</span>
        </div>
        <div class="spinner-grow text-secondary mx-1" role="status">
          <span class="visually-hidden">Chargement...</span>
        </div>
        <div class="spinner-grow text-primary mx-1" role="status">
          <span class="visually-hidden">Chargement...</span>
        </div>
        <p class="mt-3 text-muted">Chargement des données...</p>
      </div>
    </div>

    <!-- Error message avec style moderne -->
    <div *ngIf="error" class="row mb-4">
      <div class="col-12">
        <div class="alert alert-danger shadow-sm border-0 rounded-3 d-flex align-items-center">
          <i class="bi bi-exclamation-triangle-fill fs-3 me-3"></i>
          <div class="flex-grow-1">{{ error }}</div>
        </div>
      </div>
    </div>

    <!-- Formulaire avec design moderne -->
    <div class="row justify-content-center" *ngIf="!loading">
      <div class="col-lg-8">
        <div class="card border-0 shadow-sm rounded-3 overflow-hidden">
          <div class="card-header bg-gradient-primary text-white border-0 py-4">
            <h3 class="mb-0">
              <i class="bi" [ngClass]="{'bi-pencil-square': isEditMode, 'bi-plus-circle': !isEditMode}"></i>
              {{ isEditMode ? 'Informations de l\'équipe' : 'Détails de la nouvelle équipe' }}
            </h3>
          </div>

          <div class="card-body p-4">
            <form (ngSubmit)="onSubmit()" class="row g-3">
              <!-- Nom de l'équipe -->
              <div class="col-12 mb-3">
                <label for="name" class="form-label fw-medium">Nom de l'équipe <span class="text-danger">*</span></label>
                <div class="input-group">
                  <span class="input-group-text bg-light border-0">
                    <i class="bi bi-people-fill text-primary"></i>
                  </span>
                  <input
                    #nameInput
                    type="text"
                    class="form-control bg-light border-0"
                    [class.is-invalid]="nameExists || (nameError && nameInput.value.length > 0)"
                    id="name"
                    [value]="equipe.name || ''"
                    (input)="updateName(nameInput.value)"
                    required
                    minlength="3"
                    placeholder="Entrez le nom de l'équipe"
                  >
                </div>
                <div *ngIf="nameExists" class="invalid-feedback d-block small mt-1">
                  <i class="bi bi-exclamation-triangle-fill me-1"></i>
                  Ce nom d'équipe existe déjà. Veuillez en choisir un autre.
                </div>
                <div *ngIf="nameError && nameInput.value.length > 0" class="invalid-feedback d-block small mt-1">
                  <i class="bi bi-exclamation-triangle-fill me-1"></i>
                  Le nom de l'équipe doit contenir au moins 3 caractères.
                </div>
                <div *ngIf="error && !equipe.name" class="text-danger small mt-1">
                  <i class="bi bi-exclamation-circle-fill me-1"></i>
                  Le nom de l'équipe est requis.
                </div>
              </div>

              <!-- Description de l'équipe -->
              <div class="col-12 mb-3">
                <label for="description" class="form-label fw-medium">Description <span class="text-danger">*</span></label>
                <div class="input-group">
                  <span class="input-group-text bg-light border-0 align-self-start">
                    <i class="bi bi-card-text text-primary"></i>
                  </span>
                  <textarea
                    #descInput
                    class="form-control bg-light border-0"
                    id="description"
                    rows="4"
                    [class.is-invalid]="descriptionError && descInput.value.length > 0"
                    [value]="equipe.description || ''"
                    (input)="updateDescription(descInput.value)"
                    required
                    minlength="10"
                    placeholder="Décrivez l'objectif et les activités de cette équipe"
                  ></textarea>
                </div>
                <div *ngIf="descriptionError && descInput.value.length > 0" class="invalid-feedback d-block small mt-1">
                  <i class="bi bi-exclamation-triangle-fill me-1"></i>
                  La description doit contenir au moins 10 caractères.
                </div>
                <div *ngIf="error && !equipe.description" class="text-danger small mt-1">
                  <i class="bi bi-exclamation-circle-fill me-1"></i>
                  La description de l'équipe est requise.
                </div>
              </div>

              <!-- Admin field - hidden for now, using default value -->
              <input type="hidden" [value]="equipe.admin">
              <div class="col-12 mb-3">
                <div class="alert alert-info border-0 rounded-3 shadow-sm d-flex align-items-center">
                  <i class="bi bi-info-circle-fill fs-4 me-3 text-primary"></i>
                  <div>Un administrateur par défaut sera assigné à cette équipe.</div>
                </div>
              </div>

              <!-- Utilisateurs membres de l'équipe (visible uniquement en mode édition) -->
              <div *ngIf="isEditMode && equipe._id" class="col-12 mt-4">
                <div class="card border-0 shadow-sm rounded-3 mb-4">
                  <div class="card-header bg-light border-0 py-3">
                    <h4 class="mb-0 d-flex align-items-center">
                      <i class="bi bi-people-fill text-primary me-2"></i>
                      Membres de l'équipe
                    </h4>
                  </div>

                  <div class="card-body p-4">
                    <!-- Liste des membres actuels -->
                    <div *ngIf="equipe.members && equipe.members.length > 0; else noMembers">
                      <div class="table-responsive">
                        <table class="table table-hover align-middle">
                          <thead class="table-light">
                            <tr>
                              <th>
                                <div class="d-flex align-items-center">
                                  <i class="bi bi-person text-primary me-2"></i> Nom et Prénom
                                </div>
                              </th>
                              <th>
                                <div class="d-flex align-items-center">
                                  <i class="bi bi-envelope text-primary me-2"></i> Email
                                </div>
                              </th>
                              <th>
                                <div class="d-flex align-items-center">
                                  <i class="bi bi-briefcase text-primary me-2"></i> Statut
                                </div>
                              </th>
                              <th class="text-center">Actions</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr *ngFor="let membreId of equipe.members" class="transition hover-row">
                              <td>
                                <span class="fw-medium">{{ getMembreName(membreId) }}</span>
                                <small class="text-muted d-block" *ngIf="getMembreName(membreId) !== membreId">ID: {{ membreId }}</small>
                              </td>
                              <td>
                                <a *ngIf="getMembreEmail(membreId) !== 'Non renseigné'" href="mailto:{{ getMembreEmail(membreId) }}" class="text-decoration-none">
                                  {{ getMembreEmail(membreId) }}
                                </a>
                                <span *ngIf="getMembreEmail(membreId) === 'Non renseigné'" class="text-muted fst-italic">Non renseigné</span>
                              </td>
                              <td>
                                <span class="badge rounded-pill px-3 py-2 shadow-sm" [ngClass]="{
                                  'bg-primary': getMembreProfession(membreId) === 'Étudiant',
                                  'bg-success': getMembreProfession(membreId) === 'Professeur',
                                  'bg-secondary': getMembreProfession(membreId) === 'Non spécifié'
                                }">
                                  <i class="bi" [ngClass]="{
                                    'bi-mortarboard-fill': getMembreProfession(membreId) === 'Étudiant',
                                    'bi-briefcase-fill': getMembreProfession(membreId) === 'Professeur',
                                    'bi-question-circle-fill': getMembreProfession(membreId) === 'Non spécifié'
                                  }"></i>
                                  {{ getMembreProfession(membreId) }}
                                </span>
                              </td>
                              <td class="text-center">
                                <button type="button" class="btn btn-sm btn-outline-danger rounded-circle"
                                        title="Retirer de l'équipe"
                                        (click)="removeMembreFromEquipe(membreId)">
                                  <i class="bi bi-trash"></i>
                                </button>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </div>

                    <ng-template #noMembers>
                      <div class="text-center py-4">
                        <i class="bi bi-people fs-1 text-muted mb-3 d-block"></i>
                        <h5 class="text-muted">Aucun membre dans cette équipe</h5>
                        <p class="text-muted">Ajoutez des membres à l'équipe en utilisant le formulaire ci-dessous.</p>
                      </div>
                    </ng-template>

                    <!-- Formulaire pour ajouter un utilisateur comme membre -->
                    <div class="mt-4">
                      <h5 class="d-flex align-items-center mb-3">
                        <i class="bi bi-person-plus-fill text-primary me-2"></i>
                        Ajouter un membre
                      </h5>

                      <!-- Afficher un message si aucun utilisateur n'est disponible -->
                      <div *ngIf="availableUsers.length === 0" class="alert alert-info border-0 rounded-3 shadow-sm d-flex align-items-center">
                        <i class="bi bi-info-circle-fill fs-4 me-3 text-primary"></i>
                        <div>Aucun utilisateur disponible. Veuillez d'abord créer des utilisateurs.</div>
                      </div>

                      <!-- Formulaire d'ajout d'utilisateur avec rôle -->
                      <div *ngIf="availableUsers.length > 0" class="card border-0 bg-light rounded-3 mb-3">
                        <div class="card-body p-4">
                          <div class="row g-3">
                            <!-- Sélection de l'utilisateur -->
                            <div class="col-md-6">
                              <label for="userSelect" class="form-label fw-medium">Utilisateur</label>
                              <select #userSelect id="userSelect" class="form-select border-0 shadow-sm">
                                <option value="" selected disabled>Sélectionnez un utilisateur</option>
                                <option *ngFor="let user of availableUsers" [value]="user._id || user.id">
                                  {{ user.firstName || '' }} {{ user.lastName || user.name || user.id }}
                                  {{ user.email ? '- ' + user.email : '' }}
                                  {{ user.profession ? '(' + (user.profession === 'etudiant' ? 'Étudiant' : 'Professeur') + ')' :
                                     user.role ? '(' + (user.role === 'etudiant' ? 'Étudiant' : 'Professeur') + ')' : '' }}
                                </option>
                              </select>
                            </div>

                            <!-- Sélection du rôle dans l'équipe -->
                            <div class="col-md-4">
                              <label for="roleSelect" class="form-label fw-medium">Rôle dans l'équipe</label>
                              <select #roleSelect id="roleSelect" class="form-select border-0 shadow-sm">
                                <option value="membre" selected>Membre</option>
                                <option value="admin">Administrateur</option>
                              </select>
                            </div>

                            <!-- Bouton d'ajout -->
                            <div class="col-md-2 d-flex align-items-end">
                              <button type="button" class="btn btn-primary rounded-pill w-100 shadow-sm"
                                      [disabled]="!userSelect.value"
                                      (click)="addMembreToEquipe(userSelect.value, roleSelect.value); userSelect.value = ''"
                                      id="addMembreButton">
                                <i class="bi bi-plus-circle me-1"></i> Ajouter
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Boutons d'action -->
              <div class="col-12 mt-4">
                <div class="d-flex gap-3 justify-content-between">
                  <div>
                    <button
                      type="button"
                      class="btn btn-outline-secondary rounded-pill px-4 py-2"
                      (click)="cancel()"
                    >
                      <i class="bi bi-arrow-left me-2"></i>
                      Retour
                    </button>

                    <!-- Bouton de suppression (visible uniquement en mode édition) -->
                    <button
                      *ngIf="isEditMode && equipeId"
                      type="button"
                      class="btn btn-outline-danger rounded-pill px-4 py-2 ms-2"
                      (click)="deleteEquipe()"
                    >
                      <i class="bi bi-trash me-2"></i>
                      Supprimer
                    </button>
                  </div>

                  <button
                    type="submit"
                    class="btn btn-primary rounded-pill px-4 py-2 shadow-sm"
                    [disabled]="submitting || !equipe.name || !equipe.description || nameExists || nameError || descriptionError"
                  >
                    <span *ngIf="submitting" class="spinner-border spinner-border-sm me-2"></span>
                    <i *ngIf="!submitting" class="bi" [ngClass]="{'bi-save': isEditMode, 'bi-plus-circle': !isEditMode}"></i>
                    {{ isEditMode ? 'Mettre à jour' : 'Créer l\'équipe' }}
                  </button>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Styles spécifiques pour cette page -->
<style>
  /* Fond dégradé pour l'en-tête du formulaire */
  .bg-gradient-primary {
    background: linear-gradient(45deg, #007bff, #6610f2) !important;
  }

  /* Animation au survol des lignes du tableau */
  .transition {
    transition: all 0.2s ease;
  }

  .hover-row:hover {
    background-color: rgba(13, 110, 253, 0.05) !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
  }

  /* Style pour les inputs */
  .form-control:focus, .form-select:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
  }

  /* Animation pour les boutons */
  .btn {
    transition: all 0.3s ease;
  }

  .btn:hover {
    transform: translateY(-2px);
  }
</style>