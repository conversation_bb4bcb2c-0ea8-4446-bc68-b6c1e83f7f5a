{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nexport let EquipeFormComponent = class EquipeFormComponent {\n  constructor(equipeService, membreService, userService, route, router, notificationService) {\n    this.equipeService = equipeService;\n    this.membreService = membreService;\n    this.userService = userService;\n    this.route = route;\n    this.router = router;\n    this.notificationService = notificationService;\n    this.equipe = {\n      name: '',\n      description: '',\n      admin: '65f1e5b3a1d8f3c8c0f9e8d7' // ID temporaire\n    };\n\n    this.isEditMode = false;\n    this.loading = false;\n    this.submitting = false;\n    this.error = null;\n    this.equipeId = null;\n    this.nameExists = false;\n    this.nameError = false;\n    this.descriptionError = false;\n    this.checkingName = false;\n    this.existingEquipes = [];\n    this.availableMembers = []; // Liste des membres disponibles\n    this.availableUsers = []; // Liste des utilisateurs disponibles\n  }\n\n  ngOnInit() {\n    console.log('EquipeFormComponent initialized');\n    // Ensure equipe is always defined with admin\n    if (!this.equipe) {\n      this.equipe = {\n        name: '',\n        description: '',\n        admin: '65f1e5b3a1d8f3c8c0f9e8d7' // ID temporaire\n      };\n    }\n    // Charger toutes les équipes pour vérifier les noms existants\n    this.loadAllEquipes();\n    // Charger tous les membres disponibles\n    this.loadAllMembers();\n    // Charger tous les utilisateurs disponibles\n    this.loadAllUsers();\n    try {\n      // Vérifier si nous sommes en mode édition (si un ID est présent dans l'URL)\n      this.equipeId = this.route.snapshot.paramMap.get('id');\n      this.isEditMode = !!this.equipeId;\n      console.log('Edit mode:', this.isEditMode, 'ID:', this.equipeId);\n      if (this.isEditMode && this.equipeId) {\n        this.loadEquipe(this.equipeId);\n        // Ajouter un délai pour s'assurer que l'équipe est chargée\n        setTimeout(() => {\n          console.log('Après délai - this.equipeId:', this.equipeId);\n          console.log('Après délai - this.equipe:', this.equipe);\n        }, 1000);\n      }\n    } catch (error) {\n      console.error('Error in ngOnInit:', error);\n      this.error = \"Erreur d'initialisation\";\n    }\n    // Ajouter un gestionnaire d'événements pour le bouton d'ajout de membre\n    setTimeout(() => {\n      const addButton = document.getElementById('addMembreButton');\n      if (addButton) {\n        console.log(\"Bouton d'ajout de membre trouvé\");\n        addButton.addEventListener('click', () => {\n          console.log(\"Bouton d'ajout de membre cliqué\");\n        });\n      } else {\n        console.log(\"Bouton d'ajout de membre non trouvé\");\n      }\n    }, 2000);\n  }\n  loadAllMembers() {\n    this.membreService.getMembres().subscribe({\n      next: membres => {\n        this.availableMembers = membres;\n        console.log('Membres disponibles chargés:', membres);\n      },\n      error: error => {\n        console.error('Erreur lors du chargement des membres:', error);\n        this.error = 'Impossible de charger la liste des membres. Veuillez réessayer plus tard.';\n      }\n    });\n  }\n  loadAllUsers() {\n    this.userService.getUsers().subscribe({\n      next: users => {\n        this.availableUsers = users;\n        console.log('Utilisateurs disponibles chargés:', users);\n      },\n      error: error => {\n        console.error('Erreur lors du chargement des utilisateurs:', error);\n        this.error = 'Impossible de charger la liste des utilisateurs. Veuillez réessayer plus tard.';\n      }\n    });\n  }\n  loadAllEquipes() {\n    this.equipeService.getEquipes().subscribe({\n      next: equipes => {\n        this.existingEquipes = equipes;\n        console.log('Équipes existantes chargées:', equipes);\n      },\n      error: error => {\n        console.error('Erreur lors du chargement des équipes:', error);\n      }\n    });\n  }\n  loadEquipe(id) {\n    console.log('Loading equipe with ID:', id);\n    this.loading = true;\n    this.error = null;\n    this.equipeService.getEquipe(id).subscribe({\n      next: data => {\n        console.log('Équipe chargée:', data);\n        this.equipe = data;\n        // Vérifier que l'ID est correctement défini\n        console.log(\"ID de l'équipe après chargement:\", this.equipe._id);\n        console.log('this.equipeId:', this.equipeId);\n        // Si l'équipe a des membres, récupérer les informations de chaque membre\n        if (this.equipe.members && this.equipe.members.length > 0) {\n          this.loadMembersDetails();\n        }\n        this.loading = false;\n      },\n      error: error => {\n        console.error(\"Erreur lors du chargement de l'équipe:\", error);\n        this.error = \"Impossible de charger les détails de l'équipe. Veuillez réessayer plus tard.\";\n        this.loading = false;\n      }\n    });\n  }\n  // Méthode pour récupérer les détails des membres de l'équipe\n  loadMembersDetails() {\n    if (!this.equipe.members || this.equipe.members.length === 0) {\n      return;\n    }\n    console.log(\"Chargement des détails des membres de l'équipe...\");\n    // Pour chaque membre de l'équipe, essayer de trouver ses informations dans la liste des utilisateurs\n    this.equipe.members.forEach(membreId => {\n      // Chercher d'abord dans la liste des utilisateurs\n      const user = this.availableUsers.find(u => u._id === membreId || u.id === membreId);\n      if (user) {\n        console.log(`Membre ${membreId} trouvé dans la liste des utilisateurs:`, user);\n        // Vérifier si toutes les informations nécessaires sont présentes\n        if (!user.email || !user.profession && !user.role) {\n          // Si des informations manquent, essayer de les récupérer depuis l'API\n          this.userService.getUser(membreId).subscribe({\n            next: userData => {\n              console.log(`Détails supplémentaires de l'utilisateur ${membreId} récupérés:`, userData);\n              // Mettre à jour l'utilisateur dans la liste avec les nouvelles informations\n              const index = this.availableUsers.findIndex(u => u._id === membreId || u.id === membreId);\n              if (index !== -1) {\n                this.availableUsers[index] = {\n                  ...this.availableUsers[index],\n                  ...userData\n                };\n              }\n            },\n            error: error => {\n              console.error(`Erreur lors de la récupération des détails supplémentaires de l'utilisateur ${membreId}:`, error);\n            }\n          });\n        }\n      } else {\n        // Si non trouvé, essayer de récupérer l'utilisateur depuis l'API\n        this.userService.getUser(membreId).subscribe({\n          next: userData => {\n            console.log(`Détails de l'utilisateur ${membreId} récupérés:`, userData);\n            // Ajouter l'utilisateur à la liste des utilisateurs disponibles s'il n'y est pas déjà\n            if (!this.availableUsers.some(u => u._id === userData._id || u.id === userData.id)) {\n              this.availableUsers.push(userData);\n            }\n          },\n          error: error => {\n            console.error(`Erreur lors de la récupération des détails de l'utilisateur ${membreId}:`, error);\n          }\n        });\n      }\n    });\n  }\n  checkNameExists(name) {\n    // En mode édition, ignorer l'équipe actuelle\n    if (this.isEditMode && this.equipeId) {\n      return this.existingEquipes.some(e => e.name === name && e._id !== this.equipeId);\n    }\n    // En mode création, vérifier tous les noms\n    return this.existingEquipes.some(e => e.name === name);\n  }\n  updateName(value) {\n    console.log('Name updated:', value);\n    this.equipe.name = value;\n    // Vérifier si le nom existe déjà\n    this.nameExists = this.checkNameExists(value);\n    if (this.nameExists) {\n      console.warn(\"Ce nom d'équipe existe déjà\");\n    }\n    // Vérifier si le nom a au moins 3 caractères\n    this.nameError = value.length > 0 && value.length < 3;\n    if (this.nameError) {\n      console.warn('Le nom doit contenir au moins 3 caractères');\n    }\n  }\n  updateDescription(value) {\n    console.log('Description updated:', value);\n    this.equipe.description = value;\n    // Vérifier si la description a au moins 10 caractères\n    this.descriptionError = value.length > 0 && value.length < 10;\n    if (this.descriptionError) {\n      console.warn('La description doit contenir au moins 10 caractères');\n    }\n  }\n  onSubmit() {\n    console.log('Form submitted with:', this.equipe);\n    // Vérifier si le nom est présent et valide\n    if (!this.equipe.name) {\n      this.error = \"Le nom de l'équipe est requis.\";\n      return;\n    }\n    if (this.equipe.name.length < 3) {\n      this.nameError = true;\n      this.error = \"Le nom de l'équipe doit contenir au moins 3 caractères.\";\n      return;\n    }\n    // Vérifier si la description est présente et valide\n    if (!this.equipe.description) {\n      this.error = \"La description de l'équipe est requise.\";\n      return;\n    }\n    if (this.equipe.description.length < 10) {\n      this.descriptionError = true;\n      this.error = \"La description de l'équipe doit contenir au moins 10 caractères.\";\n      return;\n    }\n    // Vérifier si le nom existe déjà avant de soumettre\n    if (this.checkNameExists(this.equipe.name)) {\n      this.nameExists = true;\n      this.error = 'Une équipe avec ce nom existe déjà. Veuillez choisir un autre nom.';\n      return;\n    }\n    this.submitting = true;\n    this.error = null;\n    // Créer une copie de l'objet équipe pour éviter les problèmes de référence\n    const equipeToSave = {\n      name: this.equipe.name,\n      description: this.equipe.description || '',\n      admin: this.equipe.admin\n    };\n    // Ajouter l'ID si nous sommes en mode édition\n    if (this.isEditMode && this.equipeId) {\n      equipeToSave._id = this.equipeId;\n    }\n    console.log('Données à envoyer:', equipeToSave);\n    if (this.isEditMode && this.equipeId) {\n      // Mode édition\n      this.equipeService.updateEquipe(this.equipeId, equipeToSave).subscribe({\n        next: response => {\n          console.log('Équipe mise à jour avec succès:', response);\n          this.submitting = false;\n          this.notificationService.showSuccess(`L'équipe \"${response.name}\" a été mise à jour avec succès.`);\n          this.router.navigate(['/equipes/liste']);\n        },\n        error: error => {\n          console.error(\"Erreur lors de la mise à jour de l'équipe:\", error);\n          this.error = `Impossible de mettre à jour l'équipe: ${error.message}`;\n          this.submitting = false;\n          this.notificationService.showError(`Erreur: ${error.message}`);\n        }\n      });\n    } else {\n      // Mode ajout\n      this.equipeService.addEquipe(equipeToSave).subscribe({\n        next: response => {\n          console.log('Équipe ajoutée avec succès:', response);\n          this.submitting = false;\n          this.notificationService.showSuccess(`L'équipe \"${response.name}\" a été créée avec succès.`);\n          this.router.navigate(['/equipes/liste']);\n        },\n        error: error => {\n          console.error(\"Erreur lors de l'ajout de l'équipe:\", error);\n          this.error = `Impossible d'ajouter l'équipe: ${error.message}`;\n          this.submitting = false;\n          this.notificationService.showError(`Erreur: ${error.message}`);\n        }\n      });\n    }\n  }\n  cancel() {\n    console.log('Form cancelled');\n    this.router.navigate(['/equipes/liste']);\n  }\n  // Méthodes pour gérer les membres\n  addMembreToEquipe(membreId, role = 'membre') {\n    console.log('Début de addMembreToEquipe avec membreId:', membreId, 'et rôle:', role);\n    console.log('État actuel - this.equipeId:', this.equipeId);\n    console.log('État actuel - this.equipe:', this.equipe);\n    // Utiliser this.equipe._id si this.equipeId n'est pas défini\n    const equipeId = this.equipeId || this.equipe && this.equipe._id;\n    console.log('equipeId calculé:', equipeId);\n    if (!equipeId || !membreId) {\n      console.error(\"ID d'équipe ou ID de membre manquant\");\n      this.error = \"ID d'équipe ou ID de membre manquant\";\n      console.log('equipeId:', equipeId, 'membreId:', membreId);\n      // Afficher un message à l'utilisateur\n      this.notificationService.showError(\"Impossible d'ajouter le membre: ID d'équipe ou ID de membre manquant\");\n      return;\n    }\n    // Vérifier si le membre est déjà dans l'équipe\n    if (this.equipe.members && this.equipe.members.includes(membreId)) {\n      this.notificationService.showError(\"Ce membre fait déjà partie de l'équipe\");\n      return;\n    }\n    // Récupérer les informations de l'utilisateur pour afficher un message plus informatif\n    const user = this.availableUsers.find(u => u._id === membreId || u.id === membreId);\n    const userName = user ? user.firstName && user.lastName ? `${user.firstName} ${user.lastName}` : user.name || membreId : membreId;\n    // Créer l'objet membre avec le rôle spécifié\n    const membre = {\n      id: membreId,\n      role: role\n    };\n    this.loading = true;\n    console.log(`Ajout de l'utilisateur \"${userName}\" comme ${role} à l'équipe ${equipeId}`);\n    this.equipeService.addMembreToEquipe(equipeId, membre).subscribe({\n      next: response => {\n        console.log('Membre ajouté avec succès:', response);\n        this.notificationService.showSuccess(`${userName} a été ajouté comme ${role === 'admin' ? 'administrateur' : 'membre'} à l'équipe`);\n        // Recharger l'équipe pour mettre à jour la liste des membres\n        this.loadEquipe(equipeId);\n        this.loading = false;\n      },\n      error: error => {\n        console.error(\"Erreur lors de l'ajout du membre:\", error);\n        this.error = \"Impossible d'ajouter le membre. Veuillez réessayer plus tard.\";\n        this.notificationService.showError(\"Erreur lors de l'ajout du membre: \" + error.message);\n        this.loading = false;\n      }\n    });\n  }\n  // Méthode pour obtenir le nom complet d'un membre à partir de son ID\n  getMembreName(membreId) {\n    // Chercher d'abord dans la liste des utilisateurs\n    const user = this.availableUsers.find(u => u._id === membreId || u.id === membreId);\n    if (user) {\n      if (user.firstName && user.lastName) {\n        return `${user.firstName} ${user.lastName}`;\n      } else if (user.name) {\n        return user.name;\n      }\n    }\n    // Chercher ensuite dans la liste des membres\n    const membre = this.availableMembers.find(m => m._id === membreId || m.id === membreId);\n    if (membre && membre.name) {\n      return membre.name;\n    }\n    // Si aucun nom n'est trouvé, retourner l'ID\n    return membreId;\n  }\n  // Méthode pour obtenir l'email d'un membre\n  getMembreEmail(membreId) {\n    const user = this.availableUsers.find(u => u._id === membreId || u.id === membreId);\n    if (user && user.email) {\n      return user.email;\n    }\n    return 'Non renseigné';\n  }\n  // Méthode pour obtenir la profession d'un membre\n  getMembreProfession(membreId) {\n    const user = this.availableUsers.find(u => u._id === membreId || u.id === membreId);\n    if (user) {\n      if (user.profession) {\n        return user.profession === 'etudiant' ? 'Étudiant' : 'Professeur';\n      } else if (user.role) {\n        return user.role === 'etudiant' ? 'Étudiant' : 'Professeur';\n      }\n    }\n    return 'Non spécifié';\n  }\n  // Méthode pour obtenir le rôle d'un membre dans l'équipe\n  getMembreRole(membreId) {\n    // Cette méthode nécessiterait d'avoir accès aux rôles des membres dans l'équipe\n    // Pour l'instant, nous retournons une valeur par défaut\n    return 'Membre';\n  }\n  removeMembreFromEquipe(membreId) {\n    console.log('Méthode removeMembreFromEquipe appelée avec ID:', membreId);\n    console.log('État actuel - this.equipeId:', this.equipeId);\n    console.log('État actuel - this.equipe:', this.equipe);\n    // Utiliser this.equipe._id si this.equipeId n'est pas défini\n    const equipeId = this.equipeId || this.equipe && this.equipe._id;\n    if (!equipeId) {\n      console.error(\"ID d'équipe manquant\");\n      this.error = \"ID d'équipe manquant. Impossible de retirer le membre.\";\n      this.notificationService.showError(\"ID d'équipe manquant. Impossible de retirer le membre.\");\n      return;\n    }\n    if (!membreId) {\n      console.error('ID de membre manquant');\n      this.error = 'ID de membre manquant. Impossible de retirer le membre.';\n      this.notificationService.showError('ID de membre manquant. Impossible de retirer le membre.');\n      return;\n    }\n    // Obtenir le nom du membre pour l'afficher dans le message de confirmation\n    const membreName = this.getMembreName(membreId);\n    console.log(`Tentative de retrait de l'utilisateur ${membreId} (${membreName}) de l'équipe ${equipeId}`);\n    try {\n      if (confirm(`Êtes-vous sûr de vouloir retirer ${membreName} de l'équipe?`)) {\n        console.log('Confirmation acceptée, suppression en cours...');\n        this.loading = true;\n        this.error = null;\n        // Ajouter un délai pour s'assurer que l'utilisateur voit le chargement\n        setTimeout(() => {\n          this.equipeService.removeMembreFromEquipe(equipeId, membreId).subscribe({\n            next: response => {\n              console.log(`Utilisateur \"${membreName}\" retiré avec succès de l'équipe:`, response);\n              this.loading = false;\n              this.notificationService.showSuccess(`${membreName} a été retiré avec succès de l'équipe`);\n              // Recharger l'équipe pour mettre à jour la liste des membres\n              this.loadEquipe(equipeId);\n            },\n            error: error => {\n              console.error(`Erreur lors du retrait de l'utilisateur \"${membreName}\":`, error);\n              console.error(\"Détails de l'erreur:\", {\n                status: error.status,\n                message: error.message,\n                error: error\n              });\n              this.loading = false;\n              this.error = `Impossible de retirer l'utilisateur \"${membreName}\" de l'équipe: ${error.message || 'Erreur inconnue'}`;\n              this.notificationService.showError(`Erreur lors du retrait du membre: ${this.error}`);\n            }\n          });\n        }, 500);\n      } else {\n        console.log(\"Suppression annulée par l'utilisateur\");\n      }\n    } catch (error) {\n      console.error('Exception lors du retrait du membre:', error);\n      this.error = `Exception: ${error?.message || 'Erreur inconnue'}`;\n      this.notificationService.showError(`Exception: ${this.error}`);\n    }\n  }\n  // Méthode pour supprimer l'équipe\n  deleteEquipe() {\n    console.log('Méthode deleteEquipe appelée dans equipe-form.component.ts');\n    console.log('État actuel - this.equipeId:', this.equipeId);\n    console.log('État actuel - this.equipe:', this.equipe);\n    // Utiliser this.equipe._id si this.equipeId n'est pas défini\n    const equipeId = this.equipeId || this.equipe && this.equipe._id;\n    if (!equipeId) {\n      console.error(\"ID d'équipe manquant\");\n      this.error = \"ID d'équipe manquant. Impossible de supprimer l'équipe.\";\n      this.notificationService.showError(\"ID d'équipe manquant. Impossible de supprimer l'équipe.\");\n      return;\n    }\n    console.log(\"ID de l'équipe à supprimer (final):\", equipeId);\n    try {\n      if (confirm(`Êtes-vous sûr de vouloir supprimer l'équipe \"${this.equipe.name}\"? Cette action est irréversible.`)) {\n        console.log('Confirmation acceptée, suppression en cours...');\n        this.loading = true;\n        this.error = null;\n        // Ajouter un délai pour s'assurer que l'utilisateur voit le chargement\n        setTimeout(() => {\n          this.equipeService.deleteEquipe(equipeId).subscribe({\n            next: response => {\n              console.log('Équipe supprimée avec succès, réponse:', response);\n              this.loading = false;\n              this.notificationService.showSuccess(`L'équipe \"${this.equipe.name}\" a été supprimée avec succès.`);\n              // Ajouter un délai avant la redirection\n              setTimeout(() => {\n                this.router.navigate(['/equipes/liste']);\n              }, 500);\n            },\n            error: error => {\n              console.error(\"Erreur lors de la suppression de l'équipe:\", error);\n              console.error(\"Détails de l'erreur:\", {\n                status: error.status,\n                message: error.message,\n                error: error\n              });\n              this.loading = false;\n              this.error = `Impossible de supprimer l'équipe: ${error.message || 'Erreur inconnue'}`;\n              this.notificationService.showError(`Erreur lors de la suppression: ${this.error}`);\n            }\n          });\n        }, 500);\n      } else {\n        console.log(\"Suppression annulée par l'utilisateur\");\n      }\n    } catch (error) {\n      console.error('Exception lors de la suppression:', error);\n      this.error = `Exception: ${error?.message || 'Erreur inconnue'}`;\n      this.notificationService.showError(`Exception: ${this.error}`);\n    }\n  }\n};\nEquipeFormComponent = __decorate([Component({\n  selector: 'app-equipe-form',\n  templateUrl: './equipe-form.component.html',\n  styleUrls: ['./equipe-form.component.css']\n})], EquipeFormComponent);", "map": {"version": 3, "names": ["Component", "EquipeFormComponent", "constructor", "equipeService", "membreService", "userService", "route", "router", "notificationService", "equipe", "name", "description", "admin", "isEditMode", "loading", "submitting", "error", "equipeId", "nameExists", "nameError", "descriptionError", "checkingName", "existingEquipes", "availableMembers", "availableUsers", "ngOnInit", "console", "log", "loadAllEquipes", "loadAllMembers", "loadAllUsers", "snapshot", "paramMap", "get", "loadEquipe", "setTimeout", "addButton", "document", "getElementById", "addEventListener", "getMembres", "subscribe", "next", "membres", "getUsers", "users", "getEquipes", "equipes", "id", "getEquipe", "data", "_id", "members", "length", "loadMembersDetails", "for<PERSON>ach", "membreId", "user", "find", "u", "email", "profession", "role", "getUser", "userData", "index", "findIndex", "some", "push", "checkNameExists", "e", "updateName", "value", "warn", "updateDescription", "onSubmit", "equipeToSave", "updateEquipe", "response", "showSuccess", "navigate", "message", "showError", "addEquipe", "cancel", "addMembreToEquipe", "includes", "userName", "firstName", "lastName", "membre", "getMembreName", "m", "getMembreEmail", "getMembreProfession", "getMembreRole", "removeMembreFromEquipe", "membreName", "confirm", "status", "deleteEquipe", "__decorate", "selector", "templateUrl", "styleUrls"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\equipes\\equipe-form\\equipe-form.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { EquipeService } from 'src/app/services/equipe.service';\nimport { MembreService } from 'src/app/services/membre.service';\nimport { AuthuserService } from 'src/app/services/authuser.service';\nimport { NotificationService } from 'src/app/services/notification.service';\nimport { Equipe } from 'src/app/models/equipe.model';\nimport { Membre } from 'src/app/models/membre.model';\nimport { User } from 'src/app/models/user.model';\nimport {\n  debounceTime,\n  distinctUntilChanged,\n  switchMap,\n  catchError,\n  of,\n} from 'rxjs';\n\n@Component({\n  selector: 'app-equipe-form',\n  templateUrl: './equipe-form.component.html',\n  styleUrls: ['./equipe-form.component.css'],\n})\nexport class EquipeFormComponent implements OnInit {\n  equipe: Equipe = {\n    name: '',\n    description: '',\n    admin: '65f1e5b3a1d8f3c8c0f9e8d7', // ID temporaire\n  };\n  isEditMode = false;\n  loading = false;\n  submitting = false;\n  error: string | null = null;\n  equipeId: string | null = null;\n  nameExists = false;\n  nameError = false;\n  descriptionError = false;\n  checkingName = false;\n  existingEquipes: Equipe[] = [];\n  availableMembers: Membre[] = []; // Liste des membres disponibles\n  availableUsers: User[] = []; // Liste des utilisateurs disponibles\n\n  constructor(\n    private equipeService: EquipeService,\n    private membreService: MembreService,\n    private userService: AuthService,\n    private route: ActivatedRoute,\n    private router: Router,\n    private notificationService: NotificationService\n  ) {}\n\n  ngOnInit(): void {\n    console.log('EquipeFormComponent initialized');\n\n    // Ensure equipe is always defined with admin\n    if (!this.equipe) {\n      this.equipe = {\n        name: '',\n        description: '',\n        admin: '65f1e5b3a1d8f3c8c0f9e8d7', // ID temporaire\n      };\n    }\n\n    // Charger toutes les équipes pour vérifier les noms existants\n    this.loadAllEquipes();\n\n    // Charger tous les membres disponibles\n    this.loadAllMembers();\n\n    // Charger tous les utilisateurs disponibles\n    this.loadAllUsers();\n\n    try {\n      // Vérifier si nous sommes en mode édition (si un ID est présent dans l'URL)\n      this.equipeId = this.route.snapshot.paramMap.get('id');\n      this.isEditMode = !!this.equipeId;\n      console.log('Edit mode:', this.isEditMode, 'ID:', this.equipeId);\n\n      if (this.isEditMode && this.equipeId) {\n        this.loadEquipe(this.equipeId);\n\n        // Ajouter un délai pour s'assurer que l'équipe est chargée\n        setTimeout(() => {\n          console.log('Après délai - this.equipeId:', this.equipeId);\n          console.log('Après délai - this.equipe:', this.equipe);\n        }, 1000);\n      }\n    } catch (error) {\n      console.error('Error in ngOnInit:', error);\n      this.error = \"Erreur d'initialisation\";\n    }\n\n    // Ajouter un gestionnaire d'événements pour le bouton d'ajout de membre\n    setTimeout(() => {\n      const addButton = document.getElementById('addMembreButton');\n      if (addButton) {\n        console.log(\"Bouton d'ajout de membre trouvé\");\n        addButton.addEventListener('click', () => {\n          console.log(\"Bouton d'ajout de membre cliqué\");\n        });\n      } else {\n        console.log(\"Bouton d'ajout de membre non trouvé\");\n      }\n    }, 2000);\n  }\n\n  loadAllMembers(): void {\n    this.membreService.getMembres().subscribe({\n      next: (membres) => {\n        this.availableMembers = membres;\n        console.log('Membres disponibles chargés:', membres);\n      },\n      error: (error) => {\n        console.error('Erreur lors du chargement des membres:', error);\n        this.error =\n          'Impossible de charger la liste des membres. Veuillez réessayer plus tard.';\n      },\n    });\n  }\n\n  loadAllUsers(): void {\n    this.userService.getUsers().subscribe({\n      next: (users) => {\n        this.availableUsers = users;\n        console.log('Utilisateurs disponibles chargés:', users);\n      },\n      error: (error) => {\n        console.error('Erreur lors du chargement des utilisateurs:', error);\n        this.error =\n          'Impossible de charger la liste des utilisateurs. Veuillez réessayer plus tard.';\n      },\n    });\n  }\n\n  loadAllEquipes(): void {\n    this.equipeService.getEquipes().subscribe({\n      next: (equipes) => {\n        this.existingEquipes = equipes;\n        console.log('Équipes existantes chargées:', equipes);\n      },\n      error: (error) => {\n        console.error('Erreur lors du chargement des équipes:', error);\n      },\n    });\n  }\n\n  loadEquipe(id: string): void {\n    console.log('Loading equipe with ID:', id);\n    this.loading = true;\n    this.error = null;\n\n    this.equipeService.getEquipe(id).subscribe({\n      next: (data) => {\n        console.log('Équipe chargée:', data);\n        this.equipe = data;\n\n        // Vérifier que l'ID est correctement défini\n        console.log(\"ID de l'équipe après chargement:\", this.equipe._id);\n        console.log('this.equipeId:', this.equipeId);\n\n        // Si l'équipe a des membres, récupérer les informations de chaque membre\n        if (this.equipe.members && this.equipe.members.length > 0) {\n          this.loadMembersDetails();\n        }\n\n        this.loading = false;\n      },\n      error: (error) => {\n        console.error(\"Erreur lors du chargement de l'équipe:\", error);\n        this.error =\n          \"Impossible de charger les détails de l'équipe. Veuillez réessayer plus tard.\";\n        this.loading = false;\n      },\n    });\n  }\n\n  // Méthode pour récupérer les détails des membres de l'équipe\n  loadMembersDetails(): void {\n    if (!this.equipe.members || this.equipe.members.length === 0) {\n      return;\n    }\n\n    console.log(\"Chargement des détails des membres de l'équipe...\");\n\n    // Pour chaque membre de l'équipe, essayer de trouver ses informations dans la liste des utilisateurs\n    this.equipe.members.forEach((membreId) => {\n      // Chercher d'abord dans la liste des utilisateurs\n      const user = this.availableUsers.find(\n        (u) => u._id === membreId || u.id === membreId\n      );\n      if (user) {\n        console.log(\n          `Membre ${membreId} trouvé dans la liste des utilisateurs:`,\n          user\n        );\n\n        // Vérifier si toutes les informations nécessaires sont présentes\n        if (!user.email || (!user.profession && !user.role)) {\n          // Si des informations manquent, essayer de les récupérer depuis l'API\n          this.userService.getUser(membreId).subscribe({\n            next: (userData) => {\n              console.log(\n                `Détails supplémentaires de l'utilisateur ${membreId} récupérés:`,\n                userData\n              );\n\n              // Mettre à jour l'utilisateur dans la liste avec les nouvelles informations\n              const index = this.availableUsers.findIndex(\n                (u) => u._id === membreId || u.id === membreId\n              );\n              if (index !== -1) {\n                this.availableUsers[index] = {\n                  ...this.availableUsers[index],\n                  ...userData,\n                };\n              }\n            },\n            error: (error) => {\n              console.error(\n                `Erreur lors de la récupération des détails supplémentaires de l'utilisateur ${membreId}:`,\n                error\n              );\n            },\n          });\n        }\n      } else {\n        // Si non trouvé, essayer de récupérer l'utilisateur depuis l'API\n        this.userService.getUser(membreId).subscribe({\n          next: (userData) => {\n            console.log(\n              `Détails de l'utilisateur ${membreId} récupérés:`,\n              userData\n            );\n            // Ajouter l'utilisateur à la liste des utilisateurs disponibles s'il n'y est pas déjà\n            if (\n              !this.availableUsers.some(\n                (u) => u._id === userData._id || u.id === userData.id\n              )\n            ) {\n              this.availableUsers.push(userData);\n            }\n          },\n          error: (error) => {\n            console.error(\n              `Erreur lors de la récupération des détails de l'utilisateur ${membreId}:`,\n              error\n            );\n          },\n        });\n      }\n    });\n  }\n\n  checkNameExists(name: string): boolean {\n    // En mode édition, ignorer l'équipe actuelle\n    if (this.isEditMode && this.equipeId) {\n      return this.existingEquipes.some(\n        (e) => e.name === name && e._id !== this.equipeId\n      );\n    }\n    // En mode création, vérifier tous les noms\n    return this.existingEquipes.some((e) => e.name === name);\n  }\n\n  updateName(value: string): void {\n    console.log('Name updated:', value);\n    this.equipe.name = value;\n\n    // Vérifier si le nom existe déjà\n    this.nameExists = this.checkNameExists(value);\n    if (this.nameExists) {\n      console.warn(\"Ce nom d'équipe existe déjà\");\n    }\n\n    // Vérifier si le nom a au moins 3 caractères\n    this.nameError = value.length > 0 && value.length < 3;\n    if (this.nameError) {\n      console.warn('Le nom doit contenir au moins 3 caractères');\n    }\n  }\n\n  updateDescription(value: string): void {\n    console.log('Description updated:', value);\n    this.equipe.description = value;\n\n    // Vérifier si la description a au moins 10 caractères\n    this.descriptionError = value.length > 0 && value.length < 10;\n    if (this.descriptionError) {\n      console.warn('La description doit contenir au moins 10 caractères');\n    }\n  }\n\n  onSubmit(): void {\n    console.log('Form submitted with:', this.equipe);\n\n    // Vérifier si le nom est présent et valide\n    if (!this.equipe.name) {\n      this.error = \"Le nom de l'équipe est requis.\";\n      return;\n    }\n\n    if (this.equipe.name.length < 3) {\n      this.nameError = true;\n      this.error = \"Le nom de l'équipe doit contenir au moins 3 caractères.\";\n      return;\n    }\n\n    // Vérifier si la description est présente et valide\n    if (!this.equipe.description) {\n      this.error = \"La description de l'équipe est requise.\";\n      return;\n    }\n\n    if (this.equipe.description.length < 10) {\n      this.descriptionError = true;\n      this.error =\n        \"La description de l'équipe doit contenir au moins 10 caractères.\";\n      return;\n    }\n\n    // Vérifier si le nom existe déjà avant de soumettre\n    if (this.checkNameExists(this.equipe.name)) {\n      this.nameExists = true;\n      this.error =\n        'Une équipe avec ce nom existe déjà. Veuillez choisir un autre nom.';\n      return;\n    }\n\n    this.submitting = true;\n    this.error = null;\n\n    // Créer une copie de l'objet équipe pour éviter les problèmes de référence\n    const equipeToSave: Equipe = {\n      name: this.equipe.name,\n      description: this.equipe.description || '',\n      admin: this.equipe.admin,\n    };\n\n    // Ajouter l'ID si nous sommes en mode édition\n    if (this.isEditMode && this.equipeId) {\n      equipeToSave._id = this.equipeId;\n    }\n\n    console.log('Données à envoyer:', equipeToSave);\n\n    if (this.isEditMode && this.equipeId) {\n      // Mode édition\n      this.equipeService.updateEquipe(this.equipeId, equipeToSave).subscribe({\n        next: (response) => {\n          console.log('Équipe mise à jour avec succès:', response);\n          this.submitting = false;\n          this.notificationService.showSuccess(\n            `L'équipe \"${response.name}\" a été mise à jour avec succès.`\n          );\n          this.router.navigate(['/equipes/liste']);\n        },\n        error: (error) => {\n          console.error(\"Erreur lors de la mise à jour de l'équipe:\", error);\n          this.error = `Impossible de mettre à jour l'équipe: ${error.message}`;\n          this.submitting = false;\n          this.notificationService.showError(`Erreur: ${error.message}`);\n        },\n      });\n    } else {\n      // Mode ajout\n      this.equipeService.addEquipe(equipeToSave).subscribe({\n        next: (response) => {\n          console.log('Équipe ajoutée avec succès:', response);\n          this.submitting = false;\n          this.notificationService.showSuccess(\n            `L'équipe \"${response.name}\" a été créée avec succès.`\n          );\n          this.router.navigate(['/equipes/liste']);\n        },\n        error: (error) => {\n          console.error(\"Erreur lors de l'ajout de l'équipe:\", error);\n          this.error = `Impossible d'ajouter l'équipe: ${error.message}`;\n          this.submitting = false;\n          this.notificationService.showError(`Erreur: ${error.message}`);\n        },\n      });\n    }\n  }\n\n  cancel(): void {\n    console.log('Form cancelled');\n    this.router.navigate(['/equipes/liste']);\n  }\n\n  // Méthodes pour gérer les membres\n  addMembreToEquipe(membreId: string, role: string = 'membre'): void {\n    console.log(\n      'Début de addMembreToEquipe avec membreId:',\n      membreId,\n      'et rôle:',\n      role\n    );\n    console.log('État actuel - this.equipeId:', this.equipeId);\n    console.log('État actuel - this.equipe:', this.equipe);\n\n    // Utiliser this.equipe._id si this.equipeId n'est pas défini\n    const equipeId = this.equipeId || (this.equipe && this.equipe._id);\n\n    console.log('equipeId calculé:', equipeId);\n\n    if (!equipeId || !membreId) {\n      console.error(\"ID d'équipe ou ID de membre manquant\");\n      this.error = \"ID d'équipe ou ID de membre manquant\";\n      console.log('equipeId:', equipeId, 'membreId:', membreId);\n\n      // Afficher un message à l'utilisateur\n      this.notificationService.showError(\n        \"Impossible d'ajouter le membre: ID d'équipe ou ID de membre manquant\"\n      );\n      return;\n    }\n\n    // Vérifier si le membre est déjà dans l'équipe\n    if (this.equipe.members && this.equipe.members.includes(membreId)) {\n      this.notificationService.showError(\n        \"Ce membre fait déjà partie de l'équipe\"\n      );\n      return;\n    }\n\n    // Récupérer les informations de l'utilisateur pour afficher un message plus informatif\n    const user = this.availableUsers.find(\n      (u) => u._id === membreId || u.id === membreId\n    );\n    const userName = user\n      ? user.firstName && user.lastName\n        ? `${user.firstName} ${user.lastName}`\n        : user.name || membreId\n      : membreId;\n\n    // Créer l'objet membre avec le rôle spécifié\n    const membre: Membre = {\n      id: membreId,\n      role: role,\n    };\n\n    this.loading = true;\n\n    console.log(\n      `Ajout de l'utilisateur \"${userName}\" comme ${role} à l'équipe ${equipeId}`\n    );\n\n    this.equipeService.addMembreToEquipe(equipeId, membre).subscribe({\n      next: (response) => {\n        console.log('Membre ajouté avec succès:', response);\n        this.notificationService.showSuccess(\n          `${userName} a été ajouté comme ${\n            role === 'admin' ? 'administrateur' : 'membre'\n          } à l'équipe`\n        );\n        // Recharger l'équipe pour mettre à jour la liste des membres\n        this.loadEquipe(equipeId);\n        this.loading = false;\n      },\n      error: (error) => {\n        console.error(\"Erreur lors de l'ajout du membre:\", error);\n        this.error =\n          \"Impossible d'ajouter le membre. Veuillez réessayer plus tard.\";\n        this.notificationService.showError(\n          \"Erreur lors de l'ajout du membre: \" + error.message\n        );\n        this.loading = false;\n      },\n    });\n  }\n\n  // Méthode pour obtenir le nom complet d'un membre à partir de son ID\n  getMembreName(membreId: string): string {\n    // Chercher d'abord dans la liste des utilisateurs\n    const user = this.availableUsers.find(\n      (u) => u._id === membreId || u.id === membreId\n    );\n    if (user) {\n      if (user.firstName && user.lastName) {\n        return `${user.firstName} ${user.lastName}`;\n      } else if (user.name) {\n        return user.name;\n      }\n    }\n\n    // Chercher ensuite dans la liste des membres\n    const membre = this.availableMembers.find(\n      (m) => m._id === membreId || m.id === membreId\n    );\n    if (membre && membre.name) {\n      return membre.name;\n    }\n\n    // Si aucun nom n'est trouvé, retourner l'ID\n    return membreId;\n  }\n\n  // Méthode pour obtenir l'email d'un membre\n  getMembreEmail(membreId: string): string {\n    const user = this.availableUsers.find(\n      (u) => u._id === membreId || u.id === membreId\n    );\n    if (user && user.email) {\n      return user.email;\n    }\n    return 'Non renseigné';\n  }\n\n  // Méthode pour obtenir la profession d'un membre\n  getMembreProfession(membreId: string): string {\n    const user = this.availableUsers.find(\n      (u) => u._id === membreId || u.id === membreId\n    );\n    if (user) {\n      if (user.profession) {\n        return user.profession === 'etudiant' ? 'Étudiant' : 'Professeur';\n      } else if (user.role) {\n        return user.role === 'etudiant' ? 'Étudiant' : 'Professeur';\n      }\n    }\n    return 'Non spécifié';\n  }\n\n  // Méthode pour obtenir le rôle d'un membre dans l'équipe\n  getMembreRole(membreId: string): string {\n    // Cette méthode nécessiterait d'avoir accès aux rôles des membres dans l'équipe\n    // Pour l'instant, nous retournons une valeur par défaut\n    return 'Membre';\n  }\n\n  removeMembreFromEquipe(membreId: string): void {\n    console.log('Méthode removeMembreFromEquipe appelée avec ID:', membreId);\n    console.log('État actuel - this.equipeId:', this.equipeId);\n    console.log('État actuel - this.equipe:', this.equipe);\n\n    // Utiliser this.equipe._id si this.equipeId n'est pas défini\n    const equipeId = this.equipeId || (this.equipe && this.equipe._id);\n\n    if (!equipeId) {\n      console.error(\"ID d'équipe manquant\");\n      this.error = \"ID d'équipe manquant. Impossible de retirer le membre.\";\n      this.notificationService.showError(\n        \"ID d'équipe manquant. Impossible de retirer le membre.\"\n      );\n      return;\n    }\n\n    if (!membreId) {\n      console.error('ID de membre manquant');\n      this.error = 'ID de membre manquant. Impossible de retirer le membre.';\n      this.notificationService.showError(\n        'ID de membre manquant. Impossible de retirer le membre.'\n      );\n      return;\n    }\n\n    // Obtenir le nom du membre pour l'afficher dans le message de confirmation\n    const membreName = this.getMembreName(membreId);\n\n    console.log(\n      `Tentative de retrait de l'utilisateur ${membreId} (${membreName}) de l'équipe ${equipeId}`\n    );\n\n    try {\n      if (\n        confirm(`Êtes-vous sûr de vouloir retirer ${membreName} de l'équipe?`)\n      ) {\n        console.log('Confirmation acceptée, suppression en cours...');\n\n        this.loading = true;\n        this.error = null;\n\n        // Ajouter un délai pour s'assurer que l'utilisateur voit le chargement\n        setTimeout(() => {\n          this.equipeService\n            .removeMembreFromEquipe(equipeId, membreId)\n            .subscribe({\n              next: (response) => {\n                console.log(\n                  `Utilisateur \"${membreName}\" retiré avec succès de l'équipe:`,\n                  response\n                );\n                this.loading = false;\n                this.notificationService.showSuccess(\n                  `${membreName} a été retiré avec succès de l'équipe`\n                );\n\n                // Recharger l'équipe pour mettre à jour la liste des membres\n                this.loadEquipe(equipeId);\n              },\n              error: (error) => {\n                console.error(\n                  `Erreur lors du retrait de l'utilisateur \"${membreName}\":`,\n                  error\n                );\n                console.error(\"Détails de l'erreur:\", {\n                  status: error.status,\n                  message: error.message,\n                  error: error,\n                });\n\n                this.loading = false;\n                this.error = `Impossible de retirer l'utilisateur \"${membreName}\" de l'équipe: ${\n                  error.message || 'Erreur inconnue'\n                }`;\n                this.notificationService.showError(\n                  `Erreur lors du retrait du membre: ${this.error}`\n                );\n              },\n            });\n        }, 500);\n      } else {\n        console.log(\"Suppression annulée par l'utilisateur\");\n      }\n    } catch (error: any) {\n      console.error('Exception lors du retrait du membre:', error);\n      this.error = `Exception: ${error?.message || 'Erreur inconnue'}`;\n      this.notificationService.showError(`Exception: ${this.error}`);\n    }\n  }\n\n  // Méthode pour supprimer l'équipe\n  deleteEquipe(): void {\n    console.log('Méthode deleteEquipe appelée dans equipe-form.component.ts');\n    console.log('État actuel - this.equipeId:', this.equipeId);\n    console.log('État actuel - this.equipe:', this.equipe);\n\n    // Utiliser this.equipe._id si this.equipeId n'est pas défini\n    const equipeId = this.equipeId || (this.equipe && this.equipe._id);\n\n    if (!equipeId) {\n      console.error(\"ID d'équipe manquant\");\n      this.error = \"ID d'équipe manquant. Impossible de supprimer l'équipe.\";\n      this.notificationService.showError(\n        \"ID d'équipe manquant. Impossible de supprimer l'équipe.\"\n      );\n      return;\n    }\n\n    console.log(\"ID de l'équipe à supprimer (final):\", equipeId);\n\n    try {\n      if (\n        confirm(\n          `Êtes-vous sûr de vouloir supprimer l'équipe \"${this.equipe.name}\"? Cette action est irréversible.`\n        )\n      ) {\n        console.log('Confirmation acceptée, suppression en cours...');\n\n        this.loading = true;\n        this.error = null;\n\n        // Ajouter un délai pour s'assurer que l'utilisateur voit le chargement\n        setTimeout(() => {\n          this.equipeService.deleteEquipe(equipeId).subscribe({\n            next: (response) => {\n              console.log('Équipe supprimée avec succès, réponse:', response);\n              this.loading = false;\n              this.notificationService.showSuccess(\n                `L'équipe \"${this.equipe.name}\" a été supprimée avec succès.`\n              );\n\n              // Ajouter un délai avant la redirection\n              setTimeout(() => {\n                this.router.navigate(['/equipes/liste']);\n              }, 500);\n            },\n            error: (error) => {\n              console.error(\n                \"Erreur lors de la suppression de l'équipe:\",\n                error\n              );\n              console.error(\"Détails de l'erreur:\", {\n                status: error.status,\n                message: error.message,\n                error: error,\n              });\n\n              this.loading = false;\n              this.error = `Impossible de supprimer l'équipe: ${\n                error.message || 'Erreur inconnue'\n              }`;\n              this.notificationService.showError(\n                `Erreur lors de la suppression: ${this.error}`\n              );\n            },\n          });\n        }, 500);\n      } else {\n        console.log(\"Suppression annulée par l'utilisateur\");\n      }\n    } catch (error: any) {\n      console.error('Exception lors de la suppression:', error);\n      this.error = `Exception: ${error?.message || 'Erreur inconnue'}`;\n      this.notificationService.showError(`Exception: ${this.error}`);\n    }\n  }\n}\n"], "mappings": ";AAAA,SAASA,SAAS,QAAgB,eAAe;AAsB1C,WAAMC,mBAAmB,GAAzB,MAAMA,mBAAmB;EAmB9BC,YACUC,aAA4B,EAC5BC,aAA4B,EAC5BC,WAAwB,EACxBC,KAAqB,EACrBC,MAAc,EACdC,mBAAwC;IALxC,KAAAL,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,mBAAmB,GAAnBA,mBAAmB;IAxB7B,KAAAC,MAAM,GAAW;MACfC,IAAI,EAAE,EAAE;MACRC,WAAW,EAAE,EAAE;MACfC,KAAK,EAAE,0BAA0B,CAAE;KACpC;;IACD,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,KAAK,GAAkB,IAAI;IAC3B,KAAAC,QAAQ,GAAkB,IAAI;IAC9B,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAC,eAAe,GAAa,EAAE;IAC9B,KAAAC,gBAAgB,GAAa,EAAE,CAAC,CAAC;IACjC,KAAAC,cAAc,GAAW,EAAE,CAAC,CAAC;EAS1B;;EAEHC,QAAQA,CAAA;IACNC,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAE9C;IACA,IAAI,CAAC,IAAI,CAAClB,MAAM,EAAE;MAChB,IAAI,CAACA,MAAM,GAAG;QACZC,IAAI,EAAE,EAAE;QACRC,WAAW,EAAE,EAAE;QACfC,KAAK,EAAE,0BAA0B,CAAE;OACpC;;IAGH;IACA,IAAI,CAACgB,cAAc,EAAE;IAErB;IACA,IAAI,CAACC,cAAc,EAAE;IAErB;IACA,IAAI,CAACC,YAAY,EAAE;IAEnB,IAAI;MACF;MACA,IAAI,CAACb,QAAQ,GAAG,IAAI,CAACX,KAAK,CAACyB,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC;MACtD,IAAI,CAACpB,UAAU,GAAG,CAAC,CAAC,IAAI,CAACI,QAAQ;MACjCS,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE,IAAI,CAACd,UAAU,EAAE,KAAK,EAAE,IAAI,CAACI,QAAQ,CAAC;MAEhE,IAAI,IAAI,CAACJ,UAAU,IAAI,IAAI,CAACI,QAAQ,EAAE;QACpC,IAAI,CAACiB,UAAU,CAAC,IAAI,CAACjB,QAAQ,CAAC;QAE9B;QACAkB,UAAU,CAAC,MAAK;UACdT,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAACV,QAAQ,CAAC;UAC1DS,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAAClB,MAAM,CAAC;QACxD,CAAC,EAAE,IAAI,CAAC;;KAEX,CAAC,OAAOO,KAAK,EAAE;MACdU,OAAO,CAACV,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,IAAI,CAACA,KAAK,GAAG,yBAAyB;;IAGxC;IACAmB,UAAU,CAAC,MAAK;MACd,MAAMC,SAAS,GAAGC,QAAQ,CAACC,cAAc,CAAC,iBAAiB,CAAC;MAC5D,IAAIF,SAAS,EAAE;QACbV,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAC9CS,SAAS,CAACG,gBAAgB,CAAC,OAAO,EAAE,MAAK;UACvCb,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAChD,CAAC,CAAC;OACH,MAAM;QACLD,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;;IAEtD,CAAC,EAAE,IAAI,CAAC;EACV;EAEAE,cAAcA,CAAA;IACZ,IAAI,CAACzB,aAAa,CAACoC,UAAU,EAAE,CAACC,SAAS,CAAC;MACxCC,IAAI,EAAGC,OAAO,IAAI;QAChB,IAAI,CAACpB,gBAAgB,GAAGoB,OAAO;QAC/BjB,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEgB,OAAO,CAAC;MACtD,CAAC;MACD3B,KAAK,EAAGA,KAAK,IAAI;QACfU,OAAO,CAACV,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;QAC9D,IAAI,CAACA,KAAK,GACR,2EAA2E;MAC/E;KACD,CAAC;EACJ;EAEAc,YAAYA,CAAA;IACV,IAAI,CAACzB,WAAW,CAACuC,QAAQ,EAAE,CAACH,SAAS,CAAC;MACpCC,IAAI,EAAGG,KAAK,IAAI;QACd,IAAI,CAACrB,cAAc,GAAGqB,KAAK;QAC3BnB,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEkB,KAAK,CAAC;MACzD,CAAC;MACD7B,KAAK,EAAGA,KAAK,IAAI;QACfU,OAAO,CAACV,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;QACnE,IAAI,CAACA,KAAK,GACR,gFAAgF;MACpF;KACD,CAAC;EACJ;EAEAY,cAAcA,CAAA;IACZ,IAAI,CAACzB,aAAa,CAAC2C,UAAU,EAAE,CAACL,SAAS,CAAC;MACxCC,IAAI,EAAGK,OAAO,IAAI;QAChB,IAAI,CAACzB,eAAe,GAAGyB,OAAO;QAC9BrB,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEoB,OAAO,CAAC;MACtD,CAAC;MACD/B,KAAK,EAAGA,KAAK,IAAI;QACfU,OAAO,CAACV,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAChE;KACD,CAAC;EACJ;EAEAkB,UAAUA,CAACc,EAAU;IACnBtB,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEqB,EAAE,CAAC;IAC1C,IAAI,CAAClC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACE,KAAK,GAAG,IAAI;IAEjB,IAAI,CAACb,aAAa,CAAC8C,SAAS,CAACD,EAAE,CAAC,CAACP,SAAS,CAAC;MACzCC,IAAI,EAAGQ,IAAI,IAAI;QACbxB,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEuB,IAAI,CAAC;QACpC,IAAI,CAACzC,MAAM,GAAGyC,IAAI;QAElB;QACAxB,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE,IAAI,CAAClB,MAAM,CAAC0C,GAAG,CAAC;QAChEzB,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAACV,QAAQ,CAAC;QAE5C;QACA,IAAI,IAAI,CAACR,MAAM,CAAC2C,OAAO,IAAI,IAAI,CAAC3C,MAAM,CAAC2C,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;UACzD,IAAI,CAACC,kBAAkB,EAAE;;QAG3B,IAAI,CAACxC,OAAO,GAAG,KAAK;MACtB,CAAC;MACDE,KAAK,EAAGA,KAAK,IAAI;QACfU,OAAO,CAACV,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;QAC9D,IAAI,CAACA,KAAK,GACR,8EAA8E;QAChF,IAAI,CAACF,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEA;EACAwC,kBAAkBA,CAAA;IAChB,IAAI,CAAC,IAAI,CAAC7C,MAAM,CAAC2C,OAAO,IAAI,IAAI,CAAC3C,MAAM,CAAC2C,OAAO,CAACC,MAAM,KAAK,CAAC,EAAE;MAC5D;;IAGF3B,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;IAEhE;IACA,IAAI,CAAClB,MAAM,CAAC2C,OAAO,CAACG,OAAO,CAAEC,QAAQ,IAAI;MACvC;MACA,MAAMC,IAAI,GAAG,IAAI,CAACjC,cAAc,CAACkC,IAAI,CAClCC,CAAC,IAAKA,CAAC,CAACR,GAAG,KAAKK,QAAQ,IAAIG,CAAC,CAACX,EAAE,KAAKQ,QAAQ,CAC/C;MACD,IAAIC,IAAI,EAAE;QACR/B,OAAO,CAACC,GAAG,CACT,UAAU6B,QAAQ,yCAAyC,EAC3DC,IAAI,CACL;QAED;QACA,IAAI,CAACA,IAAI,CAACG,KAAK,IAAK,CAACH,IAAI,CAACI,UAAU,IAAI,CAACJ,IAAI,CAACK,IAAK,EAAE;UACnD;UACA,IAAI,CAACzD,WAAW,CAAC0D,OAAO,CAACP,QAAQ,CAAC,CAACf,SAAS,CAAC;YAC3CC,IAAI,EAAGsB,QAAQ,IAAI;cACjBtC,OAAO,CAACC,GAAG,CACT,4CAA4C6B,QAAQ,aAAa,EACjEQ,QAAQ,CACT;cAED;cACA,MAAMC,KAAK,GAAG,IAAI,CAACzC,cAAc,CAAC0C,SAAS,CACxCP,CAAC,IAAKA,CAAC,CAACR,GAAG,KAAKK,QAAQ,IAAIG,CAAC,CAACX,EAAE,KAAKQ,QAAQ,CAC/C;cACD,IAAIS,KAAK,KAAK,CAAC,CAAC,EAAE;gBAChB,IAAI,CAACzC,cAAc,CAACyC,KAAK,CAAC,GAAG;kBAC3B,GAAG,IAAI,CAACzC,cAAc,CAACyC,KAAK,CAAC;kBAC7B,GAAGD;iBACJ;;YAEL,CAAC;YACDhD,KAAK,EAAGA,KAAK,IAAI;cACfU,OAAO,CAACV,KAAK,CACX,+EAA+EwC,QAAQ,GAAG,EAC1FxC,KAAK,CACN;YACH;WACD,CAAC;;OAEL,MAAM;QACL;QACA,IAAI,CAACX,WAAW,CAAC0D,OAAO,CAACP,QAAQ,CAAC,CAACf,SAAS,CAAC;UAC3CC,IAAI,EAAGsB,QAAQ,IAAI;YACjBtC,OAAO,CAACC,GAAG,CACT,4BAA4B6B,QAAQ,aAAa,EACjDQ,QAAQ,CACT;YACD;YACA,IACE,CAAC,IAAI,CAACxC,cAAc,CAAC2C,IAAI,CACtBR,CAAC,IAAKA,CAAC,CAACR,GAAG,KAAKa,QAAQ,CAACb,GAAG,IAAIQ,CAAC,CAACX,EAAE,KAAKgB,QAAQ,CAAChB,EAAE,CACtD,EACD;cACA,IAAI,CAACxB,cAAc,CAAC4C,IAAI,CAACJ,QAAQ,CAAC;;UAEtC,CAAC;UACDhD,KAAK,EAAGA,KAAK,IAAI;YACfU,OAAO,CAACV,KAAK,CACX,+DAA+DwC,QAAQ,GAAG,EAC1ExC,KAAK,CACN;UACH;SACD,CAAC;;IAEN,CAAC,CAAC;EACJ;EAEAqD,eAAeA,CAAC3D,IAAY;IAC1B;IACA,IAAI,IAAI,CAACG,UAAU,IAAI,IAAI,CAACI,QAAQ,EAAE;MACpC,OAAO,IAAI,CAACK,eAAe,CAAC6C,IAAI,CAC7BG,CAAC,IAAKA,CAAC,CAAC5D,IAAI,KAAKA,IAAI,IAAI4D,CAAC,CAACnB,GAAG,KAAK,IAAI,CAAClC,QAAQ,CAClD;;IAEH;IACA,OAAO,IAAI,CAACK,eAAe,CAAC6C,IAAI,CAAEG,CAAC,IAAKA,CAAC,CAAC5D,IAAI,KAAKA,IAAI,CAAC;EAC1D;EAEA6D,UAAUA,CAACC,KAAa;IACtB9C,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE6C,KAAK,CAAC;IACnC,IAAI,CAAC/D,MAAM,CAACC,IAAI,GAAG8D,KAAK;IAExB;IACA,IAAI,CAACtD,UAAU,GAAG,IAAI,CAACmD,eAAe,CAACG,KAAK,CAAC;IAC7C,IAAI,IAAI,CAACtD,UAAU,EAAE;MACnBQ,OAAO,CAAC+C,IAAI,CAAC,6BAA6B,CAAC;;IAG7C;IACA,IAAI,CAACtD,SAAS,GAAGqD,KAAK,CAACnB,MAAM,GAAG,CAAC,IAAImB,KAAK,CAACnB,MAAM,GAAG,CAAC;IACrD,IAAI,IAAI,CAAClC,SAAS,EAAE;MAClBO,OAAO,CAAC+C,IAAI,CAAC,4CAA4C,CAAC;;EAE9D;EAEAC,iBAAiBA,CAACF,KAAa;IAC7B9C,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE6C,KAAK,CAAC;IAC1C,IAAI,CAAC/D,MAAM,CAACE,WAAW,GAAG6D,KAAK;IAE/B;IACA,IAAI,CAACpD,gBAAgB,GAAGoD,KAAK,CAACnB,MAAM,GAAG,CAAC,IAAImB,KAAK,CAACnB,MAAM,GAAG,EAAE;IAC7D,IAAI,IAAI,CAACjC,gBAAgB,EAAE;MACzBM,OAAO,CAAC+C,IAAI,CAAC,qDAAqD,CAAC;;EAEvE;EAEAE,QAAQA,CAAA;IACNjD,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAAClB,MAAM,CAAC;IAEhD;IACA,IAAI,CAAC,IAAI,CAACA,MAAM,CAACC,IAAI,EAAE;MACrB,IAAI,CAACM,KAAK,GAAG,gCAAgC;MAC7C;;IAGF,IAAI,IAAI,CAACP,MAAM,CAACC,IAAI,CAAC2C,MAAM,GAAG,CAAC,EAAE;MAC/B,IAAI,CAAClC,SAAS,GAAG,IAAI;MACrB,IAAI,CAACH,KAAK,GAAG,yDAAyD;MACtE;;IAGF;IACA,IAAI,CAAC,IAAI,CAACP,MAAM,CAACE,WAAW,EAAE;MAC5B,IAAI,CAACK,KAAK,GAAG,yCAAyC;MACtD;;IAGF,IAAI,IAAI,CAACP,MAAM,CAACE,WAAW,CAAC0C,MAAM,GAAG,EAAE,EAAE;MACvC,IAAI,CAACjC,gBAAgB,GAAG,IAAI;MAC5B,IAAI,CAACJ,KAAK,GACR,kEAAkE;MACpE;;IAGF;IACA,IAAI,IAAI,CAACqD,eAAe,CAAC,IAAI,CAAC5D,MAAM,CAACC,IAAI,CAAC,EAAE;MAC1C,IAAI,CAACQ,UAAU,GAAG,IAAI;MACtB,IAAI,CAACF,KAAK,GACR,oEAAoE;MACtE;;IAGF,IAAI,CAACD,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,KAAK,GAAG,IAAI;IAEjB;IACA,MAAM4D,YAAY,GAAW;MAC3BlE,IAAI,EAAE,IAAI,CAACD,MAAM,CAACC,IAAI;MACtBC,WAAW,EAAE,IAAI,CAACF,MAAM,CAACE,WAAW,IAAI,EAAE;MAC1CC,KAAK,EAAE,IAAI,CAACH,MAAM,CAACG;KACpB;IAED;IACA,IAAI,IAAI,CAACC,UAAU,IAAI,IAAI,CAACI,QAAQ,EAAE;MACpC2D,YAAY,CAACzB,GAAG,GAAG,IAAI,CAAClC,QAAQ;;IAGlCS,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEiD,YAAY,CAAC;IAE/C,IAAI,IAAI,CAAC/D,UAAU,IAAI,IAAI,CAACI,QAAQ,EAAE;MACpC;MACA,IAAI,CAACd,aAAa,CAAC0E,YAAY,CAAC,IAAI,CAAC5D,QAAQ,EAAE2D,YAAY,CAAC,CAACnC,SAAS,CAAC;QACrEC,IAAI,EAAGoC,QAAQ,IAAI;UACjBpD,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEmD,QAAQ,CAAC;UACxD,IAAI,CAAC/D,UAAU,GAAG,KAAK;UACvB,IAAI,CAACP,mBAAmB,CAACuE,WAAW,CAClC,aAAaD,QAAQ,CAACpE,IAAI,kCAAkC,CAC7D;UACD,IAAI,CAACH,MAAM,CAACyE,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;QAC1C,CAAC;QACDhE,KAAK,EAAGA,KAAK,IAAI;UACfU,OAAO,CAACV,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;UAClE,IAAI,CAACA,KAAK,GAAG,yCAAyCA,KAAK,CAACiE,OAAO,EAAE;UACrE,IAAI,CAAClE,UAAU,GAAG,KAAK;UACvB,IAAI,CAACP,mBAAmB,CAAC0E,SAAS,CAAC,WAAWlE,KAAK,CAACiE,OAAO,EAAE,CAAC;QAChE;OACD,CAAC;KACH,MAAM;MACL;MACA,IAAI,CAAC9E,aAAa,CAACgF,SAAS,CAACP,YAAY,CAAC,CAACnC,SAAS,CAAC;QACnDC,IAAI,EAAGoC,QAAQ,IAAI;UACjBpD,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEmD,QAAQ,CAAC;UACpD,IAAI,CAAC/D,UAAU,GAAG,KAAK;UACvB,IAAI,CAACP,mBAAmB,CAACuE,WAAW,CAClC,aAAaD,QAAQ,CAACpE,IAAI,4BAA4B,CACvD;UACD,IAAI,CAACH,MAAM,CAACyE,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;QAC1C,CAAC;QACDhE,KAAK,EAAGA,KAAK,IAAI;UACfU,OAAO,CAACV,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;UAC3D,IAAI,CAACA,KAAK,GAAG,kCAAkCA,KAAK,CAACiE,OAAO,EAAE;UAC9D,IAAI,CAAClE,UAAU,GAAG,KAAK;UACvB,IAAI,CAACP,mBAAmB,CAAC0E,SAAS,CAAC,WAAWlE,KAAK,CAACiE,OAAO,EAAE,CAAC;QAChE;OACD,CAAC;;EAEN;EAEAG,MAAMA,CAAA;IACJ1D,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAC7B,IAAI,CAACpB,MAAM,CAACyE,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;EAC1C;EAEA;EACAK,iBAAiBA,CAAC7B,QAAgB,EAAEM,IAAA,GAAe,QAAQ;IACzDpC,OAAO,CAACC,GAAG,CACT,2CAA2C,EAC3C6B,QAAQ,EACR,UAAU,EACVM,IAAI,CACL;IACDpC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAACV,QAAQ,CAAC;IAC1DS,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAAClB,MAAM,CAAC;IAEtD;IACA,MAAMQ,QAAQ,GAAG,IAAI,CAACA,QAAQ,IAAK,IAAI,CAACR,MAAM,IAAI,IAAI,CAACA,MAAM,CAAC0C,GAAI;IAElEzB,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEV,QAAQ,CAAC;IAE1C,IAAI,CAACA,QAAQ,IAAI,CAACuC,QAAQ,EAAE;MAC1B9B,OAAO,CAACV,KAAK,CAAC,sCAAsC,CAAC;MACrD,IAAI,CAACA,KAAK,GAAG,sCAAsC;MACnDU,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEV,QAAQ,EAAE,WAAW,EAAEuC,QAAQ,CAAC;MAEzD;MACA,IAAI,CAAChD,mBAAmB,CAAC0E,SAAS,CAChC,sEAAsE,CACvE;MACD;;IAGF;IACA,IAAI,IAAI,CAACzE,MAAM,CAAC2C,OAAO,IAAI,IAAI,CAAC3C,MAAM,CAAC2C,OAAO,CAACkC,QAAQ,CAAC9B,QAAQ,CAAC,EAAE;MACjE,IAAI,CAAChD,mBAAmB,CAAC0E,SAAS,CAChC,wCAAwC,CACzC;MACD;;IAGF;IACA,MAAMzB,IAAI,GAAG,IAAI,CAACjC,cAAc,CAACkC,IAAI,CAClCC,CAAC,IAAKA,CAAC,CAACR,GAAG,KAAKK,QAAQ,IAAIG,CAAC,CAACX,EAAE,KAAKQ,QAAQ,CAC/C;IACD,MAAM+B,QAAQ,GAAG9B,IAAI,GACjBA,IAAI,CAAC+B,SAAS,IAAI/B,IAAI,CAACgC,QAAQ,GAC7B,GAAGhC,IAAI,CAAC+B,SAAS,IAAI/B,IAAI,CAACgC,QAAQ,EAAE,GACpChC,IAAI,CAAC/C,IAAI,IAAI8C,QAAQ,GACvBA,QAAQ;IAEZ;IACA,MAAMkC,MAAM,GAAW;MACrB1C,EAAE,EAAEQ,QAAQ;MACZM,IAAI,EAAEA;KACP;IAED,IAAI,CAAChD,OAAO,GAAG,IAAI;IAEnBY,OAAO,CAACC,GAAG,CACT,2BAA2B4D,QAAQ,WAAWzB,IAAI,eAAe7C,QAAQ,EAAE,CAC5E;IAED,IAAI,CAACd,aAAa,CAACkF,iBAAiB,CAACpE,QAAQ,EAAEyE,MAAM,CAAC,CAACjD,SAAS,CAAC;MAC/DC,IAAI,EAAGoC,QAAQ,IAAI;QACjBpD,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEmD,QAAQ,CAAC;QACnD,IAAI,CAACtE,mBAAmB,CAACuE,WAAW,CAClC,GAAGQ,QAAQ,uBACTzB,IAAI,KAAK,OAAO,GAAG,gBAAgB,GAAG,QACxC,aAAa,CACd;QACD;QACA,IAAI,CAAC5B,UAAU,CAACjB,QAAQ,CAAC;QACzB,IAAI,CAACH,OAAO,GAAG,KAAK;MACtB,CAAC;MACDE,KAAK,EAAGA,KAAK,IAAI;QACfU,OAAO,CAACV,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QACzD,IAAI,CAACA,KAAK,GACR,+DAA+D;QACjE,IAAI,CAACR,mBAAmB,CAAC0E,SAAS,CAChC,oCAAoC,GAAGlE,KAAK,CAACiE,OAAO,CACrD;QACD,IAAI,CAACnE,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEA;EACA6E,aAAaA,CAACnC,QAAgB;IAC5B;IACA,MAAMC,IAAI,GAAG,IAAI,CAACjC,cAAc,CAACkC,IAAI,CAClCC,CAAC,IAAKA,CAAC,CAACR,GAAG,KAAKK,QAAQ,IAAIG,CAAC,CAACX,EAAE,KAAKQ,QAAQ,CAC/C;IACD,IAAIC,IAAI,EAAE;MACR,IAAIA,IAAI,CAAC+B,SAAS,IAAI/B,IAAI,CAACgC,QAAQ,EAAE;QACnC,OAAO,GAAGhC,IAAI,CAAC+B,SAAS,IAAI/B,IAAI,CAACgC,QAAQ,EAAE;OAC5C,MAAM,IAAIhC,IAAI,CAAC/C,IAAI,EAAE;QACpB,OAAO+C,IAAI,CAAC/C,IAAI;;;IAIpB;IACA,MAAMgF,MAAM,GAAG,IAAI,CAACnE,gBAAgB,CAACmC,IAAI,CACtCkC,CAAC,IAAKA,CAAC,CAACzC,GAAG,KAAKK,QAAQ,IAAIoC,CAAC,CAAC5C,EAAE,KAAKQ,QAAQ,CAC/C;IACD,IAAIkC,MAAM,IAAIA,MAAM,CAAChF,IAAI,EAAE;MACzB,OAAOgF,MAAM,CAAChF,IAAI;;IAGpB;IACA,OAAO8C,QAAQ;EACjB;EAEA;EACAqC,cAAcA,CAACrC,QAAgB;IAC7B,MAAMC,IAAI,GAAG,IAAI,CAACjC,cAAc,CAACkC,IAAI,CAClCC,CAAC,IAAKA,CAAC,CAACR,GAAG,KAAKK,QAAQ,IAAIG,CAAC,CAACX,EAAE,KAAKQ,QAAQ,CAC/C;IACD,IAAIC,IAAI,IAAIA,IAAI,CAACG,KAAK,EAAE;MACtB,OAAOH,IAAI,CAACG,KAAK;;IAEnB,OAAO,eAAe;EACxB;EAEA;EACAkC,mBAAmBA,CAACtC,QAAgB;IAClC,MAAMC,IAAI,GAAG,IAAI,CAACjC,cAAc,CAACkC,IAAI,CAClCC,CAAC,IAAKA,CAAC,CAACR,GAAG,KAAKK,QAAQ,IAAIG,CAAC,CAACX,EAAE,KAAKQ,QAAQ,CAC/C;IACD,IAAIC,IAAI,EAAE;MACR,IAAIA,IAAI,CAACI,UAAU,EAAE;QACnB,OAAOJ,IAAI,CAACI,UAAU,KAAK,UAAU,GAAG,UAAU,GAAG,YAAY;OAClE,MAAM,IAAIJ,IAAI,CAACK,IAAI,EAAE;QACpB,OAAOL,IAAI,CAACK,IAAI,KAAK,UAAU,GAAG,UAAU,GAAG,YAAY;;;IAG/D,OAAO,cAAc;EACvB;EAEA;EACAiC,aAAaA,CAACvC,QAAgB;IAC5B;IACA;IACA,OAAO,QAAQ;EACjB;EAEAwC,sBAAsBA,CAACxC,QAAgB;IACrC9B,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAE6B,QAAQ,CAAC;IACxE9B,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAACV,QAAQ,CAAC;IAC1DS,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAAClB,MAAM,CAAC;IAEtD;IACA,MAAMQ,QAAQ,GAAG,IAAI,CAACA,QAAQ,IAAK,IAAI,CAACR,MAAM,IAAI,IAAI,CAACA,MAAM,CAAC0C,GAAI;IAElE,IAAI,CAAClC,QAAQ,EAAE;MACbS,OAAO,CAACV,KAAK,CAAC,sBAAsB,CAAC;MACrC,IAAI,CAACA,KAAK,GAAG,wDAAwD;MACrE,IAAI,CAACR,mBAAmB,CAAC0E,SAAS,CAChC,wDAAwD,CACzD;MACD;;IAGF,IAAI,CAAC1B,QAAQ,EAAE;MACb9B,OAAO,CAACV,KAAK,CAAC,uBAAuB,CAAC;MACtC,IAAI,CAACA,KAAK,GAAG,yDAAyD;MACtE,IAAI,CAACR,mBAAmB,CAAC0E,SAAS,CAChC,yDAAyD,CAC1D;MACD;;IAGF;IACA,MAAMe,UAAU,GAAG,IAAI,CAACN,aAAa,CAACnC,QAAQ,CAAC;IAE/C9B,OAAO,CAACC,GAAG,CACT,yCAAyC6B,QAAQ,KAAKyC,UAAU,iBAAiBhF,QAAQ,EAAE,CAC5F;IAED,IAAI;MACF,IACEiF,OAAO,CAAC,oCAAoCD,UAAU,eAAe,CAAC,EACtE;QACAvE,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;QAE7D,IAAI,CAACb,OAAO,GAAG,IAAI;QACnB,IAAI,CAACE,KAAK,GAAG,IAAI;QAEjB;QACAmB,UAAU,CAAC,MAAK;UACd,IAAI,CAAChC,aAAa,CACf6F,sBAAsB,CAAC/E,QAAQ,EAAEuC,QAAQ,CAAC,CAC1Cf,SAAS,CAAC;YACTC,IAAI,EAAGoC,QAAQ,IAAI;cACjBpD,OAAO,CAACC,GAAG,CACT,gBAAgBsE,UAAU,mCAAmC,EAC7DnB,QAAQ,CACT;cACD,IAAI,CAAChE,OAAO,GAAG,KAAK;cACpB,IAAI,CAACN,mBAAmB,CAACuE,WAAW,CAClC,GAAGkB,UAAU,uCAAuC,CACrD;cAED;cACA,IAAI,CAAC/D,UAAU,CAACjB,QAAQ,CAAC;YAC3B,CAAC;YACDD,KAAK,EAAGA,KAAK,IAAI;cACfU,OAAO,CAACV,KAAK,CACX,4CAA4CiF,UAAU,IAAI,EAC1DjF,KAAK,CACN;cACDU,OAAO,CAACV,KAAK,CAAC,sBAAsB,EAAE;gBACpCmF,MAAM,EAAEnF,KAAK,CAACmF,MAAM;gBACpBlB,OAAO,EAAEjE,KAAK,CAACiE,OAAO;gBACtBjE,KAAK,EAAEA;eACR,CAAC;cAEF,IAAI,CAACF,OAAO,GAAG,KAAK;cACpB,IAAI,CAACE,KAAK,GAAG,wCAAwCiF,UAAU,kBAC7DjF,KAAK,CAACiE,OAAO,IAAI,iBACnB,EAAE;cACF,IAAI,CAACzE,mBAAmB,CAAC0E,SAAS,CAChC,qCAAqC,IAAI,CAAClE,KAAK,EAAE,CAClD;YACH;WACD,CAAC;QACN,CAAC,EAAE,GAAG,CAAC;OACR,MAAM;QACLU,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;;KAEvD,CAAC,OAAOX,KAAU,EAAE;MACnBU,OAAO,CAACV,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D,IAAI,CAACA,KAAK,GAAG,cAAcA,KAAK,EAAEiE,OAAO,IAAI,iBAAiB,EAAE;MAChE,IAAI,CAACzE,mBAAmB,CAAC0E,SAAS,CAAC,cAAc,IAAI,CAAClE,KAAK,EAAE,CAAC;;EAElE;EAEA;EACAoF,YAAYA,CAAA;IACV1E,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;IACzED,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAACV,QAAQ,CAAC;IAC1DS,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAAClB,MAAM,CAAC;IAEtD;IACA,MAAMQ,QAAQ,GAAG,IAAI,CAACA,QAAQ,IAAK,IAAI,CAACR,MAAM,IAAI,IAAI,CAACA,MAAM,CAAC0C,GAAI;IAElE,IAAI,CAAClC,QAAQ,EAAE;MACbS,OAAO,CAACV,KAAK,CAAC,sBAAsB,CAAC;MACrC,IAAI,CAACA,KAAK,GAAG,yDAAyD;MACtE,IAAI,CAACR,mBAAmB,CAAC0E,SAAS,CAChC,yDAAyD,CAC1D;MACD;;IAGFxD,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEV,QAAQ,CAAC;IAE5D,IAAI;MACF,IACEiF,OAAO,CACL,gDAAgD,IAAI,CAACzF,MAAM,CAACC,IAAI,mCAAmC,CACpG,EACD;QACAgB,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;QAE7D,IAAI,CAACb,OAAO,GAAG,IAAI;QACnB,IAAI,CAACE,KAAK,GAAG,IAAI;QAEjB;QACAmB,UAAU,CAAC,MAAK;UACd,IAAI,CAAChC,aAAa,CAACiG,YAAY,CAACnF,QAAQ,CAAC,CAACwB,SAAS,CAAC;YAClDC,IAAI,EAAGoC,QAAQ,IAAI;cACjBpD,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEmD,QAAQ,CAAC;cAC/D,IAAI,CAAChE,OAAO,GAAG,KAAK;cACpB,IAAI,CAACN,mBAAmB,CAACuE,WAAW,CAClC,aAAa,IAAI,CAACtE,MAAM,CAACC,IAAI,gCAAgC,CAC9D;cAED;cACAyB,UAAU,CAAC,MAAK;gBACd,IAAI,CAAC5B,MAAM,CAACyE,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;cAC1C,CAAC,EAAE,GAAG,CAAC;YACT,CAAC;YACDhE,KAAK,EAAGA,KAAK,IAAI;cACfU,OAAO,CAACV,KAAK,CACX,4CAA4C,EAC5CA,KAAK,CACN;cACDU,OAAO,CAACV,KAAK,CAAC,sBAAsB,EAAE;gBACpCmF,MAAM,EAAEnF,KAAK,CAACmF,MAAM;gBACpBlB,OAAO,EAAEjE,KAAK,CAACiE,OAAO;gBACtBjE,KAAK,EAAEA;eACR,CAAC;cAEF,IAAI,CAACF,OAAO,GAAG,KAAK;cACpB,IAAI,CAACE,KAAK,GAAG,qCACXA,KAAK,CAACiE,OAAO,IAAI,iBACnB,EAAE;cACF,IAAI,CAACzE,mBAAmB,CAAC0E,SAAS,CAChC,kCAAkC,IAAI,CAAClE,KAAK,EAAE,CAC/C;YACH;WACD,CAAC;QACJ,CAAC,EAAE,GAAG,CAAC;OACR,MAAM;QACLU,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;;KAEvD,CAAC,OAAOX,KAAU,EAAE;MACnBU,OAAO,CAACV,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,IAAI,CAACA,KAAK,GAAG,cAAcA,KAAK,EAAEiE,OAAO,IAAI,iBAAiB,EAAE;MAChE,IAAI,CAACzE,mBAAmB,CAAC0E,SAAS,CAAC,cAAc,IAAI,CAAClE,KAAK,EAAE,CAAC;;EAElE;CACD;AAlqBYf,mBAAmB,GAAAoG,UAAA,EAL/BrG,SAAS,CAAC;EACTsG,QAAQ,EAAE,iBAAiB;EAC3BC,WAAW,EAAE,8BAA8B;EAC3CC,SAAS,EAAE,CAAC,6BAA6B;CAC1C,CAAC,C,EACWvG,mBAAmB,CAkqB/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}