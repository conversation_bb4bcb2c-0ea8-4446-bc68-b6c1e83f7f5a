{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-24 22:44:44"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-24 22:44:44"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-24 22:45:21"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-24 22:45:21"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-24 23:13:21"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-24 23:13:21"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-24 23:15:15","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 12ms","timestamp":"2025-05-24 23:15:15"}
{"level":"http","message":"POST / 200 - 35ms","timestamp":"2025-05-24 23:15:15"}
{"level":"warn","message":"Unauthorized connection attempt - No token provided","timestamp":"2025-05-24 23:15:15"}
{"level":"warn","message":"Unauthorized connection attempt - No token provided","timestamp":"2025-05-24 23:15:16"}
{"level":"warn","message":"Unauthorized connection attempt - No token provided","timestamp":"2025-05-24 23:15:19"}
{"level":"warn","message":"Unauthorized connection attempt - No token provided","timestamp":"2025-05-24 23:15:24"}
{"level":"warn","message":"Unauthorized connection attempt - No token provided","timestamp":"2025-05-24 23:15:34"}
{"level":"http","message":"POST /login 200 - 287ms","timestamp":"2025-05-24 23:15:36"}
{"level":"http","message":"GET / 200 - 12ms","timestamp":"2025-05-24 23:15:44"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-24 23:15:45","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-24 23:15:45"}
{"level":"http","message":"POST / 200 - 17ms","timestamp":"2025-05-24 23:15:45"}
{"level":"http","message":"GET /user/68294132b5d5c86fd2e56e23 200 - 103ms","timestamp":"2025-05-24 23:15:47"}
{"level":"http","message":"GET /user/68294132b5d5c86fd2e56e23 200 - 42ms","timestamp":"2025-05-24 23:15:48"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversations {\n  getConversations {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    lastMessage {\n      id\n      content\n      timestamp\n      isRead\n      sender {\n        id\n        username\n      }\n    }\n    unreadCount\n    updatedAt\n  }\n}","timestamp":"2025-05-24 23:15:49","variables":{}}
{"level":"warn","message":"[MessageService] getBulkUnreadCounts called with empty conversationIds","timestamp":"2025-05-24 23:15:49"}
{"level":"http","message":"GraphQL anonymous completed in 40ms","timestamp":"2025-05-24 23:15:49"}
{"level":"http","message":"POST / 200 - 61ms","timestamp":"2025-05-24 23:15:49"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetAllUsers($search: String, $page: Int, $limit: Int, $sortBy: String, $sortOrder: String, $isOnline: Boolean) {\n  getAllUsers(\n    search: $search\n    page: $page\n    limit: $limit\n    sortBy: $sortBy\n    sortOrder: $sortOrder\n    isOnline: $isOnline\n  ) {\n    users {\n      id\n      username\n      email\n      image\n      isOnline\n      lastActive\n    }\n    totalCount\n    totalPages\n    currentPage\n    hasNextPage\n    hasPreviousPage\n  }\n}","timestamp":"2025-05-24 23:15:52","variables":{"isOnline":false,"limit":10,"page":1,"search":"","sortBy":"username","sortOrder":"asc"}}
{"level":"http","message":"GraphQL anonymous completed in 149ms","timestamp":"2025-05-24 23:15:52"}
{"level":"http","message":"POST / 200 - 173ms","timestamp":"2025-05-24 23:15:52"}
{"level":"info","message":"WebSocket connection authenticated for user 68294132b5d5c86fd2e56e23","timestamp":"2025-05-24 23:15:53"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-24 23:15:53"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-24 23:15:53"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-24 23:15:53"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-24 23:15:53"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-24 23:15:53"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-24 23:15:53"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-24 23:15:53"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation CreateConversation($userId: ID!) {\n  createConversation(userId: $userId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    lastMessage {\n      id\n      content\n      timestamp\n    }\n    unreadCount\n    updatedAt\n  }\n}","timestamp":"2025-05-24 23:15:54","variables":{"userId":"68294542b5d5c86fd2e56e4b"}}
{"level":"info","message":"[MessageService] New conversation created: 6832451afe5136e30fe9dfaf","timestamp":"2025-05-24 23:15:54"}
{"level":"http","message":"GraphQL anonymous completed in 133ms","timestamp":"2025-05-24 23:15:54"}
{"level":"http","message":"POST / 200 - 147ms","timestamp":"2025-05-24 23:15:54"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetVoiceMessages {\n  getVoiceMessages {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    endTime\n    duration\n    conversationId\n    metadata\n  }\n}","timestamp":"2025-05-24 23:15:54","variables":{}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-24 23:15:54","variables":{"conversationId":"6832451afe5136e30fe9dfaf","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6832451afe5136e30fe9dfaf, userId=68294132b5d5c86fd2e56e23","timestamp":"2025-05-24 23:15:55"}
{"level":"http","message":"GraphQL anonymous completed in 103ms","timestamp":"2025-05-24 23:15:55"}
{"level":"http","message":"POST / 200 - 129ms","timestamp":"2025-05-24 23:15:55"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6832451afe5136e30fe9dfaf, unread: 0, messages: 0","timestamp":"2025-05-24 23:15:55"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6832451afe5136e30fe9dfaf, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=68294132b5d5c86fd2e56e23, offset=0","timestamp":"2025-05-24 23:15:55"}
{"level":"info","message":"[MessageService] Retrieved 0 messages","timestamp":"2025-05-24 23:15:55"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-24 23:15:55"}
{"level":"http","message":"GraphQL anonymous completed in 134ms","timestamp":"2025-05-24 23:15:55"}
{"level":"http","message":"POST / 200 - 157ms","timestamp":"2025-05-24 23:15:55"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-24 23:15:55"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-24 23:15:55"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-24 23:15:55"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation SendMessage($receiverId: ID!, $content: String, $file: Upload, $type: MessageType, $metadata: JSON) {\n  sendMessage(\n    receiverId: $receiverId\n    content: $content\n    file: $file\n    type: $type\n    metadata: $metadata\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    conversation {\n      id\n    }\n    attachments {\n      url\n      type\n      duration\n    }\n    metadata\n  }\n}","timestamp":"2025-05-24 23:15:59","variables":{"content":"Bonjour!","receiverId":"68294542b5d5c86fd2e56e4b","type":"TEXT"}}
{"level":"info","message":"[MessageService] Starting sendMessage flow: senderId=68294132b5d5c86fd2e56e23, receiverId=68294542b5d5c86fd2e56e4b, type=TEXT, hasMetadata=false","timestamp":"2025-05-24 23:15:59"}
{"level":"info","message":"[MessageService] Message saved successfully: 6832451ffe5136e30fe9dfc2","timestamp":"2025-05-24 23:15:59"}
{"level":"info","message":"[MessageService] Message flow completed successfully: messageId=6832451ffe5136e30fe9dfc2","timestamp":"2025-05-24 23:15:59"}
{"level":"http","message":"GraphQL anonymous completed in 154ms","timestamp":"2025-05-24 23:15:59"}
{"level":"http","message":"POST / 200 - 164ms","timestamp":"2025-05-24 23:15:59"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation InitiateCall($recipientId: ID!, $callType: CallType!, $callId: String!, $offer: String!, $conversationId: ID, $options: CallOptions) {\n  initiateCall(\n    recipientId: $recipientId\n    callType: $callType\n    callId: $callId\n    offer: $offer\n    conversationId: $conversationId\n    options: $options\n  ) {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    conversationId\n  }\n}","timestamp":"2025-05-24 23:16:07","variables":{"callId":"17481249672175s0rzfm","callType":"VIDEO","conversationId":"6832451afe5136e30fe9dfaf","offer":"{\"sdp\":\"v=0\\r\\no=- 8022834030955680219 2 IN IP4 127.0.0.1\\r\\ns=-\\r\\nt=0 0\\r\\na=group:BUNDLE 0 1\\r\\na=extmap-allow-mixed\\r\\na=msid-semantic: WMS d6c46c7b-bcf4-44c3-b4ab-3a4533452cc9\\r\\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 9 0 8 13 110 126\\r\\nc=IN IP4 0.0.0.0\\r\\na=rtcp:9 IN IP4 0.0.0.0\\r\\na=ice-ufrag:yTc8\\r\\na=ice-pwd:cTQXb7KVtLGBW+VSiTONJydZ\\r\\na=ice-options:trickle\\r\\na=fingerprint:sha-256 2A:53:E2:C6:26:C1:DA:92:5A:5B:D0:41:DA:E1:62:97:51:59:CB:AB:C8:6B:25:92:24:CF:A3:FB:1E:65:61:14\\r\\na=setup:actpass\\r\\na=mid:0\\r\\na=extmap:1 urn:ietf:params:rtp-hdrext:ssrc-audio-level\\r\\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\\r\\na=extmap:3 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\\r\\na=extmap:4 urn:ietf:params:rtp-hdrext:sdes:mid\\r\\na=sendrecv\\r\\na=msid:d6c46c7b-bcf4-44c3-b4ab-3a4533452cc9 c41c8789-6141-43a6-88a9-8688ebb257bd\\r\\na=rtcp-mux\\r\\na=rtcp-rsize\\r\\na=rtpmap:111 opus/48000/2\\r\\na=rtcp-fb:111 transport-cc\\r\\na=fmtp:111 minptime=10;useinbandfec=1\\r\\na=rtpmap:63 red/48000/2\\r\\na=fmtp:63 111/111\\r\\na=rtpmap:9 G722/8000\\r\\na=rtpmap:0 PCMU/8000\\r\\na=rtpmap:8 PCMA/8000\\r\\na=rtpmap:13 CN/8000\\r\\na=rtpmap:110 telephone-event/48000\\r\\na=rtpmap:126 telephone-event/8000\\r\\na=ssrc:3340463246 cname:DTurHzVhV13xEoyr\\r\\na=ssrc:3340463246 msid:d6c46c7b-bcf4-44c3-b4ab-3a4533452cc9 c41c8789-6141-43a6-88a9-8688ebb257bd\\r\\nm=video 9 UDP/TLS/RTP/SAVPF 96 97 103 104 107 108 109 114 115 116 117 118 39 40 45 46 98 99 100 101 119 120 49 50 123 124 125\\r\\nc=IN IP4 0.0.0.0\\r\\na=rtcp:9 IN IP4 0.0.0.0\\r\\na=ice-ufrag:yTc8\\r\\na=ice-pwd:cTQXb7KVtLGBW+VSiTONJydZ\\r\\na=ice-options:trickle\\r\\na=fingerprint:sha-256 2A:53:E2:C6:26:C1:DA:92:5A:5B:D0:41:DA:E1:62:97:51:59:CB:AB:C8:6B:25:92:24:CF:A3:FB:1E:65:61:14\\r\\na=setup:actpass\\r\\na=mid:1\\r\\na=extmap:14 urn:ietf:params:rtp-hdrext:toffset\\r\\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\\r\\na=extmap:13 urn:3gpp:video-orientation\\r\\na=extmap:3 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\\r\\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\\r\\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\\r\\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\\r\\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\\r\\na=extmap:4 urn:ietf:params:rtp-hdrext:sdes:mid\\r\\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\\r\\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\\r\\na=sendrecv\\r\\na=msid:d6c46c7b-bcf4-44c3-b4ab-3a4533452cc9 ce0b9760-b0b6-41f5-96e2-31d4f1d1a9fa\\r\\na=rtcp-mux\\r\\na=rtcp-rsize\\r\\na=rtpmap:96 VP8/90000\\r\\na=rtcp-fb:96 goog-remb\\r\\na=rtcp-fb:96 transport-cc\\r\\na=rtcp-fb:96 ccm fir\\r\\na=rtcp-fb:96 nack\\r\\na=rtcp-fb:96 nack pli\\r\\na=rtpmap:97 rtx/90000\\r\\na=fmtp:97 apt=96\\r\\na=rtpmap:103 H264/90000\\r\\na=rtcp-fb:103 goog-remb\\r\\na=rtcp-fb:103 transport-cc\\r\\na=rtcp-fb:103 ccm fir\\r\\na=rtcp-fb:103 nack\\r\\na=rtcp-fb:103 nack pli\\r\\na=fmtp:103 level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=42001f\\r\\na=rtpmap:104 rtx/90000\\r\\na=fmtp:104 apt=103\\r\\na=rtpmap:107 H264/90000\\r\\na=rtcp-fb:107 goog-remb\\r\\na=rtcp-fb:107 transport-cc\\r\\na=rtcp-fb:107 ccm fir\\r\\na=rtcp-fb:107 nack\\r\\na=rtcp-fb:107 nack pli\\r\\na=fmtp:107 level-asymmetry-allowed=1;packetization-mode=0;profile-level-id=42001f\\r\\na=rtpmap:108 rtx/90000\\r\\na=fmtp:108 apt=107\\r\\na=rtpmap:109 H264/90000\\r\\na=rtcp-fb:109 goog-remb\\r\\na=rtcp-fb:109 transport-cc\\r\\na=rtcp-fb:109 ccm fir\\r\\na=rtcp-fb:109 nack\\r\\na=rtcp-fb:109 nack pli\\r\\na=fmtp:109 level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=42e01f\\r\\na=rtpmap:114 rtx/90000\\r\\na=fmtp:114 apt=109\\r\\na=rtpmap:115 H264/90000\\r\\na=rtcp-fb:115 goog-remb\\r\\na=rtcp-fb:115 transport-cc\\r\\na=rtcp-fb:115 ccm fir\\r\\na=rtcp-fb:115 nack\\r\\na=rtcp-fb:115 nack pli\\r\\na=fmtp:115 level-asymmetry-allowed=1;packetization-mode=0;profile-level-id=42e01f\\r\\na=rtpmap:116 rtx/90000\\r\\na=fmtp:116 apt=115\\r\\na=rtpmap:117 H264/90000\\r\\na=rtcp-fb:117 goog-remb\\r\\na=rtcp-fb:117 transport-cc\\r\\na=rtcp-fb:117 ccm fir\\r\\na=rtcp-fb:117 nack\\r\\na=rtcp-fb:117 nack pli\\r\\na=fmtp:117 level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=4d001f\\r\\na=rtpmap:118 rtx/90000\\r\\na=fmtp:118 apt=117\\r\\na=rtpmap:39 H264/90000\\r\\na=rtcp-fb:39 goog-remb\\r\\na=rtcp-fb:39 transport-cc\\r\\na=rtcp-fb:39 ccm fir\\r\\na=rtcp-fb:39 nack\\r\\na=rtcp-fb:39 nack pli\\r\\na=fmtp:39 level-asymmetry-allowed=1;packetization-mode=0;profile-level-id=4d001f\\r\\na=rtpmap:40 rtx/90000\\r\\na=fmtp:40 apt=39\\r\\na=rtpmap:45 AV1/90000\\r\\na=rtcp-fb:45 goog-remb\\r\\na=rtcp-fb:45 transport-cc\\r\\na=rtcp-fb:45 ccm fir\\r\\na=rtcp-fb:45 nack\\r\\na=rtcp-fb:45 nack pli\\r\\na=fmtp:45 level-idx=5;profile=0;tier=0\\r\\na=rtpmap:46 rtx/90000\\r\\na=fmtp:46 apt=45\\r\\na=rtpmap:98 VP9/90000\\r\\na=rtcp-fb:98 goog-remb\\r\\na=rtcp-fb:98 transport-cc\\r\\na=rtcp-fb:98 ccm fir\\r\\na=rtcp-fb:98 nack\\r\\na=rtcp-fb:98 nack pli\\r\\na=fmtp:98 profile-id=0\\r\\na=rtpmap:99 rtx/90000\\r\\na=fmtp:99 apt=98\\r\\na=rtpmap:100 VP9/90000\\r\\na=rtcp-fb:100 goog-remb\\r\\na=rtcp-fb:100 transport-cc\\r\\na=rtcp-fb:100 ccm fir\\r\\na=rtcp-fb:100 nack\\r\\na=rtcp-fb:100 nack pli\\r\\na=fmtp:100 profile-id=2\\r\\na=rtpmap:101 rtx/90000\\r\\na=fmtp:101 apt=100\\r\\na=rtpmap:119 H264/90000\\r\\na=rtcp-fb:119 goog-remb\\r\\na=rtcp-fb:119 transport-cc\\r\\na=rtcp-fb:119 ccm fir\\r\\na=rtcp-fb:119 nack\\r\\na=rtcp-fb:119 nack pli\\r\\na=fmtp:119 level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=64001f\\r\\na=rtpmap:120 rtx/90000\\r\\na=fmtp:120 apt=119\\r\\na=rtpmap:49 H265/90000\\r\\na=rtcp-fb:49 goog-remb\\r\\na=rtcp-fb:49 transport-cc\\r\\na=rtcp-fb:49 ccm fir\\r\\na=rtcp-fb:49 nack\\r\\na=rtcp-fb:49 nack pli\\r\\na=fmtp:49 level-id=93;profile-id=1;tier-flag=0;tx-mode=SRST\\r\\na=rtpmap:50 rtx/90000\\r\\na=fmtp:50 apt=49\\r\\na=rtpmap:123 red/90000\\r\\na=rtpmap:124 rtx/90000\\r\\na=fmtp:124 apt=123\\r\\na=rtpmap:125 ulpfec/90000\\r\\na=ssrc-group:FID 1406181799 376275677\\r\\na=ssrc:1406181799 cname:DTurHzVhV13xEoyr\\r\\na=ssrc:1406181799 msid:d6c46c7b-bcf4-44c3-b4ab-3a4533452cc9 ce0b9760-b0b6-41f5-96e2-31d4f1d1a9fa\\r\\na=ssrc:376275677 cname:DTurHzVhV13xEoyr\\r\\na=ssrc:376275677 msid:d6c46c7b-bcf4-44c3-b4ab-3a4533452cc9 ce0b9760-b0b6-41f5-96e2-31d4f1d1a9fa\\r\\n\",\"type\":\"offer\"}","recipientId":"68294542b5d5c86fd2e56e4b"}}
{"level":"warn","message":"GraphQL anonymous completed with errors in 6ms","timestamp":"2025-05-24 23:16:07"}
{"level":"http","message":"POST / 200 - 15ms","timestamp":"2025-05-24 23:16:07"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation InitiateCall($recipientId: ID!, $callType: CallType!, $callId: String!, $offer: String!, $conversationId: ID, $options: CallOptions) {\n  initiateCall(\n    recipientId: $recipientId\n    callType: $callType\n    callId: $callId\n    offer: $offer\n    conversationId: $conversationId\n    options: $options\n  ) {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    conversationId\n  }\n}","timestamp":"2025-05-24 23:16:07","variables":{"callId":"17481249672175s0rzfm","callType":"VIDEO","conversationId":"6832451afe5136e30fe9dfaf","offer":"{\"sdp\":\"v=0\\r\\no=- 8022834030955680219 2 IN IP4 127.0.0.1\\r\\ns=-\\r\\nt=0 0\\r\\na=group:BUNDLE 0 1\\r\\na=extmap-allow-mixed\\r\\na=msid-semantic: WMS d6c46c7b-bcf4-44c3-b4ab-3a4533452cc9\\r\\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 9 0 8 13 110 126\\r\\nc=IN IP4 0.0.0.0\\r\\na=rtcp:9 IN IP4 0.0.0.0\\r\\na=ice-ufrag:yTc8\\r\\na=ice-pwd:cTQXb7KVtLGBW+VSiTONJydZ\\r\\na=ice-options:trickle\\r\\na=fingerprint:sha-256 2A:53:E2:C6:26:C1:DA:92:5A:5B:D0:41:DA:E1:62:97:51:59:CB:AB:C8:6B:25:92:24:CF:A3:FB:1E:65:61:14\\r\\na=setup:actpass\\r\\na=mid:0\\r\\na=extmap:1 urn:ietf:params:rtp-hdrext:ssrc-audio-level\\r\\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\\r\\na=extmap:3 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\\r\\na=extmap:4 urn:ietf:params:rtp-hdrext:sdes:mid\\r\\na=sendrecv\\r\\na=msid:d6c46c7b-bcf4-44c3-b4ab-3a4533452cc9 c41c8789-6141-43a6-88a9-8688ebb257bd\\r\\na=rtcp-mux\\r\\na=rtcp-rsize\\r\\na=rtpmap:111 opus/48000/2\\r\\na=rtcp-fb:111 transport-cc\\r\\na=fmtp:111 minptime=10;useinbandfec=1\\r\\na=rtpmap:63 red/48000/2\\r\\na=fmtp:63 111/111\\r\\na=rtpmap:9 G722/8000\\r\\na=rtpmap:0 PCMU/8000\\r\\na=rtpmap:8 PCMA/8000\\r\\na=rtpmap:13 CN/8000\\r\\na=rtpmap:110 telephone-event/48000\\r\\na=rtpmap:126 telephone-event/8000\\r\\na=ssrc:3340463246 cname:DTurHzVhV13xEoyr\\r\\na=ssrc:3340463246 msid:d6c46c7b-bcf4-44c3-b4ab-3a4533452cc9 c41c8789-6141-43a6-88a9-8688ebb257bd\\r\\nm=video 9 UDP/TLS/RTP/SAVPF 96 97 103 104 107 108 109 114 115 116 117 118 39 40 45 46 98 99 100 101 119 120 49 50 123 124 125\\r\\nc=IN IP4 0.0.0.0\\r\\na=rtcp:9 IN IP4 0.0.0.0\\r\\na=ice-ufrag:yTc8\\r\\na=ice-pwd:cTQXb7KVtLGBW+VSiTONJydZ\\r\\na=ice-options:trickle\\r\\na=fingerprint:sha-256 2A:53:E2:C6:26:C1:DA:92:5A:5B:D0:41:DA:E1:62:97:51:59:CB:AB:C8:6B:25:92:24:CF:A3:FB:1E:65:61:14\\r\\na=setup:actpass\\r\\na=mid:1\\r\\na=extmap:14 urn:ietf:params:rtp-hdrext:toffset\\r\\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\\r\\na=extmap:13 urn:3gpp:video-orientation\\r\\na=extmap:3 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\\r\\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\\r\\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\\r\\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\\r\\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\\r\\na=extmap:4 urn:ietf:params:rtp-hdrext:sdes:mid\\r\\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\\r\\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\\r\\na=sendrecv\\r\\na=msid:d6c46c7b-bcf4-44c3-b4ab-3a4533452cc9 ce0b9760-b0b6-41f5-96e2-31d4f1d1a9fa\\r\\na=rtcp-mux\\r\\na=rtcp-rsize\\r\\na=rtpmap:96 VP8/90000\\r\\na=rtcp-fb:96 goog-remb\\r\\na=rtcp-fb:96 transport-cc\\r\\na=rtcp-fb:96 ccm fir\\r\\na=rtcp-fb:96 nack\\r\\na=rtcp-fb:96 nack pli\\r\\na=rtpmap:97 rtx/90000\\r\\na=fmtp:97 apt=96\\r\\na=rtpmap:103 H264/90000\\r\\na=rtcp-fb:103 goog-remb\\r\\na=rtcp-fb:103 transport-cc\\r\\na=rtcp-fb:103 ccm fir\\r\\na=rtcp-fb:103 nack\\r\\na=rtcp-fb:103 nack pli\\r\\na=fmtp:103 level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=42001f\\r\\na=rtpmap:104 rtx/90000\\r\\na=fmtp:104 apt=103\\r\\na=rtpmap:107 H264/90000\\r\\na=rtcp-fb:107 goog-remb\\r\\na=rtcp-fb:107 transport-cc\\r\\na=rtcp-fb:107 ccm fir\\r\\na=rtcp-fb:107 nack\\r\\na=rtcp-fb:107 nack pli\\r\\na=fmtp:107 level-asymmetry-allowed=1;packetization-mode=0;profile-level-id=42001f\\r\\na=rtpmap:108 rtx/90000\\r\\na=fmtp:108 apt=107\\r\\na=rtpmap:109 H264/90000\\r\\na=rtcp-fb:109 goog-remb\\r\\na=rtcp-fb:109 transport-cc\\r\\na=rtcp-fb:109 ccm fir\\r\\na=rtcp-fb:109 nack\\r\\na=rtcp-fb:109 nack pli\\r\\na=fmtp:109 level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=42e01f\\r\\na=rtpmap:114 rtx/90000\\r\\na=fmtp:114 apt=109\\r\\na=rtpmap:115 H264/90000\\r\\na=rtcp-fb:115 goog-remb\\r\\na=rtcp-fb:115 transport-cc\\r\\na=rtcp-fb:115 ccm fir\\r\\na=rtcp-fb:115 nack\\r\\na=rtcp-fb:115 nack pli\\r\\na=fmtp:115 level-asymmetry-allowed=1;packetization-mode=0;profile-level-id=42e01f\\r\\na=rtpmap:116 rtx/90000\\r\\na=fmtp:116 apt=115\\r\\na=rtpmap:117 H264/90000\\r\\na=rtcp-fb:117 goog-remb\\r\\na=rtcp-fb:117 transport-cc\\r\\na=rtcp-fb:117 ccm fir\\r\\na=rtcp-fb:117 nack\\r\\na=rtcp-fb:117 nack pli\\r\\na=fmtp:117 level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=4d001f\\r\\na=rtpmap:118 rtx/90000\\r\\na=fmtp:118 apt=117\\r\\na=rtpmap:39 H264/90000\\r\\na=rtcp-fb:39 goog-remb\\r\\na=rtcp-fb:39 transport-cc\\r\\na=rtcp-fb:39 ccm fir\\r\\na=rtcp-fb:39 nack\\r\\na=rtcp-fb:39 nack pli\\r\\na=fmtp:39 level-asymmetry-allowed=1;packetization-mode=0;profile-level-id=4d001f\\r\\na=rtpmap:40 rtx/90000\\r\\na=fmtp:40 apt=39\\r\\na=rtpmap:45 AV1/90000\\r\\na=rtcp-fb:45 goog-remb\\r\\na=rtcp-fb:45 transport-cc\\r\\na=rtcp-fb:45 ccm fir\\r\\na=rtcp-fb:45 nack\\r\\na=rtcp-fb:45 nack pli\\r\\na=fmtp:45 level-idx=5;profile=0;tier=0\\r\\na=rtpmap:46 rtx/90000\\r\\na=fmtp:46 apt=45\\r\\na=rtpmap:98 VP9/90000\\r\\na=rtcp-fb:98 goog-remb\\r\\na=rtcp-fb:98 transport-cc\\r\\na=rtcp-fb:98 ccm fir\\r\\na=rtcp-fb:98 nack\\r\\na=rtcp-fb:98 nack pli\\r\\na=fmtp:98 profile-id=0\\r\\na=rtpmap:99 rtx/90000\\r\\na=fmtp:99 apt=98\\r\\na=rtpmap:100 VP9/90000\\r\\na=rtcp-fb:100 goog-remb\\r\\na=rtcp-fb:100 transport-cc\\r\\na=rtcp-fb:100 ccm fir\\r\\na=rtcp-fb:100 nack\\r\\na=rtcp-fb:100 nack pli\\r\\na=fmtp:100 profile-id=2\\r\\na=rtpmap:101 rtx/90000\\r\\na=fmtp:101 apt=100\\r\\na=rtpmap:119 H264/90000\\r\\na=rtcp-fb:119 goog-remb\\r\\na=rtcp-fb:119 transport-cc\\r\\na=rtcp-fb:119 ccm fir\\r\\na=rtcp-fb:119 nack\\r\\na=rtcp-fb:119 nack pli\\r\\na=fmtp:119 level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=64001f\\r\\na=rtpmap:120 rtx/90000\\r\\na=fmtp:120 apt=119\\r\\na=rtpmap:49 H265/90000\\r\\na=rtcp-fb:49 goog-remb\\r\\na=rtcp-fb:49 transport-cc\\r\\na=rtcp-fb:49 ccm fir\\r\\na=rtcp-fb:49 nack\\r\\na=rtcp-fb:49 nack pli\\r\\na=fmtp:49 level-id=93;profile-id=1;tier-flag=0;tx-mode=SRST\\r\\na=rtpmap:50 rtx/90000\\r\\na=fmtp:50 apt=49\\r\\na=rtpmap:123 red/90000\\r\\na=rtpmap:124 rtx/90000\\r\\na=fmtp:124 apt=123\\r\\na=rtpmap:125 ulpfec/90000\\r\\na=ssrc-group:FID 1406181799 376275677\\r\\na=ssrc:1406181799 cname:DTurHzVhV13xEoyr\\r\\na=ssrc:1406181799 msid:d6c46c7b-bcf4-44c3-b4ab-3a4533452cc9 ce0b9760-b0b6-41f5-96e2-31d4f1d1a9fa\\r\\na=ssrc:376275677 cname:DTurHzVhV13xEoyr\\r\\na=ssrc:376275677 msid:d6c46c7b-bcf4-44c3-b4ab-3a4533452cc9 ce0b9760-b0b6-41f5-96e2-31d4f1d1a9fa\\r\\n\",\"type\":\"offer\"}","recipientId":"68294542b5d5c86fd2e56e4b"}}
{"level":"warn","message":"GraphQL anonymous completed with errors in 1ms","timestamp":"2025-05-24 23:16:07"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-24 23:16:07"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation InitiateCall($recipientId: ID!, $callType: CallType!, $callId: String!, $offer: String!, $conversationId: ID, $options: CallOptions) {\n  initiateCall(\n    recipientId: $recipientId\n    callType: $callType\n    callId: $callId\n    offer: $offer\n    conversationId: $conversationId\n    options: $options\n  ) {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    conversationId\n  }\n}","timestamp":"2025-05-24 23:16:15","variables":{"callId":"1748124974996xxjuszz","callType":"AUDIO","conversationId":"6832451afe5136e30fe9dfaf","offer":"{\"sdp\":\"v=0\\r\\no=- 1452722612022614353 2 IN IP4 127.0.0.1\\r\\ns=-\\r\\nt=0 0\\r\\na=group:BUNDLE 0\\r\\na=extmap-allow-mixed\\r\\na=msid-semantic: WMS 3c133dcc-32bd-4093-be27-0962d8a8f45f\\r\\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 9 0 8 13 110 126\\r\\nc=IN IP4 0.0.0.0\\r\\na=rtcp:9 IN IP4 0.0.0.0\\r\\na=ice-ufrag:kPcu\\r\\na=ice-pwd:sjMHv/GA8XSp6hpzBj2jnf/N\\r\\na=ice-options:trickle\\r\\na=fingerprint:sha-256 E4:1D:38:F3:E7:F2:EE:D9:46:67:36:3C:8F:4D:59:2F:FD:38:EF:A4:9C:42:7F:10:08:29:18:46:54:90:53:E6\\r\\na=setup:actpass\\r\\na=mid:0\\r\\na=extmap:1 urn:ietf:params:rtp-hdrext:ssrc-audio-level\\r\\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\\r\\na=extmap:3 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\\r\\na=extmap:4 urn:ietf:params:rtp-hdrext:sdes:mid\\r\\na=sendrecv\\r\\na=msid:3c133dcc-32bd-4093-be27-0962d8a8f45f 375cd4ba-8487-4085-976a-2a1726499a42\\r\\na=rtcp-mux\\r\\na=rtcp-rsize\\r\\na=rtpmap:111 opus/48000/2\\r\\na=rtcp-fb:111 transport-cc\\r\\na=fmtp:111 minptime=10;useinbandfec=1\\r\\na=rtpmap:63 red/48000/2\\r\\na=fmtp:63 111/111\\r\\na=rtpmap:9 G722/8000\\r\\na=rtpmap:0 PCMU/8000\\r\\na=rtpmap:8 PCMA/8000\\r\\na=rtpmap:13 CN/8000\\r\\na=rtpmap:110 telephone-event/48000\\r\\na=rtpmap:126 telephone-event/8000\\r\\na=ssrc:3319372313 cname:M3+U7GYDzWVF8E7L\\r\\na=ssrc:3319372313 msid:3c133dcc-32bd-4093-be27-0962d8a8f45f 375cd4ba-8487-4085-976a-2a1726499a42\\r\\n\",\"type\":\"offer\"}","recipientId":"68294542b5d5c86fd2e56e4b"}}
{"level":"warn","message":"GraphQL anonymous completed with errors in 3ms","timestamp":"2025-05-24 23:16:15"}
{"level":"http","message":"POST / 200 - 8ms","timestamp":"2025-05-24 23:16:15"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-24 23:16:15","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-24 23:16:15"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-24 23:16:15"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation InitiateCall($recipientId: ID!, $callType: CallType!, $callId: String!, $offer: String!, $conversationId: ID, $options: CallOptions) {\n  initiateCall(\n    recipientId: $recipientId\n    callType: $callType\n    callId: $callId\n    offer: $offer\n    conversationId: $conversationId\n    options: $options\n  ) {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    conversationId\n  }\n}","timestamp":"2025-05-24 23:16:15","variables":{"callId":"1748124974996xxjuszz","callType":"AUDIO","conversationId":"6832451afe5136e30fe9dfaf","offer":"{\"sdp\":\"v=0\\r\\no=- 1452722612022614353 2 IN IP4 127.0.0.1\\r\\ns=-\\r\\nt=0 0\\r\\na=group:BUNDLE 0\\r\\na=extmap-allow-mixed\\r\\na=msid-semantic: WMS 3c133dcc-32bd-4093-be27-0962d8a8f45f\\r\\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 9 0 8 13 110 126\\r\\nc=IN IP4 0.0.0.0\\r\\na=rtcp:9 IN IP4 0.0.0.0\\r\\na=ice-ufrag:kPcu\\r\\na=ice-pwd:sjMHv/GA8XSp6hpzBj2jnf/N\\r\\na=ice-options:trickle\\r\\na=fingerprint:sha-256 E4:1D:38:F3:E7:F2:EE:D9:46:67:36:3C:8F:4D:59:2F:FD:38:EF:A4:9C:42:7F:10:08:29:18:46:54:90:53:E6\\r\\na=setup:actpass\\r\\na=mid:0\\r\\na=extmap:1 urn:ietf:params:rtp-hdrext:ssrc-audio-level\\r\\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\\r\\na=extmap:3 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\\r\\na=extmap:4 urn:ietf:params:rtp-hdrext:sdes:mid\\r\\na=sendrecv\\r\\na=msid:3c133dcc-32bd-4093-be27-0962d8a8f45f 375cd4ba-8487-4085-976a-2a1726499a42\\r\\na=rtcp-mux\\r\\na=rtcp-rsize\\r\\na=rtpmap:111 opus/48000/2\\r\\na=rtcp-fb:111 transport-cc\\r\\na=fmtp:111 minptime=10;useinbandfec=1\\r\\na=rtpmap:63 red/48000/2\\r\\na=fmtp:63 111/111\\r\\na=rtpmap:9 G722/8000\\r\\na=rtpmap:0 PCMU/8000\\r\\na=rtpmap:8 PCMA/8000\\r\\na=rtpmap:13 CN/8000\\r\\na=rtpmap:110 telephone-event/48000\\r\\na=rtpmap:126 telephone-event/8000\\r\\na=ssrc:3319372313 cname:M3+U7GYDzWVF8E7L\\r\\na=ssrc:3319372313 msid:3c133dcc-32bd-4093-be27-0962d8a8f45f 375cd4ba-8487-4085-976a-2a1726499a42\\r\\n\",\"type\":\"offer\"}","recipientId":"68294542b5d5c86fd2e56e4b"}}
{"level":"warn","message":"GraphQL anonymous completed with errors in 2ms","timestamp":"2025-05-24 23:16:15"}
{"level":"http","message":"POST / 200 - 10ms","timestamp":"2025-05-24 23:16:15"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation SendMessage($receiverId: ID!, $content: String, $file: Upload, $type: MessageType, $metadata: JSON) {\n  sendMessage(\n    receiverId: $receiverId\n    content: $content\n    file: $file\n    type: $type\n    metadata: $metadata\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    conversation {\n      id\n    }\n    attachments {\n      url\n      type\n      duration\n    }\n    metadata\n  }\n}","timestamp":"2025-05-24 23:16:43","variables":{"content":null,"receiverId":"68294542b5d5c86fd2e56e4b","type":"TEXT"}}
{"level":"info","message":"[MessageService] Starting sendMessage flow: senderId=68294132b5d5c86fd2e56e23, receiverId=68294542b5d5c86fd2e56e4b, type=TEXT, hasMetadata=false","timestamp":"2025-05-24 23:16:43"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"68294542b5d5c86fd2e56e4b","senderId":"68294132b5d5c86fd2e56e23","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-24 23:16:43"}
{"level":"warn","message":"GraphQL anonymous completed with errors in 36ms","timestamp":"2025-05-24 23:16:43"}
{"level":"http","message":"POST / 200 - 41ms","timestamp":"2025-05-24 23:16:43"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation SendMessage($receiverId: ID!, $content: String, $file: Upload, $type: MessageType, $metadata: JSON) {\n  sendMessage(\n    receiverId: $receiverId\n    content: $content\n    file: $file\n    type: $type\n    metadata: $metadata\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    conversation {\n      id\n    }\n    attachments {\n      url\n      type\n      duration\n    }\n    metadata\n  }\n}","timestamp":"2025-05-24 23:16:43","variables":{"content":null,"receiverId":"68294542b5d5c86fd2e56e4b","type":"TEXT"}}
{"level":"info","message":"[MessageService] Starting sendMessage flow: senderId=68294132b5d5c86fd2e56e23, receiverId=68294542b5d5c86fd2e56e4b, type=TEXT, hasMetadata=false","timestamp":"2025-05-24 23:16:43"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"68294542b5d5c86fd2e56e4b","senderId":"68294132b5d5c86fd2e56e23","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-24 23:16:43"}
{"level":"warn","message":"GraphQL anonymous completed with errors in 27ms","timestamp":"2025-05-24 23:16:43"}
{"level":"http","message":"POST / 200 - 31ms","timestamp":"2025-05-24 23:16:43"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-24 23:16:45","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-24 23:16:45"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-24 23:16:45"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation SendMessage($receiverId: ID!, $content: String, $file: Upload, $type: MessageType, $metadata: JSON) {\n  sendMessage(\n    receiverId: $receiverId\n    content: $content\n    file: $file\n    type: $type\n    metadata: $metadata\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    conversation {\n      id\n    }\n    attachments {\n      url\n      type\n      duration\n    }\n    metadata\n  }\n}","timestamp":"2025-05-24 23:16:58","variables":{"content":null,"file":{"file":{"encoding":"7bit","filename":"ayoub1.jpg","mimetype":"image/jpeg"},"promise":{}},"receiverId":"68294542b5d5c86fd2e56e4b","type":"IMAGE"}}
{"level":"info","message":"[MessageService] Starting sendMessage flow: senderId=68294132b5d5c86fd2e56e23, receiverId=68294542b5d5c86fd2e56e4b, type=IMAGE, hasMetadata=false","timestamp":"2025-05-24 23:16:58"}
{"level":"info","message":"[MessageService] Message saved successfully: 6832455cfe5136e30fe9dfd9","timestamp":"2025-05-24 23:17:00"}
{"level":"info","message":"[MessageService] Message flow completed successfully: messageId=6832455cfe5136e30fe9dfd9","timestamp":"2025-05-24 23:17:00"}
{"level":"http","message":"GraphQL anonymous completed in 1880ms","timestamp":"2025-05-24 23:17:00"}
{"level":"http","message":"POST / 200 - 1898ms","timestamp":"2025-05-24 23:17:00"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-24 23:17:15","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 2ms","timestamp":"2025-05-24 23:17:15"}
{"level":"http","message":"POST / 200 - 10ms","timestamp":"2025-05-24 23:17:15"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-24 23:17:46","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-24 23:17:46"}
{"level":"http","message":"POST / 200 - 9ms","timestamp":"2025-05-24 23:17:46"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation SendMessage($receiverId: ID!, $content: String, $file: Upload, $type: MessageType, $metadata: JSON) {\n  sendMessage(\n    receiverId: $receiverId\n    content: $content\n    file: $file\n    type: $type\n    metadata: $metadata\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    conversation {\n      id\n    }\n    attachments {\n      url\n      type\n      duration\n    }\n    metadata\n  }\n}","timestamp":"2025-05-24 23:17:56","variables":{"content":"😫","receiverId":"68294542b5d5c86fd2e56e4b","type":"TEXT"}}
{"level":"info","message":"[MessageService] Starting sendMessage flow: senderId=68294132b5d5c86fd2e56e23, receiverId=68294542b5d5c86fd2e56e4b, type=TEXT, hasMetadata=false","timestamp":"2025-05-24 23:17:56"}
{"level":"info","message":"[MessageService] Message saved successfully: 68324594fe5136e30fe9dfe6","timestamp":"2025-05-24 23:17:56"}
{"level":"info","message":"[MessageService] Message flow completed successfully: messageId=68324594fe5136e30fe9dfe6","timestamp":"2025-05-24 23:17:56"}
{"level":"http","message":"GraphQL anonymous completed in 106ms","timestamp":"2025-05-24 23:17:56"}
{"level":"http","message":"POST / 200 - 115ms","timestamp":"2025-05-24 23:17:56"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation SendMessage($receiverId: ID!, $content: String, $file: Upload, $type: MessageType, $metadata: JSON) {\n  sendMessage(\n    receiverId: $receiverId\n    content: $content\n    file: $file\n    type: $type\n    metadata: $metadata\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    conversation {\n      id\n    }\n    attachments {\n      url\n      type\n      duration\n    }\n    metadata\n  }\n}","timestamp":"2025-05-24 23:18:07","variables":{"content":" ","conversationId":"6832451afe5136e30fe9dfaf","file":{"file":{"encoding":"7bit","filename":"voice-message-1748125087695.webm","mimetype":"audio/webm"},"promise":{}},"metadata":{"duration":0,"isVoiceMessage":true,"timestamp":1748125087695},"receiverId":"68294542b5d5c86fd2e56e4b","type":"VOICE_MESSAGE"}}
{"level":"info","message":"[MessageService] Starting sendMessage flow: senderId=68294132b5d5c86fd2e56e23, receiverId=68294542b5d5c86fd2e56e4b, type=VOICE_MESSAGE, hasMetadata=true","timestamp":"2025-05-24 23:18:07"}
{"level":"info","message":"[MessageService] Message saved successfully: 683245a1fe5136e30fe9dff3","timestamp":"2025-05-24 23:18:09"}
{"level":"info","message":"[MessageService] Message flow completed successfully: messageId=683245a1fe5136e30fe9dff3","timestamp":"2025-05-24 23:18:09"}
{"level":"http","message":"GraphQL anonymous completed in 1919ms","timestamp":"2025-05-24 23:18:09"}
{"level":"http","message":"POST / 200 - 1935ms","timestamp":"2025-05-24 23:18:09"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation SendMessage($receiverId: ID!, $content: String, $file: Upload, $type: MessageType, $metadata: JSON) {\n  sendMessage(\n    receiverId: $receiverId\n    content: $content\n    file: $file\n    type: $type\n    metadata: $metadata\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    conversation {\n      id\n    }\n    attachments {\n      url\n      type\n      duration\n    }\n    metadata\n  }\n}","timestamp":"2025-05-24 23:18:15","variables":{"content":null,"receiverId":"68294542b5d5c86fd2e56e4b","type":"TEXT"}}
{"level":"info","message":"[MessageService] Starting sendMessage flow: senderId=68294132b5d5c86fd2e56e23, receiverId=68294542b5d5c86fd2e56e4b, type=TEXT, hasMetadata=false","timestamp":"2025-05-24 23:18:15"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"68294542b5d5c86fd2e56e4b","senderId":"68294132b5d5c86fd2e56e23","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-24 23:18:15"}
{"level":"warn","message":"GraphQL anonymous completed with errors in 32ms","timestamp":"2025-05-24 23:18:15"}
{"level":"http","message":"POST / 200 - 37ms","timestamp":"2025-05-24 23:18:15"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-24 23:18:15","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-24 23:18:15"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-24 23:18:15"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation SendMessage($receiverId: ID!, $content: String, $file: Upload, $type: MessageType, $metadata: JSON) {\n  sendMessage(\n    receiverId: $receiverId\n    content: $content\n    file: $file\n    type: $type\n    metadata: $metadata\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    conversation {\n      id\n    }\n    attachments {\n      url\n      type\n      duration\n    }\n    metadata\n  }\n}","timestamp":"2025-05-24 23:18:15","variables":{"content":null,"receiverId":"68294542b5d5c86fd2e56e4b","type":"TEXT"}}
{"level":"info","message":"[MessageService] Starting sendMessage flow: senderId=68294132b5d5c86fd2e56e23, receiverId=68294542b5d5c86fd2e56e4b, type=TEXT, hasMetadata=false","timestamp":"2025-05-24 23:18:15"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"68294542b5d5c86fd2e56e4b","senderId":"68294132b5d5c86fd2e56e23","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-24 23:18:15"}
{"level":"warn","message":"GraphQL anonymous completed with errors in 28ms","timestamp":"2025-05-24 23:18:15"}
{"level":"http","message":"POST / 200 - 32ms","timestamp":"2025-05-24 23:18:15"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-24 23:18:26"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-24 23:18:27","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-24 23:18:27"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-24 23:18:27"}
{"level":"info","message":"WebSocket connection authenticated for user 68294132b5d5c86fd2e56e23","timestamp":"2025-05-24 23:18:27"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-24 23:18:27"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-24 23:18:27"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-24 23:18:27"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-24 23:18:27"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-24 23:18:27","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 200 - 12ms","timestamp":"2025-05-24 23:18:27"}
{"level":"http","message":"GraphQL anonymous completed in 26ms","timestamp":"2025-05-24 23:18:27"}
{"level":"http","message":"POST / 200 - 30ms","timestamp":"2025-05-24 23:18:27"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetVoiceMessages {\n  getVoiceMessages {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    endTime\n    duration\n    conversationId\n    metadata\n  }\n}","timestamp":"2025-05-24 23:18:27","variables":{}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 27ms","timestamp":"2025-05-24 23:18:27"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-24 23:18:27"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-24 23:18:27"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-24 23:18:27"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-24 23:18:27"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-24 23:18:27","variables":{"conversationId":"6832451afe5136e30fe9dfaf","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6832451afe5136e30fe9dfaf, userId=68294132b5d5c86fd2e56e23","timestamp":"2025-05-24 23:18:27"}
{"level":"http","message":"GraphQL anonymous completed in 40ms","timestamp":"2025-05-24 23:18:27"}
{"level":"http","message":"POST / 200 - 46ms","timestamp":"2025-05-24 23:18:27"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6832451afe5136e30fe9dfaf, unread: 0, messages: 4","timestamp":"2025-05-24 23:18:27"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6832451afe5136e30fe9dfaf, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=68294132b5d5c86fd2e56e23, offset=0","timestamp":"2025-05-24 23:18:27"}
{"level":"info","message":"[MessageService] Retrieved 4 messages","timestamp":"2025-05-24 23:18:27"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-24 23:18:27"}
{"level":"http","message":"GraphQL anonymous completed in 38ms","timestamp":"2025-05-24 23:18:27"}
{"level":"http","message":"POST / 200 - 42ms","timestamp":"2025-05-24 23:18:27"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-24 23:18:27"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-24 23:18:27"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-24 23:18:27"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-24 23:18:43","variables":{"limit":10,"page":1}}
{"level":"http","message":"GraphQL anonymous completed in 49ms","timestamp":"2025-05-24 23:18:43"}
{"level":"http","message":"POST / 200 - 53ms","timestamp":"2025-05-24 23:18:43"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation MarkNotificationsAsRead($notificationIds: [ID!]!) {\n  markNotificationsAsRead(notificationIds: $notificationIds) {\n    success\n    readCount\n    remainingCount\n  }\n}","timestamp":"2025-05-24 23:18:52","variables":{"notificationIds":["683245a1fe5136e30fe9dff7"]}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversations {\n  getConversations {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    lastMessage {\n      id\n      content\n      timestamp\n      isRead\n      sender {\n        id\n        username\n      }\n    }\n    unreadCount\n    updatedAt\n  }\n}","timestamp":"2025-05-24 23:18:52","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 53ms","timestamp":"2025-05-24 23:18:52"}
{"level":"http","message":"POST / 200 - 55ms","timestamp":"2025-05-24 23:18:52"}
{"level":"http","message":"GraphQL anonymous completed in 53ms","timestamp":"2025-05-24 23:18:52"}
{"level":"http","message":"POST / 200 - 64ms","timestamp":"2025-05-24 23:18:52"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation CreateConversation($userId: ID!) {\n  createConversation(userId: $userId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    lastMessage {\n      id\n      content\n      timestamp\n    }\n    unreadCount\n    updatedAt\n  }\n}","timestamp":"2025-05-24 23:18:52","variables":{"userId":"68294132b5d5c86fd2e56e23"}}
{"level":"warn","message":"GraphQL anonymous completed with errors in 2ms","timestamp":"2025-05-24 23:18:52"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-24 23:18:52"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation CreateConversation($userId: ID!) {\n  createConversation(userId: $userId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    lastMessage {\n      id\n      content\n      timestamp\n    }\n    unreadCount\n    updatedAt\n  }\n}","timestamp":"2025-05-24 23:18:52","variables":{"userId":"68294132b5d5c86fd2e56e23"}}
{"level":"warn","message":"GraphQL anonymous completed with errors in 2ms","timestamp":"2025-05-24 23:18:52"}
{"level":"http","message":"POST / 200 - 101ms","timestamp":"2025-05-24 23:18:52"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-24 23:18:53"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-24 23:18:53","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-24 23:18:53"}
{"level":"http","message":"POST / 200 - 14ms","timestamp":"2025-05-24 23:18:53"}
{"level":"info","message":"WebSocket connection authenticated for user 68294132b5d5c86fd2e56e23","timestamp":"2025-05-24 23:18:53"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-24 23:18:53"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-24 23:18:53"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-24 23:18:53"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-24 23:18:53"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-24 23:18:53","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 200 - 14ms","timestamp":"2025-05-24 23:18:53"}
{"level":"http","message":"GraphQL anonymous completed in 33ms","timestamp":"2025-05-24 23:18:53"}
{"level":"http","message":"POST / 200 - 37ms","timestamp":"2025-05-24 23:18:53"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 22ms","timestamp":"2025-05-24 23:18:53"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversations {\n  getConversations {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    lastMessage {\n      id\n      content\n      timestamp\n      isRead\n      sender {\n        id\n        username\n      }\n    }\n    unreadCount\n    updatedAt\n  }\n}","timestamp":"2025-05-24 23:18:53","variables":{}}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-24 23:18:53"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-24 23:18:53"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-24 23:18:53"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-24 23:18:53"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-24 23:18:53"}
{"level":"http","message":"GraphQL anonymous completed in 26ms","timestamp":"2025-05-24 23:18:53"}
{"level":"http","message":"POST / 200 - 30ms","timestamp":"2025-05-24 23:18:53"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-24 23:18:56"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-24 23:18:56","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-24 23:18:56"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-24 23:18:56"}
{"level":"info","message":"WebSocket connection authenticated for user 68294132b5d5c86fd2e56e23","timestamp":"2025-05-24 23:18:56"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-24 23:18:56"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-24 23:18:56"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-24 23:18:56"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-24 23:18:56"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-24 23:18:56","variables":{"limit":10,"page":1}}
{"level":"http","message":"GraphQL anonymous completed in 17ms","timestamp":"2025-05-24 23:18:56"}
{"level":"http","message":"POST / 200 - 21ms","timestamp":"2025-05-24 23:18:56"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation MarkNotificationsAsRead($notificationIds: [ID!]!) {\n  markNotificationsAsRead(notificationIds: $notificationIds) {\n    success\n    readCount\n    remainingCount\n  }\n}","timestamp":"2025-05-24 23:19:07","variables":{"notificationIds":["682e988c6efa48fe6984b386"]}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversations {\n  getConversations {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    lastMessage {\n      id\n      content\n      timestamp\n      isRead\n      sender {\n        id\n        username\n      }\n    }\n    unreadCount\n    updatedAt\n  }\n}","timestamp":"2025-05-24 23:19:07","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 26ms","timestamp":"2025-05-24 23:19:07"}
{"level":"http","message":"POST / 200 - 38ms","timestamp":"2025-05-24 23:19:07"}
{"level":"http","message":"GraphQL anonymous completed in 48ms","timestamp":"2025-05-24 23:19:07"}
{"level":"http","message":"POST / 200 - 53ms","timestamp":"2025-05-24 23:19:07"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation CreateConversation($userId: ID!) {\n  createConversation(userId: $userId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    lastMessage {\n      id\n      content\n      timestamp\n    }\n    unreadCount\n    updatedAt\n  }\n}","timestamp":"2025-05-24 23:19:07","variables":{"userId":"682bb95fb9b407dd58126686"}}
{"level":"info","message":"[MessageService] New conversation created: 683245dbfe5136e30fe9e03a","timestamp":"2025-05-24 23:19:07"}
{"level":"http","message":"GraphQL anonymous completed in 76ms","timestamp":"2025-05-24 23:19:07"}
{"level":"http","message":"POST / 200 - 89ms","timestamp":"2025-05-24 23:19:07"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-24 23:19:07"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-24 23:19:07","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-24 23:19:07"}
{"level":"http","message":"POST / 200 - 11ms","timestamp":"2025-05-24 23:19:07"}
{"level":"info","message":"WebSocket connection authenticated for user 68294132b5d5c86fd2e56e23","timestamp":"2025-05-24 23:19:07"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-24 23:19:07"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-24 23:19:07"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-24 23:19:07"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-24 23:19:07"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-24 23:19:07","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 200 - 11ms","timestamp":"2025-05-24 23:19:07"}
{"level":"http","message":"GraphQL anonymous completed in 29ms","timestamp":"2025-05-24 23:19:07"}
{"level":"http","message":"POST / 200 - 33ms","timestamp":"2025-05-24 23:19:07"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetVoiceMessages {\n  getVoiceMessages {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    endTime\n    duration\n    conversationId\n    metadata\n  }\n}","timestamp":"2025-05-24 23:19:07","variables":{}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-24 23:19:07","variables":{"conversationId":"683245dbfe5136e30fe9e03a","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=683245dbfe5136e30fe9e03a, userId=68294132b5d5c86fd2e56e23","timestamp":"2025-05-24 23:19:07"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-24 23:19:07"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-24 23:19:07"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-24 23:19:07"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-24 23:19:07"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 24ms","timestamp":"2025-05-24 23:19:07"}
{"level":"http","message":"GraphQL anonymous completed in 31ms","timestamp":"2025-05-24 23:19:07"}
{"level":"http","message":"POST / 200 - 34ms","timestamp":"2025-05-24 23:19:07"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 683245dbfe5136e30fe9e03a, unread: 0, messages: 0","timestamp":"2025-05-24 23:19:07"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=683245dbfe5136e30fe9e03a, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=68294132b5d5c86fd2e56e23, offset=0","timestamp":"2025-05-24 23:19:07"}
{"level":"info","message":"[MessageService] Retrieved 0 messages","timestamp":"2025-05-24 23:19:07"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-24 23:19:07"}
{"level":"http","message":"GraphQL anonymous completed in 47ms","timestamp":"2025-05-24 23:19:07"}
{"level":"http","message":"POST / 200 - 49ms","timestamp":"2025-05-24 23:19:07"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-24 23:19:08"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-24 23:19:08"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-24 23:19:08"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation SendMessage($receiverId: ID!, $content: String, $file: Upload, $type: MessageType, $metadata: JSON) {\n  sendMessage(\n    receiverId: $receiverId\n    content: $content\n    file: $file\n    type: $type\n    metadata: $metadata\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    conversation {\n      id\n    }\n    attachments {\n      url\n      type\n      duration\n    }\n    metadata\n  }\n}","timestamp":"2025-05-24 23:19:14","variables":{"content":"Bonjour!","receiverId":"682bb95fb9b407dd58126686","type":"TEXT"}}
{"level":"info","message":"[MessageService] Starting sendMessage flow: senderId=68294132b5d5c86fd2e56e23, receiverId=682bb95fb9b407dd58126686, type=TEXT, hasMetadata=false","timestamp":"2025-05-24 23:19:14"}
{"level":"info","message":"[MessageService] Message saved successfully: 683245e2fe5136e30fe9e051","timestamp":"2025-05-24 23:19:14"}
{"level":"info","message":"[MessageService] Message flow completed successfully: messageId=683245e2fe5136e30fe9e051","timestamp":"2025-05-24 23:19:14"}
{"level":"http","message":"GraphQL anonymous completed in 66ms","timestamp":"2025-05-24 23:19:14"}
{"level":"http","message":"POST / 200 - 72ms","timestamp":"2025-05-24 23:19:14"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-24 23:19:20","variables":{"limit":10,"page":1}}
{"level":"http","message":"GraphQL anonymous completed in 47ms","timestamp":"2025-05-24 23:19:20"}
{"level":"http","message":"POST / 200 - 53ms","timestamp":"2025-05-24 23:19:20"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-24 23:19:29"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-24 23:19:30","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 2ms","timestamp":"2025-05-24 23:19:30"}
{"level":"http","message":"POST / 200 - 17ms","timestamp":"2025-05-24 23:19:30"}
{"level":"info","message":"WebSocket connection authenticated for user 68294132b5d5c86fd2e56e23","timestamp":"2025-05-24 23:19:30"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-24 23:19:30"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-24 23:19:30"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-24 23:19:30"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-24 23:19:30"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-24 23:19:30","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 7ms","timestamp":"2025-05-24 23:19:30"}
{"level":"http","message":"GraphQL anonymous completed in 31ms","timestamp":"2025-05-24 23:19:30"}
{"level":"http","message":"POST / 200 - 37ms","timestamp":"2025-05-24 23:19:30"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 5ms","timestamp":"2025-05-24 23:19:30"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation MarkNotificationsAsRead($notificationIds: [ID!]!) {\n  markNotificationsAsRead(notificationIds: $notificationIds) {\n    success\n    readCount\n    remainingCount\n  }\n}","timestamp":"2025-05-24 23:19:38","variables":{"notificationIds":["682e988c6efa48fe6984b386"]}}
{"level":"http","message":"GraphQL anonymous completed in 21ms","timestamp":"2025-05-24 23:19:38"}
{"level":"http","message":"POST / 200 - 24ms","timestamp":"2025-05-24 23:19:38"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation MarkNotificationsAsRead($notificationIds: [ID!]!) {\n  markNotificationsAsRead(notificationIds: $notificationIds) {\n    success\n    readCount\n    remainingCount\n  }\n}","timestamp":"2025-05-24 23:19:48","variables":{"notificationIds":["683245e2fe5136e30fe9e055"]}}
{"level":"http","message":"GraphQL anonymous completed in 7ms","timestamp":"2025-05-24 23:19:48"}
{"level":"http","message":"POST / 200 - 20ms","timestamp":"2025-05-24 23:19:48"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation DeleteNotification($notificationId: ID!) {\n  deleteNotification(notificationId: $notificationId) {\n    success\n    message\n  }\n}","timestamp":"2025-05-24 23:19:52","variables":{"notificationId":"683245e2fe5136e30fe9e055"}}
{"level":"http","message":"GraphQL anonymous completed in 15ms","timestamp":"2025-05-24 23:19:52"}
{"level":"http","message":"POST / 200 - 19ms","timestamp":"2025-05-24 23:19:52"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation MarkNotificationsAsRead($notificationIds: [ID!]!) {\n  markNotificationsAsRead(notificationIds: $notificationIds) {\n    success\n    readCount\n    remainingCount\n  }\n}","timestamp":"2025-05-24 23:19:55","variables":{"notificationIds":["6832451ffe5136e30fe9dfc6"]}}
{"level":"http","message":"GraphQL anonymous completed in 13ms","timestamp":"2025-05-24 23:19:55"}
{"level":"http","message":"POST / 200 - 26ms","timestamp":"2025-05-24 23:19:55"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation MarkNotificationsAsRead($notificationIds: [ID!]!) {\n  markNotificationsAsRead(notificationIds: $notificationIds) {\n    success\n    readCount\n    remainingCount\n  }\n}","timestamp":"2025-05-24 23:19:56","variables":{"notificationIds":["6832451ffe5136e30fe9dfc6"]}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversations {\n  getConversations {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    lastMessage {\n      id\n      content\n      timestamp\n      isRead\n      sender {\n        id\n        username\n      }\n    }\n    unreadCount\n    updatedAt\n  }\n}","timestamp":"2025-05-24 23:19:56","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 24ms","timestamp":"2025-05-24 23:19:56"}
{"level":"http","message":"POST / 200 - 28ms","timestamp":"2025-05-24 23:19:56"}
{"level":"http","message":"GraphQL anonymous completed in 29ms","timestamp":"2025-05-24 23:19:56"}
{"level":"http","message":"POST / 200 - 37ms","timestamp":"2025-05-24 23:19:56"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation CreateConversation($userId: ID!) {\n  createConversation(userId: $userId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    lastMessage {\n      id\n      content\n      timestamp\n    }\n    unreadCount\n    updatedAt\n  }\n}","timestamp":"2025-05-24 23:19:56","variables":{"userId":"68294132b5d5c86fd2e56e23"}}
{"level":"warn","message":"GraphQL anonymous completed with errors in 5ms","timestamp":"2025-05-24 23:19:56"}
{"level":"http","message":"POST / 200 - 14ms","timestamp":"2025-05-24 23:19:56"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation CreateConversation($userId: ID!) {\n  createConversation(userId: $userId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    lastMessage {\n      id\n      content\n      timestamp\n    }\n    unreadCount\n    updatedAt\n  }\n}","timestamp":"2025-05-24 23:19:56","variables":{"userId":"68294132b5d5c86fd2e56e23"}}
{"level":"warn","message":"GraphQL anonymous completed with errors in 1ms","timestamp":"2025-05-24 23:19:56"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-24 23:19:56"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-24 23:19:56"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-24 23:19:57","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-24 23:19:57"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-24 23:19:57"}
{"level":"info","message":"WebSocket connection authenticated for user 68294132b5d5c86fd2e56e23","timestamp":"2025-05-24 23:19:57"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-24 23:19:57"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-24 23:19:57"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-24 23:19:57"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-24 23:19:57"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-24 23:19:57","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 200 - 10ms","timestamp":"2025-05-24 23:19:57"}
{"level":"http","message":"GraphQL anonymous completed in 23ms","timestamp":"2025-05-24 23:19:57"}
{"level":"http","message":"POST / 200 - 29ms","timestamp":"2025-05-24 23:19:57"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversations {\n  getConversations {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    lastMessage {\n      id\n      content\n      timestamp\n      isRead\n      sender {\n        id\n        username\n      }\n    }\n    unreadCount\n    updatedAt\n  }\n}","timestamp":"2025-05-24 23:19:57","variables":{}}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-24 23:19:57"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-24 23:19:57"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-24 23:19:57"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-24 23:19:57"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-24 23:19:57"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 14ms","timestamp":"2025-05-24 23:19:57"}
{"level":"http","message":"GraphQL anonymous completed in 26ms","timestamp":"2025-05-24 23:19:57"}
{"level":"http","message":"POST / 200 - 29ms","timestamp":"2025-05-24 23:19:57"}
{"level":"http","message":"GET /user/68294132b5d5c86fd2e56e23 304 - 25ms","timestamp":"2025-05-24 23:20:00"}
{"level":"http","message":"GET /user/68294132b5d5c86fd2e56e23 304 - 23ms","timestamp":"2025-05-24 23:20:02"}
{"level":"http","message":"GET / 304 - 9ms","timestamp":"2025-05-24 23:20:03"}
{"level":"http","message":"GET / 304 - 3ms","timestamp":"2025-05-24 23:20:11"}
{"level":"http","message":"GET /user/68294132b5d5c86fd2e56e23 304 - 12ms","timestamp":"2025-05-24 23:20:12"}
{"level":"http","message":"GET /user/68294132b5d5c86fd2e56e23 304 - 9ms","timestamp":"2025-05-24 23:20:13"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversations {\n  getConversations {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    lastMessage {\n      id\n      content\n      timestamp\n      isRead\n      sender {\n        id\n        username\n      }\n    }\n    unreadCount\n    updatedAt\n  }\n}","timestamp":"2025-05-24 23:20:13","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 28ms","timestamp":"2025-05-24 23:20:13"}
{"level":"http","message":"POST / 200 - 31ms","timestamp":"2025-05-24 23:20:13"}
{"level":"http","message":"GET /users 200 - 21ms","timestamp":"2025-05-24 23:20:15"}
{"level":"http","message":"GET /getone/682d07869c0ffc37bb8e0ee2?secret=2cinfo1&client=esprit 200 - 7ms","timestamp":"2025-05-24 23:20:24"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-24 23:20:27","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-24 23:20:27"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-24 23:20:27"}
{"level":"http","message":"GET /user/68294132b5d5c86fd2e56e23 304 - 8ms","timestamp":"2025-05-24 23:20:28"}
{"level":"http","message":"GET /profile 200 - 17ms","timestamp":"2025-05-24 23:20:29"}
{"level":"http","message":"GET /users 304 - 20ms","timestamp":"2025-05-24 23:20:29"}
{"level":"http","message":"GET /users 304 - 7ms","timestamp":"2025-05-24 23:20:33"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-24 23:20:58","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-24 23:20:58"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-24 23:20:58"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-24 23:21:28","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-24 23:21:28"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-24 23:21:28"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-24 23:21:58","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-24 23:21:58"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-24 23:21:58"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-24 23:22:28","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-24 23:22:28"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-24 23:22:28"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-24 23:23:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-24 23:23:41"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-24 23:23:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-24 23:24:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-24 23:24:41"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-24 23:24:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-24 23:25:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-24 23:25:41"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-24 23:25:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-24 23:26:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-24 23:26:41"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-24 23:26:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-24 23:27:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-24 23:27:41"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-24 23:27:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-24 23:28:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-24 23:28:41"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-24 23:28:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-24 23:29:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-24 23:29:41"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-24 23:29:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-24 23:30:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-24 23:30:41"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-24 23:30:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-24 23:31:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-24 23:31:41"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-24 23:31:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-24 23:32:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-24 23:32:41"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-24 23:32:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-24 23:33:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-24 23:33:41"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-24 23:33:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-24 23:34:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-24 23:34:41"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-24 23:34:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-24 23:35:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-24 23:35:41"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-24 23:35:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-24 23:36:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-24 23:36:41"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-24 23:36:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-24 23:37:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-24 23:37:41"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-24 23:37:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-24 23:38:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-24 23:38:41"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-24 23:38:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-24 23:39:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-24 23:39:41"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-24 23:39:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-24 23:40:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-24 23:40:41"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-24 23:40:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-24 23:41:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-24 23:41:41"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-24 23:41:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-24 23:42:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-24 23:42:41"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-24 23:42:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-24 23:43:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-24 23:43:41"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-24 23:43:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-24 23:44:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-24 23:44:41"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-24 23:44:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-24 23:45:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-24 23:45:41"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-24 23:45:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-24 23:46:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-24 23:46:41"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-24 23:46:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-24 23:47:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-24 23:47:41"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-24 23:47:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-24 23:48:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-24 23:48:41"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-24 23:48:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-24 23:49:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-24 23:49:41"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-24 23:49:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-24 23:50:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-24 23:50:41"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-24 23:50:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-24 23:51:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-24 23:51:41"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-24 23:51:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-24 23:52:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-24 23:52:41"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-24 23:52:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-24 23:53:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-24 23:53:41"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-24 23:53:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-24 23:54:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-24 23:54:41"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-24 23:54:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-24 23:55:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-24 23:55:41"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-24 23:55:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-24 23:56:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-24 23:56:41"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-24 23:56:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-24 23:57:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-24 23:57:41"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-24 23:57:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-24 23:58:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-24 23:58:41"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-24 23:58:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-24 23:59:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-24 23:59:41"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-24 23:59:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-25 00:00:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-25 00:00:41"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-25 00:00:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-25 00:01:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-25 00:01:41"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-25 00:01:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-25 00:02:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-25 00:02:41"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-25 00:02:41"}
