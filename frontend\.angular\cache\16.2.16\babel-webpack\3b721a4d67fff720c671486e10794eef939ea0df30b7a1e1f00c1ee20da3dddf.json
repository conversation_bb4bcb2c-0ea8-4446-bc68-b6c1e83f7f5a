{"ast": null, "code": "import { NavigationEnd } from '@angular/router';\nimport { Subject } from 'rxjs';\nimport { filter, takeUntil, distinctUntilChanged } from 'rxjs/operators';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/authuser.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/services/user-status.service\";\nimport * as i4 from \"src/app/services/message.service\";\nimport * as i5 from \"@app/services/theme.service\";\nimport * as i6 from \"src/app/services/data.service\";\nimport * as i7 from \"@angular/common\";\nconst _c0 = function () {\n  return {\n    exact: true\n  };\n};\nfunction FrontLayoutComponent_a_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 63);\n    i0.ɵɵelement(1, \"span\", 64);\n    i0.ɵɵelementStart(2, \"div\", 65)(3, \"div\", 8);\n    i0.ɵɵelement(4, \"i\", 66)(5, \"div\", 67);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 8);\n    i0.ɵɵtext(7, \"Accueil\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"routerLinkActiveOptions\", i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nfunction FrontLayoutComponent_a_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 68);\n    i0.ɵɵelement(1, \"span\", 64);\n    i0.ɵɵelementStart(2, \"div\", 65)(3, \"div\", 8);\n    i0.ɵɵelement(4, \"i\", 69)(5, \"div\", 67);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 8);\n    i0.ɵɵtext(7, \"Projects\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction FrontLayoutComponent_a_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 70);\n    i0.ɵɵelement(1, \"span\", 64);\n    i0.ɵɵelementStart(2, \"div\", 65)(3, \"div\", 8);\n    i0.ɵɵelement(4, \"i\", 71)(5, \"div\", 67);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 8);\n    i0.ɵɵtext(7, \"Plannings\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction FrontLayoutComponent_a_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 72);\n    i0.ɵɵelement(1, \"span\", 64);\n    i0.ɵɵelementStart(2, \"div\", 65)(3, \"div\", 8);\n    i0.ɵɵelement(4, \"i\", 73)(5, \"div\", 67);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 8);\n    i0.ɵɵtext(7, \"R\\u00E9unions\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction FrontLayoutComponent_a_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 74);\n    i0.ɵɵelement(1, \"span\", 64);\n    i0.ɵɵelementStart(2, \"div\", 65)(3, \"div\", 8);\n    i0.ɵɵelement(4, \"i\", 75)(5, \"div\", 67);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 8);\n    i0.ɵɵtext(7, \"Messages\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction FrontLayoutComponent_a_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 76);\n    i0.ɵɵelement(1, \"span\", 77);\n    i0.ɵɵelementStart(2, \"div\", 65)(3, \"div\", 8);\n    i0.ɵɵelement(4, \"i\", 78)(5, \"div\", 79);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 8);\n    i0.ɵɵtext(7, \"Go to Dashboard\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction FrontLayoutComponent_ng_container_46_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 101)(1, \"div\", 29);\n    i0.ɵɵelement(2, \"i\", 102);\n    i0.ɵɵelementStart(3, \"div\", 103);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r14.unreadNotificationsCount > 0 ? ctx_r14.unreadNotificationsCount > 99 ? \"99+\" : ctx_r14.unreadNotificationsCount : \"0\", \" \");\n  }\n}\nfunction FrontLayoutComponent_ng_container_46_i_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 104);\n  }\n}\nfunction FrontLayoutComponent_ng_container_46_i_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 105);\n  }\n}\nconst _c1 = function (a0) {\n  return {\n    \"rotate-180\": a0\n  };\n};\nfunction FrontLayoutComponent_ng_container_46_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 80);\n    i0.ɵɵtemplate(2, FrontLayoutComponent_ng_container_46_a_2_Template, 5, 1, \"a\", 81);\n    i0.ɵɵelementStart(3, \"button\", 82);\n    i0.ɵɵlistener(\"click\", function FrontLayoutComponent_ng_container_46_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.toggleDarkMode());\n    });\n    i0.ɵɵelementStart(4, \"div\", 83);\n    i0.ɵɵelement(5, \"div\", 84)(6, \"div\", 85);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"div\", 86);\n    i0.ɵɵelementStart(8, \"div\", 87);\n    i0.ɵɵpipe(9, \"async\");\n    i0.ɵɵtemplate(10, FrontLayoutComponent_ng_container_46_i_10_Template, 1, 0, \"i\", 88);\n    i0.ɵɵpipe(11, \"async\");\n    i0.ɵɵtemplate(12, FrontLayoutComponent_ng_container_46_i_12_Template, 1, 0, \"i\", 89);\n    i0.ɵɵpipe(13, \"async\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"button\", 90);\n    i0.ɵɵlistener(\"click\", function FrontLayoutComponent_ng_container_46_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r19 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r19.logout());\n    });\n    i0.ɵɵelementStart(15, \"div\", 91);\n    i0.ɵɵelement(16, \"div\", 92)(17, \"div\", 93);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(18, \"div\", 94)(19, \"i\", 95);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"a\", 96)(21, \"span\", 97);\n    i0.ɵɵtext(22, \"Profile\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"span\", 98);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"div\", 99);\n    i0.ɵɵelement(26, \"div\", 86)(27, \"img\", 100);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.authService.userLoggedIn());\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(12, _c1, i0.ɵɵpipeBind1(9, 6, ctx_r6.isDarkMode$)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !i0.ɵɵpipeBind1(11, 8, ctx_r6.isDarkMode$));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(13, 10, ctx_r6.isDarkMode$));\n    i0.ɵɵadvance(12);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.username, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", ctx_r6.imageProfile, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction FrontLayoutComponent_ng_template_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 106)(1, \"a\", 107);\n    i0.ɵɵelement(2, \"div\", 108)(3, \"div\", 109);\n    i0.ɵɵelementStart(4, \"span\", 110);\n    i0.ɵɵelement(5, \"i\", 111);\n    i0.ɵɵtext(6, \" Connexion \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"a\", 112);\n    i0.ɵɵelement(8, \"div\", 113)(9, \"div\", 114);\n    i0.ɵɵelementStart(10, \"span\", 110);\n    i0.ɵɵelement(11, \"i\", 115);\n    i0.ɵɵtext(12, \" Inscription \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction FrontLayoutComponent_a_68_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 119);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r20 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", item_r20.badge > 99 ? \"99+\" : item_r20.badge, \" \");\n  }\n}\nconst _c2 = function (a0) {\n  return {\n    exact: a0\n  };\n};\nfunction FrontLayoutComponent_a_68_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 116);\n    i0.ɵɵlistener(\"click\", function FrontLayoutComponent_a_68_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r23 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r23.toggleSidebar());\n    });\n    i0.ɵɵelement(1, \"span\", 64);\n    i0.ɵɵelementStart(2, \"div\", 117)(3, \"div\", 8);\n    i0.ɵɵelement(4, \"i\")(5, \"div\", 67);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, FrontLayoutComponent_a_68_div_8_Template, 2, 1, \"div\", 118);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r20 = ctx.$implicit;\n    i0.ɵɵpropertyInterpolate(\"routerLink\", item_r20.route);\n    i0.ɵɵproperty(\"routerLinkActiveOptions\", i0.ɵɵpureFunction1(7, _c2, item_r20.route === \"/\" ? true : false));\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassMapInterpolate1(\"\", item_r20.icon, \" h-5 w-5 mr-3 text-[#4f5fad] dark:text-[#6d78c9] group-hover:text-[#3d4a85] dark:group-hover:text-[#4f5fad] transition-colors\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r20.text);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r20.badge && item_r20.badge > 0);\n  }\n}\nfunction FrontLayoutComponent_a_69_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r26 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 120);\n    i0.ɵɵlistener(\"click\", function FrontLayoutComponent_a_69_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r26);\n      const ctx_r25 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r25.toggleSidebar());\n    });\n    i0.ɵɵelement(1, \"span\", 77);\n    i0.ɵɵelementStart(2, \"div\", 117)(3, \"div\", 8);\n    i0.ɵɵelement(4, \"i\", 121)(5, \"div\", 79);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\");\n    i0.ɵɵtext(7, \"Go to Dashboard\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction FrontLayoutComponent_ng_container_71_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"a\", 122);\n    i0.ɵɵlistener(\"click\", function FrontLayoutComponent_ng_container_71_Template_a_click_1_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r27 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r27.toggleSidebar());\n    });\n    i0.ɵɵelement(2, \"span\", 64);\n    i0.ɵɵelementStart(3, \"div\", 65)(4, \"div\", 8);\n    i0.ɵɵelement(5, \"i\", 123)(6, \"div\", 67);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\");\n    i0.ɵɵtext(8, \"Connexion\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"a\", 124);\n    i0.ɵɵlistener(\"click\", function FrontLayoutComponent_ng_container_71_Template_a_click_9_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r29 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r29.toggleSidebar());\n    });\n    i0.ɵɵelement(10, \"span\", 77);\n    i0.ɵɵelementStart(11, \"div\", 65)(12, \"div\", 8);\n    i0.ɵɵelement(13, \"i\", 125)(14, \"div\", 79);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\");\n    i0.ɵɵtext(16, \"Inscription\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction FrontLayoutComponent_ng_container_72_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r31 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"a\", 126);\n    i0.ɵɵlistener(\"click\", function FrontLayoutComponent_ng_container_72_Template_a_click_1_listener() {\n      i0.ɵɵrestoreView(_r31);\n      const ctx_r30 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r30.toggleSidebar());\n    });\n    i0.ɵɵelement(2, \"span\", 64);\n    i0.ɵɵelementStart(3, \"div\", 65)(4, \"div\", 8);\n    i0.ɵɵelement(5, \"i\", 127)(6, \"div\", 67);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\");\n    i0.ɵɵtext(8, \"Mon Profil\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"a\", 128);\n    i0.ɵɵlistener(\"click\", function FrontLayoutComponent_ng_container_72_Template_a_click_9_listener() {\n      i0.ɵɵrestoreView(_r31);\n      const ctx_r32 = i0.ɵɵnextContext();\n      ctx_r32.logout();\n      return i0.ɵɵresetView(ctx_r32.toggleSidebar());\n    });\n    i0.ɵɵelement(10, \"span\", 129);\n    i0.ɵɵelementStart(11, \"div\", 65)(12, \"div\", 8);\n    i0.ɵɵelement(13, \"i\", 130)(14, \"div\", 131);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\");\n    i0.ɵɵtext(16, \"D\\u00E9connexion\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction FrontLayoutComponent_div_78_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 132)(1, \"div\", 133)(2, \"div\", 29)(3, \"div\", 8);\n    i0.ɵɵelement(4, \"i\", 134)(5, \"div\", 135);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 136);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r13.messageFromRedirect, \" \");\n  }\n}\nconst _c3 = function () {\n  return {\n    route: \"/\",\n    icon: \"fas fa-home\",\n    text: \"Accueil\"\n  };\n};\nconst _c4 = function () {\n  return {\n    route: \"/projects\",\n    icon: \"fas fa-rocket\",\n    text: \"Projects\"\n  };\n};\nconst _c5 = function () {\n  return {\n    route: \"/plannings\",\n    icon: \"far fa-calendar-check\",\n    text: \"Plannings\"\n  };\n};\nconst _c6 = function () {\n  return {\n    route: \"/reunions\",\n    icon: \"fas fa-users-cog\",\n    text: \"R\\u00E9unions\"\n  };\n};\nconst _c7 = function () {\n  return {\n    route: \"/messages\",\n    icon: \"far fa-comment-dots\",\n    text: \"Messages\"\n  };\n};\nconst _c8 = function (a3) {\n  return {\n    route: \"/notifications\",\n    icon: \"far fa-bell\",\n    text: \"Notifications\",\n    badge: a3\n  };\n};\nconst _c9 = function (a0, a1, a2, a3, a4, a5) {\n  return [a0, a1, a2, a3, a4, a5];\n};\nexport let FrontLayoutComponent = /*#__PURE__*/(() => {\n  class FrontLayoutComponent {\n    constructor(authService, route, router, statusService, MessageService, themeService, dataService) {\n      this.authService = authService;\n      this.route = route;\n      this.router = router;\n      this.statusService = statusService;\n      this.MessageService = MessageService;\n      this.themeService = themeService;\n      this.dataService = dataService;\n      this.sidebarOpen = false;\n      this.profileMenuOpen = false;\n      this.currentUser = null;\n      this.messageFromRedirect = '';\n      this.unreadNotificationsCount = 0;\n      this.isMobileView = false;\n      this.username = '';\n      this.imageProfile = '';\n      this.destroy$ = new Subject();\n      this.MOBILE_BREAKPOINT = 768;\n      this.subscriptions = [];\n      this.checkViewport();\n      this.loadUserProfile();\n      this.isDarkMode$ = this.themeService.darkMode$;\n    }\n    loadUserProfile() {\n      // Try to get user from both services for maximum reliability\n      const authUser = this.authService.getCurrentUser();\n      const dataUser = this.dataService.currentUserValue;\n      console.log('Auth User:', authUser);\n      console.log('Data User:', dataUser);\n      // Prefer dataUser if available, otherwise use authUser\n      const user = dataUser || authUser;\n      if (user) {\n        this.updateProfileDisplay(user);\n        // Forcer une synchronisation complète des données utilisateur\n        if (this.authService.userLoggedIn()) {\n          this.dataService.syncCurrentUser().subscribe({\n            next: updatedUser => {\n              console.log('User profile synced:', updatedUser);\n              this.updateProfileDisplay(updatedUser);\n            },\n            error: err => {\n              console.error('Failed to sync user profile:', err);\n            }\n          });\n        }\n      } else {\n        // Default values if no user is found\n        this.username = '';\n        this.imageProfile = 'assets/images/default-profile.png';\n      }\n      console.log('Front layout - Image profile loaded:', this.imageProfile);\n      // Sync user data between services if needed\n      if (authUser && !dataUser) {\n        this.dataService.updateCurrentUser(authUser);\n      } else if (!authUser && dataUser) {\n        this.authService.setCurrentUser(dataUser);\n      }\n    }\n    ngOnInit() {\n      this.subscribeToQueryParams();\n      this.subscribeToCurrentUser();\n      this.subscribeToRouterEvents();\n      this.setupNotificationSystem();\n    }\n    checkViewport() {\n      this.isMobileView = window.innerWidth < this.MOBILE_BREAKPOINT;\n      if (!this.isMobileView) {\n        this.sidebarOpen = false;\n      }\n    }\n    setupNotificationSystem() {\n      // Real-time count updates\n      this.MessageService.notificationCount$.pipe(takeUntil(this.destroy$), distinctUntilChanged()).subscribe(count => {\n        this.unreadNotificationsCount = count;\n      });\n      // Charger les notifications initiales si l'utilisateur est connecté\n      if (this.authService.userLoggedIn()) {\n        this.MessageService.getNotifications(true).subscribe();\n      }\n    }\n    subscribeToCurrentUser() {\n      // S'abonner aux changements d'image de profil via AuthUserService\n      const authProfileSub = this.authService.currentUser$.pipe(takeUntil(this.destroy$)).subscribe(user => {\n        this.currentUser = user;\n        this.updateProfileDisplay(user);\n      });\n      // S'abonner aux changements d'image de profil via DataService\n      const dataProfileSub = this.dataService.currentUser$.pipe(takeUntil(this.destroy$)).subscribe(user => {\n        if (user) {\n          this.currentUser = user;\n          this.updateProfileDisplay(user);\n        }\n      });\n      this.subscriptions.push(authProfileSub, dataProfileSub);\n    }\n    updateProfileDisplay(user) {\n      if (user) {\n        this.username = user.fullName || user.username || '';\n        // Vérification plus robuste pour l'image de profil\n        let imageFound = false;\n        // Vérifier profileImage en premier\n        if (user.profileImage && user.profileImage !== 'null' && user.profileImage.trim() !== '' && user.profileImage !== 'undefined') {\n          this.imageProfile = user.profileImage;\n          imageFound = true;\n          console.log('Using profileImage:', this.imageProfile);\n        }\n        // Ensuite vérifier image si profileImage n'est pas valide\n        if (!imageFound && user.image && user.image !== 'null' && user.image.trim() !== '' && user.image !== 'undefined') {\n          this.imageProfile = user.image;\n          imageFound = true;\n          console.log('Using image:', this.imageProfile);\n        }\n        // Vérifier si l'image est une URL relative au backend\n        if (imageFound && !this.imageProfile.startsWith('http') && !this.imageProfile.startsWith('assets/')) {\n          // Si c'est une URL relative au backend, ajouter le préfixe du backend\n          if (this.imageProfile.startsWith('/')) {\n            this.imageProfile = `${environment.urlBackend.replace(/\\/$/, '')}${this.imageProfile}`;\n          } else {\n            this.imageProfile = `${environment.urlBackend.replace(/\\/$/, '')}/${this.imageProfile}`;\n          }\n          console.log('Converted to absolute URL:', this.imageProfile);\n        }\n        // Si aucune image valide n'est trouvée, utiliser l'image par défaut\n        if (!imageFound) {\n          this.imageProfile = 'assets/images/default-profile.png';\n          console.log('Using default image');\n        }\n        console.log('Front layout - Image profile updated:', this.imageProfile);\n      }\n    }\n    subscribeToQueryParams() {\n      this.route.queryParams.pipe(takeUntil(this.destroy$)).subscribe(params => {\n        this.messageFromRedirect = params['message'] || '';\n      });\n    }\n    subscribeToRouterEvents() {\n      this.router.events.pipe(filter(event => event instanceof NavigationEnd), takeUntil(this.destroy$)).subscribe(() => {\n        this.sidebarOpen = false;\n        this.profileMenuOpen = false;\n      });\n    }\n    toggleSidebar() {\n      this.sidebarOpen = !this.sidebarOpen;\n    }\n    toggleProfileMenu() {\n      this.profileMenuOpen = !this.profileMenuOpen;\n    }\n    toggleDarkMode() {\n      this.themeService.toggleDarkMode();\n    }\n    logout() {\n      this.authService.logout().subscribe({\n        next: () => {\n          this.profileMenuOpen = false;\n          this.sidebarOpen = false;\n          this.currentUser = null;\n          // Reset image to default\n          this.imageProfile = 'assets/images/default-profile.png';\n          // Clear data in both services\n          this.dataService.updateCurrentUser({});\n          this.router.navigate(['/login']);\n        },\n        error: err => {\n          console.error('Logout error:', err);\n          this.authService.clearAuthData();\n          this.currentUser = null;\n          // Reset image to default\n          this.imageProfile = 'assets/images/default-profile.png';\n          // Clear data in both services\n          this.dataService.updateCurrentUser({});\n          this.router.navigate(['/login']);\n        }\n      });\n    }\n    ngOnDestroy() {\n      // Désabonner de tous les observables pour éviter les fuites de mémoire\n      this.subscriptions.forEach(sub => sub.unsubscribe());\n      this.destroy$.next();\n      this.destroy$.complete();\n    }\n    static {\n      this.ɵfac = function FrontLayoutComponent_Factory(t) {\n        return new (t || FrontLayoutComponent)(i0.ɵɵdirectiveInject(i1.AuthuserService), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.UserStatusService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i5.ThemeService), i0.ɵɵdirectiveInject(i6.DataService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: FrontLayoutComponent,\n        selectors: [[\"app-front-layout\"]],\n        hostBindings: function FrontLayoutComponent_HostBindings(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵlistener(\"resize\", function FrontLayoutComponent_resize_HostBindingHandler() {\n              return ctx.checkViewport();\n            }, false, i0.ɵɵresolveWindow);\n          }\n        },\n        decls: 81,\n        vars: 37,\n        consts: [[1, \"flex\", \"h-screen\", \"main-grid-container\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"futuristic-layout\"], [1, \"background-grid\"], [1, \"hidden\", \"md:flex\", \"md:flex-shrink-0\"], [1, \"flex\", \"flex-col\", \"w-64\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"border-r\", \"border-[#edf1f4]\", \"dark:border-[#2a2a2a]\", \"backdrop-blur-sm\"], [1, \"flex\", \"items-center\", \"justify-center\", \"h-16\", \"px-4\", \"relative\", \"overflow-hidden\"], [1, \"absolute\", \"-top-6\", \"-left-6\", \"w-12\", \"h-12\", \"bg-gradient-to-br\", \"from-[#4f5fad]/20\", \"to-transparent\", \"rounded-full\"], [1, \"absolute\", \"-bottom-6\", \"-right-6\", \"w-12\", \"h-12\", \"bg-gradient-to-tl\", \"from-[#4f5fad]/20\", \"to-transparent\", \"rounded-full\"], [1, \"flex\", \"items-center\", \"relative\", \"z-10\"], [1, \"relative\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-8\", \"w-8\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"transform\", \"rotate-12\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/20\", \"dark:bg-[#6d78c9]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"ml-2\", \"text-xl\", \"font-bold\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\"], [1, \"flex\", \"flex-col\", \"flex-grow\", \"px-4\", \"py-4\"], [1, \"flex-1\", \"space-y-2\"], [\"routerLink\", \"/\", \"routerLinkActive\", \"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\", \"class\", \"sidebar-nav-link group flex items-center px-4 py-3 text-sm font-medium rounded-l-md text-[#6d6870] dark:text-[#a0a0a0] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-all\", 3, \"routerLinkActiveOptions\", 4, \"ngIf\"], [\"routerLink\", \"/projects\", \"routerLinkActive\", \"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\", \"class\", \"sidebar-nav-link group flex items-center px-4 py-3 text-sm font-medium rounded-l-md text-[#6d6870] dark:text-[#a0a0a0] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-all\", 4, \"ngIf\"], [\"routerLink\", \"/plannings\", \"routerLinkActive\", \"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\", \"class\", \"sidebar-nav-link group flex items-center px-4 py-3 text-sm font-medium rounded-l-md text-[#6d6870] dark:text-[#a0a0a0] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-all\", 4, \"ngIf\"], [\"routerLink\", \"/reunions\", \"routerLinkActive\", \"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\", \"class\", \"sidebar-nav-link group flex items-center px-4 py-3 text-sm font-medium rounded-l-md text-[#6d6870] dark:text-[#a0a0a0] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-all\", 4, \"ngIf\"], [\"routerLink\", \"/messages\", \"routerLinkActive\", \"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\", \"class\", \"sidebar-nav-link group flex items-center px-4 py-3 text-sm font-medium rounded-l-md text-[#6d6870] dark:text-[#a0a0a0] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-all\", 4, \"ngIf\"], [1, \"border-t\", \"border-[#edf1f4]\", \"dark:border-[#2a2a2a]\", \"my-2\"], [\"routerLink\", \"/admin/dashboard\", \"routerLinkActive\", \"active bg-gradient-to-r from-[#7826b5]/10 to-[#9d4edd]/10 dark:from-[#7826b5]/20 dark:to-[#9d4edd]/20 text-[#7826b5] dark:text-[#9d4edd] font-medium\", \"class\", \"sidebar-nav-link dashboard-link group flex items-center px-4 py-3 text-sm font-medium rounded-l-md text-[#6d6870] dark:text-[#a0a0a0] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] hover:text-[#7826b5] dark:hover:text-[#9d4edd] transition-all\", 4, \"ngIf\"], [1, \"fixed\", \"top-0\", \"left-0\", \"right-0\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"shadow-md\", \"dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)]\", \"z-50\", \"backdrop-blur-sm\", \"border-b\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\"], [1, \"max-w-7xl\", \"mx-auto\", \"px-4\", \"sm:px-6\", \"lg:px-8\", \"relative\"], [1, \"absolute\", \"inset-0\", \"overflow-hidden\", \"pointer-events-none\"], [1, \"absolute\", \"top-0\", \"left-1/4\", \"w-px\", \"h-full\", \"bg-gradient-to-b\", \"from-transparent\", \"via-[#4f5fad]/10\", \"dark:via-[#6d78c9]/5\", \"to-transparent\"], [1, \"absolute\", \"top-0\", \"right-1/3\", \"w-px\", \"h-full\", \"bg-gradient-to-b\", \"from-transparent\", \"via-[#4f5fad]/5\", \"dark:via-[#6d78c9]/3\", \"to-transparent\"], [1, \"flex\", \"items-center\", \"justify-between\", \"h-16\", \"relative\", \"z-10\"], [1, \"flex\", \"items-center\"], [\"aria-label\", \"Toggle menu\", 1, \"md:hidden\", \"flex\", \"items-center\", \"justify-center\", \"h-8\", \"px-3\", \"rounded-md\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"focus:outline-none\", \"transition-colors\", \"relative\", \"group\", \"overflow-hidden\", 3, \"click\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/10\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\", \"rounded-md\", \"blur-md\"], [\"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-5\", \"w-5\", \"relative\", \"z-10\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M4 6h16M4 12h16M4 18h16\"], [1, \"text-sm\", \"font-medium\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"ml-2\", \"relative\", \"z-10\"], [\"routerLink\", \"/\", 1, \"flex-shrink-0\", \"flex\", \"items-center\", \"group\"], [1, \"text-xl\", \"font-bold\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\", \"group-hover:scale-105\", \"transition-transform\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/10\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\"], [1, \"ml-2\", \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hidden\", \"md:block\"], [4, \"ngIf\", \"ngIfElse\"], [\"authButtons\", \"\"], [1, \"fixed\", \"inset-0\", \"z-40\", \"md:hidden\", 3, \"click\"], [1, \"absolute\", \"inset-0\", \"bg-black/30\", \"dark:bg-black/50\", \"backdrop-blur-sm\"], [1, \"fixed\", \"inset-y-0\", \"left-0\", \"max-w-xs\", \"w-full\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"shadow-lg\", \"dark:shadow-[0_0_30px_rgba(0,0,0,0.3)]\", \"transform\", \"transition-transform\", \"duration-300\", \"ease-in-out\", \"border-r\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", 3, \"click\"], [1, \"flex\", \"flex-col\", \"h-full\", \"relative\"], [1, \"absolute\", \"top-[10%]\", \"left-[5%]\", \"w-32\", \"h-32\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-2xl\"], [1, \"absolute\", \"bottom-[10%]\", \"right-[5%]\", \"w-40\", \"h-40\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-2xl\"], [1, \"flex\", \"items-center\", \"justify-between\", \"px-4\", \"py-3\", \"border-b\", \"border-[#edf1f4]\", \"dark:border-[#2a2a2a]\", \"relative\", \"z-10\"], [1, \"text-xl\", \"font-bold\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [\"aria-label\", \"Close menu\", 1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"p-2\", \"rounded-full\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"transition-colors\", \"relative\", \"group\", 3, \"click\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/10\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\", \"rounded-full\", \"blur-md\"], [1, \"fas\", \"fa-times\", \"relative\", \"z-10\"], [1, \"flex-1\", \"px-2\", \"py-4\", \"space-y-1\", \"overflow-y-auto\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"relative\", \"z-10\"], [\"class\", \"flex items-center px-3 py-2 text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] rounded-lg transition-all relative group overflow-hidden\", \"routerLinkActive\", \"bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium border-r-2 border-[#4f5fad] dark:border-[#6d78c9]\", 3, \"routerLink\", \"routerLinkActiveOptions\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"routerLink\", \"/admin/dashboard\", \"class\", \"flex items-center px-3 py-2 text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#7826b5] dark:hover:text-[#9d4edd] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] rounded-lg transition-all relative group overflow-hidden mt-2\", 3, \"click\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"flex-1\", \"flex\", \"flex-col\", \"overflow-hidden\"], [1, \"flex-1\", \"overflow-y-auto\", \"bg-[#edf1f4]\", \"dark:bg-[#121212]\", \"pt-16\", \"pb-6\", \"relative\"], [1, \"absolute\", \"top-[15%]\", \"left-[10%]\", \"w-64\", \"h-64\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"bottom-[20%]\", \"right-[10%]\", \"w-80\", \"h-80\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [\"class\", \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10 mt-4\", 4, \"ngIf\"], [1, \"max-w-7xl\", \"mx-auto\", \"px-4\", \"sm:px-6\", \"lg:px-8\", \"relative\", \"z-10\"], [\"routerLink\", \"/\", \"routerLinkActive\", \"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\", 1, \"sidebar-nav-link\", \"group\", \"flex\", \"items-center\", \"px-4\", \"py-3\", \"text-sm\", \"font-medium\", \"rounded-l-md\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"transition-all\", 3, \"routerLinkActiveOptions\"], [1, \"absolute\", \"inset-0\", \"w-1\", \"bg-gradient-to-b\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\"], [1, \"relative\", \"z-10\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-home\", \"h-5\", \"w-5\", \"mr-3\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"group-hover:text-[#3d4a85]\", \"dark:group-hover:text-[#4f5fad]\", \"transition-all\", \"group-hover:scale-110\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/20\", \"dark:bg-[#6d78c9]/20\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\", \"blur-md\", \"rounded-full\"], [\"routerLink\", \"/projects\", \"routerLinkActive\", \"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\", 1, \"sidebar-nav-link\", \"group\", \"flex\", \"items-center\", \"px-4\", \"py-3\", \"text-sm\", \"font-medium\", \"rounded-l-md\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"transition-all\"], [1, \"fas\", \"fa-rocket\", \"h-5\", \"w-5\", \"mr-3\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"group-hover:text-[#3d4a85]\", \"dark:group-hover:text-[#4f5fad]\", \"transition-all\", \"group-hover:scale-110\"], [\"routerLink\", \"/plannings\", \"routerLinkActive\", \"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\", 1, \"sidebar-nav-link\", \"group\", \"flex\", \"items-center\", \"px-4\", \"py-3\", \"text-sm\", \"font-medium\", \"rounded-l-md\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"transition-all\"], [1, \"far\", \"fa-calendar-check\", \"h-5\", \"w-5\", \"mr-3\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"group-hover:text-[#3d4a85]\", \"dark:group-hover:text-[#4f5fad]\", \"transition-all\", \"group-hover:scale-110\"], [\"routerLink\", \"/reunions\", \"routerLinkActive\", \"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\", 1, \"sidebar-nav-link\", \"group\", \"flex\", \"items-center\", \"px-4\", \"py-3\", \"text-sm\", \"font-medium\", \"rounded-l-md\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"transition-all\"], [1, \"fas\", \"fa-users-cog\", \"h-5\", \"w-5\", \"mr-3\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"group-hover:text-[#3d4a85]\", \"dark:group-hover:text-[#4f5fad]\", \"transition-all\", \"group-hover:scale-110\"], [\"routerLink\", \"/messages\", \"routerLinkActive\", \"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\", 1, \"sidebar-nav-link\", \"group\", \"flex\", \"items-center\", \"px-4\", \"py-3\", \"text-sm\", \"font-medium\", \"rounded-l-md\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"transition-all\"], [1, \"far\", \"fa-comment-dots\", \"h-5\", \"w-5\", \"mr-3\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"group-hover:text-[#3d4a85]\", \"dark:group-hover:text-[#4f5fad]\", \"transition-all\", \"group-hover:scale-110\"], [\"routerLink\", \"/admin/dashboard\", \"routerLinkActive\", \"active bg-gradient-to-r from-[#7826b5]/10 to-[#9d4edd]/10 dark:from-[#7826b5]/20 dark:to-[#9d4edd]/20 text-[#7826b5] dark:text-[#9d4edd] font-medium\", 1, \"sidebar-nav-link\", \"dashboard-link\", \"group\", \"flex\", \"items-center\", \"px-4\", \"py-3\", \"text-sm\", \"font-medium\", \"rounded-l-md\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"hover:text-[#7826b5]\", \"dark:hover:text-[#9d4edd]\", \"transition-all\"], [1, \"absolute\", \"inset-0\", \"w-1\", \"bg-gradient-to-b\", \"from-[#7826b5]\", \"to-[#9d4edd]\", \"dark:from-[#7826b5]\", \"dark:to-[#9d4edd]\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\"], [1, \"fas\", \"fa-tachometer-alt\", \"h-5\", \"w-5\", \"mr-3\", \"text-[#7826b5]\", \"dark:text-[#9d4edd]\", \"group-hover:text-[#7826b5]\", \"dark:group-hover:text-[#9d4edd]\", \"transition-all\", \"group-hover:scale-110\"], [1, \"absolute\", \"inset-0\", \"bg-[#7826b5]/20\", \"dark:bg-[#9d4edd]/20\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\", \"blur-md\", \"rounded-full\"], [1, \"ml-4\", \"flex\", \"items-center\", \"md:ml-6\"], [\"routerLink\", \"/notifications\", \"class\", \"flex items-center justify-center h-10 px-4 rounded-xl bg-white dark:bg-[#1e1e1e] border border-[#4f5fad]/20 dark:border-[#6d78c9]/20 text-[#4f5fad] dark:text-[#6d78c9] mr-2 transition-all duration-300 hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a]\", \"aria-label\", \"Notifications\", 4, \"ngIf\"], [\"aria-label\", \"Toggle dark mode\", 1, \"flex\", \"items-center\", \"justify-center\", \"h-9\", \"w-9\", \"rounded-xl\", \"bg-[#edf1f4]\", \"dark:bg-[#2a2a2a]\", \"hover:bg-[#dce4ec]\", \"dark:hover:bg-[#3a3a3a]\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"mr-3\", \"transition-all\", \"duration-300\", \"relative\", \"overflow-hidden\", \"group\", 3, \"click\"], [1, \"absolute\", \"inset-0\", \"rounded-xl\", \"overflow-hidden\"], [1, \"absolute\", \"inset-0\", \"rounded-xl\", \"border-2\", \"border-[#4f5fad]/30\", \"dark:border-[#6d78c9]/30\", \"opacity-100\"], [1, \"absolute\", \"-inset-1\", \"bg-gradient-to-r\", \"from-[#4f5fad]/0\", \"via-[#4f5fad]/30\", \"to-[#4f5fad]/0\", \"dark:from-[#6d78c9]/0\", \"dark:via-[#6d78c9]/30\", \"dark:to-[#6d78c9]/0\", \"opacity-0\", \"group-hover:opacity-100\", \"blur-sm\", \"animate-shine\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/10\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\", \"blur-md\"], [1, \"relative\", \"z-10\", \"transition-all\", \"duration-500\", \"ease-in-out\", 3, \"ngClass\"], [\"class\", \"far fa-moon text-lg group-hover:scale-110 transition-transform\", 4, \"ngIf\"], [\"class\", \"far fa-sun text-lg group-hover:scale-110 transition-transform\", 4, \"ngIf\"], [\"aria-label\", \"Logout\", 1, \"flex\", \"items-center\", \"justify-center\", \"h-8\", \"w-8\", \"rounded-full\", \"bg-[#edf1f4]\", \"dark:bg-[#2a2a2a]\", \"hover:bg-[#dce4ec]\", \"dark:hover:bg-[#3a3a3a]\", \"text-[#ff6b69]\", \"dark:text-[#ff8785]\", \"mr-3\", \"transition-all\", \"duration-300\", \"relative\", \"overflow-hidden\", \"group\", 3, \"click\"], [1, \"absolute\", \"inset-0\", \"rounded-full\", \"overflow-hidden\"], [1, \"absolute\", \"inset-0\", \"rounded-full\", \"border\", \"border-[#ff6b69]/20\", \"dark:border-[#ff8785]/20\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\"], [1, \"absolute\", \"-inset-1\", \"bg-gradient-to-r\", \"from-[#ff6b69]/0\", \"via-[#ff6b69]/30\", \"to-[#ff6b69]/0\", \"dark:from-[#ff8785]/0\", \"dark:via-[#ff8785]/30\", \"dark:to-[#ff8785]/0\", \"opacity-0\", \"group-hover:opacity-100\", \"blur-sm\", \"animate-shine\"], [1, \"absolute\", \"inset-0\", \"bg-[#ff6b69]/10\", \"dark:bg-[#ff8785]/10\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\", \"blur-md\"], [1, \"fas\", \"fa-sign-out-alt\", \"relative\", \"z-10\", \"group-hover:scale-110\", \"transition-transform\"], [\"routerLink\", \"/profile\", 1, \"flex\", \"items-center\", \"bg-[#edf1f4]\", \"dark:bg-[#2a2a2a]\", \"hover:bg-[#dce4ec]\", \"dark:hover:bg-[#3a3a3a]\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"px-3\", \"py-2\", \"rounded-lg\", \"transition-all\", \"group\"], [1, \"sr-only\"], [1, \"hidden\", \"md:inline-block\", \"mr-2\", \"text-sm\", \"font-medium\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"group-hover:scale-105\", \"transition-transform\"], [1, \"h-8\", \"w-8\", \"rounded-full\", \"overflow-hidden\", \"border-2\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]\", \"flex\", \"items-center\", \"justify-center\", \"relative\", \"group-hover:border-[#3d4a85]\", \"dark:group-hover:border-[#4f5fad]\", \"transition-colors\"], [\"alt\", \"Profile\", 1, \"h-full\", \"w-full\", \"object-cover\", 3, \"src\"], [\"routerLink\", \"/notifications\", \"aria-label\", \"Notifications\", 1, \"flex\", \"items-center\", \"justify-center\", \"h-10\", \"px-4\", \"rounded-xl\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"border\", \"border-[#4f5fad]/20\", \"dark:border-[#6d78c9]/20\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"mr-2\", \"transition-all\", \"duration-300\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\"], [1, \"far\", \"fa-bell\", \"text-lg\", \"transition-transform\", \"mr-2\"], [1, \"flex\", \"items-center\", \"justify-center\", \"h-6\", \"min-w-6\", \"px-1.5\", \"rounded-md\", \"bg-gradient-to-r\", \"from-[#ff8c00]\", \"to-[#ff6b00]\", \"text-white\", \"font-bold\", \"text-xs\", \"shadow-md\", \"animate-pulse\"], [1, \"far\", \"fa-moon\", \"text-lg\", \"group-hover:scale-110\", \"transition-transform\"], [1, \"far\", \"fa-sun\", \"text-lg\", \"group-hover:scale-110\", \"transition-transform\"], [1, \"flex\", \"space-x-4\"], [\"routerLink\", \"/login\", 1, \"inline-flex\", \"items-center\", \"relative\", \"overflow-hidden\", \"group\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#3d4a85]\", \"dark:to-[#6d78c9]\", \"rounded-lg\", \"transition-transform\", \"duration-300\", \"group-hover:scale-105\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#3d4a85]\", \"dark:to-[#6d78c9]\", \"rounded-lg\", \"opacity-0\", \"group-hover:opacity-100\", \"blur-xl\", \"transition-opacity\", \"duration-300\"], [1, \"relative\", \"flex\", \"items-center\", \"text-white\", \"font-medium\", \"py-2\", \"px-4\", \"rounded-lg\", \"transition-all\"], [1, \"fas\", \"fa-sign-in-alt\", \"mr-2\"], [\"routerLink\", \"/signup\", 1, \"inline-flex\", \"items-center\", \"relative\", \"overflow-hidden\", \"group\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#7826b5]\", \"to-[#9d4edd]\", \"dark:from-[#7826b5]\", \"dark:to-[#9d4edd]\", \"rounded-lg\", \"transition-transform\", \"duration-300\", \"group-hover:scale-105\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#7826b5]\", \"to-[#9d4edd]\", \"dark:from-[#7826b5]\", \"dark:to-[#9d4edd]\", \"rounded-lg\", \"opacity-0\", \"group-hover:opacity-100\", \"blur-xl\", \"transition-opacity\", \"duration-300\"], [1, \"fas\", \"fa-user-plus\", \"mr-2\"], [\"routerLinkActive\", \"bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium border-r-2 border-[#4f5fad] dark:border-[#6d78c9]\", 1, \"flex\", \"items-center\", \"px-3\", \"py-2\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"rounded-lg\", \"transition-all\", \"relative\", \"group\", \"overflow-hidden\", 3, \"routerLink\", \"routerLinkActiveOptions\", \"click\"], [1, \"relative\", \"z-10\", \"flex\", \"items-center\", \"w-full\"], [\"class\", \"ml-auto bg-gradient-to-r from-[#ff8c00] to-[#ff6b00] text-white text-xs rounded-full h-5 min-w-5 px-1 flex items-center justify-center shadow-md animate-pulse\", 4, \"ngIf\"], [1, \"ml-auto\", \"bg-gradient-to-r\", \"from-[#ff8c00]\", \"to-[#ff6b00]\", \"text-white\", \"text-xs\", \"rounded-full\", \"h-5\", \"min-w-5\", \"px-1\", \"flex\", \"items-center\", \"justify-center\", \"shadow-md\", \"animate-pulse\"], [\"routerLink\", \"/admin/dashboard\", 1, \"flex\", \"items-center\", \"px-3\", \"py-2\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:text-[#7826b5]\", \"dark:hover:text-[#9d4edd]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"rounded-lg\", \"transition-all\", \"relative\", \"group\", \"overflow-hidden\", \"mt-2\", 3, \"click\"], [1, \"fas\", \"fa-tachometer-alt\", \"h-5\", \"w-5\", \"mr-3\", \"text-[#7826b5]\", \"dark:text-[#9d4edd]\", \"group-hover:text-[#7826b5]\", \"dark:group-hover:text-[#9d4edd]\", \"transition-colors\"], [\"routerLink\", \"/login\", 1, \"flex\", \"items-center\", \"px-3\", \"py-2\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"rounded-lg\", \"transition-all\", \"relative\", \"group\", \"overflow-hidden\", 3, \"click\"], [1, \"fas\", \"fa-sign-in-alt\", \"mr-3\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"group-hover:scale-110\", \"transition-transform\"], [\"routerLink\", \"/signup\", 1, \"flex\", \"items-center\", \"px-3\", \"py-2\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"rounded-lg\", \"transition-all\", \"relative\", \"group\", \"overflow-hidden\", 3, \"click\"], [1, \"fas\", \"fa-user-plus\", \"mr-3\", \"text-[#7826b5]\", \"dark:text-[#9d4edd]\", \"group-hover:scale-110\", \"transition-transform\"], [\"routerLink\", \"/profile\", 1, \"flex\", \"items-center\", \"px-3\", \"py-2\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"rounded-lg\", \"transition-all\", \"relative\", \"group\", \"overflow-hidden\", 3, \"click\"], [1, \"fas\", \"fa-user\", \"mr-3\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"group-hover:scale-110\", \"transition-transform\"], [1, \"flex\", \"items-center\", \"px-3\", \"py-2\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:text-[#ff6b69]\", \"dark:hover:text-[#ff8785]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"rounded-lg\", \"transition-all\", \"relative\", \"group\", \"overflow-hidden\", \"cursor-pointer\", 3, \"click\"], [1, \"absolute\", \"inset-0\", \"w-1\", \"bg-gradient-to-b\", \"from-[#ff6b69]\", \"to-[#ff8785]\", \"dark:from-[#ff6b69]\", \"dark:to-[#ff8785]\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\"], [1, \"fas\", \"fa-sign-out-alt\", \"mr-3\", \"text-[#ff6b69]\", \"dark:text-[#ff8785]\", \"group-hover:scale-110\", \"transition-transform\"], [1, \"absolute\", \"inset-0\", \"bg-[#ff6b69]/20\", \"dark:bg-[#ff8785]/20\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\", \"blur-md\", \"rounded-full\"], [1, \"max-w-7xl\", \"mx-auto\", \"px-4\", \"sm:px-6\", \"lg:px-8\", \"relative\", \"z-10\", \"mt-4\"], [1, \"bg-white\", \"dark:bg-[#1e1e1e]\", \"border-l-4\", \"border-[#ff8c00]\", \"dark:border-[#ff6b00]\", \"rounded-lg\", \"p-4\", \"mb-6\", \"shadow-md\", \"dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)]\", \"backdrop-blur-sm\"], [1, \"fas\", \"fa-info-circle\", \"text-[#ff8c00]\", \"dark:text-[#ff6b00]\", \"text-lg\", \"mr-3\"], [1, \"absolute\", \"inset-0\", \"bg-[#ff8c00]/30\", \"dark:bg-[#ff6b00]/30\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\", \"animate-pulse\"], [1, \"ml-3\", \"text-sm\", \"font-medium\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"]],\n        template: function FrontLayoutComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0);\n            i0.ɵɵpipe(1, \"async\");\n            i0.ɵɵelement(2, \"div\", 1);\n            i0.ɵɵelementStart(3, \"div\", 2)(4, \"div\", 3)(5, \"div\", 4);\n            i0.ɵɵelement(6, \"div\", 5)(7, \"div\", 6);\n            i0.ɵɵelementStart(8, \"div\", 7)(9, \"div\", 8);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(10, \"svg\", 9);\n            i0.ɵɵelement(11, \"path\", 10)(12, \"path\", 11);\n            i0.ɵɵelementEnd();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelement(13, \"div\", 12);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(14, \"span\", 13);\n            i0.ɵɵtext(15, \"DevBridge\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(16, \"div\", 14)(17, \"nav\", 15);\n            i0.ɵɵtemplate(18, FrontLayoutComponent_a_18_Template, 8, 2, \"a\", 16);\n            i0.ɵɵtemplate(19, FrontLayoutComponent_a_19_Template, 8, 0, \"a\", 17);\n            i0.ɵɵtemplate(20, FrontLayoutComponent_a_20_Template, 8, 0, \"a\", 18);\n            i0.ɵɵtemplate(21, FrontLayoutComponent_a_21_Template, 8, 0, \"a\", 19);\n            i0.ɵɵtemplate(22, FrontLayoutComponent_a_22_Template, 8, 0, \"a\", 20);\n            i0.ɵɵelement(23, \"div\", 21);\n            i0.ɵɵtemplate(24, FrontLayoutComponent_a_24_Template, 8, 0, \"a\", 22);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(25, \"header\", 23)(26, \"div\", 24)(27, \"div\", 25);\n            i0.ɵɵelement(28, \"div\", 26)(29, \"div\", 27);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(30, \"div\", 28)(31, \"div\", 29)(32, \"button\", 30);\n            i0.ɵɵlistener(\"click\", function FrontLayoutComponent_Template_button_click_32_listener() {\n              return ctx.toggleSidebar();\n            });\n            i0.ɵɵelement(33, \"div\", 31);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(34, \"svg\", 32);\n            i0.ɵɵelement(35, \"path\", 33);\n            i0.ɵɵelementEnd();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(36, \"span\", 34);\n            i0.ɵɵtext(37, \"Menu\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(38, \"a\", 35)(39, \"div\", 8)(40, \"h1\", 36);\n            i0.ɵɵtext(41, \" DevBridge \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(42, \"div\", 37);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(43, \"span\", 38);\n            i0.ɵɵtext(44, \"Project Management Suite\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(45, \"div\", 29);\n            i0.ɵɵtemplate(46, FrontLayoutComponent_ng_container_46_Template, 28, 14, \"ng-container\", 39);\n            i0.ɵɵtemplate(47, FrontLayoutComponent_ng_template_47_Template, 13, 0, \"ng-template\", null, 40, i0.ɵɵtemplateRefExtractor);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(49, \"div\", 41);\n            i0.ɵɵlistener(\"click\", function FrontLayoutComponent_Template_div_click_49_listener() {\n              return ctx.sidebarOpen && ctx.toggleSidebar();\n            });\n            i0.ɵɵelement(50, \"div\", 42);\n            i0.ɵɵelementStart(51, \"div\", 43);\n            i0.ɵɵlistener(\"click\", function FrontLayoutComponent_Template_div_click_51_listener($event) {\n              return $event.stopPropagation();\n            });\n            i0.ɵɵelementStart(52, \"div\", 44)(53, \"div\", 25);\n            i0.ɵɵelement(54, \"div\", 26)(55, \"div\", 27)(56, \"div\", 45)(57, \"div\", 46);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(58, \"div\", 47)(59, \"div\")(60, \"h3\", 48);\n            i0.ɵɵtext(61, \" DevBridge \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(62, \"p\", 49);\n            i0.ɵɵtext(63, \" Project Management Suite \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(64, \"button\", 50);\n            i0.ɵɵlistener(\"click\", function FrontLayoutComponent_Template_button_click_64_listener() {\n              return ctx.toggleSidebar();\n            });\n            i0.ɵɵelement(65, \"div\", 51)(66, \"i\", 52);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(67, \"nav\", 53);\n            i0.ɵɵtemplate(68, FrontLayoutComponent_a_68_Template, 9, 9, \"a\", 54);\n            i0.ɵɵtemplate(69, FrontLayoutComponent_a_69_Template, 8, 0, \"a\", 55);\n            i0.ɵɵelement(70, \"div\", 21);\n            i0.ɵɵtemplate(71, FrontLayoutComponent_ng_container_71_Template, 17, 0, \"ng-container\", 56);\n            i0.ɵɵtemplate(72, FrontLayoutComponent_ng_container_72_Template, 17, 0, \"ng-container\", 56);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(73, \"div\", 57)(74, \"main\", 58)(75, \"div\", 25);\n            i0.ɵɵelement(76, \"div\", 59)(77, \"div\", 60);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(78, FrontLayoutComponent_div_78_Template, 8, 1, \"div\", 61);\n            i0.ɵɵelementStart(79, \"div\", 62);\n            i0.ɵɵelement(80, \"router-outlet\");\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            const _r7 = i0.ɵɵreference(48);\n            i0.ɵɵclassProp(\"dark\", i0.ɵɵpipeBind1(1, 21, ctx.isDarkMode$));\n            i0.ɵɵadvance(18);\n            i0.ɵɵproperty(\"ngIf\", ctx.authService.userLoggedIn());\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.authService.userLoggedIn());\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.authService.userLoggedIn());\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.authService.userLoggedIn());\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.authService.userLoggedIn());\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.authService.userLoggedIn() && ((ctx.currentUser == null ? null : ctx.currentUser.role) === \"admin\" || (ctx.currentUser == null ? null : ctx.currentUser.role) === \"teacher\"));\n            i0.ɵɵadvance(22);\n            i0.ɵɵproperty(\"ngIf\", ctx.authService.userLoggedIn())(\"ngIfElse\", _r7);\n            i0.ɵɵadvance(3);\n            i0.ɵɵclassProp(\"hidden\", !ctx.sidebarOpen);\n            i0.ɵɵadvance(2);\n            i0.ɵɵclassProp(\"translate-x-0\", ctx.sidebarOpen)(\"-translate-x-full\", !ctx.sidebarOpen);\n            i0.ɵɵadvance(17);\n            i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction6(30, _c9, i0.ɵɵpureFunction0(23, _c3), i0.ɵɵpureFunction0(24, _c4), i0.ɵɵpureFunction0(25, _c5), i0.ɵɵpureFunction0(26, _c6), i0.ɵɵpureFunction0(27, _c7), i0.ɵɵpureFunction1(28, _c8, ctx.unreadNotificationsCount)));\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.authService.userLoggedIn() && ((ctx.currentUser == null ? null : ctx.currentUser.role) === \"admin\" || (ctx.currentUser == null ? null : ctx.currentUser.role) === \"teacher\"));\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", !ctx.authService.userLoggedIn());\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.authService.userLoggedIn());\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngIf\", ctx.messageFromRedirect);\n          }\n        },\n        dependencies: [i7.NgClass, i7.NgForOf, i7.NgIf, i2.RouterOutlet, i2.RouterLink, i2.RouterLinkActive, i7.AsyncPipe],\n        styles: [\"@charset \\\"UTF-8\\\";.futuristic-layout[_ngcontent-%COMP%]{background-color:var(--dark-bg);color:var(--text-light);min-height:100vh;position:relative}.futuristic-layout[_ngcontent-%COMP%]:not(.dark){background-color:#f0f4f8;color:#6d6870}.dark[_ngcontent-%COMP%]   .futuristic-layout[_ngcontent-%COMP%]{background-color:#121212}.futuristic-layout[_ngcontent-%COMP%]:not(.dark){background-color:#f0f4f8}.futuristic-header[_ngcontent-%COMP%]{background-color:var(--medium-bg);border-bottom:1px solid rgba(0,247,255,.2);box-shadow:0 4px 20px #0000004d}.futuristic-logo[_ngcontent-%COMP%]{background:linear-gradient(135deg,var(--accent-color),var(--secondary-color));-webkit-background-clip:text;background-clip:text;color:transparent;text-shadow:0 0 10px rgba(0,247,255,.5)}.futuristic-subtitle[_ngcontent-%COMP%]{color:var(--text-dim)}.futuristic-nav-link[_ngcontent-%COMP%]{color:var(--text-dim);padding:.75rem 1rem;font-size:.875rem;font-weight:500;transition:all var(--transition-fast);position:relative;overflow:hidden}.futuristic-nav-link[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;bottom:0;left:50%;transform:translate(-50%);width:0;height:2px;background:linear-gradient(90deg,var(--accent-color),var(--secondary-color));transition:width var(--transition-fast)}.futuristic-nav-link[_ngcontent-%COMP%]:hover{color:var(--text-light)}.futuristic-nav-link[_ngcontent-%COMP%]:hover:before{width:80%}.futuristic-nav-link-active[_ngcontent-%COMP%]{color:var(--accent-color)}.futuristic-nav-link-active[_ngcontent-%COMP%]:before{width:80%}.futuristic-profile-button[_ngcontent-%COMP%]{display:flex;align-items:center;background:transparent;border:none;cursor:pointer;padding:.5rem;border-radius:var(--border-radius-md);transition:all var(--transition-fast)}.futuristic-profile-button[_ngcontent-%COMP%]:hover{background-color:#00f7ff1a}.futuristic-username[_ngcontent-%COMP%]{color:var(--text-light)}.futuristic-dropdown-menu[_ngcontent-%COMP%]{position:absolute;right:0;top:100%;margin-top:.5rem;width:12rem;background-color:var(--medium-bg);border:1px solid rgba(0,247,255,.2);border-radius:var(--border-radius-md);box-shadow:0 10px 25px #0000004d;overflow:hidden;z-index:50;animation:_ngcontent-%COMP%_fadeIn .2s ease-out}.futuristic-dropdown-item[_ngcontent-%COMP%]{display:flex;align-items:center;padding:.75rem 1rem;color:var(--text-dim);font-size:.875rem;transition:all var(--transition-fast);cursor:pointer}.futuristic-dropdown-item[_ngcontent-%COMP%]:hover{background-color:#00f7ff1a;color:var(--text-light)}.futuristic-login-button[_ngcontent-%COMP%]{display:flex;align-items:center;padding:.5rem 1rem;color:var(--accent-color);font-size:.875rem;font-weight:500;border-radius:var(--border-radius-md);transition:all var(--transition-fast);border:1px solid rgba(0,247,255,.3)}.futuristic-login-button[_ngcontent-%COMP%]:hover{background-color:#00f7ff1a;transform:translateY(-2px);box-shadow:var(--glow-effect)}.futuristic-register-button[_ngcontent-%COMP%]{display:flex;align-items:center;padding:.5rem 1rem;background:linear-gradient(135deg,var(--accent-color),var(--secondary-color));color:var(--text-light);font-size:.875rem;font-weight:500;border-radius:var(--border-radius-md);transition:all var(--transition-fast);box-shadow:0 2px 5px #0003}.futuristic-register-button[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:var(--glow-effect)}.futuristic-sidebar[_ngcontent-%COMP%]{background-color:var(--medium-bg);border-right:1px solid rgba(0,247,255,.2);box-shadow:4px 0 20px #0000004d}.futuristic-sidebar-header[_ngcontent-%COMP%]{border-bottom:1px solid rgba(0,247,255,.2)}.futuristic-close-button[_ngcontent-%COMP%]{width:32px;height:32px;display:flex;align-items:center;justify-content:center;background-color:#00f7ff1a;color:var(--accent-color);border:none;border-radius:50%;cursor:pointer;transition:all var(--transition-fast)}.futuristic-close-button[_ngcontent-%COMP%]:hover{background-color:#00f7ff33;transform:rotate(90deg)}.futuristic-sidebar-nav[_ngcontent-%COMP%]{scrollbar-width:thin;scrollbar-color:var(--accent-color) transparent}.futuristic-sidebar-nav[_ngcontent-%COMP%]::-webkit-scrollbar{width:4px}.futuristic-sidebar-nav[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:transparent}.futuristic-sidebar-nav[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background-color:var(--accent-color);border-radius:10px}.futuristic-sidebar-link[_ngcontent-%COMP%]{display:flex;align-items:center;padding:.75rem 1rem;color:var(--text-dim);font-size:.875rem;font-weight:500;border-radius:var(--border-radius-md);transition:all var(--transition-fast);position:relative}.futuristic-sidebar-link[_ngcontent-%COMP%]:hover{background-color:#00f7ff1a;color:var(--text-light)}.futuristic-sidebar-link-active[_ngcontent-%COMP%]{background-color:#00f7ff26;color:var(--accent-color);box-shadow:0 0 10px #0003}.futuristic-sidebar-icon[_ngcontent-%COMP%]{margin-right:.75rem;width:1.5rem;height:1.5rem;color:var(--text-dim);transition:color var(--transition-fast)}.futuristic-sidebar-icon-fa[_ngcontent-%COMP%]{margin-right:.75rem;width:1.5rem;font-size:1.25rem;color:var(--text-dim);transition:color var(--transition-fast);text-align:center}.futuristic-sidebar-link[_ngcontent-%COMP%]:hover   .futuristic-sidebar-icon[_ngcontent-%COMP%], .futuristic-sidebar-link[_ngcontent-%COMP%]:hover   .futuristic-sidebar-icon-fa[_ngcontent-%COMP%], .futuristic-sidebar-link-active[_ngcontent-%COMP%]   .futuristic-sidebar-icon[_ngcontent-%COMP%], .futuristic-sidebar-link-active[_ngcontent-%COMP%]   .futuristic-sidebar-icon-fa[_ngcontent-%COMP%]{color:var(--accent-color)}.futuristic-separator[_ngcontent-%COMP%]{height:1px;background:linear-gradient(to right,transparent,rgba(0,247,255,.2),transparent)}.futuristic-badge[_ngcontent-%COMP%]{background:linear-gradient(135deg,var(--accent-color),var(--secondary-color));color:var(--text-light);font-size:.75rem;font-weight:500;border-radius:9999px;padding:2px 8px;min-width:1.5rem;text-align:center;box-shadow:var(--glow-effect)}.futuristic-main-content[_ngcontent-%COMP%]{position:relative;z-index:1}.futuristic-status-message[_ngcontent-%COMP%]{background-color:#00f7ff1a;border-left:4px solid var(--accent-color);border-radius:var(--border-radius-md);padding:1rem;box-shadow:0 4px 10px #0000001a}.futuristic-status-icon[_ngcontent-%COMP%]{color:var(--accent-color);font-size:1.25rem}.futuristic-status-text[_ngcontent-%COMP%]{color:var(--text-light)}@keyframes _ngcontent-%COMP%_fadeIn{0%{opacity:0;transform:translateY(10px)}to{opacity:1;transform:translateY(0)}}.main-content-grid[_ngcontent-%COMP%]{z-index:0;pointer-events:none}.main-content-grid[_ngcontent-%COMP%]{pointer-events:none;z-index:0}.sidebar-nav-link[_ngcontent-%COMP%]{position:relative;overflow:hidden}.sidebar-nav-link[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;inset:0;border-radius:.375rem 0 0 .375rem;border:2px solid rgba(79,95,173,.1);pointer-events:none}.dark[_ngcontent-%COMP%]   .sidebar-nav-link[_ngcontent-%COMP%]:before{border-color:#6d78c91a}.sidebar-nav-link.active[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;top:0;bottom:0;right:0;width:.5rem;background:linear-gradient(to bottom,#4f5fad,#00f7ff,#4f5fad);border-radius:0 .375rem .375rem 0;animation:_ngcontent-%COMP%_pulse 2s infinite;box-shadow:0 0 15px #00f7ffb3}.dark[_ngcontent-%COMP%]   .sidebar-nav-link.active[_ngcontent-%COMP%]:after{background:linear-gradient(to bottom,#6d78c9,#00f7ff,#6d78c9);box-shadow:0 0 15px #00f7ffb3}@keyframes _ngcontent-%COMP%_pulse{0%{opacity:.7}50%{opacity:1}to{opacity:.7}}.sidebar-nav-link.active[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;bottom:0;right:0;width:.5rem;background:linear-gradient(to bottom,#4f5fad,#00f7ff,#4f5fad);border-radius:0 .375rem .375rem 0;filter:blur(8px);transform:scale(1.5);opacity:.5;animation:_ngcontent-%COMP%_pulse 2s infinite}.dark[_ngcontent-%COMP%]   .sidebar-nav-link.active[_ngcontent-%COMP%]:before{background:linear-gradient(to bottom,#6d78c9,#00f7ff,#6d78c9)}.dashboard-link.active[_ngcontent-%COMP%]:after{background:linear-gradient(to bottom,#7826b5,#9d4edd,#7826b5);box-shadow:0 0 15px #9d4eddb3}.dark[_ngcontent-%COMP%]   .dashboard-link.active[_ngcontent-%COMP%]:after{background:linear-gradient(to bottom,#7826b5,#9d4edd,#7826b5)}.dashboard-link.active[_ngcontent-%COMP%]:before{background:linear-gradient(to bottom,#7826b5,#9d4edd,#7826b5)}\"]\n      });\n    }\n  }\n  return FrontLayoutComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}