{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/equipe.service\";\nimport * as i2 from \"src/app/services/membre.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nfunction EquipeComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35)(2, \"div\", 36)(3, \"div\", 37)(4, \"div\", 38);\n    i0.ɵɵelement(5, \"i\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 40)(7, \"h3\", 41);\n    i0.ɵɵtext(8, \" Erreur \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\", 42);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function EquipeComponent_div_18_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.error = \"\");\n    });\n    i0.ɵɵelement(12, \"i\", 44);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.error, \" \");\n  }\n}\nfunction EquipeComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"div\", 46);\n    i0.ɵɵelement(2, \"div\", 47)(3, \"div\", 48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 49);\n    i0.ɵɵtext(5, \" Chargement... \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction EquipeComponent_div_20_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61)(1, \"div\", 62);\n    i0.ɵɵelement(2, \"i\", 63);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\", 64);\n    i0.ɵɵtext(4, \" Aucune \\u00E9quipe trouv\\u00E9e \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 56);\n    i0.ɵɵtext(6, \" Cr\\u00E9ez votre premi\\u00E8re \\u00E9quipe ci-dessous \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction EquipeComponent_div_20_div_15_tr_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 72)(1, \"td\", 73)(2, \"div\", 74);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"td\", 73)(5, \"div\", 75);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"td\", 73)(8, \"div\", 42);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"td\", 73)(11, \"span\", 76);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"td\", 73)(14, \"div\", 77)(15, \"button\", 78);\n    i0.ɵɵlistener(\"click\", function EquipeComponent_div_20_div_15_tr_16_Template_button_click_15_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r17);\n      const equipe_r15 = restoredCtx.$implicit;\n      const ctx_r16 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r16.editEquipe(equipe_r15));\n    });\n    i0.ɵɵelement(16, \"i\", 79);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"button\", 80);\n    i0.ɵɵlistener(\"click\", function EquipeComponent_div_20_div_15_tr_16_Template_button_click_17_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r17);\n      const equipe_r15 = restoredCtx.$implicit;\n      const ctx_r18 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r18.showMembreModal(equipe_r15));\n    });\n    i0.ɵɵelement(18, \"i\", 81);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"button\", 82);\n    i0.ɵɵlistener(\"click\", function EquipeComponent_div_20_div_15_tr_16_Template_button_click_19_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r17);\n      const equipe_r15 = restoredCtx.$implicit;\n      const ctx_r19 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(equipe_r15._id && ctx_r19.deleteEquipe(equipe_r15._id));\n    });\n    i0.ɵɵelement(20, \"i\", 83);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const equipe_r15 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", equipe_r15.name, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", equipe_r15.description || \"Aucune description\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", equipe_r15.admin || \"Non d\\u00E9fini\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", (equipe_r15.members == null ? null : equipe_r15.members.length) || 0, \" membre(s) \");\n  }\n}\nfunction EquipeComponent_div_20_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"div\", 65)(2, \"table\", 66)(3, \"thead\", 67)(4, \"tr\")(5, \"th\", 68);\n    i0.ɵɵtext(6, \" Nom \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"th\", 68);\n    i0.ɵɵtext(8, \" Description \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\", 68);\n    i0.ɵɵtext(10, \" Admin \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"th\", 68);\n    i0.ɵɵtext(12, \" Membres \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"th\", 69);\n    i0.ɵɵtext(14, \" Actions \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"tbody\", 70);\n    i0.ɵɵtemplate(16, EquipeComponent_div_20_div_15_tr_16_Template, 21, 4, \"tr\", 71);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(16);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r13.equipes);\n  }\n}\nfunction EquipeComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"div\", 51);\n    i0.ɵɵelement(2, \"div\", 12)(3, \"div\", 13);\n    i0.ɵɵelementStart(4, \"div\", 52)(5, \"div\", 53)(6, \"div\", 54)(7, \"h2\", 55);\n    i0.ɵɵtext(8, \" Liste des \\u00E9quipes \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\", 56);\n    i0.ɵɵtext(10, \" G\\u00E9rez toutes vos \\u00E9quipes depuis cette interface \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"button\", 57);\n    i0.ɵɵlistener(\"click\", function EquipeComponent_div_20_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.loadEquipes());\n    });\n    i0.ɵɵelement(12, \"i\", 58);\n    i0.ɵɵtext(13, \" Rafra\\u00EEchir \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(14, EquipeComponent_div_20_div_14_Template, 7, 0, \"div\", 59);\n    i0.ɵɵtemplate(15, EquipeComponent_div_20_div_15_Template, 17, 1, \"div\", 60);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(14);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.equipes.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.equipes.length > 0);\n  }\n}\nfunction EquipeComponent_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 84);\n    i0.ɵɵtext(1, \" Le nom de l'\\u00E9quipe est requis \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipeComponent_span_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 85);\n  }\n}\nfunction EquipeComponent_i_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 86);\n  }\n}\nfunction EquipeComponent_button_52_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 87);\n    i0.ɵɵlistener(\"click\", function EquipeComponent_button_52_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r22 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r22.cancelEdit());\n    });\n    i0.ɵɵelement(1, \"i\", 88);\n    i0.ɵɵtext(2, \" Annuler \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipeComponent_div_53_div_16_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r31 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 115)(1, \"div\", 116)(2, \"div\", 117);\n    i0.ɵɵelement(3, \"i\", 118);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 119);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"button\", 120);\n    i0.ɵɵlistener(\"click\", function EquipeComponent_div_53_div_16_div_2_Template_button_click_6_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r31);\n      const membreId_r29 = restoredCtx.$implicit;\n      const ctx_r30 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r30.removeMembreFromEquipe(ctx_r30.selectedEquipe._id, membreId_r29));\n    });\n    i0.ɵɵelement(7, \"i\", 121);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const membreId_r29 = ctx.$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(membreId_r29);\n  }\n}\nfunction EquipeComponent_div_53_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 113);\n    i0.ɵɵtemplate(2, EquipeComponent_div_53_div_16_div_2_Template, 8, 1, \"div\", 114);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r24 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r24.selectedEquipe.members);\n  }\n}\nfunction EquipeComponent_div_53_ng_template_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 122)(1, \"div\", 123);\n    i0.ɵɵelement(2, \"i\", 124);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 56);\n    i0.ɵɵtext(4, \" Aucun membre dans cette \\u00E9quipe \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction EquipeComponent_div_53_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r33 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 89);\n    i0.ɵɵlistener(\"click\", function EquipeComponent_div_53_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r32 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r32.selectedEquipe = null);\n    });\n    i0.ɵɵelementStart(1, \"div\", 90);\n    i0.ɵɵlistener(\"click\", function EquipeComponent_div_53_Template_div_click_1_listener($event) {\n      return $event.stopPropagation();\n    });\n    i0.ɵɵelementStart(2, \"div\", 15)(3, \"div\", 91)(4, \"div\")(5, \"h3\", 16);\n    i0.ɵɵtext(6, \" G\\u00E9rer les membres \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 17);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"button\", 92);\n    i0.ɵɵlistener(\"click\", function EquipeComponent_div_53_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r35 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r35.selectedEquipe = null);\n    });\n    i0.ɵɵelement(10, \"i\", 93);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 94)(12, \"div\", 34)(13, \"h4\", 95);\n    i0.ɵɵelement(14, \"i\", 96);\n    i0.ɵɵtext(15, \" Membres actuels \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(16, EquipeComponent_div_53_div_16_Template, 3, 1, \"div\", 97);\n    i0.ɵɵtemplate(17, EquipeComponent_div_53_ng_template_17_Template, 5, 0, \"ng-template\", null, 98, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 34)(20, \"h4\", 95);\n    i0.ɵɵelement(21, \"i\", 99);\n    i0.ɵɵtext(22, \" Ajouter un membre \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 100);\n    i0.ɵɵelement(24, \"input\", 101, 102);\n    i0.ɵɵelementStart(26, \"button\", 103);\n    i0.ɵɵlistener(\"click\", function EquipeComponent_div_53_Template_button_click_26_listener() {\n      i0.ɵɵrestoreView(_r33);\n      const _r27 = i0.ɵɵreference(25);\n      const ctx_r36 = i0.ɵɵnextContext();\n      ctx_r36.addMembreToEquipe(ctx_r36.selectedEquipe._id, _r27.value);\n      return i0.ɵɵresetView(_r27.value = \"\");\n    });\n    i0.ɵɵelement(27, \"i\", 104);\n    i0.ɵɵtext(28, \" Ajouter \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"p\", 105);\n    i0.ɵɵtext(30, \" Entrez l'ID du membre \\u00E0 ajouter \\u00E0 l'\\u00E9quipe \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"div\", 106)(32, \"div\", 37)(33, \"div\", 107);\n    i0.ɵɵelement(34, \"i\", 108);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"div\")(36, \"h5\", 109);\n    i0.ɵɵtext(37, \" Information importante \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"p\", 42);\n    i0.ɵɵtext(39, \" Pour ajouter un membre, vous devez d'abord cr\\u00E9er le membre dans la section des membres. \");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(40, \"div\", 110)(41, \"div\", 111)(42, \"button\", 112);\n    i0.ɵɵlistener(\"click\", function EquipeComponent_div_53_Template_button_click_42_listener() {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r37 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r37.selectedEquipe = null);\n    });\n    i0.ɵɵelement(43, \"i\", 88);\n    i0.ɵɵtext(44, \" Fermer \");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const _r25 = i0.ɵɵreference(18);\n    const _r27 = i0.ɵɵreference(25);\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \\u00C9quipe: \", ctx_r9.selectedEquipe.name, \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.selectedEquipe.members && ctx_r9.selectedEquipe.members.length > 0)(\"ngIfElse\", _r25);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"disabled\", !ctx_r9.selectedEquipe || !ctx_r9.selectedEquipe._id || !_r27.value);\n  }\n}\nexport class EquipeComponent {\n  constructor(equipeService, membreService) {\n    this.equipeService = equipeService;\n    this.membreService = membreService;\n    this.equipes = [];\n    this.newEquipe = {\n      name: '',\n      description: ''\n    };\n    this.selectedEquipe = null;\n    this.isEditing = false;\n    this.membres = [];\n    this.loading = false;\n    this.error = '';\n  }\n  ngOnInit() {\n    this.loadEquipes();\n    this.loadMembres();\n  }\n  loadEquipes() {\n    this.loading = true;\n    this.equipeService.getEquipes().subscribe({\n      next: data => {\n        console.log('Loaded equipes:', data);\n        this.equipes = data;\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Error loading equipes:', error);\n        this.error = 'Erreur lors du chargement des équipes: ' + error.message;\n        this.loading = false;\n      }\n    });\n  }\n  loadMembres() {\n    this.loading = true;\n    this.membreService.getMembres().subscribe({\n      next: data => {\n        console.log('Loaded membres:', data);\n        this.membres = data;\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Error loading membres:', error);\n        this.error = 'Erreur lors du chargement des membres: ' + error.message;\n        this.loading = false;\n      }\n    });\n  }\n  addEquipe() {\n    console.log('Adding equipe:', this.newEquipe);\n    if (!this.newEquipe.name) {\n      console.error('Team name is required');\n      this.error = 'Le nom de l\\'équipe est requis';\n      return;\n    }\n    this.loading = true;\n    this.error = '';\n    this.equipeService.addEquipe(this.newEquipe).subscribe({\n      next: response => {\n        console.log('Equipe added successfully:', response);\n        this.loadEquipes();\n        this.newEquipe = {\n          name: '',\n          description: ''\n        }; // Clear input\n        this.loading = false;\n        // Afficher un message de succès temporaire\n        const successMessage = 'Équipe créée avec succès!';\n        this.error = ''; // Effacer les erreurs précédentes\n        alert(successMessage);\n      },\n      error: error => {\n        console.error('Error adding equipe:', error);\n        this.error = 'Erreur lors de la création de l\\'équipe: ' + (error.error?.message || error.message || 'Unknown error');\n        this.loading = false;\n      }\n    });\n  }\n  editEquipe(equipe) {\n    this.isEditing = true;\n    // Créer une copie profonde pour éviter de modifier l'objet original\n    this.newEquipe = {\n      _id: equipe._id,\n      name: equipe.name || '',\n      description: equipe.description || '',\n      admin: equipe.admin,\n      members: equipe.members ? [...equipe.members] : []\n    };\n  }\n  cancelEdit() {\n    this.isEditing = false;\n    this.newEquipe = {\n      name: '',\n      description: ''\n    };\n    this.error = ''; // Effacer les erreurs\n  }\n\n  updateSelectedEquipe() {\n    if (!this.newEquipe.name) {\n      console.error('Team name is required');\n      this.error = 'Le nom de l\\'équipe est requis';\n      return;\n    }\n    if (this.newEquipe._id) {\n      this.loading = true;\n      this.error = '';\n      this.equipeService.updateEquipe(this.newEquipe._id, this.newEquipe).subscribe({\n        next: updatedEquipe => {\n          console.log('Team updated successfully:', updatedEquipe);\n          this.loadEquipes();\n          this.isEditing = false;\n          this.newEquipe = {\n            name: '',\n            description: ''\n          };\n          this.loading = false;\n          // Afficher un message de succès temporaire\n          const successMessage = 'Équipe mise à jour avec succès!';\n          alert(successMessage);\n        },\n        error: error => {\n          console.error('Error updating team:', error);\n          this.error = 'Erreur lors de la mise à jour de l\\'équipe: ' + (error.error?.message || error.message || 'Unknown error');\n          this.loading = false;\n        }\n      });\n    } else {\n      this.error = 'ID de l\\'équipe manquant pour la mise à jour';\n    }\n  }\n  deleteEquipe(id) {\n    if (!id) {\n      console.error('ID is undefined');\n      this.error = 'ID de l\\'équipe non défini';\n      return;\n    }\n    if (confirm('Êtes-vous sûr de vouloir supprimer cette équipe? Cette action est irréversible.')) {\n      this.loading = true;\n      this.error = '';\n      this.equipeService.deleteEquipe(id).subscribe({\n        next: response => {\n          console.log('Team deleted successfully:', response);\n          // Si l'équipe en cours d'édition est celle qui vient d'être supprimée, réinitialiser le formulaire\n          if (this.isEditing && this.newEquipe._id === id) {\n            this.isEditing = false;\n            this.newEquipe = {\n              name: '',\n              description: ''\n            };\n          }\n          this.loadEquipes();\n          this.loading = false;\n          // Afficher un message de succès\n          alert('Équipe supprimée avec succès');\n        },\n        error: error => {\n          console.error('Error deleting team:', error);\n          this.error = 'Erreur lors de la suppression de l\\'équipe: ' + (error.error?.message || error.message || 'Unknown error');\n          this.loading = false;\n        }\n      });\n    }\n  }\n  showMembreModal(equipe) {\n    this.selectedEquipe = equipe;\n    // Ouvrir le modal avec Bootstrap 5\n    const modalRef = document.getElementById('membreModal');\n    if (modalRef) {\n      try {\n        // Ensure Bootstrap is properly loaded\n        if (typeof window !== 'undefined' && window.bootstrap) {\n          const modal = new window.bootstrap.Modal(modalRef);\n          modal.show();\n        } else {\n          console.error('Bootstrap is not loaded properly');\n          alert('Erreur: Bootstrap n\\'est pas chargé correctement');\n        }\n      } catch (error) {\n        console.error('Error showing modal:', error);\n      }\n    } else {\n      console.error('Modal element not found');\n    }\n  }\n  addMembreToEquipe(teamId, membreId) {\n    if (!teamId) {\n      console.error('Team ID is undefined');\n      alert('ID de l\\'équipe non défini');\n      return;\n    }\n    if (!membreId || membreId.trim() === '') {\n      console.error('Member ID is empty');\n      alert('L\\'ID du membre est requis');\n      return;\n    }\n    this.loading = true;\n    // Create a proper Membre object that matches what the API expects\n    const membre = {\n      id: membreId\n    };\n    this.equipeService.addMembreToEquipe(teamId, membre).subscribe({\n      next: response => {\n        console.log('Member added successfully:', response);\n        this.loadEquipes();\n        this.loading = false;\n        // Afficher un message de succès\n        alert('Membre ajouté avec succès à l\\'équipe');\n      },\n      error: error => {\n        console.error('Error adding member:', error);\n        this.error = 'Erreur lors de l\\'ajout du membre: ' + (error.error?.message || error.message || 'Unknown error');\n        alert(this.error);\n        this.loading = false;\n      }\n    });\n  }\n  removeMembreFromEquipe(teamId, membreId) {\n    if (!teamId) {\n      console.error('Team ID is undefined');\n      alert('ID de l\\'équipe non défini');\n      return;\n    }\n    if (!membreId) {\n      console.error('Member ID is undefined');\n      alert('ID du membre non défini');\n      return;\n    }\n    if (confirm('Êtes-vous sûr de vouloir retirer ce membre de l\\'équipe?')) {\n      this.loading = true;\n      this.equipeService.removeMembreFromEquipe(teamId, membreId).subscribe({\n        next: response => {\n          console.log('Member removed successfully:', response);\n          this.loadEquipes();\n          this.loading = false;\n          // Si l'équipe sélectionnée est celle dont on vient de retirer un membre, mettre à jour l'équipe sélectionnée\n          if (this.selectedEquipe && this.selectedEquipe._id === teamId) {\n            const updatedEquipe = this.equipes.find(e => e._id === teamId);\n            if (updatedEquipe) {\n              this.selectedEquipe = updatedEquipe;\n            }\n          }\n        },\n        error: error => {\n          console.error('Error removing member:', error);\n          this.error = 'Erreur lors de la suppression du membre: ' + (error.error?.message || error.message || 'Unknown error');\n          alert(this.error);\n          this.loading = false;\n        }\n      });\n    }\n  }\n  static {\n    this.ɵfac = function EquipeComponent_Factory(t) {\n      return new (t || EquipeComponent)(i0.ɵɵdirectiveInject(i1.EquipeService), i0.ɵɵdirectiveInject(i2.MembreService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: EquipeComponent,\n      selectors: [[\"app-equipe\"]],\n      decls: 54,\n      vars: 18,\n      consts: [[1, \"min-h-screen\", \"bg-[#f0f4f8]\", \"dark:bg-[#0a0a0a]\", \"relative\", \"overflow-hidden\"], [1, \"absolute\", \"inset-0\", \"overflow-hidden\", \"pointer-events-none\"], [1, \"absolute\", \"top-[15%]\", \"left-[10%]\", \"w-64\", \"h-64\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#dac4ea]/5\", \"to-transparent\", \"dark:from-[#00f7ff]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"bottom-[20%]\", \"right-[10%]\", \"w-80\", \"h-80\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#dac4ea]/5\", \"to-transparent\", \"dark:from-[#00f7ff]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"inset-0\", \"opacity-5\", \"dark:opacity-[0.03]\"], [1, \"h-full\", \"grid\", \"grid-cols-12\"], [1, \"border-r\", \"border-[#dac4ea]\", \"dark:border-[#00f7ff]\"], [1, \"max-w-7xl\", \"mx-auto\", \"p-6\", \"relative\", \"z-10\"], [\"class\", \"mb-6\", 4, \"ngIf\"], [\"class\", \"flex flex-col items-center justify-center py-16\", 4, \"ngIf\"], [\"class\", \"mb-8\", 4, \"ngIf\"], [1, \"mb-8\", \"relative\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#dac4ea]\", \"to-[#8b5a9f]\", \"dark:from-[#00f7ff]\", \"dark:to-[#dac4ea]\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#dac4ea]\", \"to-[#8b5a9f]\", \"dark:from-[#00f7ff]\", \"dark:to-[#dac4ea]\", \"blur-md\"], [1, \"bg-white\", \"dark:bg-[#1a1a1a]\", \"rounded-xl\", \"shadow-lg\", \"dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)]\", \"overflow-hidden\", \"border\", \"border-[#dac4ea]/20\", \"dark:border-[#00f7ff]/20\"], [1, \"bg-gradient-to-r\", \"from-[#dac4ea]\", \"to-[#8b5a9f]\", \"dark:from-[#00f7ff]\", \"dark:to-[#dac4ea]\", \"p-6\"], [1, \"text-xl\", \"font-bold\", \"text-white\", \"mb-1\"], [1, \"text-white/80\", \"text-sm\"], [1, \"p-6\"], [1, \"space-y-6\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-[#dac4ea]\", \"dark:text-[#00f7ff]\", \"mb-2\"], [1, \"text-[#ff6b69]\", \"dark:text-[#ff3b30]\"], [\"type\", \"text\", \"placeholder\", \"Entrez le nom de l'\\u00E9quipe\", \"required\", \"\", 1, \"w-full\", \"px-4\", \"py-3\", \"bg-[#f0f4f8]\", \"dark:bg-[#0a0a0a]\", \"border\", \"border-[#dac4ea]/20\", \"dark:border-[#00f7ff]/20\", \"rounded-lg\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"placeholder-[#6d6870]/50\", \"dark:placeholder-[#a0a0a0]/50\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-[#dac4ea]\", \"dark:focus:ring-[#00f7ff]\", \"focus:border-transparent\", \"transition-all\", 3, \"value\", \"input\"], [\"nameInput\", \"\"], [\"class\", \"mt-1 text-sm text-[#ff6b69] dark:text-[#ff3b30]\", 4, \"ngIf\"], [\"rows\", \"4\", \"placeholder\", \"Entrez une description pour cette \\u00E9quipe\", 1, \"w-full\", \"px-4\", \"py-3\", \"bg-[#f0f4f8]\", \"dark:bg-[#0a0a0a]\", \"border\", \"border-[#dac4ea]/20\", \"dark:border-[#00f7ff]/20\", \"rounded-lg\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"placeholder-[#6d6870]/50\", \"dark:placeholder-[#a0a0a0]/50\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-[#dac4ea]\", \"dark:focus:ring-[#00f7ff]\", \"focus:border-transparent\", \"transition-all\", \"resize-none\", 3, \"value\", \"input\"], [\"descInput\", \"\"], [1, \"mt-1\", \"text-xs\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [1, \"flex\", \"items-center\", \"space-x-4\", \"pt-4\"], [\"type\", \"button\", 1, \"relative\", \"overflow-hidden\", \"group\", \"bg-gradient-to-r\", \"from-[#dac4ea]\", \"to-[#8b5a9f]\", \"dark:from-[#00f7ff]\", \"dark:to-[#dac4ea]\", \"text-white\", \"px-6\", \"py-3\", \"rounded-xl\", \"font-medium\", \"transition-all\", \"duration-300\", \"hover:scale-105\", \"shadow-lg\", \"hover:shadow-[0_0_25px_rgba(218,196,234,0.4)]\", \"dark:hover:shadow-[0_0_25px_rgba(0,247,255,0.4)]\", \"disabled:opacity-50\", \"disabled:cursor-not-allowed\", \"disabled:hover:scale-100\", 3, \"disabled\", \"click\"], [\"class\", \"inline-block w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2\", 4, \"ngIf\"], [\"class\", \"fas fa-save mr-2\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"bg-[#6d6870]/20 dark:bg-[#a0a0a0]/20 text-[#6d6870] dark:text-[#a0a0a0] px-6 py-3 rounded-xl font-medium transition-all duration-300 hover:scale-105 hover:bg-[#6d6870]/30 dark:hover:bg-[#a0a0a0]/30\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4\", 3, \"click\", 4, \"ngIf\"], [1, \"mb-6\"], [1, \"bg-[#ff6b69]/10\", \"dark:bg-[#ff3b30]/10\", \"border-l-4\", \"border-[#ff6b69]\", \"dark:border-[#ff3b30]\", \"rounded-lg\", \"p-4\", \"backdrop-blur-sm\"], [1, \"flex\", \"items-start\", \"justify-between\"], [1, \"flex\", \"items-start\"], [1, \"text-[#ff6b69]\", \"dark:text-[#ff3b30]\", \"mr-3\", \"text-xl\"], [1, \"fas\", \"fa-exclamation-triangle\"], [1, \"flex-1\"], [1, \"font-semibold\", \"text-[#ff6b69]\", \"dark:text-[#ff3b30]\", \"mb-1\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [1, \"text-[#ff6b69]\", \"dark:text-[#ff3b30]\", \"hover:bg-[#ff6b69]/10\", \"dark:hover:bg-[#ff3b30]/10\", \"p-1\", \"rounded\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"flex\", \"flex-col\", \"items-center\", \"justify-center\", \"py-16\"], [1, \"relative\"], [1, \"w-12\", \"h-12\", \"border-3\", \"border-[#dac4ea]/20\", \"dark:border-[#00f7ff]/20\", \"border-t-[#dac4ea]\", \"dark:border-t-[#00f7ff]\", \"rounded-full\", \"animate-spin\"], [1, \"absolute\", \"inset-0\", \"bg-[#dac4ea]/20\", \"dark:bg-[#00f7ff]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"mt-4\", \"text-[#dac4ea]\", \"dark:text-[#00f7ff]\", \"text-sm\", \"font-medium\", \"tracking-wide\"], [1, \"mb-8\"], [1, \"mb-6\", \"relative\"], [1, \"bg-white\", \"dark:bg-[#1a1a1a]\", \"rounded-xl\", \"shadow-lg\", \"dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)]\", \"p-6\", \"backdrop-blur-sm\", \"border\", \"border-[#dac4ea]/20\", \"dark:border-[#00f7ff]/20\"], [1, \"flex\", \"flex-col\", \"lg:flex-row\", \"lg:items-center\", \"lg:justify-between\"], [1, \"mb-4\", \"lg:mb-0\"], [1, \"text-2xl\", \"font-bold\", \"text-[#dac4ea]\", \"dark:text-[#00f7ff]\", \"mb-2\", \"tracking-wide\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"text-sm\"], [1, \"bg-[#dac4ea]/20\", \"dark:bg-[#00f7ff]/20\", \"text-[#dac4ea]\", \"dark:text-[#00f7ff]\", \"px-4\", \"py-2\", \"rounded-lg\", \"font-medium\", \"transition-all\", \"duration-300\", \"hover:scale-105\", \"hover:bg-[#dac4ea]/30\", \"dark:hover:bg-[#00f7ff]/30\", 3, \"click\"], [1, \"fas\", \"fa-sync-alt\", \"mr-2\"], [\"class\", \"text-center py-12\", 4, \"ngIf\"], [\"class\", \"bg-white dark:bg-[#1a1a1a] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] overflow-hidden border border-[#dac4ea]/20 dark:border-[#00f7ff]/20\", 4, \"ngIf\"], [1, \"text-center\", \"py-12\"], [1, \"w-16\", \"h-16\", \"mx-auto\", \"mb-4\", \"text-[#dac4ea]\", \"dark:text-[#00f7ff]\", \"opacity-70\"], [1, \"fas\", \"fa-users\", \"text-4xl\"], [1, \"text-lg\", \"font-semibold\", \"text-[#dac4ea]\", \"dark:text-[#00f7ff]\", \"mb-2\"], [1, \"overflow-x-auto\"], [1, \"w-full\"], [1, \"bg-[#dac4ea]/10\", \"dark:bg-[#00f7ff]/10\"], [1, \"px-6\", \"py-4\", \"text-left\", \"text-sm\", \"font-semibold\", \"text-[#dac4ea]\", \"dark:text-[#00f7ff]\"], [1, \"px-6\", \"py-4\", \"text-center\", \"text-sm\", \"font-semibold\", \"text-[#dac4ea]\", \"dark:text-[#00f7ff]\"], [1, \"divide-y\", \"divide-[#dac4ea]/10\", \"dark:divide-[#00f7ff]/10\"], [\"class\", \"hover:bg-[#dac4ea]/5 dark:hover:bg-[#00f7ff]/5 transition-colors\", 4, \"ngFor\", \"ngForOf\"], [1, \"hover:bg-[#dac4ea]/5\", \"dark:hover:bg-[#00f7ff]/5\", \"transition-colors\"], [1, \"px-6\", \"py-4\"], [1, \"font-medium\", \"text-[#dac4ea]\", \"dark:text-[#00f7ff]\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"max-w-xs\", \"truncate\"], [1, \"bg-[#dac4ea]/10\", \"dark:bg-[#00f7ff]/10\", \"text-[#dac4ea]\", \"dark:text-[#00f7ff]\", \"px-2\", \"py-1\", \"rounded-full\", \"text-xs\", \"font-medium\"], [1, \"flex\", \"items-center\", \"justify-center\", \"space-x-2\"], [\"title\", \"Modifier l'\\u00E9quipe\", 1, \"p-2\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:text-[#dac4ea]\", \"dark:hover:text-[#00f7ff]\", \"hover:bg-[#dac4ea]/10\", \"dark:hover:bg-[#00f7ff]/10\", \"rounded-lg\", \"transition-all\", 3, \"click\"], [1, \"fas\", \"fa-edit\"], [\"title\", \"G\\u00E9rer les membres\", 1, \"p-2\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:text-[#00ff9d]\", \"hover:bg-[#00ff9d]/10\", \"rounded-lg\", \"transition-all\", 3, \"click\"], [1, \"fas\", \"fa-users\"], [\"title\", \"Supprimer l'\\u00E9quipe\", 1, \"p-2\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:text-[#ff6b69]\", \"dark:hover:text-[#ff3b30]\", \"hover:bg-[#ff6b69]/10\", \"dark:hover:bg-[#ff3b30]/10\", \"rounded-lg\", \"transition-all\", 3, \"click\"], [1, \"fas\", \"fa-trash\"], [1, \"mt-1\", \"text-sm\", \"text-[#ff6b69]\", \"dark:text-[#ff3b30]\"], [1, \"inline-block\", \"w-4\", \"h-4\", \"border-2\", \"border-white/30\", \"border-t-white\", \"rounded-full\", \"animate-spin\", \"mr-2\"], [1, \"fas\", \"fa-save\", \"mr-2\"], [\"type\", \"button\", 1, \"bg-[#6d6870]/20\", \"dark:bg-[#a0a0a0]/20\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"px-6\", \"py-3\", \"rounded-xl\", \"font-medium\", \"transition-all\", \"duration-300\", \"hover:scale-105\", \"hover:bg-[#6d6870]/30\", \"dark:hover:bg-[#a0a0a0]/30\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"mr-2\"], [1, \"fixed\", \"inset-0\", \"bg-black/50\", \"backdrop-blur-sm\", \"z-50\", \"flex\", \"items-center\", \"justify-center\", \"p-4\", 3, \"click\"], [1, \"bg-white\", \"dark:bg-[#1a1a1a]\", \"rounded-xl\", \"shadow-2xl\", \"dark:shadow-[0_20px_50px_rgba(0,0,0,0.5)]\", \"max-w-2xl\", \"w-full\", \"max-h-[90vh]\", \"overflow-hidden\", \"border\", \"border-[#dac4ea]/20\", \"dark:border-[#00f7ff]/20\", 3, \"click\"], [1, \"flex\", \"items-center\", \"justify-between\"], [1, \"text-white/80\", \"hover:text-white\", \"hover:bg-white/10\", \"p-2\", \"rounded-lg\", \"transition-all\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"text-lg\"], [1, \"p-6\", \"max-h-[calc(90vh-140px)]\", \"overflow-y-auto\"], [1, \"text-lg\", \"font-semibold\", \"text-[#dac4ea]\", \"dark:text-[#00f7ff]\", \"mb-4\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-users\", \"mr-2\"], [4, \"ngIf\", \"ngIfElse\"], [\"noMembers\", \"\"], [1, \"fas\", \"fa-user-plus\", \"mr-2\"], [1, \"flex\", \"space-x-3\"], [\"type\", \"text\", \"placeholder\", \"ID du membre\", 1, \"flex-1\", \"px-4\", \"py-3\", \"bg-[#f0f4f8]\", \"dark:bg-[#0a0a0a]\", \"border\", \"border-[#dac4ea]/20\", \"dark:border-[#00f7ff]/20\", \"rounded-lg\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"placeholder-[#6d6870]/50\", \"dark:placeholder-[#a0a0a0]/50\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-[#dac4ea]\", \"dark:focus:ring-[#00f7ff]\", \"focus:border-transparent\", \"transition-all\"], [\"membreIdInput\", \"\"], [1, \"bg-gradient-to-r\", \"from-[#dac4ea]\", \"to-[#8b5a9f]\", \"dark:from-[#00f7ff]\", \"dark:to-[#dac4ea]\", \"text-white\", \"px-6\", \"py-3\", \"rounded-lg\", \"font-medium\", \"transition-all\", \"duration-300\", \"hover:scale-105\", \"shadow-lg\", \"hover:shadow-[0_0_25px_rgba(218,196,234,0.4)]\", \"dark:hover:shadow-[0_0_25px_rgba(0,247,255,0.4)]\", \"disabled:opacity-50\", \"disabled:cursor-not-allowed\", \"disabled:hover:scale-100\", 3, \"disabled\", \"click\"], [1, \"fas\", \"fa-plus\", \"mr-2\"], [1, \"mt-2\", \"text-xs\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [1, \"bg-[#dac4ea]/10\", \"dark:bg-[#00f7ff]/10\", \"border-l-4\", \"border-[#dac4ea]\", \"dark:border-[#00f7ff]\", \"rounded-lg\", \"p-4\"], [1, \"text-[#dac4ea]\", \"dark:text-[#00f7ff]\", \"mr-3\", \"text-lg\"], [1, \"fas\", \"fa-info-circle\"], [1, \"font-semibold\", \"text-[#dac4ea]\", \"dark:text-[#00f7ff]\", \"mb-1\"], [1, \"p-6\", \"border-t\", \"border-[#dac4ea]/10\", \"dark:border-[#00f7ff]/10\"], [1, \"flex\", \"justify-end\"], [1, \"bg-[#6d6870]/20\", \"dark:bg-[#a0a0a0]/20\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"px-6\", \"py-3\", \"rounded-xl\", \"font-medium\", \"transition-all\", \"duration-300\", \"hover:scale-105\", \"hover:bg-[#6d6870]/30\", \"dark:hover:bg-[#a0a0a0]/30\", 3, \"click\"], [1, \"space-y-2\"], [\"class\", \"flex items-center justify-between p-3 bg-[#f0f4f8] dark:bg-[#0a0a0a] rounded-lg border border-[#dac4ea]/10 dark:border-[#00f7ff]/10\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"items-center\", \"justify-between\", \"p-3\", \"bg-[#f0f4f8]\", \"dark:bg-[#0a0a0a]\", \"rounded-lg\", \"border\", \"border-[#dac4ea]/10\", \"dark:border-[#00f7ff]/10\"], [1, \"flex\", \"items-center\"], [1, \"w-8\", \"h-8\", \"bg-gradient-to-br\", \"from-[#dac4ea]\", \"to-[#8b5a9f]\", \"dark:from-[#00f7ff]\", \"dark:to-[#dac4ea]\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\", \"mr-3\"], [1, \"fas\", \"fa-user\", \"text-white\", \"text-sm\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"font-medium\"], [\"title\", \"Retirer ce membre\", 1, \"text-[#ff6b69]\", \"dark:text-[#ff3b30]\", \"hover:bg-[#ff6b69]/10\", \"dark:hover:bg-[#ff3b30]/10\", \"p-2\", \"rounded-lg\", \"transition-all\", 3, \"click\"], [1, \"fas\", \"fa-user-minus\"], [1, \"text-center\", \"py-8\"], [1, \"w-16\", \"h-16\", \"mx-auto\", \"mb-4\", \"text-[#dac4ea]\", \"dark:text-[#00f7ff]\", \"opacity-50\"], [1, \"fas\", \"fa-user-friends\", \"text-4xl\"]],\n      template: function EquipeComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r38 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵelement(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5);\n          i0.ɵɵelement(6, \"div\", 6)(7, \"div\", 6)(8, \"div\", 6)(9, \"div\", 6)(10, \"div\", 6)(11, \"div\", 6)(12, \"div\", 6)(13, \"div\", 6)(14, \"div\", 6)(15, \"div\", 6)(16, \"div\", 6);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(17, \"div\", 7);\n          i0.ɵɵtemplate(18, EquipeComponent_div_18_Template, 13, 1, \"div\", 8);\n          i0.ɵɵtemplate(19, EquipeComponent_div_19_Template, 6, 0, \"div\", 9);\n          i0.ɵɵtemplate(20, EquipeComponent_div_20_Template, 16, 2, \"div\", 10);\n          i0.ɵɵelementStart(21, \"div\", 11);\n          i0.ɵɵelement(22, \"div\", 12)(23, \"div\", 13);\n          i0.ɵɵelementStart(24, \"div\", 14)(25, \"div\", 15)(26, \"h3\", 16);\n          i0.ɵɵtext(27);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"p\", 17);\n          i0.ɵɵtext(29);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(30, \"div\", 18)(31, \"form\", 19)(32, \"div\")(33, \"label\", 20);\n          i0.ɵɵtext(34, \" Nom de l'\\u00E9quipe \");\n          i0.ɵɵelementStart(35, \"span\", 21);\n          i0.ɵɵtext(36, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(37, \"input\", 22, 23);\n          i0.ɵɵlistener(\"input\", function EquipeComponent_Template_input_input_37_listener() {\n            i0.ɵɵrestoreView(_r38);\n            const _r3 = i0.ɵɵreference(38);\n            return i0.ɵɵresetView(ctx.newEquipe.name = _r3.value);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(39, EquipeComponent_div_39_Template, 2, 0, \"div\", 24);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"div\")(41, \"label\", 20);\n          i0.ɵɵtext(42, \" Description \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"textarea\", 25, 26);\n          i0.ɵɵlistener(\"input\", function EquipeComponent_Template_textarea_input_43_listener() {\n            i0.ɵɵrestoreView(_r38);\n            const _r5 = i0.ɵɵreference(44);\n            return i0.ɵɵresetView(ctx.newEquipe.description = _r5.value);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"p\", 27);\n          i0.ɵɵtext(46, \" Une br\\u00E8ve description de l'\\u00E9quipe et de son objectif \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(47, \"div\", 28)(48, \"button\", 29);\n          i0.ɵɵlistener(\"click\", function EquipeComponent_Template_button_click_48_listener() {\n            return ctx.isEditing ? ctx.updateSelectedEquipe() : ctx.addEquipe();\n          });\n          i0.ɵɵtemplate(49, EquipeComponent_span_49_Template, 1, 0, \"span\", 30);\n          i0.ɵɵtemplate(50, EquipeComponent_i_50_Template, 1, 0, \"i\", 31);\n          i0.ɵɵtext(51);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(52, EquipeComponent_button_52_Template, 3, 0, \"button\", 32);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵtemplate(53, EquipeComponent_div_53_Template, 45, 4, \"div\", 33);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(18);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate1(\" \", ctx.isEditing ? \"Modifier une \\u00E9quipe\" : \"Cr\\u00E9er une \\u00E9quipe\", \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.isEditing ? \"Modifiez les informations de l'\\u00E9quipe\" : \"Ajoutez une nouvelle \\u00E9quipe \\u00E0 votre organisation\", \" \");\n          i0.ɵɵadvance(8);\n          i0.ɵɵclassProp(\"border-[#ff6b69]\", !ctx.newEquipe.name && (ctx.isEditing || ctx.newEquipe.name === \"\"))(\"dark:border-[#ff3b30]\", !ctx.newEquipe.name && (ctx.isEditing || ctx.newEquipe.name === \"\"));\n          i0.ɵɵproperty(\"value\", ctx.newEquipe.name);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.newEquipe.name && (ctx.isEditing || ctx.newEquipe.name === \"\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"value\", ctx.newEquipe.description || \"\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"disabled\", !ctx.newEquipe.name || ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \", ctx.isEditing ? \"Mettre \\u00E0 jour\" : \"Cr\\u00E9er\", \" \");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isEditing);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedEquipe);\n        }\n      },\n      dependencies: [i3.NgForOf, i3.NgIf, i4.ɵNgNoValidate, i4.NgControlStatusGroup, i4.NgForm],\n      styles: [\"\\n\\n.container[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n}\\n\\n.card[_ngcontent-%COMP%] {\\n  border: none;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n  border-radius: 8px;\\n}\\n\\n.card-header[_ngcontent-%COMP%] {\\n  border-radius: 8px 8px 0 0 !important;\\n}\\n\\n.table[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n}\\n\\n.table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  border-top: none;\\n  font-weight: 600;\\n  color: #495057;\\n}\\n\\n.btn-sm[_ngcontent-%COMP%] {\\n  padding: 0.25rem 0.5rem;\\n  font-size: 0.875rem;\\n}\\n\\n.spinner-border-sm[_ngcontent-%COMP%] {\\n  width: 1rem;\\n  height: 1rem;\\n}\\n\\n.alert[_ngcontent-%COMP%] {\\n  border: none;\\n  border-radius: 6px;\\n}\\n\\n.modal-content[_ngcontent-%COMP%] {\\n  border: none;\\n  border-radius: 8px;\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);\\n}\\n\\n.list-group-item[_ngcontent-%COMP%] {\\n  border: 1px solid #e9ecef;\\n  border-radius: 4px;\\n  margin-bottom: 0.5rem;\\n}\\n\\n.list-group-item[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n\\n.form-control[_ngcontent-%COMP%]:focus {\\n  border-color: #80bdff;\\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\\n}\\n\\n.btn[_ngcontent-%COMP%]:focus {\\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\\n}\\n\\n.text-muted[_ngcontent-%COMP%] {\\n  color: #6c757d !important;\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "EquipeComponent_div_18_Template_button_click_11_listener", "ɵɵrestoreView", "_r11", "ctx_r10", "ɵɵnextContext", "ɵɵresetView", "error", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "EquipeComponent_div_20_div_15_tr_16_Template_button_click_15_listener", "restoredCtx", "_r17", "equipe_r15", "$implicit", "ctx_r16", "editEquipe", "EquipeComponent_div_20_div_15_tr_16_Template_button_click_17_listener", "ctx_r18", "showMembreModal", "EquipeComponent_div_20_div_15_tr_16_Template_button_click_19_listener", "ctx_r19", "_id", "deleteEquipe", "name", "description", "admin", "members", "length", "ɵɵtemplate", "EquipeComponent_div_20_div_15_tr_16_Template", "ɵɵproperty", "ctx_r13", "equipes", "EquipeComponent_div_20_Template_button_click_11_listener", "_r21", "ctx_r20", "loadEquipes", "EquipeComponent_div_20_div_14_Template", "EquipeComponent_div_20_div_15_Template", "ctx_r2", "EquipeComponent_button_52_Template_button_click_0_listener", "_r23", "ctx_r22", "cancelEdit", "EquipeComponent_div_53_div_16_div_2_Template_button_click_6_listener", "_r31", "membreId_r29", "ctx_r30", "removeMembreFromEquipe", "selectedEquipe", "ɵɵtextInterpolate", "EquipeComponent_div_53_div_16_div_2_Template", "ctx_r24", "EquipeComponent_div_53_Template_div_click_0_listener", "_r33", "ctx_r32", "EquipeComponent_div_53_Template_div_click_1_listener", "$event", "stopPropagation", "EquipeComponent_div_53_Template_button_click_9_listener", "ctx_r35", "EquipeComponent_div_53_div_16_Template", "EquipeComponent_div_53_ng_template_17_Template", "ɵɵtemplateRefExtractor", "EquipeComponent_div_53_Template_button_click_26_listener", "_r27", "ɵɵreference", "ctx_r36", "addMembreToEquipe", "value", "EquipeComponent_div_53_Template_button_click_42_listener", "ctx_r37", "ctx_r9", "_r25", "EquipeComponent", "constructor", "equipeService", "membreService", "newEquipe", "isEditing", "membres", "loading", "ngOnInit", "loadMembres", "getEquipes", "subscribe", "next", "data", "console", "log", "message", "getMembres", "addEquipe", "response", "successMessage", "alert", "equipe", "updateSelectedEquipe", "updateEquipe", "updatedEquipe", "id", "confirm", "modalRef", "document", "getElementById", "window", "bootstrap", "modal", "Modal", "show", "teamId", "membreId", "trim", "membre", "find", "e", "ɵɵdirectiveInject", "i1", "EquipeService", "i2", "MembreService", "selectors", "decls", "vars", "consts", "template", "EquipeComponent_Template", "rf", "ctx", "EquipeComponent_div_18_Template", "EquipeComponent_div_19_Template", "EquipeComponent_div_20_Template", "EquipeComponent_Template_input_input_37_listener", "_r38", "_r3", "EquipeComponent_div_39_Template", "EquipeComponent_Template_textarea_input_43_listener", "_r5", "EquipeComponent_Template_button_click_48_listener", "EquipeComponent_span_49_Template", "EquipeComponent_i_50_Template", "EquipeComponent_button_52_Template", "EquipeComponent_div_53_Template", "ɵɵclassProp"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\admin\\equipes\\equipe\\equipe.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\admin\\equipes\\equipe\\equipe.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { EquipeService } from 'src/app/services/equipe.service';\nimport { MembreService } from 'src/app/services/membre.service';\nimport { Equipe } from 'src/app/models/equipe.model';\nimport { Membre } from 'src/app/models/membre.model';\nimport { forkJoin } from 'rxjs';\n\n// Add Bootstrap type declaration\ndeclare global {\n  interface Window {\n    bootstrap: any;\n  }\n}\n\n@Component({\n  selector: 'app-equipe',\n  templateUrl: './equipe.component.html',\n  styleUrls: ['./equipe.component.css'],\n})\nexport class EquipeComponent implements OnInit {\n  equipes: Equipe[] = [];\n  newEquipe: Equipe = { name: '', description: '' };\n  selectedEquipe: Equipe | null = null;\n  isEditing = false;\n  membres: Membre[] = [];\n  loading = false;\n  error = '';\n\n  constructor(\n    private equipeService: EquipeService,\n    private membreService: MembreService\n  ) {}\n\n  ngOnInit(): void {\n    this.loadEquipes();\n    this.loadMembres();\n  }\n\n  loadEquipes() {\n    this.loading = true;\n    this.equipeService.getEquipes().subscribe({\n      next: (data) => {\n        console.log('Loaded equipes:', data);\n        this.equipes = data;\n        this.loading = false;\n      },\n      error: (error) => {\n        console.error('Error loading equipes:', error);\n        this.error = 'Erreur lors du chargement des équipes: ' + error.message;\n        this.loading = false;\n      }\n    });\n  }\n\n  loadMembres() {\n    this.loading = true;\n    this.membreService.getMembres().subscribe({\n      next: (data) => {\n        console.log('Loaded membres:', data);\n        this.membres = data;\n        this.loading = false;\n      },\n      error: (error) => {\n        console.error('Error loading membres:', error);\n        this.error = 'Erreur lors du chargement des membres: ' + error.message;\n        this.loading = false;\n      }\n    });\n  }\n\n  addEquipe() {\n    console.log('Adding equipe:', this.newEquipe);\n\n    if (!this.newEquipe.name) {\n      console.error('Team name is required');\n      this.error = 'Le nom de l\\'équipe est requis';\n      return;\n    }\n\n    this.loading = true;\n    this.error = '';\n\n    this.equipeService.addEquipe(this.newEquipe).subscribe({\n      next: (response) => {\n        console.log('Equipe added successfully:', response);\n        this.loadEquipes();\n        this.newEquipe = { name: '', description: '' }; // Clear input\n        this.loading = false;\n\n        // Afficher un message de succès temporaire\n        const successMessage = 'Équipe créée avec succès!';\n        this.error = ''; // Effacer les erreurs précédentes\n        alert(successMessage);\n      },\n      error: (error) => {\n        console.error('Error adding equipe:', error);\n        this.error = 'Erreur lors de la création de l\\'équipe: ' + (error.error?.message || error.message || 'Unknown error');\n        this.loading = false;\n      }\n    });\n  }\n\n  editEquipe(equipe: Equipe) {\n    this.isEditing = true;\n    // Créer une copie profonde pour éviter de modifier l'objet original\n    this.newEquipe = {\n      _id: equipe._id,\n      name: equipe.name || '',\n      description: equipe.description || '',\n      admin: equipe.admin,\n      members: equipe.members ? [...equipe.members] : []\n    };\n  }\n\n  cancelEdit() {\n    this.isEditing = false;\n    this.newEquipe = { name: '', description: '' };\n    this.error = ''; // Effacer les erreurs\n  }\n\n  updateSelectedEquipe() {\n    if (!this.newEquipe.name) {\n      console.error('Team name is required');\n      this.error = 'Le nom de l\\'équipe est requis';\n      return;\n    }\n\n    if (this.newEquipe._id) {\n      this.loading = true;\n      this.error = '';\n\n      this.equipeService.updateEquipe(this.newEquipe._id, this.newEquipe).subscribe({\n        next: (updatedEquipe) => {\n          console.log('Team updated successfully:', updatedEquipe);\n          this.loadEquipes();\n          this.isEditing = false;\n          this.newEquipe = { name: '', description: '' };\n          this.loading = false;\n\n          // Afficher un message de succès temporaire\n          const successMessage = 'Équipe mise à jour avec succès!';\n          alert(successMessage);\n        },\n        error: (error) => {\n          console.error('Error updating team:', error);\n          this.error = 'Erreur lors de la mise à jour de l\\'équipe: ' + (error.error?.message || error.message || 'Unknown error');\n          this.loading = false;\n        }\n      });\n    } else {\n      this.error = 'ID de l\\'équipe manquant pour la mise à jour';\n    }\n  }\n\n  deleteEquipe(id: string) {\n    if (!id) {\n      console.error('ID is undefined');\n      this.error = 'ID de l\\'équipe non défini';\n      return;\n    }\n\n    if (confirm('Êtes-vous sûr de vouloir supprimer cette équipe? Cette action est irréversible.')) {\n      this.loading = true;\n      this.error = '';\n\n      this.equipeService.deleteEquipe(id).subscribe({\n        next: (response) => {\n          console.log('Team deleted successfully:', response);\n\n          // Si l'équipe en cours d'édition est celle qui vient d'être supprimée, réinitialiser le formulaire\n          if (this.isEditing && this.newEquipe._id === id) {\n            this.isEditing = false;\n            this.newEquipe = { name: '', description: '' };\n          }\n\n          this.loadEquipes();\n          this.loading = false;\n\n          // Afficher un message de succès\n          alert('Équipe supprimée avec succès');\n        },\n        error: (error) => {\n          console.error('Error deleting team:', error);\n          this.error = 'Erreur lors de la suppression de l\\'équipe: ' + (error.error?.message || error.message || 'Unknown error');\n          this.loading = false;\n        }\n      });\n    }\n  }\n\n  showMembreModal(equipe: Equipe) {\n    this.selectedEquipe = equipe;\n    // Ouvrir le modal avec Bootstrap 5\n    const modalRef = document.getElementById('membreModal');\n    if (modalRef) {\n      try {\n        // Ensure Bootstrap is properly loaded\n        if (typeof window !== 'undefined' && window.bootstrap) {\n          const modal = new window.bootstrap.Modal(modalRef);\n          modal.show();\n        } else {\n          console.error('Bootstrap is not loaded properly');\n          alert('Erreur: Bootstrap n\\'est pas chargé correctement');\n        }\n      } catch (error) {\n        console.error('Error showing modal:', error);\n      }\n    } else {\n      console.error('Modal element not found');\n    }\n  }\n\n  addMembreToEquipe(teamId: string | undefined, membreId: string) {\n    if (!teamId) {\n      console.error('Team ID is undefined');\n      alert('ID de l\\'équipe non défini');\n      return;\n    }\n\n    if (!membreId || membreId.trim() === '') {\n      console.error('Member ID is empty');\n      alert('L\\'ID du membre est requis');\n      return;\n    }\n\n    this.loading = true;\n\n    // Create a proper Membre object that matches what the API expects\n    const membre: Membre = { id: membreId };\n\n    this.equipeService.addMembreToEquipe(teamId, membre).subscribe({\n      next: (response) => {\n        console.log('Member added successfully:', response);\n        this.loadEquipes();\n        this.loading = false;\n\n        // Afficher un message de succès\n        alert('Membre ajouté avec succès à l\\'équipe');\n      },\n      error: (error) => {\n        console.error('Error adding member:', error);\n        this.error = 'Erreur lors de l\\'ajout du membre: ' + (error.error?.message || error.message || 'Unknown error');\n        alert(this.error);\n        this.loading = false;\n      }\n    });\n  }\n\n  removeMembreFromEquipe(teamId: string | undefined, membreId: string) {\n    if (!teamId) {\n      console.error('Team ID is undefined');\n      alert('ID de l\\'équipe non défini');\n      return;\n    }\n\n    if (!membreId) {\n      console.error('Member ID is undefined');\n      alert('ID du membre non défini');\n      return;\n    }\n\n    if (confirm('Êtes-vous sûr de vouloir retirer ce membre de l\\'équipe?')) {\n      this.loading = true;\n\n      this.equipeService.removeMembreFromEquipe(teamId, membreId).subscribe({\n        next: (response) => {\n          console.log('Member removed successfully:', response);\n          this.loadEquipes();\n          this.loading = false;\n\n          // Si l'équipe sélectionnée est celle dont on vient de retirer un membre, mettre à jour l'équipe sélectionnée\n          if (this.selectedEquipe && this.selectedEquipe._id === teamId) {\n            const updatedEquipe = this.equipes.find(e => e._id === teamId);\n            if (updatedEquipe) {\n              this.selectedEquipe = updatedEquipe;\n            }\n          }\n        },\n        error: (error) => {\n          console.error('Error removing member:', error);\n          this.error = 'Erreur lors de la suppression du membre: ' + (error.error?.message || error.message || 'Unknown error');\n          alert(this.error);\n          this.loading = false;\n        }\n      });\n    }\n  }\n}\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n", "<div\n  class=\"min-h-screen bg-[#f0f4f8] dark:bg-[#0a0a0a] relative overflow-hidden\"\n>\n  <!-- Background decorative elements -->\n  <div class=\"absolute inset-0 overflow-hidden pointer-events-none\">\n    <div\n      class=\"absolute top-[15%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-br from-[#dac4ea]/5 to-transparent dark:from-[#00f7ff]/3 dark:to-transparent blur-3xl\"\n    ></div>\n    <div\n      class=\"absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#dac4ea]/5 to-transparent dark:from-[#00f7ff]/3 dark:to-transparent blur-3xl\"\n    ></div>\n\n    <!-- Grid pattern -->\n    <div class=\"absolute inset-0 opacity-5 dark:opacity-[0.03]\">\n      <div class=\"h-full grid grid-cols-12\">\n        <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n      </div>\n    </div>\n  </div>\n\n  <div class=\"max-w-7xl mx-auto p-6 relative z-10\">\n    <!-- Error Alert -->\n    <div *ngIf=\"error\" class=\"mb-6\">\n      <div\n        class=\"bg-[#ff6b69]/10 dark:bg-[#ff3b30]/10 border-l-4 border-[#ff6b69] dark:border-[#ff3b30] rounded-lg p-4 backdrop-blur-sm\"\n      >\n        <div class=\"flex items-start justify-between\">\n          <div class=\"flex items-start\">\n            <div class=\"text-[#ff6b69] dark:text-[#ff3b30] mr-3 text-xl\">\n              <i class=\"fas fa-exclamation-triangle\"></i>\n            </div>\n            <div class=\"flex-1\">\n              <h3 class=\"font-semibold text-[#ff6b69] dark:text-[#ff3b30] mb-1\">\n                Erreur\n              </h3>\n              <p class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0]\">\n                {{ error }}\n              </p>\n            </div>\n          </div>\n          <button\n            (click)=\"error = ''\"\n            class=\"text-[#ff6b69] dark:text-[#ff3b30] hover:bg-[#ff6b69]/10 dark:hover:bg-[#ff3b30]/10 p-1 rounded transition-colors\"\n          >\n            <i class=\"fas fa-times\"></i>\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Loading Indicator -->\n    <div\n      *ngIf=\"loading\"\n      class=\"flex flex-col items-center justify-center py-16\"\n    >\n      <div class=\"relative\">\n        <div\n          class=\"w-12 h-12 border-3 border-[#dac4ea]/20 dark:border-[#00f7ff]/20 border-t-[#dac4ea] dark:border-t-[#00f7ff] rounded-full animate-spin\"\n        ></div>\n        <div\n          class=\"absolute inset-0 bg-[#dac4ea]/20 dark:bg-[#00f7ff]/20 blur-xl rounded-full transform scale-150 -z-10\"\n        ></div>\n      </div>\n      <p\n        class=\"mt-4 text-[#dac4ea] dark:text-[#00f7ff] text-sm font-medium tracking-wide\"\n      >\n        Chargement...\n      </p>\n    </div>\n\n    <!-- Liste des équipes -->\n    <div *ngIf=\"!loading\" class=\"mb-8\">\n      <!-- Header avec titre et bouton refresh -->\n      <div class=\"mb-6 relative\">\n        <!-- Decorative top border -->\n        <div\n          class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea]\"\n        ></div>\n        <div\n          class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea] blur-md\"\n        ></div>\n\n        <div\n          class=\"bg-white dark:bg-[#1a1a1a] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] p-6 backdrop-blur-sm border border-[#dac4ea]/20 dark:border-[#00f7ff]/20\"\n        >\n          <div\n            class=\"flex flex-col lg:flex-row lg:items-center lg:justify-between\"\n          >\n            <div class=\"mb-4 lg:mb-0\">\n              <h2\n                class=\"text-2xl font-bold text-[#dac4ea] dark:text-[#00f7ff] mb-2 tracking-wide\"\n              >\n                Liste des équipes\n              </h2>\n              <p class=\"text-[#6d6870] dark:text-[#a0a0a0] text-sm\">\n                Gérez toutes vos équipes depuis cette interface\n              </p>\n            </div>\n\n            <button\n              (click)=\"loadEquipes()\"\n              class=\"bg-[#dac4ea]/20 dark:bg-[#00f7ff]/20 text-[#dac4ea] dark:text-[#00f7ff] px-4 py-2 rounded-lg font-medium transition-all duration-300 hover:scale-105 hover:bg-[#dac4ea]/30 dark:hover:bg-[#00f7ff]/30\"\n            >\n              <i class=\"fas fa-sync-alt mr-2\"></i>\n              Rafraîchir\n            </button>\n          </div>\n        </div>\n      </div>\n\n      <!-- No teams message -->\n      <div *ngIf=\"equipes.length === 0\" class=\"text-center py-12\">\n        <div\n          class=\"w-16 h-16 mx-auto mb-4 text-[#dac4ea] dark:text-[#00f7ff] opacity-70\"\n        >\n          <i class=\"fas fa-users text-4xl\"></i>\n        </div>\n        <h3\n          class=\"text-lg font-semibold text-[#dac4ea] dark:text-[#00f7ff] mb-2\"\n        >\n          Aucune équipe trouvée\n        </h3>\n        <p class=\"text-[#6d6870] dark:text-[#a0a0a0] text-sm\">\n          Créez votre première équipe ci-dessous\n        </p>\n      </div>\n\n      <!-- Teams Table -->\n      <div\n        *ngIf=\"equipes.length > 0\"\n        class=\"bg-white dark:bg-[#1a1a1a] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] overflow-hidden border border-[#dac4ea]/20 dark:border-[#00f7ff]/20\"\n      >\n        <div class=\"overflow-x-auto\">\n          <table class=\"w-full\">\n            <thead class=\"bg-[#dac4ea]/10 dark:bg-[#00f7ff]/10\">\n              <tr>\n                <th\n                  class=\"px-6 py-4 text-left text-sm font-semibold text-[#dac4ea] dark:text-[#00f7ff]\"\n                >\n                  Nom\n                </th>\n                <th\n                  class=\"px-6 py-4 text-left text-sm font-semibold text-[#dac4ea] dark:text-[#00f7ff]\"\n                >\n                  Description\n                </th>\n                <th\n                  class=\"px-6 py-4 text-left text-sm font-semibold text-[#dac4ea] dark:text-[#00f7ff]\"\n                >\n                  Admin\n                </th>\n                <th\n                  class=\"px-6 py-4 text-left text-sm font-semibold text-[#dac4ea] dark:text-[#00f7ff]\"\n                >\n                  Membres\n                </th>\n                <th\n                  class=\"px-6 py-4 text-center text-sm font-semibold text-[#dac4ea] dark:text-[#00f7ff]\"\n                >\n                  Actions\n                </th>\n              </tr>\n            </thead>\n            <tbody\n              class=\"divide-y divide-[#dac4ea]/10 dark:divide-[#00f7ff]/10\"\n            >\n              <tr\n                *ngFor=\"let equipe of equipes\"\n                class=\"hover:bg-[#dac4ea]/5 dark:hover:bg-[#00f7ff]/5 transition-colors\"\n              >\n                <td class=\"px-6 py-4\">\n                  <div class=\"font-medium text-[#dac4ea] dark:text-[#00f7ff]\">\n                    {{ equipe.name }}\n                  </div>\n                </td>\n                <td class=\"px-6 py-4\">\n                  <div\n                    class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0] max-w-xs truncate\"\n                  >\n                    {{ equipe.description || \"Aucune description\" }}\n                  </div>\n                </td>\n                <td class=\"px-6 py-4\">\n                  <div class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0]\">\n                    {{ equipe.admin || \"Non défini\" }}\n                  </div>\n                </td>\n                <td class=\"px-6 py-4\">\n                  <span\n                    class=\"bg-[#dac4ea]/10 dark:bg-[#00f7ff]/10 text-[#dac4ea] dark:text-[#00f7ff] px-2 py-1 rounded-full text-xs font-medium\"\n                  >\n                    {{ equipe.members?.length || 0 }} membre(s)\n                  </span>\n                </td>\n                <td class=\"px-6 py-4\">\n                  <div class=\"flex items-center justify-center space-x-2\">\n                    <button\n                      (click)=\"editEquipe(equipe)\"\n                      class=\"p-2 text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#dac4ea] dark:hover:text-[#00f7ff] hover:bg-[#dac4ea]/10 dark:hover:bg-[#00f7ff]/10 rounded-lg transition-all\"\n                      title=\"Modifier l'équipe\"\n                    >\n                      <i class=\"fas fa-edit\"></i>\n                    </button>\n                    <button\n                      (click)=\"showMembreModal(equipe)\"\n                      class=\"p-2 text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#00ff9d] hover:bg-[#00ff9d]/10 rounded-lg transition-all\"\n                      title=\"Gérer les membres\"\n                    >\n                      <i class=\"fas fa-users\"></i>\n                    </button>\n                    <button\n                      (click)=\"equipe._id && deleteEquipe(equipe._id)\"\n                      class=\"p-2 text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#ff6b69] dark:hover:text-[#ff3b30] hover:bg-[#ff6b69]/10 dark:hover:bg-[#ff3b30]/10 rounded-lg transition-all\"\n                      title=\"Supprimer l'équipe\"\n                    >\n                      <i class=\"fas fa-trash\"></i>\n                    </button>\n                  </div>\n                </td>\n              </tr>\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </div>\n\n    <!-- Formulaire de création d'équipe -->\n    <div class=\"mb-8 relative\">\n      <!-- Decorative top border -->\n      <div\n        class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea]\"\n      ></div>\n      <div\n        class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea] blur-md\"\n      ></div>\n\n      <div\n        class=\"bg-white dark:bg-[#1a1a1a] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] overflow-hidden border border-[#dac4ea]/20 dark:border-[#00f7ff]/20\"\n      >\n        <!-- Header -->\n        <div\n          class=\"bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea] p-6\"\n        >\n          <h3 class=\"text-xl font-bold text-white mb-1\">\n            {{ isEditing ? \"Modifier une équipe\" : \"Créer une équipe\" }}\n          </h3>\n          <p class=\"text-white/80 text-sm\">\n            {{\n              isEditing\n                ? \"Modifiez les informations de l'équipe\"\n                : \"Ajoutez une nouvelle équipe à votre organisation\"\n            }}\n          </p>\n        </div>\n\n        <!-- Form -->\n        <div class=\"p-6\">\n          <form class=\"space-y-6\">\n            <!-- Nom de l'équipe -->\n            <div>\n              <label\n                class=\"block text-sm font-medium text-[#dac4ea] dark:text-[#00f7ff] mb-2\"\n              >\n                Nom de l'équipe\n                <span class=\"text-[#ff6b69] dark:text-[#ff3b30]\">*</span>\n              </label>\n              <input\n                #nameInput\n                type=\"text\"\n                [value]=\"newEquipe.name\"\n                (input)=\"newEquipe.name = nameInput.value\"\n                class=\"w-full px-4 py-3 bg-[#f0f4f8] dark:bg-[#0a0a0a] border border-[#dac4ea]/20 dark:border-[#00f7ff]/20 rounded-lg text-[#6d6870] dark:text-[#a0a0a0] placeholder-[#6d6870]/50 dark:placeholder-[#a0a0a0]/50 focus:outline-none focus:ring-2 focus:ring-[#dac4ea] dark:focus:ring-[#00f7ff] focus:border-transparent transition-all\"\n                [class.border-[#ff6b69]]=\"\n                  !newEquipe.name && (isEditing || newEquipe.name === '')\n                \"\n                [class.dark:border-[#ff3b30]]=\"\n                  !newEquipe.name && (isEditing || newEquipe.name === '')\n                \"\n                placeholder=\"Entrez le nom de l'équipe\"\n                required\n              />\n              <div\n                *ngIf=\"!newEquipe.name && (isEditing || newEquipe.name === '')\"\n                class=\"mt-1 text-sm text-[#ff6b69] dark:text-[#ff3b30]\"\n              >\n                Le nom de l'équipe est requis\n              </div>\n            </div>\n\n            <!-- Description -->\n            <div>\n              <label\n                class=\"block text-sm font-medium text-[#dac4ea] dark:text-[#00f7ff] mb-2\"\n              >\n                Description\n              </label>\n              <textarea\n                #descInput\n                [value]=\"newEquipe.description || ''\"\n                (input)=\"newEquipe.description = descInput.value\"\n                rows=\"4\"\n                class=\"w-full px-4 py-3 bg-[#f0f4f8] dark:bg-[#0a0a0a] border border-[#dac4ea]/20 dark:border-[#00f7ff]/20 rounded-lg text-[#6d6870] dark:text-[#a0a0a0] placeholder-[#6d6870]/50 dark:placeholder-[#a0a0a0]/50 focus:outline-none focus:ring-2 focus:ring-[#dac4ea] dark:focus:ring-[#00f7ff] focus:border-transparent transition-all resize-none\"\n                placeholder=\"Entrez une description pour cette équipe\"\n              ></textarea>\n              <p class=\"mt-1 text-xs text-[#6d6870] dark:text-[#a0a0a0]\">\n                Une brève description de l'équipe et de son objectif\n              </p>\n            </div>\n\n            <!-- Buttons -->\n            <div class=\"flex items-center space-x-4 pt-4\">\n              <button\n                type=\"button\"\n                [disabled]=\"!newEquipe.name || loading\"\n                (click)=\"isEditing ? updateSelectedEquipe() : addEquipe()\"\n                class=\"relative overflow-hidden group bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea] text-white px-6 py-3 rounded-xl font-medium transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-[0_0_25px_rgba(218,196,234,0.4)] dark:hover:shadow-[0_0_25px_rgba(0,247,255,0.4)] disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100\"\n              >\n                <span\n                  *ngIf=\"loading\"\n                  class=\"inline-block w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2\"\n                ></span>\n                <i *ngIf=\"!loading\" class=\"fas fa-save mr-2\"></i>\n                {{ isEditing ? \"Mettre à jour\" : \"Créer\" }}\n              </button>\n\n              <button\n                *ngIf=\"isEditing\"\n                type=\"button\"\n                (click)=\"cancelEdit()\"\n                class=\"bg-[#6d6870]/20 dark:bg-[#a0a0a0]/20 text-[#6d6870] dark:text-[#a0a0a0] px-6 py-3 rounded-xl font-medium transition-all duration-300 hover:scale-105 hover:bg-[#6d6870]/30 dark:hover:bg-[#a0a0a0]/30\"\n              >\n                <i class=\"fas fa-times mr-2\"></i>\n                Annuler\n              </button>\n            </div>\n          </form>\n        </div>\n      </div>\n    </div>\n\n    <!-- Modal pour gérer les membres -->\n    <div\n      *ngIf=\"selectedEquipe\"\n      class=\"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4\"\n      (click)=\"selectedEquipe = null\"\n    >\n      <div\n        class=\"bg-white dark:bg-[#1a1a1a] rounded-xl shadow-2xl dark:shadow-[0_20px_50px_rgba(0,0,0,0.5)] max-w-2xl w-full max-h-[90vh] overflow-hidden border border-[#dac4ea]/20 dark:border-[#00f7ff]/20\"\n        (click)=\"$event.stopPropagation()\"\n      >\n        <!-- Header -->\n        <div\n          class=\"bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea] p-6\"\n        >\n          <div class=\"flex items-center justify-between\">\n            <div>\n              <h3 class=\"text-xl font-bold text-white mb-1\">\n                Gérer les membres\n              </h3>\n              <p class=\"text-white/80 text-sm\">\n                Équipe: {{ selectedEquipe.name }}\n              </p>\n            </div>\n            <button\n              (click)=\"selectedEquipe = null\"\n              class=\"text-white/80 hover:text-white hover:bg-white/10 p-2 rounded-lg transition-all\"\n            >\n              <i class=\"fas fa-times text-lg\"></i>\n            </button>\n          </div>\n        </div>\n\n        <!-- Content -->\n        <div class=\"p-6 max-h-[calc(90vh-140px)] overflow-y-auto\">\n          <!-- Membres actuels -->\n          <div class=\"mb-6\">\n            <h4\n              class=\"text-lg font-semibold text-[#dac4ea] dark:text-[#00f7ff] mb-4 flex items-center\"\n            >\n              <i class=\"fas fa-users mr-2\"></i>\n              Membres actuels\n            </h4>\n\n            <div\n              *ngIf=\"\n                selectedEquipe.members && selectedEquipe.members.length > 0;\n                else noMembers\n              \"\n            >\n              <div class=\"space-y-2\">\n                <div\n                  *ngFor=\"let membreId of selectedEquipe.members\"\n                  class=\"flex items-center justify-between p-3 bg-[#f0f4f8] dark:bg-[#0a0a0a] rounded-lg border border-[#dac4ea]/10 dark:border-[#00f7ff]/10\"\n                >\n                  <div class=\"flex items-center\">\n                    <div\n                      class=\"w-8 h-8 bg-gradient-to-br from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea] rounded-full flex items-center justify-center mr-3\"\n                    >\n                      <i class=\"fas fa-user text-white text-sm\"></i>\n                    </div>\n                    <span\n                      class=\"text-[#6d6870] dark:text-[#a0a0a0] font-medium\"\n                      >{{ membreId }}</span\n                    >\n                  </div>\n                  <button\n                    (click)=\"\n                      removeMembreFromEquipe(selectedEquipe._id, membreId)\n                    \"\n                    class=\"text-[#ff6b69] dark:text-[#ff3b30] hover:bg-[#ff6b69]/10 dark:hover:bg-[#ff3b30]/10 p-2 rounded-lg transition-all\"\n                    title=\"Retirer ce membre\"\n                  >\n                    <i class=\"fas fa-user-minus\"></i>\n                  </button>\n                </div>\n              </div>\n            </div>\n\n            <ng-template #noMembers>\n              <div class=\"text-center py-8\">\n                <div\n                  class=\"w-16 h-16 mx-auto mb-4 text-[#dac4ea] dark:text-[#00f7ff] opacity-50\"\n                >\n                  <i class=\"fas fa-user-friends text-4xl\"></i>\n                </div>\n                <p class=\"text-[#6d6870] dark:text-[#a0a0a0] text-sm\">\n                  Aucun membre dans cette équipe\n                </p>\n              </div>\n            </ng-template>\n          </div>\n\n          <!-- Ajouter un membre -->\n          <div class=\"mb-6\">\n            <h4\n              class=\"text-lg font-semibold text-[#dac4ea] dark:text-[#00f7ff] mb-4 flex items-center\"\n            >\n              <i class=\"fas fa-user-plus mr-2\"></i>\n              Ajouter un membre\n            </h4>\n\n            <div class=\"flex space-x-3\">\n              <input\n                #membreIdInput\n                type=\"text\"\n                class=\"flex-1 px-4 py-3 bg-[#f0f4f8] dark:bg-[#0a0a0a] border border-[#dac4ea]/20 dark:border-[#00f7ff]/20 rounded-lg text-[#6d6870] dark:text-[#a0a0a0] placeholder-[#6d6870]/50 dark:placeholder-[#a0a0a0]/50 focus:outline-none focus:ring-2 focus:ring-[#dac4ea] dark:focus:ring-[#00f7ff] focus:border-transparent transition-all\"\n                placeholder=\"ID du membre\"\n              />\n              <button\n                [disabled]=\"\n                  !selectedEquipe || !selectedEquipe._id || !membreIdInput.value\n                \"\n                (click)=\"\n                  addMembreToEquipe(selectedEquipe._id, membreIdInput.value);\n                  membreIdInput.value = ''\n                \"\n                class=\"bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea] text-white px-6 py-3 rounded-lg font-medium transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-[0_0_25px_rgba(218,196,234,0.4)] dark:hover:shadow-[0_0_25px_rgba(0,247,255,0.4)] disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100\"\n              >\n                <i class=\"fas fa-plus mr-2\"></i>\n                Ajouter\n              </button>\n            </div>\n\n            <p class=\"mt-2 text-xs text-[#6d6870] dark:text-[#a0a0a0]\">\n              Entrez l'ID du membre à ajouter à l'équipe\n            </p>\n          </div>\n\n          <!-- Note informative -->\n          <div\n            class=\"bg-[#dac4ea]/10 dark:bg-[#00f7ff]/10 border-l-4 border-[#dac4ea] dark:border-[#00f7ff] rounded-lg p-4\"\n          >\n            <div class=\"flex items-start\">\n              <div class=\"text-[#dac4ea] dark:text-[#00f7ff] mr-3 text-lg\">\n                <i class=\"fas fa-info-circle\"></i>\n              </div>\n              <div>\n                <h5\n                  class=\"font-semibold text-[#dac4ea] dark:text-[#00f7ff] mb-1\"\n                >\n                  Information importante\n                </h5>\n                <p class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0]\">\n                  Pour ajouter un membre, vous devez d'abord créer le membre\n                  dans la section des membres.\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Footer -->\n        <div class=\"p-6 border-t border-[#dac4ea]/10 dark:border-[#00f7ff]/10\">\n          <div class=\"flex justify-end\">\n            <button\n              (click)=\"selectedEquipe = null\"\n              class=\"bg-[#6d6870]/20 dark:bg-[#a0a0a0]/20 text-[#6d6870] dark:text-[#a0a0a0] px-6 py-3 rounded-xl font-medium transition-all duration-300 hover:scale-105 hover:bg-[#6d6870]/30 dark:hover:bg-[#a0a0a0]/30\"\n            >\n              <i class=\"fas fa-times mr-2\"></i>\n              Fermer\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": ";;;;;;;;ICgCIA,EAAA,CAAAC,cAAA,cAAgC;IAOtBD,EAAA,CAAAE,SAAA,YAA2C;IAC7CF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAoB;IAEhBD,EAAA,CAAAI,MAAA,eACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,YAAsD;IACpDD,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAGRH,EAAA,CAAAC,cAAA,kBAGC;IAFCD,EAAA,CAAAK,UAAA,mBAAAC,yDAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAAF,OAAA,CAAAG,KAAA,GAAiB,EAAE;IAAA,EAAC;IAGpBZ,EAAA,CAAAE,SAAA,aAA4B;IAC9BF,EAAA,CAAAG,YAAA,EAAS;;;;IATHH,EAAA,CAAAa,SAAA,IACF;IADEb,EAAA,CAAAc,kBAAA,MAAAC,MAAA,CAAAH,KAAA,MACF;;;;;IAcVZ,EAAA,CAAAC,cAAA,cAGC;IAEGD,EAAA,CAAAE,SAAA,cAEO;IAITF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,YAEC;IACCD,EAAA,CAAAI,MAAA,sBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;;IA4CJH,EAAA,CAAAC,cAAA,cAA4D;IAIxDD,EAAA,CAAAE,SAAA,YAAqC;IACvCF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,aAEC;IACCD,EAAA,CAAAI,MAAA,wCACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,YAAsD;IACpDD,EAAA,CAAAI,MAAA,8DACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;;;IA0CEH,EAAA,CAAAC,cAAA,aAGC;IAGKD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,aAAsB;IAIlBD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,aAAsB;IAElBD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,cAAsB;IAIlBD,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAETH,EAAA,CAAAC,cAAA,cAAsB;IAGhBD,EAAA,CAAAK,UAAA,mBAAAW,sEAAA;MAAA,MAAAC,WAAA,GAAAjB,EAAA,CAAAO,aAAA,CAAAW,IAAA;MAAA,MAAAC,UAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAC,OAAA,GAAArB,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAU,OAAA,CAAAC,UAAA,CAAAH,UAAA,CAAkB;IAAA,EAAC;IAI5BnB,EAAA,CAAAE,SAAA,aAA2B;IAC7BF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAIC;IAHCD,EAAA,CAAAK,UAAA,mBAAAkB,sEAAA;MAAA,MAAAN,WAAA,GAAAjB,EAAA,CAAAO,aAAA,CAAAW,IAAA;MAAA,MAAAC,UAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAI,OAAA,GAAAxB,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAa,OAAA,CAAAC,eAAA,CAAAN,UAAA,CAAuB;IAAA,EAAC;IAIjCnB,EAAA,CAAAE,SAAA,aAA4B;IAC9BF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAIC;IAHCD,EAAA,CAAAK,UAAA,mBAAAqB,sEAAA;MAAA,MAAAT,WAAA,GAAAjB,EAAA,CAAAO,aAAA,CAAAW,IAAA;MAAA,MAAAC,UAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAO,OAAA,GAAA3B,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAQ,UAAA,CAAAS,GAAA,IAAcD,OAAA,CAAAE,YAAA,CAAAV,UAAA,CAAAS,GAAA,CAAwB;IAAA,EAAC;IAIhD5B,EAAA,CAAAE,SAAA,aAA4B;IAC9BF,EAAA,CAAAG,YAAA,EAAS;;;;IA5CTH,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,MAAAK,UAAA,CAAAW,IAAA,MACF;IAME9B,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,MAAAK,UAAA,CAAAY,WAAA,8BACF;IAIE/B,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,MAAAK,UAAA,CAAAa,KAAA,2BACF;IAMEhC,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,OAAAK,UAAA,CAAAc,OAAA,kBAAAd,UAAA,CAAAc,OAAA,CAAAC,MAAA,sBACF;;;;;IAhEZlC,EAAA,CAAAC,cAAA,cAGC;IAQWD,EAAA,CAAAI,MAAA,YACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,aAEC;IACCD,EAAA,CAAAI,MAAA,oBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,aAEC;IACCD,EAAA,CAAAI,MAAA,eACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAEC;IACCD,EAAA,CAAAI,MAAA,iBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAEC;IACCD,EAAA,CAAAI,MAAA,iBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAGTH,EAAA,CAAAC,cAAA,iBAEC;IACCD,EAAA,CAAAmC,UAAA,KAAAC,4CAAA,kBAqDK;IACPpC,EAAA,CAAAG,YAAA,EAAQ;;;;IArDeH,EAAA,CAAAa,SAAA,IAAU;IAAVb,EAAA,CAAAqC,UAAA,YAAAC,OAAA,CAAAC,OAAA,CAAU;;;;;;IAhGzCvC,EAAA,CAAAC,cAAA,cAAmC;IAI/BD,EAAA,CAAAE,SAAA,cAEO;IAKPF,EAAA,CAAAC,cAAA,cAEC;IAQOD,EAAA,CAAAI,MAAA,+BACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,YAAsD;IACpDD,EAAA,CAAAI,MAAA,mEACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAGNH,EAAA,CAAAC,cAAA,kBAGC;IAFCD,EAAA,CAAAK,UAAA,mBAAAmC,yDAAA;MAAAxC,EAAA,CAAAO,aAAA,CAAAkC,IAAA;MAAA,MAAAC,OAAA,GAAA1C,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAA+B,OAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;IAGvB3C,EAAA,CAAAE,SAAA,aAAoC;IACpCF,EAAA,CAAAI,MAAA,yBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IAMfH,EAAA,CAAAmC,UAAA,KAAAS,sCAAA,kBAcM;IAGN5C,EAAA,CAAAmC,UAAA,KAAAU,sCAAA,mBA+FM;IACR7C,EAAA,CAAAG,YAAA,EAAM;;;;IAjHEH,EAAA,CAAAa,SAAA,IAA0B;IAA1Bb,EAAA,CAAAqC,UAAA,SAAAS,MAAA,CAAAP,OAAA,CAAAL,MAAA,OAA0B;IAkB7BlC,EAAA,CAAAa,SAAA,GAAwB;IAAxBb,EAAA,CAAAqC,UAAA,SAAAS,MAAA,CAAAP,OAAA,CAAAL,MAAA,KAAwB;;;;;IAwJnBlC,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAI,MAAA,2CACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;;IA+BJH,EAAA,CAAAE,SAAA,eAGQ;;;;;IACRF,EAAA,CAAAE,SAAA,YAAiD;;;;;;IAInDF,EAAA,CAAAC,cAAA,iBAKC;IAFCD,EAAA,CAAAK,UAAA,mBAAA0C,2DAAA;MAAA/C,EAAA,CAAAO,aAAA,CAAAyC,IAAA;MAAA,MAAAC,OAAA,GAAAjD,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAsC,OAAA,CAAAC,UAAA,EAAY;IAAA,EAAC;IAGtBlD,EAAA,CAAAE,SAAA,YAAiC;IACjCF,EAAA,CAAAI,MAAA,gBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;;;IAyDPH,EAAA,CAAAC,cAAA,eAGC;IAKKD,EAAA,CAAAE,SAAA,aAA8C;IAChDF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAEG;IAAAD,EAAA,CAAAI,MAAA,GAAc;IAAAJ,EAAA,CAAAG,YAAA,EAChB;IAEHH,EAAA,CAAAC,cAAA,kBAMC;IALCD,EAAA,CAAAK,UAAA,mBAAA8C,qEAAA;MAAA,MAAAlC,WAAA,GAAAjB,EAAA,CAAAO,aAAA,CAAA6C,IAAA;MAAA,MAAAC,YAAA,GAAApC,WAAA,CAAAG,SAAA;MAAA,MAAAkC,OAAA,GAAAtD,EAAA,CAAAU,aAAA;MAAA,OACyBV,EAAA,CAAAW,WAAA,CAAA2C,OAAA,CAAAC,sBAAA,CAAAD,OAAA,CAAAE,cAAA,CAAA5B,GAAA,EAAAyB,YAAA,CAE7C;IAAA,EADqB;IAIDrD,EAAA,CAAAE,SAAA,aAAiC;IACnCF,EAAA,CAAAG,YAAA,EAAS;;;;IAXJH,EAAA,CAAAa,SAAA,GAAc;IAAdb,EAAA,CAAAyD,iBAAA,CAAAJ,YAAA,CAAc;;;;;IAnBzBrD,EAAA,CAAAC,cAAA,UAKC;IAEGD,EAAA,CAAAmC,UAAA,IAAAuB,4CAAA,mBAwBM;IACR1D,EAAA,CAAAG,YAAA,EAAM;;;;IAxBmBH,EAAA,CAAAa,SAAA,GAAyB;IAAzBb,EAAA,CAAAqC,UAAA,YAAAsB,OAAA,CAAAH,cAAA,CAAAvB,OAAA,CAAyB;;;;;IA4BlDjC,EAAA,CAAAC,cAAA,eAA8B;IAI1BD,EAAA,CAAAE,SAAA,aAA4C;IAC9CF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,YAAsD;IACpDD,EAAA,CAAAI,MAAA,4CACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;;;IAtFhBH,EAAA,CAAAC,cAAA,cAIC;IADCD,EAAA,CAAAK,UAAA,mBAAAuD,qDAAA;MAAA5D,EAAA,CAAAO,aAAA,CAAAsD,IAAA;MAAA,MAAAC,OAAA,GAAA9D,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAAmD,OAAA,CAAAN,cAAA,GAA0B,IAAI;IAAA,EAAC;IAE/BxD,EAAA,CAAAC,cAAA,cAGC;IADCD,EAAA,CAAAK,UAAA,mBAAA0D,qDAAAC,MAAA;MAAA,OAASA,MAAA,CAAAC,eAAA,EAAwB;IAAA,EAAC;IAGlCjE,EAAA,CAAAC,cAAA,cAEC;IAIOD,EAAA,CAAAI,MAAA,+BACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,YAAiC;IAC/BD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAENH,EAAA,CAAAC,cAAA,iBAGC;IAFCD,EAAA,CAAAK,UAAA,mBAAA6D,wDAAA;MAAAlE,EAAA,CAAAO,aAAA,CAAAsD,IAAA;MAAA,MAAAM,OAAA,GAAAnE,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAAwD,OAAA,CAAAX,cAAA,GAA0B,IAAI;IAAA,EAAC;IAG/BxD,EAAA,CAAAE,SAAA,aAAoC;IACtCF,EAAA,CAAAG,YAAA,EAAS;IAKbH,EAAA,CAAAC,cAAA,eAA0D;IAMpDD,EAAA,CAAAE,SAAA,aAAiC;IACjCF,EAAA,CAAAI,MAAA,yBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAELH,EAAA,CAAAmC,UAAA,KAAAiC,sCAAA,kBAiCM;IAENpE,EAAA,CAAAmC,UAAA,KAAAkC,8CAAA,iCAAArE,EAAA,CAAAsE,sBAAA,CAWc;IAChBtE,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAAkB;IAIdD,EAAA,CAAAE,SAAA,aAAqC;IACrCF,EAAA,CAAAI,MAAA,2BACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAELH,EAAA,CAAAC,cAAA,gBAA4B;IAC1BD,EAAA,CAAAE,SAAA,uBAKE;IACFF,EAAA,CAAAC,cAAA,mBASC;IALCD,EAAA,CAAAK,UAAA,mBAAAkE,yDAAA;MAAAvE,EAAA,CAAAO,aAAA,CAAAsD,IAAA;MAAA,MAAAW,IAAA,GAAAxE,EAAA,CAAAyE,WAAA;MAAA,MAAAC,OAAA,GAAA1E,EAAA,CAAAU,aAAA;MACqBgE,OAAA,CAAAC,iBAAA,CAAAD,OAAA,CAAAlB,cAAA,CAAA5B,GAAA,EAAA4C,IAAA,CAAAI,KAAA,CACpB;MAAA,OAAoB5E,EAAA,CAAAW,WAAA,CAAA6D,IAAA,CAAAI,KAAA,GACrB,EAChB;IAAA,EADiB;IAGD5E,EAAA,CAAAE,SAAA,cAAgC;IAChCF,EAAA,CAAAI,MAAA,iBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IAGXH,EAAA,CAAAC,cAAA,cAA2D;IACzDD,EAAA,CAAAI,MAAA,mEACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAINH,EAAA,CAAAC,cAAA,gBAEC;IAGKD,EAAA,CAAAE,SAAA,cAAkC;IACpCF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,WAAK;IAIDD,EAAA,CAAAI,MAAA,gCACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,aAAsD;IACpDD,EAAA,CAAAI,MAAA,sGAEF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAOZH,EAAA,CAAAC,cAAA,gBAAuE;IAGjED,EAAA,CAAAK,UAAA,mBAAAwE,yDAAA;MAAA7E,EAAA,CAAAO,aAAA,CAAAsD,IAAA;MAAA,MAAAiB,OAAA,GAAA9E,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAAmE,OAAA,CAAAtB,cAAA,GAA0B,IAAI;IAAA,EAAC;IAG/BxD,EAAA,CAAAE,SAAA,aAAiC;IACjCF,EAAA,CAAAI,MAAA,gBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;;;IA5ILH,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,mBAAAiE,MAAA,CAAAvB,cAAA,CAAA1B,IAAA,MACF;IAuBC9B,EAAA,CAAAa,SAAA,GAGb;IAHab,EAAA,CAAAqC,UAAA,SAAA0C,MAAA,CAAAvB,cAAA,CAAAvB,OAAA,IAAA8C,MAAA,CAAAvB,cAAA,CAAAvB,OAAA,CAAAC,MAAA,KAGb,aAAA8C,IAAA;IA8DchF,EAAA,CAAAa,SAAA,IAEC;IAFDb,EAAA,CAAAqC,UAAA,cAAA0C,MAAA,CAAAvB,cAAA,KAAAuB,MAAA,CAAAvB,cAAA,CAAA5B,GAAA,KAAA4C,IAAA,CAAAI,KAAA,CAEC;;;AD1bjB,OAAM,MAAOK,eAAe;EAS1BC,YACUC,aAA4B,EAC5BC,aAA4B;IAD5B,KAAAD,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;IAVvB,KAAA7C,OAAO,GAAa,EAAE;IACtB,KAAA8C,SAAS,GAAW;MAAEvD,IAAI,EAAE,EAAE;MAAEC,WAAW,EAAE;IAAE,CAAE;IACjD,KAAAyB,cAAc,GAAkB,IAAI;IACpC,KAAA8B,SAAS,GAAG,KAAK;IACjB,KAAAC,OAAO,GAAa,EAAE;IACtB,KAAAC,OAAO,GAAG,KAAK;IACf,KAAA5E,KAAK,GAAG,EAAE;EAKP;EAEH6E,QAAQA,CAAA;IACN,IAAI,CAAC9C,WAAW,EAAE;IAClB,IAAI,CAAC+C,WAAW,EAAE;EACpB;EAEA/C,WAAWA,CAAA;IACT,IAAI,CAAC6C,OAAO,GAAG,IAAI;IACnB,IAAI,CAACL,aAAa,CAACQ,UAAU,EAAE,CAACC,SAAS,CAAC;MACxCC,IAAI,EAAGC,IAAI,IAAI;QACbC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEF,IAAI,CAAC;QACpC,IAAI,CAACvD,OAAO,GAAGuD,IAAI;QACnB,IAAI,CAACN,OAAO,GAAG,KAAK;MACtB,CAAC;MACD5E,KAAK,EAAGA,KAAK,IAAI;QACfmF,OAAO,CAACnF,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAACA,KAAK,GAAG,yCAAyC,GAAGA,KAAK,CAACqF,OAAO;QACtE,IAAI,CAACT,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAE,WAAWA,CAAA;IACT,IAAI,CAACF,OAAO,GAAG,IAAI;IACnB,IAAI,CAACJ,aAAa,CAACc,UAAU,EAAE,CAACN,SAAS,CAAC;MACxCC,IAAI,EAAGC,IAAI,IAAI;QACbC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEF,IAAI,CAAC;QACpC,IAAI,CAACP,OAAO,GAAGO,IAAI;QACnB,IAAI,CAACN,OAAO,GAAG,KAAK;MACtB,CAAC;MACD5E,KAAK,EAAGA,KAAK,IAAI;QACfmF,OAAO,CAACnF,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAACA,KAAK,GAAG,yCAAyC,GAAGA,KAAK,CAACqF,OAAO;QACtE,IAAI,CAACT,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAW,SAASA,CAAA;IACPJ,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAACX,SAAS,CAAC;IAE7C,IAAI,CAAC,IAAI,CAACA,SAAS,CAACvD,IAAI,EAAE;MACxBiE,OAAO,CAACnF,KAAK,CAAC,uBAAuB,CAAC;MACtC,IAAI,CAACA,KAAK,GAAG,gCAAgC;MAC7C;;IAGF,IAAI,CAAC4E,OAAO,GAAG,IAAI;IACnB,IAAI,CAAC5E,KAAK,GAAG,EAAE;IAEf,IAAI,CAACuE,aAAa,CAACgB,SAAS,CAAC,IAAI,CAACd,SAAS,CAAC,CAACO,SAAS,CAAC;MACrDC,IAAI,EAAGO,QAAQ,IAAI;QACjBL,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEI,QAAQ,CAAC;QACnD,IAAI,CAACzD,WAAW,EAAE;QAClB,IAAI,CAAC0C,SAAS,GAAG;UAAEvD,IAAI,EAAE,EAAE;UAAEC,WAAW,EAAE;QAAE,CAAE,CAAC,CAAC;QAChD,IAAI,CAACyD,OAAO,GAAG,KAAK;QAEpB;QACA,MAAMa,cAAc,GAAG,2BAA2B;QAClD,IAAI,CAACzF,KAAK,GAAG,EAAE,CAAC,CAAC;QACjB0F,KAAK,CAACD,cAAc,CAAC;MACvB,CAAC;MACDzF,KAAK,EAAGA,KAAK,IAAI;QACfmF,OAAO,CAACnF,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C,IAAI,CAACA,KAAK,GAAG,2CAA2C,IAAIA,KAAK,CAACA,KAAK,EAAEqF,OAAO,IAAIrF,KAAK,CAACqF,OAAO,IAAI,eAAe,CAAC;QACrH,IAAI,CAACT,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAlE,UAAUA,CAACiF,MAAc;IACvB,IAAI,CAACjB,SAAS,GAAG,IAAI;IACrB;IACA,IAAI,CAACD,SAAS,GAAG;MACfzD,GAAG,EAAE2E,MAAM,CAAC3E,GAAG;MACfE,IAAI,EAAEyE,MAAM,CAACzE,IAAI,IAAI,EAAE;MACvBC,WAAW,EAAEwE,MAAM,CAACxE,WAAW,IAAI,EAAE;MACrCC,KAAK,EAAEuE,MAAM,CAACvE,KAAK;MACnBC,OAAO,EAAEsE,MAAM,CAACtE,OAAO,GAAG,CAAC,GAAGsE,MAAM,CAACtE,OAAO,CAAC,GAAG;KACjD;EACH;EAEAiB,UAAUA,CAAA;IACR,IAAI,CAACoC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACD,SAAS,GAAG;MAAEvD,IAAI,EAAE,EAAE;MAAEC,WAAW,EAAE;IAAE,CAAE;IAC9C,IAAI,CAACnB,KAAK,GAAG,EAAE,CAAC,CAAC;EACnB;;EAEA4F,oBAAoBA,CAAA;IAClB,IAAI,CAAC,IAAI,CAACnB,SAAS,CAACvD,IAAI,EAAE;MACxBiE,OAAO,CAACnF,KAAK,CAAC,uBAAuB,CAAC;MACtC,IAAI,CAACA,KAAK,GAAG,gCAAgC;MAC7C;;IAGF,IAAI,IAAI,CAACyE,SAAS,CAACzD,GAAG,EAAE;MACtB,IAAI,CAAC4D,OAAO,GAAG,IAAI;MACnB,IAAI,CAAC5E,KAAK,GAAG,EAAE;MAEf,IAAI,CAACuE,aAAa,CAACsB,YAAY,CAAC,IAAI,CAACpB,SAAS,CAACzD,GAAG,EAAE,IAAI,CAACyD,SAAS,CAAC,CAACO,SAAS,CAAC;QAC5EC,IAAI,EAAGa,aAAa,IAAI;UACtBX,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEU,aAAa,CAAC;UACxD,IAAI,CAAC/D,WAAW,EAAE;UAClB,IAAI,CAAC2C,SAAS,GAAG,KAAK;UACtB,IAAI,CAACD,SAAS,GAAG;YAAEvD,IAAI,EAAE,EAAE;YAAEC,WAAW,EAAE;UAAE,CAAE;UAC9C,IAAI,CAACyD,OAAO,GAAG,KAAK;UAEpB;UACA,MAAMa,cAAc,GAAG,iCAAiC;UACxDC,KAAK,CAACD,cAAc,CAAC;QACvB,CAAC;QACDzF,KAAK,EAAGA,KAAK,IAAI;UACfmF,OAAO,CAACnF,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;UAC5C,IAAI,CAACA,KAAK,GAAG,8CAA8C,IAAIA,KAAK,CAACA,KAAK,EAAEqF,OAAO,IAAIrF,KAAK,CAACqF,OAAO,IAAI,eAAe,CAAC;UACxH,IAAI,CAACT,OAAO,GAAG,KAAK;QACtB;OACD,CAAC;KACH,MAAM;MACL,IAAI,CAAC5E,KAAK,GAAG,8CAA8C;;EAE/D;EAEAiB,YAAYA,CAAC8E,EAAU;IACrB,IAAI,CAACA,EAAE,EAAE;MACPZ,OAAO,CAACnF,KAAK,CAAC,iBAAiB,CAAC;MAChC,IAAI,CAACA,KAAK,GAAG,4BAA4B;MACzC;;IAGF,IAAIgG,OAAO,CAAC,iFAAiF,CAAC,EAAE;MAC9F,IAAI,CAACpB,OAAO,GAAG,IAAI;MACnB,IAAI,CAAC5E,KAAK,GAAG,EAAE;MAEf,IAAI,CAACuE,aAAa,CAACtD,YAAY,CAAC8E,EAAE,CAAC,CAACf,SAAS,CAAC;QAC5CC,IAAI,EAAGO,QAAQ,IAAI;UACjBL,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEI,QAAQ,CAAC;UAEnD;UACA,IAAI,IAAI,CAACd,SAAS,IAAI,IAAI,CAACD,SAAS,CAACzD,GAAG,KAAK+E,EAAE,EAAE;YAC/C,IAAI,CAACrB,SAAS,GAAG,KAAK;YACtB,IAAI,CAACD,SAAS,GAAG;cAAEvD,IAAI,EAAE,EAAE;cAAEC,WAAW,EAAE;YAAE,CAAE;;UAGhD,IAAI,CAACY,WAAW,EAAE;UAClB,IAAI,CAAC6C,OAAO,GAAG,KAAK;UAEpB;UACAc,KAAK,CAAC,8BAA8B,CAAC;QACvC,CAAC;QACD1F,KAAK,EAAGA,KAAK,IAAI;UACfmF,OAAO,CAACnF,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;UAC5C,IAAI,CAACA,KAAK,GAAG,8CAA8C,IAAIA,KAAK,CAACA,KAAK,EAAEqF,OAAO,IAAIrF,KAAK,CAACqF,OAAO,IAAI,eAAe,CAAC;UACxH,IAAI,CAACT,OAAO,GAAG,KAAK;QACtB;OACD,CAAC;;EAEN;EAEA/D,eAAeA,CAAC8E,MAAc;IAC5B,IAAI,CAAC/C,cAAc,GAAG+C,MAAM;IAC5B;IACA,MAAMM,QAAQ,GAAGC,QAAQ,CAACC,cAAc,CAAC,aAAa,CAAC;IACvD,IAAIF,QAAQ,EAAE;MACZ,IAAI;QACF;QACA,IAAI,OAAOG,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,SAAS,EAAE;UACrD,MAAMC,KAAK,GAAG,IAAIF,MAAM,CAACC,SAAS,CAACE,KAAK,CAACN,QAAQ,CAAC;UAClDK,KAAK,CAACE,IAAI,EAAE;SACb,MAAM;UACLrB,OAAO,CAACnF,KAAK,CAAC,kCAAkC,CAAC;UACjD0F,KAAK,CAAC,kDAAkD,CAAC;;OAE5D,CAAC,OAAO1F,KAAK,EAAE;QACdmF,OAAO,CAACnF,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;;KAE/C,MAAM;MACLmF,OAAO,CAACnF,KAAK,CAAC,yBAAyB,CAAC;;EAE5C;EAEA+D,iBAAiBA,CAAC0C,MAA0B,EAAEC,QAAgB;IAC5D,IAAI,CAACD,MAAM,EAAE;MACXtB,OAAO,CAACnF,KAAK,CAAC,sBAAsB,CAAC;MACrC0F,KAAK,CAAC,4BAA4B,CAAC;MACnC;;IAGF,IAAI,CAACgB,QAAQ,IAAIA,QAAQ,CAACC,IAAI,EAAE,KAAK,EAAE,EAAE;MACvCxB,OAAO,CAACnF,KAAK,CAAC,oBAAoB,CAAC;MACnC0F,KAAK,CAAC,4BAA4B,CAAC;MACnC;;IAGF,IAAI,CAACd,OAAO,GAAG,IAAI;IAEnB;IACA,MAAMgC,MAAM,GAAW;MAAEb,EAAE,EAAEW;IAAQ,CAAE;IAEvC,IAAI,CAACnC,aAAa,CAACR,iBAAiB,CAAC0C,MAAM,EAAEG,MAAM,CAAC,CAAC5B,SAAS,CAAC;MAC7DC,IAAI,EAAGO,QAAQ,IAAI;QACjBL,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEI,QAAQ,CAAC;QACnD,IAAI,CAACzD,WAAW,EAAE;QAClB,IAAI,CAAC6C,OAAO,GAAG,KAAK;QAEpB;QACAc,KAAK,CAAC,uCAAuC,CAAC;MAChD,CAAC;MACD1F,KAAK,EAAGA,KAAK,IAAI;QACfmF,OAAO,CAACnF,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C,IAAI,CAACA,KAAK,GAAG,qCAAqC,IAAIA,KAAK,CAACA,KAAK,EAAEqF,OAAO,IAAIrF,KAAK,CAACqF,OAAO,IAAI,eAAe,CAAC;QAC/GK,KAAK,CAAC,IAAI,CAAC1F,KAAK,CAAC;QACjB,IAAI,CAAC4E,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAjC,sBAAsBA,CAAC8D,MAA0B,EAAEC,QAAgB;IACjE,IAAI,CAACD,MAAM,EAAE;MACXtB,OAAO,CAACnF,KAAK,CAAC,sBAAsB,CAAC;MACrC0F,KAAK,CAAC,4BAA4B,CAAC;MACnC;;IAGF,IAAI,CAACgB,QAAQ,EAAE;MACbvB,OAAO,CAACnF,KAAK,CAAC,wBAAwB,CAAC;MACvC0F,KAAK,CAAC,yBAAyB,CAAC;MAChC;;IAGF,IAAIM,OAAO,CAAC,0DAA0D,CAAC,EAAE;MACvE,IAAI,CAACpB,OAAO,GAAG,IAAI;MAEnB,IAAI,CAACL,aAAa,CAAC5B,sBAAsB,CAAC8D,MAAM,EAAEC,QAAQ,CAAC,CAAC1B,SAAS,CAAC;QACpEC,IAAI,EAAGO,QAAQ,IAAI;UACjBL,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEI,QAAQ,CAAC;UACrD,IAAI,CAACzD,WAAW,EAAE;UAClB,IAAI,CAAC6C,OAAO,GAAG,KAAK;UAEpB;UACA,IAAI,IAAI,CAAChC,cAAc,IAAI,IAAI,CAACA,cAAc,CAAC5B,GAAG,KAAKyF,MAAM,EAAE;YAC7D,MAAMX,aAAa,GAAG,IAAI,CAACnE,OAAO,CAACkF,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC9F,GAAG,KAAKyF,MAAM,CAAC;YAC9D,IAAIX,aAAa,EAAE;cACjB,IAAI,CAAClD,cAAc,GAAGkD,aAAa;;;QAGzC,CAAC;QACD9F,KAAK,EAAGA,KAAK,IAAI;UACfmF,OAAO,CAACnF,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;UAC9C,IAAI,CAACA,KAAK,GAAG,2CAA2C,IAAIA,KAAK,CAACA,KAAK,EAAEqF,OAAO,IAAIrF,KAAK,CAACqF,OAAO,IAAI,eAAe,CAAC;UACrHK,KAAK,CAAC,IAAI,CAAC1F,KAAK,CAAC;UACjB,IAAI,CAAC4E,OAAO,GAAG,KAAK;QACtB;OACD,CAAC;;EAEN;;;uBA3QWP,eAAe,EAAAjF,EAAA,CAAA2H,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAA7H,EAAA,CAAA2H,iBAAA,CAAAG,EAAA,CAAAC,aAAA;IAAA;EAAA;;;YAAf9C,eAAe;MAAA+C,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCnB5BtI,EAAA,CAAAC,cAAA,aAEC;UAGGD,EAAA,CAAAE,SAAA,aAEO;UAMPF,EAAA,CAAAC,cAAA,aAA4D;UAExDD,EAAA,CAAAE,SAAA,aAAmE;UAWrEF,EAAA,CAAAG,YAAA,EAAM;UAIVH,EAAA,CAAAC,cAAA,cAAiD;UAE/CD,EAAA,CAAAmC,UAAA,KAAAqG,+BAAA,kBA0BM;UAGNxI,EAAA,CAAAmC,UAAA,KAAAsG,+BAAA,iBAiBM;UAGNzI,EAAA,CAAAmC,UAAA,KAAAuG,+BAAA,mBAyJM;UAGN1I,EAAA,CAAAC,cAAA,eAA2B;UAEzBD,EAAA,CAAAE,SAAA,eAEO;UAKPF,EAAA,CAAAC,cAAA,eAEC;UAMKD,EAAA,CAAAI,MAAA,IACF;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,aAAiC;UAC/BD,EAAA,CAAAI,MAAA,IAKF;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAINH,EAAA,CAAAC,cAAA,eAAiB;UAOTD,EAAA,CAAAI,MAAA,8BACA;UAAAJ,EAAA,CAAAC,cAAA,gBAAiD;UAAAD,EAAA,CAAAI,MAAA,SAAC;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UAE3DH,EAAA,CAAAC,cAAA,qBAcE;UAVAD,EAAA,CAAAK,UAAA,mBAAAsI,iDAAA;YAAA3I,EAAA,CAAAO,aAAA,CAAAqI,IAAA;YAAA,MAAAC,GAAA,GAAA7I,EAAA,CAAAyE,WAAA;YAAA,OAASzE,EAAA,CAAAW,WAAA,CAAA4H,GAAA,CAAAlD,SAAA,CAAAvD,IAAA,GAAA+G,GAAA,CAAAjE,KAAA,CAAgC;UAAA,EAAC;UAJ5C5E,EAAA,CAAAG,YAAA,EAcE;UACFH,EAAA,CAAAmC,UAAA,KAAA2G,+BAAA,kBAKM;UACR9I,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,WAAK;UAIDD,EAAA,CAAAI,MAAA,qBACF;UAAAJ,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,wBAOC;UAJCD,EAAA,CAAAK,UAAA,mBAAA0I,oDAAA;YAAA/I,EAAA,CAAAO,aAAA,CAAAqI,IAAA;YAAA,MAAAI,GAAA,GAAAhJ,EAAA,CAAAyE,WAAA;YAAA,OAASzE,EAAA,CAAAW,WAAA,CAAA4H,GAAA,CAAAlD,SAAA,CAAAtD,WAAA,GAAAiH,GAAA,CAAApE,KAAA,CAAuC;UAAA,EAAC;UAIlD5E,EAAA,CAAAG,YAAA,EAAW;UACZH,EAAA,CAAAC,cAAA,aAA2D;UACzDD,EAAA,CAAAI,MAAA,wEACF;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAINH,EAAA,CAAAC,cAAA,eAA8C;UAI1CD,EAAA,CAAAK,UAAA,mBAAA4I,kDAAA;YAAA,OAAAV,GAAA,CAAAjD,SAAA,GAAqBiD,GAAA,CAAA/B,oBAAA,EAAsB,GAAG+B,GAAA,CAAApC,SAAA,EAAW;UAAA,EAAC;UAG1DnG,EAAA,CAAAmC,UAAA,KAAA+G,gCAAA,mBAGQ;UACRlJ,EAAA,CAAAmC,UAAA,KAAAgH,6BAAA,gBAAiD;UACjDnJ,EAAA,CAAAI,MAAA,IACF;UAAAJ,EAAA,CAAAG,YAAA,EAAS;UAETH,EAAA,CAAAmC,UAAA,KAAAiH,kCAAA,qBAQS;UACXpJ,EAAA,CAAAG,YAAA,EAAM;UAOdH,EAAA,CAAAmC,UAAA,KAAAkH,+BAAA,mBAmKM;UACRrJ,EAAA,CAAAG,YAAA,EAAM;;;UAneEH,EAAA,CAAAa,SAAA,IAAW;UAAXb,EAAA,CAAAqC,UAAA,SAAAkG,GAAA,CAAA3H,KAAA,CAAW;UA8BdZ,EAAA,CAAAa,SAAA,GAAa;UAAbb,EAAA,CAAAqC,UAAA,SAAAkG,GAAA,CAAA/C,OAAA,CAAa;UAmBVxF,EAAA,CAAAa,SAAA,GAAc;UAAdb,EAAA,CAAAqC,UAAA,UAAAkG,GAAA,CAAA/C,OAAA,CAAc;UA6KZxF,EAAA,CAAAa,SAAA,GACF;UADEb,EAAA,CAAAc,kBAAA,MAAAyH,GAAA,CAAAjD,SAAA,kEACF;UAEEtF,EAAA,CAAAa,SAAA,GAKF;UALEb,EAAA,CAAAc,kBAAA,MAAAyH,GAAA,CAAAjD,SAAA,oHAKF;UAoBMtF,EAAA,CAAAa,SAAA,GAEC;UAFDb,EAAA,CAAAsJ,WAAA,sBAAAf,GAAA,CAAAlD,SAAA,CAAAvD,IAAA,KAAAyG,GAAA,CAAAjD,SAAA,IAAAiD,GAAA,CAAAlD,SAAA,CAAAvD,IAAA,SAEC,2BAAAyG,GAAA,CAAAlD,SAAA,CAAAvD,IAAA,KAAAyG,GAAA,CAAAjD,SAAA,IAAAiD,GAAA,CAAAlD,SAAA,CAAAvD,IAAA;UALD9B,EAAA,CAAAqC,UAAA,UAAAkG,GAAA,CAAAlD,SAAA,CAAAvD,IAAA,CAAwB;UAavB9B,EAAA,CAAAa,SAAA,GAA6D;UAA7Db,EAAA,CAAAqC,UAAA,UAAAkG,GAAA,CAAAlD,SAAA,CAAAvD,IAAA,KAAAyG,GAAA,CAAAjD,SAAA,IAAAiD,GAAA,CAAAlD,SAAA,CAAAvD,IAAA,SAA6D;UAgB9D9B,EAAA,CAAAa,SAAA,GAAqC;UAArCb,EAAA,CAAAqC,UAAA,UAAAkG,GAAA,CAAAlD,SAAA,CAAAtD,WAAA,OAAqC;UAerC/B,EAAA,CAAAa,SAAA,GAAuC;UAAvCb,EAAA,CAAAqC,UAAA,cAAAkG,GAAA,CAAAlD,SAAA,CAAAvD,IAAA,IAAAyG,GAAA,CAAA/C,OAAA,CAAuC;UAKpCxF,EAAA,CAAAa,SAAA,GAAa;UAAbb,EAAA,CAAAqC,UAAA,SAAAkG,GAAA,CAAA/C,OAAA,CAAa;UAGZxF,EAAA,CAAAa,SAAA,GAAc;UAAdb,EAAA,CAAAqC,UAAA,UAAAkG,GAAA,CAAA/C,OAAA,CAAc;UAClBxF,EAAA,CAAAa,SAAA,GACF;UADEb,EAAA,CAAAc,kBAAA,MAAAyH,GAAA,CAAAjD,SAAA,4CACF;UAGGtF,EAAA,CAAAa,SAAA,GAAe;UAAfb,EAAA,CAAAqC,UAAA,SAAAkG,GAAA,CAAAjD,SAAA,CAAe;UAgBzBtF,EAAA,CAAAa,SAAA,GAAoB;UAApBb,EAAA,CAAAqC,UAAA,SAAAkG,GAAA,CAAA/E,cAAA,CAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}