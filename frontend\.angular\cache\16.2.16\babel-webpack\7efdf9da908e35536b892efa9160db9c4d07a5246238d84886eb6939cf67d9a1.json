{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { FrontLayoutComponent } from './layouts/front-layout/front-layout.component';\nimport { AdminLayoutComponent } from './layouts/admin-layout/admin-layout.component';\nimport { AuthAdminLayoutComponent } from './layouts/auth-admin-layout/auth-admin-layout.component';\nimport { guardadminGuard } from './views/guards/guardadmin.guard';\nimport { guarduserGuard } from './views/guards/guarduser.guard';\nimport { noguarduserGuard } from './views/guards/noguarduser.guard';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [\n// Espace Front\n{\n  path: '',\n  component: FrontLayoutComponent,\n  children: [{\n    path: '',\n    loadChildren: () => import('./views/front/home/<USER>').then(m => m.HomeModule)\n  }, {\n    path: 'profile',\n    loadChildren: () => import('./views/front/profile/profile.module').then(m => m.ProfileModule),\n    canActivateChild: [guarduserGuard]\n  }, {\n    path: 'messages',\n    loadChildren: () => import('./views/front/messages/messages.module').then(m => m.MessagesModule),\n    canActivateChild: [guarduserGuard]\n  }, {\n    path: 'plannings',\n    loadChildren: () => import('./views/front/plannings/plannings.module').then(m => m.PlanningsModule),\n    canActivateChild: [guarduserGuard]\n  }, {\n    path: 'reunions',\n    loadChildren: () => import('./views/front/reunions/reunions.module').then(m => m.ReunionsModule),\n    canActivateChild: [guarduserGuard]\n  }, {\n    path: 'notifications',\n    loadChildren: () => import('./views/front/notifications/notifications.module').then(m => m.NotificationsModule),\n    canActivateChild: [guarduserGuard]\n  }, {\n    path: 'change-password',\n    loadChildren: () => import('./views/front/change-password/change-password.module').then(m => m.ChangePasswordModule),\n    canActivateChild: [guarduserGuard]\n  }, {\n    path: 'signup',\n    loadChildren: () => import('./views/front/signup/signup.module').then(m => m.SignupModule),\n    canActivateChild: [noguarduserGuard]\n  }, {\n    path: 'login',\n    loadChildren: () => import('./views/front/login/login.module').then(m => m.LoginModule),\n    canActivateChild: [noguarduserGuard]\n  }, {\n    path: 'verify-email',\n    loadChildren: () => import('./views/front/verify-email/verify-email.module').then(m => m.VerifyEmailModule)\n  }, {\n    path: 'reset-password',\n    loadChildren: () => import('./views/front/reset-password/reset-password.module').then(m => m.ResetPasswordModule),\n    canActivateChild: [guarduserGuard]\n  }, {\n    path: 'forgot-password',\n    loadChildren: () => import('./views/front/forgot-password/forgot-password.module').then(m => m.ForgotPasswordModule)\n  }, {\n    path: 'projects',\n    loadChildren: () => import('./views/front/projects/projects.module').then(m => m.ProjectsModule),\n    canActivateChild: [guarduserGuard] // Protection pour utilisateurs authentifiés\n  }, {\n    path: 'equipes',\n    loadChildren: () => import('./views/front/equipes/equipes.module').then(m => m.EquipesModule),\n    canActivateChild: [guarduserGuard] // Protection pour utilisateurs authentifiés\n  }]\n},\n//  Espace Admin\n{\n  path: 'admin',\n  component: AdminLayoutComponent,\n  canActivate: [guardadminGuard],\n  children: [{\n    path: '',\n    loadChildren: () => import('./views/admin/dashboard/dashboard.module').then(m => m.DashboardModule)\n  }, {\n    path: 'dashboard',\n    loadChildren: () => import('./views/admin/dashboard/dashboard.module').then(m => m.DashboardModule)\n  }, {\n    path: 'userdetails/:id',\n    loadChildren: () => import('./views/admin/userdetails/userdetails.module').then(m => m.UserdetailsModule)\n  }, {\n    path: 'plannings',\n    loadChildren: () => import('./views/admin/plannings/plannings.module').then(m => m.PlanningsModule)\n  }, {\n    path: 'reunions',\n    loadChildren: () => import('./views/admin/reunions/reunions.module').then(m => m.ReunionsModule)\n  }, {\n    path: 'projects',\n    loadChildren: () => import('./views/admin/projects/projects.module').then(m => m.ProjectsModule),\n    canActivateChild: [guarduserGuard]\n  }, {\n    path: 'profile',\n    loadChildren: () => import('./views/admin/profile/profile.module').then(m => m.ProfileModule)\n  }, {\n    path: 'equipes',\n    loadChildren: () => import('./views/front/equipes/equipes.module').then(m => m.EquipesModule)\n  }]\n},\n//  Espace Auth-admin\n{\n  path: 'admin/login',\n  component: AuthAdminLayoutComponent\n}];\nexport class AppRoutingModule {\n  static {\n    this.ɵfac = function AppRoutingModule_Factory(t) {\n      return new (t || AppRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forRoot(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "FrontLayoutComponent", "AdminLayoutComponent", "AuthAdminLayoutComponent", "guardad<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "noguarduser<PERSON><PERSON>", "routes", "path", "component", "children", "loadChildren", "then", "m", "HomeModule", "ProfileModule", "canActivateChild", "MessagesModule", "PlanningsModule", "ReunionsModule", "NotificationsModule", "ChangePasswordModule", "SignupModule", "LoginModule", "VerifyEmailModule", "ResetPasswordModule", "ForgotPasswordModule", "ProjectsModule", "EquipesModule", "canActivate", "DashboardModule", "UserdetailsModule", "AppRoutingModule", "forRoot", "imports", "i1", "exports"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\app-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { RouterModule, Routes } from '@angular/router';\nimport { FrontLayoutComponent } from './layouts/front-layout/front-layout.component';\nimport { AdminLayoutComponent } from './layouts/admin-layout/admin-layout.component';\nimport { AuthAdminLayoutComponent } from './layouts/auth-admin-layout/auth-admin-layout.component';\nimport { guardadminGuard } from './views/guards/guardadmin.guard';\nimport { guarduserGuard } from './views/guards/guarduser.guard';\nimport { noguarduserGuard } from './views/guards/noguarduser.guard';\nconst routes: Routes = [\n  // Espace Front\n  {\n    path: '',\n    component: FrontLayoutComponent,\n    children: [\n      {\n        path: '',\n        loadChildren: () =>\n          import('./views/front/home/<USER>').then((m) => m.HomeModule),\n      },\n      {\n        path: 'profile',\n        loadChildren: () =>\n          import('./views/front/profile/profile.module').then(\n            (m) => m.ProfileModule\n          ),\n        canActivateChild: [guarduserGuard],\n      },\n      {\n        path: 'messages',\n        loadChildren: () =>\n          import('./views/front/messages/messages.module').then(\n            (m) => m.MessagesModule\n          ),\n        canActivateChild: [guarduserGuard],\n      },\n      {\n        path: 'plannings',\n        loadChildren: () =>\n          import('./views/front/plannings/plannings.module').then(\n            (m) => m.PlanningsModule\n          ),\n        canActivateChild: [guarduserGuard],\n      },\n      {\n        path: 'reunions',\n        loadChildren: () =>\n          import('./views/front/reunions/reunions.module').then(\n            (m) => m.ReunionsModule\n          ),\n        canActivateChild: [guarduserGuard],\n      },\n      {\n        path: 'notifications',\n        loadChildren: () =>\n          import('./views/front/notifications/notifications.module').then(\n            (m) => m.NotificationsModule\n          ),\n        canActivateChild: [guarduserGuard],\n      },\n      {\n        path: 'change-password',\n        loadChildren: () =>\n          import('./views/front/change-password/change-password.module').then(\n            (m) => m.ChangePasswordModule\n          ),\n        canActivateChild: [guarduserGuard],\n      },\n      {\n        path: 'signup',\n        loadChildren: () =>\n          import('./views/front/signup/signup.module').then(\n            (m) => m.SignupModule\n          ),\n        canActivateChild: [noguarduserGuard],\n      },\n      {\n        path: 'login',\n        loadChildren: () =>\n          import('./views/front/login/login.module').then((m) => m.LoginModule),\n        canActivateChild: [noguarduserGuard],\n      },\n      {\n        path: 'verify-email',\n        loadChildren: () =>\n          import('./views/front/verify-email/verify-email.module').then(\n            (m) => m.VerifyEmailModule\n          ),\n      },\n      {\n        path: 'reset-password',\n        loadChildren: () =>\n          import('./views/front/reset-password/reset-password.module').then(\n            (m) => m.ResetPasswordModule\n          ),\n        canActivateChild: [guarduserGuard],\n      },\n      {\n        path: 'forgot-password',\n        loadChildren: () =>\n          import('./views/front/forgot-password/forgot-password.module').then(\n            (m) => m.ForgotPasswordModule\n          ),\n      },\n      {\n        path: 'projects',\n        loadChildren: () =>\n          import('./views/front/projects/projects.module').then(\n            (m) => m.ProjectsModule\n          ),\n        canActivateChild: [guarduserGuard], // Protection pour utilisateurs authentifiés\n      },\n      {\n        path: 'equipes',\n        loadChildren: () =>\n          import('./views/front/equipes/equipes.module').then(\n            (m) => m.EquipesModule\n          ),\n        canActivateChild: [guarduserGuard], // Protection pour utilisateurs authentifiés\n      },\n    ],\n  },\n  //  Espace Admin\n  {\n    path: 'admin',\n    component: AdminLayoutComponent,\n    canActivate: [guardadminGuard],\n    children: [\n      {\n        path: '',\n        loadChildren: () =>\n          import('./views/admin/dashboard/dashboard.module').then(\n            (m) => m.DashboardModule\n          ),\n      },\n      {\n        path: 'dashboard',\n        loadChildren: () =>\n          import('./views/admin/dashboard/dashboard.module').then(\n            (m) => m.DashboardModule\n          ),\n      },\n      {\n        path: 'userdetails/:id',\n        loadChildren: () =>\n          import('./views/admin/userdetails/userdetails.module').then(\n            (m) => m.UserdetailsModule\n          ),\n      },\n      {\n        path: 'plannings',\n        loadChildren: () =>\n          import('./views/admin/plannings/plannings.module').then(\n            (m) => m.PlanningsModule\n          ),\n      },\n      {\n        path: 'reunions',\n        loadChildren: () =>\n          import('./views/admin/reunions/reunions.module').then(\n            (m) => m.ReunionsModule\n          ),\n      },\n      {\n        path: 'projects',\n        loadChildren: () =>\n          import('./views/admin/projects/projects.module').then(\n            (m) => m.ProjectsModule\n          ),\n        canActivateChild: [guarduserGuard],\n      },\n      {\n        path: 'profile',\n        loadChildren: () =>\n          import('./views/admin/profile/profile.module').then(\n            (m) => m.ProfileModule\n          ),\n      },\n      {\n           path: 'equipes',\n        loadChildren: () =>\n          import('./views/front/equipes/equipes.module').then(\n            (m) => m.EquipesModule\n          ),\n      },\n    ],\n  },\n  //  Espace Auth-admin\n  { path: 'admin/login', component: AuthAdminLayoutComponent },\n];\n\n@NgModule({\n  imports: [RouterModule.forRoot(routes)],\n  exports: [RouterModule],\n})\nexport class AppRoutingModule {}\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,oBAAoB,QAAQ,+CAA+C;AACpF,SAASC,oBAAoB,QAAQ,+CAA+C;AACpF,SAASC,wBAAwB,QAAQ,yDAAyD;AAClG,SAASC,eAAe,QAAQ,iCAAiC;AACjE,SAASC,cAAc,QAAQ,gCAAgC;AAC/D,SAASC,gBAAgB,QAAQ,kCAAkC;;;AACnE,MAAMC,MAAM,GAAW;AACrB;AACA;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAER,oBAAoB;EAC/BS,QAAQ,EAAE,CACR;IACEF,IAAI,EAAE,EAAE;IACRG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,gCAAgC,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACC,UAAU;GACpE,EACD;IACEN,IAAI,EAAE,SAAS;IACfG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,sCAAsC,CAAC,CAACC,IAAI,CAChDC,CAAC,IAAKA,CAAC,CAACE,aAAa,CACvB;IACHC,gBAAgB,EAAE,CAACX,cAAc;GAClC,EACD;IACEG,IAAI,EAAE,UAAU;IAChBG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,wCAAwC,CAAC,CAACC,IAAI,CAClDC,CAAC,IAAKA,CAAC,CAACI,cAAc,CACxB;IACHD,gBAAgB,EAAE,CAACX,cAAc;GAClC,EACD;IACEG,IAAI,EAAE,WAAW;IACjBG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,0CAA0C,CAAC,CAACC,IAAI,CACpDC,CAAC,IAAKA,CAAC,CAACK,eAAe,CACzB;IACHF,gBAAgB,EAAE,CAACX,cAAc;GAClC,EACD;IACEG,IAAI,EAAE,UAAU;IAChBG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,wCAAwC,CAAC,CAACC,IAAI,CAClDC,CAAC,IAAKA,CAAC,CAACM,cAAc,CACxB;IACHH,gBAAgB,EAAE,CAACX,cAAc;GAClC,EACD;IACEG,IAAI,EAAE,eAAe;IACrBG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,kDAAkD,CAAC,CAACC,IAAI,CAC5DC,CAAC,IAAKA,CAAC,CAACO,mBAAmB,CAC7B;IACHJ,gBAAgB,EAAE,CAACX,cAAc;GAClC,EACD;IACEG,IAAI,EAAE,iBAAiB;IACvBG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,sDAAsD,CAAC,CAACC,IAAI,CAChEC,CAAC,IAAKA,CAAC,CAACQ,oBAAoB,CAC9B;IACHL,gBAAgB,EAAE,CAACX,cAAc;GAClC,EACD;IACEG,IAAI,EAAE,QAAQ;IACdG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,oCAAoC,CAAC,CAACC,IAAI,CAC9CC,CAAC,IAAKA,CAAC,CAACS,YAAY,CACtB;IACHN,gBAAgB,EAAE,CAACV,gBAAgB;GACpC,EACD;IACEE,IAAI,EAAE,OAAO;IACbG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,kCAAkC,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACU,WAAW,CAAC;IACvEP,gBAAgB,EAAE,CAACV,gBAAgB;GACpC,EACD;IACEE,IAAI,EAAE,cAAc;IACpBG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,gDAAgD,CAAC,CAACC,IAAI,CAC1DC,CAAC,IAAKA,CAAC,CAACW,iBAAiB;GAE/B,EACD;IACEhB,IAAI,EAAE,gBAAgB;IACtBG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,oDAAoD,CAAC,CAACC,IAAI,CAC9DC,CAAC,IAAKA,CAAC,CAACY,mBAAmB,CAC7B;IACHT,gBAAgB,EAAE,CAACX,cAAc;GAClC,EACD;IACEG,IAAI,EAAE,iBAAiB;IACvBG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,sDAAsD,CAAC,CAACC,IAAI,CAChEC,CAAC,IAAKA,CAAC,CAACa,oBAAoB;GAElC,EACD;IACElB,IAAI,EAAE,UAAU;IAChBG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,wCAAwC,CAAC,CAACC,IAAI,CAClDC,CAAC,IAAKA,CAAC,CAACc,cAAc,CACxB;IACHX,gBAAgB,EAAE,CAACX,cAAc,CAAC,CAAE;GACrC,EACD;IACEG,IAAI,EAAE,SAAS;IACfG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,sCAAsC,CAAC,CAACC,IAAI,CAChDC,CAAC,IAAKA,CAAC,CAACe,aAAa,CACvB;IACHZ,gBAAgB,EAAE,CAACX,cAAc,CAAC,CAAE;GACrC;CAEJ;AACD;AACA;EACEG,IAAI,EAAE,OAAO;EACbC,SAAS,EAAEP,oBAAoB;EAC/B2B,WAAW,EAAE,CAACzB,eAAe,CAAC;EAC9BM,QAAQ,EAAE,CACR;IACEF,IAAI,EAAE,EAAE;IACRG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,0CAA0C,CAAC,CAACC,IAAI,CACpDC,CAAC,IAAKA,CAAC,CAACiB,eAAe;GAE7B,EACD;IACEtB,IAAI,EAAE,WAAW;IACjBG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,0CAA0C,CAAC,CAACC,IAAI,CACpDC,CAAC,IAAKA,CAAC,CAACiB,eAAe;GAE7B,EACD;IACEtB,IAAI,EAAE,iBAAiB;IACvBG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,8CAA8C,CAAC,CAACC,IAAI,CACxDC,CAAC,IAAKA,CAAC,CAACkB,iBAAiB;GAE/B,EACD;IACEvB,IAAI,EAAE,WAAW;IACjBG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,0CAA0C,CAAC,CAACC,IAAI,CACpDC,CAAC,IAAKA,CAAC,CAACK,eAAe;GAE7B,EACD;IACEV,IAAI,EAAE,UAAU;IAChBG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,wCAAwC,CAAC,CAACC,IAAI,CAClDC,CAAC,IAAKA,CAAC,CAACM,cAAc;GAE5B,EACD;IACEX,IAAI,EAAE,UAAU;IAChBG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,wCAAwC,CAAC,CAACC,IAAI,CAClDC,CAAC,IAAKA,CAAC,CAACc,cAAc,CACxB;IACHX,gBAAgB,EAAE,CAACX,cAAc;GAClC,EACD;IACEG,IAAI,EAAE,SAAS;IACfG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,sCAAsC,CAAC,CAACC,IAAI,CAChDC,CAAC,IAAKA,CAAC,CAACE,aAAa;GAE3B,EACD;IACKP,IAAI,EAAE,SAAS;IAClBG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,sCAAsC,CAAC,CAACC,IAAI,CAChDC,CAAC,IAAKA,CAAC,CAACe,aAAa;GAE3B;CAEJ;AACD;AACA;EAAEpB,IAAI,EAAE,aAAa;EAAEC,SAAS,EAAEN;AAAwB,CAAE,CAC7D;AAMD,OAAM,MAAO6B,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA;IAAgB;EAAA;;;gBAHjBhC,YAAY,CAACiC,OAAO,CAAC1B,MAAM,CAAC,EAC5BP,YAAY;IAAA;EAAA;;;2EAEXgC,gBAAgB;IAAAE,OAAA,GAAAC,EAAA,CAAAnC,YAAA;IAAAoC,OAAA,GAFjBpC,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}