{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/equipe.service\";\nimport * as i2 from \"src/app/services/membre.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nfunction EquipeComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 41)(1, \"div\", 42)(2, \"div\", 43)(3, \"div\", 44)(4, \"div\", 45);\n    i0.ɵɵelement(5, \"i\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 47)(7, \"h3\", 48);\n    i0.ɵɵtext(8, \" Erreur \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\", 49);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function EquipeComponent_div_18_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.error = \"\");\n    });\n    i0.ɵɵelement(12, \"i\", 51);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.error, \" \");\n  }\n}\nfunction EquipeComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 52)(1, \"div\", 53);\n    i0.ɵɵelement(2, \"div\", 54)(3, \"div\", 55);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 56);\n    i0.ɵɵtext(5, \" Chargement... \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction EquipeComponent_div_20_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 70)(1, \"div\", 71);\n    i0.ɵɵelement(2, \"i\", 72);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\", 73);\n    i0.ɵɵtext(4, \" Aucune \\u00E9quipe trouv\\u00E9e \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 65);\n    i0.ɵɵtext(6, \" Cr\\u00E9ez votre premi\\u00E8re \\u00E9quipe ci-dessous \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction EquipeComponent_div_20_div_15_tr_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 82)(1, \"td\", 83)(2, \"div\", 84);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"td\", 83)(5, \"div\", 85);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"td\", 83)(8, \"div\", 49);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"td\", 83)(11, \"span\", 86);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"td\", 83)(14, \"div\", 87)(15, \"button\", 88);\n    i0.ɵɵlistener(\"click\", function EquipeComponent_div_20_div_15_tr_16_Template_button_click_15_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r15);\n      const equipe_r13 = restoredCtx.$implicit;\n      const ctx_r14 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r14.editEquipe(equipe_r13));\n    });\n    i0.ɵɵelement(16, \"i\", 89);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"button\", 90);\n    i0.ɵɵlistener(\"click\", function EquipeComponent_div_20_div_15_tr_16_Template_button_click_17_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r15);\n      const equipe_r13 = restoredCtx.$implicit;\n      const ctx_r16 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r16.showMembreModal(equipe_r13));\n    });\n    i0.ɵɵelement(18, \"i\", 91);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"button\", 92);\n    i0.ɵɵlistener(\"click\", function EquipeComponent_div_20_div_15_tr_16_Template_button_click_19_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r15);\n      const equipe_r13 = restoredCtx.$implicit;\n      const ctx_r17 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(equipe_r13._id && ctx_r17.deleteEquipe(equipe_r13._id));\n    });\n    i0.ɵɵelement(20, \"i\", 93);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const equipe_r13 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", equipe_r13.name, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", equipe_r13.description || \"Aucune description\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", equipe_r13.admin || \"Non d\\u00E9fini\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", (equipe_r13.members == null ? null : equipe_r13.members.length) || 0, \" membre(s) \");\n  }\n}\nfunction EquipeComponent_div_20_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 74)(1, \"div\", 75)(2, \"table\", 76)(3, \"thead\", 77)(4, \"tr\")(5, \"th\", 78);\n    i0.ɵɵtext(6, \" Nom \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"th\", 78);\n    i0.ɵɵtext(8, \" Description \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\", 78);\n    i0.ɵɵtext(10, \" Admin \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"th\", 78);\n    i0.ɵɵtext(12, \" Membres \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"th\", 79);\n    i0.ɵɵtext(14, \" Actions \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"tbody\", 80);\n    i0.ɵɵtemplate(16, EquipeComponent_div_20_div_15_tr_16_Template, 21, 4, \"tr\", 81);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(16);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r11.equipes);\n  }\n}\nfunction EquipeComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 57)(1, \"div\", 58);\n    i0.ɵɵelement(2, \"div\", 59)(3, \"div\", 60);\n    i0.ɵɵelementStart(4, \"div\", 61)(5, \"div\", 62)(6, \"div\", 63)(7, \"h2\", 64);\n    i0.ɵɵtext(8, \" Liste des \\u00E9quipes \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\", 65);\n    i0.ɵɵtext(10, \" G\\u00E9rez toutes vos \\u00E9quipes depuis cette interface \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"button\", 66);\n    i0.ɵɵlistener(\"click\", function EquipeComponent_div_20_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.loadEquipes());\n    });\n    i0.ɵɵelement(12, \"i\", 67);\n    i0.ɵɵtext(13, \" Rafra\\u00EEchir \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(14, EquipeComponent_div_20_div_14_Template, 7, 0, \"div\", 68);\n    i0.ɵɵtemplate(15, EquipeComponent_div_20_div_15_Template, 17, 1, \"div\", 69);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(14);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.equipes.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.equipes.length > 0);\n  }\n}\nfunction EquipeComponent_span_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 94);\n  }\n}\nfunction EquipeComponent_button_49_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 95);\n    i0.ɵɵlistener(\"click\", function EquipeComponent_button_49_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.cancelEdit());\n    });\n    i0.ɵɵtext(1, \" Annuler \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipeComponent_div_58_div_6_li_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 105)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 106);\n    i0.ɵɵlistener(\"click\", function EquipeComponent_div_58_div_6_li_2_Template_button_click_3_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r29);\n      const membreId_r27 = restoredCtx.$implicit;\n      const ctx_r28 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r28.removeMembreFromEquipe(ctx_r28.selectedEquipe._id, membreId_r27));\n    });\n    i0.ɵɵtext(4, \" Retirer \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const membreId_r27 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(membreId_r27);\n  }\n}\nfunction EquipeComponent_div_58_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"ul\", 103);\n    i0.ɵɵtemplate(2, EquipeComponent_div_58_div_6_li_2_Template, 5, 1, \"li\", 104);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r22 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r22.selectedEquipe.members);\n  }\n}\nfunction EquipeComponent_div_58_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 26);\n    i0.ɵɵtext(1, \"Aucun membre dans cette \\u00E9quipe\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipeComponent_div_58_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r31 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"h6\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 17)(4, \"h6\");\n    i0.ɵɵtext(5, \"Membres actuels:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, EquipeComponent_div_58_div_6_Template, 3, 1, \"div\", 96);\n    i0.ɵɵtemplate(7, EquipeComponent_div_58_ng_template_7_Template, 2, 0, \"ng-template\", null, 97, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 17)(10, \"h6\");\n    i0.ɵɵtext(11, \"Ajouter un membre:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 98);\n    i0.ɵɵelement(13, \"input\", 99, 100);\n    i0.ɵɵelementStart(15, \"button\", 101);\n    i0.ɵɵlistener(\"click\", function EquipeComponent_div_58_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r31);\n      const _r25 = i0.ɵɵreference(14);\n      const ctx_r30 = i0.ɵɵnextContext();\n      ctx_r30.addMembreToEquipe(ctx_r30.selectedEquipe._id, _r25.value);\n      return i0.ɵɵresetView(_r25.value = \"\");\n    });\n    i0.ɵɵtext(16, \" Ajouter \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"small\", 26);\n    i0.ɵɵtext(18, \"Entrez l'ID du membre \\u00E0 ajouter \\u00E0 l'\\u00E9quipe\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 102)(20, \"p\", 15)(21, \"strong\");\n    i0.ɵɵtext(22, \"Note:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(23, \" Pour ajouter un membre, vous devez d'abord cr\\u00E9er le membre dans la section des membres. \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const _r23 = i0.ɵɵreference(8);\n    const _r25 = i0.ɵɵreference(14);\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u00C9quipe: \", ctx_r7.selectedEquipe.name, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.selectedEquipe.members && ctx_r7.selectedEquipe.members.length > 0)(\"ngIfElse\", _r23);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"disabled\", !ctx_r7.selectedEquipe || !ctx_r7.selectedEquipe._id || !_r25.value);\n  }\n}\nexport class EquipeComponent {\n  constructor(equipeService, membreService) {\n    this.equipeService = equipeService;\n    this.membreService = membreService;\n    this.equipes = [];\n    this.newEquipe = {\n      name: '',\n      description: ''\n    };\n    this.selectedEquipe = null;\n    this.isEditing = false;\n    this.membres = [];\n    this.loading = false;\n    this.error = '';\n  }\n  ngOnInit() {\n    this.loadEquipes();\n    this.loadMembres();\n  }\n  loadEquipes() {\n    this.loading = true;\n    this.equipeService.getEquipes().subscribe({\n      next: data => {\n        console.log('Loaded equipes:', data);\n        this.equipes = data;\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Error loading equipes:', error);\n        this.error = 'Erreur lors du chargement des équipes: ' + error.message;\n        this.loading = false;\n      }\n    });\n  }\n  loadMembres() {\n    this.loading = true;\n    this.membreService.getMembres().subscribe({\n      next: data => {\n        console.log('Loaded membres:', data);\n        this.membres = data;\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Error loading membres:', error);\n        this.error = 'Erreur lors du chargement des membres: ' + error.message;\n        this.loading = false;\n      }\n    });\n  }\n  addEquipe() {\n    console.log('Adding equipe:', this.newEquipe);\n    if (!this.newEquipe.name) {\n      console.error('Team name is required');\n      this.error = 'Le nom de l\\'équipe est requis';\n      return;\n    }\n    this.loading = true;\n    this.error = '';\n    this.equipeService.addEquipe(this.newEquipe).subscribe({\n      next: response => {\n        console.log('Equipe added successfully:', response);\n        this.loadEquipes();\n        this.newEquipe = {\n          name: '',\n          description: ''\n        }; // Clear input\n        this.loading = false;\n        // Afficher un message de succès temporaire\n        const successMessage = 'Équipe créée avec succès!';\n        this.error = ''; // Effacer les erreurs précédentes\n        alert(successMessage);\n      },\n      error: error => {\n        console.error('Error adding equipe:', error);\n        this.error = 'Erreur lors de la création de l\\'équipe: ' + (error.error?.message || error.message || 'Unknown error');\n        this.loading = false;\n      }\n    });\n  }\n  editEquipe(equipe) {\n    this.isEditing = true;\n    // Créer une copie profonde pour éviter de modifier l'objet original\n    this.newEquipe = {\n      _id: equipe._id,\n      name: equipe.name || '',\n      description: equipe.description || '',\n      admin: equipe.admin,\n      members: equipe.members ? [...equipe.members] : []\n    };\n  }\n  cancelEdit() {\n    this.isEditing = false;\n    this.newEquipe = {\n      name: '',\n      description: ''\n    };\n    this.error = ''; // Effacer les erreurs\n  }\n\n  updateSelectedEquipe() {\n    if (!this.newEquipe.name) {\n      console.error('Team name is required');\n      this.error = 'Le nom de l\\'équipe est requis';\n      return;\n    }\n    if (this.newEquipe._id) {\n      this.loading = true;\n      this.error = '';\n      this.equipeService.updateEquipe(this.newEquipe._id, this.newEquipe).subscribe({\n        next: updatedEquipe => {\n          console.log('Team updated successfully:', updatedEquipe);\n          this.loadEquipes();\n          this.isEditing = false;\n          this.newEquipe = {\n            name: '',\n            description: ''\n          };\n          this.loading = false;\n          // Afficher un message de succès temporaire\n          const successMessage = 'Équipe mise à jour avec succès!';\n          alert(successMessage);\n        },\n        error: error => {\n          console.error('Error updating team:', error);\n          this.error = 'Erreur lors de la mise à jour de l\\'équipe: ' + (error.error?.message || error.message || 'Unknown error');\n          this.loading = false;\n        }\n      });\n    } else {\n      this.error = 'ID de l\\'équipe manquant pour la mise à jour';\n    }\n  }\n  deleteEquipe(id) {\n    if (!id) {\n      console.error('ID is undefined');\n      this.error = 'ID de l\\'équipe non défini';\n      return;\n    }\n    if (confirm('Êtes-vous sûr de vouloir supprimer cette équipe? Cette action est irréversible.')) {\n      this.loading = true;\n      this.error = '';\n      this.equipeService.deleteEquipe(id).subscribe({\n        next: response => {\n          console.log('Team deleted successfully:', response);\n          // Si l'équipe en cours d'édition est celle qui vient d'être supprimée, réinitialiser le formulaire\n          if (this.isEditing && this.newEquipe._id === id) {\n            this.isEditing = false;\n            this.newEquipe = {\n              name: '',\n              description: ''\n            };\n          }\n          this.loadEquipes();\n          this.loading = false;\n          // Afficher un message de succès\n          alert('Équipe supprimée avec succès');\n        },\n        error: error => {\n          console.error('Error deleting team:', error);\n          this.error = 'Erreur lors de la suppression de l\\'équipe: ' + (error.error?.message || error.message || 'Unknown error');\n          this.loading = false;\n        }\n      });\n    }\n  }\n  showMembreModal(equipe) {\n    this.selectedEquipe = equipe;\n    // Ouvrir le modal avec Bootstrap 5\n    const modalRef = document.getElementById('membreModal');\n    if (modalRef) {\n      try {\n        // Ensure Bootstrap is properly loaded\n        if (typeof window !== 'undefined' && window.bootstrap) {\n          const modal = new window.bootstrap.Modal(modalRef);\n          modal.show();\n        } else {\n          console.error('Bootstrap is not loaded properly');\n          alert('Erreur: Bootstrap n\\'est pas chargé correctement');\n        }\n      } catch (error) {\n        console.error('Error showing modal:', error);\n      }\n    } else {\n      console.error('Modal element not found');\n    }\n  }\n  addMembreToEquipe(teamId, membreId) {\n    if (!teamId) {\n      console.error('Team ID is undefined');\n      alert('ID de l\\'équipe non défini');\n      return;\n    }\n    if (!membreId || membreId.trim() === '') {\n      console.error('Member ID is empty');\n      alert('L\\'ID du membre est requis');\n      return;\n    }\n    this.loading = true;\n    // Create a proper Membre object that matches what the API expects\n    const membre = {\n      id: membreId\n    };\n    this.equipeService.addMembreToEquipe(teamId, membre).subscribe({\n      next: response => {\n        console.log('Member added successfully:', response);\n        this.loadEquipes();\n        this.loading = false;\n        // Afficher un message de succès\n        alert('Membre ajouté avec succès à l\\'équipe');\n      },\n      error: error => {\n        console.error('Error adding member:', error);\n        this.error = 'Erreur lors de l\\'ajout du membre: ' + (error.error?.message || error.message || 'Unknown error');\n        alert(this.error);\n        this.loading = false;\n      }\n    });\n  }\n  removeMembreFromEquipe(teamId, membreId) {\n    if (!teamId) {\n      console.error('Team ID is undefined');\n      alert('ID de l\\'équipe non défini');\n      return;\n    }\n    if (!membreId) {\n      console.error('Member ID is undefined');\n      alert('ID du membre non défini');\n      return;\n    }\n    if (confirm('Êtes-vous sûr de vouloir retirer ce membre de l\\'équipe?')) {\n      this.loading = true;\n      this.equipeService.removeMembreFromEquipe(teamId, membreId).subscribe({\n        next: response => {\n          console.log('Member removed successfully:', response);\n          this.loadEquipes();\n          this.loading = false;\n          // Si l'équipe sélectionnée est celle dont on vient de retirer un membre, mettre à jour l'équipe sélectionnée\n          if (this.selectedEquipe && this.selectedEquipe._id === teamId) {\n            const updatedEquipe = this.equipes.find(e => e._id === teamId);\n            if (updatedEquipe) {\n              this.selectedEquipe = updatedEquipe;\n            }\n          }\n        },\n        error: error => {\n          console.error('Error removing member:', error);\n          this.error = 'Erreur lors de la suppression du membre: ' + (error.error?.message || error.message || 'Unknown error');\n          alert(this.error);\n          this.loading = false;\n        }\n      });\n    }\n  }\n  static {\n    this.ɵfac = function EquipeComponent_Factory(t) {\n      return new (t || EquipeComponent)(i0.ɵɵdirectiveInject(i1.EquipeService), i0.ɵɵdirectiveInject(i2.MembreService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: EquipeComponent,\n      selectors: [[\"app-equipe\"]],\n      decls: 62,\n      vars: 13,\n      consts: [[1, \"min-h-screen\", \"bg-[#f0f4f8]\", \"dark:bg-[#0a0a0a]\", \"relative\", \"overflow-hidden\"], [1, \"absolute\", \"inset-0\", \"overflow-hidden\", \"pointer-events-none\"], [1, \"absolute\", \"top-[15%]\", \"left-[10%]\", \"w-64\", \"h-64\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#dac4ea]/5\", \"to-transparent\", \"dark:from-[#00f7ff]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"bottom-[20%]\", \"right-[10%]\", \"w-80\", \"h-80\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#dac4ea]/5\", \"to-transparent\", \"dark:from-[#00f7ff]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"inset-0\", \"opacity-5\", \"dark:opacity-[0.03]\"], [1, \"h-full\", \"grid\", \"grid-cols-12\"], [1, \"border-r\", \"border-[#dac4ea]\", \"dark:border-[#00f7ff]\"], [1, \"max-w-7xl\", \"mx-auto\", \"p-6\", \"relative\", \"z-10\"], [\"class\", \"mb-6\", 4, \"ngIf\"], [\"class\", \"flex flex-col items-center justify-center py-16\", 4, \"ngIf\"], [\"class\", \"mb-8\", 4, \"ngIf\"], [1, \"row\", \"mb-4\"], [1, \"col-12\"], [1, \"card\"], [1, \"card-header\", \"bg-primary\", \"text-white\"], [1, \"mb-0\"], [1, \"card-body\"], [1, \"mb-3\"], [\"for\", \"name\", 1, \"form-label\"], [1, \"text-danger\"], [\"type\", \"text\", \"id\", \"name\", \"required\", \"\", \"placeholder\", \"Entrez le nom de l'\\u00E9quipe\", 1, \"form-control\", 3, \"value\", \"input\"], [\"nameInput\", \"\"], [1, \"invalid-feedback\"], [\"for\", \"description\", 1, \"form-label\"], [\"id\", \"description\", \"rows\", \"3\", \"placeholder\", \"Entrez une description pour cette \\u00E9quipe\", 1, \"form-control\", 3, \"value\", \"input\"], [\"descInput\", \"\"], [1, \"text-muted\"], [1, \"d-flex\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", 3, \"disabled\", \"click\"], [\"class\", \"spinner-border spinner-border-sm me-1\", \"role\", \"status\", \"aria-hidden\", \"true\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"btn btn-secondary ms-2\", 3, \"click\", 4, \"ngIf\"], [\"id\", \"membreModal\", \"tabindex\", \"-1\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [1, \"modal-dialog\"], [1, \"modal-content\"], [1, \"modal-header\"], [1, \"modal-title\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", \"aria-label\", \"Close\", 1, \"btn-close\"], [1, \"modal-body\"], [4, \"ngIf\"], [1, \"modal-footer\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", 1, \"btn\", \"btn-secondary\"], [1, \"mb-6\"], [1, \"bg-[#ff6b69]/10\", \"dark:bg-[#ff3b30]/10\", \"border-l-4\", \"border-[#ff6b69]\", \"dark:border-[#ff3b30]\", \"rounded-lg\", \"p-4\", \"backdrop-blur-sm\"], [1, \"flex\", \"items-start\", \"justify-between\"], [1, \"flex\", \"items-start\"], [1, \"text-[#ff6b69]\", \"dark:text-[#ff3b30]\", \"mr-3\", \"text-xl\"], [1, \"fas\", \"fa-exclamation-triangle\"], [1, \"flex-1\"], [1, \"font-semibold\", \"text-[#ff6b69]\", \"dark:text-[#ff3b30]\", \"mb-1\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [1, \"text-[#ff6b69]\", \"dark:text-[#ff3b30]\", \"hover:bg-[#ff6b69]/10\", \"dark:hover:bg-[#ff3b30]/10\", \"p-1\", \"rounded\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"flex\", \"flex-col\", \"items-center\", \"justify-center\", \"py-16\"], [1, \"relative\"], [1, \"w-12\", \"h-12\", \"border-3\", \"border-[#dac4ea]/20\", \"dark:border-[#00f7ff]/20\", \"border-t-[#dac4ea]\", \"dark:border-t-[#00f7ff]\", \"rounded-full\", \"animate-spin\"], [1, \"absolute\", \"inset-0\", \"bg-[#dac4ea]/20\", \"dark:bg-[#00f7ff]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"mt-4\", \"text-[#dac4ea]\", \"dark:text-[#00f7ff]\", \"text-sm\", \"font-medium\", \"tracking-wide\"], [1, \"mb-8\"], [1, \"mb-6\", \"relative\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#dac4ea]\", \"to-[#8b5a9f]\", \"dark:from-[#00f7ff]\", \"dark:to-[#dac4ea]\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#dac4ea]\", \"to-[#8b5a9f]\", \"dark:from-[#00f7ff]\", \"dark:to-[#dac4ea]\", \"blur-md\"], [1, \"bg-white\", \"dark:bg-[#1a1a1a]\", \"rounded-xl\", \"shadow-lg\", \"dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)]\", \"p-6\", \"backdrop-blur-sm\", \"border\", \"border-[#dac4ea]/20\", \"dark:border-[#00f7ff]/20\"], [1, \"flex\", \"flex-col\", \"lg:flex-row\", \"lg:items-center\", \"lg:justify-between\"], [1, \"mb-4\", \"lg:mb-0\"], [1, \"text-2xl\", \"font-bold\", \"text-[#dac4ea]\", \"dark:text-[#00f7ff]\", \"mb-2\", \"tracking-wide\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"text-sm\"], [1, \"bg-[#dac4ea]/20\", \"dark:bg-[#00f7ff]/20\", \"text-[#dac4ea]\", \"dark:text-[#00f7ff]\", \"px-4\", \"py-2\", \"rounded-lg\", \"font-medium\", \"transition-all\", \"duration-300\", \"hover:scale-105\", \"hover:bg-[#dac4ea]/30\", \"dark:hover:bg-[#00f7ff]/30\", 3, \"click\"], [1, \"fas\", \"fa-sync-alt\", \"mr-2\"], [\"class\", \"text-center py-12\", 4, \"ngIf\"], [\"class\", \"bg-white dark:bg-[#1a1a1a] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] overflow-hidden border border-[#dac4ea]/20 dark:border-[#00f7ff]/20\", 4, \"ngIf\"], [1, \"text-center\", \"py-12\"], [1, \"w-16\", \"h-16\", \"mx-auto\", \"mb-4\", \"text-[#dac4ea]\", \"dark:text-[#00f7ff]\", \"opacity-70\"], [1, \"fas\", \"fa-users\", \"text-4xl\"], [1, \"text-lg\", \"font-semibold\", \"text-[#dac4ea]\", \"dark:text-[#00f7ff]\", \"mb-2\"], [1, \"bg-white\", \"dark:bg-[#1a1a1a]\", \"rounded-xl\", \"shadow-lg\", \"dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)]\", \"overflow-hidden\", \"border\", \"border-[#dac4ea]/20\", \"dark:border-[#00f7ff]/20\"], [1, \"overflow-x-auto\"], [1, \"w-full\"], [1, \"bg-[#dac4ea]/10\", \"dark:bg-[#00f7ff]/10\"], [1, \"px-6\", \"py-4\", \"text-left\", \"text-sm\", \"font-semibold\", \"text-[#dac4ea]\", \"dark:text-[#00f7ff]\"], [1, \"px-6\", \"py-4\", \"text-center\", \"text-sm\", \"font-semibold\", \"text-[#dac4ea]\", \"dark:text-[#00f7ff]\"], [1, \"divide-y\", \"divide-[#dac4ea]/10\", \"dark:divide-[#00f7ff]/10\"], [\"class\", \"hover:bg-[#dac4ea]/5 dark:hover:bg-[#00f7ff]/5 transition-colors\", 4, \"ngFor\", \"ngForOf\"], [1, \"hover:bg-[#dac4ea]/5\", \"dark:hover:bg-[#00f7ff]/5\", \"transition-colors\"], [1, \"px-6\", \"py-4\"], [1, \"font-medium\", \"text-[#dac4ea]\", \"dark:text-[#00f7ff]\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"max-w-xs\", \"truncate\"], [1, \"bg-[#dac4ea]/10\", \"dark:bg-[#00f7ff]/10\", \"text-[#dac4ea]\", \"dark:text-[#00f7ff]\", \"px-2\", \"py-1\", \"rounded-full\", \"text-xs\", \"font-medium\"], [1, \"flex\", \"items-center\", \"justify-center\", \"space-x-2\"], [\"title\", \"Modifier l'\\u00E9quipe\", 1, \"p-2\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:text-[#dac4ea]\", \"dark:hover:text-[#00f7ff]\", \"hover:bg-[#dac4ea]/10\", \"dark:hover:bg-[#00f7ff]/10\", \"rounded-lg\", \"transition-all\", 3, \"click\"], [1, \"fas\", \"fa-edit\"], [\"title\", \"G\\u00E9rer les membres\", 1, \"p-2\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:text-[#00ff9d]\", \"hover:bg-[#00ff9d]/10\", \"rounded-lg\", \"transition-all\", 3, \"click\"], [1, \"fas\", \"fa-users\"], [\"title\", \"Supprimer l'\\u00E9quipe\", 1, \"p-2\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:text-[#ff6b69]\", \"dark:hover:text-[#ff3b30]\", \"hover:bg-[#ff6b69]/10\", \"dark:hover:bg-[#ff3b30]/10\", \"rounded-lg\", \"transition-all\", 3, \"click\"], [1, \"fas\", \"fa-trash\"], [\"role\", \"status\", \"aria-hidden\", \"true\", 1, \"spinner-border\", \"spinner-border-sm\", \"me-1\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", \"ms-2\", 3, \"click\"], [4, \"ngIf\", \"ngIfElse\"], [\"noMembers\", \"\"], [1, \"input-group\", \"mb-2\"], [\"type\", \"text\", \"placeholder\", \"ID du membre\", 1, \"form-control\"], [\"membreIdInput\", \"\"], [1, \"btn\", \"btn-primary\", 3, \"disabled\", \"click\"], [1, \"alert\", \"alert-info\", \"mt-3\"], [1, \"list-group\"], [\"class\", \"list-group-item d-flex justify-content-between align-items-center\", 4, \"ngFor\", \"ngForOf\"], [1, \"list-group-item\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"btn\", \"btn-sm\", \"btn-danger\", 3, \"click\"]],\n      template: function EquipeComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r32 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵelement(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5);\n          i0.ɵɵelement(6, \"div\", 6)(7, \"div\", 6)(8, \"div\", 6)(9, \"div\", 6)(10, \"div\", 6)(11, \"div\", 6)(12, \"div\", 6)(13, \"div\", 6)(14, \"div\", 6)(15, \"div\", 6)(16, \"div\", 6);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(17, \"div\", 7);\n          i0.ɵɵtemplate(18, EquipeComponent_div_18_Template, 13, 1, \"div\", 8);\n          i0.ɵɵtemplate(19, EquipeComponent_div_19_Template, 6, 0, \"div\", 9);\n          i0.ɵɵtemplate(20, EquipeComponent_div_20_Template, 16, 2, \"div\", 10);\n          i0.ɵɵelementStart(21, \"div\", 11)(22, \"div\", 12)(23, \"div\", 13)(24, \"div\", 14)(25, \"h3\", 15);\n          i0.ɵɵtext(26);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(27, \"div\", 16)(28, \"form\")(29, \"div\", 17)(30, \"label\", 18);\n          i0.ɵɵtext(31, \"Nom de l'\\u00E9quipe \");\n          i0.ɵɵelementStart(32, \"span\", 19);\n          i0.ɵɵtext(33, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(34, \"input\", 20, 21);\n          i0.ɵɵlistener(\"input\", function EquipeComponent_Template_input_input_34_listener() {\n            i0.ɵɵrestoreView(_r32);\n            const _r3 = i0.ɵɵreference(35);\n            return i0.ɵɵresetView(ctx.newEquipe.name = _r3.value);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"div\", 22);\n          i0.ɵɵtext(37, \" Le nom de l'\\u00E9quipe est requis \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(38, \"div\", 17)(39, \"label\", 23);\n          i0.ɵɵtext(40, \"Description\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"textarea\", 24, 25);\n          i0.ɵɵlistener(\"input\", function EquipeComponent_Template_textarea_input_41_listener() {\n            i0.ɵɵrestoreView(_r32);\n            const _r4 = i0.ɵɵreference(42);\n            return i0.ɵɵresetView(ctx.newEquipe.description = _r4.value);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"small\", 26);\n          i0.ɵɵtext(44, \"Une br\\u00E8ve description de l'\\u00E9quipe et de son objectif\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(45, \"div\", 27)(46, \"button\", 28);\n          i0.ɵɵlistener(\"click\", function EquipeComponent_Template_button_click_46_listener() {\n            return ctx.isEditing ? ctx.updateSelectedEquipe() : ctx.addEquipe();\n          });\n          i0.ɵɵtemplate(47, EquipeComponent_span_47_Template, 1, 0, \"span\", 29);\n          i0.ɵɵtext(48);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(49, EquipeComponent_button_49_Template, 2, 0, \"button\", 30);\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(50, \"div\", 31)(51, \"div\", 32)(52, \"div\", 33)(53, \"div\", 34)(54, \"h5\", 35);\n          i0.ɵɵtext(55, \"G\\u00E9rer les membres de l'\\u00E9quipe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(56, \"button\", 36);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(57, \"div\", 37);\n          i0.ɵɵtemplate(58, EquipeComponent_div_58_Template, 24, 4, \"div\", 38);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(59, \"div\", 39)(60, \"button\", 40);\n          i0.ɵɵtext(61, \" Fermer \");\n          i0.ɵɵelementEnd()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(18);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate1(\" \", ctx.isEditing ? \"Modifier une \\u00E9quipe\" : \"Cr\\u00E9er une \\u00E9quipe\", \" \");\n          i0.ɵɵadvance(8);\n          i0.ɵɵclassProp(\"is-invalid\", !ctx.newEquipe.name && (ctx.isEditing || ctx.newEquipe.name === \"\"));\n          i0.ɵɵproperty(\"value\", ctx.newEquipe.name);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"value\", ctx.newEquipe.description || \"\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"disabled\", !ctx.newEquipe.name || ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \", ctx.isEditing ? \"Mettre \\u00E0 jour\" : \"Cr\\u00E9er\", \" \");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isEditing);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedEquipe);\n        }\n      },\n      dependencies: [i3.NgForOf, i3.NgIf, i4.ɵNgNoValidate, i4.NgControlStatusGroup, i4.NgForm],\n      styles: [\"\\n\\n.container[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n}\\n\\n.card[_ngcontent-%COMP%] {\\n  border: none;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n  border-radius: 8px;\\n}\\n\\n.card-header[_ngcontent-%COMP%] {\\n  border-radius: 8px 8px 0 0 !important;\\n}\\n\\n.table[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n}\\n\\n.table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  border-top: none;\\n  font-weight: 600;\\n  color: #495057;\\n}\\n\\n.btn-sm[_ngcontent-%COMP%] {\\n  padding: 0.25rem 0.5rem;\\n  font-size: 0.875rem;\\n}\\n\\n.spinner-border-sm[_ngcontent-%COMP%] {\\n  width: 1rem;\\n  height: 1rem;\\n}\\n\\n.alert[_ngcontent-%COMP%] {\\n  border: none;\\n  border-radius: 6px;\\n}\\n\\n.modal-content[_ngcontent-%COMP%] {\\n  border: none;\\n  border-radius: 8px;\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);\\n}\\n\\n.list-group-item[_ngcontent-%COMP%] {\\n  border: 1px solid #e9ecef;\\n  border-radius: 4px;\\n  margin-bottom: 0.5rem;\\n}\\n\\n.list-group-item[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n\\n.form-control[_ngcontent-%COMP%]:focus {\\n  border-color: #80bdff;\\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\\n}\\n\\n.btn[_ngcontent-%COMP%]:focus {\\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\\n}\\n\\n.text-muted[_ngcontent-%COMP%] {\\n  color: #6c757d !important;\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "EquipeComponent_div_18_Template_button_click_11_listener", "ɵɵrestoreView", "_r9", "ctx_r8", "ɵɵnextContext", "ɵɵresetView", "error", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "EquipeComponent_div_20_div_15_tr_16_Template_button_click_15_listener", "restoredCtx", "_r15", "equipe_r13", "$implicit", "ctx_r14", "editEquipe", "EquipeComponent_div_20_div_15_tr_16_Template_button_click_17_listener", "ctx_r16", "showMembreModal", "EquipeComponent_div_20_div_15_tr_16_Template_button_click_19_listener", "ctx_r17", "_id", "deleteEquipe", "name", "description", "admin", "members", "length", "ɵɵtemplate", "EquipeComponent_div_20_div_15_tr_16_Template", "ɵɵproperty", "ctx_r11", "equipes", "EquipeComponent_div_20_Template_button_click_11_listener", "_r19", "ctx_r18", "loadEquipes", "EquipeComponent_div_20_div_14_Template", "EquipeComponent_div_20_div_15_Template", "ctx_r2", "EquipeComponent_button_49_Template_button_click_0_listener", "_r21", "ctx_r20", "cancelEdit", "EquipeComponent_div_58_div_6_li_2_Template_button_click_3_listener", "_r29", "membreId_r27", "ctx_r28", "removeMembreFromEquipe", "selectedEquipe", "ɵɵtextInterpolate", "EquipeComponent_div_58_div_6_li_2_Template", "ctx_r22", "EquipeComponent_div_58_div_6_Template", "EquipeComponent_div_58_ng_template_7_Template", "ɵɵtemplateRefExtractor", "EquipeComponent_div_58_Template_button_click_15_listener", "_r31", "_r25", "ɵɵreference", "ctx_r30", "addMembreToEquipe", "value", "ctx_r7", "_r23", "EquipeComponent", "constructor", "equipeService", "membreService", "newEquipe", "isEditing", "membres", "loading", "ngOnInit", "loadMembres", "getEquipes", "subscribe", "next", "data", "console", "log", "message", "getMembres", "addEquipe", "response", "successMessage", "alert", "equipe", "updateSelectedEquipe", "updateEquipe", "updatedEquipe", "id", "confirm", "modalRef", "document", "getElementById", "window", "bootstrap", "modal", "Modal", "show", "teamId", "membreId", "trim", "membre", "find", "e", "ɵɵdirectiveInject", "i1", "EquipeService", "i2", "MembreService", "selectors", "decls", "vars", "consts", "template", "EquipeComponent_Template", "rf", "ctx", "EquipeComponent_div_18_Template", "EquipeComponent_div_19_Template", "EquipeComponent_div_20_Template", "EquipeComponent_Template_input_input_34_listener", "_r32", "_r3", "EquipeComponent_Template_textarea_input_41_listener", "_r4", "EquipeComponent_Template_button_click_46_listener", "EquipeComponent_span_47_Template", "EquipeComponent_button_49_Template", "EquipeComponent_div_58_Template", "ɵɵclassProp"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\admin\\equipes\\equipe\\equipe.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\admin\\equipes\\equipe\\equipe.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { EquipeService } from 'src/app/services/equipe.service';\nimport { MembreService } from 'src/app/services/membre.service';\nimport { Equipe } from 'src/app/models/equipe.model';\nimport { Membre } from 'src/app/models/membre.model';\nimport { forkJoin } from 'rxjs';\n\n// Add Bootstrap type declaration\ndeclare global {\n  interface Window {\n    bootstrap: any;\n  }\n}\n\n@Component({\n  selector: 'app-equipe',\n  templateUrl: './equipe.component.html',\n  styleUrls: ['./equipe.component.css'],\n})\nexport class EquipeComponent implements OnInit {\n  equipes: Equipe[] = [];\n  newEquipe: Equipe = { name: '', description: '' };\n  selectedEquipe: Equipe | null = null;\n  isEditing = false;\n  membres: Membre[] = [];\n  loading = false;\n  error = '';\n\n  constructor(\n    private equipeService: EquipeService,\n    private membreService: MembreService\n  ) {}\n\n  ngOnInit(): void {\n    this.loadEquipes();\n    this.loadMembres();\n  }\n\n  loadEquipes() {\n    this.loading = true;\n    this.equipeService.getEquipes().subscribe({\n      next: (data) => {\n        console.log('Loaded equipes:', data);\n        this.equipes = data;\n        this.loading = false;\n      },\n      error: (error) => {\n        console.error('Error loading equipes:', error);\n        this.error = 'Erreur lors du chargement des équipes: ' + error.message;\n        this.loading = false;\n      }\n    });\n  }\n\n  loadMembres() {\n    this.loading = true;\n    this.membreService.getMembres().subscribe({\n      next: (data) => {\n        console.log('Loaded membres:', data);\n        this.membres = data;\n        this.loading = false;\n      },\n      error: (error) => {\n        console.error('Error loading membres:', error);\n        this.error = 'Erreur lors du chargement des membres: ' + error.message;\n        this.loading = false;\n      }\n    });\n  }\n\n  addEquipe() {\n    console.log('Adding equipe:', this.newEquipe);\n\n    if (!this.newEquipe.name) {\n      console.error('Team name is required');\n      this.error = 'Le nom de l\\'équipe est requis';\n      return;\n    }\n\n    this.loading = true;\n    this.error = '';\n\n    this.equipeService.addEquipe(this.newEquipe).subscribe({\n      next: (response) => {\n        console.log('Equipe added successfully:', response);\n        this.loadEquipes();\n        this.newEquipe = { name: '', description: '' }; // Clear input\n        this.loading = false;\n\n        // Afficher un message de succès temporaire\n        const successMessage = 'Équipe créée avec succès!';\n        this.error = ''; // Effacer les erreurs précédentes\n        alert(successMessage);\n      },\n      error: (error) => {\n        console.error('Error adding equipe:', error);\n        this.error = 'Erreur lors de la création de l\\'équipe: ' + (error.error?.message || error.message || 'Unknown error');\n        this.loading = false;\n      }\n    });\n  }\n\n  editEquipe(equipe: Equipe) {\n    this.isEditing = true;\n    // Créer une copie profonde pour éviter de modifier l'objet original\n    this.newEquipe = {\n      _id: equipe._id,\n      name: equipe.name || '',\n      description: equipe.description || '',\n      admin: equipe.admin,\n      members: equipe.members ? [...equipe.members] : []\n    };\n  }\n\n  cancelEdit() {\n    this.isEditing = false;\n    this.newEquipe = { name: '', description: '' };\n    this.error = ''; // Effacer les erreurs\n  }\n\n  updateSelectedEquipe() {\n    if (!this.newEquipe.name) {\n      console.error('Team name is required');\n      this.error = 'Le nom de l\\'équipe est requis';\n      return;\n    }\n\n    if (this.newEquipe._id) {\n      this.loading = true;\n      this.error = '';\n\n      this.equipeService.updateEquipe(this.newEquipe._id, this.newEquipe).subscribe({\n        next: (updatedEquipe) => {\n          console.log('Team updated successfully:', updatedEquipe);\n          this.loadEquipes();\n          this.isEditing = false;\n          this.newEquipe = { name: '', description: '' };\n          this.loading = false;\n\n          // Afficher un message de succès temporaire\n          const successMessage = 'Équipe mise à jour avec succès!';\n          alert(successMessage);\n        },\n        error: (error) => {\n          console.error('Error updating team:', error);\n          this.error = 'Erreur lors de la mise à jour de l\\'équipe: ' + (error.error?.message || error.message || 'Unknown error');\n          this.loading = false;\n        }\n      });\n    } else {\n      this.error = 'ID de l\\'équipe manquant pour la mise à jour';\n    }\n  }\n\n  deleteEquipe(id: string) {\n    if (!id) {\n      console.error('ID is undefined');\n      this.error = 'ID de l\\'équipe non défini';\n      return;\n    }\n\n    if (confirm('Êtes-vous sûr de vouloir supprimer cette équipe? Cette action est irréversible.')) {\n      this.loading = true;\n      this.error = '';\n\n      this.equipeService.deleteEquipe(id).subscribe({\n        next: (response) => {\n          console.log('Team deleted successfully:', response);\n\n          // Si l'équipe en cours d'édition est celle qui vient d'être supprimée, réinitialiser le formulaire\n          if (this.isEditing && this.newEquipe._id === id) {\n            this.isEditing = false;\n            this.newEquipe = { name: '', description: '' };\n          }\n\n          this.loadEquipes();\n          this.loading = false;\n\n          // Afficher un message de succès\n          alert('Équipe supprimée avec succès');\n        },\n        error: (error) => {\n          console.error('Error deleting team:', error);\n          this.error = 'Erreur lors de la suppression de l\\'équipe: ' + (error.error?.message || error.message || 'Unknown error');\n          this.loading = false;\n        }\n      });\n    }\n  }\n\n  showMembreModal(equipe: Equipe) {\n    this.selectedEquipe = equipe;\n    // Ouvrir le modal avec Bootstrap 5\n    const modalRef = document.getElementById('membreModal');\n    if (modalRef) {\n      try {\n        // Ensure Bootstrap is properly loaded\n        if (typeof window !== 'undefined' && window.bootstrap) {\n          const modal = new window.bootstrap.Modal(modalRef);\n          modal.show();\n        } else {\n          console.error('Bootstrap is not loaded properly');\n          alert('Erreur: Bootstrap n\\'est pas chargé correctement');\n        }\n      } catch (error) {\n        console.error('Error showing modal:', error);\n      }\n    } else {\n      console.error('Modal element not found');\n    }\n  }\n\n  addMembreToEquipe(teamId: string | undefined, membreId: string) {\n    if (!teamId) {\n      console.error('Team ID is undefined');\n      alert('ID de l\\'équipe non défini');\n      return;\n    }\n\n    if (!membreId || membreId.trim() === '') {\n      console.error('Member ID is empty');\n      alert('L\\'ID du membre est requis');\n      return;\n    }\n\n    this.loading = true;\n\n    // Create a proper Membre object that matches what the API expects\n    const membre: Membre = { id: membreId };\n\n    this.equipeService.addMembreToEquipe(teamId, membre).subscribe({\n      next: (response) => {\n        console.log('Member added successfully:', response);\n        this.loadEquipes();\n        this.loading = false;\n\n        // Afficher un message de succès\n        alert('Membre ajouté avec succès à l\\'équipe');\n      },\n      error: (error) => {\n        console.error('Error adding member:', error);\n        this.error = 'Erreur lors de l\\'ajout du membre: ' + (error.error?.message || error.message || 'Unknown error');\n        alert(this.error);\n        this.loading = false;\n      }\n    });\n  }\n\n  removeMembreFromEquipe(teamId: string | undefined, membreId: string) {\n    if (!teamId) {\n      console.error('Team ID is undefined');\n      alert('ID de l\\'équipe non défini');\n      return;\n    }\n\n    if (!membreId) {\n      console.error('Member ID is undefined');\n      alert('ID du membre non défini');\n      return;\n    }\n\n    if (confirm('Êtes-vous sûr de vouloir retirer ce membre de l\\'équipe?')) {\n      this.loading = true;\n\n      this.equipeService.removeMembreFromEquipe(teamId, membreId).subscribe({\n        next: (response) => {\n          console.log('Member removed successfully:', response);\n          this.loadEquipes();\n          this.loading = false;\n\n          // Si l'équipe sélectionnée est celle dont on vient de retirer un membre, mettre à jour l'équipe sélectionnée\n          if (this.selectedEquipe && this.selectedEquipe._id === teamId) {\n            const updatedEquipe = this.equipes.find(e => e._id === teamId);\n            if (updatedEquipe) {\n              this.selectedEquipe = updatedEquipe;\n            }\n          }\n        },\n        error: (error) => {\n          console.error('Error removing member:', error);\n          this.error = 'Erreur lors de la suppression du membre: ' + (error.error?.message || error.message || 'Unknown error');\n          alert(this.error);\n          this.loading = false;\n        }\n      });\n    }\n  }\n}\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n", "<div\n  class=\"min-h-screen bg-[#f0f4f8] dark:bg-[#0a0a0a] relative overflow-hidden\"\n>\n  <!-- Background decorative elements -->\n  <div class=\"absolute inset-0 overflow-hidden pointer-events-none\">\n    <div\n      class=\"absolute top-[15%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-br from-[#dac4ea]/5 to-transparent dark:from-[#00f7ff]/3 dark:to-transparent blur-3xl\"\n    ></div>\n    <div\n      class=\"absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#dac4ea]/5 to-transparent dark:from-[#00f7ff]/3 dark:to-transparent blur-3xl\"\n    ></div>\n\n    <!-- Grid pattern -->\n    <div class=\"absolute inset-0 opacity-5 dark:opacity-[0.03]\">\n      <div class=\"h-full grid grid-cols-12\">\n        <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n      </div>\n    </div>\n  </div>\n\n  <div class=\"max-w-7xl mx-auto p-6 relative z-10\">\n    <!-- Error Alert -->\n    <div *ngIf=\"error\" class=\"mb-6\">\n      <div\n        class=\"bg-[#ff6b69]/10 dark:bg-[#ff3b30]/10 border-l-4 border-[#ff6b69] dark:border-[#ff3b30] rounded-lg p-4 backdrop-blur-sm\"\n      >\n        <div class=\"flex items-start justify-between\">\n          <div class=\"flex items-start\">\n            <div class=\"text-[#ff6b69] dark:text-[#ff3b30] mr-3 text-xl\">\n              <i class=\"fas fa-exclamation-triangle\"></i>\n            </div>\n            <div class=\"flex-1\">\n              <h3 class=\"font-semibold text-[#ff6b69] dark:text-[#ff3b30] mb-1\">\n                Erreur\n              </h3>\n              <p class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0]\">\n                {{ error }}\n              </p>\n            </div>\n          </div>\n          <button\n            (click)=\"error = ''\"\n            class=\"text-[#ff6b69] dark:text-[#ff3b30] hover:bg-[#ff6b69]/10 dark:hover:bg-[#ff3b30]/10 p-1 rounded transition-colors\"\n          >\n            <i class=\"fas fa-times\"></i>\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Loading Indicator -->\n    <div\n      *ngIf=\"loading\"\n      class=\"flex flex-col items-center justify-center py-16\"\n    >\n      <div class=\"relative\">\n        <div\n          class=\"w-12 h-12 border-3 border-[#dac4ea]/20 dark:border-[#00f7ff]/20 border-t-[#dac4ea] dark:border-t-[#00f7ff] rounded-full animate-spin\"\n        ></div>\n        <div\n          class=\"absolute inset-0 bg-[#dac4ea]/20 dark:bg-[#00f7ff]/20 blur-xl rounded-full transform scale-150 -z-10\"\n        ></div>\n      </div>\n      <p\n        class=\"mt-4 text-[#dac4ea] dark:text-[#00f7ff] text-sm font-medium tracking-wide\"\n      >\n        Chargement...\n      </p>\n    </div>\n\n    <!-- Liste des équipes -->\n    <div *ngIf=\"!loading\" class=\"mb-8\">\n      <!-- Header avec titre et bouton refresh -->\n      <div class=\"mb-6 relative\">\n        <!-- Decorative top border -->\n        <div\n          class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea]\"\n        ></div>\n        <div\n          class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea] blur-md\"\n        ></div>\n\n        <div\n          class=\"bg-white dark:bg-[#1a1a1a] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] p-6 backdrop-blur-sm border border-[#dac4ea]/20 dark:border-[#00f7ff]/20\"\n        >\n          <div\n            class=\"flex flex-col lg:flex-row lg:items-center lg:justify-between\"\n          >\n            <div class=\"mb-4 lg:mb-0\">\n              <h2\n                class=\"text-2xl font-bold text-[#dac4ea] dark:text-[#00f7ff] mb-2 tracking-wide\"\n              >\n                Liste des équipes\n              </h2>\n              <p class=\"text-[#6d6870] dark:text-[#a0a0a0] text-sm\">\n                Gérez toutes vos équipes depuis cette interface\n              </p>\n            </div>\n\n            <button\n              (click)=\"loadEquipes()\"\n              class=\"bg-[#dac4ea]/20 dark:bg-[#00f7ff]/20 text-[#dac4ea] dark:text-[#00f7ff] px-4 py-2 rounded-lg font-medium transition-all duration-300 hover:scale-105 hover:bg-[#dac4ea]/30 dark:hover:bg-[#00f7ff]/30\"\n            >\n              <i class=\"fas fa-sync-alt mr-2\"></i>\n              Rafraîchir\n            </button>\n          </div>\n        </div>\n      </div>\n\n      <!-- No teams message -->\n      <div *ngIf=\"equipes.length === 0\" class=\"text-center py-12\">\n        <div\n          class=\"w-16 h-16 mx-auto mb-4 text-[#dac4ea] dark:text-[#00f7ff] opacity-70\"\n        >\n          <i class=\"fas fa-users text-4xl\"></i>\n        </div>\n        <h3\n          class=\"text-lg font-semibold text-[#dac4ea] dark:text-[#00f7ff] mb-2\"\n        >\n          Aucune équipe trouvée\n        </h3>\n        <p class=\"text-[#6d6870] dark:text-[#a0a0a0] text-sm\">\n          Créez votre première équipe ci-dessous\n        </p>\n      </div>\n\n      <!-- Teams Table -->\n      <div\n        *ngIf=\"equipes.length > 0\"\n        class=\"bg-white dark:bg-[#1a1a1a] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] overflow-hidden border border-[#dac4ea]/20 dark:border-[#00f7ff]/20\"\n      >\n        <div class=\"overflow-x-auto\">\n          <table class=\"w-full\">\n            <thead class=\"bg-[#dac4ea]/10 dark:bg-[#00f7ff]/10\">\n              <tr>\n                <th\n                  class=\"px-6 py-4 text-left text-sm font-semibold text-[#dac4ea] dark:text-[#00f7ff]\"\n                >\n                  Nom\n                </th>\n                <th\n                  class=\"px-6 py-4 text-left text-sm font-semibold text-[#dac4ea] dark:text-[#00f7ff]\"\n                >\n                  Description\n                </th>\n                <th\n                  class=\"px-6 py-4 text-left text-sm font-semibold text-[#dac4ea] dark:text-[#00f7ff]\"\n                >\n                  Admin\n                </th>\n                <th\n                  class=\"px-6 py-4 text-left text-sm font-semibold text-[#dac4ea] dark:text-[#00f7ff]\"\n                >\n                  Membres\n                </th>\n                <th\n                  class=\"px-6 py-4 text-center text-sm font-semibold text-[#dac4ea] dark:text-[#00f7ff]\"\n                >\n                  Actions\n                </th>\n              </tr>\n            </thead>\n            <tbody\n              class=\"divide-y divide-[#dac4ea]/10 dark:divide-[#00f7ff]/10\"\n            >\n              <tr\n                *ngFor=\"let equipe of equipes\"\n                class=\"hover:bg-[#dac4ea]/5 dark:hover:bg-[#00f7ff]/5 transition-colors\"\n              >\n                <td class=\"px-6 py-4\">\n                  <div class=\"font-medium text-[#dac4ea] dark:text-[#00f7ff]\">\n                    {{ equipe.name }}\n                  </div>\n                </td>\n                <td class=\"px-6 py-4\">\n                  <div\n                    class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0] max-w-xs truncate\"\n                  >\n                    {{ equipe.description || \"Aucune description\" }}\n                  </div>\n                </td>\n                <td class=\"px-6 py-4\">\n                  <div class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0]\">\n                    {{ equipe.admin || \"Non défini\" }}\n                  </div>\n                </td>\n                <td class=\"px-6 py-4\">\n                  <span\n                    class=\"bg-[#dac4ea]/10 dark:bg-[#00f7ff]/10 text-[#dac4ea] dark:text-[#00f7ff] px-2 py-1 rounded-full text-xs font-medium\"\n                  >\n                    {{ equipe.members?.length || 0 }} membre(s)\n                  </span>\n                </td>\n                <td class=\"px-6 py-4\">\n                  <div class=\"flex items-center justify-center space-x-2\">\n                    <button\n                      (click)=\"editEquipe(equipe)\"\n                      class=\"p-2 text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#dac4ea] dark:hover:text-[#00f7ff] hover:bg-[#dac4ea]/10 dark:hover:bg-[#00f7ff]/10 rounded-lg transition-all\"\n                      title=\"Modifier l'équipe\"\n                    >\n                      <i class=\"fas fa-edit\"></i>\n                    </button>\n                    <button\n                      (click)=\"showMembreModal(equipe)\"\n                      class=\"p-2 text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#00ff9d] hover:bg-[#00ff9d]/10 rounded-lg transition-all\"\n                      title=\"Gérer les membres\"\n                    >\n                      <i class=\"fas fa-users\"></i>\n                    </button>\n                    <button\n                      (click)=\"equipe._id && deleteEquipe(equipe._id)\"\n                      class=\"p-2 text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#ff6b69] dark:hover:text-[#ff3b30] hover:bg-[#ff6b69]/10 dark:hover:bg-[#ff3b30]/10 rounded-lg transition-all\"\n                      title=\"Supprimer l'équipe\"\n                    >\n                      <i class=\"fas fa-trash\"></i>\n                    </button>\n                  </div>\n                </td>\n              </tr>\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </div>\n\n    <!-- Formulaire de création d'équipe -->\n    <div class=\"row mb-4\">\n      <div class=\"col-12\">\n        <div class=\"card\">\n          <div class=\"card-header bg-primary text-white\">\n            <h3 class=\"mb-0\">\n              {{ isEditing ? \"Modifier une équipe\" : \"Créer une équipe\" }}\n            </h3>\n          </div>\n          <div class=\"card-body\">\n            <form>\n              <div class=\"mb-3\">\n                <label for=\"name\" class=\"form-label\"\n                  >Nom de l'équipe <span class=\"text-danger\">*</span></label\n                >\n                <input\n                  #nameInput\n                  type=\"text\"\n                  class=\"form-control\"\n                  id=\"name\"\n                  [value]=\"newEquipe.name\"\n                  (input)=\"newEquipe.name = nameInput.value\"\n                  required\n                  [class.is-invalid]=\"\n                    !newEquipe.name && (isEditing || newEquipe.name === '')\n                  \"\n                  placeholder=\"Entrez le nom de l'équipe\"\n                />\n                <div class=\"invalid-feedback\">\n                  Le nom de l'équipe est requis\n                </div>\n              </div>\n              <div class=\"mb-3\">\n                <label for=\"description\" class=\"form-label\">Description</label>\n                <textarea\n                  #descInput\n                  class=\"form-control\"\n                  id=\"description\"\n                  [value]=\"newEquipe.description || ''\"\n                  (input)=\"newEquipe.description = descInput.value\"\n                  rows=\"3\"\n                  placeholder=\"Entrez une description pour cette équipe\"\n                ></textarea>\n                <small class=\"text-muted\"\n                  >Une brève description de l'équipe et de son objectif</small\n                >\n              </div>\n              <div class=\"d-flex\">\n                <button\n                  type=\"button\"\n                  class=\"btn btn-primary\"\n                  [disabled]=\"!newEquipe.name || loading\"\n                  (click)=\"isEditing ? updateSelectedEquipe() : addEquipe()\"\n                >\n                  <span\n                    *ngIf=\"loading\"\n                    class=\"spinner-border spinner-border-sm me-1\"\n                    role=\"status\"\n                    aria-hidden=\"true\"\n                  ></span>\n                  {{ isEditing ? \"Mettre à jour\" : \"Créer\" }}\n                </button>\n                <button\n                  *ngIf=\"isEditing\"\n                  type=\"button\"\n                  class=\"btn btn-secondary ms-2\"\n                  (click)=\"cancelEdit()\"\n                >\n                  Annuler\n                </button>\n              </div>\n            </form>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Modal pour gérer les membres -->\n    <div class=\"modal fade\" id=\"membreModal\" tabindex=\"-1\" aria-hidden=\"true\">\n      <div class=\"modal-dialog\">\n        <div class=\"modal-content\">\n          <div class=\"modal-header\">\n            <h5 class=\"modal-title\">Gérer les membres de l'équipe</h5>\n            <button\n              type=\"button\"\n              class=\"btn-close\"\n              data-bs-dismiss=\"modal\"\n              aria-label=\"Close\"\n            ></button>\n          </div>\n          <div class=\"modal-body\">\n            <div *ngIf=\"selectedEquipe\">\n              <h6>Équipe: {{ selectedEquipe.name }}</h6>\n\n              <!-- Liste des membres actuels -->\n              <div class=\"mb-3\">\n                <h6>Membres actuels:</h6>\n                <div\n                  *ngIf=\"\n                    selectedEquipe.members && selectedEquipe.members.length > 0;\n                    else noMembers\n                  \"\n                >\n                  <ul class=\"list-group\">\n                    <li\n                      class=\"list-group-item d-flex justify-content-between align-items-center\"\n                      *ngFor=\"let membreId of selectedEquipe.members\"\n                    >\n                      <span>{{ membreId }}</span>\n                      <button\n                        class=\"btn btn-sm btn-danger\"\n                        (click)=\"\n                          removeMembreFromEquipe(selectedEquipe._id, membreId)\n                        \"\n                      >\n                        Retirer\n                      </button>\n                    </li>\n                  </ul>\n                </div>\n                <ng-template #noMembers>\n                  <p class=\"text-muted\">Aucun membre dans cette équipe</p>\n                </ng-template>\n              </div>\n\n              <!-- Formulaire pour ajouter un membre -->\n              <div class=\"mb-3\">\n                <h6>Ajouter un membre:</h6>\n                <div class=\"input-group mb-2\">\n                  <input\n                    #membreIdInput\n                    type=\"text\"\n                    class=\"form-control\"\n                    placeholder=\"ID du membre\"\n                  />\n                  <button\n                    class=\"btn btn-primary\"\n                    [disabled]=\"\n                      !selectedEquipe ||\n                      !selectedEquipe._id ||\n                      !membreIdInput.value\n                    \"\n                    (click)=\"\n                      addMembreToEquipe(\n                        selectedEquipe._id,\n                        membreIdInput.value\n                      );\n                      membreIdInput.value = ''\n                    \"\n                  >\n                    Ajouter\n                  </button>\n                </div>\n                <small class=\"text-muted\"\n                  >Entrez l'ID du membre à ajouter à l'équipe</small\n                >\n              </div>\n\n              <!-- Informations supplémentaires -->\n              <div class=\"alert alert-info mt-3\">\n                <p class=\"mb-0\">\n                  <strong>Note:</strong> Pour ajouter un membre, vous devez\n                  d'abord créer le membre dans la section des membres.\n                </p>\n              </div>\n            </div>\n          </div>\n          <div class=\"modal-footer\">\n            <button\n              type=\"button\"\n              class=\"btn btn-secondary\"\n              data-bs-dismiss=\"modal\"\n            >\n              Fermer\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": ";;;;;;;;ICgCIA,EAAA,CAAAC,cAAA,cAAgC;IAOtBD,EAAA,CAAAE,SAAA,YAA2C;IAC7CF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAoB;IAEhBD,EAAA,CAAAI,MAAA,eACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,YAAsD;IACpDD,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAGRH,EAAA,CAAAC,cAAA,kBAGC;IAFCD,EAAA,CAAAK,UAAA,mBAAAC,yDAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAAF,MAAA,CAAAG,KAAA,GAAiB,EAAE;IAAA,EAAC;IAGpBZ,EAAA,CAAAE,SAAA,aAA4B;IAC9BF,EAAA,CAAAG,YAAA,EAAS;;;;IATHH,EAAA,CAAAa,SAAA,IACF;IADEb,EAAA,CAAAc,kBAAA,MAAAC,MAAA,CAAAH,KAAA,MACF;;;;;IAcVZ,EAAA,CAAAC,cAAA,cAGC;IAEGD,EAAA,CAAAE,SAAA,cAEO;IAITF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,YAEC;IACCD,EAAA,CAAAI,MAAA,sBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;;IA4CJH,EAAA,CAAAC,cAAA,cAA4D;IAIxDD,EAAA,CAAAE,SAAA,YAAqC;IACvCF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,aAEC;IACCD,EAAA,CAAAI,MAAA,wCACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,YAAsD;IACpDD,EAAA,CAAAI,MAAA,8DACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;;;IA0CEH,EAAA,CAAAC,cAAA,aAGC;IAGKD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,aAAsB;IAIlBD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,aAAsB;IAElBD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,cAAsB;IAIlBD,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAETH,EAAA,CAAAC,cAAA,cAAsB;IAGhBD,EAAA,CAAAK,UAAA,mBAAAW,sEAAA;MAAA,MAAAC,WAAA,GAAAjB,EAAA,CAAAO,aAAA,CAAAW,IAAA;MAAA,MAAAC,UAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAC,OAAA,GAAArB,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAU,OAAA,CAAAC,UAAA,CAAAH,UAAA,CAAkB;IAAA,EAAC;IAI5BnB,EAAA,CAAAE,SAAA,aAA2B;IAC7BF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAIC;IAHCD,EAAA,CAAAK,UAAA,mBAAAkB,sEAAA;MAAA,MAAAN,WAAA,GAAAjB,EAAA,CAAAO,aAAA,CAAAW,IAAA;MAAA,MAAAC,UAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAI,OAAA,GAAAxB,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAa,OAAA,CAAAC,eAAA,CAAAN,UAAA,CAAuB;IAAA,EAAC;IAIjCnB,EAAA,CAAAE,SAAA,aAA4B;IAC9BF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAIC;IAHCD,EAAA,CAAAK,UAAA,mBAAAqB,sEAAA;MAAA,MAAAT,WAAA,GAAAjB,EAAA,CAAAO,aAAA,CAAAW,IAAA;MAAA,MAAAC,UAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAO,OAAA,GAAA3B,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAQ,UAAA,CAAAS,GAAA,IAAcD,OAAA,CAAAE,YAAA,CAAAV,UAAA,CAAAS,GAAA,CAAwB;IAAA,EAAC;IAIhD5B,EAAA,CAAAE,SAAA,aAA4B;IAC9BF,EAAA,CAAAG,YAAA,EAAS;;;;IA5CTH,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,MAAAK,UAAA,CAAAW,IAAA,MACF;IAME9B,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,MAAAK,UAAA,CAAAY,WAAA,8BACF;IAIE/B,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,MAAAK,UAAA,CAAAa,KAAA,2BACF;IAMEhC,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,OAAAK,UAAA,CAAAc,OAAA,kBAAAd,UAAA,CAAAc,OAAA,CAAAC,MAAA,sBACF;;;;;IAhEZlC,EAAA,CAAAC,cAAA,cAGC;IAQWD,EAAA,CAAAI,MAAA,YACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,aAEC;IACCD,EAAA,CAAAI,MAAA,oBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,aAEC;IACCD,EAAA,CAAAI,MAAA,eACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAEC;IACCD,EAAA,CAAAI,MAAA,iBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAEC;IACCD,EAAA,CAAAI,MAAA,iBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAGTH,EAAA,CAAAC,cAAA,iBAEC;IACCD,EAAA,CAAAmC,UAAA,KAAAC,4CAAA,kBAqDK;IACPpC,EAAA,CAAAG,YAAA,EAAQ;;;;IArDeH,EAAA,CAAAa,SAAA,IAAU;IAAVb,EAAA,CAAAqC,UAAA,YAAAC,OAAA,CAAAC,OAAA,CAAU;;;;;;IAhGzCvC,EAAA,CAAAC,cAAA,cAAmC;IAI/BD,EAAA,CAAAE,SAAA,cAEO;IAKPF,EAAA,CAAAC,cAAA,cAEC;IAQOD,EAAA,CAAAI,MAAA,+BACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,YAAsD;IACpDD,EAAA,CAAAI,MAAA,mEACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAGNH,EAAA,CAAAC,cAAA,kBAGC;IAFCD,EAAA,CAAAK,UAAA,mBAAAmC,yDAAA;MAAAxC,EAAA,CAAAO,aAAA,CAAAkC,IAAA;MAAA,MAAAC,OAAA,GAAA1C,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAA+B,OAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;IAGvB3C,EAAA,CAAAE,SAAA,aAAoC;IACpCF,EAAA,CAAAI,MAAA,yBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IAMfH,EAAA,CAAAmC,UAAA,KAAAS,sCAAA,kBAcM;IAGN5C,EAAA,CAAAmC,UAAA,KAAAU,sCAAA,mBA+FM;IACR7C,EAAA,CAAAG,YAAA,EAAM;;;;IAjHEH,EAAA,CAAAa,SAAA,IAA0B;IAA1Bb,EAAA,CAAAqC,UAAA,SAAAS,MAAA,CAAAP,OAAA,CAAAL,MAAA,OAA0B;IAkB7BlC,EAAA,CAAAa,SAAA,GAAwB;IAAxBb,EAAA,CAAAqC,UAAA,SAAAS,MAAA,CAAAP,OAAA,CAAAL,MAAA,KAAwB;;;;;IAuJflC,EAAA,CAAAE,SAAA,eAKQ;;;;;;IAGVF,EAAA,CAAAC,cAAA,iBAKC;IADCD,EAAA,CAAAK,UAAA,mBAAA0C,2DAAA;MAAA/C,EAAA,CAAAO,aAAA,CAAAyC,IAAA;MAAA,MAAAC,OAAA,GAAAjD,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAsC,OAAA,CAAAC,UAAA,EAAY;IAAA,EAAC;IAEtBlD,EAAA,CAAAI,MAAA,gBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;;;IAmCLH,EAAA,CAAAC,cAAA,cAGC;IACOD,EAAA,CAAAI,MAAA,GAAc;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAC3BH,EAAA,CAAAC,cAAA,kBAKC;IAHCD,EAAA,CAAAK,UAAA,mBAAA8C,mEAAA;MAAA,MAAAlC,WAAA,GAAAjB,EAAA,CAAAO,aAAA,CAAA6C,IAAA;MAAA,MAAAC,YAAA,GAAApC,WAAA,CAAAG,SAAA;MAAA,MAAAkC,OAAA,GAAAtD,EAAA,CAAAU,aAAA;MAAA,OAC6BV,EAAA,CAAAW,WAAA,CAAA2C,OAAA,CAAAC,sBAAA,CAAAD,OAAA,CAAAE,cAAA,CAAA5B,GAAA,EAAAyB,YAAA,CAErD;IAAA,EADyB;IAEDrD,EAAA,CAAAI,MAAA,gBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;IARHH,EAAA,CAAAa,SAAA,GAAc;IAAdb,EAAA,CAAAyD,iBAAA,CAAAJ,YAAA,CAAc;;;;;IAX1BrD,EAAA,CAAAC,cAAA,UAKC;IAEGD,EAAA,CAAAmC,UAAA,IAAAuB,0CAAA,kBAaK;IACP1D,EAAA,CAAAG,YAAA,EAAK;;;;IAZoBH,EAAA,CAAAa,SAAA,GAAyB;IAAzBb,EAAA,CAAAqC,UAAA,YAAAsB,OAAA,CAAAH,cAAA,CAAAvB,OAAA,CAAyB;;;;;IAelDjC,EAAA,CAAAC,cAAA,YAAsB;IAAAD,EAAA,CAAAI,MAAA,0CAA8B;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;;;IA9B9DH,EAAA,CAAAC,cAAA,UAA4B;IACtBD,EAAA,CAAAI,MAAA,GAAiC;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAG1CH,EAAA,CAAAC,cAAA,cAAkB;IACZD,EAAA,CAAAI,MAAA,uBAAgB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAmC,UAAA,IAAAyB,qCAAA,kBAsBM;IACN5D,EAAA,CAAAmC,UAAA,IAAA0B,6CAAA,iCAAA7D,EAAA,CAAA8D,sBAAA,CAEc;IAChB9D,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,cAAkB;IACZD,EAAA,CAAAI,MAAA,0BAAkB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAC3BH,EAAA,CAAAC,cAAA,eAA8B;IAC5BD,EAAA,CAAAE,SAAA,sBAKE;IACFF,EAAA,CAAAC,cAAA,mBAcC;IAPCD,EAAA,CAAAK,UAAA,mBAAA0D,yDAAA;MAAA/D,EAAA,CAAAO,aAAA,CAAAyD,IAAA;MAAA,MAAAC,IAAA,GAAAjE,EAAA,CAAAkE,WAAA;MAAA,MAAAC,OAAA,GAAAnE,EAAA,CAAAU,aAAA;MAEhByD,OAAA,CAAAC,iBAAA,CAAAD,OAAA,CAAAX,cAAA,CAAA5B,GAAA,EAAAqC,IAAA,CAAAI,KAAA,CAGiB;MAAA,OAAwBrE,EAAA,CAAAW,WAAA,CAAAsD,IAAA,CAAAI,KAAA,GACzB,EACpB;IAAA,EADqB;IAEDrE,EAAA,CAAAI,MAAA,iBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IAEXH,EAAA,CAAAC,cAAA,iBACG;IAAAD,EAAA,CAAAI,MAAA,iEAA0C;IAAAJ,EAAA,CAAAG,YAAA,EAC5C;IAIHH,EAAA,CAAAC,cAAA,gBAAmC;IAEvBD,EAAA,CAAAI,MAAA,aAAK;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAI,MAAA,sGAEzB;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;;;IAvEFH,EAAA,CAAAa,SAAA,GAAiC;IAAjCb,EAAA,CAAAc,kBAAA,kBAAAwD,MAAA,CAAAd,cAAA,CAAA1B,IAAA,KAAiC;IAMhC9B,EAAA,CAAAa,SAAA,GAGb;IAHab,EAAA,CAAAqC,UAAA,SAAAiC,MAAA,CAAAd,cAAA,CAAAvB,OAAA,IAAAqC,MAAA,CAAAd,cAAA,CAAAvB,OAAA,CAAAC,MAAA,KAGb,aAAAqC,IAAA;IAoCcvE,EAAA,CAAAa,SAAA,GAIC;IAJDb,EAAA,CAAAqC,UAAA,cAAAiC,MAAA,CAAAd,cAAA,KAAAc,MAAA,CAAAd,cAAA,CAAA5B,GAAA,KAAAqC,IAAA,CAAAI,KAAA,CAIC;;;ADtWrB,OAAM,MAAOG,eAAe;EAS1BC,YACUC,aAA4B,EAC5BC,aAA4B;IAD5B,KAAAD,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;IAVvB,KAAApC,OAAO,GAAa,EAAE;IACtB,KAAAqC,SAAS,GAAW;MAAE9C,IAAI,EAAE,EAAE;MAAEC,WAAW,EAAE;IAAE,CAAE;IACjD,KAAAyB,cAAc,GAAkB,IAAI;IACpC,KAAAqB,SAAS,GAAG,KAAK;IACjB,KAAAC,OAAO,GAAa,EAAE;IACtB,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAnE,KAAK,GAAG,EAAE;EAKP;EAEHoE,QAAQA,CAAA;IACN,IAAI,CAACrC,WAAW,EAAE;IAClB,IAAI,CAACsC,WAAW,EAAE;EACpB;EAEAtC,WAAWA,CAAA;IACT,IAAI,CAACoC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACL,aAAa,CAACQ,UAAU,EAAE,CAACC,SAAS,CAAC;MACxCC,IAAI,EAAGC,IAAI,IAAI;QACbC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEF,IAAI,CAAC;QACpC,IAAI,CAAC9C,OAAO,GAAG8C,IAAI;QACnB,IAAI,CAACN,OAAO,GAAG,KAAK;MACtB,CAAC;MACDnE,KAAK,EAAGA,KAAK,IAAI;QACf0E,OAAO,CAAC1E,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAACA,KAAK,GAAG,yCAAyC,GAAGA,KAAK,CAAC4E,OAAO;QACtE,IAAI,CAACT,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAE,WAAWA,CAAA;IACT,IAAI,CAACF,OAAO,GAAG,IAAI;IACnB,IAAI,CAACJ,aAAa,CAACc,UAAU,EAAE,CAACN,SAAS,CAAC;MACxCC,IAAI,EAAGC,IAAI,IAAI;QACbC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEF,IAAI,CAAC;QACpC,IAAI,CAACP,OAAO,GAAGO,IAAI;QACnB,IAAI,CAACN,OAAO,GAAG,KAAK;MACtB,CAAC;MACDnE,KAAK,EAAGA,KAAK,IAAI;QACf0E,OAAO,CAAC1E,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAACA,KAAK,GAAG,yCAAyC,GAAGA,KAAK,CAAC4E,OAAO;QACtE,IAAI,CAACT,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAW,SAASA,CAAA;IACPJ,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAACX,SAAS,CAAC;IAE7C,IAAI,CAAC,IAAI,CAACA,SAAS,CAAC9C,IAAI,EAAE;MACxBwD,OAAO,CAAC1E,KAAK,CAAC,uBAAuB,CAAC;MACtC,IAAI,CAACA,KAAK,GAAG,gCAAgC;MAC7C;;IAGF,IAAI,CAACmE,OAAO,GAAG,IAAI;IACnB,IAAI,CAACnE,KAAK,GAAG,EAAE;IAEf,IAAI,CAAC8D,aAAa,CAACgB,SAAS,CAAC,IAAI,CAACd,SAAS,CAAC,CAACO,SAAS,CAAC;MACrDC,IAAI,EAAGO,QAAQ,IAAI;QACjBL,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEI,QAAQ,CAAC;QACnD,IAAI,CAAChD,WAAW,EAAE;QAClB,IAAI,CAACiC,SAAS,GAAG;UAAE9C,IAAI,EAAE,EAAE;UAAEC,WAAW,EAAE;QAAE,CAAE,CAAC,CAAC;QAChD,IAAI,CAACgD,OAAO,GAAG,KAAK;QAEpB;QACA,MAAMa,cAAc,GAAG,2BAA2B;QAClD,IAAI,CAAChF,KAAK,GAAG,EAAE,CAAC,CAAC;QACjBiF,KAAK,CAACD,cAAc,CAAC;MACvB,CAAC;MACDhF,KAAK,EAAGA,KAAK,IAAI;QACf0E,OAAO,CAAC1E,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C,IAAI,CAACA,KAAK,GAAG,2CAA2C,IAAIA,KAAK,CAACA,KAAK,EAAE4E,OAAO,IAAI5E,KAAK,CAAC4E,OAAO,IAAI,eAAe,CAAC;QACrH,IAAI,CAACT,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAzD,UAAUA,CAACwE,MAAc;IACvB,IAAI,CAACjB,SAAS,GAAG,IAAI;IACrB;IACA,IAAI,CAACD,SAAS,GAAG;MACfhD,GAAG,EAAEkE,MAAM,CAAClE,GAAG;MACfE,IAAI,EAAEgE,MAAM,CAAChE,IAAI,IAAI,EAAE;MACvBC,WAAW,EAAE+D,MAAM,CAAC/D,WAAW,IAAI,EAAE;MACrCC,KAAK,EAAE8D,MAAM,CAAC9D,KAAK;MACnBC,OAAO,EAAE6D,MAAM,CAAC7D,OAAO,GAAG,CAAC,GAAG6D,MAAM,CAAC7D,OAAO,CAAC,GAAG;KACjD;EACH;EAEAiB,UAAUA,CAAA;IACR,IAAI,CAAC2B,SAAS,GAAG,KAAK;IACtB,IAAI,CAACD,SAAS,GAAG;MAAE9C,IAAI,EAAE,EAAE;MAAEC,WAAW,EAAE;IAAE,CAAE;IAC9C,IAAI,CAACnB,KAAK,GAAG,EAAE,CAAC,CAAC;EACnB;;EAEAmF,oBAAoBA,CAAA;IAClB,IAAI,CAAC,IAAI,CAACnB,SAAS,CAAC9C,IAAI,EAAE;MACxBwD,OAAO,CAAC1E,KAAK,CAAC,uBAAuB,CAAC;MACtC,IAAI,CAACA,KAAK,GAAG,gCAAgC;MAC7C;;IAGF,IAAI,IAAI,CAACgE,SAAS,CAAChD,GAAG,EAAE;MACtB,IAAI,CAACmD,OAAO,GAAG,IAAI;MACnB,IAAI,CAACnE,KAAK,GAAG,EAAE;MAEf,IAAI,CAAC8D,aAAa,CAACsB,YAAY,CAAC,IAAI,CAACpB,SAAS,CAAChD,GAAG,EAAE,IAAI,CAACgD,SAAS,CAAC,CAACO,SAAS,CAAC;QAC5EC,IAAI,EAAGa,aAAa,IAAI;UACtBX,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEU,aAAa,CAAC;UACxD,IAAI,CAACtD,WAAW,EAAE;UAClB,IAAI,CAACkC,SAAS,GAAG,KAAK;UACtB,IAAI,CAACD,SAAS,GAAG;YAAE9C,IAAI,EAAE,EAAE;YAAEC,WAAW,EAAE;UAAE,CAAE;UAC9C,IAAI,CAACgD,OAAO,GAAG,KAAK;UAEpB;UACA,MAAMa,cAAc,GAAG,iCAAiC;UACxDC,KAAK,CAACD,cAAc,CAAC;QACvB,CAAC;QACDhF,KAAK,EAAGA,KAAK,IAAI;UACf0E,OAAO,CAAC1E,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;UAC5C,IAAI,CAACA,KAAK,GAAG,8CAA8C,IAAIA,KAAK,CAACA,KAAK,EAAE4E,OAAO,IAAI5E,KAAK,CAAC4E,OAAO,IAAI,eAAe,CAAC;UACxH,IAAI,CAACT,OAAO,GAAG,KAAK;QACtB;OACD,CAAC;KACH,MAAM;MACL,IAAI,CAACnE,KAAK,GAAG,8CAA8C;;EAE/D;EAEAiB,YAAYA,CAACqE,EAAU;IACrB,IAAI,CAACA,EAAE,EAAE;MACPZ,OAAO,CAAC1E,KAAK,CAAC,iBAAiB,CAAC;MAChC,IAAI,CAACA,KAAK,GAAG,4BAA4B;MACzC;;IAGF,IAAIuF,OAAO,CAAC,iFAAiF,CAAC,EAAE;MAC9F,IAAI,CAACpB,OAAO,GAAG,IAAI;MACnB,IAAI,CAACnE,KAAK,GAAG,EAAE;MAEf,IAAI,CAAC8D,aAAa,CAAC7C,YAAY,CAACqE,EAAE,CAAC,CAACf,SAAS,CAAC;QAC5CC,IAAI,EAAGO,QAAQ,IAAI;UACjBL,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEI,QAAQ,CAAC;UAEnD;UACA,IAAI,IAAI,CAACd,SAAS,IAAI,IAAI,CAACD,SAAS,CAAChD,GAAG,KAAKsE,EAAE,EAAE;YAC/C,IAAI,CAACrB,SAAS,GAAG,KAAK;YACtB,IAAI,CAACD,SAAS,GAAG;cAAE9C,IAAI,EAAE,EAAE;cAAEC,WAAW,EAAE;YAAE,CAAE;;UAGhD,IAAI,CAACY,WAAW,EAAE;UAClB,IAAI,CAACoC,OAAO,GAAG,KAAK;UAEpB;UACAc,KAAK,CAAC,8BAA8B,CAAC;QACvC,CAAC;QACDjF,KAAK,EAAGA,KAAK,IAAI;UACf0E,OAAO,CAAC1E,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;UAC5C,IAAI,CAACA,KAAK,GAAG,8CAA8C,IAAIA,KAAK,CAACA,KAAK,EAAE4E,OAAO,IAAI5E,KAAK,CAAC4E,OAAO,IAAI,eAAe,CAAC;UACxH,IAAI,CAACT,OAAO,GAAG,KAAK;QACtB;OACD,CAAC;;EAEN;EAEAtD,eAAeA,CAACqE,MAAc;IAC5B,IAAI,CAACtC,cAAc,GAAGsC,MAAM;IAC5B;IACA,MAAMM,QAAQ,GAAGC,QAAQ,CAACC,cAAc,CAAC,aAAa,CAAC;IACvD,IAAIF,QAAQ,EAAE;MACZ,IAAI;QACF;QACA,IAAI,OAAOG,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,SAAS,EAAE;UACrD,MAAMC,KAAK,GAAG,IAAIF,MAAM,CAACC,SAAS,CAACE,KAAK,CAACN,QAAQ,CAAC;UAClDK,KAAK,CAACE,IAAI,EAAE;SACb,MAAM;UACLrB,OAAO,CAAC1E,KAAK,CAAC,kCAAkC,CAAC;UACjDiF,KAAK,CAAC,kDAAkD,CAAC;;OAE5D,CAAC,OAAOjF,KAAK,EAAE;QACd0E,OAAO,CAAC1E,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;;KAE/C,MAAM;MACL0E,OAAO,CAAC1E,KAAK,CAAC,yBAAyB,CAAC;;EAE5C;EAEAwD,iBAAiBA,CAACwC,MAA0B,EAAEC,QAAgB;IAC5D,IAAI,CAACD,MAAM,EAAE;MACXtB,OAAO,CAAC1E,KAAK,CAAC,sBAAsB,CAAC;MACrCiF,KAAK,CAAC,4BAA4B,CAAC;MACnC;;IAGF,IAAI,CAACgB,QAAQ,IAAIA,QAAQ,CAACC,IAAI,EAAE,KAAK,EAAE,EAAE;MACvCxB,OAAO,CAAC1E,KAAK,CAAC,oBAAoB,CAAC;MACnCiF,KAAK,CAAC,4BAA4B,CAAC;MACnC;;IAGF,IAAI,CAACd,OAAO,GAAG,IAAI;IAEnB;IACA,MAAMgC,MAAM,GAAW;MAAEb,EAAE,EAAEW;IAAQ,CAAE;IAEvC,IAAI,CAACnC,aAAa,CAACN,iBAAiB,CAACwC,MAAM,EAAEG,MAAM,CAAC,CAAC5B,SAAS,CAAC;MAC7DC,IAAI,EAAGO,QAAQ,IAAI;QACjBL,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEI,QAAQ,CAAC;QACnD,IAAI,CAAChD,WAAW,EAAE;QAClB,IAAI,CAACoC,OAAO,GAAG,KAAK;QAEpB;QACAc,KAAK,CAAC,uCAAuC,CAAC;MAChD,CAAC;MACDjF,KAAK,EAAGA,KAAK,IAAI;QACf0E,OAAO,CAAC1E,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C,IAAI,CAACA,KAAK,GAAG,qCAAqC,IAAIA,KAAK,CAACA,KAAK,EAAE4E,OAAO,IAAI5E,KAAK,CAAC4E,OAAO,IAAI,eAAe,CAAC;QAC/GK,KAAK,CAAC,IAAI,CAACjF,KAAK,CAAC;QACjB,IAAI,CAACmE,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAxB,sBAAsBA,CAACqD,MAA0B,EAAEC,QAAgB;IACjE,IAAI,CAACD,MAAM,EAAE;MACXtB,OAAO,CAAC1E,KAAK,CAAC,sBAAsB,CAAC;MACrCiF,KAAK,CAAC,4BAA4B,CAAC;MACnC;;IAGF,IAAI,CAACgB,QAAQ,EAAE;MACbvB,OAAO,CAAC1E,KAAK,CAAC,wBAAwB,CAAC;MACvCiF,KAAK,CAAC,yBAAyB,CAAC;MAChC;;IAGF,IAAIM,OAAO,CAAC,0DAA0D,CAAC,EAAE;MACvE,IAAI,CAACpB,OAAO,GAAG,IAAI;MAEnB,IAAI,CAACL,aAAa,CAACnB,sBAAsB,CAACqD,MAAM,EAAEC,QAAQ,CAAC,CAAC1B,SAAS,CAAC;QACpEC,IAAI,EAAGO,QAAQ,IAAI;UACjBL,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEI,QAAQ,CAAC;UACrD,IAAI,CAAChD,WAAW,EAAE;UAClB,IAAI,CAACoC,OAAO,GAAG,KAAK;UAEpB;UACA,IAAI,IAAI,CAACvB,cAAc,IAAI,IAAI,CAACA,cAAc,CAAC5B,GAAG,KAAKgF,MAAM,EAAE;YAC7D,MAAMX,aAAa,GAAG,IAAI,CAAC1D,OAAO,CAACyE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACrF,GAAG,KAAKgF,MAAM,CAAC;YAC9D,IAAIX,aAAa,EAAE;cACjB,IAAI,CAACzC,cAAc,GAAGyC,aAAa;;;QAGzC,CAAC;QACDrF,KAAK,EAAGA,KAAK,IAAI;UACf0E,OAAO,CAAC1E,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;UAC9C,IAAI,CAACA,KAAK,GAAG,2CAA2C,IAAIA,KAAK,CAACA,KAAK,EAAE4E,OAAO,IAAI5E,KAAK,CAAC4E,OAAO,IAAI,eAAe,CAAC;UACrHK,KAAK,CAAC,IAAI,CAACjF,KAAK,CAAC;UACjB,IAAI,CAACmE,OAAO,GAAG,KAAK;QACtB;OACD,CAAC;;EAEN;;;uBA3QWP,eAAe,EAAAxE,EAAA,CAAAkH,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAApH,EAAA,CAAAkH,iBAAA,CAAAG,EAAA,CAAAC,aAAA;IAAA;EAAA;;;YAAf9C,eAAe;MAAA+C,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCnB5B7H,EAAA,CAAAC,cAAA,aAEC;UAGGD,EAAA,CAAAE,SAAA,aAEO;UAMPF,EAAA,CAAAC,cAAA,aAA4D;UAExDD,EAAA,CAAAE,SAAA,aAAmE;UAWrEF,EAAA,CAAAG,YAAA,EAAM;UAIVH,EAAA,CAAAC,cAAA,cAAiD;UAE/CD,EAAA,CAAAmC,UAAA,KAAA4F,+BAAA,kBA0BM;UAGN/H,EAAA,CAAAmC,UAAA,KAAA6F,+BAAA,iBAiBM;UAGNhI,EAAA,CAAAmC,UAAA,KAAA8F,+BAAA,mBAyJM;UAGNjI,EAAA,CAAAC,cAAA,eAAsB;UAKZD,EAAA,CAAAI,MAAA,IACF;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UAEPH,EAAA,CAAAC,cAAA,eAAuB;UAIdD,EAAA,CAAAI,MAAA,6BAAgB;UAAAJ,EAAA,CAAAC,cAAA,gBAA0B;UAAAD,EAAA,CAAAI,MAAA,SAAC;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UAErDH,EAAA,CAAAC,cAAA,qBAYE;UANAD,EAAA,CAAAK,UAAA,mBAAA6H,iDAAA;YAAAlI,EAAA,CAAAO,aAAA,CAAA4H,IAAA;YAAA,MAAAC,GAAA,GAAApI,EAAA,CAAAkE,WAAA;YAAA,OAASlE,EAAA,CAAAW,WAAA,CAAAmH,GAAA,CAAAlD,SAAA,CAAA9C,IAAA,GAAAsG,GAAA,CAAA/D,KAAA,CAAgC;UAAA,EAAC;UAN5CrE,EAAA,CAAAG,YAAA,EAYE;UACFH,EAAA,CAAAC,cAAA,eAA8B;UAC5BD,EAAA,CAAAI,MAAA,4CACF;UAAAJ,EAAA,CAAAG,YAAA,EAAM;UAERH,EAAA,CAAAC,cAAA,eAAkB;UAC4BD,EAAA,CAAAI,MAAA,mBAAW;UAAAJ,EAAA,CAAAG,YAAA,EAAQ;UAC/DH,EAAA,CAAAC,cAAA,wBAQC;UAHCD,EAAA,CAAAK,UAAA,mBAAAgI,oDAAA;YAAArI,EAAA,CAAAO,aAAA,CAAA4H,IAAA;YAAA,MAAAG,GAAA,GAAAtI,EAAA,CAAAkE,WAAA;YAAA,OAASlE,EAAA,CAAAW,WAAA,CAAAmH,GAAA,CAAAlD,SAAA,CAAA7C,WAAA,GAAAuG,GAAA,CAAAjE,KAAA,CAAuC;UAAA,EAAC;UAGlDrE,EAAA,CAAAG,YAAA,EAAW;UACZH,EAAA,CAAAC,cAAA,iBACG;UAAAD,EAAA,CAAAI,MAAA,sEAAoD;UAAAJ,EAAA,CAAAG,YAAA,EACtD;UAEHH,EAAA,CAAAC,cAAA,eAAoB;UAKhBD,EAAA,CAAAK,UAAA,mBAAAkI,kDAAA;YAAA,OAAAT,GAAA,CAAAjD,SAAA,GAAqBiD,GAAA,CAAA/B,oBAAA,EAAsB,GAAG+B,GAAA,CAAApC,SAAA,EAAW;UAAA,EAAC;UAE1D1F,EAAA,CAAAmC,UAAA,KAAAqG,gCAAA,mBAKQ;UACRxI,EAAA,CAAAI,MAAA,IACF;UAAAJ,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAmC,UAAA,KAAAsG,kCAAA,qBAOS;UACXzI,EAAA,CAAAG,YAAA,EAAM;UAQhBH,EAAA,CAAAC,cAAA,eAA0E;UAI1CD,EAAA,CAAAI,MAAA,+CAA6B;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UAC1DH,EAAA,CAAAE,SAAA,kBAKU;UACZF,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,eAAwB;UACtBD,EAAA,CAAAmC,UAAA,KAAAuG,+BAAA,mBA0EM;UACR1I,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,eAA0B;UAMtBD,EAAA,CAAAI,MAAA,gBACF;UAAAJ,EAAA,CAAAG,YAAA,EAAS;;;UA1XXH,EAAA,CAAAa,SAAA,IAAW;UAAXb,EAAA,CAAAqC,UAAA,SAAAyF,GAAA,CAAAlH,KAAA,CAAW;UA8BdZ,EAAA,CAAAa,SAAA,GAAa;UAAbb,EAAA,CAAAqC,UAAA,SAAAyF,GAAA,CAAA/C,OAAA,CAAa;UAmBV/E,EAAA,CAAAa,SAAA,GAAc;UAAdb,EAAA,CAAAqC,UAAA,UAAAyF,GAAA,CAAA/C,OAAA,CAAc;UAiKV/E,EAAA,CAAAa,SAAA,GACF;UADEb,EAAA,CAAAc,kBAAA,MAAAgH,GAAA,CAAAjD,SAAA,kEACF;UAgBM7E,EAAA,CAAAa,SAAA,GAEC;UAFDb,EAAA,CAAA2I,WAAA,gBAAAb,GAAA,CAAAlD,SAAA,CAAA9C,IAAA,KAAAgG,GAAA,CAAAjD,SAAA,IAAAiD,GAAA,CAAAlD,SAAA,CAAA9C,IAAA,SAEC;UALD9B,EAAA,CAAAqC,UAAA,UAAAyF,GAAA,CAAAlD,SAAA,CAAA9C,IAAA,CAAwB;UAkBxB9B,EAAA,CAAAa,SAAA,GAAqC;UAArCb,EAAA,CAAAqC,UAAA,UAAAyF,GAAA,CAAAlD,SAAA,CAAA7C,WAAA,OAAqC;UAarC/B,EAAA,CAAAa,SAAA,GAAuC;UAAvCb,EAAA,CAAAqC,UAAA,cAAAyF,GAAA,CAAAlD,SAAA,CAAA9C,IAAA,IAAAgG,GAAA,CAAA/C,OAAA,CAAuC;UAIpC/E,EAAA,CAAAa,SAAA,GAAa;UAAbb,EAAA,CAAAqC,UAAA,SAAAyF,GAAA,CAAA/C,OAAA,CAAa;UAKhB/E,EAAA,CAAAa,SAAA,GACF;UADEb,EAAA,CAAAc,kBAAA,MAAAgH,GAAA,CAAAjD,SAAA,4CACF;UAEG7E,EAAA,CAAAa,SAAA,GAAe;UAAfb,EAAA,CAAAqC,UAAA,SAAAyF,GAAA,CAAAjD,SAAA,CAAe;UA4BhB7E,EAAA,CAAAa,SAAA,GAAoB;UAApBb,EAAA,CAAAqC,UAAA,SAAAyF,GAAA,CAAAtE,cAAA,CAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}