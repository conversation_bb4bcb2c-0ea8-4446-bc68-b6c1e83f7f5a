{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/equipe.service\";\nimport * as i2 from \"src/app/services/membre.service\";\nexport let EquipeComponent = /*#__PURE__*/(() => {\n  class EquipeComponent {\n    constructor(equipeService, membreService) {\n      this.equipeService = equipeService;\n      this.membreService = membreService;\n      this.equipes = [];\n      this.newEquipe = {\n        name: '',\n        description: ''\n      };\n      this.selectedEquipe = null;\n      this.isEditing = false;\n      this.membres = [];\n      this.loading = false;\n      this.error = '';\n    }\n    ngOnInit() {\n      this.loadEquipes();\n      this.loadMembres();\n    }\n    loadEquipes() {\n      this.loading = true;\n      this.equipeService.getEquipes().subscribe({\n        next: data => {\n          console.log('Loaded equipes:', data);\n          this.equipes = data;\n          this.loading = false;\n        },\n        error: error => {\n          console.error('Error loading equipes:', error);\n          this.error = 'Erreur lors du chargement des équipes: ' + error.message;\n          this.loading = false;\n        }\n      });\n    }\n    loadMembres() {\n      this.loading = true;\n      this.membreService.getMembres().subscribe({\n        next: data => {\n          console.log('Loaded membres:', data);\n          this.membres = data;\n          this.loading = false;\n        },\n        error: error => {\n          console.error('Error loading membres:', error);\n          this.error = 'Erreur lors du chargement des membres: ' + error.message;\n          this.loading = false;\n        }\n      });\n    }\n    addEquipe() {\n      console.log('Adding equipe:', this.newEquipe);\n      if (!this.newEquipe.name) {\n        console.error('Team name is required');\n        this.error = 'Le nom de l\\'équipe est requis';\n        return;\n      }\n      this.loading = true;\n      this.error = '';\n      this.equipeService.addEquipe(this.newEquipe).subscribe({\n        next: response => {\n          console.log('Equipe added successfully:', response);\n          this.loadEquipes();\n          this.newEquipe = {\n            name: '',\n            description: ''\n          }; // Clear input\n          this.loading = false;\n          // Afficher un message de succès temporaire\n          const successMessage = 'Équipe créée avec succès!';\n          this.error = ''; // Effacer les erreurs précédentes\n          alert(successMessage);\n        },\n        error: error => {\n          console.error('Error adding equipe:', error);\n          this.error = 'Erreur lors de la création de l\\'équipe: ' + (error.error?.message || error.message || 'Unknown error');\n          this.loading = false;\n        }\n      });\n    }\n    editEquipe(equipe) {\n      this.isEditing = true;\n      // Créer une copie profonde pour éviter de modifier l'objet original\n      this.newEquipe = {\n        _id: equipe._id,\n        name: equipe.name || '',\n        description: equipe.description || '',\n        admin: equipe.admin,\n        members: equipe.members ? [...equipe.members] : []\n      };\n    }\n    cancelEdit() {\n      this.isEditing = false;\n      this.newEquipe = {\n        name: '',\n        description: ''\n      };\n      this.error = ''; // Effacer les erreurs\n    }\n\n    updateSelectedEquipe() {\n      if (!this.newEquipe.name) {\n        console.error('Team name is required');\n        this.error = 'Le nom de l\\'équipe est requis';\n        return;\n      }\n      if (this.newEquipe._id) {\n        this.loading = true;\n        this.error = '';\n        this.equipeService.updateEquipe(this.newEquipe._id, this.newEquipe).subscribe({\n          next: updatedEquipe => {\n            console.log('Team updated successfully:', updatedEquipe);\n            this.loadEquipes();\n            this.isEditing = false;\n            this.newEquipe = {\n              name: '',\n              description: ''\n            };\n            this.loading = false;\n            // Afficher un message de succès temporaire\n            const successMessage = 'Équipe mise à jour avec succès!';\n            alert(successMessage);\n          },\n          error: error => {\n            console.error('Error updating team:', error);\n            this.error = 'Erreur lors de la mise à jour de l\\'équipe: ' + (error.error?.message || error.message || 'Unknown error');\n            this.loading = false;\n          }\n        });\n      } else {\n        this.error = 'ID de l\\'équipe manquant pour la mise à jour';\n      }\n    }\n    deleteEquipe(id) {\n      if (!id) {\n        console.error('ID is undefined');\n        this.error = 'ID de l\\'équipe non défini';\n        return;\n      }\n      if (confirm('Êtes-vous sûr de vouloir supprimer cette équipe? Cette action est irréversible.')) {\n        this.loading = true;\n        this.error = '';\n        this.equipeService.deleteEquipe(id).subscribe({\n          next: response => {\n            console.log('Team deleted successfully:', response);\n            // Si l'équipe en cours d'édition est celle qui vient d'être supprimée, réinitialiser le formulaire\n            if (this.isEditing && this.newEquipe._id === id) {\n              this.isEditing = false;\n              this.newEquipe = {\n                name: '',\n                description: ''\n              };\n            }\n            this.loadEquipes();\n            this.loading = false;\n            // Afficher un message de succès\n            alert('Équipe supprimée avec succès');\n          },\n          error: error => {\n            console.error('Error deleting team:', error);\n            this.error = 'Erreur lors de la suppression de l\\'équipe: ' + (error.error?.message || error.message || 'Unknown error');\n            this.loading = false;\n          }\n        });\n      }\n    }\n    showMembreModal(equipe) {\n      this.selectedEquipe = equipe;\n      // Ouvrir le modal avec Bootstrap 5\n      const modalRef = document.getElementById('membreModal');\n      if (modalRef) {\n        try {\n          // Ensure Bootstrap is properly loaded\n          if (typeof window !== 'undefined' && window.bootstrap) {\n            const modal = new window.bootstrap.Modal(modalRef);\n            modal.show();\n          } else {\n            console.error('Bootstrap is not loaded properly');\n            alert('Erreur: Bootstrap n\\'est pas chargé correctement');\n          }\n        } catch (error) {\n          console.error('Error showing modal:', error);\n        }\n      } else {\n        console.error('Modal element not found');\n      }\n    }\n    addMembreToEquipe(teamId, membreId) {\n      if (!teamId) {\n        console.error('Team ID is undefined');\n        alert('ID de l\\'équipe non défini');\n        return;\n      }\n      if (!membreId || membreId.trim() === '') {\n        console.error('Member ID is empty');\n        alert('L\\'ID du membre est requis');\n        return;\n      }\n      this.loading = true;\n      // Create a proper Membre object that matches what the API expects\n      const membre = {\n        id: membreId\n      };\n      this.equipeService.addMembreToEquipe(teamId, membre).subscribe({\n        next: response => {\n          console.log('Member added successfully:', response);\n          this.loadEquipes();\n          this.loading = false;\n          // Afficher un message de succès\n          alert('Membre ajouté avec succès à l\\'équipe');\n        },\n        error: error => {\n          console.error('Error adding member:', error);\n          this.error = 'Erreur lors de l\\'ajout du membre: ' + (error.error?.message || error.message || 'Unknown error');\n          alert(this.error);\n          this.loading = false;\n        }\n      });\n    }\n    removeMembreFromEquipe(teamId, membreId) {\n      if (!teamId) {\n        console.error('Team ID is undefined');\n        alert('ID de l\\'équipe non défini');\n        return;\n      }\n      if (!membreId) {\n        console.error('Member ID is undefined');\n        alert('ID du membre non défini');\n        return;\n      }\n      if (confirm('Êtes-vous sûr de vouloir retirer ce membre de l\\'équipe?')) {\n        this.loading = true;\n        this.equipeService.removeMembreFromEquipe(teamId, membreId).subscribe({\n          next: response => {\n            console.log('Member removed successfully:', response);\n            this.loadEquipes();\n            this.loading = false;\n            // Si l'équipe sélectionnée est celle dont on vient de retirer un membre, mettre à jour l'équipe sélectionnée\n            if (this.selectedEquipe && this.selectedEquipe._id === teamId) {\n              const updatedEquipe = this.equipes.find(e => e._id === teamId);\n              if (updatedEquipe) {\n                this.selectedEquipe = updatedEquipe;\n              }\n            }\n          },\n          error: error => {\n            console.error('Error removing member:', error);\n            this.error = 'Erreur lors de la suppression du membre: ' + (error.error?.message || error.message || 'Unknown error');\n            alert(this.error);\n            this.loading = false;\n          }\n        });\n      }\n    }\n    static {\n      this.ɵfac = function EquipeComponent_Factory(t) {\n        return new (t || EquipeComponent)(i0.ɵɵdirectiveInject(i1.EquipeService), i0.ɵɵdirectiveInject(i2.MembreService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: EquipeComponent,\n        selectors: [[\"app-equipe\"]],\n        decls: 2,\n        vars: 0,\n        template: function EquipeComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"p\");\n            i0.ɵɵtext(1, \"equipe works!\");\n            i0.ɵɵelementEnd();\n          }\n        },\n        styles: [\"var[_ngcontent-%COMP%]   resource[_ngcontent-%COMP%]; (()[_ngcontent-%COMP%]   =[_ngcontent-%COMP%] >  {// webpackBootstrap var __webpack_modules__ = ({363: (() => {throw new Error('Module build failed (from ./node_modules/postcss-loader/dist/cjs.js):nnSyntaxErrornn(64:9) C:\\\\\\\\Users\\\\\\\\<USER>\\\\\\\\OneDrive\\\\\\\\Bureau\\\\\\\\Project PI\\\\\\\\devBridge\\\\\\\\frontend\\\\\\\\src\\\\\\\\app\\\\\\\\views\\\\\\\\front\\\\\\\\equipes\\\\\\\\equipe\\\\\\\\equipe.component.css Unknown wordnn u001b[90m 62 | u001b[39m        <div class=u001b[32m\\\"card-header bg-primary text-white\\\"u001b[39m>n u001b[90m 63 | u001b[39m          <h3 class=u001b[32m\\\"mb-0\\\"u001b[39m>u001b[33m{u001b[39mu001b[33m{u001b[39m isEditing ? u001b[32m\\\\'Modifier une \\\\e9quipe\\\\'u001b[39m u001b[33m:u001b[39m u001b[32m\\\\'Cr\\\\e9 er une \\\\e9quipe\\\\'u001b[39m u001b[33m}u001b[39mu001b[33m}u001b[39m</h3>nu001b[1mu001b[31m>u001b[39mu001b[22mu001b[90m 64 | u001b[39m        </div>n u001b[90m    | u001b[39m        u001b[1mu001b[31m^u001b[39mu001b[22mn u001b[90m 65 | u001b[39m        <div class=u001b[32m\\\"card-body\\\"u001b[39m>n u001b[90m 66 | u001b[39m          <form>n');})}); // startup // Load entry module and return exports // This entry module doesn\\\"t tell about it\\\"s top-level declarations so it can't be inlined\\nvar __webpack_exports__ = {}; __webpack_modules__[363](); resource = __webpack_exports__;})()[_ngcontent-%COMP%]   ;[_ngcontent-%COMP%]{}\"]\n      });\n    }\n  }\n  return EquipeComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}