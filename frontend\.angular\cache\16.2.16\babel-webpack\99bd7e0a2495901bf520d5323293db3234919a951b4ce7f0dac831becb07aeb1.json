{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { moveItemInArray, transferArrayItem } from '@angular/cdk/drag-drop';\nimport { finalize } from 'rxjs/operators';\nexport let TaskListComponent = class TaskListComponent {\n  constructor(taskService, equipeService, userService, route, router, notificationService) {\n    this.taskService = taskService;\n    this.equipeService = equipeService;\n    this.userService = userService;\n    this.route = route;\n    this.router = router;\n    this.notificationService = notificationService;\n    this.tasks = [];\n    this.teamId = null;\n    this.team = null;\n    this.loading = false;\n    this.error = null;\n    this.users = [];\n    this.editingTask = null;\n    this.showTaskForm = false;\n    // Filtres\n    this.statusFilter = 'all';\n    this.priorityFilter = 'all';\n    this.searchTerm = '';\n  }\n  ngOnInit() {\n    // Initialiser la nouvelle tâche\n    this.newTask = this.initializeNewTask();\n    this.route.paramMap.subscribe(params => {\n      this.teamId = params.get('id');\n      if (this.teamId) {\n        this.loadTeamDetails(this.teamId);\n        this.loadTasks(this.teamId);\n        this.loadUsers();\n      } else {\n        this.error = \"ID d'équipe manquant\";\n        this.notificationService.showError(\"ID d'équipe manquant\");\n      }\n    });\n  }\n  loadTeamDetails(teamId) {\n    this.loading = true;\n    // Utiliser les données de test si l'API n'est pas disponible\n    const useMockData = false; // Mettre à true pour utiliser les données de test\n    if (useMockData) {\n      // Données de test pour simuler les détails de l'équipe\n      const mockTeam = {\n        _id: teamId,\n        name: 'Équipe ' + teamId,\n        description: \"Description de l'équipe \" + teamId,\n        admin: 'admin123',\n        members: []\n      };\n      setTimeout(() => {\n        this.team = mockTeam;\n        this.loading = false;\n        console.log(\"Détails de l'équipe chargés (mock):\", this.team);\n      }, 300);\n    } else {\n      // Utiliser l'API réelle\n      this.equipeService.getEquipe(teamId).pipe(finalize(() => this.loading = false)).subscribe({\n        next: data => {\n          this.team = data;\n          console.log(\"Détails de l'équipe chargés depuis l'API:\", this.team);\n        },\n        error: error => {\n          console.error(\"Erreur lors du chargement des détails de l'équipe:\", error);\n          this.error = \"Impossible de charger les détails de l'équipe\";\n          this.notificationService.showError(\"Erreur lors du chargement des détails de l'équipe\");\n          // Fallback aux données de test en cas d'erreur\n          const mockTeam = {\n            _id: teamId,\n            name: 'Équipe ' + teamId + ' (fallback)',\n            description: \"Description de l'équipe \" + teamId,\n            admin: 'admin123',\n            members: []\n          };\n          this.team = mockTeam;\n        }\n      });\n    }\n  }\n  loadTasks(teamId) {\n    this.loading = true;\n    // Utiliser les données de test si l'API n'est pas disponible\n    const useMockData = false; // Mettre à true pour utiliser les données de test\n    if (useMockData) {\n      // Données de test pour simuler les tâches\n      const mockTasks = [{\n        _id: '1',\n        title: 'Tâche 1',\n        description: 'Description de la tâche 1',\n        status: 'todo',\n        priority: 'high',\n        teamId: teamId\n      }, {\n        _id: '2',\n        title: 'Tâche 2',\n        description: 'Description de la tâche 2',\n        status: 'todo',\n        priority: 'medium',\n        teamId: teamId\n      }, {\n        _id: '3',\n        title: 'Tâche 3',\n        description: 'Description de la tâche 3',\n        status: 'in-progress',\n        priority: 'high',\n        teamId: teamId\n      }, {\n        _id: '4',\n        title: 'Tâche 4',\n        description: 'Description de la tâche 4',\n        status: 'done',\n        priority: 'low',\n        teamId: teamId\n      }];\n      setTimeout(() => {\n        this.tasks = mockTasks;\n        this.sortTasks();\n        this.loading = false;\n        console.log('Tâches chargées (mock):', this.tasks);\n      }, 500);\n    } else {\n      // Utiliser l'API réelle\n      this.taskService.getTasksByTeam(teamId).pipe(finalize(() => this.loading = false)).subscribe({\n        next: data => {\n          this.tasks = data;\n          this.sortTasks();\n          console.log(\"Tâches chargées depuis l'API:\", this.tasks);\n        },\n        error: error => {\n          console.error('Erreur lors du chargement des tâches:', error);\n          this.error = 'Impossible de charger les tâches';\n          this.notificationService.showError('Erreur lors du chargement des tâches');\n          // Fallback aux données de test en cas d'erreur\n          const mockTasks = [{\n            _id: '1',\n            title: 'Tâche 1 (fallback)',\n            description: 'Description de la tâche 1',\n            status: 'todo',\n            priority: 'high',\n            teamId: teamId\n          }, {\n            _id: '2',\n            title: 'Tâche 2 (fallback)',\n            description: 'Description de la tâche 2',\n            status: 'todo',\n            priority: 'medium',\n            teamId: teamId\n          }];\n          this.tasks = mockTasks;\n          this.sortTasks();\n          console.log('Tâches chargées (fallback):', this.tasks);\n        }\n      });\n    }\n  }\n  // Gestion du glisser-déposer\n  drop(event) {\n    if (event.previousContainer === event.container) {\n      // Déplacement dans la même liste\n      moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);\n    } else {\n      // Déplacement entre listes\n      transferArrayItem(event.previousContainer.data, event.container.data, event.previousIndex, event.currentIndex);\n      // Mettre à jour le statut de la tâche\n      const task = event.container.data[event.currentIndex];\n      let newStatus;\n      if (event.container.id === 'todo-list') {\n        newStatus = 'todo';\n      } else if (event.container.id === 'in-progress-list') {\n        newStatus = 'in-progress';\n      } else {\n        newStatus = 'done';\n      }\n      if (task._id && task.status !== newStatus) {\n        task.status = newStatus;\n        this.updateTaskStatus(task, newStatus);\n      }\n    }\n  }\n  loadUsers() {\n    // Utiliser les données de test si l'API n'est pas disponible\n    const useMockData = false; // Mettre à true pour utiliser les données de test\n    if (useMockData) {\n      // Données de test pour simuler les utilisateurs\n      const mockUsers = [{\n        _id: 'user1',\n        name: 'John Doe',\n        email: '<EMAIL>',\n        role: 'admin'\n      }, {\n        _id: 'user2',\n        name: 'Jane Smith',\n        email: '<EMAIL>',\n        role: 'member'\n      }];\n      setTimeout(() => {\n        this.users = mockUsers;\n        console.log('Utilisateurs chargés (mock):', this.users);\n      }, 400);\n    } else {\n      // Utiliser l'API réelle\n      this.userService.getUsers().subscribe({\n        next: data => {\n          this.users = data;\n          console.log(\"Utilisateurs chargés depuis l'API:\", this.users);\n        },\n        error: error => {\n          console.error('Erreur lors du chargement des utilisateurs:', error);\n          // Fallback aux données de test en cas d'erreur\n          const mockUsers = [{\n            _id: 'user1',\n            name: 'John Doe (fallback)',\n            email: '<EMAIL>',\n            role: 'admin'\n          }, {\n            _id: 'user2',\n            name: 'Jane Smith (fallback)',\n            email: '<EMAIL>',\n            role: 'member'\n          }];\n          this.users = mockUsers;\n          console.log('Utilisateurs chargés (fallback):', this.users);\n        }\n      });\n    }\n  }\n  getUserName(userId) {\n    const user = this.users.find(u => u._id === userId || u.id === userId);\n    if (user) {\n      if (user.firstName && user.lastName) {\n        return `${user.firstName} ${user.lastName}`;\n      } else if (user.name) {\n        return user.name;\n      }\n    }\n    return 'Utilisateur inconnu';\n  }\n  createTask() {\n    if (!this.teamId) {\n      this.notificationService.showError(\"ID d'équipe manquant\");\n      return;\n    }\n    this.newTask.teamId = this.teamId;\n    this.loading = true;\n    this.taskService.createTask(this.newTask).pipe(finalize(() => this.loading = false)).subscribe({\n      next: data => {\n        this.tasks.push(data);\n        this.sortTasks();\n        this.newTask = this.initializeNewTask();\n        this.showTaskForm = false;\n        this.notificationService.showSuccess('Tâche créée avec succès');\n      },\n      error: error => {\n        console.error('Erreur lors de la création de la tâche:', error);\n        this.notificationService.showError('Erreur lors de la création de la tâche');\n      }\n    });\n  }\n  updateTask() {\n    if (!this.editingTask || !this.editingTask._id) {\n      this.notificationService.showError('Tâche invalide');\n      return;\n    }\n    this.loading = true;\n    this.taskService.updateTask(this.editingTask._id, this.editingTask).pipe(finalize(() => this.loading = false)).subscribe({\n      next: data => {\n        const index = this.tasks.findIndex(t => t._id === data._id);\n        if (index !== -1) {\n          this.tasks[index] = data;\n        }\n        this.editingTask = null;\n        this.notificationService.showSuccess('Tâche mise à jour avec succès');\n      },\n      error: error => {\n        console.error('Erreur lors de la mise à jour de la tâche:', error);\n        this.notificationService.showError('Erreur lors de la mise à jour de la tâche');\n      }\n    });\n  }\n  deleteTask(id) {\n    if (confirm('Êtes-vous sûr de vouloir supprimer cette tâche ?')) {\n      this.loading = true;\n      this.taskService.deleteTask(id).pipe(finalize(() => this.loading = false)).subscribe({\n        next: () => {\n          this.tasks = this.tasks.filter(t => t._id !== id);\n          this.notificationService.showSuccess('Tâche supprimée avec succès');\n        },\n        error: error => {\n          console.error('Erreur lors de la suppression de la tâche:', error);\n          this.notificationService.showError('Erreur lors de la suppression de la tâche');\n        }\n      });\n    }\n  }\n  updateTaskStatus(task, status) {\n    if (!task._id) return;\n    this.loading = true;\n    this.taskService.updateTaskStatus(task._id, status).pipe(finalize(() => this.loading = false)).subscribe({\n      next: data => {\n        const index = this.tasks.findIndex(t => t._id === data._id);\n        if (index !== -1) {\n          this.tasks[index] = data;\n        }\n        this.notificationService.showSuccess('Statut de la tâche mis à jour');\n      },\n      error: error => {\n        console.error('Erreur lors de la mise à jour du statut:', error);\n        this.notificationService.showError('Erreur lors de la mise à jour du statut');\n      }\n    });\n  }\n  editTask(task) {\n    this.editingTask = {\n      ...task\n    };\n  }\n  cancelEdit() {\n    this.editingTask = null;\n  }\n  toggleTaskForm() {\n    this.showTaskForm = !this.showTaskForm;\n    if (this.showTaskForm) {\n      this.newTask = this.initializeNewTask();\n    }\n  }\n  initializeNewTask() {\n    return {\n      title: '',\n      description: '',\n      status: 'todo',\n      priority: 'medium',\n      teamId: this.teamId || '',\n      dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // Par défaut, une semaine à partir d'aujourd'hui\n    };\n  }\n\n  sortTasks() {\n    // Trier par priorité (high > medium > low) puis par statut (todo > in-progress > done)\n    this.tasks.sort((a, b) => {\n      const priorityOrder = {\n        high: 0,\n        medium: 1,\n        low: 2\n      };\n      const statusOrder = {\n        todo: 0,\n        'in-progress': 1,\n        done: 2\n      };\n      // D'abord par priorité\n      if (priorityOrder[a.priority] !== priorityOrder[b.priority]) {\n        return priorityOrder[a.priority] - priorityOrder[b.priority];\n      }\n      // Ensuite par statut\n      return statusOrder[a.status] - statusOrder[b.status];\n    });\n  }\n  // Méthodes de filtrage\n  filterTasks() {\n    return this.tasks.filter(task => {\n      // Filtre par statut\n      if (this.statusFilter !== 'all' && task.status !== this.statusFilter) {\n        return false;\n      }\n      // Filtre par priorité\n      if (this.priorityFilter !== 'all' && task.priority !== this.priorityFilter) {\n        return false;\n      }\n      // Filtre par terme de recherche\n      if (this.searchTerm && !task.title.toLowerCase().includes(this.searchTerm.toLowerCase())) {\n        return false;\n      }\n      return true;\n    });\n  }\n  // Méthodes pour obtenir les tâches par statut\n  getTodoTasks() {\n    return this.tasks.filter(task => task.status === 'todo' && (this.priorityFilter === 'all' || task.priority === this.priorityFilter) && (!this.searchTerm || task.title.toLowerCase().includes(this.searchTerm.toLowerCase())));\n  }\n  getInProgressTasks() {\n    return this.tasks.filter(task => task.status === 'in-progress' && (this.priorityFilter === 'all' || task.priority === this.priorityFilter) && (!this.searchTerm || task.title.toLowerCase().includes(this.searchTerm.toLowerCase())));\n  }\n  getDoneTasks() {\n    return this.tasks.filter(task => task.status === 'done' && (this.priorityFilter === 'all' || task.priority === this.priorityFilter) && (!this.searchTerm || task.title.toLowerCase().includes(this.searchTerm.toLowerCase())));\n  }\n  // Méthodes pour compter les tâches par statut\n  getTodoTasksCount() {\n    return this.tasks.filter(task => task.status === 'todo').length;\n  }\n  getInProgressTasksCount() {\n    return this.tasks.filter(task => task.status === 'in-progress').length;\n  }\n  getDoneTasksCount() {\n    return this.tasks.filter(task => task.status === 'done').length;\n  }\n  navigateBack() {\n    this.router.navigate(['/liste']);\n  }\n};\nTaskListComponent = __decorate([Component({\n  selector: 'app-task-list',\n  templateUrl: './task-list.component.html',\n  styleUrls: ['./task-list.component.css']\n})], TaskListComponent);", "map": {"version": 3, "names": ["Component", "moveItemInArray", "transferArrayItem", "finalize", "TaskListComponent", "constructor", "taskService", "equipeService", "userService", "route", "router", "notificationService", "tasks", "teamId", "team", "loading", "error", "users", "editingTask", "showTaskForm", "statusFilter", "priorityFilter", "searchTerm", "ngOnInit", "newTask", "initializeNewTask", "paramMap", "subscribe", "params", "get", "loadTeamDetails", "loadTasks", "loadUsers", "showError", "useMockData", "mockTeam", "_id", "name", "description", "admin", "members", "setTimeout", "console", "log", "getEquipe", "pipe", "next", "data", "mockTasks", "title", "status", "priority", "sortTasks", "getTasksByTeam", "drop", "event", "previousContainer", "container", "previousIndex", "currentIndex", "task", "newStatus", "id", "updateTaskStatus", "mockUsers", "email", "role", "getUsers", "getUserName", "userId", "user", "find", "u", "firstName", "lastName", "createTask", "push", "showSuccess", "updateTask", "index", "findIndex", "t", "deleteTask", "confirm", "filter", "editTask", "cancelEdit", "toggleTaskForm", "dueDate", "Date", "now", "sort", "a", "b", "priorityOrder", "high", "medium", "low", "statusOrder", "todo", "done", "filterTasks", "toLowerCase", "includes", "getTodoTasks", "getInProgressTasks", "getDoneTasks", "getTodoTasksCount", "length", "getInProgressTasksCount", "getDoneTasksCount", "navigateBack", "navigate", "__decorate", "selector", "templateUrl", "styleUrls"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\equipes\\task-list\\task-list.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport {\n  CdkDragDrop,\n  moveItemInArray,\n  transferArrayItem,\n} from '@angular/cdk/drag-drop';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { TaskService } from 'src/app/services/task.service';\nimport { EquipeService } from 'src/app/services/equipe.service';\nimport { UserService } from 'src/app/services/user.service';\nimport { NotificationService } from 'src/app/services/notification.service';\nimport { Task } from 'src/app/models/task.model';\nimport { Equipe } from 'src/app//models/equipe.model';\nimport { User } from 'src/app/models/user.model';\nimport { finalize } from 'rxjs/operators';\n\n@Component({\n  selector: 'app-task-list',\n  templateUrl: './task-list.component.html',\n  styleUrls: ['./task-list.component.css'],\n})\nexport class TaskListComponent implements OnInit {\n  tasks: Task[] = [];\n  teamId: string | null = null;\n  team: Equipe | null = null;\n  loading = false;\n  error: string | null = null;\n  users: User[] = [];\n  newTask!: Task;\n  editingTask: Task | null = null;\n  showTaskForm = false;\n\n  // Filtres\n  statusFilter: string = 'all';\n  priorityFilter: string = 'all';\n  searchTerm: string = '';\n\n  constructor(\n    private taskService: TaskService,\n    private equipeService: EquipeService,\n    private userService: UserService,\n    private route: ActivatedRoute,\n    private router: Router,\n    private notificationService: NotificationService\n  ) {}\n\n  ngOnInit(): void {\n    // Initialiser la nouvelle tâche\n    this.newTask = this.initializeNewTask();\n\n    this.route.paramMap.subscribe((params) => {\n      this.teamId = params.get('id');\n      if (this.teamId) {\n        this.loadTeamDetails(this.teamId);\n        this.loadTasks(this.teamId);\n        this.loadUsers();\n      } else {\n        this.error = \"ID d'équipe manquant\";\n        this.notificationService.showError(\"ID d'équipe manquant\");\n      }\n    });\n  }\n\n  loadTeamDetails(teamId: string): void {\n    this.loading = true;\n\n    // Utiliser les données de test si l'API n'est pas disponible\n    const useMockData = false; // Mettre à true pour utiliser les données de test\n\n    if (useMockData) {\n      // Données de test pour simuler les détails de l'équipe\n      const mockTeam: Equipe = {\n        _id: teamId,\n        name: 'Équipe ' + teamId,\n        description: \"Description de l'équipe \" + teamId,\n        admin: 'admin123',\n        members: [],\n      };\n\n      setTimeout(() => {\n        this.team = mockTeam;\n        this.loading = false;\n        console.log(\"Détails de l'équipe chargés (mock):\", this.team);\n      }, 300);\n    } else {\n      // Utiliser l'API réelle\n      this.equipeService\n        .getEquipe(teamId)\n        .pipe(finalize(() => (this.loading = false)))\n        .subscribe({\n          next: (data) => {\n            this.team = data;\n            console.log(\"Détails de l'équipe chargés depuis l'API:\", this.team);\n          },\n          error: (error) => {\n            console.error(\n              \"Erreur lors du chargement des détails de l'équipe:\",\n              error\n            );\n            this.error = \"Impossible de charger les détails de l'équipe\";\n            this.notificationService.showError(\n              \"Erreur lors du chargement des détails de l'équipe\"\n            );\n\n            // Fallback aux données de test en cas d'erreur\n            const mockTeam: Equipe = {\n              _id: teamId,\n              name: 'Équipe ' + teamId + ' (fallback)',\n              description: \"Description de l'équipe \" + teamId,\n              admin: 'admin123',\n              members: [],\n            };\n\n            this.team = mockTeam;\n          },\n        });\n    }\n  }\n\n  loadTasks(teamId: string): void {\n    this.loading = true;\n\n    // Utiliser les données de test si l'API n'est pas disponible\n    const useMockData = false; // Mettre à true pour utiliser les données de test\n\n    if (useMockData) {\n      // Données de test pour simuler les tâches\n      const mockTasks: Task[] = [\n        {\n          _id: '1',\n          title: 'Tâche 1',\n          description: 'Description de la tâche 1',\n          status: 'todo',\n          priority: 'high',\n          teamId: teamId,\n        },\n        {\n          _id: '2',\n          title: 'Tâche 2',\n          description: 'Description de la tâche 2',\n          status: 'todo',\n          priority: 'medium',\n          teamId: teamId,\n        },\n        {\n          _id: '3',\n          title: 'Tâche 3',\n          description: 'Description de la tâche 3',\n          status: 'in-progress',\n          priority: 'high',\n          teamId: teamId,\n        },\n        {\n          _id: '4',\n          title: 'Tâche 4',\n          description: 'Description de la tâche 4',\n          status: 'done',\n          priority: 'low',\n          teamId: teamId,\n        },\n      ];\n\n      setTimeout(() => {\n        this.tasks = mockTasks;\n        this.sortTasks();\n        this.loading = false;\n        console.log('Tâches chargées (mock):', this.tasks);\n      }, 500);\n    } else {\n      // Utiliser l'API réelle\n      this.taskService\n        .getTasksByTeam(teamId)\n        .pipe(finalize(() => (this.loading = false)))\n        .subscribe({\n          next: (data: Task[]) => {\n            this.tasks = data;\n            this.sortTasks();\n            console.log(\"Tâches chargées depuis l'API:\", this.tasks);\n          },\n          error: (error: any) => {\n            console.error('Erreur lors du chargement des tâches:', error);\n            this.error = 'Impossible de charger les tâches';\n            this.notificationService.showError(\n              'Erreur lors du chargement des tâches'\n            );\n\n            // Fallback aux données de test en cas d'erreur\n            const mockTasks: Task[] = [\n              {\n                _id: '1',\n                title: 'Tâche 1 (fallback)',\n                description: 'Description de la tâche 1',\n                status: 'todo',\n                priority: 'high',\n                teamId: teamId,\n              },\n              {\n                _id: '2',\n                title: 'Tâche 2 (fallback)',\n                description: 'Description de la tâche 2',\n                status: 'todo',\n                priority: 'medium',\n                teamId: teamId,\n              },\n            ];\n\n            this.tasks = mockTasks;\n            this.sortTasks();\n            console.log('Tâches chargées (fallback):', this.tasks);\n          },\n        });\n    }\n  }\n\n  // Gestion du glisser-déposer\n  drop(event: CdkDragDrop<Task[]>) {\n    if (event.previousContainer === event.container) {\n      // Déplacement dans la même liste\n      moveItemInArray(\n        event.container.data,\n        event.previousIndex,\n        event.currentIndex\n      );\n    } else {\n      // Déplacement entre listes\n      transferArrayItem(\n        event.previousContainer.data,\n        event.container.data,\n        event.previousIndex,\n        event.currentIndex\n      );\n\n      // Mettre à jour le statut de la tâche\n      const task = event.container.data[event.currentIndex];\n      let newStatus: 'todo' | 'in-progress' | 'done';\n\n      if (event.container.id === 'todo-list') {\n        newStatus = 'todo';\n      } else if (event.container.id === 'in-progress-list') {\n        newStatus = 'in-progress';\n      } else {\n        newStatus = 'done';\n      }\n\n      if (task._id && task.status !== newStatus) {\n        task.status = newStatus;\n        this.updateTaskStatus(task, newStatus);\n      }\n    }\n  }\n\n  loadUsers(): void {\n    // Utiliser les données de test si l'API n'est pas disponible\n    const useMockData = false; // Mettre à true pour utiliser les données de test\n\n    if (useMockData) {\n      // Données de test pour simuler les utilisateurs\n      const mockUsers: User[] = [\n        {\n          _id: 'user1',\n          name: 'John Doe',\n          email: '<EMAIL>',\n          role: 'admin',\n        },\n        {\n          _id: 'user2',\n          name: 'Jane Smith',\n          email: '<EMAIL>',\n          role: 'member',\n        },\n      ];\n\n      setTimeout(() => {\n        this.users = mockUsers;\n        console.log('Utilisateurs chargés (mock):', this.users);\n      }, 400);\n    } else {\n      // Utiliser l'API réelle\n      this.userService.getUsers().subscribe({\n        next: (data: User[]) => {\n          this.users = data;\n          console.log(\"Utilisateurs chargés depuis l'API:\", this.users);\n        },\n        error: (error: any) => {\n          console.error('Erreur lors du chargement des utilisateurs:', error);\n\n          // Fallback aux données de test en cas d'erreur\n          const mockUsers: User[] = [\n            {\n              _id: 'user1',\n              name: 'John Doe (fallback)',\n              email: '<EMAIL>',\n              role: 'admin',\n            },\n            {\n              _id: 'user2',\n              name: 'Jane Smith (fallback)',\n              email: '<EMAIL>',\n              role: 'member',\n            },\n          ];\n\n          this.users = mockUsers;\n          console.log('Utilisateurs chargés (fallback):', this.users);\n        },\n      });\n    }\n  }\n\n  getUserName(userId: string): string {\n    const user = this.users.find((u) => u._id === userId || u.id === userId);\n    if (user) {\n      if (user.firstName && user.lastName) {\n        return `${user.firstName} ${user.lastName}`;\n      } else if (user.name) {\n        return user.name;\n      }\n    }\n    return 'Utilisateur inconnu';\n  }\n\n  createTask(): void {\n    if (!this.teamId) {\n      this.notificationService.showError(\"ID d'équipe manquant\");\n      return;\n    }\n\n    this.newTask.teamId = this.teamId;\n\n    this.loading = true;\n    this.taskService\n      .createTask(this.newTask)\n      .pipe(finalize(() => (this.loading = false)))\n      .subscribe({\n        next: (data: Task) => {\n          this.tasks.push(data);\n          this.sortTasks();\n          this.newTask = this.initializeNewTask();\n          this.showTaskForm = false;\n          this.notificationService.showSuccess('Tâche créée avec succès');\n        },\n        error: (error: any) => {\n          console.error('Erreur lors de la création de la tâche:', error);\n          this.notificationService.showError(\n            'Erreur lors de la création de la tâche'\n          );\n        },\n      });\n  }\n\n  updateTask(): void {\n    if (!this.editingTask || !this.editingTask._id) {\n      this.notificationService.showError('Tâche invalide');\n      return;\n    }\n\n    this.loading = true;\n    this.taskService\n      .updateTask(this.editingTask._id, this.editingTask)\n      .pipe(finalize(() => (this.loading = false)))\n      .subscribe({\n        next: (data: Task) => {\n          const index = this.tasks.findIndex((t) => t._id === data._id);\n          if (index !== -1) {\n            this.tasks[index] = data;\n          }\n          this.editingTask = null;\n          this.notificationService.showSuccess('Tâche mise à jour avec succès');\n        },\n        error: (error: any) => {\n          console.error('Erreur lors de la mise à jour de la tâche:', error);\n          this.notificationService.showError(\n            'Erreur lors de la mise à jour de la tâche'\n          );\n        },\n      });\n  }\n\n  deleteTask(id: string): void {\n    if (confirm('Êtes-vous sûr de vouloir supprimer cette tâche ?')) {\n      this.loading = true;\n      this.taskService\n        .deleteTask(id)\n        .pipe(finalize(() => (this.loading = false)))\n        .subscribe({\n          next: () => {\n            this.tasks = this.tasks.filter((t) => t._id !== id);\n            this.notificationService.showSuccess('Tâche supprimée avec succès');\n          },\n          error: (error: any) => {\n            console.error('Erreur lors de la suppression de la tâche:', error);\n            this.notificationService.showError(\n              'Erreur lors de la suppression de la tâche'\n            );\n          },\n        });\n    }\n  }\n\n  updateTaskStatus(task: Task, status: 'todo' | 'in-progress' | 'done'): void {\n    if (!task._id) return;\n\n    this.loading = true;\n    this.taskService\n      .updateTaskStatus(task._id, status)\n      .pipe(finalize(() => (this.loading = false)))\n      .subscribe({\n        next: (data: Task) => {\n          const index = this.tasks.findIndex((t) => t._id === data._id);\n          if (index !== -1) {\n            this.tasks[index] = data;\n          }\n          this.notificationService.showSuccess('Statut de la tâche mis à jour');\n        },\n        error: (error: any) => {\n          console.error('Erreur lors de la mise à jour du statut:', error);\n          this.notificationService.showError(\n            'Erreur lors de la mise à jour du statut'\n          );\n        },\n      });\n  }\n\n  editTask(task: Task): void {\n    this.editingTask = { ...task };\n  }\n\n  cancelEdit(): void {\n    this.editingTask = null;\n  }\n\n  toggleTaskForm(): void {\n    this.showTaskForm = !this.showTaskForm;\n    if (this.showTaskForm) {\n      this.newTask = this.initializeNewTask();\n    }\n  }\n\n  initializeNewTask(): Task {\n    return {\n      title: '',\n      description: '',\n      status: 'todo',\n      priority: 'medium',\n      teamId: this.teamId || '',\n      dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // Par défaut, une semaine à partir d'aujourd'hui\n    };\n  }\n\n  sortTasks(): void {\n    // Trier par priorité (high > medium > low) puis par statut (todo > in-progress > done)\n    this.tasks.sort((a, b) => {\n      const priorityOrder: { [key: string]: number } = {\n        high: 0,\n        medium: 1,\n        low: 2,\n      };\n      const statusOrder: { [key: string]: number } = {\n        todo: 0,\n        'in-progress': 1,\n        done: 2,\n      };\n\n      // D'abord par priorité\n      if (priorityOrder[a.priority] !== priorityOrder[b.priority]) {\n        return priorityOrder[a.priority] - priorityOrder[b.priority];\n      }\n\n      // Ensuite par statut\n      return statusOrder[a.status] - statusOrder[b.status];\n    });\n  }\n\n  // Méthodes de filtrage\n  filterTasks(): Task[] {\n    return this.tasks.filter((task) => {\n      // Filtre par statut\n      if (this.statusFilter !== 'all' && task.status !== this.statusFilter) {\n        return false;\n      }\n\n      // Filtre par priorité\n      if (\n        this.priorityFilter !== 'all' &&\n        task.priority !== this.priorityFilter\n      ) {\n        return false;\n      }\n\n      // Filtre par terme de recherche\n      if (\n        this.searchTerm &&\n        !task.title.toLowerCase().includes(this.searchTerm.toLowerCase())\n      ) {\n        return false;\n      }\n\n      return true;\n    });\n  }\n\n  // Méthodes pour obtenir les tâches par statut\n  getTodoTasks(): Task[] {\n    return this.tasks.filter(\n      (task) =>\n        task.status === 'todo' &&\n        (this.priorityFilter === 'all' ||\n          task.priority === this.priorityFilter) &&\n        (!this.searchTerm ||\n          task.title.toLowerCase().includes(this.searchTerm.toLowerCase()))\n    );\n  }\n\n  getInProgressTasks(): Task[] {\n    return this.tasks.filter(\n      (task) =>\n        task.status === 'in-progress' &&\n        (this.priorityFilter === 'all' ||\n          task.priority === this.priorityFilter) &&\n        (!this.searchTerm ||\n          task.title.toLowerCase().includes(this.searchTerm.toLowerCase()))\n    );\n  }\n\n  getDoneTasks(): Task[] {\n    return this.tasks.filter(\n      (task) =>\n        task.status === 'done' &&\n        (this.priorityFilter === 'all' ||\n          task.priority === this.priorityFilter) &&\n        (!this.searchTerm ||\n          task.title.toLowerCase().includes(this.searchTerm.toLowerCase()))\n    );\n  }\n\n  // Méthodes pour compter les tâches par statut\n  getTodoTasksCount(): number {\n    return this.tasks.filter((task) => task.status === 'todo').length;\n  }\n\n  getInProgressTasksCount(): number {\n    return this.tasks.filter((task) => task.status === 'in-progress').length;\n  }\n\n  getDoneTasksCount(): number {\n    return this.tasks.filter((task) => task.status === 'done').length;\n  }\n\n  navigateBack(): void {\n    this.router.navigate(['/liste']);\n  }\n}\n"], "mappings": ";AAAA,SAASA,SAAS,QAAgB,eAAe;AACjD,SAEEC,eAAe,EACfC,iBAAiB,QACZ,wBAAwB;AAS/B,SAASC,QAAQ,QAAQ,gBAAgB;AAOlC,WAAMC,iBAAiB,GAAvB,MAAMA,iBAAiB;EAgB5BC,YACUC,WAAwB,EACxBC,aAA4B,EAC5BC,WAAwB,EACxBC,KAAqB,EACrBC,MAAc,EACdC,mBAAwC;IALxC,KAAAL,WAAW,GAAXA,WAAW;IACX,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,mBAAmB,GAAnBA,mBAAmB;IArB7B,KAAAC,KAAK,GAAW,EAAE;IAClB,KAAAC,MAAM,GAAkB,IAAI;IAC5B,KAAAC,IAAI,GAAkB,IAAI;IAC1B,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAC,KAAK,GAAkB,IAAI;IAC3B,KAAAC,KAAK,GAAW,EAAE;IAElB,KAAAC,WAAW,GAAgB,IAAI;IAC/B,KAAAC,YAAY,GAAG,KAAK;IAEpB;IACA,KAAAC,YAAY,GAAW,KAAK;IAC5B,KAAAC,cAAc,GAAW,KAAK;IAC9B,KAAAC,UAAU,GAAW,EAAE;EASpB;EAEHC,QAAQA,CAAA;IACN;IACA,IAAI,CAACC,OAAO,GAAG,IAAI,CAACC,iBAAiB,EAAE;IAEvC,IAAI,CAAChB,KAAK,CAACiB,QAAQ,CAACC,SAAS,CAAEC,MAAM,IAAI;MACvC,IAAI,CAACf,MAAM,GAAGe,MAAM,CAACC,GAAG,CAAC,IAAI,CAAC;MAC9B,IAAI,IAAI,CAAChB,MAAM,EAAE;QACf,IAAI,CAACiB,eAAe,CAAC,IAAI,CAACjB,MAAM,CAAC;QACjC,IAAI,CAACkB,SAAS,CAAC,IAAI,CAAClB,MAAM,CAAC;QAC3B,IAAI,CAACmB,SAAS,EAAE;OACjB,MAAM;QACL,IAAI,CAAChB,KAAK,GAAG,sBAAsB;QACnC,IAAI,CAACL,mBAAmB,CAACsB,SAAS,CAAC,sBAAsB,CAAC;;IAE9D,CAAC,CAAC;EACJ;EAEAH,eAAeA,CAACjB,MAAc;IAC5B,IAAI,CAACE,OAAO,GAAG,IAAI;IAEnB;IACA,MAAMmB,WAAW,GAAG,KAAK,CAAC,CAAC;IAE3B,IAAIA,WAAW,EAAE;MACf;MACA,MAAMC,QAAQ,GAAW;QACvBC,GAAG,EAAEvB,MAAM;QACXwB,IAAI,EAAE,SAAS,GAAGxB,MAAM;QACxByB,WAAW,EAAE,0BAA0B,GAAGzB,MAAM;QAChD0B,KAAK,EAAE,UAAU;QACjBC,OAAO,EAAE;OACV;MAEDC,UAAU,CAAC,MAAK;QACd,IAAI,CAAC3B,IAAI,GAAGqB,QAAQ;QACpB,IAAI,CAACpB,OAAO,GAAG,KAAK;QACpB2B,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE,IAAI,CAAC7B,IAAI,CAAC;MAC/D,CAAC,EAAE,GAAG,CAAC;KACR,MAAM;MACL;MACA,IAAI,CAACP,aAAa,CACfqC,SAAS,CAAC/B,MAAM,CAAC,CACjBgC,IAAI,CAAC1C,QAAQ,CAAC,MAAO,IAAI,CAACY,OAAO,GAAG,KAAM,CAAC,CAAC,CAC5CY,SAAS,CAAC;QACTmB,IAAI,EAAGC,IAAI,IAAI;UACb,IAAI,CAACjC,IAAI,GAAGiC,IAAI;UAChBL,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAE,IAAI,CAAC7B,IAAI,CAAC;QACrE,CAAC;QACDE,KAAK,EAAGA,KAAK,IAAI;UACf0B,OAAO,CAAC1B,KAAK,CACX,oDAAoD,EACpDA,KAAK,CACN;UACD,IAAI,CAACA,KAAK,GAAG,+CAA+C;UAC5D,IAAI,CAACL,mBAAmB,CAACsB,SAAS,CAChC,mDAAmD,CACpD;UAED;UACA,MAAME,QAAQ,GAAW;YACvBC,GAAG,EAAEvB,MAAM;YACXwB,IAAI,EAAE,SAAS,GAAGxB,MAAM,GAAG,aAAa;YACxCyB,WAAW,EAAE,0BAA0B,GAAGzB,MAAM;YAChD0B,KAAK,EAAE,UAAU;YACjBC,OAAO,EAAE;WACV;UAED,IAAI,CAAC1B,IAAI,GAAGqB,QAAQ;QACtB;OACD,CAAC;;EAER;EAEAJ,SAASA,CAAClB,MAAc;IACtB,IAAI,CAACE,OAAO,GAAG,IAAI;IAEnB;IACA,MAAMmB,WAAW,GAAG,KAAK,CAAC,CAAC;IAE3B,IAAIA,WAAW,EAAE;MACf;MACA,MAAMc,SAAS,GAAW,CACxB;QACEZ,GAAG,EAAE,GAAG;QACRa,KAAK,EAAE,SAAS;QAChBX,WAAW,EAAE,2BAA2B;QACxCY,MAAM,EAAE,MAAM;QACdC,QAAQ,EAAE,MAAM;QAChBtC,MAAM,EAAEA;OACT,EACD;QACEuB,GAAG,EAAE,GAAG;QACRa,KAAK,EAAE,SAAS;QAChBX,WAAW,EAAE,2BAA2B;QACxCY,MAAM,EAAE,MAAM;QACdC,QAAQ,EAAE,QAAQ;QAClBtC,MAAM,EAAEA;OACT,EACD;QACEuB,GAAG,EAAE,GAAG;QACRa,KAAK,EAAE,SAAS;QAChBX,WAAW,EAAE,2BAA2B;QACxCY,MAAM,EAAE,aAAa;QACrBC,QAAQ,EAAE,MAAM;QAChBtC,MAAM,EAAEA;OACT,EACD;QACEuB,GAAG,EAAE,GAAG;QACRa,KAAK,EAAE,SAAS;QAChBX,WAAW,EAAE,2BAA2B;QACxCY,MAAM,EAAE,MAAM;QACdC,QAAQ,EAAE,KAAK;QACftC,MAAM,EAAEA;OACT,CACF;MAED4B,UAAU,CAAC,MAAK;QACd,IAAI,CAAC7B,KAAK,GAAGoC,SAAS;QACtB,IAAI,CAACI,SAAS,EAAE;QAChB,IAAI,CAACrC,OAAO,GAAG,KAAK;QACpB2B,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE,IAAI,CAAC/B,KAAK,CAAC;MACpD,CAAC,EAAE,GAAG,CAAC;KACR,MAAM;MACL;MACA,IAAI,CAACN,WAAW,CACb+C,cAAc,CAACxC,MAAM,CAAC,CACtBgC,IAAI,CAAC1C,QAAQ,CAAC,MAAO,IAAI,CAACY,OAAO,GAAG,KAAM,CAAC,CAAC,CAC5CY,SAAS,CAAC;QACTmB,IAAI,EAAGC,IAAY,IAAI;UACrB,IAAI,CAACnC,KAAK,GAAGmC,IAAI;UACjB,IAAI,CAACK,SAAS,EAAE;UAChBV,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE,IAAI,CAAC/B,KAAK,CAAC;QAC1D,CAAC;QACDI,KAAK,EAAGA,KAAU,IAAI;UACpB0B,OAAO,CAAC1B,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;UAC7D,IAAI,CAACA,KAAK,GAAG,kCAAkC;UAC/C,IAAI,CAACL,mBAAmB,CAACsB,SAAS,CAChC,sCAAsC,CACvC;UAED;UACA,MAAMe,SAAS,GAAW,CACxB;YACEZ,GAAG,EAAE,GAAG;YACRa,KAAK,EAAE,oBAAoB;YAC3BX,WAAW,EAAE,2BAA2B;YACxCY,MAAM,EAAE,MAAM;YACdC,QAAQ,EAAE,MAAM;YAChBtC,MAAM,EAAEA;WACT,EACD;YACEuB,GAAG,EAAE,GAAG;YACRa,KAAK,EAAE,oBAAoB;YAC3BX,WAAW,EAAE,2BAA2B;YACxCY,MAAM,EAAE,MAAM;YACdC,QAAQ,EAAE,QAAQ;YAClBtC,MAAM,EAAEA;WACT,CACF;UAED,IAAI,CAACD,KAAK,GAAGoC,SAAS;UACtB,IAAI,CAACI,SAAS,EAAE;UAChBV,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE,IAAI,CAAC/B,KAAK,CAAC;QACxD;OACD,CAAC;;EAER;EAEA;EACA0C,IAAIA,CAACC,KAA0B;IAC7B,IAAIA,KAAK,CAACC,iBAAiB,KAAKD,KAAK,CAACE,SAAS,EAAE;MAC/C;MACAxD,eAAe,CACbsD,KAAK,CAACE,SAAS,CAACV,IAAI,EACpBQ,KAAK,CAACG,aAAa,EACnBH,KAAK,CAACI,YAAY,CACnB;KACF,MAAM;MACL;MACAzD,iBAAiB,CACfqD,KAAK,CAACC,iBAAiB,CAACT,IAAI,EAC5BQ,KAAK,CAACE,SAAS,CAACV,IAAI,EACpBQ,KAAK,CAACG,aAAa,EACnBH,KAAK,CAACI,YAAY,CACnB;MAED;MACA,MAAMC,IAAI,GAAGL,KAAK,CAACE,SAAS,CAACV,IAAI,CAACQ,KAAK,CAACI,YAAY,CAAC;MACrD,IAAIE,SAA0C;MAE9C,IAAIN,KAAK,CAACE,SAAS,CAACK,EAAE,KAAK,WAAW,EAAE;QACtCD,SAAS,GAAG,MAAM;OACnB,MAAM,IAAIN,KAAK,CAACE,SAAS,CAACK,EAAE,KAAK,kBAAkB,EAAE;QACpDD,SAAS,GAAG,aAAa;OAC1B,MAAM;QACLA,SAAS,GAAG,MAAM;;MAGpB,IAAID,IAAI,CAACxB,GAAG,IAAIwB,IAAI,CAACV,MAAM,KAAKW,SAAS,EAAE;QACzCD,IAAI,CAACV,MAAM,GAAGW,SAAS;QACvB,IAAI,CAACE,gBAAgB,CAACH,IAAI,EAAEC,SAAS,CAAC;;;EAG5C;EAEA7B,SAASA,CAAA;IACP;IACA,MAAME,WAAW,GAAG,KAAK,CAAC,CAAC;IAE3B,IAAIA,WAAW,EAAE;MACf;MACA,MAAM8B,SAAS,GAAW,CACxB;QACE5B,GAAG,EAAE,OAAO;QACZC,IAAI,EAAE,UAAU;QAChB4B,KAAK,EAAE,kBAAkB;QACzBC,IAAI,EAAE;OACP,EACD;QACE9B,GAAG,EAAE,OAAO;QACZC,IAAI,EAAE,YAAY;QAClB4B,KAAK,EAAE,kBAAkB;QACzBC,IAAI,EAAE;OACP,CACF;MAEDzB,UAAU,CAAC,MAAK;QACd,IAAI,CAACxB,KAAK,GAAG+C,SAAS;QACtBtB,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAAC1B,KAAK,CAAC;MACzD,CAAC,EAAE,GAAG,CAAC;KACR,MAAM;MACL;MACA,IAAI,CAACT,WAAW,CAAC2D,QAAQ,EAAE,CAACxC,SAAS,CAAC;QACpCmB,IAAI,EAAGC,IAAY,IAAI;UACrB,IAAI,CAAC9B,KAAK,GAAG8B,IAAI;UACjBL,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE,IAAI,CAAC1B,KAAK,CAAC;QAC/D,CAAC;QACDD,KAAK,EAAGA,KAAU,IAAI;UACpB0B,OAAO,CAAC1B,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;UAEnE;UACA,MAAMgD,SAAS,GAAW,CACxB;YACE5B,GAAG,EAAE,OAAO;YACZC,IAAI,EAAE,qBAAqB;YAC3B4B,KAAK,EAAE,kBAAkB;YACzBC,IAAI,EAAE;WACP,EACD;YACE9B,GAAG,EAAE,OAAO;YACZC,IAAI,EAAE,uBAAuB;YAC7B4B,KAAK,EAAE,kBAAkB;YACzBC,IAAI,EAAE;WACP,CACF;UAED,IAAI,CAACjD,KAAK,GAAG+C,SAAS;UACtBtB,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE,IAAI,CAAC1B,KAAK,CAAC;QAC7D;OACD,CAAC;;EAEN;EAEAmD,WAAWA,CAACC,MAAc;IACxB,MAAMC,IAAI,GAAG,IAAI,CAACrD,KAAK,CAACsD,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACpC,GAAG,KAAKiC,MAAM,IAAIG,CAAC,CAACV,EAAE,KAAKO,MAAM,CAAC;IACxE,IAAIC,IAAI,EAAE;MACR,IAAIA,IAAI,CAACG,SAAS,IAAIH,IAAI,CAACI,QAAQ,EAAE;QACnC,OAAO,GAAGJ,IAAI,CAACG,SAAS,IAAIH,IAAI,CAACI,QAAQ,EAAE;OAC5C,MAAM,IAAIJ,IAAI,CAACjC,IAAI,EAAE;QACpB,OAAOiC,IAAI,CAACjC,IAAI;;;IAGpB,OAAO,qBAAqB;EAC9B;EAEAsC,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAAC9D,MAAM,EAAE;MAChB,IAAI,CAACF,mBAAmB,CAACsB,SAAS,CAAC,sBAAsB,CAAC;MAC1D;;IAGF,IAAI,CAACT,OAAO,CAACX,MAAM,GAAG,IAAI,CAACA,MAAM;IAEjC,IAAI,CAACE,OAAO,GAAG,IAAI;IACnB,IAAI,CAACT,WAAW,CACbqE,UAAU,CAAC,IAAI,CAACnD,OAAO,CAAC,CACxBqB,IAAI,CAAC1C,QAAQ,CAAC,MAAO,IAAI,CAACY,OAAO,GAAG,KAAM,CAAC,CAAC,CAC5CY,SAAS,CAAC;MACTmB,IAAI,EAAGC,IAAU,IAAI;QACnB,IAAI,CAACnC,KAAK,CAACgE,IAAI,CAAC7B,IAAI,CAAC;QACrB,IAAI,CAACK,SAAS,EAAE;QAChB,IAAI,CAAC5B,OAAO,GAAG,IAAI,CAACC,iBAAiB,EAAE;QACvC,IAAI,CAACN,YAAY,GAAG,KAAK;QACzB,IAAI,CAACR,mBAAmB,CAACkE,WAAW,CAAC,yBAAyB,CAAC;MACjE,CAAC;MACD7D,KAAK,EAAGA,KAAU,IAAI;QACpB0B,OAAO,CAAC1B,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;QAC/D,IAAI,CAACL,mBAAmB,CAACsB,SAAS,CAChC,wCAAwC,CACzC;MACH;KACD,CAAC;EACN;EAEA6C,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAAC5D,WAAW,IAAI,CAAC,IAAI,CAACA,WAAW,CAACkB,GAAG,EAAE;MAC9C,IAAI,CAACzB,mBAAmB,CAACsB,SAAS,CAAC,gBAAgB,CAAC;MACpD;;IAGF,IAAI,CAAClB,OAAO,GAAG,IAAI;IACnB,IAAI,CAACT,WAAW,CACbwE,UAAU,CAAC,IAAI,CAAC5D,WAAW,CAACkB,GAAG,EAAE,IAAI,CAAClB,WAAW,CAAC,CAClD2B,IAAI,CAAC1C,QAAQ,CAAC,MAAO,IAAI,CAACY,OAAO,GAAG,KAAM,CAAC,CAAC,CAC5CY,SAAS,CAAC;MACTmB,IAAI,EAAGC,IAAU,IAAI;QACnB,MAAMgC,KAAK,GAAG,IAAI,CAACnE,KAAK,CAACoE,SAAS,CAAEC,CAAC,IAAKA,CAAC,CAAC7C,GAAG,KAAKW,IAAI,CAACX,GAAG,CAAC;QAC7D,IAAI2C,KAAK,KAAK,CAAC,CAAC,EAAE;UAChB,IAAI,CAACnE,KAAK,CAACmE,KAAK,CAAC,GAAGhC,IAAI;;QAE1B,IAAI,CAAC7B,WAAW,GAAG,IAAI;QACvB,IAAI,CAACP,mBAAmB,CAACkE,WAAW,CAAC,+BAA+B,CAAC;MACvE,CAAC;MACD7D,KAAK,EAAGA,KAAU,IAAI;QACpB0B,OAAO,CAAC1B,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;QAClE,IAAI,CAACL,mBAAmB,CAACsB,SAAS,CAChC,2CAA2C,CAC5C;MACH;KACD,CAAC;EACN;EAEAiD,UAAUA,CAACpB,EAAU;IACnB,IAAIqB,OAAO,CAAC,kDAAkD,CAAC,EAAE;MAC/D,IAAI,CAACpE,OAAO,GAAG,IAAI;MACnB,IAAI,CAACT,WAAW,CACb4E,UAAU,CAACpB,EAAE,CAAC,CACdjB,IAAI,CAAC1C,QAAQ,CAAC,MAAO,IAAI,CAACY,OAAO,GAAG,KAAM,CAAC,CAAC,CAC5CY,SAAS,CAAC;QACTmB,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAAClC,KAAK,GAAG,IAAI,CAACA,KAAK,CAACwE,MAAM,CAAEH,CAAC,IAAKA,CAAC,CAAC7C,GAAG,KAAK0B,EAAE,CAAC;UACnD,IAAI,CAACnD,mBAAmB,CAACkE,WAAW,CAAC,6BAA6B,CAAC;QACrE,CAAC;QACD7D,KAAK,EAAGA,KAAU,IAAI;UACpB0B,OAAO,CAAC1B,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;UAClE,IAAI,CAACL,mBAAmB,CAACsB,SAAS,CAChC,2CAA2C,CAC5C;QACH;OACD,CAAC;;EAER;EAEA8B,gBAAgBA,CAACH,IAAU,EAAEV,MAAuC;IAClE,IAAI,CAACU,IAAI,CAACxB,GAAG,EAAE;IAEf,IAAI,CAACrB,OAAO,GAAG,IAAI;IACnB,IAAI,CAACT,WAAW,CACbyD,gBAAgB,CAACH,IAAI,CAACxB,GAAG,EAAEc,MAAM,CAAC,CAClCL,IAAI,CAAC1C,QAAQ,CAAC,MAAO,IAAI,CAACY,OAAO,GAAG,KAAM,CAAC,CAAC,CAC5CY,SAAS,CAAC;MACTmB,IAAI,EAAGC,IAAU,IAAI;QACnB,MAAMgC,KAAK,GAAG,IAAI,CAACnE,KAAK,CAACoE,SAAS,CAAEC,CAAC,IAAKA,CAAC,CAAC7C,GAAG,KAAKW,IAAI,CAACX,GAAG,CAAC;QAC7D,IAAI2C,KAAK,KAAK,CAAC,CAAC,EAAE;UAChB,IAAI,CAACnE,KAAK,CAACmE,KAAK,CAAC,GAAGhC,IAAI;;QAE1B,IAAI,CAACpC,mBAAmB,CAACkE,WAAW,CAAC,+BAA+B,CAAC;MACvE,CAAC;MACD7D,KAAK,EAAGA,KAAU,IAAI;QACpB0B,OAAO,CAAC1B,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;QAChE,IAAI,CAACL,mBAAmB,CAACsB,SAAS,CAChC,yCAAyC,CAC1C;MACH;KACD,CAAC;EACN;EAEAoD,QAAQA,CAACzB,IAAU;IACjB,IAAI,CAAC1C,WAAW,GAAG;MAAE,GAAG0C;IAAI,CAAE;EAChC;EAEA0B,UAAUA,CAAA;IACR,IAAI,CAACpE,WAAW,GAAG,IAAI;EACzB;EAEAqE,cAAcA,CAAA;IACZ,IAAI,CAACpE,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;IACtC,IAAI,IAAI,CAACA,YAAY,EAAE;MACrB,IAAI,CAACK,OAAO,GAAG,IAAI,CAACC,iBAAiB,EAAE;;EAE3C;EAEAA,iBAAiBA,CAAA;IACf,OAAO;MACLwB,KAAK,EAAE,EAAE;MACTX,WAAW,EAAE,EAAE;MACfY,MAAM,EAAE,MAAM;MACdC,QAAQ,EAAE,QAAQ;MAClBtC,MAAM,EAAE,IAAI,CAACA,MAAM,IAAI,EAAE;MACzB2E,OAAO,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAE;KAC1D;EACH;;EAEAtC,SAASA,CAAA;IACP;IACA,IAAI,CAACxC,KAAK,CAAC+E,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MACvB,MAAMC,aAAa,GAA8B;QAC/CC,IAAI,EAAE,CAAC;QACPC,MAAM,EAAE,CAAC;QACTC,GAAG,EAAE;OACN;MACD,MAAMC,WAAW,GAA8B;QAC7CC,IAAI,EAAE,CAAC;QACP,aAAa,EAAE,CAAC;QAChBC,IAAI,EAAE;OACP;MAED;MACA,IAAIN,aAAa,CAACF,CAAC,CAACzC,QAAQ,CAAC,KAAK2C,aAAa,CAACD,CAAC,CAAC1C,QAAQ,CAAC,EAAE;QAC3D,OAAO2C,aAAa,CAACF,CAAC,CAACzC,QAAQ,CAAC,GAAG2C,aAAa,CAACD,CAAC,CAAC1C,QAAQ,CAAC;;MAG9D;MACA,OAAO+C,WAAW,CAACN,CAAC,CAAC1C,MAAM,CAAC,GAAGgD,WAAW,CAACL,CAAC,CAAC3C,MAAM,CAAC;IACtD,CAAC,CAAC;EACJ;EAEA;EACAmD,WAAWA,CAAA;IACT,OAAO,IAAI,CAACzF,KAAK,CAACwE,MAAM,CAAExB,IAAI,IAAI;MAChC;MACA,IAAI,IAAI,CAACxC,YAAY,KAAK,KAAK,IAAIwC,IAAI,CAACV,MAAM,KAAK,IAAI,CAAC9B,YAAY,EAAE;QACpE,OAAO,KAAK;;MAGd;MACA,IACE,IAAI,CAACC,cAAc,KAAK,KAAK,IAC7BuC,IAAI,CAACT,QAAQ,KAAK,IAAI,CAAC9B,cAAc,EACrC;QACA,OAAO,KAAK;;MAGd;MACA,IACE,IAAI,CAACC,UAAU,IACf,CAACsC,IAAI,CAACX,KAAK,CAACqD,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACjF,UAAU,CAACgF,WAAW,EAAE,CAAC,EACjE;QACA,OAAO,KAAK;;MAGd,OAAO,IAAI;IACb,CAAC,CAAC;EACJ;EAEA;EACAE,YAAYA,CAAA;IACV,OAAO,IAAI,CAAC5F,KAAK,CAACwE,MAAM,CACrBxB,IAAI,IACHA,IAAI,CAACV,MAAM,KAAK,MAAM,KACrB,IAAI,CAAC7B,cAAc,KAAK,KAAK,IAC5BuC,IAAI,CAACT,QAAQ,KAAK,IAAI,CAAC9B,cAAc,CAAC,KACvC,CAAC,IAAI,CAACC,UAAU,IACfsC,IAAI,CAACX,KAAK,CAACqD,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACjF,UAAU,CAACgF,WAAW,EAAE,CAAC,CAAC,CACtE;EACH;EAEAG,kBAAkBA,CAAA;IAChB,OAAO,IAAI,CAAC7F,KAAK,CAACwE,MAAM,CACrBxB,IAAI,IACHA,IAAI,CAACV,MAAM,KAAK,aAAa,KAC5B,IAAI,CAAC7B,cAAc,KAAK,KAAK,IAC5BuC,IAAI,CAACT,QAAQ,KAAK,IAAI,CAAC9B,cAAc,CAAC,KACvC,CAAC,IAAI,CAACC,UAAU,IACfsC,IAAI,CAACX,KAAK,CAACqD,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACjF,UAAU,CAACgF,WAAW,EAAE,CAAC,CAAC,CACtE;EACH;EAEAI,YAAYA,CAAA;IACV,OAAO,IAAI,CAAC9F,KAAK,CAACwE,MAAM,CACrBxB,IAAI,IACHA,IAAI,CAACV,MAAM,KAAK,MAAM,KACrB,IAAI,CAAC7B,cAAc,KAAK,KAAK,IAC5BuC,IAAI,CAACT,QAAQ,KAAK,IAAI,CAAC9B,cAAc,CAAC,KACvC,CAAC,IAAI,CAACC,UAAU,IACfsC,IAAI,CAACX,KAAK,CAACqD,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACjF,UAAU,CAACgF,WAAW,EAAE,CAAC,CAAC,CACtE;EACH;EAEA;EACAK,iBAAiBA,CAAA;IACf,OAAO,IAAI,CAAC/F,KAAK,CAACwE,MAAM,CAAExB,IAAI,IAAKA,IAAI,CAACV,MAAM,KAAK,MAAM,CAAC,CAAC0D,MAAM;EACnE;EAEAC,uBAAuBA,CAAA;IACrB,OAAO,IAAI,CAACjG,KAAK,CAACwE,MAAM,CAAExB,IAAI,IAAKA,IAAI,CAACV,MAAM,KAAK,aAAa,CAAC,CAAC0D,MAAM;EAC1E;EAEAE,iBAAiBA,CAAA;IACf,OAAO,IAAI,CAAClG,KAAK,CAACwE,MAAM,CAAExB,IAAI,IAAKA,IAAI,CAACV,MAAM,KAAK,MAAM,CAAC,CAAC0D,MAAM;EACnE;EAEAG,YAAYA,CAAA;IACV,IAAI,CAACrG,MAAM,CAACsG,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;CACD;AAlhBY5G,iBAAiB,GAAA6G,UAAA,EAL7BjH,SAAS,CAAC;EACTkH,QAAQ,EAAE,eAAe;EACzBC,WAAW,EAAE,4BAA4B;EACzCC,SAAS,EAAE,CAAC,2BAA2B;CACxC,CAAC,C,EACWhH,iBAAiB,CAkhB7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}