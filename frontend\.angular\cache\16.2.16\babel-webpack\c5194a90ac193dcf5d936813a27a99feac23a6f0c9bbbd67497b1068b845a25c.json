{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@app/services/projects.service\";\nimport * as i3 from \"src/app/services/file.service\";\nimport * as i4 from \"@angular/common\";\nfunction DetailProjectComponent_div_18_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41)(1, \"a\", 42);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 43);\n    i0.ɵɵelement(3, \"path\", 44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5, \"T\\u00E9l\\u00E9charger\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const file_r5 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"href\", ctx_r4.getFileUrl(file_r5), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DetailProjectComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"h4\", 35);\n    i0.ɵɵtext(2, \"Fichiers joints\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 39);\n    i0.ɵɵtemplate(4, DetailProjectComponent_div_18_div_4_Template, 6, 1, \"div\", 40);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.projet.fichiers);\n  }\n}\nfunction DetailProjectComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵtext(1, \" Aucun fichier joint \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DetailProjectComponent_div_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"div\", 47);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 48)(4, \"div\", 49);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 32);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const etudiant_r6 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", (etudiant_r6.nom == null ? null : etudiant_r6.nom.charAt(0)) || \"\", \"\", (etudiant_r6.prenom == null ? null : etudiant_r6.prenom.charAt(0)) || \"\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", etudiant_r6.prenom, \" \", etudiant_r6.nom, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.formatDate(etudiant_r6.dateRendu));\n  }\n}\nfunction DetailProjectComponent_div_62_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50);\n    i0.ɵɵtext(1, \" Aucun rendu pour le moment \");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c0 = function (a1) {\n  return [\"/admin/projects/editProjet\", a1];\n};\nconst _c1 = function () {\n  return [\"/admin/projects/rendus\"];\n};\nconst _c2 = function (a0) {\n  return {\n    projetId: a0\n  };\n};\nconst _c3 = function () {\n  return [];\n};\nexport class DetailProjectComponent {\n  constructor(route, router, projectService, fileService) {\n    this.route = route;\n    this.router = router;\n    this.projectService = projectService;\n    this.fileService = fileService;\n    this.projet = null;\n  }\n  ngOnInit() {\n    const id = this.route.snapshot.paramMap.get('id');\n    if (id) {\n      this.projectService.getProjetById(id).subscribe(data => {\n        this.projet = data;\n      });\n    }\n  }\n  getFileUrl(filePath) {\n    return this.fileService.getDownloadUrl(filePath);\n  }\n  deleteProjet(id) {\n    if (!id) return;\n    if (confirm('Êtes-vous sûr de vouloir supprimer ce projet ?')) {\n      this.projectService.deleteProjet(id).subscribe({\n        next: () => {\n          alert('Projet supprimé avec succès');\n          this.router.navigate(['/admin/projects']);\n        },\n        error: err => {\n          console.error('Erreur lors de la suppression du projet', err);\n          alert('Erreur lors de la suppression du projet');\n        }\n      });\n    }\n  }\n  formatDate(date) {\n    const d = new Date(date);\n    return `${d.getDate().toString().padStart(2, '0')}/${(d.getMonth() + 1).toString().padStart(2, '0')}/${d.getFullYear()}`;\n  }\n  static {\n    this.ɵfac = function DetailProjectComponent_Factory(t) {\n      return new (t || DetailProjectComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.ProjetService), i0.ɵɵdirectiveInject(i3.FileService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DetailProjectComponent,\n      selectors: [[\"app-detail-project\"]],\n      decls: 63,\n      vars: 24,\n      consts: [[1, \"max-w-6xl\", \"mx-auto\", \"my-8\", \"space-y-6\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-3\", \"gap-6\"], [1, \"md:col-span-2\", \"bg-white\", \"rounded-2xl\", \"shadow-md\", \"hover:shadow-xl\", \"transition-shadow\", \"duration-300\", \"overflow-hidden\", \"border\", \"border-[#e4e7ec]\"], [1, \"bg-gradient-to-r\", \"from-[#6C63FF]\", \"to-[#C77DFF]\", \"p-6\", \"text-white\", \"rounded-t-2xl\"], [1, \"flex\", \"justify-between\", \"items-start\"], [1, \"text-2xl\", \"font-bold\"], [1, \"mt-1\", \"text-sm\", \"text-[#e4dbf8]\"], [1, \"px-3\", \"py-1\", \"bg-white/20\", \"rounded-full\", \"text-xs\", \"font-semibold\"], [1, \"p-6\", \"space-y-6\"], [1, \"text-sm\", \"font-semibold\", \"text-gray-600\", \"mb-2\"], [1, \"text-gray-700\"], [4, \"ngIf\"], [\"class\", \"text-sm text-gray-500 italic\", 4, \"ngIf\"], [1, \"flex\", \"flex-wrap\", \"gap-3\", \"pt-4\", \"border-t\", \"border-gray-100\"], [1, \"flex-1\", \"bg-gradient-to-r\", \"from-[#3CAEA3]\", \"to-[#20BF55]\", \"hover:from-[#2d9b91]\", \"hover:to-[#18a046]\", \"text-white\", \"py-2.5\", \"px-4\", \"rounded-lg\", \"font-medium\", \"text-sm\", \"text-center\", \"shadow-sm\", \"hover:shadow-md\", \"transition-all\", \"flex\", \"items-center\", \"justify-center\", 3, \"routerLink\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-5\", \"w-5\", \"mr-2\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"], [1, \"flex-1\", \"bg-gradient-to-r\", \"from-[#F5576C]\", \"to-[#F093FB]\", \"hover:from-[#e04054]\", \"hover:to-[#d87fe0]\", \"text-white\", \"py-2.5\", \"px-4\", \"rounded-lg\", \"font-medium\", \"text-sm\", \"text-center\", \"shadow-sm\", \"hover:shadow-md\", \"transition-all\", \"flex\", \"items-center\", \"justify-center\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"], [1, \"flex-1\", \"bg-gradient-to-r\", \"from-[#6C63FF]\", \"to-[#8E2DE2]\", \"hover:from-[#5046e5]\", \"hover:to-[#7816c7]\", \"text-white\", \"py-2.5\", \"px-4\", \"rounded-lg\", \"font-medium\", \"text-sm\", \"text-center\", \"shadow-sm\", \"hover:shadow-md\", \"transition-all\", \"flex\", \"items-center\", \"justify-center\", 3, \"routerLink\", \"queryParams\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\"], [1, \"bg-white\", \"rounded-2xl\", \"shadow-md\", \"border\", \"border-[#e4e7ec]\", \"overflow-hidden\"], [1, \"bg-gradient-to-r\", \"from-[#6C63FF]\", \"to-[#C77DFF]\", \"p-4\", \"text-white\"], [1, \"font-semibold\"], [1, \"flex\", \"justify-between\", \"mb-2\"], [1, \"text-sm\", \"font-medium\", \"text-gray-700\"], [1, \"text-sm\", \"font-medium\", \"text-[#6C63FF]\"], [1, \"w-full\", \"bg-gray-200\", \"rounded-full\", \"h-2.5\"], [1, \"bg-gradient-to-r\", \"from-[#6C63FF]\", \"to-[#C77DFF]\", \"h-2.5\", \"rounded-full\"], [1, \"grid\", \"grid-cols-2\", \"gap-4\"], [1, \"bg-[#f0f7ff]\", \"p-4\", \"rounded-lg\", \"border\", \"border-[#e4e7ec]\"], [1, \"text-2xl\", \"font-bold\", \"text-[#4A00E0]\"], [1, \"text-xs\", \"text-gray-500\"], [1, \"bg-[#fff5f5]\", \"p-4\", \"rounded-lg\", \"border\", \"border-[#e4e7ec]\"], [1, \"text-2xl\", \"font-bold\", \"text-[#E02D6D]\"], [1, \"text-sm\", \"font-semibold\", \"text-gray-600\", \"mb-3\"], [1, \"space-y-3\"], [\"class\", \"flex items-center space-x-3\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"text-sm text-gray-500 italic text-center py-2\", 4, \"ngIf\"], [1, \"grid\", \"grid-cols-1\", \"sm:grid-cols-2\", \"gap-3\"], [\"class\", \"flex items-center\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"items-center\"], [\"download\", \"\", 1, \"flex-1\", \"text-center\", \"text-sm\", \"text-white\", \"bg-gradient-to-r\", \"from-[#8E2DE2]\", \"to-[#4A00E0]\", \"hover:from-[#7c22d2]\", \"hover:to-[#3f00cc]\", \"rounded-lg\", \"py-2\", \"px-4\", \"transition-all\", \"hover:scale-[1.02]\", \"flex\", \"items-center\", \"justify-center\", \"space-x-2\", 3, \"href\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-4\", \"w-4\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4\"], [1, \"text-sm\", \"text-gray-500\", \"italic\"], [1, \"flex\", \"items-center\", \"space-x-3\"], [1, \"h-8\", \"w-8\", \"rounded-full\", \"bg-[#6C63FF]\", \"flex\", \"items-center\", \"justify-center\", \"text-white\", \"text-xs\", \"font-bold\"], [1, \"text-sm\"], [1, \"font-medium\"], [1, \"text-sm\", \"text-gray-500\", \"italic\", \"text-center\", \"py-2\"]],\n      template: function DetailProjectComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"div\")(6, \"h3\", 5);\n          i0.ɵɵtext(7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"p\", 6);\n          i0.ɵɵtext(9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"span\", 7);\n          i0.ɵɵtext(11);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(12, \"div\", 8)(13, \"div\")(14, \"h4\", 9);\n          i0.ɵɵtext(15, \"Description\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"p\", 10);\n          i0.ɵɵtext(17);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(18, DetailProjectComponent_div_18_Template, 5, 1, \"div\", 11);\n          i0.ɵɵtemplate(19, DetailProjectComponent_div_19_Template, 2, 0, \"div\", 12);\n          i0.ɵɵelementStart(20, \"div\", 13)(21, \"a\", 14);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(22, \"svg\", 15);\n          i0.ɵɵelement(23, \"path\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(24, \" Modifier \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(25, \"button\", 17);\n          i0.ɵɵlistener(\"click\", function DetailProjectComponent_Template_button_click_25_listener() {\n            return ctx.deleteProjet(ctx.projet == null ? null : ctx.projet._id);\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(26, \"svg\", 15);\n          i0.ɵɵelement(27, \"path\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(28, \" Supprimer \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(29, \"a\", 19);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(30, \"svg\", 15);\n          i0.ɵɵelement(31, \"path\", 20);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(32, \" Voir les rendus \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(33, \"div\", 21)(34, \"div\", 22)(35, \"h3\", 23);\n          i0.ɵɵtext(36, \"Statistiques de rendu\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(37, \"div\", 8)(38, \"div\")(39, \"div\", 24)(40, \"span\", 25);\n          i0.ɵɵtext(41, \"Progression\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"span\", 26);\n          i0.ɵɵtext(43);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(44, \"div\", 27);\n          i0.ɵɵelement(45, \"div\", 28);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(46, \"div\", 29)(47, \"div\", 30)(48, \"div\", 31);\n          i0.ɵɵtext(49);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"div\", 32);\n          i0.ɵɵtext(51, \"Rendus\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(52, \"div\", 33)(53, \"div\", 34);\n          i0.ɵɵtext(54);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(55, \"div\", 32);\n          i0.ɵɵtext(56, \"En attente\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(57, \"div\")(58, \"h4\", 35);\n          i0.ɵɵtext(59, \"Derniers rendus\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(60, \"div\", 36);\n          i0.ɵɵtemplate(61, DetailProjectComponent_div_61_Template, 8, 5, \"div\", 37);\n          i0.ɵɵtemplate(62, DetailProjectComponent_div_62_Template, 2, 0, \"div\", 38);\n          i0.ɵɵelementEnd()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate(ctx.projet == null ? null : ctx.projet.titre);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate2(\" Groupe: \", ctx.projet == null ? null : ctx.projet.groupe, \" \\u2022 Date limite: \", (ctx.projet == null ? null : ctx.projet.dateLimite) ? ctx.formatDate(ctx.projet == null ? null : ctx.projet.dateLimite) : \"\", \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", (ctx.projet == null ? null : ctx.projet.statut) || \"En cours\", \" \");\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate((ctx.projet == null ? null : ctx.projet.description) || \"Aucune description fournie\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", (ctx.projet == null ? null : ctx.projet.fichiers == null ? null : ctx.projet.fichiers.length) > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !(ctx.projet == null ? null : ctx.projet.fichiers) || ctx.projet.fichiers.length === 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(18, _c0, ctx.projet == null ? null : ctx.projet._id));\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(20, _c1))(\"queryParams\", i0.ɵɵpureFunction1(21, _c2, ctx.projet == null ? null : ctx.projet._id));\n          i0.ɵɵadvance(14);\n          i0.ɵɵtextInterpolate2(\"\", (ctx.projet == null ? null : ctx.projet.etudiantsRendus == null ? null : ctx.projet.etudiantsRendus.length) || 0, \"/\", (ctx.projet == null ? null : ctx.projet.totalEtudiants) || 0, \"\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleProp(\"width\", ((ctx.projet == null ? null : ctx.projet.etudiantsRendus == null ? null : ctx.projet.etudiantsRendus.length) || 0) / ((ctx.projet == null ? null : ctx.projet.totalEtudiants) || 1) * 100, \"%\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate((ctx.projet == null ? null : ctx.projet.etudiantsRendus == null ? null : ctx.projet.etudiantsRendus.length) || 0);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(((ctx.projet == null ? null : ctx.projet.totalEtudiants) || 0) - ((ctx.projet == null ? null : ctx.projet.etudiantsRendus == null ? null : ctx.projet.etudiantsRendus.length) || 0));\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngForOf\", (ctx.projet == null ? null : ctx.projet.derniersRendus) || i0.ɵɵpureFunction0(23, _c3));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !(ctx.projet == null ? null : ctx.projet.derniersRendus) || ctx.projet.derniersRendus.length === 0);\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i1.RouterLink],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJkZXRhaWwtcHJvamVjdC5jb21wb25lbnQuY3NzIn0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvYWRtaW4vcHJvamVjdHMvZGV0YWlsLXByb2plY3QvZGV0YWlsLXByb2plY3QuY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0FBQ0EsNEtBQTRLIiwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵnamespaceSVG", "ɵɵelement", "ɵɵelementEnd", "ɵɵnamespaceHTML", "ɵɵtext", "ɵɵadvance", "ɵɵproperty", "ctx_r4", "getFileUrl", "file_r5", "ɵɵsanitizeUrl", "ɵɵtemplate", "DetailProjectComponent_div_18_div_4_Template", "ctx_r0", "projet", "fichiers", "ɵɵtextInterpolate2", "etudiant_r6", "nom", "char<PERSON>t", "prenom", "ɵɵtextInterpolate", "ctx_r2", "formatDate", "dateRendu", "DetailProjectComponent", "constructor", "route", "router", "projectService", "fileService", "ngOnInit", "id", "snapshot", "paramMap", "get", "getProjetById", "subscribe", "data", "filePath", "getDownloadUrl", "deleteProjet", "confirm", "next", "alert", "navigate", "error", "err", "console", "date", "d", "Date", "getDate", "toString", "padStart", "getMonth", "getFullYear", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "ProjetService", "i3", "FileService", "selectors", "decls", "vars", "consts", "template", "DetailProjectComponent_Template", "rf", "ctx", "DetailProjectComponent_div_18_Template", "DetailProjectComponent_div_19_Template", "ɵɵlistener", "DetailProjectComponent_Template_button_click_25_listener", "_id", "DetailProjectComponent_div_61_Template", "DetailProjectComponent_div_62_Template", "titre", "groupe", "dateLimite", "ɵɵtextInterpolate1", "statut", "description", "length", "ɵɵpureFunction1", "_c0", "ɵɵpureFunction0", "_c1", "_c2", "etudiantsRendus", "totalEtudiants", "ɵɵstyleProp", "derniersRendus", "_c3"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\admin\\projects\\detail-project\\detail-project.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\admin\\projects\\detail-project\\detail-project.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { ProjetService } from '@app/services/projects.service';\nimport { FileService } from 'src/app/services/file.service';\n\n@Component({\n  selector: 'app-detail-project',\n  templateUrl: './detail-project.component.html',\n  styleUrls: ['./detail-project.component.css'],\n})\nexport class DetailProjectComponent implements OnInit {\n  projet: any = null;\n\n  constructor(\n    private route: ActivatedRoute,\n    private router: Router,\n    private projectService: ProjetService,\n    private fileService: FileService\n  ) {}\n\n  ngOnInit(): void {\n    const id = this.route.snapshot.paramMap.get('id');\n    if (id) {\n      this.projectService.getProjetById(id).subscribe((data: any) => {\n        this.projet = data;\n      });\n    }\n  }\n\n  getFileUrl(filePath: string): string {\n    return this.fileService.getDownloadUrl(filePath);\n  }\n\n  deleteProjet(id: string | undefined): void {\n    if (!id) return;\n\n    if (confirm('Êtes-vous sûr de vouloir supprimer ce projet ?')) {\n      this.projectService.deleteProjet(id).subscribe({\n        next: () => {\n          alert('Projet supprimé avec succès');\n          this.router.navigate(['/admin/projects']);\n        },\n        error: (err) => {\n          console.error('Erreur lors de la suppression du projet', err);\n          alert('Erreur lors de la suppression du projet');\n        },\n      });\n    }\n  }\n\n  formatDate(date: string | Date): string {\n    const d = new Date(date);\n    return `${d.getDate().toString().padStart(2, '0')}/${(d.getMonth() + 1)\n      .toString()\n      .padStart(2, '0')}/${d.getFullYear()}`;\n  }\n}\n", "<div class=\"max-w-6xl mx-auto my-8 space-y-6\">\n  <!-- Header avec stats -->\n  <div class=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n    <!-- Carte principale -->\n    <div class=\"md:col-span-2 bg-white rounded-2xl shadow-md hover:shadow-xl transition-shadow duration-300 overflow-hidden border border-[#e4e7ec]\">\n      <div class=\"bg-gradient-to-r from-[#6C63FF] to-[#C77DFF] p-6 text-white rounded-t-2xl\">\n        <div class=\"flex justify-between items-start\">\n          <div>\n            <h3 class=\"text-2xl font-bold\">{{ projet?.titre }}</h3>\n            <p class=\"mt-1 text-sm text-[#e4dbf8]\">\n              Groupe: {{ projet?.groupe }} • Date limite: {{ projet?.dateLimite ? formatDate(projet?.dateLimite) : '' }}\n            </p>\n          </div>\n          <span class=\"px-3 py-1 bg-white/20 rounded-full text-xs font-semibold\">\n            {{ projet?.statut || 'En cours' }}\n          </span>\n        </div>\n      </div>\n      <div class=\"p-6 space-y-6\">\n        <div>\n          <h4 class=\"text-sm font-semibold text-gray-600 mb-2\">Description</h4>\n          <p class=\"text-gray-700\">{{ projet?.description || 'Aucune description fournie' }}</p>\n        </div>\n\n        <div *ngIf=\"projet?.fichiers?.length > 0\">\n          <h4 class=\"text-sm font-semibold text-gray-600 mb-3\">Fichiers joints</h4>\n          <div class=\"grid grid-cols-1 sm:grid-cols-2 gap-3\">\n            <div *ngFor=\"let file of projet.fichiers\" class=\"flex items-center\">\n              <a [href]=\"getFileUrl(file)\" download\n                class=\"flex-1 text-center text-sm text-white bg-gradient-to-r from-[#8E2DE2] to-[#4A00E0] hover:from-[#7c22d2] hover:to-[#3f00cc] rounded-lg py-2 px-4 transition-all hover:scale-[1.02] flex items-center justify-center space-x-2\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4\" />\n                </svg>\n                <span>Télécharger</span>\n              </a>\n            </div>\n          </div>\n        </div>\n\n        <div *ngIf=\"!projet?.fichiers || projet.fichiers.length === 0\" class=\"text-sm text-gray-500 italic\">\n          Aucun fichier joint\n        </div>\n        \n        <!-- Boutons d'action -->\n        <div class=\"flex flex-wrap gap-3 pt-4 border-t border-gray-100\">\n          <a [routerLink]=\"['/admin/projects/editProjet', projet?._id]\" \n             class=\"flex-1 bg-gradient-to-r from-[#3CAEA3] to-[#20BF55] hover:from-[#2d9b91] hover:to-[#18a046] text-white py-2.5 px-4 rounded-lg font-medium text-sm text-center shadow-sm hover:shadow-md transition-all flex items-center justify-center\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\" />\n            </svg>\n            Modifier\n          </a>\n          \n          <button (click)=\"deleteProjet(projet?._id)\" \n                  class=\"flex-1 bg-gradient-to-r from-[#F5576C] to-[#F093FB] hover:from-[#e04054] hover:to-[#d87fe0] text-white py-2.5 px-4 rounded-lg font-medium text-sm text-center shadow-sm hover:shadow-md transition-all flex items-center justify-center\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\" />\n            </svg>\n            Supprimer\n          </button>\n          \n          <a [routerLink]=\"['/admin/projects/rendus']\" [queryParams]=\"{projetId: projet?._id}\"\n             class=\"flex-1 bg-gradient-to-r from-[#6C63FF] to-[#8E2DE2] hover:from-[#5046e5] hover:to-[#7816c7] text-white py-2.5 px-4 rounded-lg font-medium text-sm text-center shadow-sm hover:shadow-md transition-all flex items-center justify-center\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\" />\n            </svg>\n            Voir les rendus\n          </a>\n        </div>\n      </div>\n    </div>\n\n    <!-- Dashboard de rendu -->\n    <div class=\"bg-white rounded-2xl shadow-md border border-[#e4e7ec] overflow-hidden\">\n      <div class=\"bg-gradient-to-r from-[#6C63FF] to-[#C77DFF] p-4 text-white\">\n        <h3 class=\"font-semibold\">Statistiques de rendu</h3>\n      </div>\n      <div class=\"p-6 space-y-6\">\n        <!-- Progression générale -->\n        <div>\n          <div class=\"flex justify-between mb-2\">\n            <span class=\"text-sm font-medium text-gray-700\">Progression</span>\n            <span class=\"text-sm font-medium text-[#6C63FF]\">{{ (projet?.etudiantsRendus?.length || 0) }}/{{ projet?.totalEtudiants || 0 }}</span>\n          </div>\n          <div class=\"w-full bg-gray-200 rounded-full h-2.5\">\n            <div class=\"bg-gradient-to-r from-[#6C63FF] to-[#C77DFF] h-2.5 rounded-full\" \n                 [style.width.%]=\"((projet?.etudiantsRendus?.length || 0) / (projet?.totalEtudiants || 1) * 100)\"></div>\n          </div>\n        </div>\n\n        <!-- Cartes stats -->\n        <div class=\"grid grid-cols-2 gap-4\">\n          <div class=\"bg-[#f0f7ff] p-4 rounded-lg border border-[#e4e7ec]\">\n            <div class=\"text-2xl font-bold text-[#4A00E0]\">{{ projet?.etudiantsRendus?.length || 0 }}</div>\n            <div class=\"text-xs text-gray-500\">Rendus</div>\n          </div>\n          <div class=\"bg-[#fff5f5] p-4 rounded-lg border border-[#e4e7ec]\">\n            <div class=\"text-2xl font-bold text-[#E02D6D]\">{{ (projet?.totalEtudiants || 0) - (projet?.etudiantsRendus?.length || 0) }}</div>\n            <div class=\"text-xs text-gray-500\">En attente</div>\n          </div>\n        </div>\n\n        <!-- Liste rapide -->\n        <div>\n          <h4 class=\"text-sm font-semibold text-gray-600 mb-3\">Derniers rendus</h4>\n          <div class=\"space-y-3\">\n            <div *ngFor=\"let etudiant of projet?.derniersRendus || []\" class=\"flex items-center space-x-3\">\n              <div class=\"h-8 w-8 rounded-full bg-[#6C63FF] flex items-center justify-center text-white text-xs font-bold\">\n                {{ etudiant.nom?.charAt(0) || '' }}{{ etudiant.prenom?.charAt(0) || '' }}\n              </div>\n              <div class=\"text-sm\">\n                <div class=\"font-medium\">{{ etudiant.prenom }} {{ etudiant.nom }}</div>\n                <div class=\"text-xs text-gray-500\">{{ formatDate(etudiant.dateRendu) }}</div>\n              </div>\n            </div>\n            <div *ngIf=\"!projet?.derniersRendus || projet.derniersRendus.length === 0\" class=\"text-sm text-gray-500 italic text-center py-2\">\n              Aucun rendu pour le moment\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n\n\n\n\n\n\n\n\n\n\n\n"], "mappings": ";;;;;;;IC2BYA,EAAA,CAAAC,cAAA,cAAoE;IAGhED,EAAA,CAAAE,cAAA,EAA8G;IAA9GF,EAAA,CAAAC,cAAA,cAA8G;IAC5GD,EAAA,CAAAG,SAAA,eAA2I;IAC7IH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAK,eAAA,EAAM;IAANL,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAM,MAAA,4BAAW;IAAAN,EAAA,CAAAI,YAAA,EAAO;;;;;IALvBJ,EAAA,CAAAO,SAAA,GAAyB;IAAzBP,EAAA,CAAAQ,UAAA,SAAAC,MAAA,CAAAC,UAAA,CAAAC,OAAA,GAAAX,EAAA,CAAAY,aAAA,CAAyB;;;;;IAJlCZ,EAAA,CAAAC,cAAA,UAA0C;IACaD,EAAA,CAAAM,MAAA,sBAAe;IAAAN,EAAA,CAAAI,YAAA,EAAK;IACzEJ,EAAA,CAAAC,cAAA,cAAmD;IACjDD,EAAA,CAAAa,UAAA,IAAAC,4CAAA,kBAQM;IACRd,EAAA,CAAAI,YAAA,EAAM;;;;IATkBJ,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAQ,UAAA,YAAAO,MAAA,CAAAC,MAAA,CAAAC,QAAA,CAAkB;;;;;IAY5CjB,EAAA,CAAAC,cAAA,cAAoG;IAClGD,EAAA,CAAAM,MAAA,4BACF;IAAAN,EAAA,CAAAI,YAAA,EAAM;;;;;IAiEFJ,EAAA,CAAAC,cAAA,cAA+F;IAE3FD,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,cAAqB;IACMD,EAAA,CAAAM,MAAA,GAAwC;IAAAN,EAAA,CAAAI,YAAA,EAAM;IACvEJ,EAAA,CAAAC,cAAA,cAAmC;IAAAD,EAAA,CAAAM,MAAA,GAAoC;IAAAN,EAAA,CAAAI,YAAA,EAAM;;;;;IAJ7EJ,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAkB,kBAAA,OAAAC,WAAA,CAAAC,GAAA,kBAAAD,WAAA,CAAAC,GAAA,CAAAC,MAAA,iBAAAF,WAAA,CAAAG,MAAA,kBAAAH,WAAA,CAAAG,MAAA,CAAAD,MAAA,gBACF;IAE2BrB,EAAA,CAAAO,SAAA,GAAwC;IAAxCP,EAAA,CAAAkB,kBAAA,KAAAC,WAAA,CAAAG,MAAA,OAAAH,WAAA,CAAAC,GAAA,KAAwC;IAC9BpB,EAAA,CAAAO,SAAA,GAAoC;IAApCP,EAAA,CAAAuB,iBAAA,CAAAC,MAAA,CAAAC,UAAA,CAAAN,WAAA,CAAAO,SAAA,EAAoC;;;;;IAG3E1B,EAAA,CAAAC,cAAA,cAAiI;IAC/HD,EAAA,CAAAM,MAAA,mCACF;IAAAN,EAAA,CAAAI,YAAA,EAAM;;;;;;;;;;;;;;;;;AD3GlB,OAAM,MAAOuB,sBAAsB;EAGjCC,YACUC,KAAqB,EACrBC,MAAc,EACdC,cAA6B,EAC7BC,WAAwB;IAHxB,KAAAH,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IANrB,KAAAhB,MAAM,GAAQ,IAAI;EAOf;EAEHiB,QAAQA,CAAA;IACN,MAAMC,EAAE,GAAG,IAAI,CAACL,KAAK,CAACM,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC;IACjD,IAAIH,EAAE,EAAE;MACN,IAAI,CAACH,cAAc,CAACO,aAAa,CAACJ,EAAE,CAAC,CAACK,SAAS,CAAEC,IAAS,IAAI;QAC5D,IAAI,CAACxB,MAAM,GAAGwB,IAAI;MACpB,CAAC,CAAC;;EAEN;EAEA9B,UAAUA,CAAC+B,QAAgB;IACzB,OAAO,IAAI,CAACT,WAAW,CAACU,cAAc,CAACD,QAAQ,CAAC;EAClD;EAEAE,YAAYA,CAACT,EAAsB;IACjC,IAAI,CAACA,EAAE,EAAE;IAET,IAAIU,OAAO,CAAC,gDAAgD,CAAC,EAAE;MAC7D,IAAI,CAACb,cAAc,CAACY,YAAY,CAACT,EAAE,CAAC,CAACK,SAAS,CAAC;QAC7CM,IAAI,EAAEA,CAAA,KAAK;UACTC,KAAK,CAAC,6BAA6B,CAAC;UACpC,IAAI,CAAChB,MAAM,CAACiB,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;QAC3C,CAAC;QACDC,KAAK,EAAGC,GAAG,IAAI;UACbC,OAAO,CAACF,KAAK,CAAC,yCAAyC,EAAEC,GAAG,CAAC;UAC7DH,KAAK,CAAC,yCAAyC,CAAC;QAClD;OACD,CAAC;;EAEN;EAEArB,UAAUA,CAAC0B,IAAmB;IAC5B,MAAMC,CAAC,GAAG,IAAIC,IAAI,CAACF,IAAI,CAAC;IACxB,OAAO,GAAGC,CAAC,CAACE,OAAO,EAAE,CAACC,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,CAACJ,CAAC,CAACK,QAAQ,EAAE,GAAG,CAAC,EACnEF,QAAQ,EAAE,CACVC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIJ,CAAC,CAACM,WAAW,EAAE,EAAE;EAC1C;;;uBA7CW/B,sBAAsB,EAAA3B,EAAA,CAAA2D,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA7D,EAAA,CAAA2D,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAA9D,EAAA,CAAA2D,iBAAA,CAAAI,EAAA,CAAAC,aAAA,GAAAhE,EAAA,CAAA2D,iBAAA,CAAAM,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAtBvC,sBAAsB;MAAAwC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCVnCzE,EAAA,CAAAC,cAAA,aAA8C;UAQHD,EAAA,CAAAM,MAAA,GAAmB;UAAAN,EAAA,CAAAI,YAAA,EAAK;UACvDJ,EAAA,CAAAC,cAAA,WAAuC;UACrCD,EAAA,CAAAM,MAAA,GACF;UAAAN,EAAA,CAAAI,YAAA,EAAI;UAENJ,EAAA,CAAAC,cAAA,eAAuE;UACrED,EAAA,CAAAM,MAAA,IACF;UAAAN,EAAA,CAAAI,YAAA,EAAO;UAGXJ,EAAA,CAAAC,cAAA,cAA2B;UAE8BD,EAAA,CAAAM,MAAA,mBAAW;UAAAN,EAAA,CAAAI,YAAA,EAAK;UACrEJ,EAAA,CAAAC,cAAA,aAAyB;UAAAD,EAAA,CAAAM,MAAA,IAAyD;UAAAN,EAAA,CAAAI,YAAA,EAAI;UAGxFJ,EAAA,CAAAa,UAAA,KAAA8D,sCAAA,kBAaM;UAEN3E,EAAA,CAAAa,UAAA,KAAA+D,sCAAA,kBAEM;UAGN5E,EAAA,CAAAC,cAAA,eAAgE;UAG5DD,EAAA,CAAAE,cAAA,EAAmH;UAAnHF,EAAA,CAAAC,cAAA,eAAmH;UACjHD,EAAA,CAAAG,SAAA,gBAAmM;UACrMH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAM,MAAA,kBACF;UAAAN,EAAA,CAAAI,YAAA,EAAI;UAEJJ,EAAA,CAAAK,eAAA,EACwP;UADxPL,EAAA,CAAAC,cAAA,kBACwP;UADhPD,EAAA,CAAA6E,UAAA,mBAAAC,yDAAA;YAAA,OAASJ,GAAA,CAAA/B,YAAA,CAAA+B,GAAA,CAAA1D,MAAA,kBAAA0D,GAAA,CAAA1D,MAAA,CAAA+D,GAAA,CAAyB;UAAA,EAAC;UAEzC/E,EAAA,CAAAE,cAAA,EAAmH;UAAnHF,EAAA,CAAAC,cAAA,eAAmH;UACjHD,EAAA,CAAAG,SAAA,gBAAyM;UAC3MH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAM,MAAA,mBACF;UAAAN,EAAA,CAAAI,YAAA,EAAS;UAETJ,EAAA,CAAAK,eAAA,EACmP;UADnPL,EAAA,CAAAC,cAAA,aACmP;UACjPD,EAAA,CAAAE,cAAA,EAAmH;UAAnHF,EAAA,CAAAC,cAAA,eAAmH;UACjHD,EAAA,CAAAG,SAAA,gBAA4M;UAC9MH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAM,MAAA,yBACF;UAAAN,EAAA,CAAAI,YAAA,EAAI;UAMVJ,EAAA,CAAAK,eAAA,EAAoF;UAApFL,EAAA,CAAAC,cAAA,eAAoF;UAEtDD,EAAA,CAAAM,MAAA,6BAAqB;UAAAN,EAAA,CAAAI,YAAA,EAAK;UAEtDJ,EAAA,CAAAC,cAAA,cAA2B;UAI2BD,EAAA,CAAAM,MAAA,mBAAW;UAAAN,EAAA,CAAAI,YAAA,EAAO;UAClEJ,EAAA,CAAAC,cAAA,gBAAiD;UAAAD,EAAA,CAAAM,MAAA,IAA8E;UAAAN,EAAA,CAAAI,YAAA,EAAO;UAExIJ,EAAA,CAAAC,cAAA,eAAmD;UACjDD,EAAA,CAAAG,SAAA,eAC4G;UAC9GH,EAAA,CAAAI,YAAA,EAAM;UAIRJ,EAAA,CAAAC,cAAA,eAAoC;UAEeD,EAAA,CAAAM,MAAA,IAA0C;UAAAN,EAAA,CAAAI,YAAA,EAAM;UAC/FJ,EAAA,CAAAC,cAAA,eAAmC;UAAAD,EAAA,CAAAM,MAAA,cAAM;UAAAN,EAAA,CAAAI,YAAA,EAAM;UAEjDJ,EAAA,CAAAC,cAAA,eAAiE;UAChBD,EAAA,CAAAM,MAAA,IAA4E;UAAAN,EAAA,CAAAI,YAAA,EAAM;UACjIJ,EAAA,CAAAC,cAAA,eAAmC;UAAAD,EAAA,CAAAM,MAAA,kBAAU;UAAAN,EAAA,CAAAI,YAAA,EAAM;UAKvDJ,EAAA,CAAAC,cAAA,WAAK;UACkDD,EAAA,CAAAM,MAAA,uBAAe;UAAAN,EAAA,CAAAI,YAAA,EAAK;UACzEJ,EAAA,CAAAC,cAAA,eAAuB;UACrBD,EAAA,CAAAa,UAAA,KAAAmE,sCAAA,kBAQM;UACNhF,EAAA,CAAAa,UAAA,KAAAoE,sCAAA,kBAEM;UACRjF,EAAA,CAAAI,YAAA,EAAM;;;UA9G2BJ,EAAA,CAAAO,SAAA,GAAmB;UAAnBP,EAAA,CAAAuB,iBAAA,CAAAmD,GAAA,CAAA1D,MAAA,kBAAA0D,GAAA,CAAA1D,MAAA,CAAAkE,KAAA,CAAmB;UAEhDlF,EAAA,CAAAO,SAAA,GACF;UADEP,EAAA,CAAAkB,kBAAA,cAAAwD,GAAA,CAAA1D,MAAA,kBAAA0D,GAAA,CAAA1D,MAAA,CAAAmE,MAAA,4BAAAT,GAAA,CAAA1D,MAAA,kBAAA0D,GAAA,CAAA1D,MAAA,CAAAoE,UAAA,IAAAV,GAAA,CAAAjD,UAAA,CAAAiD,GAAA,CAAA1D,MAAA,kBAAA0D,GAAA,CAAA1D,MAAA,CAAAoE,UAAA,YACF;UAGApF,EAAA,CAAAO,SAAA,GACF;UADEP,EAAA,CAAAqF,kBAAA,OAAAX,GAAA,CAAA1D,MAAA,kBAAA0D,GAAA,CAAA1D,MAAA,CAAAsE,MAAA,qBACF;UAMyBtF,EAAA,CAAAO,SAAA,GAAyD;UAAzDP,EAAA,CAAAuB,iBAAA,EAAAmD,GAAA,CAAA1D,MAAA,kBAAA0D,GAAA,CAAA1D,MAAA,CAAAuE,WAAA,kCAAyD;UAG9EvF,EAAA,CAAAO,SAAA,GAAkC;UAAlCP,EAAA,CAAAQ,UAAA,UAAAkE,GAAA,CAAA1D,MAAA,kBAAA0D,GAAA,CAAA1D,MAAA,CAAAC,QAAA,kBAAAyD,GAAA,CAAA1D,MAAA,CAAAC,QAAA,CAAAuE,MAAA,MAAkC;UAelCxF,EAAA,CAAAO,SAAA,GAAuD;UAAvDP,EAAA,CAAAQ,UAAA,WAAAkE,GAAA,CAAA1D,MAAA,kBAAA0D,GAAA,CAAA1D,MAAA,CAAAC,QAAA,KAAAyD,GAAA,CAAA1D,MAAA,CAAAC,QAAA,CAAAuE,MAAA,OAAuD;UAMxDxF,EAAA,CAAAO,SAAA,GAA0D;UAA1DP,EAAA,CAAAQ,UAAA,eAAAR,EAAA,CAAAyF,eAAA,KAAAC,GAAA,EAAAhB,GAAA,CAAA1D,MAAA,kBAAA0D,GAAA,CAAA1D,MAAA,CAAA+D,GAAA,EAA0D;UAgB1D/E,EAAA,CAAAO,SAAA,GAAyC;UAAzCP,EAAA,CAAAQ,UAAA,eAAAR,EAAA,CAAA2F,eAAA,KAAAC,GAAA,EAAyC,gBAAA5F,EAAA,CAAAyF,eAAA,KAAAI,GAAA,EAAAnB,GAAA,CAAA1D,MAAA,kBAAA0D,GAAA,CAAA1D,MAAA,CAAA+D,GAAA;UAqBO/E,EAAA,CAAAO,SAAA,IAA8E;UAA9EP,EAAA,CAAAkB,kBAAA,MAAAwD,GAAA,CAAA1D,MAAA,kBAAA0D,GAAA,CAAA1D,MAAA,CAAA8E,eAAA,kBAAApB,GAAA,CAAA1D,MAAA,CAAA8E,eAAA,CAAAN,MAAA,cAAAd,GAAA,CAAA1D,MAAA,kBAAA0D,GAAA,CAAA1D,MAAA,CAAA+E,cAAA,WAA8E;UAI1H/F,EAAA,CAAAO,SAAA,GAAgG;UAAhGP,EAAA,CAAAgG,WAAA,YAAAtB,GAAA,CAAA1D,MAAA,kBAAA0D,GAAA,CAAA1D,MAAA,CAAA8E,eAAA,kBAAApB,GAAA,CAAA1D,MAAA,CAAA8E,eAAA,CAAAN,MAAA,YAAAd,GAAA,CAAA1D,MAAA,kBAAA0D,GAAA,CAAA1D,MAAA,CAAA+E,cAAA,mBAAgG;UAOtD/F,EAAA,CAAAO,SAAA,GAA0C;UAA1CP,EAAA,CAAAuB,iBAAA,EAAAmD,GAAA,CAAA1D,MAAA,kBAAA0D,GAAA,CAAA1D,MAAA,CAAA8E,eAAA,kBAAApB,GAAA,CAAA1D,MAAA,CAAA8E,eAAA,CAAAN,MAAA,OAA0C;UAI1CxF,EAAA,CAAAO,SAAA,GAA4E;UAA5EP,EAAA,CAAAuB,iBAAA,GAAAmD,GAAA,CAAA1D,MAAA,kBAAA0D,GAAA,CAAA1D,MAAA,CAAA+E,cAAA,YAAArB,GAAA,CAAA1D,MAAA,kBAAA0D,GAAA,CAAA1D,MAAA,CAAA8E,eAAA,kBAAApB,GAAA,CAAA1D,MAAA,CAAA8E,eAAA,CAAAN,MAAA,QAA4E;UASjGxF,EAAA,CAAAO,SAAA,GAA+B;UAA/BP,EAAA,CAAAQ,UAAA,aAAAkE,GAAA,CAAA1D,MAAA,kBAAA0D,GAAA,CAAA1D,MAAA,CAAAiF,cAAA,KAAAjG,EAAA,CAAA2F,eAAA,KAAAO,GAAA,EAA+B;UASnDlG,EAAA,CAAAO,SAAA,GAAmE;UAAnEP,EAAA,CAAAQ,UAAA,WAAAkE,GAAA,CAAA1D,MAAA,kBAAA0D,GAAA,CAAA1D,MAAA,CAAAiF,cAAA,KAAAvB,GAAA,CAAA1D,MAAA,CAAAiF,cAAA,CAAAT,MAAA,OAAmE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}