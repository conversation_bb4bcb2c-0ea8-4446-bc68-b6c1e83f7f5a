{"ast": null, "code": "import { finalize } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/equipe.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/services/notification.service\";\nimport * as i4 from \"@angular/common\";\nfunction EquipeListComponent_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"div\", 20);\n    i0.ɵɵelement(2, \"div\", 21)(3, \"div\", 22);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction EquipeListComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"div\", 24)(2, \"div\", 25);\n    i0.ɵɵelement(3, \"i\", 26);\n    i0.ɵɵelementStart(4, \"div\", 27);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function EquipeListComponent_div_31_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.loadEquipes());\n    });\n    i0.ɵɵelement(7, \"i\", 29);\n    i0.ɵɵtext(8, \" R\\u00E9essayer \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.error, \" \");\n  }\n}\nfunction EquipeListComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"div\", 30)(2, \"div\", 31);\n    i0.ɵɵelement(3, \"i\", 32);\n    i0.ɵɵelementStart(4, \"h3\", 33);\n    i0.ɵɵtext(5, \"Aucune \\u00E9quipe trouv\\u00E9e\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 34);\n    i0.ɵɵtext(7, \" Commencez par cr\\u00E9er une nouvelle \\u00E9quipe pour organiser vos projets et membres. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function EquipeListComponent_div_32_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.navigateToAddEquipe());\n    });\n    i0.ɵɵelement(9, \"i\", 36);\n    i0.ɵɵtext(10, \" Cr\\u00E9er une \\u00E9quipe \");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction EquipeListComponent_div_33_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"div\", 40)(2, \"div\", 41)(3, \"h5\", 42);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 43)(6, \"p\", 44);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"slice\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 45);\n    i0.ɵɵelement(10, \"i\", 46);\n    i0.ɵɵelementStart(11, \"span\", 47);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(13, \"div\", 48)(14, \"div\", 49)(15, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function EquipeListComponent_div_33_div_1_Template_button_click_15_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r11);\n      const equipe_r9 = restoredCtx.$implicit;\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(equipe_r9._id && ctx_r10.navigateToEquipeDetail(equipe_r9._id));\n    });\n    i0.ɵɵelement(16, \"i\", 51);\n    i0.ɵɵtext(17, \" D\\u00E9tails \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\")(19, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function EquipeListComponent_div_33_div_1_Template_button_click_19_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r11);\n      const equipe_r9 = restoredCtx.$implicit;\n      const ctx_r12 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(equipe_r9._id && ctx_r12.navigateToEditEquipe(equipe_r9._id));\n    });\n    i0.ɵɵelement(20, \"i\", 53);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"button\", 54);\n    i0.ɵɵlistener(\"click\", function EquipeListComponent_div_33_div_1_Template_button_click_21_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r11);\n      const equipe_r9 = restoredCtx.$implicit;\n      const ctx_r13 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(equipe_r9._id && ctx_r13.deleteEquipe(equipe_r9._id));\n    });\n    i0.ɵɵelement(22, \"i\", 55);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(23, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function EquipeListComponent_div_33_div_1_Template_button_click_23_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r11);\n      const equipe_r9 = restoredCtx.$implicit;\n      const ctx_r14 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(equipe_r9._id && ctx_r14.navigateToTasks(equipe_r9._id));\n    });\n    i0.ɵɵelement(24, \"i\", 57);\n    i0.ɵɵtext(25, \" Tasks \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const equipe_r9 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(equipe_r9.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", equipe_r9.description && equipe_r9.description.length > 100 ? i0.ɵɵpipeBind3(8, 3, equipe_r9.description, 0, 100) + \"...\" : equipe_r9.description || \"Aucune description\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", (equipe_r9.members == null ? null : equipe_r9.members.length) || 0, \" membre(s) \");\n  }\n}\nfunction EquipeListComponent_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵtemplate(1, EquipeListComponent_div_33_div_1_Template, 26, 7, \"div\", 38);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.equipes);\n  }\n}\nexport class EquipeListComponent {\n  constructor(equipeService, router, notificationService) {\n    this.equipeService = equipeService;\n    this.router = router;\n    this.notificationService = notificationService;\n    this.equipes = [];\n    this.loading = false;\n    this.error = null;\n  }\n  ngOnInit() {\n    this.loadEquipes();\n  }\n  loadEquipes() {\n    this.loading = true;\n    this.error = null;\n    this.equipeService.getEquipes().pipe(finalize(() => this.loading = false)).subscribe({\n      next: data => {\n        console.log('Équipes chargées:', data);\n        this.equipes = data;\n        // Trier les équipes par nom\n        this.equipes.sort((a, b) => {\n          if (a.name && b.name) {\n            return a.name.localeCompare(b.name);\n          }\n          return 0;\n        });\n      },\n      error: error => {\n        console.error('Erreur lors du chargement des équipes:', error);\n        this.error = 'Impossible de charger les équipes. Veuillez réessayer plus tard.';\n        this.notificationService.showError('Erreur lors du chargement des équipes');\n      }\n    });\n  }\n  navigateToAddEquipe() {\n    this.router.navigate(['/equipes/ajouter']);\n  }\n  navigateToEditEquipe(id) {\n    this.router.navigate(['/equipes/modifier', id]);\n  }\n  navigateToEquipeDetail(id) {\n    this.router.navigate(['/equipes/detail', id]);\n  }\n  deleteEquipe(id) {\n    if (!id) {\n      console.error('ID est indéfini');\n      this.notificationService.showError('ID d\\'équipe invalide');\n      return;\n    }\n    // Trouver le nom de l'équipe pour l'afficher dans le message de confirmation\n    const equipe = this.equipes.find(e => e._id === id);\n    const equipeName = equipe?.name || 'cette équipe';\n    if (confirm(`Êtes-vous sûr de vouloir supprimer l'équipe \"${equipeName}\" ?`)) {\n      this.loading = true;\n      this.equipeService.deleteEquipe(id).pipe(finalize(() => this.loading = false)).subscribe({\n        next: () => {\n          console.log('Équipe supprimée avec succès');\n          this.notificationService.showSuccess(`L'équipe \"${equipeName}\" a été supprimée avec succès`);\n          this.loadEquipes();\n        },\n        error: error => {\n          console.error('Erreur lors de la suppression de l\\'équipe:', error);\n          this.error = 'Impossible de supprimer l\\'équipe. Veuillez réessayer plus tard.';\n          this.notificationService.showError(`Erreur lors de la suppression de l'équipe \"${equipeName}\"`);\n        }\n      });\n    }\n  }\n  navigateToTasks(id) {\n    if (!id) {\n      console.error('ID est indéfini');\n      this.notificationService.showError('ID d\\'équipe invalide');\n      return;\n    }\n    const equipe = this.equipes.find(e => e._id === id);\n    const equipeName = equipe?.name || 'cette équipe';\n    // Naviguer vers la page des tâches de l'équipe\n    this.router.navigate(['/equipe/tasks', id]);\n  }\n  static {\n    this.ɵfac = function EquipeListComponent_Factory(t) {\n      return new (t || EquipeListComponent)(i0.ɵɵdirectiveInject(i1.EquipeService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.NotificationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: EquipeListComponent,\n      selectors: [[\"app-equipe-list\"]],\n      decls: 34,\n      vars: 4,\n      consts: [[1, \"min-h-screen\", \"bg-[#edf1f4]\", \"dark:bg-[#121212]\", \"p-4\", \"md:p-6\", \"relative\"], [1, \"absolute\", \"inset-0\", \"overflow-hidden\", \"pointer-events-none\"], [1, \"absolute\", \"top-[15%]\", \"left-[10%]\", \"w-64\", \"h-64\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#dac4ea]/5\", \"to-transparent\", \"dark:from-[#dac4ea]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"bottom-[20%]\", \"right-[10%]\", \"w-80\", \"h-80\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#dac4ea]/5\", \"to-transparent\", \"dark:from-[#dac4ea]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"inset-0\", \"opacity-5\", \"dark:opacity-[0.03]\"], [1, \"h-full\", \"grid\", \"grid-cols-12\"], [1, \"border-r\", \"border-[#dac4ea]\", \"dark:border-[#dac4ea]\"], [1, \"max-w-6xl\", \"mx-auto\", \"relative\", \"z-10\"], [1, \"flex\", \"flex-col\", \"md:flex-row\", \"md:items-center\", \"md:justify-between\", \"mb-8\"], [1, \"text-3xl\", \"font-bold\", \"bg-gradient-to-r\", \"from-[#8b5a9f]\", \"to-[#dac4ea]\", \"dark:from-[#dac4ea]\", \"dark:to-[#8b5a9f]\", \"bg-clip-text\", \"text-transparent\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"text-sm\", \"md:text-base\", \"mt-1\"], [1, \"mt-4\", \"md:mt-0\", \"relative\", \"overflow-hidden\", \"group\", 3, \"click\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#8b5a9f]\", \"to-[#dac4ea]\", \"dark:from-[#8b5a9f]\", \"dark:to-[#dac4ea]\", \"rounded-lg\", \"transition-transform\", \"duration-300\", \"group-hover:scale-105\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#8b5a9f]\", \"to-[#dac4ea]\", \"dark:from-[#8b5a9f]\", \"dark:to-[#dac4ea]\", \"rounded-lg\", \"opacity-0\", \"group-hover:opacity-100\", \"blur-md\", \"transition-opacity\", \"duration-300\"], [1, \"relative\", \"flex\", \"items-center\", \"text-white\", \"font-medium\", \"px-6\", \"py-3\", \"rounded-lg\", \"transition-all\", \"z-10\"], [1, \"fas\", \"fa-plus\", \"mr-2\", \"group-hover:scale-110\", \"transition-transform\"], [\"class\", \"flex justify-center my-12\", 4, \"ngIf\"], [\"class\", \"row justify-content-center my-5\", 4, \"ngIf\"], [\"class\", \"row g-4\", 4, \"ngIf\"], [1, \"flex\", \"justify-center\", \"my-12\"], [1, \"relative\"], [1, \"w-14\", \"h-14\", \"border-4\", \"border-[#dac4ea]/20\", \"dark:border-[#dac4ea]/20\", \"border-t-[#dac4ea]\", \"dark:border-t-[#dac4ea]\", \"rounded-full\", \"animate-spin\"], [1, \"absolute\", \"inset-0\", \"bg-[#dac4ea]/20\", \"dark:bg-[#dac4ea]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"row\", \"justify-content-center\", \"my-5\"], [1, \"col-md-8\"], [1, \"alert\", \"alert-danger\", \"shadow-sm\", \"border-0\", \"rounded-3\", \"d-flex\", \"align-items-center\"], [1, \"bi\", \"bi-exclamation-triangle-fill\", \"fs-3\", \"me-3\"], [1, \"flex-grow-1\"], [1, \"btn\", \"btn-danger\", \"rounded-pill\", \"ms-3\", 3, \"click\"], [1, \"bi\", \"bi-arrow-clockwise\", \"me-1\"], [1, \"col-md-8\", \"text-center\"], [1, \"p-5\", \"bg-white\", \"rounded-3\", \"shadow-sm\"], [1, \"bi\", \"bi-people\", \"fs-1\", \"text-muted\", \"mb-3\"], [1, \"mb-3\"], [1, \"text-muted\", \"mb-4\"], [1, \"btn\", \"btn-primary\", \"rounded-pill\", \"px-4\", \"py-2\", 3, \"click\"], [1, \"bi\", \"bi-plus-circle\", \"me-2\"], [1, \"row\", \"g-4\"], [\"class\", \"col-md-4 col-lg-3\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-md-4\", \"col-lg-3\"], [1, \"card\", \"h-100\", \"border-0\", \"shadow-sm\", \"rounded-3\", \"hover-shadow\", \"transition\"], [1, \"card-header\", \"bg-primary\", \"text-white\", \"rounded-top\", \"p-3\"], [1, \"card-title\", \"mb-0\", \"text-truncate\"], [1, \"card-body\", \"p-4\"], [1, \"card-text\", \"mb-4\", 2, \"min-height\", \"60px\"], [1, \"d-flex\", \"align-items-center\", \"mb-3\"], [1, \"bi\", \"bi-people-fill\", \"text-primary\", \"me-2\"], [1, \"badge\", \"bg-light\", \"text-dark\", \"rounded-pill\", \"px-3\", \"py-2\", \"shadow-sm\"], [1, \"card-footer\", \"bg-white\", \"border-0\", \"p-3\"], [1, \"d-flex\", \"justify-content-between\", \"mb-2\"], [\"title\", \"Voir les d\\u00E9tails\", 1, \"btn\", \"btn-sm\", \"btn-outline-primary\", \"rounded-pill\", 3, \"click\"], [1, \"bi\", \"bi-eye\", \"me-1\"], [\"title\", \"Modifier l'\\u00E9quipe\", 1, \"btn\", \"btn-sm\", \"btn-outline-secondary\", \"rounded-circle\", \"me-2\", 3, \"click\"], [1, \"bi\", \"bi-pencil\"], [\"title\", \"Supprimer l'\\u00E9quipe\", 1, \"btn\", \"btn-sm\", \"btn-outline-danger\", \"rounded-circle\", 3, \"click\"], [1, \"bi\", \"bi-trash\"], [\"title\", \"G\\u00E9rer les t\\u00E2ches de l'\\u00E9quipe\", 1, \"btn\", \"btn-sm\", \"btn-success\", \"w-100\", \"rounded-pill\", 3, \"click\"], [1, \"bi\", \"bi-list-check\", \"me-1\"]],\n      template: function EquipeListComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵelement(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5);\n          i0.ɵɵelement(6, \"div\", 6)(7, \"div\", 6)(8, \"div\", 6)(9, \"div\", 6)(10, \"div\", 6)(11, \"div\", 6)(12, \"div\", 6)(13, \"div\", 6)(14, \"div\", 6)(15, \"div\", 6)(16, \"div\", 6);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(17, \"div\", 7)(18, \"div\", 8)(19, \"div\")(20, \"h1\", 9);\n          i0.ɵɵtext(21, \" \\u00C9quipes \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"p\", 10);\n          i0.ɵɵtext(23, \" G\\u00E9rez vos \\u00E9quipes et leurs membres \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(24, \"button\", 11);\n          i0.ɵɵlistener(\"click\", function EquipeListComponent_Template_button_click_24_listener() {\n            return ctx.navigateToAddEquipe();\n          });\n          i0.ɵɵelement(25, \"div\", 12)(26, \"div\", 13);\n          i0.ɵɵelementStart(27, \"span\", 14);\n          i0.ɵɵelement(28, \"i\", 15);\n          i0.ɵɵtext(29, \" Nouvelle \\u00C9quipe \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(30, EquipeListComponent_div_30_Template, 4, 0, \"div\", 16);\n          i0.ɵɵtemplate(31, EquipeListComponent_div_31_Template, 9, 1, \"div\", 17);\n          i0.ɵɵtemplate(32, EquipeListComponent_div_32_Template, 11, 0, \"div\", 17);\n          i0.ɵɵtemplate(33, EquipeListComponent_div_33_Template, 2, 1, \"div\", 18);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(30);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && !ctx.error && ctx.equipes.length === 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.equipes.length > 0);\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i4.SlicePipe],\n      styles: [\".hover-shadow[_ngcontent-%COMP%]:hover {\\n    transform: translateY(-5px);\\n    box-shadow: 0 10px 20px rgba(0,0,0,0.1) !important;\\n  }\\n\\n  .transition[_ngcontent-%COMP%] {\\n    transition: all 0.3s ease;\\n  }\\n\\n  .card-header.bg-primary[_ngcontent-%COMP%] {\\n    background: linear-gradient(45deg, #007bff, #6610f2) !important;\\n  }\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImVxdWlwZS1saXN0LmNvbXBvbmVudC5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkVBQUU7SUFDRSwyQkFBMkI7SUFDM0Isa0RBQWtEO0VBQ3BEOztFQUVBO0lBQ0UseUJBQXlCO0VBQzNCOztFQUVBO0lBQ0UsK0RBQStEO0VBQ2pFIiwiZmlsZSI6ImVxdWlwZS1saXN0LmNvbXBvbmVudC5jc3MiLCJzb3VyY2VzQ29udGVudCI6WyIgIC5ob3Zlci1zaGFkb3c6aG92ZXIge1xyXG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC01cHgpO1xyXG4gICAgYm94LXNoYWRvdzogMCAxMHB4IDIwcHggcmdiYSgwLDAsMCwwLjEpICFpbXBvcnRhbnQ7XHJcbiAgfVxyXG5cclxuICAudHJhbnNpdGlvbiB7XHJcbiAgICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xyXG4gIH1cclxuXHJcbiAgLmNhcmQtaGVhZGVyLmJnLXByaW1hcnkge1xyXG4gICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDQ1ZGVnLCAjMDA3YmZmLCAjNjYxMGYyKSAhaW1wb3J0YW50O1xyXG4gIH0iXX0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvYWRtaW4vZXF1aXBlcy9lcXVpcGUtbGlzdC9lcXVpcGUtbGlzdC5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJFQUFFO0lBQ0UsMkJBQTJCO0lBQzNCLGtEQUFrRDtFQUNwRDs7RUFFQTtJQUNFLHlCQUF5QjtFQUMzQjs7RUFFQTtJQUNFLCtEQUErRDtFQUNqRTtBQUNGLDR1QkFBNHVCIiwic291cmNlc0NvbnRlbnQiOlsiICAuaG92ZXItc2hhZG93OmhvdmVyIHtcclxuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtNXB4KTtcclxuICAgIGJveC1zaGFkb3c6IDAgMTBweCAyMHB4IHJnYmEoMCwwLDAsMC4xKSAhaW1wb3J0YW50O1xyXG4gIH1cclxuXHJcbiAgLnRyYW5zaXRpb24ge1xyXG4gICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcclxuICB9XHJcblxyXG4gIC5jYXJkLWhlYWRlci5iZy1wcmltYXJ5IHtcclxuICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCg0NWRlZywgIzAwN2JmZiwgIzY2MTBmMikgIWltcG9ydGFudDtcclxuICB9Il0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["finalize", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "EquipeListComponent_div_31_Template_button_click_6_listener", "ɵɵrestoreView", "_r5", "ctx_r4", "ɵɵnextContext", "ɵɵresetView", "loadEquipes", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r1", "error", "EquipeListComponent_div_32_Template_button_click_8_listener", "_r7", "ctx_r6", "navigateToAddEquipe", "EquipeListComponent_div_33_div_1_Template_button_click_15_listener", "restoredCtx", "_r11", "equipe_r9", "$implicit", "ctx_r10", "_id", "navigateToEquipeDetail", "EquipeListComponent_div_33_div_1_Template_button_click_19_listener", "ctx_r12", "navigateToEditEquipe", "EquipeListComponent_div_33_div_1_Template_button_click_21_listener", "ctx_r13", "deleteEquipe", "EquipeListComponent_div_33_div_1_Template_button_click_23_listener", "ctx_r14", "navigateToTasks", "ɵɵtextInterpolate", "name", "description", "length", "ɵɵpipeBind3", "members", "ɵɵtemplate", "EquipeListComponent_div_33_div_1_Template", "ɵɵproperty", "ctx_r3", "equipes", "EquipeListComponent", "constructor", "equipeService", "router", "notificationService", "loading", "ngOnInit", "getEquipes", "pipe", "subscribe", "next", "data", "console", "log", "sort", "a", "b", "localeCompare", "showError", "navigate", "id", "equipe", "find", "e", "equipeName", "confirm", "showSuccess", "ɵɵdirectiveInject", "i1", "EquipeService", "i2", "Router", "i3", "NotificationService", "selectors", "decls", "vars", "consts", "template", "EquipeListComponent_Template", "rf", "ctx", "EquipeListComponent_Template_button_click_24_listener", "EquipeListComponent_div_30_Template", "EquipeListComponent_div_31_Template", "EquipeListComponent_div_32_Template", "EquipeListComponent_div_33_Template"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\admin\\equipes\\equipe-list\\equipe-list.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\admin\\equipes\\equipe-list\\equipe-list.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { EquipeService } from 'src/app/services/equipe.service';\nimport { NotificationService } from 'src/app/services/notification.service';\nimport { Equipe } from 'src/app/models/equipe.model';\nimport { finalize } from 'rxjs/operators';\n\n@Component({\n  selector: 'app-equipe-list',\n  templateUrl: './equipe-list.component.html',\n  styleUrls: ['./equipe-list.component.css']\n})\nexport class EquipeListComponent implements OnInit {\n  equipes: Equipe[] = [];\n  loading = false;\n  error: string | null = null;\n\n  constructor(\n    private equipeService: EquipeService,\n    private router: Router,\n    private notificationService: NotificationService\n  ) {}\n\n  ngOnInit(): void {\n    this.loadEquipes();\n  }\n\n  loadEquipes(): void {\n    this.loading = true;\n    this.error = null;\n\n    this.equipeService.getEquipes().pipe(\n      finalize(() => this.loading = false)\n    ).subscribe({\n      next: (data) => {\n        console.log('Équipes chargées:', data);\n        this.equipes = data;\n\n        // Trier les équipes par nom\n        this.equipes.sort((a, b) => {\n          if (a.name && b.name) {\n            return a.name.localeCompare(b.name);\n          }\n          return 0;\n        });\n      },\n      error: (error) => {\n        console.error('Erreur lors du chargement des équipes:', error);\n        this.error = 'Impossible de charger les équipes. Veuillez réessayer plus tard.';\n        this.notificationService.showError('Erreur lors du chargement des équipes');\n      }\n    });\n  }\n\n  navigateToAddEquipe(): void {\n    this.router.navigate(['/equipes/ajouter']);\n  }\n\n  navigateToEditEquipe(id: string): void {\n    this.router.navigate(['/equipes/modifier', id]);\n  }\n\n  navigateToEquipeDetail(id: string): void {\n    this.router.navigate(['/equipes/detail', id]);\n  }\n\n  deleteEquipe(id: string): void {\n    if (!id) {\n      console.error('ID est indéfini');\n      this.notificationService.showError('ID d\\'équipe invalide');\n      return;\n    }\n\n    // Trouver le nom de l'équipe pour l'afficher dans le message de confirmation\n    const equipe = this.equipes.find(e => e._id === id);\n    const equipeName = equipe?.name || 'cette équipe';\n\n    if (confirm(`Êtes-vous sûr de vouloir supprimer l'équipe \"${equipeName}\" ?`)) {\n      this.loading = true;\n\n      this.equipeService.deleteEquipe(id).pipe(\n        finalize(() => this.loading = false)\n      ).subscribe({\n        next: () => {\n          console.log('Équipe supprimée avec succès');\n          this.notificationService.showSuccess(`L'équipe \"${equipeName}\" a été supprimée avec succès`);\n          this.loadEquipes();\n        },\n        error: (error) => {\n          console.error('Erreur lors de la suppression de l\\'équipe:', error);\n          this.error = 'Impossible de supprimer l\\'équipe. Veuillez réessayer plus tard.';\n          this.notificationService.showError(`Erreur lors de la suppression de l'équipe \"${equipeName}\"`);\n        }\n      });\n    }\n  }\n\n  navigateToTasks(id: string): void {\n    if (!id) {\n      console.error('ID est indéfini');\n      this.notificationService.showError('ID d\\'équipe invalide');\n      return;\n    }\n\n    const equipe = this.equipes.find(e => e._id === id);\n    const equipeName = equipe?.name || 'cette équipe';\n\n    // Naviguer vers la page des tâches de l'équipe\n    this.router.navigate(['/equipe/tasks', id]);    }\n}\n\n", "<div class=\"min-h-screen bg-[#edf1f4] dark:bg-[#121212] p-4 md:p-6 relative\">\n  <!-- Background decorative elements -->\n  <div class=\"absolute inset-0 overflow-hidden pointer-events-none\">\n    <div\n      class=\"absolute top-[15%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-br from-[#dac4ea]/5 to-transparent dark:from-[#dac4ea]/3 dark:to-transparent blur-3xl\"\n    ></div>\n    <div\n      class=\"absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#dac4ea]/5 to-transparent dark:from-[#dac4ea]/3 dark:to-transparent blur-3xl\"\n    ></div>\n\n    <!-- Grid pattern -->\n    <div class=\"absolute inset-0 opacity-5 dark:opacity-[0.03]\">\n      <div class=\"h-full grid grid-cols-12\">\n        <div class=\"border-r border-[#dac4ea] dark:border-[#dac4ea]\"></div>\n        <div class=\"border-r border-[#dac4ea] dark:border-[#dac4ea]\"></div>\n        <div class=\"border-r border-[#dac4ea] dark:border-[#dac4ea]\"></div>\n        <div class=\"border-r border-[#dac4ea] dark:border-[#dac4ea]\"></div>\n        <div class=\"border-r border-[#dac4ea] dark:border-[#dac4ea]\"></div>\n        <div class=\"border-r border-[#dac4ea] dark:border-[#dac4ea]\"></div>\n        <div class=\"border-r border-[#dac4ea] dark:border-[#dac4ea]\"></div>\n        <div class=\"border-r border-[#dac4ea] dark:border-[#dac4ea]\"></div>\n        <div class=\"border-r border-[#dac4ea] dark:border-[#dac4ea]\"></div>\n        <div class=\"border-r border-[#dac4ea] dark:border-[#dac4ea]\"></div>\n        <div class=\"border-r border-[#dac4ea] dark:border-[#dac4ea]\"></div>\n      </div>\n    </div>\n  </div>\n\n  <div class=\"max-w-6xl mx-auto relative z-10\">\n    <!-- Header avec titre et bouton d'ajout -->\n    <div\n      class=\"flex flex-col md:flex-row md:items-center md:justify-between mb-8\"\n    >\n      <div>\n        <h1\n          class=\"text-3xl font-bold bg-gradient-to-r from-[#8b5a9f] to-[#dac4ea] dark:from-[#dac4ea] dark:to-[#8b5a9f] bg-clip-text text-transparent\"\n        >\n          Équipes\n        </h1>\n        <p class=\"text-[#6d6870] dark:text-[#a0a0a0] text-sm md:text-base mt-1\">\n          Gérez vos équipes et leurs membres\n        </p>\n      </div>\n      <button\n        (click)=\"navigateToAddEquipe()\"\n        class=\"mt-4 md:mt-0 relative overflow-hidden group\"\n      >\n        <div\n          class=\"absolute inset-0 bg-gradient-to-r from-[#8b5a9f] to-[#dac4ea] dark:from-[#8b5a9f] dark:to-[#dac4ea] rounded-lg transition-transform duration-300 group-hover:scale-105\"\n        ></div>\n        <div\n          class=\"absolute inset-0 bg-gradient-to-r from-[#8b5a9f] to-[#dac4ea] dark:from-[#8b5a9f] dark:to-[#dac4ea] rounded-lg opacity-0 group-hover:opacity-100 blur-md transition-opacity duration-300\"\n        ></div>\n        <span\n          class=\"relative flex items-center text-white font-medium px-6 py-3 rounded-lg transition-all z-10\"\n        >\n          <i\n            class=\"fas fa-plus mr-2 group-hover:scale-110 transition-transform\"\n          ></i>\n          Nouvelle Équipe\n        </span>\n      </button>\n    </div>\n\n    <!-- Loading Indicator -->\n    <div *ngIf=\"loading\" class=\"flex justify-center my-12\">\n      <div class=\"relative\">\n        <div\n          class=\"w-14 h-14 border-4 border-[#dac4ea]/20 dark:border-[#dac4ea]/20 border-t-[#dac4ea] dark:border-t-[#dac4ea] rounded-full animate-spin\"\n        ></div>\n        <!-- Glow effect -->\n        <div\n          class=\"absolute inset-0 bg-[#dac4ea]/20 dark:bg-[#dac4ea]/20 blur-xl rounded-full transform scale-150 -z-10\"\n        ></div>\n      </div>\n    </div>\n\n    <!-- Message d'erreur avec style moderne -->\n    <div *ngIf=\"error\" class=\"row justify-content-center my-5\">\n      <div class=\"col-md-8\">\n        <div\n          class=\"alert alert-danger shadow-sm border-0 rounded-3 d-flex align-items-center\"\n        >\n          <i class=\"bi bi-exclamation-triangle-fill fs-3 me-3\"></i>\n          <div class=\"flex-grow-1\">\n            {{ error }}\n          </div>\n          <button\n            class=\"btn btn-danger rounded-pill ms-3\"\n            (click)=\"loadEquipes()\"\n          >\n            <i class=\"bi bi-arrow-clockwise me-1\"></i> Réessayer\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Message quand aucune équipe n'est trouvée -->\n    <div\n      *ngIf=\"!loading && !error && equipes.length === 0\"\n      class=\"row justify-content-center my-5\"\n    >\n      <div class=\"col-md-8 text-center\">\n        <div class=\"p-5 bg-white rounded-3 shadow-sm\">\n          <i class=\"bi bi-people fs-1 text-muted mb-3\"></i>\n          <h3 class=\"mb-3\">Aucune équipe trouvée</h3>\n          <p class=\"text-muted mb-4\">\n            Commencez par créer une nouvelle équipe pour organiser vos projets\n            et membres.\n          </p>\n          <button\n            class=\"btn btn-primary rounded-pill px-4 py-2\"\n            (click)=\"navigateToAddEquipe()\"\n          >\n            <i class=\"bi bi-plus-circle me-2\"></i> Créer une équipe\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Liste des équipes avec design moderne en cartes -->\n    <div class=\"row g-4\" *ngIf=\"equipes.length > 0\">\n      <div class=\"col-md-4 col-lg-3\" *ngFor=\"let equipe of equipes\">\n        <div\n          class=\"card h-100 border-0 shadow-sm rounded-3 hover-shadow transition\"\n        >\n          <!-- En-tête de la carte avec couleur aléatoire -->\n          <div class=\"card-header bg-primary text-white rounded-top p-3\">\n            <h5 class=\"card-title mb-0 text-truncate\">{{ equipe.name }}</h5>\n          </div>\n\n          <div class=\"card-body p-4\">\n            <!-- Description avec limite de caractères -->\n            <p class=\"card-text mb-4\" style=\"min-height: 60px\">\n              {{\n                equipe.description && equipe.description.length > 100\n                  ? (equipe.description | slice : 0 : 100) + \"...\"\n                  : equipe.description || \"Aucune description\"\n              }}\n            </p>\n\n            <!-- Badge pour le nombre de membres -->\n            <div class=\"d-flex align-items-center mb-3\">\n              <i class=\"bi bi-people-fill text-primary me-2\"></i>\n              <span\n                class=\"badge bg-light text-dark rounded-pill px-3 py-2 shadow-sm\"\n              >\n                {{ equipe.members?.length || 0 }} membre(s)\n              </span>\n            </div>\n          </div>\n\n          <!-- Actions avec boutons modernes -->\n          <div class=\"card-footer bg-white border-0 p-3\">\n            <div class=\"d-flex justify-content-between mb-2\">\n              <button\n                class=\"btn btn-sm btn-outline-primary rounded-pill\"\n                (click)=\"equipe._id && navigateToEquipeDetail(equipe._id)\"\n                title=\"Voir les détails\"\n              >\n                <i class=\"bi bi-eye me-1\"></i> Détails\n              </button>\n              <div>\n                <button\n                  class=\"btn btn-sm btn-outline-secondary rounded-circle me-2\"\n                  (click)=\"equipe._id && navigateToEditEquipe(equipe._id)\"\n                  title=\"Modifier l'équipe\"\n                >\n                  <i class=\"bi bi-pencil\"></i>\n                </button>\n                <button\n                  class=\"btn btn-sm btn-outline-danger rounded-circle\"\n                  (click)=\"equipe._id && deleteEquipe(equipe._id)\"\n                  title=\"Supprimer l'équipe\"\n                >\n                  <i class=\"bi bi-trash\"></i>\n                </button>\n              </div>\n            </div>\n            <!-- Bouton Tasks -->\n            <button\n              class=\"btn btn-sm btn-success w-100 rounded-pill\"\n              (click)=\"equipe._id && navigateToTasks(equipe._id)\"\n              title=\"Gérer les tâches de l'équipe\"\n            >\n              <i class=\"bi bi-list-check me-1\"></i> Tasks\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AAKA,SAASA,QAAQ,QAAQ,gBAAgB;;;;;;;;IC4DrCC,EAAA,CAAAC,cAAA,cAAuD;IAEnDD,EAAA,CAAAE,SAAA,cAEO;IAKTF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAIRH,EAAA,CAAAC,cAAA,cAA2D;IAKrDD,EAAA,CAAAE,SAAA,YAAyD;IACzDF,EAAA,CAAAC,cAAA,cAAyB;IACvBD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,iBAGC;IADCD,EAAA,CAAAK,UAAA,mBAAAC,4DAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IAEvBZ,EAAA,CAAAE,SAAA,YAA0C;IAACF,EAAA,CAAAI,MAAA,uBAC7C;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;IAPPH,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,MAAAC,MAAA,CAAAC,KAAA,MACF;;;;;;IAYNhB,EAAA,CAAAC,cAAA,cAGC;IAGKD,EAAA,CAAAE,SAAA,YAAiD;IACjDF,EAAA,CAAAC,cAAA,aAAiB;IAAAD,EAAA,CAAAI,MAAA,sCAAqB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAC3CH,EAAA,CAAAC,cAAA,YAA2B;IACzBD,EAAA,CAAAI,MAAA,iGAEF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAC,cAAA,iBAGC;IADCD,EAAA,CAAAK,UAAA,mBAAAY,4DAAA;MAAAjB,EAAA,CAAAO,aAAA,CAAAW,GAAA;MAAA,MAAAC,MAAA,GAAAnB,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAQ,MAAA,CAAAC,mBAAA,EAAqB;IAAA,EAAC;IAE/BpB,EAAA,CAAAE,SAAA,YAAsC;IAACF,EAAA,CAAAI,MAAA,oCACzC;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;;;IAObH,EAAA,CAAAC,cAAA,cAA8D;IAMdD,EAAA,CAAAI,MAAA,GAAiB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAGlEH,EAAA,CAAAC,cAAA,cAA2B;IAGvBD,EAAA,CAAAI,MAAA,GAKF;;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAGJH,EAAA,CAAAC,cAAA,cAA4C;IAC1CD,EAAA,CAAAE,SAAA,aAAmD;IACnDF,EAAA,CAAAC,cAAA,gBAEC;IACCD,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAKXH,EAAA,CAAAC,cAAA,eAA+C;IAIzCD,EAAA,CAAAK,UAAA,mBAAAgB,mEAAA;MAAA,MAAAC,WAAA,GAAAtB,EAAA,CAAAO,aAAA,CAAAgB,IAAA;MAAA,MAAAC,SAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAC,OAAA,GAAA1B,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAa,SAAA,CAAAG,GAAA,IAAcD,OAAA,CAAAE,sBAAA,CAAAJ,SAAA,CAAAG,GAAA,CAAkC;IAAA,EAAC;IAG1D3B,EAAA,CAAAE,SAAA,aAA8B;IAACF,EAAA,CAAAI,MAAA,sBACjC;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,WAAK;IAGDD,EAAA,CAAAK,UAAA,mBAAAwB,mEAAA;MAAA,MAAAP,WAAA,GAAAtB,EAAA,CAAAO,aAAA,CAAAgB,IAAA;MAAA,MAAAC,SAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAK,OAAA,GAAA9B,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAa,SAAA,CAAAG,GAAA,IAAcG,OAAA,CAAAC,oBAAA,CAAAP,SAAA,CAAAG,GAAA,CAAgC;IAAA,EAAC;IAGxD3B,EAAA,CAAAE,SAAA,aAA4B;IAC9BF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAIC;IAFCD,EAAA,CAAAK,UAAA,mBAAA2B,mEAAA;MAAA,MAAAV,WAAA,GAAAtB,EAAA,CAAAO,aAAA,CAAAgB,IAAA;MAAA,MAAAC,SAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAQ,OAAA,GAAAjC,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAa,SAAA,CAAAG,GAAA,IAAcM,OAAA,CAAAC,YAAA,CAAAV,SAAA,CAAAG,GAAA,CAAwB;IAAA,EAAC;IAGhD3B,EAAA,CAAAE,SAAA,aAA2B;IAC7BF,EAAA,CAAAG,YAAA,EAAS;IAIbH,EAAA,CAAAC,cAAA,kBAIC;IAFCD,EAAA,CAAAK,UAAA,mBAAA8B,mEAAA;MAAA,MAAAb,WAAA,GAAAtB,EAAA,CAAAO,aAAA,CAAAgB,IAAA;MAAA,MAAAC,SAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAW,OAAA,GAAApC,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAa,SAAA,CAAAG,GAAA,IAAcS,OAAA,CAAAC,eAAA,CAAAb,SAAA,CAAAG,GAAA,CAA2B;IAAA,EAAC;IAGnD3B,EAAA,CAAAE,SAAA,aAAqC;IAACF,EAAA,CAAAI,MAAA,eACxC;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;IA1DiCH,EAAA,CAAAa,SAAA,GAAiB;IAAjBb,EAAA,CAAAsC,iBAAA,CAAAd,SAAA,CAAAe,IAAA,CAAiB;IAMzDvC,EAAA,CAAAa,SAAA,GAKF;IALEb,EAAA,CAAAc,kBAAA,MAAAU,SAAA,CAAAgB,WAAA,IAAAhB,SAAA,CAAAgB,WAAA,CAAAC,MAAA,SAAAzC,EAAA,CAAA0C,WAAA,OAAAlB,SAAA,CAAAgB,WAAA,oBAAAhB,SAAA,CAAAgB,WAAA,8BAKF;IAQIxC,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,OAAAU,SAAA,CAAAmB,OAAA,kBAAAnB,SAAA,CAAAmB,OAAA,CAAAF,MAAA,sBACF;;;;;IA3BVzC,EAAA,CAAAC,cAAA,cAAgD;IAC9CD,EAAA,CAAA4C,UAAA,IAAAC,yCAAA,mBAmEM;IACR7C,EAAA,CAAAG,YAAA,EAAM;;;;IApE8CH,EAAA,CAAAa,SAAA,GAAU;IAAVb,EAAA,CAAA8C,UAAA,YAAAC,MAAA,CAAAC,OAAA,CAAU;;;AD9GlE,OAAM,MAAOC,mBAAmB;EAK9BC,YACUC,aAA4B,EAC5BC,MAAc,EACdC,mBAAwC;IAFxC,KAAAF,aAAa,GAAbA,aAAa;IACb,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,mBAAmB,GAAnBA,mBAAmB;IAP7B,KAAAL,OAAO,GAAa,EAAE;IACtB,KAAAM,OAAO,GAAG,KAAK;IACf,KAAAtC,KAAK,GAAkB,IAAI;EAMxB;EAEHuC,QAAQA,CAAA;IACN,IAAI,CAAC3C,WAAW,EAAE;EACpB;EAEAA,WAAWA,CAAA;IACT,IAAI,CAAC0C,OAAO,GAAG,IAAI;IACnB,IAAI,CAACtC,KAAK,GAAG,IAAI;IAEjB,IAAI,CAACmC,aAAa,CAACK,UAAU,EAAE,CAACC,IAAI,CAClC1D,QAAQ,CAAC,MAAM,IAAI,CAACuD,OAAO,GAAG,KAAK,CAAC,CACrC,CAACI,SAAS,CAAC;MACVC,IAAI,EAAGC,IAAI,IAAI;QACbC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEF,IAAI,CAAC;QACtC,IAAI,CAACZ,OAAO,GAAGY,IAAI;QAEnB;QACA,IAAI,CAACZ,OAAO,CAACe,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;UACzB,IAAID,CAAC,CAACzB,IAAI,IAAI0B,CAAC,CAAC1B,IAAI,EAAE;YACpB,OAAOyB,CAAC,CAACzB,IAAI,CAAC2B,aAAa,CAACD,CAAC,CAAC1B,IAAI,CAAC;;UAErC,OAAO,CAAC;QACV,CAAC,CAAC;MACJ,CAAC;MACDvB,KAAK,EAAGA,KAAK,IAAI;QACf6C,OAAO,CAAC7C,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;QAC9D,IAAI,CAACA,KAAK,GAAG,kEAAkE;QAC/E,IAAI,CAACqC,mBAAmB,CAACc,SAAS,CAAC,uCAAuC,CAAC;MAC7E;KACD,CAAC;EACJ;EAEA/C,mBAAmBA,CAAA;IACjB,IAAI,CAACgC,MAAM,CAACgB,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;EAC5C;EAEArC,oBAAoBA,CAACsC,EAAU;IAC7B,IAAI,CAACjB,MAAM,CAACgB,QAAQ,CAAC,CAAC,mBAAmB,EAAEC,EAAE,CAAC,CAAC;EACjD;EAEAzC,sBAAsBA,CAACyC,EAAU;IAC/B,IAAI,CAACjB,MAAM,CAACgB,QAAQ,CAAC,CAAC,iBAAiB,EAAEC,EAAE,CAAC,CAAC;EAC/C;EAEAnC,YAAYA,CAACmC,EAAU;IACrB,IAAI,CAACA,EAAE,EAAE;MACPR,OAAO,CAAC7C,KAAK,CAAC,iBAAiB,CAAC;MAChC,IAAI,CAACqC,mBAAmB,CAACc,SAAS,CAAC,uBAAuB,CAAC;MAC3D;;IAGF;IACA,MAAMG,MAAM,GAAG,IAAI,CAACtB,OAAO,CAACuB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC7C,GAAG,KAAK0C,EAAE,CAAC;IACnD,MAAMI,UAAU,GAAGH,MAAM,EAAE/B,IAAI,IAAI,cAAc;IAEjD,IAAImC,OAAO,CAAC,gDAAgDD,UAAU,KAAK,CAAC,EAAE;MAC5E,IAAI,CAACnB,OAAO,GAAG,IAAI;MAEnB,IAAI,CAACH,aAAa,CAACjB,YAAY,CAACmC,EAAE,CAAC,CAACZ,IAAI,CACtC1D,QAAQ,CAAC,MAAM,IAAI,CAACuD,OAAO,GAAG,KAAK,CAAC,CACrC,CAACI,SAAS,CAAC;QACVC,IAAI,EAAEA,CAAA,KAAK;UACTE,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;UAC3C,IAAI,CAACT,mBAAmB,CAACsB,WAAW,CAAC,aAAaF,UAAU,+BAA+B,CAAC;UAC5F,IAAI,CAAC7D,WAAW,EAAE;QACpB,CAAC;QACDI,KAAK,EAAGA,KAAK,IAAI;UACf6C,OAAO,CAAC7C,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;UACnE,IAAI,CAACA,KAAK,GAAG,kEAAkE;UAC/E,IAAI,CAACqC,mBAAmB,CAACc,SAAS,CAAC,8CAA8CM,UAAU,GAAG,CAAC;QACjG;OACD,CAAC;;EAEN;EAEApC,eAAeA,CAACgC,EAAU;IACxB,IAAI,CAACA,EAAE,EAAE;MACPR,OAAO,CAAC7C,KAAK,CAAC,iBAAiB,CAAC;MAChC,IAAI,CAACqC,mBAAmB,CAACc,SAAS,CAAC,uBAAuB,CAAC;MAC3D;;IAGF,MAAMG,MAAM,GAAG,IAAI,CAACtB,OAAO,CAACuB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC7C,GAAG,KAAK0C,EAAE,CAAC;IACnD,MAAMI,UAAU,GAAGH,MAAM,EAAE/B,IAAI,IAAI,cAAc;IAEjD;IACA,IAAI,CAACa,MAAM,CAACgB,QAAQ,CAAC,CAAC,eAAe,EAAEC,EAAE,CAAC,CAAC;EAAK;;;uBAhGvCpB,mBAAmB,EAAAjD,EAAA,CAAA4E,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAA9E,EAAA,CAAA4E,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAhF,EAAA,CAAA4E,iBAAA,CAAAK,EAAA,CAAAC,mBAAA;IAAA;EAAA;;;YAAnBjC,mBAAmB;MAAAkC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZhCzF,EAAA,CAAAC,cAAA,aAA6E;UAGzED,EAAA,CAAAE,SAAA,aAEO;UAMPF,EAAA,CAAAC,cAAA,aAA4D;UAExDD,EAAA,CAAAE,SAAA,aAAmE;UAWrEF,EAAA,CAAAG,YAAA,EAAM;UAIVH,EAAA,CAAAC,cAAA,cAA6C;UASrCD,EAAA,CAAAI,MAAA,sBACF;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,aAAwE;UACtED,EAAA,CAAAI,MAAA,sDACF;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAENH,EAAA,CAAAC,cAAA,kBAGC;UAFCD,EAAA,CAAAK,UAAA,mBAAAsF,sDAAA;YAAA,OAASD,GAAA,CAAAtE,mBAAA,EAAqB;UAAA,EAAC;UAG/BpB,EAAA,CAAAE,SAAA,eAEO;UAIPF,EAAA,CAAAC,cAAA,gBAEC;UACCD,EAAA,CAAAE,SAAA,aAEK;UACLF,EAAA,CAAAI,MAAA,8BACF;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UAKXH,EAAA,CAAA4C,UAAA,KAAAgD,mCAAA,kBAUM;UAGN5F,EAAA,CAAA4C,UAAA,KAAAiD,mCAAA,kBAiBM;UAGN7F,EAAA,CAAA4C,UAAA,KAAAkD,mCAAA,mBAoBM;UAGN9F,EAAA,CAAA4C,UAAA,KAAAmD,mCAAA,kBAqEM;UACR/F,EAAA,CAAAG,YAAA,EAAM;;;UA9HEH,EAAA,CAAAa,SAAA,IAAa;UAAbb,EAAA,CAAA8C,UAAA,SAAA4C,GAAA,CAAApC,OAAA,CAAa;UAabtD,EAAA,CAAAa,SAAA,GAAW;UAAXb,EAAA,CAAA8C,UAAA,SAAA4C,GAAA,CAAA1E,KAAA,CAAW;UAqBdhB,EAAA,CAAAa,SAAA,GAAgD;UAAhDb,EAAA,CAAA8C,UAAA,UAAA4C,GAAA,CAAApC,OAAA,KAAAoC,GAAA,CAAA1E,KAAA,IAAA0E,GAAA,CAAA1C,OAAA,CAAAP,MAAA,OAAgD;UAsB7BzC,EAAA,CAAAa,SAAA,GAAwB;UAAxBb,EAAA,CAAA8C,UAAA,SAAA4C,GAAA,CAAA1C,OAAA,CAAAP,MAAA,KAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}