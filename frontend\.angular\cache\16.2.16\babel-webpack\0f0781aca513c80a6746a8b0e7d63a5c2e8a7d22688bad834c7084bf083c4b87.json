{"ast": null, "code": "import { finalize } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/equipe.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/services/notification.service\";\nimport * as i4 from \"@angular/common\";\nfunction EquipeListComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 23);\n    i0.ɵɵelement(2, \"div\", 24)(3, \"div\", 25);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction EquipeListComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"div\", 27)(2, \"div\", 28);\n    i0.ɵɵelement(3, \"i\", 29)(4, \"div\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 31)(6, \"h3\", 32);\n    i0.ɵɵtext(7, \" Erreur \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 33);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function EquipeListComponent_div_32_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.loadEquipes());\n    });\n    i0.ɵɵelement(11, \"div\", 35);\n    i0.ɵɵelementStart(12, \"span\", 36);\n    i0.ɵɵelement(13, \"i\", 37);\n    i0.ɵɵtext(14, \" R\\u00E9essayer \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.error, \" \");\n  }\n}\nfunction EquipeListComponent_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"div\", 39);\n    i0.ɵɵelement(2, \"i\", 40)(3, \"div\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\", 41);\n    i0.ɵɵtext(5, \" Aucune \\u00E9quipe trouv\\u00E9e \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 42);\n    i0.ɵɵtext(7, \" Commencez par cr\\u00E9er une nouvelle \\u00E9quipe pour organiser vos projets et membres \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function EquipeListComponent_div_33_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.navigateToAddEquipe());\n    });\n    i0.ɵɵelement(9, \"div\", 44)(10, \"div\", 45);\n    i0.ɵɵelementStart(11, \"span\", 46);\n    i0.ɵɵelement(12, \"i\", 47);\n    i0.ɵɵtext(13, \" Cr\\u00E9er une \\u00E9quipe \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction EquipeListComponent_div_34_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"div\", 51);\n    i0.ɵɵelement(2, \"div\", 52)(3, \"div\", 53);\n    i0.ɵɵelementStart(4, \"div\", 54)(5, \"h3\", 55);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 56)(8, \"span\", 57);\n    i0.ɵɵelement(9, \"i\", 58);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(11, \"div\", 59)(12, \"p\", 60);\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"slice\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 61)(16, \"a\", 62);\n    i0.ɵɵlistener(\"click\", function EquipeListComponent_div_34_div_1_Template_a_click_16_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r11);\n      const equipe_r9 = restoredCtx.$implicit;\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(equipe_r9._id && ctx_r10.navigateToEquipeDetail(equipe_r9._id));\n    });\n    i0.ɵɵelementStart(17, \"div\", 63);\n    i0.ɵɵelement(18, \"i\", 64)(19, \"div\", 65);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"span\");\n    i0.ɵɵtext(21, \"D\\u00E9tails\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 66)(23, \"button\", 67);\n    i0.ɵɵlistener(\"click\", function EquipeListComponent_div_34_div_1_Template_button_click_23_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r11);\n      const equipe_r9 = restoredCtx.$implicit;\n      const ctx_r12 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(equipe_r9._id && ctx_r12.navigateToEditEquipe(equipe_r9._id));\n    });\n    i0.ɵɵelement(24, \"i\", 68)(25, \"div\", 69);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"button\", 70);\n    i0.ɵɵlistener(\"click\", function EquipeListComponent_div_34_div_1_Template_button_click_26_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r11);\n      const equipe_r9 = restoredCtx.$implicit;\n      const ctx_r13 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(equipe_r9._id && ctx_r13.deleteEquipe(equipe_r9._id));\n    });\n    i0.ɵɵelement(27, \"i\", 71)(28, \"div\", 72);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"button\", 73);\n    i0.ɵɵlistener(\"click\", function EquipeListComponent_div_34_div_1_Template_button_click_29_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r11);\n      const equipe_r9 = restoredCtx.$implicit;\n      const ctx_r14 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(equipe_r9._id && ctx_r14.navigateToTasks(equipe_r9._id));\n    });\n    i0.ɵɵelement(30, \"div\", 74)(31, \"div\", 75);\n    i0.ɵɵelementStart(32, \"span\", 36);\n    i0.ɵɵelement(33, \"i\", 76);\n    i0.ɵɵtext(34, \" Tasks \");\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const equipe_r9 = ctx.$implicit;\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", equipe_r9.name, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", (equipe_r9.members == null ? null : equipe_r9.members.length) || 0, \" membre(s) \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", equipe_r9.description && equipe_r9.description.length > 100 ? i0.ɵɵpipeBind3(14, 3, equipe_r9.description, 0, 100) + \"...\" : equipe_r9.description || \"Aucune description\", \" \");\n  }\n}\nfunction EquipeListComponent_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48);\n    i0.ɵɵtemplate(1, EquipeListComponent_div_34_div_1_Template, 35, 7, \"div\", 49);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.equipes);\n  }\n}\nexport class EquipeListComponent {\n  constructor(equipeService, router, notificationService) {\n    this.equipeService = equipeService;\n    this.router = router;\n    this.notificationService = notificationService;\n    this.equipes = [];\n    this.loading = false;\n    this.error = null;\n  }\n  ngOnInit() {\n    this.loadEquipes();\n  }\n  loadEquipes() {\n    this.loading = true;\n    this.error = null;\n    this.equipeService.getEquipes().pipe(finalize(() => this.loading = false)).subscribe({\n      next: data => {\n        console.log('Équipes chargées:', data);\n        this.equipes = data;\n        // Trier les équipes par nom\n        this.equipes.sort((a, b) => {\n          if (a.name && b.name) {\n            return a.name.localeCompare(b.name);\n          }\n          return 0;\n        });\n      },\n      error: error => {\n        console.error('Erreur lors du chargement des équipes:', error);\n        this.error = 'Impossible de charger les équipes. Veuillez réessayer plus tard.';\n        this.notificationService.showError('Erreur lors du chargement des équipes');\n      }\n    });\n  }\n  navigateToAddEquipe() {\n    this.router.navigate(['/equipes/ajouter']);\n  }\n  navigateToEditEquipe(id) {\n    this.router.navigate(['/equipes/modifier', id]);\n  }\n  navigateToEquipeDetail(id) {\n    this.router.navigate(['/equipes/detail', id]);\n  }\n  deleteEquipe(id) {\n    if (!id) {\n      console.error('ID est indéfini');\n      this.notificationService.showError('ID d\\'équipe invalide');\n      return;\n    }\n    // Trouver le nom de l'équipe pour l'afficher dans le message de confirmation\n    const equipe = this.equipes.find(e => e._id === id);\n    const equipeName = equipe?.name || 'cette équipe';\n    if (confirm(`Êtes-vous sûr de vouloir supprimer l'équipe \"${equipeName}\" ?`)) {\n      this.loading = true;\n      this.equipeService.deleteEquipe(id).pipe(finalize(() => this.loading = false)).subscribe({\n        next: () => {\n          console.log('Équipe supprimée avec succès');\n          this.notificationService.showSuccess(`L'équipe \"${equipeName}\" a été supprimée avec succès`);\n          this.loadEquipes();\n        },\n        error: error => {\n          console.error('Erreur lors de la suppression de l\\'équipe:', error);\n          this.error = 'Impossible de supprimer l\\'équipe. Veuillez réessayer plus tard.';\n          this.notificationService.showError(`Erreur lors de la suppression de l'équipe \"${equipeName}\"`);\n        }\n      });\n    }\n  }\n  navigateToTasks(id) {\n    if (!id) {\n      console.error('ID est indéfini');\n      this.notificationService.showError('ID d\\'équipe invalide');\n      return;\n    }\n    const equipe = this.equipes.find(e => e._id === id);\n    const equipeName = equipe?.name || 'cette équipe';\n    // Naviguer vers la page des tâches de l'équipe\n    this.router.navigate(['/tasks', id]);\n  }\n  static {\n    this.ɵfac = function EquipeListComponent_Factory(t) {\n      return new (t || EquipeListComponent)(i0.ɵɵdirectiveInject(i1.EquipeService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.NotificationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: EquipeListComponent,\n      selectors: [[\"app-equipe-list\"]],\n      decls: 35,\n      vars: 4,\n      consts: [[1, \"min-h-screen\", \"bg-[#f0f4f8]\", \"dark:bg-[#0a0a0a]\", \"relative\", \"overflow-hidden\"], [1, \"absolute\", \"inset-0\", \"overflow-hidden\", \"pointer-events-none\"], [1, \"absolute\", \"top-[15%]\", \"left-[10%]\", \"w-64\", \"h-64\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#00f7ff]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"bottom-[20%]\", \"right-[10%]\", \"w-80\", \"h-80\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#00f7ff]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"inset-0\", \"opacity-5\", \"dark:opacity-[0.03]\"], [1, \"h-full\", \"grid\", \"grid-cols-12\"], [1, \"border-r\", \"border-[#4f5fad]\", \"dark:border-[#00f7ff]\"], [1, \"max-w-7xl\", \"mx-auto\", \"p-6\", \"relative\", \"z-10\"], [1, \"mb-8\", \"relative\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"dark:from-[#00f7ff]\", \"dark:to-[#4f5fad]\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"dark:from-[#00f7ff]\", \"dark:to-[#4f5fad]\", \"blur-md\"], [1, \"bg-white\", \"dark:bg-[#1a1a1a]\", \"rounded-xl\", \"shadow-lg\", \"dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)]\", \"p-6\", \"backdrop-blur-sm\", \"border\", \"border-[#4f5fad]/20\", \"dark:border-[#00f7ff]/20\"], [1, \"flex\", \"flex-col\", \"lg:flex-row\", \"lg:items-center\", \"lg:justify-between\"], [1, \"mb-4\", \"lg:mb-0\"], [1, \"text-3xl\", \"font-bold\", \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\", \"mb-2\", \"tracking-wide\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"text-sm\"], [1, \"relative\", \"overflow-hidden\", \"group\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"dark:from-[#00f7ff]\", \"dark:to-[#4f5fad]\", \"text-white\", \"px-6\", \"py-3\", \"rounded-xl\", \"font-medium\", \"transition-all\", \"duration-300\", \"hover:scale-105\", \"shadow-lg\", \"hover:shadow-[0_0_25px_rgba(79,95,173,0.4)]\", \"dark:hover:shadow-[0_0_25px_rgba(0,247,255,0.4)]\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"mr-2\", \"group-hover:rotate-90\", \"transition-transform\", \"duration-300\"], [\"class\", \"flex justify-center my-12\", 4, \"ngIf\"], [\"class\", \"bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 border border-[#ff6b69] dark:border-[#ff6b69]/30 rounded-lg p-4 mx-auto max-w-3xl mb-6 backdrop-blur-sm\", 4, \"ngIf\"], [\"class\", \"bg-white dark:bg-[#1e1e1e] rounded-xl shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)] p-8 text-center backdrop-blur-sm border border-[#edf1f4]/50 dark:border-[#2a2a2a]\", 4, \"ngIf\"], [\"class\", \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\", 4, \"ngIf\"], [1, \"flex\", \"justify-center\", \"my-12\"], [1, \"relative\"], [1, \"w-14\", \"h-14\", \"border-4\", \"border-[#4f5fad]/20\", \"dark:border-[#6d78c9]/20\", \"border-t-[#4f5fad]\", \"dark:border-t-[#6d78c9]\", \"rounded-full\", \"animate-spin\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/20\", \"dark:bg-[#6d78c9]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"bg-[#ff6b69]/10\", \"dark:bg-[#ff6b69]/5\", \"border\", \"border-[#ff6b69]\", \"dark:border-[#ff6b69]/30\", \"rounded-lg\", \"p-4\", \"mx-auto\", \"max-w-3xl\", \"mb-6\", \"backdrop-blur-sm\"], [1, \"flex\", \"items-start\"], [1, \"text-[#ff6b69]\", \"dark:text-[#ff8785]\", \"mr-3\", \"text-xl\", \"relative\"], [1, \"fas\", \"fa-exclamation-triangle\"], [1, \"absolute\", \"inset-0\", \"bg-[#ff6b69]/20\", \"dark:bg-[#ff8785]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"flex-1\"], [1, \"font-medium\", \"text-[#ff6b69]\", \"dark:text-[#ff8785]\", \"mb-1\"], [1, \"text-sm\", \"text-[#ff6b69]/80\", \"dark:text-[#ff8785]/80\"], [1, \"ml-4\", \"relative\", \"overflow-hidden\", \"group\", 3, \"click\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#ff6b69]\", \"to-[#ff8785]\", \"dark:from-[#ff6b69]\", \"dark:to-[#ff8785]\", \"rounded-lg\", \"transition-transform\", \"duration-300\", \"group-hover:scale-105\"], [1, \"relative\", \"flex\", \"items-center\", \"text-white\", \"text-sm\", \"font-medium\", \"px-3\", \"py-1.5\", \"rounded-lg\", \"transition-all\", \"z-10\"], [1, \"fas\", \"fa-redo\", \"mr-1\", \"group-hover:scale-110\", \"transition-transform\"], [1, \"bg-white\", \"dark:bg-[#1e1e1e]\", \"rounded-xl\", \"shadow-md\", \"dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)]\", \"p-8\", \"text-center\", \"backdrop-blur-sm\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\"], [1, \"w-24\", \"h-24\", \"mx-auto\", \"mb-6\", \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/10\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\", \"relative\"], [1, \"fas\", \"fa-users\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"text-4xl\", \"relative\", \"z-10\"], [1, \"text-xl\", \"font-medium\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\", \"mb-2\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mt-1\", \"mb-6\"], [1, \"relative\", \"overflow-hidden\", \"group\", 3, \"click\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#3d4a85]\", \"dark:to-[#6d78c9]\", \"rounded-lg\", \"transition-transform\", \"duration-300\", \"group-hover:scale-105\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#3d4a85]\", \"dark:to-[#6d78c9]\", \"rounded-lg\", \"opacity-0\", \"group-hover:opacity-100\", \"blur-md\", \"transition-opacity\", \"duration-300\"], [1, \"relative\", \"flex\", \"items-center\", \"text-white\", \"font-medium\", \"px-6\", \"py-3\", \"rounded-lg\", \"transition-all\", \"z-10\"], [1, \"fas\", \"fa-plus\", \"mr-2\", \"group-hover:scale-110\", \"transition-transform\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"lg:grid-cols-3\", \"gap-6\"], [\"class\", \"bg-white dark:bg-[#1e1e1e] rounded-xl shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)] overflow-hidden hover:shadow-lg dark:hover:shadow-[0_8px_30px_rgba(0,0,0,0.3)] transition-all duration-300 hover:-translate-y-1 backdrop-blur-sm border border-[#edf1f4]/50 dark:border-[#2a2a2a] group\", 4, \"ngFor\", \"ngForOf\"], [1, \"bg-white\", \"dark:bg-[#1e1e1e]\", \"rounded-xl\", \"shadow-md\", \"dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)]\", \"overflow-hidden\", \"hover:shadow-lg\", \"dark:hover:shadow-[0_8px_30px_rgba(0,0,0,0.3)]\", \"transition-all\", \"duration-300\", \"hover:-translate-y-1\", \"backdrop-blur-sm\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"group\"], [1, \"relative\", \"overflow-hidden\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"opacity-0\", \"group-hover:opacity-100\", \"blur-md\", \"transition-opacity\", \"duration-300\"], [1, \"p-5\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"relative\"], [1, \"text-lg\", \"font-bold\", \"pr-10\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\", \"group-hover:scale-[1.01]\", \"transition-transform\", \"duration-300\", \"origin-left\"], [1, \"flex\", \"items-center\", \"mt-2\", \"text-xs\", \"space-x-2\"], [1, \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/10\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"px-2\", \"py-0.5\", \"rounded-full\", \"backdrop-blur-sm\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-users\", \"mr-1\"], [1, \"p-5\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mb-4\", \"line-clamp-3\"], [1, \"flex\", \"justify-between\", \"items-center\", \"pt-3\", \"border-t\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\"], [1, \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"hover:text-[#3d4a85]\", \"dark:hover:text-[#4f5fad]\", \"text-sm\", \"font-medium\", \"flex\", \"items-center\", \"transition-colors\", \"relative\", \"group/details\", \"cursor-pointer\", 3, \"click\"], [1, \"relative\", \"mr-1\"], [1, \"fas\", \"fa-eye\", \"relative\", \"z-10\", \"group-hover/details:scale-110\", \"transition-transform\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/20\", \"dark:bg-[#6d78c9]/20\", \"opacity-0\", \"group-hover/details:opacity-100\", \"transition-opacity\", \"blur-md\", \"rounded-full\"], [1, \"flex\", \"items-center\", \"space-x-2\"], [\"title\", \"Modifier l'\\u00E9quipe\", 1, \"p-2\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"hover:bg-[#edf1f4]/50\", \"dark:hover:bg-[#2a2a2a]/50\", \"rounded-lg\", \"transition-all\", \"relative\", \"group/edit\", 3, \"click\"], [1, \"fas\", \"fa-edit\", \"relative\", \"z-10\", \"group-hover/edit:scale-110\", \"transition-transform\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/20\", \"dark:bg-[#6d78c9]/20\", \"opacity-0\", \"group-hover/edit:opacity-100\", \"transition-opacity\", \"blur-md\", \"rounded-lg\"], [\"title\", \"Supprimer l'\\u00E9quipe\", 1, \"p-2\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:text-[#ff6b69]\", \"dark:hover:text-[#ff8785]\", \"hover:bg-[#ff6b69]/10\", \"dark:hover:bg-[#ff6b69]/10\", \"rounded-lg\", \"transition-all\", \"relative\", \"group/delete\", 3, \"click\"], [1, \"fas\", \"fa-trash\", \"relative\", \"z-10\", \"group-hover/delete:scale-110\", \"transition-transform\"], [1, \"absolute\", \"inset-0\", \"bg-[#ff6b69]/20\", \"dark:bg-[#ff8785]/20\", \"opacity-0\", \"group-hover/delete:opacity-100\", \"transition-opacity\", \"blur-md\", \"rounded-lg\"], [\"title\", \"G\\u00E9rer les t\\u00E2ches de l'\\u00E9quipe\", 1, \"relative\", \"overflow-hidden\", \"group/tasks\", 3, \"click\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#2a5a03]\", \"to-[#afcf75]\", \"dark:from-[#2a5a03]\", \"dark:to-[#afcf75]\", \"rounded-lg\", \"transition-transform\", \"duration-300\", \"group-hover/tasks:scale-105\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#2a5a03]\", \"to-[#afcf75]\", \"dark:from-[#2a5a03]\", \"dark:to-[#afcf75]\", \"rounded-lg\", \"opacity-0\", \"group-hover/tasks:opacity-100\", \"blur-md\", \"transition-opacity\", \"duration-300\"], [1, \"fas\", \"fa-tasks\", \"mr-1\", \"group-hover/tasks:scale-110\", \"transition-transform\"]],\n      template: function EquipeListComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵelement(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5);\n          i0.ɵɵelement(6, \"div\", 6)(7, \"div\", 6)(8, \"div\", 6)(9, \"div\", 6)(10, \"div\", 6)(11, \"div\", 6)(12, \"div\", 6)(13, \"div\", 6)(14, \"div\", 6)(15, \"div\", 6)(16, \"div\", 6);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(17, \"div\", 7)(18, \"div\", 8);\n          i0.ɵɵelement(19, \"div\", 9)(20, \"div\", 10);\n          i0.ɵɵelementStart(21, \"div\", 11)(22, \"div\", 12)(23, \"div\", 13)(24, \"h1\", 14);\n          i0.ɵɵtext(25, \" \\u00C9quipes \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"p\", 15);\n          i0.ɵɵtext(27, \" G\\u00E9rez vos \\u00E9quipes et leurs membres avec style futuriste \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(28, \"button\", 16);\n          i0.ɵɵlistener(\"click\", function EquipeListComponent_Template_button_click_28_listener() {\n            return ctx.navigateToAddEquipe();\n          });\n          i0.ɵɵelement(29, \"i\", 17);\n          i0.ɵɵtext(30, \" Nouvelle \\u00C9quipe \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(31, EquipeListComponent_div_31_Template, 4, 0, \"div\", 18);\n          i0.ɵɵtemplate(32, EquipeListComponent_div_32_Template, 15, 1, \"div\", 19);\n          i0.ɵɵtemplate(33, EquipeListComponent_div_33_Template, 14, 0, \"div\", 20);\n          i0.ɵɵtemplate(34, EquipeListComponent_div_34_Template, 2, 1, \"div\", 21);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(31);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && !ctx.error && ctx.equipes.length === 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.equipes.length > 0);\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i4.SlicePipe],\n      styles: [\".hover-shadow[_ngcontent-%COMP%]:hover {\\n    transform: translateY(-5px);\\n    box-shadow: 0 10px 20px rgba(0,0,0,0.1) !important;\\n  }\\n\\n  .transition[_ngcontent-%COMP%] {\\n    transition: all 0.3s ease;\\n  }\\n\\n  .card-header.bg-primary[_ngcontent-%COMP%] {\\n    background: linear-gradient(45deg, #007bff, #6610f2) !important;\\n  }\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImVxdWlwZS1saXN0LmNvbXBvbmVudC5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkVBQUU7SUFDRSwyQkFBMkI7SUFDM0Isa0RBQWtEO0VBQ3BEOztFQUVBO0lBQ0UseUJBQXlCO0VBQzNCOztFQUVBO0lBQ0UsK0RBQStEO0VBQ2pFIiwiZmlsZSI6ImVxdWlwZS1saXN0LmNvbXBvbmVudC5jc3MiLCJzb3VyY2VzQ29udGVudCI6WyIgIC5ob3Zlci1zaGFkb3c6aG92ZXIge1xyXG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC01cHgpO1xyXG4gICAgYm94LXNoYWRvdzogMCAxMHB4IDIwcHggcmdiYSgwLDAsMCwwLjEpICFpbXBvcnRhbnQ7XHJcbiAgfVxyXG5cclxuICAudHJhbnNpdGlvbiB7XHJcbiAgICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xyXG4gIH1cclxuXHJcbiAgLmNhcmQtaGVhZGVyLmJnLXByaW1hcnkge1xyXG4gICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDQ1ZGVnLCAjMDA3YmZmLCAjNjYxMGYyKSAhaW1wb3J0YW50O1xyXG4gIH0iXX0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvZnJvbnQvZXF1aXBlcy9lcXVpcGUtbGlzdC9lcXVpcGUtbGlzdC5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJFQUFFO0lBQ0UsMkJBQTJCO0lBQzNCLGtEQUFrRDtFQUNwRDs7RUFFQTtJQUNFLHlCQUF5QjtFQUMzQjs7RUFFQTtJQUNFLCtEQUErRDtFQUNqRTtBQUNGLDR1QkFBNHVCIiwic291cmNlc0NvbnRlbnQiOlsiICAuaG92ZXItc2hhZG93OmhvdmVyIHtcclxuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtNXB4KTtcclxuICAgIGJveC1zaGFkb3c6IDAgMTBweCAyMHB4IHJnYmEoMCwwLDAsMC4xKSAhaW1wb3J0YW50O1xyXG4gIH1cclxuXHJcbiAgLnRyYW5zaXRpb24ge1xyXG4gICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcclxuICB9XHJcblxyXG4gIC5jYXJkLWhlYWRlci5iZy1wcmltYXJ5IHtcclxuICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCg0NWRlZywgIzAwN2JmZiwgIzY2MTBmMikgIWltcG9ydGFudDtcclxuICB9Il0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["finalize", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "EquipeListComponent_div_32_Template_button_click_10_listener", "ɵɵrestoreView", "_r5", "ctx_r4", "ɵɵnextContext", "ɵɵresetView", "loadEquipes", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r1", "error", "EquipeListComponent_div_33_Template_button_click_8_listener", "_r7", "ctx_r6", "navigateToAddEquipe", "EquipeListComponent_div_34_div_1_Template_a_click_16_listener", "restoredCtx", "_r11", "equipe_r9", "$implicit", "ctx_r10", "_id", "navigateToEquipeDetail", "EquipeListComponent_div_34_div_1_Template_button_click_23_listener", "ctx_r12", "navigateToEditEquipe", "EquipeListComponent_div_34_div_1_Template_button_click_26_listener", "ctx_r13", "deleteEquipe", "EquipeListComponent_div_34_div_1_Template_button_click_29_listener", "ctx_r14", "navigateToTasks", "name", "members", "length", "description", "ɵɵpipeBind3", "ɵɵtemplate", "EquipeListComponent_div_34_div_1_Template", "ɵɵproperty", "ctx_r3", "equipes", "EquipeListComponent", "constructor", "equipeService", "router", "notificationService", "loading", "ngOnInit", "getEquipes", "pipe", "subscribe", "next", "data", "console", "log", "sort", "a", "b", "localeCompare", "showError", "navigate", "id", "equipe", "find", "e", "equipeName", "confirm", "showSuccess", "ɵɵdirectiveInject", "i1", "EquipeService", "i2", "Router", "i3", "NotificationService", "selectors", "decls", "vars", "consts", "template", "EquipeListComponent_Template", "rf", "ctx", "EquipeListComponent_Template_button_click_28_listener", "EquipeListComponent_div_31_Template", "EquipeListComponent_div_32_Template", "EquipeListComponent_div_33_Template", "EquipeListComponent_div_34_Template"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\equipes\\equipe-list\\equipe-list.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\equipes\\equipe-list\\equipe-list.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { EquipeService } from 'src/app/services/equipe.service';\nimport { NotificationService } from 'src/app/services/notification.service';\nimport { Equipe } from 'src/app/models/equipe.model';\nimport { finalize } from 'rxjs/operators';\n\n@Component({\n  selector: 'app-equipe-list',\n  templateUrl: './equipe-list.component.html',\n  styleUrls: ['./equipe-list.component.css']\n})\nexport class EquipeListComponent implements OnInit {\n  equipes: Equipe[] = [];\n  loading = false;\n  error: string | null = null;\n\n  constructor(\n    private equipeService: EquipeService,\n    private router: Router,\n    private notificationService: NotificationService\n  ) {}\n\n  ngOnInit(): void {\n    this.loadEquipes();\n  }\n\n  loadEquipes(): void {\n    this.loading = true;\n    this.error = null;\n\n    this.equipeService.getEquipes().pipe(\n      finalize(() => this.loading = false)\n    ).subscribe({\n      next: (data) => {\n        console.log('Équipes chargées:', data);\n        this.equipes = data;\n\n        // Trier les équipes par nom\n        this.equipes.sort((a, b) => {\n          if (a.name && b.name) {\n            return a.name.localeCompare(b.name);\n          }\n          return 0;\n        });\n      },\n      error: (error) => {\n        console.error('Erreur lors du chargement des équipes:', error);\n        this.error = 'Impossible de charger les équipes. Veuillez réessayer plus tard.';\n        this.notificationService.showError('Erreur lors du chargement des équipes');\n      }\n    });\n  }\n\n  navigateToAddEquipe(): void {\n    this.router.navigate(['/equipes/ajouter']);\n  }\n\n  navigateToEditEquipe(id: string): void {\n    this.router.navigate(['/equipes/modifier', id]);\n  }\n\n  navigateToEquipeDetail(id: string): void {\n    this.router.navigate(['/equipes/detail', id]);\n  }\n\n  deleteEquipe(id: string): void {\n    if (!id) {\n      console.error('ID est indéfini');\n      this.notificationService.showError('ID d\\'équipe invalide');\n      return;\n    }\n\n    // Trouver le nom de l'équipe pour l'afficher dans le message de confirmation\n    const equipe = this.equipes.find(e => e._id === id);\n    const equipeName = equipe?.name || 'cette équipe';\n\n    if (confirm(`Êtes-vous sûr de vouloir supprimer l'équipe \"${equipeName}\" ?`)) {\n      this.loading = true;\n\n      this.equipeService.deleteEquipe(id).pipe(\n        finalize(() => this.loading = false)\n      ).subscribe({\n        next: () => {\n          console.log('Équipe supprimée avec succès');\n          this.notificationService.showSuccess(`L'équipe \"${equipeName}\" a été supprimée avec succès`);\n          this.loadEquipes();\n        },\n        error: (error) => {\n          console.error('Erreur lors de la suppression de l\\'équipe:', error);\n          this.error = 'Impossible de supprimer l\\'équipe. Veuillez réessayer plus tard.';\n          this.notificationService.showError(`Erreur lors de la suppression de l'équipe \"${equipeName}\"`);\n        }\n      });\n    }\n  }\n\n  navigateToTasks(id: string): void {\n    if (!id) {\n      console.error('ID est indéfini');\n      this.notificationService.showError('ID d\\'équipe invalide');\n      return;\n    }\n\n    const equipe = this.equipes.find(e => e._id === id);\n    const equipeName = equipe?.name || 'cette équipe';\n\n    // Naviguer vers la page des tâches de l'équipe\n    this.router.navigate(['/tasks', id]);    }\n}\n\n", "<div\n  class=\"min-h-screen bg-[#f0f4f8] dark:bg-[#0a0a0a] relative overflow-hidden\"\n>\n  <!-- Background decorative elements -->\n  <div class=\"absolute inset-0 overflow-hidden pointer-events-none\">\n    <div\n      class=\"absolute top-[15%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-br from-[#4f5fad]/5 to-transparent dark:from-[#00f7ff]/3 dark:to-transparent blur-3xl\"\n    ></div>\n    <div\n      class=\"absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#4f5fad]/5 to-transparent dark:from-[#00f7ff]/3 dark:to-transparent blur-3xl\"\n    ></div>\n\n    <!-- Grid pattern -->\n    <div class=\"absolute inset-0 opacity-5 dark:opacity-[0.03]\">\n      <div class=\"h-full grid grid-cols-12\">\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n      </div>\n    </div>\n  </div>\n\n  <div class=\"max-w-7xl mx-auto p-6 relative z-10\">\n    <!-- Header futuriste -->\n    <div class=\"mb-8 relative\">\n      <!-- Decorative top border -->\n      <div\n        class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad]\"\n      ></div>\n      <div\n        class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad] blur-md\"\n      ></div>\n\n      <div\n        class=\"bg-white dark:bg-[#1a1a1a] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] p-6 backdrop-blur-sm border border-[#4f5fad]/20 dark:border-[#00f7ff]/20\"\n      >\n        <div\n          class=\"flex flex-col lg:flex-row lg:items-center lg:justify-between\"\n        >\n          <div class=\"mb-4 lg:mb-0\">\n            <h1\n              class=\"text-3xl font-bold text-[#4f5fad] dark:text-[#00f7ff] mb-2 tracking-wide\"\n            >\n              Équipes\n            </h1>\n            <p class=\"text-[#6d6870] dark:text-[#a0a0a0] text-sm\">\n              Gérez vos équipes et leurs membres avec style futuriste\n            </p>\n          </div>\n\n          <button\n            (click)=\"navigateToAddEquipe()\"\n            class=\"relative overflow-hidden group bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad] text-white px-6 py-3 rounded-xl font-medium transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-[0_0_25px_rgba(79,95,173,0.4)] dark:hover:shadow-[0_0_25px_rgba(0,247,255,0.4)]\"\n          >\n            <i\n              class=\"fas fa-plus mr-2 group-hover:rotate-90 transition-transform duration-300\"\n            ></i>\n            Nouvelle Équipe\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Loading Indicator -->\n    <div *ngIf=\"loading\" class=\"flex justify-center my-12\">\n      <div class=\"relative\">\n        <div\n          class=\"w-14 h-14 border-4 border-[#4f5fad]/20 dark:border-[#6d78c9]/20 border-t-[#4f5fad] dark:border-t-[#6d78c9] rounded-full animate-spin\"\n        ></div>\n        <!-- Glow effect -->\n        <div\n          class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 blur-xl rounded-full transform scale-150 -z-10\"\n        ></div>\n      </div>\n    </div>\n\n    <!-- Error Alert -->\n    <div\n      *ngIf=\"error\"\n      class=\"bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 border border-[#ff6b69] dark:border-[#ff6b69]/30 rounded-lg p-4 mx-auto max-w-3xl mb-6 backdrop-blur-sm\"\n    >\n      <div class=\"flex items-start\">\n        <div class=\"text-[#ff6b69] dark:text-[#ff8785] mr-3 text-xl relative\">\n          <i class=\"fas fa-exclamation-triangle\"></i>\n          <!-- Glow effect -->\n          <div\n            class=\"absolute inset-0 bg-[#ff6b69]/20 dark:bg-[#ff8785]/20 blur-xl rounded-full transform scale-150 -z-10\"\n          ></div>\n        </div>\n        <div class=\"flex-1\">\n          <h3 class=\"font-medium text-[#ff6b69] dark:text-[#ff8785] mb-1\">\n            Erreur\n          </h3>\n          <p class=\"text-sm text-[#ff6b69]/80 dark:text-[#ff8785]/80\">\n            {{ error }}\n          </p>\n        </div>\n        <button\n          (click)=\"loadEquipes()\"\n          class=\"ml-4 relative overflow-hidden group\"\n        >\n          <div\n            class=\"absolute inset-0 bg-gradient-to-r from-[#ff6b69] to-[#ff8785] dark:from-[#ff6b69] dark:to-[#ff8785] rounded-lg transition-transform duration-300 group-hover:scale-105\"\n          ></div>\n          <span\n            class=\"relative flex items-center text-white text-sm font-medium px-3 py-1.5 rounded-lg transition-all z-10\"\n          >\n            <i\n              class=\"fas fa-redo mr-1 group-hover:scale-110 transition-transform\"\n            ></i>\n            Réessayer\n          </span>\n        </button>\n      </div>\n    </div>\n\n    <!-- No Teams -->\n    <div\n      *ngIf=\"!loading && !error && equipes.length === 0\"\n      class=\"bg-white dark:bg-[#1e1e1e] rounded-xl shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)] p-8 text-center backdrop-blur-sm border border-[#edf1f4]/50 dark:border-[#2a2a2a]\"\n    >\n      <div\n        class=\"w-24 h-24 mx-auto mb-6 bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 rounded-full flex items-center justify-center relative\"\n      >\n        <i\n          class=\"fas fa-users text-[#4f5fad] dark:text-[#6d78c9] text-4xl relative z-10\"\n        ></i>\n        <!-- Glow effect -->\n        <div\n          class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 blur-xl rounded-full transform scale-150 -z-10\"\n        ></div>\n      </div>\n      <h3\n        class=\"text-xl font-medium bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent mb-2\"\n      >\n        Aucune équipe trouvée\n      </h3>\n      <p class=\"text-[#6d6870] dark:text-[#a0a0a0] mt-1 mb-6\">\n        Commencez par créer une nouvelle équipe pour organiser vos projets et\n        membres\n      </p>\n      <button\n        (click)=\"navigateToAddEquipe()\"\n        class=\"relative overflow-hidden group\"\n      >\n        <div\n          class=\"absolute inset-0 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#3d4a85] dark:to-[#6d78c9] rounded-lg transition-transform duration-300 group-hover:scale-105\"\n        ></div>\n        <div\n          class=\"absolute inset-0 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#3d4a85] dark:to-[#6d78c9] rounded-lg opacity-0 group-hover:opacity-100 blur-md transition-opacity duration-300\"\n        ></div>\n        <span\n          class=\"relative flex items-center text-white font-medium px-6 py-3 rounded-lg transition-all z-10\"\n        >\n          <i\n            class=\"fas fa-plus mr-2 group-hover:scale-110 transition-transform\"\n          ></i>\n          Créer une équipe\n        </span>\n      </button>\n    </div>\n\n    <!-- Teams Grid -->\n    <div\n      *ngIf=\"!loading && equipes.length > 0\"\n      class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\"\n    >\n      <div\n        *ngFor=\"let equipe of equipes\"\n        class=\"bg-white dark:bg-[#1e1e1e] rounded-xl shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)] overflow-hidden hover:shadow-lg dark:hover:shadow-[0_8px_30px_rgba(0,0,0,0.3)] transition-all duration-300 hover:-translate-y-1 backdrop-blur-sm border border-[#edf1f4]/50 dark:border-[#2a2a2a] group\"\n      >\n        <!-- Header -->\n        <div class=\"relative overflow-hidden\">\n          <!-- Decorative gradient top border -->\n          <div\n            class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad]\"\n          ></div>\n\n          <!-- Glow effect on hover -->\n          <div\n            class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] opacity-0 group-hover:opacity-100 blur-md transition-opacity duration-300\"\n          ></div>\n\n          <div class=\"p-5 bg-white dark:bg-[#1e1e1e] relative\">\n            <h3\n              class=\"text-lg font-bold pr-10 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent group-hover:scale-[1.01] transition-transform duration-300 origin-left\"\n            >\n              {{ equipe.name }}\n            </h3>\n            <div class=\"flex items-center mt-2 text-xs space-x-2\">\n              <span\n                class=\"bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 text-[#4f5fad] dark:text-[#6d78c9] px-2 py-0.5 rounded-full backdrop-blur-sm flex items-center\"\n              >\n                <i class=\"fas fa-users mr-1\"></i>\n                {{ equipe.members?.length || 0 }} membre(s)\n              </span>\n            </div>\n          </div>\n        </div>\n\n        <!-- Content -->\n        <div class=\"p-5\">\n          <p\n            class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0] mb-4 line-clamp-3\"\n          >\n            {{\n              equipe.description && equipe.description.length > 100\n                ? (equipe.description | slice : 0 : 100) + \"...\"\n                : equipe.description || \"Aucune description\"\n            }}\n          </p>\n\n          <!-- Actions -->\n          <div\n            class=\"flex justify-between items-center pt-3 border-t border-[#edf1f4]/50 dark:border-[#2a2a2a]\"\n          >\n            <a\n              (click)=\"equipe._id && navigateToEquipeDetail(equipe._id)\"\n              class=\"text-[#4f5fad] dark:text-[#6d78c9] hover:text-[#3d4a85] dark:hover:text-[#4f5fad] text-sm font-medium flex items-center transition-colors relative group/details cursor-pointer\"\n            >\n              <div class=\"relative mr-1\">\n                <i\n                  class=\"fas fa-eye relative z-10 group-hover/details:scale-110 transition-transform\"\n                ></i>\n                <!-- Glow effect -->\n                <div\n                  class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 opacity-0 group-hover/details:opacity-100 transition-opacity blur-md rounded-full\"\n                ></div>\n              </div>\n              <span>Détails</span>\n            </a>\n\n            <div class=\"flex items-center space-x-2\">\n              <button\n                (click)=\"equipe._id && navigateToEditEquipe(equipe._id)\"\n                class=\"p-2 text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] hover:bg-[#edf1f4]/50 dark:hover:bg-[#2a2a2a]/50 rounded-lg transition-all relative group/edit\"\n                title=\"Modifier l'équipe\"\n              >\n                <i\n                  class=\"fas fa-edit relative z-10 group-hover/edit:scale-110 transition-transform\"\n                ></i>\n                <!-- Glow effect -->\n                <div\n                  class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 opacity-0 group-hover/edit:opacity-100 transition-opacity blur-md rounded-lg\"\n                ></div>\n              </button>\n\n              <button\n                (click)=\"equipe._id && deleteEquipe(equipe._id)\"\n                class=\"p-2 text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#ff6b69] dark:hover:text-[#ff8785] hover:bg-[#ff6b69]/10 dark:hover:bg-[#ff6b69]/10 rounded-lg transition-all relative group/delete\"\n                title=\"Supprimer l'équipe\"\n              >\n                <i\n                  class=\"fas fa-trash relative z-10 group-hover/delete:scale-110 transition-transform\"\n                ></i>\n                <!-- Glow effect -->\n                <div\n                  class=\"absolute inset-0 bg-[#ff6b69]/20 dark:bg-[#ff8785]/20 opacity-0 group-hover/delete:opacity-100 transition-opacity blur-md rounded-lg\"\n                ></div>\n              </button>\n\n              <button\n                (click)=\"equipe._id && navigateToTasks(equipe._id)\"\n                class=\"relative overflow-hidden group/tasks\"\n                title=\"Gérer les tâches de l'équipe\"\n              >\n                <div\n                  class=\"absolute inset-0 bg-gradient-to-r from-[#2a5a03] to-[#afcf75] dark:from-[#2a5a03] dark:to-[#afcf75] rounded-lg transition-transform duration-300 group-hover/tasks:scale-105\"\n                ></div>\n                <div\n                  class=\"absolute inset-0 bg-gradient-to-r from-[#2a5a03] to-[#afcf75] dark:from-[#2a5a03] dark:to-[#afcf75] rounded-lg opacity-0 group-hover/tasks:opacity-100 blur-md transition-opacity duration-300\"\n                ></div>\n                <span\n                  class=\"relative flex items-center text-white text-sm font-medium px-3 py-1.5 rounded-lg transition-all z-10\"\n                >\n                  <i\n                    class=\"fas fa-tasks mr-1 group-hover/tasks:scale-110 transition-transform\"\n                  ></i>\n                  Tasks\n                </span>\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AAKA,SAASA,QAAQ,QAAQ,gBAAgB;;;;;;;;ICmErCC,EAAA,CAAAC,cAAA,cAAuD;IAEnDD,EAAA,CAAAE,SAAA,cAEO;IAKTF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAIRH,EAAA,CAAAC,cAAA,cAGC;IAGKD,EAAA,CAAAE,SAAA,YAA2C;IAK7CF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAoB;IAEhBD,EAAA,CAAAI,MAAA,eACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,YAA4D;IAC1DD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAENH,EAAA,CAAAC,cAAA,kBAGC;IAFCD,EAAA,CAAAK,UAAA,mBAAAC,6DAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IAGvBZ,EAAA,CAAAE,SAAA,eAEO;IACPF,EAAA,CAAAC,cAAA,gBAEC;IACCD,EAAA,CAAAE,SAAA,aAEK;IACLF,EAAA,CAAAI,MAAA,wBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;IAjBLH,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,MAAAC,MAAA,CAAAC,KAAA,MACF;;;;;;IAsBNhB,EAAA,CAAAC,cAAA,cAGC;IAIGD,EAAA,CAAAE,SAAA,YAEK;IAKPF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,aAEC;IACCD,EAAA,CAAAI,MAAA,wCACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,YAAwD;IACtDD,EAAA,CAAAI,MAAA,gGAEF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAC,cAAA,iBAGC;IAFCD,EAAA,CAAAK,UAAA,mBAAAY,4DAAA;MAAAjB,EAAA,CAAAO,aAAA,CAAAW,GAAA;MAAA,MAAAC,MAAA,GAAAnB,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAQ,MAAA,CAAAC,mBAAA,EAAqB;IAAA,EAAC;IAG/BpB,EAAA,CAAAE,SAAA,cAEO;IAIPF,EAAA,CAAAC,cAAA,gBAEC;IACCD,EAAA,CAAAE,SAAA,aAEK;IACLF,EAAA,CAAAI,MAAA,oCACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;;IASTH,EAAA,CAAAC,cAAA,cAGC;IAIGD,EAAA,CAAAE,SAAA,cAEO;IAOPF,EAAA,CAAAC,cAAA,cAAqD;IAIjDD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAAsD;IAIlDD,EAAA,CAAAE,SAAA,YAAiC;IACjCF,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAMbH,EAAA,CAAAC,cAAA,eAAiB;IAIbD,EAAA,CAAAI,MAAA,IAKF;;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAGJH,EAAA,CAAAC,cAAA,eAEC;IAEGD,EAAA,CAAAK,UAAA,mBAAAgB,8DAAA;MAAA,MAAAC,WAAA,GAAAtB,EAAA,CAAAO,aAAA,CAAAgB,IAAA;MAAA,MAAAC,SAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAC,OAAA,GAAA1B,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAa,SAAA,CAAAG,GAAA,IAAcD,OAAA,CAAAE,sBAAA,CAAAJ,SAAA,CAAAG,GAAA,CAAkC;IAAA,EAAC;IAG1D3B,EAAA,CAAAC,cAAA,eAA2B;IACzBD,EAAA,CAAAE,SAAA,aAEK;IAKPF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAI,MAAA,oBAAO;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAGtBH,EAAA,CAAAC,cAAA,eAAyC;IAErCD,EAAA,CAAAK,UAAA,mBAAAwB,mEAAA;MAAA,MAAAP,WAAA,GAAAtB,EAAA,CAAAO,aAAA,CAAAgB,IAAA;MAAA,MAAAC,SAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAK,OAAA,GAAA9B,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAa,SAAA,CAAAG,GAAA,IAAcG,OAAA,CAAAC,oBAAA,CAAAP,SAAA,CAAAG,GAAA,CAAgC;IAAA,EAAC;IAIxD3B,EAAA,CAAAE,SAAA,aAEK;IAKPF,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAAC,cAAA,kBAIC;IAHCD,EAAA,CAAAK,UAAA,mBAAA2B,mEAAA;MAAA,MAAAV,WAAA,GAAAtB,EAAA,CAAAO,aAAA,CAAAgB,IAAA;MAAA,MAAAC,SAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAQ,OAAA,GAAAjC,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAa,SAAA,CAAAG,GAAA,IAAcM,OAAA,CAAAC,YAAA,CAAAV,SAAA,CAAAG,GAAA,CAAwB;IAAA,EAAC;IAIhD3B,EAAA,CAAAE,SAAA,aAEK;IAKPF,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAAC,cAAA,kBAIC;IAHCD,EAAA,CAAAK,UAAA,mBAAA8B,mEAAA;MAAA,MAAAb,WAAA,GAAAtB,EAAA,CAAAO,aAAA,CAAAgB,IAAA;MAAA,MAAAC,SAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAW,OAAA,GAAApC,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAa,SAAA,CAAAG,GAAA,IAAcS,OAAA,CAAAC,eAAA,CAAAb,SAAA,CAAAG,GAAA,CAA2B;IAAA,EAAC;IAInD3B,EAAA,CAAAE,SAAA,eAEO;IAIPF,EAAA,CAAAC,cAAA,gBAEC;IACCD,EAAA,CAAAE,SAAA,aAEK;IACLF,EAAA,CAAAI,MAAA,eACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;IA5FTH,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,MAAAU,SAAA,CAAAc,IAAA,MACF;IAMItC,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,OAAAU,SAAA,CAAAe,OAAA,kBAAAf,SAAA,CAAAe,OAAA,CAAAC,MAAA,sBACF;IAUFxC,EAAA,CAAAa,SAAA,GAKF;IALEb,EAAA,CAAAc,kBAAA,MAAAU,SAAA,CAAAiB,WAAA,IAAAjB,SAAA,CAAAiB,WAAA,CAAAD,MAAA,SAAAxC,EAAA,CAAA0C,WAAA,QAAAlB,SAAA,CAAAiB,WAAA,oBAAAjB,SAAA,CAAAiB,WAAA,8BAKF;;;;;IA/CNzC,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAA2C,UAAA,IAAAC,yCAAA,mBAqHM;IACR5C,EAAA,CAAAG,YAAA,EAAM;;;;IArHiBH,EAAA,CAAAa,SAAA,GAAU;IAAVb,EAAA,CAAA6C,UAAA,YAAAC,MAAA,CAAAC,OAAA,CAAU;;;ADpKrC,OAAM,MAAOC,mBAAmB;EAK9BC,YACUC,aAA4B,EAC5BC,MAAc,EACdC,mBAAwC;IAFxC,KAAAF,aAAa,GAAbA,aAAa;IACb,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,mBAAmB,GAAnBA,mBAAmB;IAP7B,KAAAL,OAAO,GAAa,EAAE;IACtB,KAAAM,OAAO,GAAG,KAAK;IACf,KAAArC,KAAK,GAAkB,IAAI;EAMxB;EAEHsC,QAAQA,CAAA;IACN,IAAI,CAAC1C,WAAW,EAAE;EACpB;EAEAA,WAAWA,CAAA;IACT,IAAI,CAACyC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACrC,KAAK,GAAG,IAAI;IAEjB,IAAI,CAACkC,aAAa,CAACK,UAAU,EAAE,CAACC,IAAI,CAClCzD,QAAQ,CAAC,MAAM,IAAI,CAACsD,OAAO,GAAG,KAAK,CAAC,CACrC,CAACI,SAAS,CAAC;MACVC,IAAI,EAAGC,IAAI,IAAI;QACbC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEF,IAAI,CAAC;QACtC,IAAI,CAACZ,OAAO,GAAGY,IAAI;QAEnB;QACA,IAAI,CAACZ,OAAO,CAACe,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;UACzB,IAAID,CAAC,CAACzB,IAAI,IAAI0B,CAAC,CAAC1B,IAAI,EAAE;YACpB,OAAOyB,CAAC,CAACzB,IAAI,CAAC2B,aAAa,CAACD,CAAC,CAAC1B,IAAI,CAAC;;UAErC,OAAO,CAAC;QACV,CAAC,CAAC;MACJ,CAAC;MACDtB,KAAK,EAAGA,KAAK,IAAI;QACf4C,OAAO,CAAC5C,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;QAC9D,IAAI,CAACA,KAAK,GAAG,kEAAkE;QAC/E,IAAI,CAACoC,mBAAmB,CAACc,SAAS,CAAC,uCAAuC,CAAC;MAC7E;KACD,CAAC;EACJ;EAEA9C,mBAAmBA,CAAA;IACjB,IAAI,CAAC+B,MAAM,CAACgB,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;EAC5C;EAEApC,oBAAoBA,CAACqC,EAAU;IAC7B,IAAI,CAACjB,MAAM,CAACgB,QAAQ,CAAC,CAAC,mBAAmB,EAAEC,EAAE,CAAC,CAAC;EACjD;EAEAxC,sBAAsBA,CAACwC,EAAU;IAC/B,IAAI,CAACjB,MAAM,CAACgB,QAAQ,CAAC,CAAC,iBAAiB,EAAEC,EAAE,CAAC,CAAC;EAC/C;EAEAlC,YAAYA,CAACkC,EAAU;IACrB,IAAI,CAACA,EAAE,EAAE;MACPR,OAAO,CAAC5C,KAAK,CAAC,iBAAiB,CAAC;MAChC,IAAI,CAACoC,mBAAmB,CAACc,SAAS,CAAC,uBAAuB,CAAC;MAC3D;;IAGF;IACA,MAAMG,MAAM,GAAG,IAAI,CAACtB,OAAO,CAACuB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC5C,GAAG,KAAKyC,EAAE,CAAC;IACnD,MAAMI,UAAU,GAAGH,MAAM,EAAE/B,IAAI,IAAI,cAAc;IAEjD,IAAImC,OAAO,CAAC,gDAAgDD,UAAU,KAAK,CAAC,EAAE;MAC5E,IAAI,CAACnB,OAAO,GAAG,IAAI;MAEnB,IAAI,CAACH,aAAa,CAAChB,YAAY,CAACkC,EAAE,CAAC,CAACZ,IAAI,CACtCzD,QAAQ,CAAC,MAAM,IAAI,CAACsD,OAAO,GAAG,KAAK,CAAC,CACrC,CAACI,SAAS,CAAC;QACVC,IAAI,EAAEA,CAAA,KAAK;UACTE,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;UAC3C,IAAI,CAACT,mBAAmB,CAACsB,WAAW,CAAC,aAAaF,UAAU,+BAA+B,CAAC;UAC5F,IAAI,CAAC5D,WAAW,EAAE;QACpB,CAAC;QACDI,KAAK,EAAGA,KAAK,IAAI;UACf4C,OAAO,CAAC5C,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;UACnE,IAAI,CAACA,KAAK,GAAG,kEAAkE;UAC/E,IAAI,CAACoC,mBAAmB,CAACc,SAAS,CAAC,8CAA8CM,UAAU,GAAG,CAAC;QACjG;OACD,CAAC;;EAEN;EAEAnC,eAAeA,CAAC+B,EAAU;IACxB,IAAI,CAACA,EAAE,EAAE;MACPR,OAAO,CAAC5C,KAAK,CAAC,iBAAiB,CAAC;MAChC,IAAI,CAACoC,mBAAmB,CAACc,SAAS,CAAC,uBAAuB,CAAC;MAC3D;;IAGF,MAAMG,MAAM,GAAG,IAAI,CAACtB,OAAO,CAACuB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC5C,GAAG,KAAKyC,EAAE,CAAC;IACnD,MAAMI,UAAU,GAAGH,MAAM,EAAE/B,IAAI,IAAI,cAAc;IAEjD;IACA,IAAI,CAACa,MAAM,CAACgB,QAAQ,CAAC,CAAC,QAAQ,EAAEC,EAAE,CAAC,CAAC;EAAK;;;uBAhGhCpB,mBAAmB,EAAAhD,EAAA,CAAA2E,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAA7E,EAAA,CAAA2E,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAA/E,EAAA,CAAA2E,iBAAA,CAAAK,EAAA,CAAAC,mBAAA;IAAA;EAAA;;;YAAnBjC,mBAAmB;MAAAkC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZhCxF,EAAA,CAAAC,cAAA,aAEC;UAGGD,EAAA,CAAAE,SAAA,aAEO;UAMPF,EAAA,CAAAC,cAAA,aAA4D;UAExDD,EAAA,CAAAE,SAAA,aAAmE;UAWrEF,EAAA,CAAAG,YAAA,EAAM;UAIVH,EAAA,CAAAC,cAAA,cAAiD;UAI7CD,EAAA,CAAAE,SAAA,cAEO;UAKPF,EAAA,CAAAC,cAAA,eAEC;UAQOD,EAAA,CAAAI,MAAA,sBACF;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,aAAsD;UACpDD,EAAA,CAAAI,MAAA,2EACF;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAGNH,EAAA,CAAAC,cAAA,kBAGC;UAFCD,EAAA,CAAAK,UAAA,mBAAAqF,sDAAA;YAAA,OAASD,GAAA,CAAArE,mBAAA,EAAqB;UAAA,EAAC;UAG/BpB,EAAA,CAAAE,SAAA,aAEK;UACLF,EAAA,CAAAI,MAAA,8BACF;UAAAJ,EAAA,CAAAG,YAAA,EAAS;UAMfH,EAAA,CAAA2C,UAAA,KAAAgD,mCAAA,kBAUM;UAGN3F,EAAA,CAAA2C,UAAA,KAAAiD,mCAAA,mBAqCM;UAGN5F,EAAA,CAAA2C,UAAA,KAAAkD,mCAAA,mBA2CM;UAGN7F,EAAA,CAAA2C,UAAA,KAAAmD,mCAAA,kBA0HM;UACR9F,EAAA,CAAAG,YAAA,EAAM;;;UA9NEH,EAAA,CAAAa,SAAA,IAAa;UAAbb,EAAA,CAAA6C,UAAA,SAAA4C,GAAA,CAAApC,OAAA,CAAa;UAchBrD,EAAA,CAAAa,SAAA,GAAW;UAAXb,EAAA,CAAA6C,UAAA,SAAA4C,GAAA,CAAAzE,KAAA,CAAW;UAwCXhB,EAAA,CAAAa,SAAA,GAAgD;UAAhDb,EAAA,CAAA6C,UAAA,UAAA4C,GAAA,CAAApC,OAAA,KAAAoC,GAAA,CAAAzE,KAAA,IAAAyE,GAAA,CAAA1C,OAAA,CAAAP,MAAA,OAAgD;UA8ChDxC,EAAA,CAAAa,SAAA,GAAoC;UAApCb,EAAA,CAAA6C,UAAA,UAAA4C,GAAA,CAAApC,OAAA,IAAAoC,GAAA,CAAA1C,OAAA,CAAAP,MAAA,KAAoC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}