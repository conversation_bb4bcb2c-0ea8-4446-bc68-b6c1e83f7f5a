<div
  class="min-h-screen bg-[#f0f4f8] dark:bg-[#0a0a0a] relative overflow-hidden"
>
  <!-- Background decorative elements -->
  <div class="absolute inset-0 overflow-hidden pointer-events-none">
    <div
      class="absolute top-[15%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-br from-[#dac4ea]/5 to-transparent dark:from-[#00f7ff]/3 dark:to-transparent blur-3xl"
    ></div>
    <div
      class="absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#dac4ea]/5 to-transparent dark:from-[#00f7ff]/3 dark:to-transparent blur-3xl"
    ></div>

    <!-- Grid pattern -->
    <div class="absolute inset-0 opacity-5 dark:opacity-[0.03]">
      <div class="h-full grid grid-cols-12">
        <div class="border-r border-[#dac4ea] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#dac4ea] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#dac4ea] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#dac4ea] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#dac4ea] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#dac4ea] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#dac4ea] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#dac4ea] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#dac4ea] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#dac4ea] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#dac4ea] dark:border-[#00f7ff]"></div>
      </div>
    </div>
  </div>

  <div class="max-w-7xl mx-auto p-6 relative z-10">
    <!-- Error Alert -->
    <div *ngIf="error" class="mb-6">
      <div
        class="bg-[#ff6b69]/10 dark:bg-[#ff3b30]/10 border-l-4 border-[#ff6b69] dark:border-[#ff3b30] rounded-lg p-4 backdrop-blur-sm"
      >
        <div class="flex items-start justify-between">
          <div class="flex items-start">
            <div class="text-[#ff6b69] dark:text-[#ff3b30] mr-3 text-xl">
              <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="flex-1">
              <h3 class="font-semibold text-[#ff6b69] dark:text-[#ff3b30] mb-1">
                Erreur
              </h3>
              <p class="text-sm text-[#6d6870] dark:text-[#a0a0a0]">
                {{ error }}
              </p>
            </div>
          </div>
          <button
            (click)="error = ''"
            class="text-[#ff6b69] dark:text-[#ff3b30] hover:bg-[#ff6b69]/10 dark:hover:bg-[#ff3b30]/10 p-1 rounded transition-colors"
          >
            <i class="fas fa-times"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- Loading Indicator -->
    <div
      *ngIf="loading"
      class="flex flex-col items-center justify-center py-16"
    >
      <div class="relative">
        <div
          class="w-12 h-12 border-3 border-[#dac4ea]/20 dark:border-[#00f7ff]/20 border-t-[#dac4ea] dark:border-t-[#00f7ff] rounded-full animate-spin"
        ></div>
        <div
          class="absolute inset-0 bg-[#dac4ea]/20 dark:bg-[#00f7ff]/20 blur-xl rounded-full transform scale-150 -z-10"
        ></div>
      </div>
      <p
        class="mt-4 text-[#dac4ea] dark:text-[#00f7ff] text-sm font-medium tracking-wide"
      >
        Chargement...
      </p>
    </div>

    <!-- Liste des équipes -->
    <div *ngIf="!loading" class="mb-8">
      <!-- Header avec titre et bouton refresh -->
      <div class="mb-6 relative">
        <!-- Decorative top border -->
        <div
          class="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea]"
        ></div>
        <div
          class="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea] blur-md"
        ></div>

        <div
          class="bg-white dark:bg-[#1a1a1a] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] p-6 backdrop-blur-sm border border-[#dac4ea]/20 dark:border-[#00f7ff]/20"
        >
          <div
            class="flex flex-col lg:flex-row lg:items-center lg:justify-between"
          >
            <div class="mb-4 lg:mb-0">
              <h2
                class="text-2xl font-bold text-[#dac4ea] dark:text-[#00f7ff] mb-2 tracking-wide"
              >
                Liste des équipes
              </h2>
              <p class="text-[#6d6870] dark:text-[#a0a0a0] text-sm">
                Gérez toutes vos équipes depuis cette interface
              </p>
            </div>

            <button
              (click)="loadEquipes()"
              class="bg-[#dac4ea]/20 dark:bg-[#00f7ff]/20 text-[#dac4ea] dark:text-[#00f7ff] px-4 py-2 rounded-lg font-medium transition-all duration-300 hover:scale-105 hover:bg-[#dac4ea]/30 dark:hover:bg-[#00f7ff]/30"
            >
              <i class="fas fa-sync-alt mr-2"></i>
              Rafraîchir
            </button>
          </div>
        </div>
      </div>

      <!-- No teams message -->
      <div *ngIf="equipes.length === 0" class="text-center py-12">
        <div
          class="w-16 h-16 mx-auto mb-4 text-[#dac4ea] dark:text-[#00f7ff] opacity-70"
        >
          <i class="fas fa-users text-4xl"></i>
        </div>
        <h3
          class="text-lg font-semibold text-[#dac4ea] dark:text-[#00f7ff] mb-2"
        >
          Aucune équipe trouvée
        </h3>
        <p class="text-[#6d6870] dark:text-[#a0a0a0] text-sm">
          Créez votre première équipe ci-dessous
        </p>
      </div>

      <!-- Teams Table -->
      <div
        *ngIf="equipes.length > 0"
        class="bg-white dark:bg-[#1a1a1a] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] overflow-hidden border border-[#dac4ea]/20 dark:border-[#00f7ff]/20"
      >
        <div class="overflow-x-auto">
          <table class="w-full">
            <thead class="bg-[#dac4ea]/10 dark:bg-[#00f7ff]/10">
              <tr>
                <th
                  class="px-6 py-4 text-left text-sm font-semibold text-[#dac4ea] dark:text-[#00f7ff]"
                >
                  Nom
                </th>
                <th
                  class="px-6 py-4 text-left text-sm font-semibold text-[#dac4ea] dark:text-[#00f7ff]"
                >
                  Description
                </th>
                <th
                  class="px-6 py-4 text-left text-sm font-semibold text-[#dac4ea] dark:text-[#00f7ff]"
                >
                  Admin
                </th>
                <th
                  class="px-6 py-4 text-left text-sm font-semibold text-[#dac4ea] dark:text-[#00f7ff]"
                >
                  Membres
                </th>
                <th
                  class="px-6 py-4 text-center text-sm font-semibold text-[#dac4ea] dark:text-[#00f7ff]"
                >
                  Actions
                </th>
              </tr>
            </thead>
            <tbody
              class="divide-y divide-[#dac4ea]/10 dark:divide-[#00f7ff]/10"
            >
              <tr
                *ngFor="let equipe of equipes"
                class="hover:bg-[#dac4ea]/5 dark:hover:bg-[#00f7ff]/5 transition-colors"
              >
                <td class="px-6 py-4">
                  <div class="font-medium text-[#dac4ea] dark:text-[#00f7ff]">
                    {{ equipe.name }}
                  </div>
                </td>
                <td class="px-6 py-4">
                  <div
                    class="text-sm text-[#6d6870] dark:text-[#a0a0a0] max-w-xs truncate"
                  >
                    {{ equipe.description || "Aucune description" }}
                  </div>
                </td>
                <td class="px-6 py-4">
                  <div class="text-sm text-[#6d6870] dark:text-[#a0a0a0]">
                    {{ equipe.admin || "Non défini" }}
                  </div>
                </td>
                <td class="px-6 py-4">
                  <span
                    class="bg-[#dac4ea]/10 dark:bg-[#00f7ff]/10 text-[#dac4ea] dark:text-[#00f7ff] px-2 py-1 rounded-full text-xs font-medium"
                  >
                    {{ equipe.members?.length || 0 }} membre(s)
                  </span>
                </td>
                <td class="px-6 py-4">
                  <div class="flex items-center justify-center space-x-2">
                    <button
                      (click)="editEquipe(equipe)"
                      class="p-2 text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#dac4ea] dark:hover:text-[#00f7ff] hover:bg-[#dac4ea]/10 dark:hover:bg-[#00f7ff]/10 rounded-lg transition-all"
                      title="Modifier l'équipe"
                    >
                      <i class="fas fa-edit"></i>
                    </button>
                    <button
                      (click)="showMembreModal(equipe)"
                      class="p-2 text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#00ff9d] hover:bg-[#00ff9d]/10 rounded-lg transition-all"
                      title="Gérer les membres"
                    >
                      <i class="fas fa-users"></i>
                    </button>
                    <button
                      (click)="equipe._id && deleteEquipe(equipe._id)"
                      class="p-2 text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#ff6b69] dark:hover:text-[#ff3b30] hover:bg-[#ff6b69]/10 dark:hover:bg-[#ff3b30]/10 rounded-lg transition-all"
                      title="Supprimer l'équipe"
                    >
                      <i class="fas fa-trash"></i>
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Formulaire de création d'équipe -->
    <div class="mb-8 relative">
      <!-- Decorative top border -->
      <div
        class="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea]"
      ></div>
      <div
        class="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea] blur-md"
      ></div>

      <div
        class="bg-white dark:bg-[#1a1a1a] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] overflow-hidden border border-[#dac4ea]/20 dark:border-[#00f7ff]/20"
      >
        <!-- Header -->
        <div
          class="bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea] p-6"
        >
          <h3 class="text-xl font-bold text-white mb-1">
            {{ isEditing ? "Modifier une équipe" : "Créer une équipe" }}
          </h3>
          <p class="text-white/80 text-sm">
            {{
              isEditing
                ? "Modifiez les informations de l'équipe"
                : "Ajoutez une nouvelle équipe à votre organisation"
            }}
          </p>
        </div>

        <!-- Form -->
        <div class="p-6">
          <form class="space-y-6">
            <!-- Nom de l'équipe -->
            <div>
              <label
                class="block text-sm font-medium text-[#dac4ea] dark:text-[#00f7ff] mb-2"
              >
                Nom de l'équipe
                <span class="text-[#ff6b69] dark:text-[#ff3b30]">*</span>
              </label>
              <input
                #nameInput
                type="text"
                [value]="newEquipe.name"
                (input)="newEquipe.name = nameInput.value"
                class="w-full px-4 py-3 bg-[#f0f4f8] dark:bg-[#0a0a0a] border border-[#dac4ea]/20 dark:border-[#00f7ff]/20 rounded-lg text-[#6d6870] dark:text-[#a0a0a0] placeholder-[#6d6870]/50 dark:placeholder-[#a0a0a0]/50 focus:outline-none focus:ring-2 focus:ring-[#dac4ea] dark:focus:ring-[#00f7ff] focus:border-transparent transition-all"
                [class.border-[#ff6b69]]="
                  !newEquipe.name && (isEditing || newEquipe.name === '')
                "
                [class.dark:border-[#ff3b30]]="
                  !newEquipe.name && (isEditing || newEquipe.name === '')
                "
                placeholder="Entrez le nom de l'équipe"
                required
              />
              <div
                *ngIf="!newEquipe.name && (isEditing || newEquipe.name === '')"
                class="mt-1 text-sm text-[#ff6b69] dark:text-[#ff3b30]"
              >
                Le nom de l'équipe est requis
              </div>
            </div>

            <!-- Description -->
            <div>
              <label
                class="block text-sm font-medium text-[#dac4ea] dark:text-[#00f7ff] mb-2"
              >
                Description
              </label>
              <textarea
                #descInput
                [value]="newEquipe.description || ''"
                (input)="newEquipe.description = descInput.value"
                rows="4"
                class="w-full px-4 py-3 bg-[#f0f4f8] dark:bg-[#0a0a0a] border border-[#dac4ea]/20 dark:border-[#00f7ff]/20 rounded-lg text-[#6d6870] dark:text-[#a0a0a0] placeholder-[#6d6870]/50 dark:placeholder-[#a0a0a0]/50 focus:outline-none focus:ring-2 focus:ring-[#dac4ea] dark:focus:ring-[#00f7ff] focus:border-transparent transition-all resize-none"
                placeholder="Entrez une description pour cette équipe"
              ></textarea>
              <p class="mt-1 text-xs text-[#6d6870] dark:text-[#a0a0a0]">
                Une brève description de l'équipe et de son objectif
              </p>
            </div>

            <!-- Buttons -->
            <div class="flex items-center space-x-4 pt-4">
              <button
                type="button"
                [disabled]="!newEquipe.name || loading"
                (click)="isEditing ? updateSelectedEquipe() : addEquipe()"
                class="relative overflow-hidden group bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea] text-white px-6 py-3 rounded-xl font-medium transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-[0_0_25px_rgba(218,196,234,0.4)] dark:hover:shadow-[0_0_25px_rgba(0,247,255,0.4)] disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100"
              >
                <span
                  *ngIf="loading"
                  class="inline-block w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2"
                ></span>
                <i *ngIf="!loading" class="fas fa-save mr-2"></i>
                {{ isEditing ? "Mettre à jour" : "Créer" }}
              </button>

              <button
                *ngIf="isEditing"
                type="button"
                (click)="cancelEdit()"
                class="bg-[#6d6870]/20 dark:bg-[#a0a0a0]/20 text-[#6d6870] dark:text-[#a0a0a0] px-6 py-3 rounded-xl font-medium transition-all duration-300 hover:scale-105 hover:bg-[#6d6870]/30 dark:hover:bg-[#a0a0a0]/30"
              >
                <i class="fas fa-times mr-2"></i>
                Annuler
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- Modal pour gérer les membres -->
    <div
      *ngIf="selectedEquipe"
      class="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
      (click)="selectedEquipe = null"
    >
      <div
        class="bg-white dark:bg-[#1a1a1a] rounded-xl shadow-2xl dark:shadow-[0_20px_50px_rgba(0,0,0,0.5)] max-w-2xl w-full max-h-[90vh] overflow-hidden border border-[#dac4ea]/20 dark:border-[#00f7ff]/20"
        (click)="$event.stopPropagation()"
      >
        <!-- Header -->
        <div
          class="bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea] p-6"
        >
          <div class="flex items-center justify-between">
            <div>
              <h3 class="text-xl font-bold text-white mb-1">
                Gérer les membres
              </h3>
              <p class="text-white/80 text-sm">
                Équipe: {{ selectedEquipe.name }}
              </p>
            </div>
            <button
              (click)="selectedEquipe = null"
              class="text-white/80 hover:text-white hover:bg-white/10 p-2 rounded-lg transition-all"
            >
              <i class="fas fa-times text-lg"></i>
            </button>
          </div>
        </div>

        <!-- Content -->
        <div class="p-6 max-h-[calc(90vh-140px)] overflow-y-auto">
          <!-- Membres actuels -->
          <div class="mb-6">
            <h4
              class="text-lg font-semibold text-[#dac4ea] dark:text-[#00f7ff] mb-4 flex items-center"
            >
              <i class="fas fa-users mr-2"></i>
              Membres actuels
            </h4>

            <div
              *ngIf="
                selectedEquipe.members && selectedEquipe.members.length > 0;
                else noMembers
              "
            >
              <div class="space-y-2">
                <div
                  *ngFor="let membreId of selectedEquipe.members"
                  class="flex items-center justify-between p-3 bg-[#f0f4f8] dark:bg-[#0a0a0a] rounded-lg border border-[#dac4ea]/10 dark:border-[#00f7ff]/10"
                >
                  <div class="flex items-center">
                    <div
                      class="w-8 h-8 bg-gradient-to-br from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea] rounded-full flex items-center justify-center mr-3"
                    >
                      <i class="fas fa-user text-white text-sm"></i>
                    </div>
                    <span
                      class="text-[#6d6870] dark:text-[#a0a0a0] font-medium"
                      >{{ membreId }}</span
                    >
                  </div>
                  <button
                    (click)="
                      removeMembreFromEquipe(selectedEquipe._id, membreId)
                    "
                    class="text-[#ff6b69] dark:text-[#ff3b30] hover:bg-[#ff6b69]/10 dark:hover:bg-[#ff3b30]/10 p-2 rounded-lg transition-all"
                    title="Retirer ce membre"
                  >
                    <i class="fas fa-user-minus"></i>
                  </button>
                </div>
              </div>
            </div>

            <ng-template #noMembers>
              <div class="text-center py-8">
                <div
                  class="w-16 h-16 mx-auto mb-4 text-[#dac4ea] dark:text-[#00f7ff] opacity-50"
                >
                  <i class="fas fa-user-friends text-4xl"></i>
                </div>
                <p class="text-[#6d6870] dark:text-[#a0a0a0] text-sm">
                  Aucun membre dans cette équipe
                </p>
              </div>
            </ng-template>
          </div>

          <!-- Ajouter un membre -->
          <div class="mb-6">
            <h4
              class="text-lg font-semibold text-[#dac4ea] dark:text-[#00f7ff] mb-4 flex items-center"
            >
              <i class="fas fa-user-plus mr-2"></i>
              Ajouter un membre
            </h4>

            <div class="flex space-x-3">
              <input
                #membreIdInput
                type="text"
                class="flex-1 px-4 py-3 bg-[#f0f4f8] dark:bg-[#0a0a0a] border border-[#dac4ea]/20 dark:border-[#00f7ff]/20 rounded-lg text-[#6d6870] dark:text-[#a0a0a0] placeholder-[#6d6870]/50 dark:placeholder-[#a0a0a0]/50 focus:outline-none focus:ring-2 focus:ring-[#dac4ea] dark:focus:ring-[#00f7ff] focus:border-transparent transition-all"
                placeholder="ID du membre"
              />
              <button
                [disabled]="
                  !selectedEquipe || !selectedEquipe._id || !membreIdInput.value
                "
                (click)="
                  addMembreToEquipe(selectedEquipe._id, membreIdInput.value);
                  membreIdInput.value = ''
                "
                class="bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea] text-white px-6 py-3 rounded-lg font-medium transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-[0_0_25px_rgba(218,196,234,0.4)] dark:hover:shadow-[0_0_25px_rgba(0,247,255,0.4)] disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100"
              >
                <i class="fas fa-plus mr-2"></i>
                Ajouter
              </button>
            </div>

            <p class="mt-2 text-xs text-[#6d6870] dark:text-[#a0a0a0]">
              Entrez l'ID du membre à ajouter à l'équipe
            </p>
          </div>

          <!-- Note informative -->
          <div
            class="bg-[#dac4ea]/10 dark:bg-[#00f7ff]/10 border-l-4 border-[#dac4ea] dark:border-[#00f7ff] rounded-lg p-4"
          >
            <div class="flex items-start">
              <div class="text-[#dac4ea] dark:text-[#00f7ff] mr-3 text-lg">
                <i class="fas fa-info-circle"></i>
              </div>
              <div>
                <h5
                  class="font-semibold text-[#dac4ea] dark:text-[#00f7ff] mb-1"
                >
                  Information importante
                </h5>
                <p class="text-sm text-[#6d6870] dark:text-[#a0a0a0]">
                  Pour ajouter un membre, vous devez d'abord créer le membre
                  dans la section des membres.
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- Footer -->
        <div class="p-6 border-t border-[#dac4ea]/10 dark:border-[#00f7ff]/10">
          <div class="flex justify-end">
            <button
              (click)="selectedEquipe = null"
              class="bg-[#6d6870]/20 dark:bg-[#a0a0a0]/20 text-[#6d6870] dark:text-[#a0a0a0] px-6 py-3 rounded-xl font-medium transition-all duration-300 hover:scale-105 hover:bg-[#6d6870]/30 dark:hover:bg-[#a0a0a0]/30"
            >
              <i class="fas fa-times mr-2"></i>
              Fermer
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
