<div
  class="min-h-screen bg-[#f0f4f8] dark:bg-[#0a0a0a] relative overflow-hidden"
  *ngIf="equipe"
>
  <!-- Background decorative elements -->
  <div class="absolute inset-0 overflow-hidden pointer-events-none">
    <div
      class="absolute top-[15%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-br from-[#dac4ea]/5 to-transparent dark:from-[#00f7ff]/3 dark:to-transparent blur-3xl"
    ></div>
    <div
      class="absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#dac4ea]/5 to-transparent dark:from-[#00f7ff]/3 dark:to-transparent blur-3xl"
    ></div>

    <!-- Grid pattern -->
    <div class="absolute inset-0 opacity-5 dark:opacity-[0.03]">
      <div class="h-full grid grid-cols-12">
        <div class="border-r border-[#dac4ea] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#dac4ea] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#dac4ea] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#dac4ea] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#dac4ea] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#dac4ea] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#dac4ea] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#dac4ea] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#dac4ea] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#dac4ea] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#dac4ea] dark:border-[#00f7ff]"></div>
      </div>
    </div>
  </div>

  <div class="max-w-7xl mx-auto p-6 relative z-10">
    <!-- Header Hero Section -->
    <div class="mb-8 relative">
      <!-- Decorative top border -->
      <div
        class="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea]"
      ></div>
      <div
        class="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea] blur-md"
      ></div>

      <div
        class="bg-white dark:bg-[#1a1a1a] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] overflow-hidden border border-[#dac4ea]/20 dark:border-[#00f7ff]/20"
      >
        <div class="grid grid-cols-1 lg:grid-cols-3">
          <!-- Team Info -->
          <div
            class="lg:col-span-2 bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea] p-8 text-white"
          >
            <h1 class="text-4xl font-bold mb-2">{{ equipe.name }}</h1>
            <p class="text-white/80 text-lg mb-6">
              Gestion et collaboration d'équipe
            </p>

            <!-- Stats -->
            <div class="grid grid-cols-3 gap-4">
              <div
                class="bg-white/20 backdrop-blur-sm rounded-lg p-4 text-center"
              >
                <i class="fas fa-users text-2xl mb-2 block"></i>
                <div class="text-xl font-bold">
                  {{ equipe.members?.length || 0 }}
                </div>
                <div class="text-sm text-white/80">Membres</div>
              </div>
              <div
                class="bg-white/20 backdrop-blur-sm rounded-lg p-4 text-center"
              >
                <i class="fas fa-tasks text-2xl mb-2 block"></i>
                <div class="text-xl font-bold">0</div>
                <div class="text-sm text-white/80">Tâches</div>
              </div>
              <div
                class="bg-white/20 backdrop-blur-sm rounded-lg p-4 text-center"
              >
                <i class="fas fa-calendar-check text-2xl mb-2 block"></i>
                <div class="text-xl font-bold">
                  {{ formatDate(equipe.createdAt) }}
                </div>
                <div class="text-sm text-white/80">Créée le</div>
              </div>
            </div>
          </div>

          <!-- Quick Actions -->
          <div class="p-8 bg-white dark:bg-[#1a1a1a]">
            <h3
              class="text-lg font-bold text-[#dac4ea] dark:text-[#00f7ff] mb-6 flex items-center"
            >
              <i class="fas fa-bolt mr-2"></i>
              Actions rapides
            </h3>
            <div class="space-y-3">
              <button
                (click)="navigateToTasks()"
                class="w-full bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea] text-white px-4 py-3 rounded-lg font-medium transition-all duration-300 hover:scale-105 shadow-lg"
              >
                <i class="fas fa-tasks mr-2"></i>
                Gérer les tâches
              </button>
              <button
                (click)="navigateToEditEquipe()"
                class="w-full bg-[#dac4ea]/20 dark:bg-[#00f7ff]/20 text-[#dac4ea] dark:text-[#00f7ff] px-4 py-3 rounded-lg font-medium transition-all duration-300 hover:scale-105"
              >
                <i class="fas fa-edit mr-2"></i>
                Modifier l'équipe
              </button>
              <div class="flex space-x-2">
                <button
                  (click)="navigateToEquipeList()"
                  class="flex-1 bg-[#6d6870]/20 dark:bg-[#a0a0a0]/20 text-[#6d6870] dark:text-[#a0a0a0] px-4 py-3 rounded-lg font-medium transition-all duration-300 hover:scale-105"
                >
                  <i class="fas fa-arrow-left mr-2"></i>
                  Retour
                </button>
                <button
                  (click)="deleteEquipe()"
                  class="bg-[#ff6b69]/20 dark:bg-[#ff3b30]/20 text-[#ff6b69] dark:text-[#ff3b30] px-4 py-3 rounded-lg font-medium transition-all duration-300 hover:scale-105"
                >
                  <i class="fas fa-trash"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Team Information -->
    <div class="mb-8 relative">
      <!-- Decorative top border -->
      <div
        class="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea]"
      ></div>
      <div
        class="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea] blur-md"
      ></div>

      <div
        class="bg-white dark:bg-[#1a1a1a] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] overflow-hidden border border-[#dac4ea]/20 dark:border-[#00f7ff]/20"
      >
        <div class="grid grid-cols-1 md:grid-cols-4">
          <!-- Icon Section -->
          <div
            class="bg-gradient-to-br from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea] p-6 flex flex-col items-center justify-center text-white"
          >
            <div
              class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mb-4"
            >
              <i class="fas fa-info-circle text-2xl"></i>
            </div>
            <h3 class="text-lg font-bold mb-1">À propos</h3>
            <p class="text-white/80 text-sm text-center">
              Détails et informations
            </p>
          </div>

          <!-- Content Section -->
          <div class="md:col-span-3 p-6">
            <div class="flex justify-between items-center mb-4">
              <h4 class="text-lg font-bold text-[#dac4ea] dark:text-[#00f7ff]">
                Description
              </h4>
              <span
                class="bg-[#dac4ea]/10 dark:bg-[#00f7ff]/10 text-[#dac4ea] dark:text-[#00f7ff] px-3 py-1 rounded-full text-sm font-medium"
              >
                <i class="fas fa-user-shield mr-1"></i>
                Admin:
                {{
                  equipe.admin
                    ? getUserName(equipe.admin) || equipe.admin
                    : "Non défini"
                }}
              </span>
            </div>

            <div
              class="bg-[#f0f4f8] dark:bg-[#0a0a0a] border-l-4 border-[#dac4ea] dark:border-[#00f7ff] rounded-lg p-4 mb-4"
            >
              <p class="text-[#6d6870] dark:text-[#a0a0a0] leading-relaxed">
                {{
                  equipe.description ||
                    "Aucune description disponible pour cette équipe."
                }}
              </p>
            </div>

            <!-- Tags -->
            <div class="flex flex-wrap gap-2">
              <span
                class="bg-[#dac4ea]/10 dark:bg-[#00f7ff]/10 text-[#dac4ea] dark:text-[#00f7ff] px-3 py-1 rounded-full text-sm font-medium"
              >
                <i class="fas fa-users mr-1"></i>
                {{ equipe.members?.length || 0 }} membres
              </span>
              <span
                class="bg-[#00ff9d]/10 text-[#00ff9d] px-3 py-1 rounded-full text-sm font-medium"
              >
                <i class="fas fa-calendar-check mr-1"></i>
                Créée le {{ formatDate(equipe.createdAt) }}
              </span>
              <span
                class="bg-[#4f5fad]/10 text-[#4f5fad] px-3 py-1 rounded-full text-sm font-medium"
              >
                <i class="fas fa-project-diagram mr-1"></i>
                Gestion de projet
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- AI Assistant -->
    <div class="mb-8 relative">
      <!-- Decorative top border -->
      <div
        class="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#8b5a9f] to-[#4a00e0] dark:from-[#00f7ff] dark:to-[#8b5a9f]"
      ></div>
      <div
        class="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#8b5a9f] to-[#4a00e0] dark:from-[#00f7ff] dark:to-[#8b5a9f] blur-md"
      ></div>

      <div
        class="bg-white dark:bg-[#1a1a1a] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] overflow-hidden border border-[#8b5a9f]/20 dark:border-[#00f7ff]/20"
      >
        <!-- Header -->
        <div
          class="bg-gradient-to-r from-[#8b5a9f] to-[#4a00e0] dark:from-[#00f7ff] dark:to-[#8b5a9f] p-6"
        >
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <div
                class="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mr-4"
              >
                <i class="fas fa-robot text-white text-xl"></i>
              </div>
              <div>
                <h3 class="text-xl font-bold text-white">
                  Assistant IA Gemini
                </h3>
                <p class="text-white/80 text-sm">
                  Génération de tâches intelligente
                </p>
              </div>
            </div>
            <span
              class="bg-white/20 text-white px-3 py-1 rounded-full text-sm font-medium"
            >
              <i class="fas fa-magic mr-1"></i>
              IA
            </span>
          </div>
        </div>

        <!-- AI Chat Component -->
        <div class="p-0">
          <app-ai-chat [team]="equipe"></app-ai-chat>
        </div>
      </div>
    </div>

    <!-- Team Members Section -->
    <div class="mb-8 relative">
      <!-- Decorative top border -->
      <div
        class="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#00ff9d] to-[#38ef7d] dark:from-[#00f7ff] dark:to-[#00ff9d]"
      ></div>
      <div
        class="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#00ff9d] to-[#38ef7d] dark:from-[#00f7ff] dark:to-[#00ff9d] blur-md"
      ></div>

      <div
        class="bg-white dark:bg-[#1a1a1a] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] overflow-hidden border border-[#00ff9d]/20 dark:border-[#00f7ff]/20"
      >
        <!-- Header -->
        <div
          class="bg-gradient-to-r from-[#00ff9d] to-[#38ef7d] dark:from-[#00f7ff] dark:to-[#00ff9d] p-6"
        >
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <div
                class="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mr-4"
              >
                <i class="fas fa-users text-white text-xl"></i>
              </div>
              <div>
                <h3 class="text-xl font-bold text-white">
                  Membres de l'équipe
                </h3>
                <p class="text-white/80 text-sm">
                  Gérez les membres et leurs rôles
                </p>
              </div>
            </div>
            <span
              class="bg-white/20 text-white px-3 py-1 rounded-full text-sm font-medium"
            >
              {{ teamMembers.length || 0 }} membres
            </span>
          </div>
        </div>

        <!-- Members Content -->
        <div class="grid grid-cols-1 lg:grid-cols-3">
          <!-- Members List -->
          <div class="lg:col-span-2 p-6">
            <div
              *ngIf="teamMembers && teamMembers.length > 0; else noMembers"
              class="space-y-4 max-h-96 overflow-y-auto"
            >
              <div
                *ngFor="let membre of teamMembers"
                class="bg-[#f0f4f8] dark:bg-[#0a0a0a] rounded-lg p-4 border border-[#00ff9d]/10 dark:border-[#00f7ff]/10 hover:shadow-lg transition-all duration-300"
              >
                <div class="flex items-center justify-between">
                  <!-- Member Info -->
                  <div class="flex items-center">
                    <div
                      class="w-12 h-12 rounded-full flex items-center justify-center text-white mr-4"
                      [ngClass]="{
                        'bg-gradient-to-br from-[#4f5fad] to-[#7826b5]':
                          getUserProfession(membre.user) === 'etudiant',
                        'bg-gradient-to-br from-[#00ff9d] to-[#38ef7d]':
                          getUserProfession(membre.user) === 'professeur',
                        'bg-gradient-to-br from-[#6d6870] to-[#a0a0a0]':
                          !getUserProfession(membre.user)
                      }"
                    >
                      <i
                        class="fas"
                        [ngClass]="{
                          'fa-graduation-cap':
                            getUserProfession(membre.user) === 'etudiant',
                          'fa-chalkboard-teacher':
                            getUserProfession(membre.user) === 'professeur',
                          'fa-user': !getUserProfession(membre.user)
                        }"
                      ></i>
                    </div>
                    <div>
                      <h6
                        class="font-bold text-[#6d6870] dark:text-[#a0a0a0] mb-1"
                      >
                        {{ getUserName(membre.user) }}
                      </h6>
                      <div class="flex items-center space-x-2">
                        <span
                          class="px-2 py-1 rounded-full text-xs font-medium"
                          [ngClass]="{
                            'bg-[#00ff9d]/10 text-[#00ff9d]':
                              membre.role === 'admin',
                            'bg-[#4f5fad]/10 text-[#4f5fad]':
                              membre.role === 'membre'
                          }"
                        >
                          <i
                            class="fas mr-1"
                            [ngClass]="{
                              'fa-user-shield': membre.role === 'admin',
                              'fa-user': membre.role === 'membre'
                            }"
                          ></i>
                          {{
                            membre.role === "admin"
                              ? "Administrateur"
                              : "Membre"
                          }}
                        </span>
                        <span
                          class="text-xs text-[#6d6870] dark:text-[#a0a0a0]"
                        >
                          {{
                            getUserProfession(membre.user) === "etudiant"
                              ? "Étudiant"
                              : getUserProfession(membre.user) === "professeur"
                              ? "Professeur"
                              : "Utilisateur"
                          }}
                        </span>
                      </div>
                    </div>
                  </div>

                  <!-- Remove Button -->
                  <button
                    (click)="removeMembreFromEquipe(membre._id)"
                    class="p-2 text-[#ff6b69] dark:text-[#ff3b30] hover:bg-[#ff6b69]/10 dark:hover:bg-[#ff3b30]/10 rounded-lg transition-all"
                    title="Retirer de l'équipe"
                  >
                    <i class="fas fa-user-minus"></i>
                  </button>
                </div>
              </div>
            </div>

            <ng-template #noMembers>
              <div class="text-center py-12">
                <div
                  class="w-16 h-16 mx-auto mb-4 text-[#00ff9d] dark:text-[#00f7ff] opacity-50"
                >
                  <i class="fas fa-users text-4xl"></i>
                </div>
                <h5
                  class="text-lg font-semibold text-[#00ff9d] dark:text-[#00f7ff] mb-2"
                >
                  Aucun membre
                </h5>
                <p class="text-[#6d6870] dark:text-[#a0a0a0] text-sm">
                  Ajoutez des membres à l'équipe
                </p>
              </div>
            </ng-template>
          </div>

          <!-- Add Member Form -->
          <div class="bg-[#f0f4f8] dark:bg-[#0a0a0a] p-6">
            <h5
              class="text-lg font-bold text-[#00ff9d] dark:text-[#00f7ff] mb-4 flex items-center"
            >
              <i class="fas fa-user-plus mr-2"></i>
              Ajouter un membre
            </h5>

            <!-- No users available -->
            <div
              *ngIf="availableUsers.length === 0"
              class="bg-[#4f5fad]/10 border-l-4 border-[#4f5fad] rounded-lg p-4"
            >
              <div class="flex items-center">
                <div class="text-[#4f5fad] mr-3 text-lg">
                  <i class="fas fa-info-circle"></i>
                </div>
                <div class="text-sm text-[#6d6870] dark:text-[#a0a0a0]">
                  Aucun utilisateur disponible. Veuillez d'abord créer des
                  utilisateurs.
                </div>
              </div>
            </div>

            <!-- Add member form -->
            <div *ngIf="availableUsers.length > 0" class="space-y-4">
              <div>
                <label
                  class="block text-sm font-medium text-[#00ff9d] dark:text-[#00f7ff] mb-2"
                  >Utilisateur</label
                >
                <select
                  #userSelect
                  class="w-full px-3 py-2 bg-white dark:bg-[#1a1a1a] border border-[#00ff9d]/20 dark:border-[#00f7ff]/20 rounded-lg text-[#6d6870] dark:text-[#a0a0a0] focus:outline-none focus:ring-2 focus:ring-[#00ff9d] dark:focus:ring-[#00f7ff] focus:border-transparent"
                >
                  <option value="" selected disabled>
                    Sélectionnez un utilisateur
                  </option>
                  <option
                    *ngFor="let user of availableUsers"
                    [value]="user._id || user.id"
                  >
                    {{ user.firstName || "" }}
                    {{ user.lastName || user.name || user.id }}
                    {{ user.email ? "- " + user.email : "" }}
                    {{
                      user.profession
                        ? "(" +
                          (user.profession === "etudiant"
                            ? "Étudiant"
                            : "Professeur") +
                          ")"
                        : ""
                    }}
                  </option>
                </select>
              </div>

              <div>
                <label
                  class="block text-sm font-medium text-[#00ff9d] dark:text-[#00f7ff] mb-2"
                  >Rôle dans l'équipe</label
                >
                <div class="grid grid-cols-2 gap-2">
                  <label
                    class="flex flex-col items-center p-3 border border-[#00ff9d]/20 dark:border-[#00f7ff]/20 rounded-lg cursor-pointer hover:bg-[#00ff9d]/5 dark:hover:bg-[#00f7ff]/5 transition-all"
                  >
                    <input
                      type="radio"
                      name="roleRadio"
                      value="membre"
                      checked
                      #roleMembre
                      class="sr-only"
                    />
                    <i class="fas fa-user text-xl mb-1 text-[#4f5fad]"></i>
                    <span
                      class="text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0]"
                      >Membre</span
                    >
                  </label>
                  <label
                    class="flex flex-col items-center p-3 border border-[#00ff9d]/20 dark:border-[#00f7ff]/20 rounded-lg cursor-pointer hover:bg-[#00ff9d]/5 dark:hover:bg-[#00f7ff]/5 transition-all"
                  >
                    <input
                      type="radio"
                      name="roleRadio"
                      value="admin"
                      #roleAdmin
                      class="sr-only"
                    />
                    <i
                      class="fas fa-user-shield text-xl mb-1 text-[#00ff9d]"
                    ></i>
                    <span
                      class="text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0]"
                      >Admin</span
                    >
                  </label>
                </div>
              </div>

              <button
                type="button"
                [disabled]="!userSelect.value"
                (click)="
                  addMembre(
                    userSelect.value,
                    roleMembre.checked ? 'membre' : 'admin'
                  );
                  userSelect.value = ''
                "
                class="w-full bg-gradient-to-r from-[#00ff9d] to-[#38ef7d] dark:from-[#00f7ff] dark:to-[#00ff9d] text-white px-4 py-3 rounded-lg font-medium transition-all duration-300 hover:scale-105 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100"
              >
                <i class="fas fa-plus-circle mr-2"></i>
                Ajouter à l'équipe
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Loading/Error State -->
<div
  class="min-h-screen bg-[#f0f4f8] dark:bg-[#0a0a0a] flex items-center justify-center"
  *ngIf="!equipe"
>
  <div class="text-center">
    <div
      class="w-20 h-20 mx-auto mb-6 text-[#dac4ea] dark:text-[#00f7ff] opacity-70"
    >
      <i class="fas fa-exclamation-triangle text-5xl"></i>
    </div>
    <h3 class="text-xl font-semibold text-[#dac4ea] dark:text-[#00f7ff] mb-2">
      Équipe non trouvée
    </h3>
    <p class="text-[#6d6870] dark:text-[#a0a0a0] text-sm mb-6">
      L'équipe est en cours de chargement ou n'existe pas
    </p>
    <button
      (click)="navigateToEquipeList()"
      class="bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea] text-white px-6 py-3 rounded-xl font-medium transition-all duration-300 hover:scale-105"
    >
      <i class="fas fa-arrow-left mr-2"></i>
      Retour à la liste des équipes
    </button>
  </div>
</div>
