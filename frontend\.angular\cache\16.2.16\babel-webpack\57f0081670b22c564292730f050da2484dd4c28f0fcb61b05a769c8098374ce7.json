{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/equipe.service\";\nimport * as i2 from \"src/app/services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"../ai-chat/ai-chat.component\";\nfunction EquipeDetailComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3);\n    i0.ɵɵelement(2, \"div\", 4)(3, \"div\", 5);\n    i0.ɵɵelementStart(4, \"div\", 6)(5, \"div\", 7);\n    i0.ɵɵelement(6, \"div\", 8)(7, \"div\", 8)(8, \"div\", 8)(9, \"div\", 8)(10, \"div\", 8)(11, \"div\", 8)(12, \"div\", 8)(13, \"div\", 8)(14, \"div\", 8)(15, \"div\", 8)(16, \"div\", 8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"div\", 9)(18, \"div\", 10);\n    i0.ɵɵelement(19, \"div\", 11)(20, \"div\", 12);\n    i0.ɵɵelementStart(21, \"div\", 13)(22, \"div\", 14)(23, \"div\", 15)(24, \"h1\", 16);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"p\", 17);\n    i0.ɵɵtext(27, \"Gestion et collaboration d'\\u00E9quipe\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 18)(29, \"div\", 19);\n    i0.ɵɵelement(30, \"i\", 20);\n    i0.ɵɵelementStart(31, \"div\", 21);\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"div\", 22);\n    i0.ɵɵtext(34, \"Membres\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(35, \"div\", 19);\n    i0.ɵɵelement(36, \"i\", 23);\n    i0.ɵɵelementStart(37, \"div\", 21);\n    i0.ɵɵtext(38, \"0\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"div\", 22);\n    i0.ɵɵtext(40, \"T\\u00E2ches\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(41, \"div\", 19);\n    i0.ɵɵelement(42, \"i\", 24);\n    i0.ɵɵelementStart(43, \"div\", 21);\n    i0.ɵɵtext(44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"div\", 22);\n    i0.ɵɵtext(46, \"Cr\\u00E9\\u00E9e le\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(47, \"div\", 25)(48, \"h3\", 26);\n    i0.ɵɵelement(49, \"i\", 27);\n    i0.ɵɵtext(50, \" Actions rapides \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(51, \"div\", 28)(52, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function EquipeDetailComponent_div_0_Template_button_click_52_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.navigateToTasks());\n    });\n    i0.ɵɵelement(53, \"i\", 30);\n    i0.ɵɵtext(54, \" G\\u00E9rer les t\\u00E2ches \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(55, \"button\", 31);\n    i0.ɵɵlistener(\"click\", function EquipeDetailComponent_div_0_Template_button_click_55_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.navigateToEditEquipe());\n    });\n    i0.ɵɵelement(56, \"i\", 32);\n    i0.ɵɵtext(57, \" Modifier l'\\u00E9quipe \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(58, \"div\", 33)(59, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function EquipeDetailComponent_div_0_Template_button_click_59_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.navigateToEquipeList());\n    });\n    i0.ɵɵelement(60, \"i\", 35);\n    i0.ɵɵtext(61, \" Retour \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(62, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function EquipeDetailComponent_div_0_Template_button_click_62_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.deleteEquipe());\n    });\n    i0.ɵɵelement(63, \"i\", 37);\n    i0.ɵɵelementEnd()()()()()()();\n    i0.ɵɵelementStart(64, \"div\", 10);\n    i0.ɵɵelement(65, \"div\", 11)(66, \"div\", 12);\n    i0.ɵɵelementStart(67, \"div\", 13)(68, \"div\", 38)(69, \"div\", 39)(70, \"div\", 40);\n    i0.ɵɵelement(71, \"i\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(72, \"h3\", 42);\n    i0.ɵɵtext(73, \"\\u00C0 propos\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(74, \"p\", 43);\n    i0.ɵɵtext(75, \"D\\u00E9tails et informations\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(76, \"div\", 44)(77, \"div\", 45)(78, \"h4\", 46);\n    i0.ɵɵtext(79, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(80, \"span\", 47);\n    i0.ɵɵelement(81, \"i\", 48);\n    i0.ɵɵtext(82);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(83, \"div\", 49)(84, \"p\", 50);\n    i0.ɵɵtext(85);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(86, \"div\", 51)(87, \"span\", 47);\n    i0.ɵɵelement(88, \"i\", 52);\n    i0.ɵɵtext(89);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(90, \"span\", 53);\n    i0.ɵɵelement(91, \"i\", 54);\n    i0.ɵɵtext(92);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(93, \"span\", 55);\n    i0.ɵɵelement(94, \"i\", 56);\n    i0.ɵɵtext(95, \" Gestion de projet \");\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(96, \"div\", 10);\n    i0.ɵɵelement(97, \"div\", 57)(98, \"div\", 58);\n    i0.ɵɵelementStart(99, \"div\", 59)(100, \"div\", 60)(101, \"div\", 61)(102, \"div\", 62)(103, \"div\", 63);\n    i0.ɵɵelement(104, \"i\", 64);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(105, \"div\")(106, \"h3\", 65);\n    i0.ɵɵtext(107, \"Assistant IA Gemini\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(108, \"p\", 66);\n    i0.ɵɵtext(109, \"G\\u00E9n\\u00E9ration de t\\u00E2ches intelligente\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(110, \"span\", 67);\n    i0.ɵɵelement(111, \"i\", 68);\n    i0.ɵɵtext(112, \" IA \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(113, \"div\", 69);\n    i0.ɵɵelement(114, \"app-ai-chat\", 70);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(25);\n    i0.ɵɵtextInterpolate(ctx_r0.equipe.name);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate((ctx_r0.equipe.members == null ? null : ctx_r0.equipe.members.length) || 0);\n    i0.ɵɵadvance(12);\n    i0.ɵɵtextInterpolate(ctx_r0.formatDate(ctx_r0.equipe.createdAt));\n    i0.ɵɵadvance(38);\n    i0.ɵɵtextInterpolate1(\" Admin: \", ctx_r0.equipe.admin ? ctx_r0.getUserName(ctx_r0.equipe.admin) || ctx_r0.equipe.admin : \"Non d\\u00E9fini\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.equipe.description || \"Aucune description disponible pour cette \\u00E9quipe.\", \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r0.equipe.members == null ? null : ctx_r0.equipe.members.length) || 0, \" membres \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" Cr\\u00E9\\u00E9e le \", ctx_r0.formatDate(ctx_r0.equipe.createdAt), \" \");\n    i0.ɵɵadvance(22);\n    i0.ɵɵproperty(\"team\", ctx_r0.equipe);\n  }\n}\nfunction EquipeDetailComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 71)(1, \"div\", 72)(2, \"div\", 73);\n    i0.ɵɵelement(3, \"i\", 74);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\", 75);\n    i0.ɵɵtext(5, \" \\u00C9quipe non trouv\\u00E9e \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 76);\n    i0.ɵɵtext(7, \" L'\\u00E9quipe est en cours de chargement ou n'existe pas \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 77);\n    i0.ɵɵlistener(\"click\", function EquipeDetailComponent_div_1_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.navigateToEquipeList());\n    });\n    i0.ɵɵelement(9, \"i\", 35);\n    i0.ɵɵtext(10, \" Retour \\u00E0 la liste des \\u00E9quipes \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nexport class EquipeDetailComponent {\n  constructor(equipeService, userService,\n  // TODO: Will be used when implementing real user API calls\n  route, router) {\n    this.equipeService = equipeService;\n    this.userService = userService;\n    this.route = route;\n    this.router = router;\n    this.equipe = null;\n    this.loading = false;\n    this.error = null;\n    this.equipeId = null;\n    this.newMembre = {\n      id: '',\n      role: 'membre'\n    };\n    this.availableUsers = [];\n    this.memberNames = {}; // Map pour stocker les noms des membres\n    this.teamMembers = []; // Liste des membres de l'équipe avec leurs détails\n  }\n\n  ngOnInit() {\n    this.equipeId = this.route.snapshot.paramMap.get('id');\n    // Charger tous les utilisateurs disponibles\n    this.loadUsers();\n    if (this.equipeId) {\n      this.loadEquipe(this.equipeId);\n    } else {\n      this.error = \"ID d'équipe non spécifié\";\n    }\n  }\n  // Méthode pour charger tous les utilisateurs\n  loadUsers() {\n    // TODO: Implémenter l'API pour récupérer les utilisateurs\n    // Pour l'instant, utiliser des données mockées\n    const mockUsers = [{\n      _id: 'user1',\n      username: 'john_doe',\n      email: '<EMAIL>',\n      role: 'admin',\n      isActive: true\n    }, {\n      _id: 'user2',\n      username: 'jane_smith',\n      email: '<EMAIL>',\n      role: 'student',\n      isActive: true\n    }, {\n      _id: 'user3',\n      username: 'bob_wilson',\n      email: '<EMAIL>',\n      role: 'teacher',\n      isActive: true\n    }];\n    // Simuler un délai d'API\n    setTimeout(() => {\n      // Stocker tous les utilisateurs pour la recherche de noms\n      const allUsers = [...mockUsers];\n      console.log('Tous les utilisateurs chargés (mock):', allUsers);\n      // Filtrer les utilisateurs disponibles (non membres de l'équipe)\n      if (this.teamMembers && this.teamMembers.length > 0) {\n        const memberUserIds = this.teamMembers.map(m => m.user);\n        this.availableUsers = mockUsers.filter(user => !memberUserIds.includes(user._id || user.id || ''));\n      } else {\n        this.availableUsers = mockUsers;\n      }\n      console.log('Utilisateurs disponibles:', this.availableUsers);\n      // Si l'équipe est déjà chargée, mettre à jour les noms des membres\n      if (this.equipe && this.equipe.members) {\n        this.updateMemberNames();\n      }\n    }, 500);\n  }\n  // Méthode pour mettre à jour les noms des membres\n  updateMemberNames() {\n    if (!this.equipe || !this.equipe.members) return;\n    this.equipe.members.forEach(membreId => {\n      const user = this.availableUsers.find(u => u._id === membreId || u.id === membreId);\n      if (user && user.name) {\n        this.memberNames[membreId] = user.name;\n      } else {\n        // Si l'utilisateur n'est pas trouvé dans la liste, essayer de le récupérer individuellement\n        // TODO: Implémenter getUser dans AuthuserService\n        // Pour l'instant, utiliser l'ID comme nom par défaut\n        this.memberNames[membreId] = membreId;\n      }\n    });\n  }\n  // Méthode pour obtenir le nom d'un membre\n  getMembreName(membreId) {\n    return this.memberNames[membreId] || membreId;\n  }\n  // Méthode pour obtenir le nom d'un utilisateur à partir de son ID\n  getUserName(userId) {\n    if (!userId) {\n      return 'Non défini';\n    }\n    const user = this.availableUsers.find(u => u._id === userId || u.id === userId);\n    if (user) {\n      if (user.firstName && user.lastName) {\n        return `${user.firstName} ${user.lastName}`;\n      } else if (user.name) {\n        return user.name;\n      }\n    }\n    return userId;\n  }\n  // Méthode pour obtenir la profession d'un utilisateur à partir de son ID\n  getUserProfession(userId) {\n    if (!userId) {\n      return '';\n    }\n    const user = this.availableUsers.find(u => u._id === userId || u.id === userId);\n    if (user) {\n      return user.profession || user.role || '';\n    }\n    return '';\n  }\n  loadEquipe(id) {\n    this.loading = true;\n    this.error = null;\n    this.equipeService.getEquipe(id).subscribe({\n      next: data => {\n        console.log(\"Détails de l'équipe chargés:\", data);\n        this.equipe = data;\n        // Charger les détails des membres de l'équipe\n        this.loadTeamMembers(id);\n        // Mettre à jour les noms des membres\n        if (this.equipe && this.equipe.members && this.equipe.members.length > 0) {\n          this.updateMemberNames();\n        }\n        this.loading = false;\n      },\n      error: error => {\n        console.error(\"Erreur lors du chargement des détails de l'équipe:\", error);\n        this.error = \"Impossible de charger les détails de l'équipe. Veuillez réessayer plus tard.\";\n        this.loading = false;\n      }\n    });\n  }\n  // Méthode pour charger les détails des membres de l'équipe\n  loadTeamMembers(teamId) {\n    this.equipeService.getTeamMembers(teamId).subscribe({\n      next: members => {\n        console.log('Détails des membres chargés:', members);\n        this.teamMembers = members;\n      },\n      error: error => {\n        console.error('Erreur lors du chargement des détails des membres:', error);\n      }\n    });\n  }\n  navigateToEditEquipe() {\n    if (this.equipeId) {\n      this.router.navigate(['/equipes/modifier', this.equipeId]);\n    }\n  }\n  navigateToEquipeList() {\n    this.router.navigate(['/equipes/liste']);\n  }\n  navigateToTasks() {\n    if (this.equipeId) {\n      this.router.navigate(['/equipes/tasks', this.equipeId]);\n    }\n  }\n  // Méthode pour formater les dates\n  formatDate(date) {\n    if (!date) {\n      return 'N/A';\n    }\n    try {\n      let dateObj;\n      if (typeof date === 'string') {\n        dateObj = new Date(date);\n      } else {\n        dateObj = date;\n      }\n      if (isNaN(dateObj.getTime())) {\n        return 'Date invalide';\n      }\n      // Format: JJ/MM/AAAA\n      return dateObj.toLocaleDateString('fr-FR', {\n        day: '2-digit',\n        month: '2-digit',\n        year: 'numeric'\n      });\n    } catch (error) {\n      console.error('Erreur lors du formatage de la date:', error);\n      return 'Erreur de date';\n    }\n  }\n  // Méthode pour ajouter un membre à l'équipe\n  addMembre(userId, role) {\n    console.log(`Ajout de l'utilisateur ${userId} avec le rôle ${role}`);\n    if (!this.equipeId || !userId) {\n      console.error(\"ID d'équipe ou ID d'utilisateur manquant\");\n      this.error = \"ID d'équipe ou ID d'utilisateur manquant\";\n      return;\n    }\n    // Vérifier si l'utilisateur est déjà membre de l'équipe\n    const isAlreadyMember = this.teamMembers.some(m => m.user === userId);\n    if (isAlreadyMember) {\n      this.error = \"Cet utilisateur est déjà membre de l'équipe\";\n      alert(\"Cet utilisateur est déjà membre de l'équipe\");\n      return;\n    }\n    // Créer l'objet membre avec le rôle spécifié\n    const membre = {\n      id: userId,\n      role: role || 'membre'\n    };\n    // Récupérer les informations de l'utilisateur pour afficher un message plus informatif\n    const userName = this.getUserName(userId);\n    const roleName = role === 'admin' ? 'administrateur' : 'membre';\n    this.equipeService.addMembreToEquipe(this.equipeId, membre).subscribe({\n      next: response => {\n        console.log(`Utilisateur \"${userName}\" ajouté comme ${roleName} avec succès:`, response);\n        // Afficher un message de succès\n        alert(`Utilisateur \"${userName}\" ajouté comme ${roleName} avec succès`);\n        // Recharger les membres de l'équipe\n        this.loadTeamMembers(this.equipeId);\n        // Recharger l'équipe pour mettre à jour la liste des membres\n        this.loadEquipe(this.equipeId);\n        // Mettre à jour la liste des utilisateurs disponibles\n        this.updateAvailableUsers();\n      },\n      error: error => {\n        console.error(\"Erreur lors de l'ajout de l'utilisateur comme membre:\", error);\n        this.error = `Impossible d'ajouter l'utilisateur \"${userName}\" comme ${roleName}. Veuillez réessayer plus tard.`;\n        alert(this.error);\n      }\n    });\n  }\n  // Méthode pour mettre à jour la liste des utilisateurs disponibles\n  updateAvailableUsers() {\n    // TODO: Implémenter l'API pour récupérer les utilisateurs\n    // Pour l'instant, utiliser les données mockées de loadUsers()\n    this.loadUsers();\n  }\n  // Ancienne méthode maintenue pour compatibilité\n  addMembreToEquipe() {\n    if (!this.equipeId || !this.newMembre.id) {\n      console.error(\"ID d'équipe ou ID de membre manquant\");\n      return;\n    }\n    this.addMembre(this.newMembre.id, this.newMembre.role || 'membre');\n  }\n  removeMembreFromEquipe(membreId) {\n    console.log('Méthode removeMembreFromEquipe appelée avec ID:', membreId);\n    if (!this.equipeId) {\n      console.error(\"ID d'équipe manquant\");\n      this.error = \"ID d'équipe manquant. Impossible de retirer le membre.\";\n      return;\n    }\n    // Dans notre implémentation actuelle, membre._id est l'ID de l'utilisateur\n    const userId = membreId;\n    // Récupérer le nom de l'utilisateur pour un message plus informatif\n    const userName = this.getUserName(userId);\n    console.log(`Tentative de retrait de l'utilisateur ${userId} (${userName}) de l'équipe ${this.equipeId}`);\n    if (confirm(`Êtes-vous sûr de vouloir retirer l'utilisateur \"${userName}\" de l'équipe?`)) {\n      console.log('Confirmation acceptée, suppression en cours...');\n      this.loading = true;\n      this.error = null;\n      this.equipeService.removeMembreFromEquipe(this.equipeId, userId).subscribe({\n        next: response => {\n          console.log(`Utilisateur \"${userName}\" retiré avec succès de l'équipe:`, response);\n          this.loading = false;\n          // Afficher un message de succès\n          alert(`Utilisateur \"${userName}\" retiré avec succès de l'équipe`);\n          // Recharger les membres de l'équipe\n          this.loadTeamMembers(this.equipeId);\n          // Recharger l'équipe pour mettre à jour la liste des membres\n          this.loadEquipe(this.equipeId);\n          // Mettre à jour la liste des utilisateurs disponibles\n          this.updateAvailableUsers();\n        },\n        error: error => {\n          console.error(`Erreur lors du retrait de l'utilisateur \"${userName}\":`, error);\n          this.loading = false;\n          this.error = `Impossible de retirer l'utilisateur \"${userName}\" de l'équipe: ${error.message || 'Erreur inconnue'}`;\n        }\n      });\n    } else {\n      console.log(\"Suppression annulée par l'utilisateur\");\n    }\n  }\n  deleteEquipe() {\n    console.log('Méthode deleteEquipe appelée');\n    if (!this.equipeId) {\n      console.error(\"ID d'équipe manquant\");\n      this.error = \"ID d'équipe manquant. Impossible de supprimer l'équipe.\";\n      return;\n    }\n    console.log(\"ID de l'équipe à supprimer:\", this.equipeId);\n    if (confirm(`Êtes-vous sûr de vouloir supprimer l'équipe \"${this.equipe?.name}\"? Cette action est irréversible.`)) {\n      console.log('Confirmation acceptée, suppression en cours...');\n      this.loading = true;\n      this.error = null;\n      this.equipeService.deleteEquipe(this.equipeId).subscribe({\n        next: () => {\n          console.log('Équipe supprimée avec succès');\n          this.loading = false;\n          alert('Équipe supprimée avec succès');\n          this.router.navigate(['/equipes/liste']);\n        },\n        error: error => {\n          console.error(\"Erreur lors de la suppression de l'équipe:\", error);\n          this.loading = false;\n          this.error = `Impossible de supprimer l'équipe: ${error.message || 'Erreur inconnue'}`;\n          alert(`Erreur lors de la suppression: ${this.error}`);\n        }\n      });\n    } else {\n      console.log(\"Suppression annulée par l'utilisateur\");\n    }\n  }\n  static {\n    this.ɵfac = function EquipeDetailComponent_Factory(t) {\n      return new (t || EquipeDetailComponent)(i0.ɵɵdirectiveInject(i1.EquipeService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: EquipeDetailComponent,\n      selectors: [[\"app-equipe-detail\"]],\n      decls: 2,\n      vars: 2,\n      consts: [[\"class\", \"min-h-screen bg-[#f0f4f8] dark:bg-[#0a0a0a] relative overflow-hidden\", 4, \"ngIf\"], [\"class\", \"min-h-screen bg-[#f0f4f8] dark:bg-[#0a0a0a] flex items-center justify-center\", 4, \"ngIf\"], [1, \"min-h-screen\", \"bg-[#f0f4f8]\", \"dark:bg-[#0a0a0a]\", \"relative\", \"overflow-hidden\"], [1, \"absolute\", \"inset-0\", \"overflow-hidden\", \"pointer-events-none\"], [1, \"absolute\", \"top-[15%]\", \"left-[10%]\", \"w-64\", \"h-64\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#dac4ea]/5\", \"to-transparent\", \"dark:from-[#00f7ff]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"bottom-[20%]\", \"right-[10%]\", \"w-80\", \"h-80\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#dac4ea]/5\", \"to-transparent\", \"dark:from-[#00f7ff]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"inset-0\", \"opacity-5\", \"dark:opacity-[0.03]\"], [1, \"h-full\", \"grid\", \"grid-cols-12\"], [1, \"border-r\", \"border-[#dac4ea]\", \"dark:border-[#00f7ff]\"], [1, \"max-w-7xl\", \"mx-auto\", \"p-6\", \"relative\", \"z-10\"], [1, \"mb-8\", \"relative\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#dac4ea]\", \"to-[#8b5a9f]\", \"dark:from-[#00f7ff]\", \"dark:to-[#dac4ea]\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#dac4ea]\", \"to-[#8b5a9f]\", \"dark:from-[#00f7ff]\", \"dark:to-[#dac4ea]\", \"blur-md\"], [1, \"bg-white\", \"dark:bg-[#1a1a1a]\", \"rounded-xl\", \"shadow-lg\", \"dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)]\", \"overflow-hidden\", \"border\", \"border-[#dac4ea]/20\", \"dark:border-[#00f7ff]/20\"], [1, \"grid\", \"grid-cols-1\", \"lg:grid-cols-3\"], [1, \"lg:col-span-2\", \"bg-gradient-to-r\", \"from-[#dac4ea]\", \"to-[#8b5a9f]\", \"dark:from-[#00f7ff]\", \"dark:to-[#dac4ea]\", \"p-8\", \"text-white\"], [1, \"text-4xl\", \"font-bold\", \"mb-2\"], [1, \"text-white/80\", \"text-lg\", \"mb-6\"], [1, \"grid\", \"grid-cols-3\", \"gap-4\"], [1, \"bg-white/20\", \"backdrop-blur-sm\", \"rounded-lg\", \"p-4\", \"text-center\"], [1, \"fas\", \"fa-users\", \"text-2xl\", \"mb-2\", \"block\"], [1, \"text-xl\", \"font-bold\"], [1, \"text-sm\", \"text-white/80\"], [1, \"fas\", \"fa-tasks\", \"text-2xl\", \"mb-2\", \"block\"], [1, \"fas\", \"fa-calendar-check\", \"text-2xl\", \"mb-2\", \"block\"], [1, \"p-8\", \"bg-white\", \"dark:bg-[#1a1a1a]\"], [1, \"text-lg\", \"font-bold\", \"text-[#dac4ea]\", \"dark:text-[#00f7ff]\", \"mb-6\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-bolt\", \"mr-2\"], [1, \"space-y-3\"], [1, \"w-full\", \"bg-gradient-to-r\", \"from-[#dac4ea]\", \"to-[#8b5a9f]\", \"dark:from-[#00f7ff]\", \"dark:to-[#dac4ea]\", \"text-white\", \"px-4\", \"py-3\", \"rounded-lg\", \"font-medium\", \"transition-all\", \"duration-300\", \"hover:scale-105\", \"shadow-lg\", 3, \"click\"], [1, \"fas\", \"fa-tasks\", \"mr-2\"], [1, \"w-full\", \"bg-[#dac4ea]/20\", \"dark:bg-[#00f7ff]/20\", \"text-[#dac4ea]\", \"dark:text-[#00f7ff]\", \"px-4\", \"py-3\", \"rounded-lg\", \"font-medium\", \"transition-all\", \"duration-300\", \"hover:scale-105\", 3, \"click\"], [1, \"fas\", \"fa-edit\", \"mr-2\"], [1, \"flex\", \"space-x-2\"], [1, \"flex-1\", \"bg-[#6d6870]/20\", \"dark:bg-[#a0a0a0]/20\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"px-4\", \"py-3\", \"rounded-lg\", \"font-medium\", \"transition-all\", \"duration-300\", \"hover:scale-105\", 3, \"click\"], [1, \"fas\", \"fa-arrow-left\", \"mr-2\"], [1, \"bg-[#ff6b69]/20\", \"dark:bg-[#ff3b30]/20\", \"text-[#ff6b69]\", \"dark:text-[#ff3b30]\", \"px-4\", \"py-3\", \"rounded-lg\", \"font-medium\", \"transition-all\", \"duration-300\", \"hover:scale-105\", 3, \"click\"], [1, \"fas\", \"fa-trash\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-4\"], [1, \"bg-gradient-to-br\", \"from-[#dac4ea]\", \"to-[#8b5a9f]\", \"dark:from-[#00f7ff]\", \"dark:to-[#dac4ea]\", \"p-6\", \"flex\", \"flex-col\", \"items-center\", \"justify-center\", \"text-white\"], [1, \"w-16\", \"h-16\", \"bg-white/20\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\", \"mb-4\"], [1, \"fas\", \"fa-info-circle\", \"text-2xl\"], [1, \"text-lg\", \"font-bold\", \"mb-1\"], [1, \"text-white/80\", \"text-sm\", \"text-center\"], [1, \"md:col-span-3\", \"p-6\"], [1, \"flex\", \"justify-between\", \"items-center\", \"mb-4\"], [1, \"text-lg\", \"font-bold\", \"text-[#dac4ea]\", \"dark:text-[#00f7ff]\"], [1, \"bg-[#dac4ea]/10\", \"dark:bg-[#00f7ff]/10\", \"text-[#dac4ea]\", \"dark:text-[#00f7ff]\", \"px-3\", \"py-1\", \"rounded-full\", \"text-sm\", \"font-medium\"], [1, \"fas\", \"fa-user-shield\", \"mr-1\"], [1, \"bg-[#f0f4f8]\", \"dark:bg-[#0a0a0a]\", \"border-l-4\", \"border-[#dac4ea]\", \"dark:border-[#00f7ff]\", \"rounded-lg\", \"p-4\", \"mb-4\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"leading-relaxed\"], [1, \"flex\", \"flex-wrap\", \"gap-2\"], [1, \"fas\", \"fa-users\", \"mr-1\"], [1, \"bg-[#00ff9d]/10\", \"text-[#00ff9d]\", \"px-3\", \"py-1\", \"rounded-full\", \"text-sm\", \"font-medium\"], [1, \"fas\", \"fa-calendar-check\", \"mr-1\"], [1, \"bg-[#4f5fad]/10\", \"text-[#4f5fad]\", \"px-3\", \"py-1\", \"rounded-full\", \"text-sm\", \"font-medium\"], [1, \"fas\", \"fa-project-diagram\", \"mr-1\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#8b5a9f]\", \"to-[#4a00e0]\", \"dark:from-[#00f7ff]\", \"dark:to-[#8b5a9f]\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#8b5a9f]\", \"to-[#4a00e0]\", \"dark:from-[#00f7ff]\", \"dark:to-[#8b5a9f]\", \"blur-md\"], [1, \"bg-white\", \"dark:bg-[#1a1a1a]\", \"rounded-xl\", \"shadow-lg\", \"dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)]\", \"overflow-hidden\", \"border\", \"border-[#8b5a9f]/20\", \"dark:border-[#00f7ff]/20\"], [1, \"bg-gradient-to-r\", \"from-[#8b5a9f]\", \"to-[#4a00e0]\", \"dark:from-[#00f7ff]\", \"dark:to-[#8b5a9f]\", \"p-6\"], [1, \"flex\", \"items-center\", \"justify-between\"], [1, \"flex\", \"items-center\"], [1, \"w-12\", \"h-12\", \"bg-white/20\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\", \"mr-4\"], [1, \"fas\", \"fa-robot\", \"text-white\", \"text-xl\"], [1, \"text-xl\", \"font-bold\", \"text-white\"], [1, \"text-white/80\", \"text-sm\"], [1, \"bg-white/20\", \"text-white\", \"px-3\", \"py-1\", \"rounded-full\", \"text-sm\", \"font-medium\"], [1, \"fas\", \"fa-magic\", \"mr-1\"], [1, \"p-0\"], [3, \"team\"], [1, \"min-h-screen\", \"bg-[#f0f4f8]\", \"dark:bg-[#0a0a0a]\", \"flex\", \"items-center\", \"justify-center\"], [1, \"text-center\"], [1, \"w-20\", \"h-20\", \"mx-auto\", \"mb-6\", \"text-[#dac4ea]\", \"dark:text-[#00f7ff]\", \"opacity-70\"], [1, \"fas\", \"fa-exclamation-triangle\", \"text-5xl\"], [1, \"text-xl\", \"font-semibold\", \"text-[#dac4ea]\", \"dark:text-[#00f7ff]\", \"mb-2\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"text-sm\", \"mb-6\"], [1, \"bg-gradient-to-r\", \"from-[#dac4ea]\", \"to-[#8b5a9f]\", \"dark:from-[#00f7ff]\", \"dark:to-[#dac4ea]\", \"text-white\", \"px-6\", \"py-3\", \"rounded-xl\", \"font-medium\", \"transition-all\", \"duration-300\", \"hover:scale-105\", 3, \"click\"]],\n      template: function EquipeDetailComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, EquipeDetailComponent_div_0_Template, 115, 8, \"div\", 0);\n          i0.ɵɵtemplate(1, EquipeDetailComponent_div_1_Template, 11, 0, \"div\", 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.equipe);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.equipe);\n        }\n      },\n      dependencies: [i4.NgIf, i5.AiChatComponent],\n      styles: [\"\\n\\n.cursor-pointer[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n}\\n\\nsummary[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImVxdWlwZS1kZXRhaWwuY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQSwyQ0FBMkM7QUFDM0M7RUFDRSxlQUFlO0FBQ2pCOztBQUVBO0VBQ0UsMEJBQTBCO0FBQzVCIiwiZmlsZSI6ImVxdWlwZS1kZXRhaWwuY29tcG9uZW50LmNzcyIsInNvdXJjZXNDb250ZW50IjpbIi8qIFN0eWxlcyBwb3VyIGxlIGNvbXBvc2FudCBlcXVpcGUtZGV0YWlsICovXHJcbi5jdXJzb3ItcG9pbnRlciB7XHJcbiAgY3Vyc29yOiBwb2ludGVyO1xyXG59XHJcblxyXG5zdW1tYXJ5OmhvdmVyIHtcclxuICB0ZXh0LWRlY29yYXRpb246IHVuZGVybGluZTtcclxufSJdfQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvYWRtaW4vZXF1aXBlcy9lcXVpcGUtZGV0YWlsL2VxdWlwZS1kZXRhaWwuY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQSwyQ0FBMkM7QUFDM0M7RUFDRSxlQUFlO0FBQ2pCOztBQUVBO0VBQ0UsMEJBQTBCO0FBQzVCO0FBQ0Esd2dCQUF3Z0IiLCJzb3VyY2VzQ29udGVudCI6WyIvKiBTdHlsZXMgcG91ciBsZSBjb21wb3NhbnQgZXF1aXBlLWRldGFpbCAqL1xyXG4uY3Vyc29yLXBvaW50ZXIge1xyXG4gIGN1cnNvcjogcG9pbnRlcjtcclxufVxyXG5cclxuc3VtbWFyeTpob3ZlciB7XHJcbiAgdGV4dC1kZWNvcmF0aW9uOiB1bmRlcmxpbmU7XHJcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "EquipeDetailComponent_div_0_Template_button_click_52_listener", "ɵɵrestoreView", "_r3", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "navigateToTasks", "EquipeDetailComponent_div_0_Template_button_click_55_listener", "ctx_r4", "navigateToEditEquipe", "EquipeDetailComponent_div_0_Template_button_click_59_listener", "ctx_r5", "navigateToEquipeList", "EquipeDetailComponent_div_0_Template_button_click_62_listener", "ctx_r6", "deleteEquipe", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "equipe", "name", "members", "length", "formatDate", "createdAt", "ɵɵtextInterpolate1", "admin", "getUserName", "description", "ɵɵproperty", "EquipeDetailComponent_div_1_Template_button_click_8_listener", "_r8", "ctx_r7", "EquipeDetailComponent", "constructor", "equipeService", "userService", "route", "router", "loading", "error", "equipeId", "newMembre", "id", "role", "availableUsers", "memberNames", "teamMembers", "ngOnInit", "snapshot", "paramMap", "get", "loadUsers", "loadEquipe", "mockUsers", "_id", "username", "email", "isActive", "setTimeout", "allUsers", "console", "log", "memberUserIds", "map", "m", "user", "filter", "includes", "updateMemberNames", "for<PERSON>ach", "membreId", "find", "u", "getMembreName", "userId", "firstName", "lastName", "getUserProfession", "profession", "getEquipe", "subscribe", "next", "data", "loadTeamMembers", "teamId", "getTeamMembers", "navigate", "date", "date<PERSON><PERSON>j", "Date", "isNaN", "getTime", "toLocaleDateString", "day", "month", "year", "addMembre", "isAlreadyMember", "some", "alert", "membre", "userName", "<PERSON><PERSON><PERSON>", "addMembreToEquipe", "response", "updateAvailableUsers", "removeMembreFromEquipe", "confirm", "message", "ɵɵdirectiveInject", "i1", "EquipeService", "i2", "AuthService", "i3", "ActivatedRoute", "Router", "selectors", "decls", "vars", "consts", "template", "EquipeDetailComponent_Template", "rf", "ctx", "ɵɵtemplate", "EquipeDetailComponent_div_0_Template", "EquipeDetailComponent_div_1_Template"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\admin\\equipes\\equipe-detail\\equipe-detail.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\admin\\equipes\\equipe-detail\\equipe-detail.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { EquipeService } from 'src/app/services/equipe.service';\nimport { AuthService } from 'src/app/services/auth.service';\nimport { Equipe } from 'src/app/models/equipe.model';\nimport { Membre } from 'src/app/models/membre.model';\nimport { User } from 'src/app/models/user.model';\n@Component({\n  selector: 'app-equipe-detail',\n  templateUrl: './equipe-detail.component.html',\n  styleUrls: ['./equipe-detail.component.css'],\n})\nexport class EquipeDetailComponent implements OnInit {\n  equipe: Equipe | null = null;\n  loading = false;\n  error: string | null = null;\n  equipeId: string | null = null;\n  newMembre: any = { id: '', role: 'membre' };\n  availableUsers: User[] = [];\n  memberNames: { [key: string]: string } = {}; // Map pour stocker les noms des membres\n  teamMembers: any[] = []; // Liste des membres de l'équipe avec leurs détails\n\n  constructor(\n    private equipeService: EquipeService,\n    private userService: AuthService, // TODO: Will be used when implementing real user API calls\n    private route: ActivatedRoute,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    this.equipeId = this.route.snapshot.paramMap.get('id');\n\n    // Charger tous les utilisateurs disponibles\n    this.loadUsers();\n\n    if (this.equipeId) {\n      this.loadEquipe(this.equipeId);\n    } else {\n      this.error = \"ID d'équipe non spécifié\";\n    }\n  }\n\n  // Méthode pour charger tous les utilisateurs\n  loadUsers(): void {\n    // TODO: Implémenter l'API pour récupérer les utilisateurs\n    // Pour l'instant, utiliser des données mockées\n    const mockUsers: User[] = [\n      {\n        _id: 'user1',\n        username: 'john_doe',\n        email: '<EMAIL>',\n        role: 'admin',\n        isActive: true,\n      },\n      {\n        _id: 'user2',\n        username: 'jane_smith',\n        email: '<EMAIL>',\n        role: 'student',\n        isActive: true,\n      },\n      {\n        _id: 'user3',\n        username: 'bob_wilson',\n        email: '<EMAIL>',\n        role: 'teacher',\n        isActive: true,\n      },\n    ];\n\n    // Simuler un délai d'API\n    setTimeout(() => {\n      // Stocker tous les utilisateurs pour la recherche de noms\n      const allUsers = [...mockUsers];\n      console.log('Tous les utilisateurs chargés (mock):', allUsers);\n\n      // Filtrer les utilisateurs disponibles (non membres de l'équipe)\n      if (this.teamMembers && this.teamMembers.length > 0) {\n        const memberUserIds = this.teamMembers.map((m) => m.user);\n        this.availableUsers = mockUsers.filter(\n          (user) => !memberUserIds.includes(user._id || user.id || '')\n        );\n      } else {\n        this.availableUsers = mockUsers;\n      }\n\n      console.log('Utilisateurs disponibles:', this.availableUsers);\n\n      // Si l'équipe est déjà chargée, mettre à jour les noms des membres\n      if (this.equipe && this.equipe.members) {\n        this.updateMemberNames();\n      }\n    }, 500);\n  }\n\n  // Méthode pour mettre à jour les noms des membres\n  updateMemberNames(): void {\n    if (!this.equipe || !this.equipe.members) return;\n\n    this.equipe.members.forEach((membreId) => {\n      const user = this.availableUsers.find(\n        (u) => u._id === membreId || u.id === membreId\n      );\n      if (user && user.name) {\n        this.memberNames[membreId] = user.name;\n      } else {\n        // Si l'utilisateur n'est pas trouvé dans la liste, essayer de le récupérer individuellement\n        // TODO: Implémenter getUser dans AuthuserService\n        // Pour l'instant, utiliser l'ID comme nom par défaut\n        this.memberNames[membreId] = membreId;\n      }\n    });\n  }\n\n  // Méthode pour obtenir le nom d'un membre\n  getMembreName(membreId: string): string {\n    return this.memberNames[membreId] || membreId;\n  }\n\n  // Méthode pour obtenir le nom d'un utilisateur à partir de son ID\n  getUserName(userId: string | undefined): string {\n    if (!userId) {\n      return 'Non défini';\n    }\n\n    const user = this.availableUsers.find(\n      (u) => u._id === userId || u.id === userId\n    );\n    if (user) {\n      if (user.firstName && user.lastName) {\n        return `${user.firstName} ${user.lastName}`;\n      } else if (user.name) {\n        return user.name;\n      }\n    }\n    return userId;\n  }\n\n  // Méthode pour obtenir la profession d'un utilisateur à partir de son ID\n  getUserProfession(userId: string | undefined): string {\n    if (!userId) {\n      return '';\n    }\n\n    const user = this.availableUsers.find(\n      (u) => u._id === userId || u.id === userId\n    );\n    if (user) {\n      return user.profession || user.role || '';\n    }\n    return '';\n  }\n\n  loadEquipe(id: string): void {\n    this.loading = true;\n    this.error = null;\n\n    this.equipeService.getEquipe(id).subscribe({\n      next: (data) => {\n        console.log(\"Détails de l'équipe chargés:\", data);\n        this.equipe = data;\n\n        // Charger les détails des membres de l'équipe\n        this.loadTeamMembers(id);\n\n        // Mettre à jour les noms des membres\n        if (\n          this.equipe &&\n          this.equipe.members &&\n          this.equipe.members.length > 0\n        ) {\n          this.updateMemberNames();\n        }\n\n        this.loading = false;\n      },\n      error: (error) => {\n        console.error(\n          \"Erreur lors du chargement des détails de l'équipe:\",\n          error\n        );\n        this.error =\n          \"Impossible de charger les détails de l'équipe. Veuillez réessayer plus tard.\";\n        this.loading = false;\n      },\n    });\n  }\n\n  // Méthode pour charger les détails des membres de l'équipe\n  loadTeamMembers(teamId: string): void {\n    this.equipeService.getTeamMembers(teamId).subscribe({\n      next: (members) => {\n        console.log('Détails des membres chargés:', members);\n        this.teamMembers = members;\n      },\n      error: (error) => {\n        console.error(\n          'Erreur lors du chargement des détails des membres:',\n          error\n        );\n      },\n    });\n  }\n\n  navigateToEditEquipe(): void {\n    if (this.equipeId) {\n      this.router.navigate(['/equipes/modifier', this.equipeId]);\n    }\n  }\n\n  navigateToEquipeList(): void {\n    this.router.navigate(['/equipes/liste']);\n  }\n\n  navigateToTasks(): void {\n    if (this.equipeId) {\n      this.router.navigate(['/equipes/tasks', this.equipeId]);\n    }\n  }\n\n  // Méthode pour formater les dates\n  formatDate(date: Date | string | undefined): string {\n    if (!date) {\n      return 'N/A';\n    }\n\n    try {\n      let dateObj: Date;\n\n      if (typeof date === 'string') {\n        dateObj = new Date(date);\n      } else {\n        dateObj = date;\n      }\n\n      if (isNaN(dateObj.getTime())) {\n        return 'Date invalide';\n      }\n\n      // Format: JJ/MM/AAAA\n      return dateObj.toLocaleDateString('fr-FR', {\n        day: '2-digit',\n        month: '2-digit',\n        year: 'numeric',\n      });\n    } catch (error) {\n      console.error('Erreur lors du formatage de la date:', error);\n      return 'Erreur de date';\n    }\n  }\n\n  // Méthode pour ajouter un membre à l'équipe\n  addMembre(userId: string, role: string): void {\n    console.log(`Ajout de l'utilisateur ${userId} avec le rôle ${role}`);\n\n    if (!this.equipeId || !userId) {\n      console.error(\"ID d'équipe ou ID d'utilisateur manquant\");\n      this.error = \"ID d'équipe ou ID d'utilisateur manquant\";\n      return;\n    }\n\n    // Vérifier si l'utilisateur est déjà membre de l'équipe\n    const isAlreadyMember = this.teamMembers.some((m) => m.user === userId);\n    if (isAlreadyMember) {\n      this.error = \"Cet utilisateur est déjà membre de l'équipe\";\n      alert(\"Cet utilisateur est déjà membre de l'équipe\");\n      return;\n    }\n\n    // Créer l'objet membre avec le rôle spécifié\n    const membre: Membre = {\n      id: userId,\n      role: role || 'membre',\n    };\n\n    // Récupérer les informations de l'utilisateur pour afficher un message plus informatif\n    const userName = this.getUserName(userId);\n    const roleName = role === 'admin' ? 'administrateur' : 'membre';\n\n    this.equipeService.addMembreToEquipe(this.equipeId, membre).subscribe({\n      next: (response) => {\n        console.log(\n          `Utilisateur \"${userName}\" ajouté comme ${roleName} avec succès:`,\n          response\n        );\n\n        // Afficher un message de succès\n        alert(`Utilisateur \"${userName}\" ajouté comme ${roleName} avec succès`);\n\n        // Recharger les membres de l'équipe\n        this.loadTeamMembers(this.equipeId!);\n\n        // Recharger l'équipe pour mettre à jour la liste des membres\n        this.loadEquipe(this.equipeId!);\n\n        // Mettre à jour la liste des utilisateurs disponibles\n        this.updateAvailableUsers();\n      },\n      error: (error) => {\n        console.error(\n          \"Erreur lors de l'ajout de l'utilisateur comme membre:\",\n          error\n        );\n        this.error = `Impossible d'ajouter l'utilisateur \"${userName}\" comme ${roleName}. Veuillez réessayer plus tard.`;\n        alert(this.error);\n      },\n    });\n  }\n\n  // Méthode pour mettre à jour la liste des utilisateurs disponibles\n  updateAvailableUsers(): void {\n    // TODO: Implémenter l'API pour récupérer les utilisateurs\n    // Pour l'instant, utiliser les données mockées de loadUsers()\n    this.loadUsers();\n  }\n\n  // Ancienne méthode maintenue pour compatibilité\n  addMembreToEquipe(): void {\n    if (!this.equipeId || !this.newMembre.id) {\n      console.error(\"ID d'équipe ou ID de membre manquant\");\n      return;\n    }\n\n    this.addMembre(this.newMembre.id, this.newMembre.role || 'membre');\n  }\n\n  removeMembreFromEquipe(membreId: string): void {\n    console.log('Méthode removeMembreFromEquipe appelée avec ID:', membreId);\n\n    if (!this.equipeId) {\n      console.error(\"ID d'équipe manquant\");\n      this.error = \"ID d'équipe manquant. Impossible de retirer le membre.\";\n      return;\n    }\n\n    // Dans notre implémentation actuelle, membre._id est l'ID de l'utilisateur\n    const userId = membreId;\n\n    // Récupérer le nom de l'utilisateur pour un message plus informatif\n    const userName = this.getUserName(userId);\n\n    console.log(\n      `Tentative de retrait de l'utilisateur ${userId} (${userName}) de l'équipe ${this.equipeId}`\n    );\n\n    if (\n      confirm(\n        `Êtes-vous sûr de vouloir retirer l'utilisateur \"${userName}\" de l'équipe?`\n      )\n    ) {\n      console.log('Confirmation acceptée, suppression en cours...');\n\n      this.loading = true;\n      this.error = null;\n\n      this.equipeService\n        .removeMembreFromEquipe(this.equipeId, userId)\n        .subscribe({\n          next: (response) => {\n            console.log(\n              `Utilisateur \"${userName}\" retiré avec succès de l'équipe:`,\n              response\n            );\n            this.loading = false;\n\n            // Afficher un message de succès\n            alert(`Utilisateur \"${userName}\" retiré avec succès de l'équipe`);\n\n            // Recharger les membres de l'équipe\n            this.loadTeamMembers(this.equipeId!);\n\n            // Recharger l'équipe pour mettre à jour la liste des membres\n            this.loadEquipe(this.equipeId!);\n\n            // Mettre à jour la liste des utilisateurs disponibles\n            this.updateAvailableUsers();\n          },\n          error: (error) => {\n            console.error(\n              `Erreur lors du retrait de l'utilisateur \"${userName}\":`,\n              error\n            );\n            this.loading = false;\n            this.error = `Impossible de retirer l'utilisateur \"${userName}\" de l'équipe: ${\n              error.message || 'Erreur inconnue'\n            }`;\n          },\n        });\n    } else {\n      console.log(\"Suppression annulée par l'utilisateur\");\n    }\n  }\n\n  deleteEquipe(): void {\n    console.log('Méthode deleteEquipe appelée');\n\n    if (!this.equipeId) {\n      console.error(\"ID d'équipe manquant\");\n      this.error = \"ID d'équipe manquant. Impossible de supprimer l'équipe.\";\n      return;\n    }\n\n    console.log(\"ID de l'équipe à supprimer:\", this.equipeId);\n\n    if (\n      confirm(\n        `Êtes-vous sûr de vouloir supprimer l'équipe \"${this.equipe?.name}\"? Cette action est irréversible.`\n      )\n    ) {\n      console.log('Confirmation acceptée, suppression en cours...');\n\n      this.loading = true;\n      this.error = null;\n\n      this.equipeService.deleteEquipe(this.equipeId).subscribe({\n        next: () => {\n          console.log('Équipe supprimée avec succès');\n          this.loading = false;\n          alert('Équipe supprimée avec succès');\n          this.router.navigate(['/equipes/liste']);\n        },\n        error: (error) => {\n          console.error(\"Erreur lors de la suppression de l'équipe:\", error);\n          this.loading = false;\n          this.error = `Impossible de supprimer l'équipe: ${\n            error.message || 'Erreur inconnue'\n          }`;\n          alert(`Erreur lors de la suppression: ${this.error}`);\n        },\n      });\n    } else {\n      console.log(\"Suppression annulée par l'utilisateur\");\n    }\n  }\n}\n", "<div class=\"min-h-screen bg-[#f0f4f8] dark:bg-[#0a0a0a] relative overflow-hidden\" *ngIf=\"equipe\">\n  <!-- Background decorative elements -->\n  <div class=\"absolute inset-0 overflow-hidden pointer-events-none\">\n    <div\n      class=\"absolute top-[15%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-br from-[#dac4ea]/5 to-transparent dark:from-[#00f7ff]/3 dark:to-transparent blur-3xl\"\n    ></div>\n    <div\n      class=\"absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#dac4ea]/5 to-transparent dark:from-[#00f7ff]/3 dark:to-transparent blur-3xl\"\n    ></div>\n\n    <!-- Grid pattern -->\n    <div class=\"absolute inset-0 opacity-5 dark:opacity-[0.03]\">\n      <div class=\"h-full grid grid-cols-12\">\n        <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n      </div>\n    </div>\n  </div>\n\n  <div class=\"max-w-7xl mx-auto p-6 relative z-10\">\n    <!-- Header Hero Section -->\n    <div class=\"mb-8 relative\">\n      <!-- Decorative top border -->\n      <div class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea]\"></div>\n      <div class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea] blur-md\"></div>\n      \n      <div class=\"bg-white dark:bg-[#1a1a1a] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] overflow-hidden border border-[#dac4ea]/20 dark:border-[#00f7ff]/20\">\n        <div class=\"grid grid-cols-1 lg:grid-cols-3\">\n          <!-- Team Info -->\n          <div class=\"lg:col-span-2 bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea] p-8 text-white\">\n            <h1 class=\"text-4xl font-bold mb-2\">{{ equipe.name }}</h1>\n            <p class=\"text-white/80 text-lg mb-6\">Gestion et collaboration d'équipe</p>\n            \n            <!-- Stats -->\n            <div class=\"grid grid-cols-3 gap-4\">\n              <div class=\"bg-white/20 backdrop-blur-sm rounded-lg p-4 text-center\">\n                <i class=\"fas fa-users text-2xl mb-2 block\"></i>\n                <div class=\"text-xl font-bold\">{{ equipe.members?.length || 0 }}</div>\n                <div class=\"text-sm text-white/80\">Membres</div>\n              </div>\n              <div class=\"bg-white/20 backdrop-blur-sm rounded-lg p-4 text-center\">\n                <i class=\"fas fa-tasks text-2xl mb-2 block\"></i>\n                <div class=\"text-xl font-bold\">0</div>\n                <div class=\"text-sm text-white/80\">Tâches</div>\n              </div>\n              <div class=\"bg-white/20 backdrop-blur-sm rounded-lg p-4 text-center\">\n                <i class=\"fas fa-calendar-check text-2xl mb-2 block\"></i>\n                <div class=\"text-xl font-bold\">{{ formatDate(equipe.createdAt) }}</div>\n                <div class=\"text-sm text-white/80\">Créée le</div>\n              </div>\n            </div>\n          </div>\n          \n          <!-- Quick Actions -->\n          <div class=\"p-8 bg-white dark:bg-[#1a1a1a]\">\n            <h3 class=\"text-lg font-bold text-[#dac4ea] dark:text-[#00f7ff] mb-6 flex items-center\">\n              <i class=\"fas fa-bolt mr-2\"></i>\n              Actions rapides\n            </h3>\n            <div class=\"space-y-3\">\n              <button \n                (click)=\"navigateToTasks()\"\n                class=\"w-full bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea] text-white px-4 py-3 rounded-lg font-medium transition-all duration-300 hover:scale-105 shadow-lg\"\n              >\n                <i class=\"fas fa-tasks mr-2\"></i>\n                Gérer les tâches\n              </button>\n              <button \n                (click)=\"navigateToEditEquipe()\"\n                class=\"w-full bg-[#dac4ea]/20 dark:bg-[#00f7ff]/20 text-[#dac4ea] dark:text-[#00f7ff] px-4 py-3 rounded-lg font-medium transition-all duration-300 hover:scale-105\"\n              >\n                <i class=\"fas fa-edit mr-2\"></i>\n                Modifier l'équipe\n              </button>\n              <div class=\"flex space-x-2\">\n                <button \n                  (click)=\"navigateToEquipeList()\"\n                  class=\"flex-1 bg-[#6d6870]/20 dark:bg-[#a0a0a0]/20 text-[#6d6870] dark:text-[#a0a0a0] px-4 py-3 rounded-lg font-medium transition-all duration-300 hover:scale-105\"\n                >\n                  <i class=\"fas fa-arrow-left mr-2\"></i>\n                  Retour\n                </button>\n                <button \n                  (click)=\"deleteEquipe()\"\n                  class=\"bg-[#ff6b69]/20 dark:bg-[#ff3b30]/20 text-[#ff6b69] dark:text-[#ff3b30] px-4 py-3 rounded-lg font-medium transition-all duration-300 hover:scale-105\"\n                >\n                  <i class=\"fas fa-trash\"></i>\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Team Information -->\n    <div class=\"mb-8 relative\">\n      <!-- Decorative top border -->\n      <div class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea]\"></div>\n      <div class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea] blur-md\"></div>\n      \n      <div class=\"bg-white dark:bg-[#1a1a1a] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] overflow-hidden border border-[#dac4ea]/20 dark:border-[#00f7ff]/20\">\n        <div class=\"grid grid-cols-1 md:grid-cols-4\">\n          <!-- Icon Section -->\n          <div class=\"bg-gradient-to-br from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea] p-6 flex flex-col items-center justify-center text-white\">\n            <div class=\"w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mb-4\">\n              <i class=\"fas fa-info-circle text-2xl\"></i>\n            </div>\n            <h3 class=\"text-lg font-bold mb-1\">À propos</h3>\n            <p class=\"text-white/80 text-sm text-center\">Détails et informations</p>\n          </div>\n          \n          <!-- Content Section -->\n          <div class=\"md:col-span-3 p-6\">\n            <div class=\"flex justify-between items-center mb-4\">\n              <h4 class=\"text-lg font-bold text-[#dac4ea] dark:text-[#00f7ff]\">Description</h4>\n              <span class=\"bg-[#dac4ea]/10 dark:bg-[#00f7ff]/10 text-[#dac4ea] dark:text-[#00f7ff] px-3 py-1 rounded-full text-sm font-medium\">\n                <i class=\"fas fa-user-shield mr-1\"></i>\n                Admin: {{ equipe.admin ? (getUserName(equipe.admin) || equipe.admin) : 'Non défini' }}\n              </span>\n            </div>\n            \n            <div class=\"bg-[#f0f4f8] dark:bg-[#0a0a0a] border-l-4 border-[#dac4ea] dark:border-[#00f7ff] rounded-lg p-4 mb-4\">\n              <p class=\"text-[#6d6870] dark:text-[#a0a0a0] leading-relaxed\">\n                {{ equipe.description || 'Aucune description disponible pour cette équipe.' }}\n              </p>\n            </div>\n            \n            <!-- Tags -->\n            <div class=\"flex flex-wrap gap-2\">\n              <span class=\"bg-[#dac4ea]/10 dark:bg-[#00f7ff]/10 text-[#dac4ea] dark:text-[#00f7ff] px-3 py-1 rounded-full text-sm font-medium\">\n                <i class=\"fas fa-users mr-1\"></i>\n                {{ equipe.members?.length || 0 }} membres\n              </span>\n              <span class=\"bg-[#00ff9d]/10 text-[#00ff9d] px-3 py-1 rounded-full text-sm font-medium\">\n                <i class=\"fas fa-calendar-check mr-1\"></i>\n                Créée le {{ formatDate(equipe.createdAt) }}\n              </span>\n              <span class=\"bg-[#4f5fad]/10 text-[#4f5fad] px-3 py-1 rounded-full text-sm font-medium\">\n                <i class=\"fas fa-project-diagram mr-1\"></i>\n                Gestion de projet\n              </span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- AI Assistant -->\n    <div class=\"mb-8 relative\">\n      <!-- Decorative top border -->\n      <div class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#8b5a9f] to-[#4a00e0] dark:from-[#00f7ff] dark:to-[#8b5a9f]\"></div>\n      <div class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#8b5a9f] to-[#4a00e0] dark:from-[#00f7ff] dark:to-[#8b5a9f] blur-md\"></div>\n      \n      <div class=\"bg-white dark:bg-[#1a1a1a] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] overflow-hidden border border-[#8b5a9f]/20 dark:border-[#00f7ff]/20\">\n        <!-- Header -->\n        <div class=\"bg-gradient-to-r from-[#8b5a9f] to-[#4a00e0] dark:from-[#00f7ff] dark:to-[#8b5a9f] p-6\">\n          <div class=\"flex items-center justify-between\">\n            <div class=\"flex items-center\">\n              <div class=\"w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mr-4\">\n                <i class=\"fas fa-robot text-white text-xl\"></i>\n              </div>\n              <div>\n                <h3 class=\"text-xl font-bold text-white\">Assistant IA Gemini</h3>\n                <p class=\"text-white/80 text-sm\">Génération de tâches intelligente</p>\n              </div>\n            </div>\n            <span class=\"bg-white/20 text-white px-3 py-1 rounded-full text-sm font-medium\">\n              <i class=\"fas fa-magic mr-1\"></i>\n              IA\n            </span>\n          </div>\n        </div>\n        \n        <!-- AI Chat Component -->\n        <div class=\"p-0\">\n          <app-ai-chat [team]=\"equipe\"></app-ai-chat>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n\n<!-- Loading/Error State -->\n<div class=\"min-h-screen bg-[#f0f4f8] dark:bg-[#0a0a0a] flex items-center justify-center\" *ngIf=\"!equipe\">\n  <div class=\"text-center\">\n    <div class=\"w-20 h-20 mx-auto mb-6 text-[#dac4ea] dark:text-[#00f7ff] opacity-70\">\n      <i class=\"fas fa-exclamation-triangle text-5xl\"></i>\n    </div>\n    <h3 class=\"text-xl font-semibold text-[#dac4ea] dark:text-[#00f7ff] mb-2\">\n      Équipe non trouvée\n    </h3>\n    <p class=\"text-[#6d6870] dark:text-[#a0a0a0] text-sm mb-6\">\n      L'équipe est en cours de chargement ou n'existe pas\n    </p>\n    <button \n      (click)=\"navigateToEquipeList()\"\n      class=\"bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea] text-white px-6 py-3 rounded-xl font-medium transition-all duration-300 hover:scale-105\"\n    >\n      <i class=\"fas fa-arrow-left mr-2\"></i>\n      Retour à la liste des équipes\n    </button>\n  </div>\n</div>\n"], "mappings": ";;;;;;;;;ICAAA,EAAA,CAAAC,cAAA,aAAiG;IAG7FD,EAAA,CAAAE,SAAA,aAEO;IAMPF,EAAA,CAAAC,cAAA,aAA4D;IAExDD,EAAA,CAAAE,SAAA,aAAmE;IAWrEF,EAAA,CAAAG,YAAA,EAAM;IAIVH,EAAA,CAAAC,cAAA,cAAiD;IAI7CD,EAAA,CAAAE,SAAA,eAAwI;IAGxIF,EAAA,CAAAC,cAAA,eAA0K;IAIhID,EAAA,CAAAI,MAAA,IAAiB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAC1DH,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAI,MAAA,8CAAiC;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAG3EH,EAAA,CAAAC,cAAA,eAAoC;IAEhCD,EAAA,CAAAE,SAAA,aAAgD;IAChDF,EAAA,CAAAC,cAAA,eAA+B;IAAAD,EAAA,CAAAI,MAAA,IAAiC;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IACtEH,EAAA,CAAAC,cAAA,eAAmC;IAAAD,EAAA,CAAAI,MAAA,eAAO;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAElDH,EAAA,CAAAC,cAAA,eAAqE;IACnED,EAAA,CAAAE,SAAA,aAAgD;IAChDF,EAAA,CAAAC,cAAA,eAA+B;IAAAD,EAAA,CAAAI,MAAA,SAAC;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IACtCH,EAAA,CAAAC,cAAA,eAAmC;IAAAD,EAAA,CAAAI,MAAA,mBAAM;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAEjDH,EAAA,CAAAC,cAAA,eAAqE;IACnED,EAAA,CAAAE,SAAA,aAAyD;IACzDF,EAAA,CAAAC,cAAA,eAA+B;IAAAD,EAAA,CAAAI,MAAA,IAAkC;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IACvEH,EAAA,CAAAC,cAAA,eAAmC;IAAAD,EAAA,CAAAI,MAAA,0BAAQ;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAMvDH,EAAA,CAAAC,cAAA,eAA4C;IAExCD,EAAA,CAAAE,SAAA,aAAgC;IAChCF,EAAA,CAAAI,MAAA,yBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,eAAuB;IAEnBD,EAAA,CAAAK,UAAA,mBAAAC,8DAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAF,MAAA,CAAAG,eAAA,EAAiB;IAAA,EAAC;IAG3BZ,EAAA,CAAAE,SAAA,aAAiC;IACjCF,EAAA,CAAAI,MAAA,oCACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAGC;IAFCD,EAAA,CAAAK,UAAA,mBAAAQ,8DAAA;MAAAb,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAM,MAAA,GAAAd,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAG,MAAA,CAAAC,oBAAA,EAAsB;IAAA,EAAC;IAGhCf,EAAA,CAAAE,SAAA,aAAgC;IAChCF,EAAA,CAAAI,MAAA,gCACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,eAA4B;IAExBD,EAAA,CAAAK,UAAA,mBAAAW,8DAAA;MAAAhB,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAS,MAAA,GAAAjB,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAM,MAAA,CAAAC,oBAAA,EAAsB;IAAA,EAAC;IAGhClB,EAAA,CAAAE,SAAA,aAAsC;IACtCF,EAAA,CAAAI,MAAA,gBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAGC;IAFCD,EAAA,CAAAK,UAAA,mBAAAc,8DAAA;MAAAnB,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAY,MAAA,GAAApB,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAS,MAAA,CAAAC,YAAA,EAAc;IAAA,EAAC;IAGxBrB,EAAA,CAAAE,SAAA,aAA4B;IAC9BF,EAAA,CAAAG,YAAA,EAAS;IASrBH,EAAA,CAAAC,cAAA,eAA2B;IAEzBD,EAAA,CAAAE,SAAA,eAAwI;IAGxIF,EAAA,CAAAC,cAAA,eAA0K;IAKlKD,EAAA,CAAAE,SAAA,aAA2C;IAC7CF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAmC;IAAAD,EAAA,CAAAI,MAAA,qBAAQ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAChDH,EAAA,CAAAC,cAAA,aAA6C;IAAAD,EAAA,CAAAI,MAAA,oCAAuB;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAI1EH,EAAA,CAAAC,cAAA,eAA+B;IAEsCD,EAAA,CAAAI,MAAA,mBAAW;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACjFH,EAAA,CAAAC,cAAA,gBAAiI;IAC/HD,EAAA,CAAAE,SAAA,aAAuC;IACvCF,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAGTH,EAAA,CAAAC,cAAA,eAAkH;IAE9GD,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAINH,EAAA,CAAAC,cAAA,eAAkC;IAE9BD,EAAA,CAAAE,SAAA,aAAiC;IACjCF,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,gBAAwF;IACtFD,EAAA,CAAAE,SAAA,aAA0C;IAC1CF,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,gBAAwF;IACtFD,EAAA,CAAAE,SAAA,aAA2C;IAC3CF,EAAA,CAAAI,MAAA,2BACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAQjBH,EAAA,CAAAC,cAAA,eAA2B;IAEzBD,EAAA,CAAAE,SAAA,eAAwI;IAGxIF,EAAA,CAAAC,cAAA,eAA0K;IAMhKD,EAAA,CAAAE,SAAA,cAA+C;IACjDF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,YAAK;IACsCD,EAAA,CAAAI,MAAA,4BAAmB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACjEH,EAAA,CAAAC,cAAA,cAAiC;IAAAD,EAAA,CAAAI,MAAA,yDAAiC;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAG1EH,EAAA,CAAAC,cAAA,iBAAgF;IAC9ED,EAAA,CAAAE,SAAA,cAAiC;IACjCF,EAAA,CAAAI,MAAA,aACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAKXH,EAAA,CAAAC,cAAA,gBAAiB;IACfD,EAAA,CAAAE,SAAA,wBAA2C;IAC7CF,EAAA,CAAAG,YAAA,EAAM;;;;IAnJkCH,EAAA,CAAAsB,SAAA,IAAiB;IAAjBtB,EAAA,CAAAuB,iBAAA,CAAAC,MAAA,CAAAC,MAAA,CAAAC,IAAA,CAAiB;IAOlB1B,EAAA,CAAAsB,SAAA,GAAiC;IAAjCtB,EAAA,CAAAuB,iBAAA,EAAAC,MAAA,CAAAC,MAAA,CAAAE,OAAA,kBAAAH,MAAA,CAAAC,MAAA,CAAAE,OAAA,CAAAC,MAAA,OAAiC;IAUjC5B,EAAA,CAAAsB,SAAA,IAAkC;IAAlCtB,EAAA,CAAAuB,iBAAA,CAAAC,MAAA,CAAAK,UAAA,CAAAL,MAAA,CAAAC,MAAA,CAAAK,SAAA,EAAkC;IAuEjE9B,EAAA,CAAAsB,SAAA,IACF;IADEtB,EAAA,CAAA+B,kBAAA,aAAAP,MAAA,CAAAC,MAAA,CAAAO,KAAA,GAAAR,MAAA,CAAAS,WAAA,CAAAT,MAAA,CAAAC,MAAA,CAAAO,KAAA,KAAAR,MAAA,CAAAC,MAAA,CAAAO,KAAA,0BACF;IAKEhC,EAAA,CAAAsB,SAAA,GACF;IADEtB,EAAA,CAAA+B,kBAAA,MAAAP,MAAA,CAAAC,MAAA,CAAAS,WAAA,iEACF;IAOElC,EAAA,CAAAsB,SAAA,GACF;IADEtB,EAAA,CAAA+B,kBAAA,OAAAP,MAAA,CAAAC,MAAA,CAAAE,OAAA,kBAAAH,MAAA,CAAAC,MAAA,CAAAE,OAAA,CAAAC,MAAA,oBACF;IAGE5B,EAAA,CAAAsB,SAAA,GACF;IADEtB,EAAA,CAAA+B,kBAAA,yBAAAP,MAAA,CAAAK,UAAA,CAAAL,MAAA,CAAAC,MAAA,CAAAK,SAAA,OACF;IAuCS9B,EAAA,CAAAsB,SAAA,IAAe;IAAftB,EAAA,CAAAmC,UAAA,SAAAX,MAAA,CAAAC,MAAA,CAAe;;;;;;IAQtCzB,EAAA,CAAAC,cAAA,cAA0G;IAGpGD,EAAA,CAAAE,SAAA,YAAoD;IACtDF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,aAA0E;IACxED,EAAA,CAAAI,MAAA,qCACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,YAA2D;IACzDD,EAAA,CAAAI,MAAA,iEACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAC,cAAA,iBAGC;IAFCD,EAAA,CAAAK,UAAA,mBAAA+B,6DAAA;MAAApC,EAAA,CAAAO,aAAA,CAAA8B,GAAA;MAAA,MAAAC,MAAA,GAAAtC,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAA2B,MAAA,CAAApB,oBAAA,EAAsB;IAAA,EAAC;IAGhClB,EAAA,CAAAE,SAAA,YAAsC;IACtCF,EAAA,CAAAI,MAAA,iDACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;ADtMb,OAAM,MAAOoC,qBAAqB;EAUhCC,YACUC,aAA4B,EAC5BC,WAAwB;EAAE;EAC1BC,KAAqB,EACrBC,MAAc;IAHd,KAAAH,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IAbhB,KAAAnB,MAAM,GAAkB,IAAI;IAC5B,KAAAoB,OAAO,GAAG,KAAK;IACf,KAAAC,KAAK,GAAkB,IAAI;IAC3B,KAAAC,QAAQ,GAAkB,IAAI;IAC9B,KAAAC,SAAS,GAAQ;MAAEC,EAAE,EAAE,EAAE;MAAEC,IAAI,EAAE;IAAQ,CAAE;IAC3C,KAAAC,cAAc,GAAW,EAAE;IAC3B,KAAAC,WAAW,GAA8B,EAAE,CAAC,CAAC;IAC7C,KAAAC,WAAW,GAAU,EAAE,CAAC,CAAC;EAOtB;;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACP,QAAQ,GAAG,IAAI,CAACJ,KAAK,CAACY,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC;IAEtD;IACA,IAAI,CAACC,SAAS,EAAE;IAEhB,IAAI,IAAI,CAACX,QAAQ,EAAE;MACjB,IAAI,CAACY,UAAU,CAAC,IAAI,CAACZ,QAAQ,CAAC;KAC/B,MAAM;MACL,IAAI,CAACD,KAAK,GAAG,0BAA0B;;EAE3C;EAEA;EACAY,SAASA,CAAA;IACP;IACA;IACA,MAAME,SAAS,GAAW,CACxB;MACEC,GAAG,EAAE,OAAO;MACZC,QAAQ,EAAE,UAAU;MACpBC,KAAK,EAAE,kBAAkB;MACzBb,IAAI,EAAE,OAAO;MACbc,QAAQ,EAAE;KACX,EACD;MACEH,GAAG,EAAE,OAAO;MACZC,QAAQ,EAAE,YAAY;MACtBC,KAAK,EAAE,kBAAkB;MACzBb,IAAI,EAAE,SAAS;MACfc,QAAQ,EAAE;KACX,EACD;MACEH,GAAG,EAAE,OAAO;MACZC,QAAQ,EAAE,YAAY;MACtBC,KAAK,EAAE,iBAAiB;MACxBb,IAAI,EAAE,SAAS;MACfc,QAAQ,EAAE;KACX,CACF;IAED;IACAC,UAAU,CAAC,MAAK;MACd;MACA,MAAMC,QAAQ,GAAG,CAAC,GAAGN,SAAS,CAAC;MAC/BO,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEF,QAAQ,CAAC;MAE9D;MACA,IAAI,IAAI,CAACb,WAAW,IAAI,IAAI,CAACA,WAAW,CAACzB,MAAM,GAAG,CAAC,EAAE;QACnD,MAAMyC,aAAa,GAAG,IAAI,CAAChB,WAAW,CAACiB,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACC,IAAI,CAAC;QACzD,IAAI,CAACrB,cAAc,GAAGS,SAAS,CAACa,MAAM,CACnCD,IAAI,IAAK,CAACH,aAAa,CAACK,QAAQ,CAACF,IAAI,CAACX,GAAG,IAAIW,IAAI,CAACvB,EAAE,IAAI,EAAE,CAAC,CAC7D;OACF,MAAM;QACL,IAAI,CAACE,cAAc,GAAGS,SAAS;;MAGjCO,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE,IAAI,CAACjB,cAAc,CAAC;MAE7D;MACA,IAAI,IAAI,CAAC1B,MAAM,IAAI,IAAI,CAACA,MAAM,CAACE,OAAO,EAAE;QACtC,IAAI,CAACgD,iBAAiB,EAAE;;IAE5B,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;EACAA,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAAClD,MAAM,IAAI,CAAC,IAAI,CAACA,MAAM,CAACE,OAAO,EAAE;IAE1C,IAAI,CAACF,MAAM,CAACE,OAAO,CAACiD,OAAO,CAAEC,QAAQ,IAAI;MACvC,MAAML,IAAI,GAAG,IAAI,CAACrB,cAAc,CAAC2B,IAAI,CAClCC,CAAC,IAAKA,CAAC,CAAClB,GAAG,KAAKgB,QAAQ,IAAIE,CAAC,CAAC9B,EAAE,KAAK4B,QAAQ,CAC/C;MACD,IAAIL,IAAI,IAAIA,IAAI,CAAC9C,IAAI,EAAE;QACrB,IAAI,CAAC0B,WAAW,CAACyB,QAAQ,CAAC,GAAGL,IAAI,CAAC9C,IAAI;OACvC,MAAM;QACL;QACA;QACA;QACA,IAAI,CAAC0B,WAAW,CAACyB,QAAQ,CAAC,GAAGA,QAAQ;;IAEzC,CAAC,CAAC;EACJ;EAEA;EACAG,aAAaA,CAACH,QAAgB;IAC5B,OAAO,IAAI,CAACzB,WAAW,CAACyB,QAAQ,CAAC,IAAIA,QAAQ;EAC/C;EAEA;EACA5C,WAAWA,CAACgD,MAA0B;IACpC,IAAI,CAACA,MAAM,EAAE;MACX,OAAO,YAAY;;IAGrB,MAAMT,IAAI,GAAG,IAAI,CAACrB,cAAc,CAAC2B,IAAI,CAClCC,CAAC,IAAKA,CAAC,CAAClB,GAAG,KAAKoB,MAAM,IAAIF,CAAC,CAAC9B,EAAE,KAAKgC,MAAM,CAC3C;IACD,IAAIT,IAAI,EAAE;MACR,IAAIA,IAAI,CAACU,SAAS,IAAIV,IAAI,CAACW,QAAQ,EAAE;QACnC,OAAO,GAAGX,IAAI,CAACU,SAAS,IAAIV,IAAI,CAACW,QAAQ,EAAE;OAC5C,MAAM,IAAIX,IAAI,CAAC9C,IAAI,EAAE;QACpB,OAAO8C,IAAI,CAAC9C,IAAI;;;IAGpB,OAAOuD,MAAM;EACf;EAEA;EACAG,iBAAiBA,CAACH,MAA0B;IAC1C,IAAI,CAACA,MAAM,EAAE;MACX,OAAO,EAAE;;IAGX,MAAMT,IAAI,GAAG,IAAI,CAACrB,cAAc,CAAC2B,IAAI,CAClCC,CAAC,IAAKA,CAAC,CAAClB,GAAG,KAAKoB,MAAM,IAAIF,CAAC,CAAC9B,EAAE,KAAKgC,MAAM,CAC3C;IACD,IAAIT,IAAI,EAAE;MACR,OAAOA,IAAI,CAACa,UAAU,IAAIb,IAAI,CAACtB,IAAI,IAAI,EAAE;;IAE3C,OAAO,EAAE;EACX;EAEAS,UAAUA,CAACV,EAAU;IACnB,IAAI,CAACJ,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,KAAK,GAAG,IAAI;IAEjB,IAAI,CAACL,aAAa,CAAC6C,SAAS,CAACrC,EAAE,CAAC,CAACsC,SAAS,CAAC;MACzCC,IAAI,EAAGC,IAAI,IAAI;QACbtB,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEqB,IAAI,CAAC;QACjD,IAAI,CAAChE,MAAM,GAAGgE,IAAI;QAElB;QACA,IAAI,CAACC,eAAe,CAACzC,EAAE,CAAC;QAExB;QACA,IACE,IAAI,CAACxB,MAAM,IACX,IAAI,CAACA,MAAM,CAACE,OAAO,IACnB,IAAI,CAACF,MAAM,CAACE,OAAO,CAACC,MAAM,GAAG,CAAC,EAC9B;UACA,IAAI,CAAC+C,iBAAiB,EAAE;;QAG1B,IAAI,CAAC9B,OAAO,GAAG,KAAK;MACtB,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfqB,OAAO,CAACrB,KAAK,CACX,oDAAoD,EACpDA,KAAK,CACN;QACD,IAAI,CAACA,KAAK,GACR,8EAA8E;QAChF,IAAI,CAACD,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEA;EACA6C,eAAeA,CAACC,MAAc;IAC5B,IAAI,CAAClD,aAAa,CAACmD,cAAc,CAACD,MAAM,CAAC,CAACJ,SAAS,CAAC;MAClDC,IAAI,EAAG7D,OAAO,IAAI;QAChBwC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEzC,OAAO,CAAC;QACpD,IAAI,CAAC0B,WAAW,GAAG1B,OAAO;MAC5B,CAAC;MACDmB,KAAK,EAAGA,KAAK,IAAI;QACfqB,OAAO,CAACrB,KAAK,CACX,oDAAoD,EACpDA,KAAK,CACN;MACH;KACD,CAAC;EACJ;EAEA/B,oBAAoBA,CAAA;IAClB,IAAI,IAAI,CAACgC,QAAQ,EAAE;MACjB,IAAI,CAACH,MAAM,CAACiD,QAAQ,CAAC,CAAC,mBAAmB,EAAE,IAAI,CAAC9C,QAAQ,CAAC,CAAC;;EAE9D;EAEA7B,oBAAoBA,CAAA;IAClB,IAAI,CAAC0B,MAAM,CAACiD,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;EAC1C;EAEAjF,eAAeA,CAAA;IACb,IAAI,IAAI,CAACmC,QAAQ,EAAE;MACjB,IAAI,CAACH,MAAM,CAACiD,QAAQ,CAAC,CAAC,gBAAgB,EAAE,IAAI,CAAC9C,QAAQ,CAAC,CAAC;;EAE3D;EAEA;EACAlB,UAAUA,CAACiE,IAA+B;IACxC,IAAI,CAACA,IAAI,EAAE;MACT,OAAO,KAAK;;IAGd,IAAI;MACF,IAAIC,OAAa;MAEjB,IAAI,OAAOD,IAAI,KAAK,QAAQ,EAAE;QAC5BC,OAAO,GAAG,IAAIC,IAAI,CAACF,IAAI,CAAC;OACzB,MAAM;QACLC,OAAO,GAAGD,IAAI;;MAGhB,IAAIG,KAAK,CAACF,OAAO,CAACG,OAAO,EAAE,CAAC,EAAE;QAC5B,OAAO,eAAe;;MAGxB;MACA,OAAOH,OAAO,CAACI,kBAAkB,CAAC,OAAO,EAAE;QACzCC,GAAG,EAAE,SAAS;QACdC,KAAK,EAAE,SAAS;QAChBC,IAAI,EAAE;OACP,CAAC;KACH,CAAC,OAAOxD,KAAK,EAAE;MACdqB,OAAO,CAACrB,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D,OAAO,gBAAgB;;EAE3B;EAEA;EACAyD,SAASA,CAACtB,MAAc,EAAE/B,IAAY;IACpCiB,OAAO,CAACC,GAAG,CAAC,0BAA0Ba,MAAM,iBAAiB/B,IAAI,EAAE,CAAC;IAEpE,IAAI,CAAC,IAAI,CAACH,QAAQ,IAAI,CAACkC,MAAM,EAAE;MAC7Bd,OAAO,CAACrB,KAAK,CAAC,0CAA0C,CAAC;MACzD,IAAI,CAACA,KAAK,GAAG,0CAA0C;MACvD;;IAGF;IACA,MAAM0D,eAAe,GAAG,IAAI,CAACnD,WAAW,CAACoD,IAAI,CAAElC,CAAC,IAAKA,CAAC,CAACC,IAAI,KAAKS,MAAM,CAAC;IACvE,IAAIuB,eAAe,EAAE;MACnB,IAAI,CAAC1D,KAAK,GAAG,6CAA6C;MAC1D4D,KAAK,CAAC,6CAA6C,CAAC;MACpD;;IAGF;IACA,MAAMC,MAAM,GAAW;MACrB1D,EAAE,EAAEgC,MAAM;MACV/B,IAAI,EAAEA,IAAI,IAAI;KACf;IAED;IACA,MAAM0D,QAAQ,GAAG,IAAI,CAAC3E,WAAW,CAACgD,MAAM,CAAC;IACzC,MAAM4B,QAAQ,GAAG3D,IAAI,KAAK,OAAO,GAAG,gBAAgB,GAAG,QAAQ;IAE/D,IAAI,CAACT,aAAa,CAACqE,iBAAiB,CAAC,IAAI,CAAC/D,QAAQ,EAAE4D,MAAM,CAAC,CAACpB,SAAS,CAAC;MACpEC,IAAI,EAAGuB,QAAQ,IAAI;QACjB5C,OAAO,CAACC,GAAG,CACT,gBAAgBwC,QAAQ,kBAAkBC,QAAQ,eAAe,EACjEE,QAAQ,CACT;QAED;QACAL,KAAK,CAAC,gBAAgBE,QAAQ,kBAAkBC,QAAQ,cAAc,CAAC;QAEvE;QACA,IAAI,CAACnB,eAAe,CAAC,IAAI,CAAC3C,QAAS,CAAC;QAEpC;QACA,IAAI,CAACY,UAAU,CAAC,IAAI,CAACZ,QAAS,CAAC;QAE/B;QACA,IAAI,CAACiE,oBAAoB,EAAE;MAC7B,CAAC;MACDlE,KAAK,EAAGA,KAAK,IAAI;QACfqB,OAAO,CAACrB,KAAK,CACX,uDAAuD,EACvDA,KAAK,CACN;QACD,IAAI,CAACA,KAAK,GAAG,uCAAuC8D,QAAQ,WAAWC,QAAQ,iCAAiC;QAChHH,KAAK,CAAC,IAAI,CAAC5D,KAAK,CAAC;MACnB;KACD,CAAC;EACJ;EAEA;EACAkE,oBAAoBA,CAAA;IAClB;IACA;IACA,IAAI,CAACtD,SAAS,EAAE;EAClB;EAEA;EACAoD,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAAC/D,QAAQ,IAAI,CAAC,IAAI,CAACC,SAAS,CAACC,EAAE,EAAE;MACxCkB,OAAO,CAACrB,KAAK,CAAC,sCAAsC,CAAC;MACrD;;IAGF,IAAI,CAACyD,SAAS,CAAC,IAAI,CAACvD,SAAS,CAACC,EAAE,EAAE,IAAI,CAACD,SAAS,CAACE,IAAI,IAAI,QAAQ,CAAC;EACpE;EAEA+D,sBAAsBA,CAACpC,QAAgB;IACrCV,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAES,QAAQ,CAAC;IAExE,IAAI,CAAC,IAAI,CAAC9B,QAAQ,EAAE;MAClBoB,OAAO,CAACrB,KAAK,CAAC,sBAAsB,CAAC;MACrC,IAAI,CAACA,KAAK,GAAG,wDAAwD;MACrE;;IAGF;IACA,MAAMmC,MAAM,GAAGJ,QAAQ;IAEvB;IACA,MAAM+B,QAAQ,GAAG,IAAI,CAAC3E,WAAW,CAACgD,MAAM,CAAC;IAEzCd,OAAO,CAACC,GAAG,CACT,yCAAyCa,MAAM,KAAK2B,QAAQ,iBAAiB,IAAI,CAAC7D,QAAQ,EAAE,CAC7F;IAED,IACEmE,OAAO,CACL,mDAAmDN,QAAQ,gBAAgB,CAC5E,EACD;MACAzC,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;MAE7D,IAAI,CAACvB,OAAO,GAAG,IAAI;MACnB,IAAI,CAACC,KAAK,GAAG,IAAI;MAEjB,IAAI,CAACL,aAAa,CACfwE,sBAAsB,CAAC,IAAI,CAAClE,QAAQ,EAAEkC,MAAM,CAAC,CAC7CM,SAAS,CAAC;QACTC,IAAI,EAAGuB,QAAQ,IAAI;UACjB5C,OAAO,CAACC,GAAG,CACT,gBAAgBwC,QAAQ,mCAAmC,EAC3DG,QAAQ,CACT;UACD,IAAI,CAAClE,OAAO,GAAG,KAAK;UAEpB;UACA6D,KAAK,CAAC,gBAAgBE,QAAQ,kCAAkC,CAAC;UAEjE;UACA,IAAI,CAAClB,eAAe,CAAC,IAAI,CAAC3C,QAAS,CAAC;UAEpC;UACA,IAAI,CAACY,UAAU,CAAC,IAAI,CAACZ,QAAS,CAAC;UAE/B;UACA,IAAI,CAACiE,oBAAoB,EAAE;QAC7B,CAAC;QACDlE,KAAK,EAAGA,KAAK,IAAI;UACfqB,OAAO,CAACrB,KAAK,CACX,4CAA4C8D,QAAQ,IAAI,EACxD9D,KAAK,CACN;UACD,IAAI,CAACD,OAAO,GAAG,KAAK;UACpB,IAAI,CAACC,KAAK,GAAG,wCAAwC8D,QAAQ,kBAC3D9D,KAAK,CAACqE,OAAO,IAAI,iBACnB,EAAE;QACJ;OACD,CAAC;KACL,MAAM;MACLhD,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;;EAExD;EAEA/C,YAAYA,CAAA;IACV8C,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;IAE3C,IAAI,CAAC,IAAI,CAACrB,QAAQ,EAAE;MAClBoB,OAAO,CAACrB,KAAK,CAAC,sBAAsB,CAAC;MACrC,IAAI,CAACA,KAAK,GAAG,yDAAyD;MACtE;;IAGFqB,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE,IAAI,CAACrB,QAAQ,CAAC;IAEzD,IACEmE,OAAO,CACL,gDAAgD,IAAI,CAACzF,MAAM,EAAEC,IAAI,mCAAmC,CACrG,EACD;MACAyC,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;MAE7D,IAAI,CAACvB,OAAO,GAAG,IAAI;MACnB,IAAI,CAACC,KAAK,GAAG,IAAI;MAEjB,IAAI,CAACL,aAAa,CAACpB,YAAY,CAAC,IAAI,CAAC0B,QAAQ,CAAC,CAACwC,SAAS,CAAC;QACvDC,IAAI,EAAEA,CAAA,KAAK;UACTrB,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;UAC3C,IAAI,CAACvB,OAAO,GAAG,KAAK;UACpB6D,KAAK,CAAC,8BAA8B,CAAC;UACrC,IAAI,CAAC9D,MAAM,CAACiD,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;QAC1C,CAAC;QACD/C,KAAK,EAAGA,KAAK,IAAI;UACfqB,OAAO,CAACrB,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;UAClE,IAAI,CAACD,OAAO,GAAG,KAAK;UACpB,IAAI,CAACC,KAAK,GAAG,qCACXA,KAAK,CAACqE,OAAO,IAAI,iBACnB,EAAE;UACFT,KAAK,CAAC,kCAAkC,IAAI,CAAC5D,KAAK,EAAE,CAAC;QACvD;OACD,CAAC;KACH,MAAM;MACLqB,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;;EAExD;;;uBAraW7B,qBAAqB,EAAAvC,EAAA,CAAAoH,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAAtH,EAAA,CAAAoH,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAxH,EAAA,CAAAoH,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAA1H,EAAA,CAAAoH,iBAAA,CAAAK,EAAA,CAAAE,MAAA;IAAA;EAAA;;;YAArBpF,qBAAqB;MAAAqF,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZlClI,EAAA,CAAAoI,UAAA,IAAAC,oCAAA,mBA8LM;UAGNrI,EAAA,CAAAoI,UAAA,IAAAE,oCAAA,kBAmBM;;;UApN6EtI,EAAA,CAAAmC,UAAA,SAAAgG,GAAA,CAAA1G,MAAA,CAAY;UAiMJzB,EAAA,CAAAsB,SAAA,GAAa;UAAbtB,EAAA,CAAAmC,UAAA,UAAAgG,GAAA,CAAA1G,MAAA,CAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}