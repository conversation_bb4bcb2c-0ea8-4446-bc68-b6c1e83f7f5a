{"name": "frontend", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "lint": "ng lint"}, "private": true, "dependencies": {"@angular/animations": "^16.2.0", "@angular/cdk": "^16.2.12", "@angular/common": "^16.2.0", "@angular/compiler": "^16.2.0", "@angular/core": "^16.2.0", "@angular/forms": "^16.2.0", "@angular/material": "^16.2.12", "@angular/platform-browser": "^16.2.0", "@angular/platform-browser-dynamic": "^16.2.0", "@angular/router": "^16.2.0", "@auth0/angular-jwt": "^5.2.0", "@fortawesome/fontawesome-free": "^6.7.2", "@google/generative-ai": "^0.24.1", "angular-calendar": "^0.31.1", "apollo-angular": "^5.0.2", "apollo-upload-client": "^17.0.0", "bootstrap": "^5.3.6", "datatables.net-bs4": "^2.3.0", "date-fns": "^4.1.0", "graphql": "16.8.1", "graphql-ws": "5.14.0", "lightbox2": "^2.11.5", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.13.0"}, "devDependencies": {"@angular-builders/custom-webpack": "^16.0.1", "@angular-devkit/build-angular": "^16.2.0", "@angular/cli": "^16.2.0", "@angular/compiler-cli": "^16.2.0", "@types/jasmine": "~4.3.0", "autoprefixer": "^10.4.21", "jasmine-core": "~4.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "patch-package": "^8.0.0", "postcss": "^8.5.3", "postinstall-postinstall": "^2.1.0", "tailwindcss": "^3.4.17", "typescript": "5.1.6"}}