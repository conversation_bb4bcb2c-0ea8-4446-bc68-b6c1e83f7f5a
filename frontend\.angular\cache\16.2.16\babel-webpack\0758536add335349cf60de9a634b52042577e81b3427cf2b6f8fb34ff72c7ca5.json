{"ast": null, "code": "import { GoogleGenerativeAI } from '@google/generative-ai';\nimport { Observable, from, of } from 'rxjs';\nimport { map, catchError } from 'rxjs/operators';\nimport { environment } from '../../environments/environment';\nimport * as i0 from \"@angular/core\";\nexport let AiService = /*#__PURE__*/(() => {\n  class AiService {\n    constructor() {\n      this.apiAvailable = true;\n      // Utiliser une clé API de l'environnement ou une clé de démonstration\n      // Pour une utilisation en production, obtenez une clé API sur https://makersuite.google.com/\n      const apiKey = environment.geminiApiKey || 'AIzaSyDCXc16FzaVWSJkW4RGboTZ8AD9_PTDL88';\n      try {\n        // Initialiser l'API Gemini\n        this.genAI = new GoogleGenerativeAI(apiKey);\n        // Utiliser le modèle gemini-1.5-pro qui est disponible dans la version actuelle de l'API\n        this.model = this.genAI.getGenerativeModel({\n          model: 'gemini-1.5-pro'\n        });\n        console.log('Service AI initialisé avec succès');\n      } catch (error) {\n        console.error('Erreur lors de l\\'initialisation du service AI:', error);\n        // Créer des objets factices pour éviter les erreurs null\n        // Ces objets ne feront rien mais éviteront les erreurs d'exécution\n        this.genAI = {};\n        this.model = {\n          generateContent: () => Promise.resolve({\n            response: {\n              text: () => 'Service AI non disponible'\n            }\n          })\n        };\n        // Marquer l'API comme non disponible\n        this.apiAvailable = false;\n      }\n    }\n    /**\n     * Génère des tâches pour un projet en fonction du nombre de membres\n     * @param projectTitle Titre du projet\n     * @param memberCount Nombre de membres dans l'équipe\n     * @param teamMembers Liste des membres de l'équipe (optionnel)\n     * @returns Observable contenant les tâches générées\n     */\n    generateProjectTasks(projectTitle, memberCount, teamMembers) {\n      // Si le nombre de membres est trop petit, utiliser au moins 3 entités\n      const effectiveMemberCount = Math.max(memberCount, 3);\n      // Données de démonstration à utiliser en cas d'erreur ou si l'API n'est pas disponible\n      const fallbackData = this.createFallbackTaskData(projectTitle, effectiveMemberCount, teamMembers);\n      // Si nous savons déjà que l'API n'est pas disponible, retourner directement les données de démonstration\n      if (!this.isApiAvailable()) {\n        console.log('API Gemini non disponible, utilisation des données de démonstration');\n        return new Observable(observer => {\n          setTimeout(() => {\n            observer.next(fallbackData);\n            observer.complete();\n          }, 1000); // Simuler un délai d'API\n        });\n      }\n      // Préparer les informations sur les membres de l'équipe pour le prompt\n      let teamMembersInfo = '';\n      if (teamMembers && teamMembers.length > 0) {\n        teamMembersInfo = `\n      Membres de l'équipe:\n      ${teamMembers.map((member, index) => {\n          const memberName = member.name || member.firstName || member.lastName || `Membre ${index + 1}`;\n          const memberRole = member.role || 'membre';\n          return `- ${memberName} (${memberRole})`;\n        }).join('\\n')}\n      `;\n      }\n      const prompt = `\n      Agis comme un expert en gestion de projet. Je travaille sur un projet intitulé \"${projectTitle}\"\n      avec une équipe de ${effectiveMemberCount} membres.\n      ${teamMembersInfo}\n\n      Divise ce projet en exactement ${effectiveMemberCount} entités ou modules principaux qui peuvent être travaillés en parallèle par chaque membre de l'équipe.\n\n      IMPORTANT: Chaque entité doit être simple, claire et concise (maximum 3-4 mots).\n      Exemples d'entités pour un site e-commerce avec 3 membres:\n      - CRUD des produits\n      - Interface utilisateur\n      - Déploiement\n\n      Pour chaque entité/module:\n      1. Donne un nom très court et concis (maximum 3-4 mots)\n      2. Fournis une brève description (1 phrase maximum)\n      3. Liste 2-3 tâches spécifiques avec leur priorité (haute, moyenne, basse)\n\n      Réponds au format JSON suivant sans aucun texte supplémentaire:\n      {\n        \"projectTitle\": \"${projectTitle}\",\n        \"entities\": [\n          {\n            \"name\": \"Nom court de l'entité\",\n            \"description\": \"Description très brève de l'entité\",\n            \"assignedTo\": \"Nom du membre (optionnel)\",\n            \"tasks\": [\n              {\n                \"title\": \"Titre court de la tâche\",\n                \"description\": \"Description brève de la tâche\",\n                \"priority\": \"high|medium|low\",\n                \"status\": \"todo\"\n              }\n            ]\n          }\n        ]\n      }\n    `;\n      try {\n        return from(this.model.generateContent(prompt)).pipe(map(result => {\n          try {\n            const textResult = result.response.text();\n            // Extraire le JSON de la réponse\n            const jsonMatch = textResult.match(/\\{[\\s\\S]*\\}/);\n            if (jsonMatch) {\n              return JSON.parse(jsonMatch[0]);\n            } else {\n              console.warn('Format JSON non trouvé dans la réponse, utilisation des données de démonstration');\n              return fallbackData;\n            }\n          } catch (error) {\n            console.error('Erreur lors du parsing de la réponse:', error);\n            return fallbackData;\n          }\n        }),\n        // Capturer les erreurs au niveau de l'Observable\n        catchError(error => {\n          console.error('Erreur lors de la génération de contenu:', error);\n          // Marquer l'API comme non disponible pour les prochains appels\n          this.markApiAsUnavailable();\n          return of(fallbackData);\n        }));\n      } catch (error) {\n        console.error('Erreur lors de l\\'appel à l\\'API:', error);\n        this.markApiAsUnavailable();\n        return of(fallbackData);\n      }\n    }\n    // Méthode pour créer des données de démonstration\n    createFallbackTaskData(projectTitle, memberCount, teamMembers) {\n      // Préparer les informations sur les membres pour l'assignation\n      const memberNames = [];\n      if (teamMembers && teamMembers.length > 0) {\n        teamMembers.forEach((member, index) => {\n          const memberName = member.name || member.firstName || (member.firstName && member.lastName ? `${member.firstName} ${member.lastName}` : null) || `Membre ${index + 1}`;\n          memberNames.push(memberName);\n        });\n      }\n      // Si pas assez de noms de membres, compléter avec des noms génériques\n      while (memberNames.length < memberCount) {\n        memberNames.push(`Membre ${memberNames.length + 1}`);\n      }\n      // Données de démonstration pour un site e-commerce\n      if (projectTitle.toLowerCase().includes('ecommerce') || projectTitle.toLowerCase().includes('e-commerce') || projectTitle.toLowerCase().includes('boutique')) {\n        const ecommerceEntities = [{\n          name: \"CRUD des produits\",\n          description: \"Gestion des produits dans la base de données\",\n          assignedTo: memberNames[0] || \"Non assigné\",\n          tasks: [{\n            title: \"Créer API produits\",\n            description: \"Développer les endpoints pour créer, lire, modifier et supprimer des produits\",\n            priority: \"high\",\n            status: \"todo\"\n          }, {\n            title: \"Modèle de données\",\n            description: \"Concevoir le schéma de la base de données pour les produits\",\n            priority: \"medium\",\n            status: \"todo\"\n          }]\n        }, {\n          name: \"Interface utilisateur\",\n          description: \"Développement du frontend de l'application\",\n          assignedTo: memberNames[1] || \"Non assigné\",\n          tasks: [{\n            title: \"Page d'accueil\",\n            description: \"Créer la page d'accueil avec la liste des produits\",\n            priority: \"high\",\n            status: \"todo\"\n          }, {\n            title: \"Panier d'achat\",\n            description: \"Implémenter la fonctionnalité du panier d'achat\",\n            priority: \"medium\",\n            status: \"todo\"\n          }]\n        }, {\n          name: \"Déploiement\",\n          description: \"Mise en production de l'application\",\n          assignedTo: memberNames[2] || \"Non assigné\",\n          tasks: [{\n            title: \"Configuration serveur\",\n            description: \"Configurer le serveur pour l'hébergement\",\n            priority: \"medium\",\n            status: \"todo\"\n          }, {\n            title: \"Tests d'intégration\",\n            description: \"Effectuer des tests d'intégration avant le déploiement\",\n            priority: \"high\",\n            status: \"todo\"\n          }]\n        }, {\n          name: \"Gestion utilisateurs\",\n          description: \"Système d'authentification et profils\",\n          assignedTo: memberNames[3] || \"Non assigné\",\n          tasks: [{\n            title: \"Authentification\",\n            description: \"Implémenter le système de connexion et d'inscription\",\n            priority: \"high\",\n            status: \"todo\"\n          }, {\n            title: \"Profils utilisateurs\",\n            description: \"Créer les pages de profil et de gestion des informations personnelles\",\n            priority: \"medium\",\n            status: \"todo\"\n          }]\n        }, {\n          name: \"Paiement en ligne\",\n          description: \"Intégration des systèmes de paiement\",\n          assignedTo: memberNames[4] || \"Non assigné\",\n          tasks: [{\n            title: \"API de paiement\",\n            description: \"Intégrer une passerelle de paiement comme Stripe ou PayPal\",\n            priority: \"high\",\n            status: \"todo\"\n          }, {\n            title: \"Sécurité transactions\",\n            description: \"Mettre en place les mesures de sécurité pour les transactions\",\n            priority: \"high\",\n            status: \"todo\"\n          }]\n        }, {\n          name: \"SEO & Analytics\",\n          description: \"Optimisation pour les moteurs de recherche\",\n          assignedTo: memberNames[5] || \"Non assigné\",\n          tasks: [{\n            title: \"Balises méta\",\n            description: \"Optimiser les balises méta et la structure du site\",\n            priority: \"medium\",\n            status: \"todo\"\n          }, {\n            title: \"Google Analytics\",\n            description: \"Intégrer des outils d'analyse du trafic\",\n            priority: \"low\",\n            status: \"todo\"\n          }]\n        }];\n        // Limiter au nombre de membres\n        return {\n          projectTitle: projectTitle,\n          entities: ecommerceEntities.slice(0, memberCount)\n        };\n      }\n      // Données génériques pour tout autre type de projet\n      const moduleTypes = [{\n        name: \"Backend\",\n        description: \"Développement du backend de l'application\"\n      }, {\n        name: \"Frontend\",\n        description: \"Développement de l'interface utilisateur\"\n      }, {\n        name: \"Base de données\",\n        description: \"Conception et gestion de la base de données\"\n      }, {\n        name: \"Tests\",\n        description: \"Tests et assurance qualité\"\n      }, {\n        name: \"Déploiement\",\n        description: \"Configuration et déploiement de l'application\"\n      }, {\n        name: \"Documentation\",\n        description: \"Rédaction de la documentation technique\"\n      }];\n      return {\n        projectTitle: projectTitle,\n        entities: Array.from({\n          length: memberCount\n        }, (_, i) => ({\n          name: moduleTypes[i % moduleTypes.length].name,\n          description: moduleTypes[i % moduleTypes.length].description,\n          assignedTo: memberNames[i] || \"Non assigné\",\n          tasks: [{\n            title: `Conception ${moduleTypes[i % moduleTypes.length].name}`,\n            description: `Planifier l'architecture du ${moduleTypes[i % moduleTypes.length].name.toLowerCase()}`,\n            priority: 'high',\n            status: 'todo'\n          }, {\n            title: `Implémentation ${moduleTypes[i % moduleTypes.length].name}`,\n            description: `Développer les fonctionnalités du ${moduleTypes[i % moduleTypes.length].name.toLowerCase()}`,\n            priority: 'medium',\n            status: 'todo'\n          }, {\n            title: `Tests ${moduleTypes[i % moduleTypes.length].name}`,\n            description: `Tester les fonctionnalités du ${moduleTypes[i % moduleTypes.length].name.toLowerCase()}`,\n            priority: 'medium',\n            status: 'todo'\n          }]\n        }))\n      };\n    }\n    // Méthode pour vérifier si l'API est disponible\n    isApiAvailable() {\n      return this.apiAvailable;\n    }\n    // Méthode pour marquer l'API comme non disponible\n    markApiAsUnavailable() {\n      this.apiAvailable = false;\n      console.warn('API Gemini marquée comme non disponible pour les prochains appels');\n    }\n    /**\n     * Génère une réponse à une question sur le projet\n     * @param question Question posée par l'utilisateur\n     * @param projectContext Contexte du projet (titre, description, etc.)\n     * @returns Observable contenant la réponse générée\n     */\n    askProjectQuestion(question, projectContext) {\n      // Réponses de secours en cas d'erreur\n      const fallbackResponses = [`Pour votre projet \"${projectContext.title || 'en cours'}\", je recommande de commencer par définir clairement les objectifs et les livrables attendus.`, `La gestion efficace d'un projet comme \"${projectContext.title || 'celui-ci'}\" nécessite une bonne planification et une communication claire entre les membres de l'équipe.`, `Pour répondre à votre question sur \"${question}\", je vous suggère de diviser le travail en tâches plus petites et de les assigner aux membres de l'équipe en fonction de leurs compétences.`, `Dans le cadre de votre projet, il est important de définir des jalons clairs et de suivre régulièrement l'avancement des travaux.`];\n      // Sélectionner une réponse aléatoire de secours\n      const getRandomFallbackResponse = () => {\n        const randomIndex = Math.floor(Math.random() * fallbackResponses.length);\n        return fallbackResponses[randomIndex];\n      };\n      // Si l'API n'est pas disponible, retourner directement une réponse de secours\n      if (!this.isApiAvailable()) {\n        console.log('API Gemini non disponible, utilisation d\\'une réponse de secours');\n        return of(getRandomFallbackResponse());\n      }\n      const prompt = `\n      Contexte du projet:\n      Titre: ${projectContext.title || 'Non spécifié'}\n      Description: ${projectContext.description || 'Non spécifiée'}\n\n      Question: ${question}\n\n      Réponds de manière concise et professionnelle en tant qu'assistant de gestion de projet.\n    `;\n      try {\n        return from(this.model.generateContent(prompt)).pipe(map(result => {\n          try {\n            return result.response.text();\n          } catch (error) {\n            console.error('Erreur lors de la récupération de la réponse:', error);\n            return getRandomFallbackResponse();\n          }\n        }), catchError(error => {\n          console.error('Erreur lors de la génération de contenu:', error);\n          this.markApiAsUnavailable();\n          return of(getRandomFallbackResponse());\n        }));\n      } catch (error) {\n        console.error('Erreur lors de la génération de contenu:', error);\n        this.markApiAsUnavailable();\n        return of(getRandomFallbackResponse());\n      }\n    }\n    static {\n      this.ɵfac = function AiService_Factory(t) {\n        return new (t || AiService)();\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: AiService,\n        factory: AiService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return AiService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}