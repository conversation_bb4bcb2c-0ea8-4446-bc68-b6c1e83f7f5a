<div class="min-h-screen bg-[#edf1f4] dark:bg-[#121212] p-4 md:p-6 relative">
  <!-- Background decorative elements -->
  <div class="absolute inset-0 overflow-hidden pointer-events-none">
    <div
      class="absolute top-[15%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-br from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl"
    ></div>
    <div
      class="absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl"
    ></div>

    <!-- Grid pattern -->
    <div class="absolute inset-0 opacity-5 dark:opacity-[0.03]">
      <div class="h-full grid grid-cols-12">
        <div class="border-r border-[#4f5fad] dark:border-[#6d78c9]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#6d78c9]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#6d78c9]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#6d78c9]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#6d78c9]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#6d78c9]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#6d78c9]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#6d78c9]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#6d78c9]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#6d78c9]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#6d78c9]"></div>
      </div>
    </div>
  </div>

  <div class="max-w-6xl mx-auto relative z-10">
    <!-- Header avec titre et bouton d'ajout -->
    <div
      class="flex flex-col md:flex-row md:items-center md:justify-between mb-8"
    >
      <div>
        <h1
          class="text-3xl font-bold bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent"
        >
          Équipes
        </h1>
        <p class="text-[#6d6870] dark:text-[#a0a0a0] text-sm md:text-base mt-1">
          Gérez vos équipes et leurs membres
        </p>
      </div>
      <button
        (click)="navigateToAddEquipe()"
        class="mt-4 md:mt-0 relative overflow-hidden group"
      >
        <div
          class="absolute inset-0 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#3d4a85] dark:to-[#6d78c9] rounded-lg transition-transform duration-300 group-hover:scale-105"
        ></div>
        <div
          class="absolute inset-0 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#3d4a85] dark:to-[#6d78c9] rounded-lg opacity-0 group-hover:opacity-100 blur-md transition-opacity duration-300"
        ></div>
        <span
          class="relative flex items-center text-white font-medium px-6 py-3 rounded-lg transition-all z-10"
        >
          <i
            class="fas fa-plus mr-2 group-hover:scale-110 transition-transform"
          ></i>
          Nouvelle Équipe
        </span>
      </button>
    </div>

    <!-- Loading Indicator -->
    <div *ngIf="loading" class="flex justify-center my-12">
      <div class="relative">
        <div
          class="w-14 h-14 border-4 border-[#4f5fad]/20 dark:border-[#6d78c9]/20 border-t-[#4f5fad] dark:border-t-[#6d78c9] rounded-full animate-spin"
        ></div>
        <!-- Glow effect -->
        <div
          class="absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 blur-xl rounded-full transform scale-150 -z-10"
        ></div>
      </div>
    </div>

    <!-- Error Alert -->
    <div
      *ngIf="error"
      class="bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 border border-[#ff6b69] dark:border-[#ff6b69]/30 rounded-lg p-4 mx-auto max-w-3xl mb-6 backdrop-blur-sm"
    >
      <div class="flex items-start">
        <div class="text-[#ff6b69] dark:text-[#ff8785] mr-3 text-xl relative">
          <i class="fas fa-exclamation-triangle"></i>
          <!-- Glow effect -->
          <div
            class="absolute inset-0 bg-[#ff6b69]/20 dark:bg-[#ff8785]/20 blur-xl rounded-full transform scale-150 -z-10"
          ></div>
        </div>
        <div class="flex-1">
          <h3 class="font-medium text-[#ff6b69] dark:text-[#ff8785] mb-1">
            Erreur
          </h3>
          <p class="text-sm text-[#ff6b69]/80 dark:text-[#ff8785]/80">
            {{ error }}
          </p>
        </div>
        <button
          (click)="loadEquipes()"
          class="ml-4 relative overflow-hidden group"
        >
          <div
            class="absolute inset-0 bg-gradient-to-r from-[#ff6b69] to-[#ff8785] dark:from-[#ff6b69] dark:to-[#ff8785] rounded-lg transition-transform duration-300 group-hover:scale-105"
          ></div>
          <span
            class="relative flex items-center text-white text-sm font-medium px-3 py-1.5 rounded-lg transition-all z-10"
          >
            <i
              class="fas fa-redo mr-1 group-hover:scale-110 transition-transform"
            ></i>
            Réessayer
          </span>
        </button>
      </div>
    </div>

    <!-- No Teams -->
    <div
      *ngIf="!loading && !error && equipes.length === 0"
      class="bg-white dark:bg-[#1e1e1e] rounded-xl shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)] p-8 text-center backdrop-blur-sm border border-[#edf1f4]/50 dark:border-[#2a2a2a]"
    >
      <div
        class="w-24 h-24 mx-auto mb-6 bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 rounded-full flex items-center justify-center relative"
      >
        <i
          class="fas fa-users text-[#4f5fad] dark:text-[#6d78c9] text-4xl relative z-10"
        ></i>
        <!-- Glow effect -->
        <div
          class="absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 blur-xl rounded-full transform scale-150 -z-10"
        ></div>
      </div>
      <h3
        class="text-xl font-medium bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent mb-2"
      >
        Aucune équipe trouvée
      </h3>
      <p class="text-[#6d6870] dark:text-[#a0a0a0] mt-1 mb-6">
        Commencez par créer une nouvelle équipe pour organiser vos projets et
        membres
      </p>
      <button
        (click)="navigateToAddEquipe()"
        class="relative overflow-hidden group"
      >
        <div
          class="absolute inset-0 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#3d4a85] dark:to-[#6d78c9] rounded-lg transition-transform duration-300 group-hover:scale-105"
        ></div>
        <div
          class="absolute inset-0 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#3d4a85] dark:to-[#6d78c9] rounded-lg opacity-0 group-hover:opacity-100 blur-md transition-opacity duration-300"
        ></div>
        <span
          class="relative flex items-center text-white font-medium px-6 py-3 rounded-lg transition-all z-10"
        >
          <i
            class="fas fa-plus mr-2 group-hover:scale-110 transition-transform"
          ></i>
          Créer une équipe
        </span>
      </button>
    </div>

    <!-- Teams Grid -->
    <div
      *ngIf="!loading && equipes.length > 0"
      class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
    >
      <div
        *ngFor="let equipe of equipes"
        class="bg-white dark:bg-[#1e1e1e] rounded-xl shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)] overflow-hidden hover:shadow-lg dark:hover:shadow-[0_8px_30px_rgba(0,0,0,0.3)] transition-all duration-300 hover:-translate-y-1 backdrop-blur-sm border border-[#edf1f4]/50 dark:border-[#2a2a2a] group"
      >
        <!-- Header -->
        <div class="relative overflow-hidden">
          <!-- Decorative gradient top border -->
          <div
            class="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad]"
          ></div>

          <!-- Glow effect on hover -->
          <div
            class="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] opacity-0 group-hover:opacity-100 blur-md transition-opacity duration-300"
          ></div>

          <div class="p-5 bg-white dark:bg-[#1e1e1e] relative">
            <h3
              class="text-lg font-bold pr-10 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent group-hover:scale-[1.01] transition-transform duration-300 origin-left"
            >
              {{ equipe.name }}
            </h3>
            <div class="flex items-center mt-2 text-xs space-x-2">
              <span
                class="bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 text-[#4f5fad] dark:text-[#6d78c9] px-2 py-0.5 rounded-full backdrop-blur-sm flex items-center"
              >
                <i class="fas fa-users mr-1"></i>
                {{ equipe.members?.length || 0 }} membre(s)
              </span>
            </div>
          </div>
        </div>

        <!-- Content -->
        <div class="p-5">
          <p
            class="text-sm text-[#6d6870] dark:text-[#a0a0a0] mb-4 line-clamp-3"
          >
            {{
              equipe.description && equipe.description.length > 100
                ? (equipe.description | slice : 0 : 100) + "..."
                : equipe.description || "Aucune description"
            }}
          </p>

          <!-- Actions -->
          <div
            class="flex justify-between items-center pt-3 border-t border-[#edf1f4]/50 dark:border-[#2a2a2a]"
          >
            <a
              (click)="equipe._id && navigateToEquipeDetail(equipe._id)"
              class="text-[#4f5fad] dark:text-[#6d78c9] hover:text-[#3d4a85] dark:hover:text-[#4f5fad] text-sm font-medium flex items-center transition-colors relative group/details cursor-pointer"
            >
              <div class="relative mr-1">
                <i
                  class="fas fa-eye relative z-10 group-hover/details:scale-110 transition-transform"
                ></i>
                <!-- Glow effect -->
                <div
                  class="absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 opacity-0 group-hover/details:opacity-100 transition-opacity blur-md rounded-full"
                ></div>
              </div>
              <span>Détails</span>
            </a>

            <div class="flex items-center space-x-2">
              <button
                (click)="equipe._id && navigateToEditEquipe(equipe._id)"
                class="p-2 text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] hover:bg-[#edf1f4]/50 dark:hover:bg-[#2a2a2a]/50 rounded-lg transition-all relative group/edit"
                title="Modifier l'équipe"
              >
                <i
                  class="fas fa-edit relative z-10 group-hover/edit:scale-110 transition-transform"
                ></i>
                <!-- Glow effect -->
                <div
                  class="absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 opacity-0 group-hover/edit:opacity-100 transition-opacity blur-md rounded-lg"
                ></div>
              </button>

              <button
                (click)="equipe._id && deleteEquipe(equipe._id)"
                class="p-2 text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#ff6b69] dark:hover:text-[#ff8785] hover:bg-[#ff6b69]/10 dark:hover:bg-[#ff6b69]/10 rounded-lg transition-all relative group/delete"
                title="Supprimer l'équipe"
              >
                <i
                  class="fas fa-trash relative z-10 group-hover/delete:scale-110 transition-transform"
                ></i>
                <!-- Glow effect -->
                <div
                  class="absolute inset-0 bg-[#ff6b69]/20 dark:bg-[#ff8785]/20 opacity-0 group-hover/delete:opacity-100 transition-opacity blur-md rounded-lg"
                ></div>
              </button>

              <button
                (click)="equipe._id && navigateToTasks(equipe._id)"
                class="relative overflow-hidden group/tasks"
                title="Gérer les tâches de l'équipe"
              >
                <div
                  class="absolute inset-0 bg-gradient-to-r from-[#2a5a03] to-[#afcf75] dark:from-[#2a5a03] dark:to-[#afcf75] rounded-lg transition-transform duration-300 group-hover/tasks:scale-105"
                ></div>
                <div
                  class="absolute inset-0 bg-gradient-to-r from-[#2a5a03] to-[#afcf75] dark:from-[#2a5a03] dark:to-[#afcf75] rounded-lg opacity-0 group-hover/tasks:opacity-100 blur-md transition-opacity duration-300"
                ></div>
                <span
                  class="relative flex items-center text-white text-sm font-medium px-3 py-1.5 rounded-lg transition-all z-10"
                >
                  <i
                    class="fas fa-tasks mr-1 group-hover/tasks:scale-110 transition-transform"
                  ></i>
                  Tasks
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
