<div class="w-full">
  <!-- Messages du chat -->
  <div class="p-6 max-h-96 overflow-y-auto" #chatContainer>
    <div *ngFor="let message of messages; let i = index" class="mb-4">
      <!-- Message utilisateur -->
      <div *ngIf="message.role === 'user'" class="flex justify-end">
        <div class="flex items-end space-x-2 max-w-xs lg:max-w-md">
          <div
            class="bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea] text-white p-3 rounded-lg rounded-br-none shadow-lg"
          >
            <p class="text-sm" [innerHTML]="message.content"></p>
            <div class="text-xs text-white/80 mt-1 text-right">
              Vous • {{ getCurrentTime() }}
            </div>
          </div>
          <div
            class="w-8 h-8 bg-gradient-to-br from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea] rounded-full flex items-center justify-center"
          >
            <i class="fas fa-user text-white text-sm"></i>
          </div>
        </div>
      </div>

      <!-- Message assistant -->
      <div *ngIf="message.role === 'assistant'" class="flex justify-start">
        <div class="flex items-end space-x-2 max-w-xs lg:max-w-md">
          <div
            class="w-8 h-8 bg-gradient-to-br from-[#8b5a9f] to-[#4a00e0] dark:from-[#00f7ff] dark:to-[#8b5a9f] rounded-full flex items-center justify-center"
          >
            <i class="fas fa-robot text-white text-sm"></i>
          </div>
          <div
            class="bg-[#f0f4f8] dark:bg-[#0a0a0a] border border-[#8b5a9f]/20 dark:border-[#00f7ff]/20 p-3 rounded-lg rounded-bl-none shadow-lg"
          >
            <p
              class="text-sm text-[#6d6870] dark:text-[#a0a0a0]"
              [innerHTML]="message.content"
            ></p>
            <div class="text-xs text-[#6d6870]/60 dark:text-[#a0a0a0]/60 mt-1">
              Assistant IA • {{ getCurrentTime() }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Indicateur de chargement -->
    <div
      *ngIf="isGenerating || isAskingQuestion"
      class="flex justify-start mb-4"
    >
      <div class="flex items-end space-x-2">
        <div
          class="w-8 h-8 bg-gradient-to-br from-[#8b5a9f] to-[#4a00e0] dark:from-[#00f7ff] dark:to-[#8b5a9f] rounded-full flex items-center justify-center"
        >
          <i class="fas fa-robot text-white text-sm"></i>
        </div>
        <div
          class="bg-[#f0f4f8] dark:bg-[#0a0a0a] border border-[#8b5a9f]/20 dark:border-[#00f7ff]/20 p-3 rounded-lg rounded-bl-none shadow-lg"
        >
          <div class="flex space-x-1">
            <div
              class="w-2 h-2 bg-[#8b5a9f] dark:bg-[#00f7ff] rounded-full animate-bounce"
            ></div>
            <div
              class="w-2 h-2 bg-[#8b5a9f] dark:bg-[#00f7ff] rounded-full animate-bounce"
              style="animation-delay: 0.1s"
            ></div>
            <div
              class="w-2 h-2 bg-[#8b5a9f] dark:bg-[#00f7ff] rounded-full animate-bounce"
              style="animation-delay: 0.2s"
            ></div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Résultats générés -->
  <div
    *ngIf="generatedContent"
    class="border-t border-[#8b5a9f]/20 dark:border-[#00f7ff]/20"
  >
    <!-- Header du projet généré -->
    <div
      class="p-6 bg-gradient-to-r from-[#8b5a9f]/10 to-[#4a00e0]/10 dark:from-[#00f7ff]/10 dark:to-[#8b5a9f]/10"
    >
      <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
        <div class="mb-4 lg:mb-0">
          <h3
            class="text-xl font-bold text-[#8b5a9f] dark:text-[#00f7ff] mb-2 flex items-center"
          >
            <i class="fas fa-project-diagram mr-2"></i>
            Plan du projet "{{ generatedContent.projectTitle }}"
          </h3>
          <p
            class="text-[#6d6870] dark:text-[#a0a0a0] text-sm flex items-center"
          >
            <i class="fas fa-info-circle mr-2"></i>
            {{ generatedContent.entities.length }} modules générés avec
            {{ countTasks(generatedContent) }} tâches au total
          </p>
        </div>
        <div
          class="bg-[#8b5a9f]/20 dark:bg-[#00f7ff]/20 text-[#8b5a9f] dark:text-[#00f7ff] px-4 py-2 rounded-full text-sm font-medium"
        >
          <i class="fas fa-users mr-2"></i>
          {{ team && team.members ? team.members.length : 0 }} membres
        </div>
      </div>
    </div>

    <!-- Grille des modules -->
    <div class="p-6">
      <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
        <div
          *ngFor="let entity of generatedContent.entities; let i = index"
          class="group relative"
        >
          <!-- Badge numéro de module -->
          <div
            class="absolute -top-2 -right-2 z-10 w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-bold shadow-lg"
            [ngStyle]="{ background: getGradientForIndex(i) }"
          >
            {{ i + 1 }}
          </div>

          <div
            class="bg-white dark:bg-[#1a1a1a] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] overflow-hidden border border-[#8b5a9f]/20 dark:border-[#00f7ff]/20 hover:shadow-xl transition-all duration-300 hover:-translate-y-1"
          >
            <!-- Header du module -->
            <div
              class="p-6 text-white relative overflow-hidden"
              [ngStyle]="{ background: getGradientForIndex(i) }"
            >
              <div
                class="absolute top-0 right-0 w-20 h-20 rounded-full bg-white/10 -mr-10 -mt-10"
              ></div>
              <div class="relative z-10">
                <div
                  class="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mb-4"
                >
                  <i
                    class="fas"
                    [ngClass]="getIconForModule(entity.name)"
                    class="text-xl"
                  ></i>
                </div>
                <h4 class="text-lg font-bold">{{ entity.name }}</h4>
              </div>
            </div>

            <div class="p-6">
              <!-- Description -->
              <div
                class="bg-[#f0f4f8] dark:bg-[#0a0a0a] border-l-4 rounded-lg p-3 mb-4"
                [ngStyle]="{ 'border-color': getColorForIndex(i) }"
              >
                <p class="text-sm text-[#6d6870] dark:text-[#a0a0a0]">
                  {{ entity.description }}
                </p>
              </div>

              <!-- Assignation -->
              <div
                *ngIf="entity.assignedTo"
                class="flex items-center p-3 rounded-lg mb-4"
                [ngStyle]="{ background: getColorForIndex(i) + '10' }"
              >
                <div
                  class="w-8 h-8 rounded-full flex items-center justify-center text-white mr-3"
                  [ngStyle]="{ background: getGradientForIndex(i) }"
                >
                  <i class="fas fa-user text-sm"></i>
                </div>
                <div>
                  <div class="text-xs text-[#6d6870] dark:text-[#a0a0a0]">
                    Responsable
                  </div>
                  <div
                    class="font-semibold"
                    [ngStyle]="{ color: getColorForIndex(i) }"
                  >
                    {{ entity.assignedTo }}
                  </div>
                </div>
              </div>

              <!-- Header des tâches -->
              <div
                class="flex items-center justify-between mb-4 pb-2 border-b border-[#8b5a9f]/10 dark:border-[#00f7ff]/10"
              >
                <h5
                  class="text-sm font-semibold text-[#6d6870] dark:text-[#a0a0a0] flex items-center"
                >
                  <i
                    class="fas fa-tasks mr-2"
                    [ngStyle]="{ color: getColorForIndex(i) }"
                  ></i>
                  Tâches à réaliser
                </h5>
                <span
                  class="px-2 py-1 rounded-full text-xs font-medium text-white"
                  [ngStyle]="{ background: getGradientForIndex(i) }"
                >
                  {{ entity.tasks.length }} tâches
                </span>
              </div>

              <!-- Liste des tâches -->
              <div class="space-y-3 max-h-64 overflow-y-auto">
                <div
                  *ngFor="let task of entity.tasks; let j = index"
                  class="p-3 rounded-lg border transition-all hover:shadow-md"
                  [ngClass]="{
                    'border-[#ff6b69]/20 bg-[#ff6b69]/5':
                      task.priority === 'high',
                    'border-[#ffa726]/20 bg-[#ffa726]/5':
                      task.priority === 'medium',
                    'border-[#42a5f5]/20 bg-[#42a5f5]/5':
                      task.priority === 'low'
                  }"
                >
                  <!-- Titre et priorité -->
                  <div class="flex items-start justify-between mb-2">
                    <h6
                      class="text-sm font-semibold text-[#6d6870] dark:text-[#a0a0a0] flex-1 mr-2"
                    >
                      {{ task.title }}
                    </h6>
                    <span
                      class="px-2 py-1 rounded-full text-xs font-medium"
                      [ngClass]="{
                        'bg-[#ff6b69] text-white': task.priority === 'high',
                        'bg-[#ffa726] text-white': task.priority === 'medium',
                        'bg-[#42a5f5] text-white': task.priority === 'low'
                      }"
                    >
                      {{
                        task.priority === "high"
                          ? "Haute"
                          : task.priority === "medium"
                          ? "Moyenne"
                          : "Basse"
                      }}
                    </span>
                  </div>

                  <!-- Description -->
                  <p class="text-xs text-[#6d6870]/80 dark:text-[#a0a0a0]/80">
                    {{ task.description }}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Section de création de tâches -->
    <div class="p-6 border-t border-[#8b5a9f]/20 dark:border-[#00f7ff]/20">
      <div
        class="bg-gradient-to-r from-[#00ff9d]/10 to-[#38ef7d]/10 dark:from-[#00f7ff]/10 dark:to-[#00ff9d]/10 rounded-xl p-6"
      >
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 items-center">
          <!-- Étapes -->
          <div class="lg:col-span-2 space-y-4">
            <h4
              class="text-xl font-bold text-[#00ff9d] dark:text-[#00f7ff] mb-4 flex items-center"
            >
              <i class="fas fa-check-circle mr-2"></i>
              Plan de projet prêt à être implémenté
            </h4>

            <!-- Étape 1 -->
            <div class="flex items-center">
              <div
                class="w-8 h-8 bg-[#00ff9d] dark:bg-[#00f7ff] text-white rounded-full flex items-center justify-center font-bold text-sm mr-4"
              >
                1
              </div>
              <div>
                <h6 class="font-semibold text-[#6d6870] dark:text-[#a0a0a0]">
                  Création des tâches
                </h6>
                <p class="text-sm text-[#6d6870]/80 dark:text-[#a0a0a0]/80">
                  {{ countTasks(generatedContent) }} tâches seront créées dans
                  le système
                </p>
              </div>
            </div>

            <!-- Étape 2 -->
            <div class="flex items-center">
              <div
                class="w-8 h-8 bg-[#4f5fad] dark:bg-[#8b5a9f] text-white rounded-full flex items-center justify-center font-bold text-sm mr-4"
              >
                2
              </div>
              <div>
                <h6 class="font-semibold text-[#6d6870] dark:text-[#a0a0a0]">
                  Assignation aux membres
                </h6>
                <p class="text-sm text-[#6d6870]/80 dark:text-[#a0a0a0]/80">
                  Les tâches seront assignées aux
                  {{ team && team.members ? team.members.length : 0 }} membres
                  de l'équipe
                </p>
              </div>
            </div>

            <!-- Étape 3 -->
            <div class="flex items-center">
              <div
                class="w-8 h-8 bg-[#42a5f5] text-white rounded-full flex items-center justify-center font-bold text-sm mr-4"
              >
                3
              </div>
              <div>
                <h6 class="font-semibold text-[#6d6870] dark:text-[#a0a0a0]">
                  Suivi du projet
                </h6>
                <p class="text-sm text-[#6d6870]/80 dark:text-[#a0a0a0]/80">
                  Vous pourrez suivre l'avancement dans le tableau de bord des
                  tâches
                </p>
              </div>
            </div>
          </div>

          <!-- Bouton de création -->
          <div class="text-center">
            <button
              (click)="createTasks()"
              class="bg-gradient-to-r from-[#00ff9d] to-[#38ef7d] dark:from-[#00f7ff] dark:to-[#00ff9d] text-white px-8 py-4 rounded-xl font-bold text-lg transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-[0_0_25px_rgba(0,255,157,0.4)] dark:hover:shadow-[0_0_25px_rgba(0,247,255,0.4)]"
            >
              <i class="fas fa-plus-circle mr-2"></i>
              Créer les tâches
            </button>
            <p class="text-xs text-[#6d6870] dark:text-[#a0a0a0] mt-2">
              <i class="fas fa-info-circle mr-1"></i>
              Cette action est irréversible
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Message d'erreur -->
  <div
    *ngIf="error"
    class="p-4 border-t border-[#ff6b69]/20 dark:border-[#ff3b30]/20"
  >
    <div
      class="bg-[#ff6b69]/10 dark:bg-[#ff3b30]/10 border-l-4 border-[#ff6b69] dark:border-[#ff3b30] rounded-lg p-4"
    >
      <div class="flex items-center">
        <div class="text-[#ff6b69] dark:text-[#ff3b30] mr-3 text-lg">
          <i class="fas fa-exclamation-triangle"></i>
        </div>
        <div class="text-sm text-[#6d6870] dark:text-[#a0a0a0]">
          {{ error }}
        </div>
      </div>
    </div>
  </div>

  <!-- Zone de saisie -->
  <div
    class="p-6 border-t border-[#8b5a9f]/20 dark:border-[#00f7ff]/20 bg-[#f0f4f8] dark:bg-[#0a0a0a]"
  >
    <!-- Générateur de tâches -->
    <div *ngIf="!generatedContent" class="mb-6">
      <div
        class="bg-white dark:bg-[#1a1a1a] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] p-6 border border-[#8b5a9f]/20 dark:border-[#00f7ff]/20"
      >
        <h4
          class="text-lg font-bold text-[#8b5a9f] dark:text-[#00f7ff] mb-4 flex items-center"
        >
          <i class="fas fa-magic mr-2"></i>
          Générer des tâches avec l'IA
        </h4>

        <div class="space-y-4">
          <div>
            <label
              class="block text-sm font-medium text-[#8b5a9f] dark:text-[#00f7ff] mb-2"
            >
              Titre de votre projet
            </label>
            <div class="relative">
              <div
                class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
              >
                <i
                  class="fas fa-lightbulb text-[#8b5a9f] dark:text-[#00f7ff]"
                ></i>
              </div>
              <input
                type="text"
                id="projectTitle"
                class="w-full pl-10 pr-4 py-3 bg-[#f0f4f8] dark:bg-[#0a0a0a] border border-[#8b5a9f]/20 dark:border-[#00f7ff]/20 rounded-lg text-[#6d6870] dark:text-[#a0a0a0] placeholder-[#6d6870]/50 dark:placeholder-[#a0a0a0]/50 focus:outline-none focus:ring-2 focus:ring-[#8b5a9f] dark:focus:ring-[#00f7ff] focus:border-transparent transition-all"
                placeholder="Ex: Site e-commerce, Application mobile, Système de gestion..."
                [(ngModel)]="projectTitle"
                [disabled]="isGenerating"
              />
            </div>
            <p
              class="mt-2 text-xs text-[#6d6870] dark:text-[#a0a0a0] flex items-center"
            >
              <i class="fas fa-info-circle mr-2"></i>
              L'IA générera
              {{ team && team.members ? team.members.length : 3 }} modules, un
              pour chaque membre de l'équipe.
            </p>
          </div>

          <button
            (click)="generateTasks()"
            [disabled]="isGenerating || !projectTitle.trim()"
            class="w-full bg-gradient-to-r from-[#8b5a9f] to-[#4a00e0] dark:from-[#00f7ff] dark:to-[#8b5a9f] text-white px-6 py-3 rounded-lg font-medium transition-all duration-300 hover:scale-105 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100"
          >
            <span
              *ngIf="isGenerating"
              class="inline-block w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2"
            ></span>
            <i *ngIf="!isGenerating" class="fas fa-magic mr-2"></i>
            {{ isGenerating ? "Génération en cours..." : "Générer des tâches" }}
          </button>
        </div>
      </div>
    </div>

    <!-- Chat avec l'IA -->
    <div
      class="bg-white dark:bg-[#1a1a1a] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] p-4 border border-[#8b5a9f]/20 dark:border-[#00f7ff]/20"
    >
      <div class="flex space-x-3">
        <div class="relative flex-1">
          <div
            class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
          >
            <i
              class="fas fa-comment-dots text-[#8b5a9f] dark:text-[#00f7ff]"
            ></i>
          </div>
          <input
            type="text"
            class="w-full pl-10 pr-4 py-3 bg-[#f0f4f8] dark:bg-[#0a0a0a] border border-[#8b5a9f]/20 dark:border-[#00f7ff]/20 rounded-lg text-[#6d6870] dark:text-[#a0a0a0] placeholder-[#6d6870]/50 dark:placeholder-[#a0a0a0]/50 focus:outline-none focus:ring-2 focus:ring-[#8b5a9f] dark:focus:ring-[#00f7ff] focus:border-transparent transition-all"
            placeholder="Posez une question sur la gestion de projet..."
            [(ngModel)]="userQuestion"
            (keyup.enter)="askQuestion()"
            [disabled]="isAskingQuestion"
          />
        </div>
        <button
          (click)="askQuestion()"
          [disabled]="isAskingQuestion || !userQuestion.trim()"
          class="bg-gradient-to-r from-[#8b5a9f] to-[#4a00e0] dark:from-[#00f7ff] dark:to-[#8b5a9f] text-white px-4 py-3 rounded-lg font-medium transition-all duration-300 hover:scale-105 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100"
        >
          <span
            *ngIf="isAskingQuestion"
            class="inline-block w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"
          ></span>
          <i *ngIf="!isAskingQuestion" class="fas fa-paper-plane"></i>
        </button>
      </div>
    </div>
  </div>
</div>
