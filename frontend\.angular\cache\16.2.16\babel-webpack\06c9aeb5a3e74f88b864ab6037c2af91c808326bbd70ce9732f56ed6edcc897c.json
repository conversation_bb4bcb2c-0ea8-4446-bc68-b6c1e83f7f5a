{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nexport class EquipeLayoutComponent {\n  static {\n    this.ɵfac = function EquipeLayoutComponent_Factory(t) {\n      return new (t || EquipeLayoutComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: EquipeLayoutComponent,\n      selectors: [[\"app-equipe-layout\"]],\n      decls: 4,\n      vars: 4,\n      consts: [[1, \"layout-container\", \"futuristic-layout\"], [1, \"main-content\", \"futuristic-main-content\"]],\n      template: function EquipeLayoutComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵpipe(2, \"async\");\n          i0.ɵɵelement(3, \"router-outlet\");\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassProp(\"sidebar-hidden\", !i0.ɵɵpipeBind1(2, 2, ctx.sidebarVisible$));\n        }\n      },\n      dependencies: [i1.RouterOutlet, i2.AsyncPipe],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJlcXVpcGUtbGF5b3V0LmNvbXBvbmVudC5jc3MifQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvZnJvbnQvZXF1aXBlcy9lcXVpcGUtbGF5b3V0L2VxdWlwZS1sYXlvdXQuY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0FBQ0EsNEtBQTRLIiwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EquipeLayoutComponent", "selectors", "decls", "vars", "consts", "template", "EquipeLayoutComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵclassProp", "ɵɵpipeBind1", "sidebarVisible$"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\equipes\\equipe-layout\\equipe-layout.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\equipes\\equipe-layout\\equipe-layout.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-equipe-layout',\n  templateUrl: './equipe-layout.component.html',\n  styleUrls: ['./equipe-layout.component.css']\n})\nexport class EquipeLayoutComponent {\n\n}\n", "<div class=\"layout-container futuristic-layout\">\n  <!-- <app-messages-sidebar *ngIf=\"context === 'messages'\"></app-messages-sidebar> -->\n  <div\n    class=\"main-content futuristic-main-content\"\n    [class.sidebar-hidden]=\"!(sidebarVisible$ | async)\"\n  >\n    <router-outlet></router-outlet>\n  </div>\n</div>\n"], "mappings": ";;;AAOA,OAAM,MAAOA,qBAAqB;;;uBAArBA,qBAAqB;IAAA;EAAA;;;YAArBA,qBAAqB;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCPlCE,EAAA,CAAAC,cAAA,aAAgD;;UAM5CD,EAAA,CAAAE,SAAA,oBAA+B;UACjCF,EAAA,CAAAG,YAAA,EAAM;;;UAHJH,EAAA,CAAAI,SAAA,GAAmD;UAAnDJ,EAAA,CAAAK,WAAA,oBAAAL,EAAA,CAAAM,WAAA,OAAAP,GAAA,CAAAQ,eAAA,EAAmD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}