{"ast": null, "code": "import { throwError } from 'rxjs';\nimport { catchError, tap, map } from 'rxjs/operators';\nimport { environment } from '../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class EquipeService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = `${environment.apiBaseUrl}/teams`;\n    console.log('API URL:', this.apiUrl);\n  }\n  getEquipes() {\n    console.log('Fetching teams from:', this.apiUrl);\n    return this.http.get(this.apiUrl).pipe(tap(data => console.log('Teams received:', data)), catchError(this.handleError));\n  }\n  getEquipe(id) {\n    console.log(`Fetching team with id ${id} from: ${this.apiUrl}/${id}`);\n    return this.http.get(`${this.apiUrl}/${id}`).pipe(tap(data => console.log('Team received:', data)), catchError(this.handleError));\n  }\n  addEquipe(equipe) {\n    console.log('Adding team:', equipe);\n    return this.http.post(this.apiUrl, equipe).pipe(tap(data => console.log('Team added, response:', data)), catchError(this.handleError));\n  }\n  updateEquipe(id, equipe) {\n    console.log(`Updating team with id ${id}:`, equipe);\n    return this.http.put(`${this.apiUrl}/${id}`, equipe).pipe(tap(data => console.log('Team updated, response:', data)), catchError(this.handleError));\n  }\n  deleteEquipe(id) {\n    console.log(`Deleting team with id ${id}`);\n    console.log(`API URL: ${this.apiUrl}/${id}`);\n    return this.http.delete(`${this.apiUrl}/${id}`).pipe(tap(data => console.log('Team deleted, response:', data)), catchError(error => {\n      console.error('Error deleting team:', error);\n      console.error('Request URL:', `${this.apiUrl}/${id}`);\n      return this.handleError(error);\n    }));\n  }\n  addMembreToEquipe(teamId, membre) {\n    console.log(`Adding member to team ${teamId}:`, membre);\n    // Créer l'objet attendu par le backend\n    const memberData = {\n      userId: membre.id,\n      role: membre.role || \"membre\" // Utiliser le rôle spécifié ou \"membre\" par défaut\n    };\n\n    console.log('Sending to backend:', memberData);\n    console.log('Team ID type:', typeof teamId, 'value:', teamId);\n    console.log('User ID type:', typeof membre.id, 'value:', membre.id);\n    // Utiliser la route directe pour ajouter un membre à une équipe\n    return this.http.post(`${this.apiUrl}/${teamId}/members`, memberData).pipe(tap(data => console.log('Member added, response:', data)), catchError(this.handleError));\n  }\n  removeMembreFromEquipe(teamId, membreId) {\n    console.log(`Removing member ${membreId} from team ${teamId}`);\n    console.log(`API URL: ${this.apiUrl}/${teamId}/members/${membreId}`);\n    // Utiliser la route directe pour supprimer un membre d'une équipe\n    return this.http.delete(`${this.apiUrl}/${teamId}/members/${membreId}`).pipe(tap(data => console.log('Member removed, response:', data)), catchError(error => {\n      console.error('Error removing member:', error);\n      console.error('Request URL:', `${this.apiUrl}/${teamId}/members/${membreId}`);\n      return this.handleError(error);\n    }));\n  }\n  /**\n   * Récupère les détails des membres d'une équipe\n   * @param teamId ID de l'équipe\n   * @returns Observable contenant la liste des membres avec leurs détails\n   */\n  getTeamMembers(teamId) {\n    console.log(`Fetching team members for team ${teamId}`);\n    // Utiliser la route de l'équipe pour récupérer les détails de l'équipe, qui contient les membres\n    return this.http.get(`${this.apiUrl}/${teamId}`).pipe(map(team => {\n      console.log('Team data received:', team);\n      // Transformer les IDs des membres en objets avec l'ID et le rôle\n      if (team && team.members) {\n        return team.members.map(memberId => ({\n          user: memberId,\n          role: 'membre',\n          _id: memberId // Utiliser l'ID du membre comme ID du TeamMember\n        }));\n      }\n\n      return [];\n    }), tap(data => console.log('Team members processed:', data)), catchError(this.handleError));\n  }\n  handleError(error) {\n    let errorMessage = '';\n    if (error.error instanceof ErrorEvent) {\n      // Erreur côté client\n      errorMessage = `Erreur client: ${error.error.message}`;\n    } else {\n      // Erreur côté serveur\n      const status = error.status;\n      const message = error.error?.message || error.statusText;\n      errorMessage = `Erreur serveur: Code ${status}, Message: ${message}`;\n      // Log des détails supplémentaires pour le débogage\n      console.error('Error details:', {\n        status: error.status,\n        statusText: error.statusText,\n        url: error.url,\n        error: error.error\n      });\n      if (status === 0) {\n        console.error('Le serveur est-il en cours d\\'exécution? Vérifiez la connexion réseau.');\n      }\n    }\n    console.error('API Error:', errorMessage);\n    return throwError(() => new Error(errorMessage));\n  }\n  static {\n    this.ɵfac = function EquipeService_Factory(t) {\n      return new (t || EquipeService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: EquipeService,\n      factory: EquipeService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["throwError", "catchError", "tap", "map", "environment", "EquipeService", "constructor", "http", "apiUrl", "apiBaseUrl", "console", "log", "getEquipes", "get", "pipe", "data", "handleError", "getEquipe", "id", "addEquipe", "equipe", "post", "updateEquipe", "put", "deleteEquipe", "delete", "error", "addMembreToEquipe", "teamId", "membre", "memberData", "userId", "role", "removeMembreFromEquipe", "membreId", "getTeamMembers", "team", "members", "memberId", "user", "_id", "errorMessage", "ErrorEvent", "message", "status", "statusText", "url", "Error", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\services\\equipe.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient, HttpErrorResponse } from '@angular/common/http';\r\nimport { Observable, throwError } from 'rxjs';\r\nimport { catchError, tap, map } from 'rxjs/operators';\r\nimport { Equipe } from '../models/equipe.model';\r\nimport { environment } from '../../environments/environment';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class EquipeService {\r\n  private apiUrl = `${environment.apiBaseUrl}/teams`;\r\n\r\n  constructor(private http: HttpClient) {\r\n    console.log('API URL:', this.apiUrl);\r\n  }\r\n\r\n  getEquipes(): Observable<Equipe[]> {\r\n    console.log('Fetching teams from:', this.apiUrl);\r\n    return this.http.get<Equipe[]>(this.apiUrl).pipe(\r\n      tap(data => console.log('Teams received:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  getEquipe(id: string): Observable<Equipe> {\r\n    console.log(`Fetching team with id ${id} from: ${this.apiUrl}/${id}`);\r\n    return this.http.get<Equipe>(`${this.apiUrl}/${id}`).pipe(\r\n      tap(data => console.log('Team received:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  addEquipe(equipe: Equipe): Observable<Equipe> {\r\n    console.log('Adding team:', equipe);\r\n    return this.http.post<Equipe>(this.apiUrl, equipe).pipe(\r\n      tap(data => console.log('Team added, response:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  updateEquipe(id: string, equipe: Equipe): Observable<Equipe> {\r\n    console.log(`Updating team with id ${id}:`, equipe);\r\n    return this.http.put<Equipe>(`${this.apiUrl}/${id}`, equipe).pipe(\r\n      tap(data => console.log('Team updated, response:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  deleteEquipe(id: string): Observable<any> {\r\n    console.log(`Deleting team with id ${id}`);\r\n    console.log(`API URL: ${this.apiUrl}/${id}`);\r\n\r\n    return this.http.delete(`${this.apiUrl}/${id}`).pipe(\r\n      tap(data => console.log('Team deleted, response:', data)),\r\n      catchError(error => {\r\n        console.error('Error deleting team:', error);\r\n        console.error('Request URL:', `${this.apiUrl}/${id}`);\r\n        return this.handleError(error);\r\n      })\r\n    );\r\n  }\r\n\r\n  addMembreToEquipe(teamId: string, membre: any): Observable<any> {\r\n    console.log(`Adding member to team ${teamId}:`, membre);\r\n\r\n    // Créer l'objet attendu par le backend\r\n    const memberData = {\r\n      userId: membre.id,\r\n      role: membre.role || \"membre\" // Utiliser le rôle spécifié ou \"membre\" par défaut\r\n    };\r\n\r\n    console.log('Sending to backend:', memberData);\r\n    console.log('Team ID type:', typeof teamId, 'value:', teamId);\r\n    console.log('User ID type:', typeof membre.id, 'value:', membre.id);\r\n\r\n    // Utiliser la route directe pour ajouter un membre à une équipe\r\n    return this.http.post<any>(`${this.apiUrl}/${teamId}/members`, memberData).pipe(\r\n      tap(data => console.log('Member added, response:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  removeMembreFromEquipe(teamId: string, membreId: string): Observable<any> {\r\n    console.log(`Removing member ${membreId} from team ${teamId}`);\r\n    console.log(`API URL: ${this.apiUrl}/${teamId}/members/${membreId}`);\r\n\r\n    // Utiliser la route directe pour supprimer un membre d'une équipe\r\n    return this.http.delete<any>(`${this.apiUrl}/${teamId}/members/${membreId}`).pipe(\r\n      tap(data => console.log('Member removed, response:', data)),\r\n      catchError(error => {\r\n        console.error('Error removing member:', error);\r\n        console.error('Request URL:', `${this.apiUrl}/${teamId}/members/${membreId}`);\r\n        return this.handleError(error);\r\n      })\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Récupère les détails des membres d'une équipe\r\n   * @param teamId ID de l'équipe\r\n   * @returns Observable contenant la liste des membres avec leurs détails\r\n   */\r\n  getTeamMembers(teamId: string): Observable<any[]> {\r\n    console.log(`Fetching team members for team ${teamId}`);\r\n    // Utiliser la route de l'équipe pour récupérer les détails de l'équipe, qui contient les membres\r\n    return this.http.get<any>(`${this.apiUrl}/${teamId}`).pipe(\r\n      map(team => {\r\n        console.log('Team data received:', team);\r\n        // Transformer les IDs des membres en objets avec l'ID et le rôle\r\n        if (team && team.members) {\r\n          return team.members.map((memberId: string) => ({\r\n            user: memberId,\r\n            role: 'membre', // Par défaut, tous les membres ont le rôle \"membre\"\r\n            _id: memberId   // Utiliser l'ID du membre comme ID du TeamMember\r\n          }));\r\n        }\r\n        return [];\r\n      }),\r\n      tap(data => console.log('Team members processed:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  private handleError(error: HttpErrorResponse) {\r\n    let errorMessage = '';\r\n\r\n    if (error.error instanceof ErrorEvent) {\r\n      // Erreur côté client\r\n      errorMessage = `Erreur client: ${error.error.message}`;\r\n    } else {\r\n      // Erreur côté serveur\r\n      const status = error.status;\r\n      const message = error.error?.message || error.statusText;\r\n\r\n      errorMessage = `Erreur serveur: Code ${status}, Message: ${message}`;\r\n\r\n      // Log des détails supplémentaires pour le débogage\r\n      console.error('Error details:', {\r\n        status: error.status,\r\n        statusText: error.statusText,\r\n        url: error.url,\r\n        error: error.error\r\n      });\r\n\r\n      if (status === 0) {\r\n        console.error('Le serveur est-il en cours d\\'exécution? Vérifiez la connexion réseau.');\r\n      }\r\n    }\r\n\r\n    console.error('API Error:', errorMessage);\r\n    return throwError(() => new Error(errorMessage));\r\n  }\r\n}\r\n\r\n\r\n\r\n\r\n"], "mappings": "AAEA,SAAqBA,UAAU,QAAQ,MAAM;AAC7C,SAASC,UAAU,EAAEC,GAAG,EAAEC,GAAG,QAAQ,gBAAgB;AAErD,SAASC,WAAW,QAAQ,gCAAgC;;;AAK5D,OAAM,MAAOC,aAAa;EAGxBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,MAAM,GAAG,GAAGJ,WAAW,CAACK,UAAU,QAAQ;IAGhDC,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE,IAAI,CAACH,MAAM,CAAC;EACtC;EAEAI,UAAUA,CAAA;IACRF,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAACH,MAAM,CAAC;IAChD,OAAO,IAAI,CAACD,IAAI,CAACM,GAAG,CAAW,IAAI,CAACL,MAAM,CAAC,CAACM,IAAI,CAC9CZ,GAAG,CAACa,IAAI,IAAIL,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEI,IAAI,CAAC,CAAC,EACjDd,UAAU,CAAC,IAAI,CAACe,WAAW,CAAC,CAC7B;EACH;EAEAC,SAASA,CAACC,EAAU;IAClBR,OAAO,CAACC,GAAG,CAAC,yBAAyBO,EAAE,UAAU,IAAI,CAACV,MAAM,IAAIU,EAAE,EAAE,CAAC;IACrE,OAAO,IAAI,CAACX,IAAI,CAACM,GAAG,CAAS,GAAG,IAAI,CAACL,MAAM,IAAIU,EAAE,EAAE,CAAC,CAACJ,IAAI,CACvDZ,GAAG,CAACa,IAAI,IAAIL,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEI,IAAI,CAAC,CAAC,EAChDd,UAAU,CAAC,IAAI,CAACe,WAAW,CAAC,CAC7B;EACH;EAEAG,SAASA,CAACC,MAAc;IACtBV,OAAO,CAACC,GAAG,CAAC,cAAc,EAAES,MAAM,CAAC;IACnC,OAAO,IAAI,CAACb,IAAI,CAACc,IAAI,CAAS,IAAI,CAACb,MAAM,EAAEY,MAAM,CAAC,CAACN,IAAI,CACrDZ,GAAG,CAACa,IAAI,IAAIL,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEI,IAAI,CAAC,CAAC,EACvDd,UAAU,CAAC,IAAI,CAACe,WAAW,CAAC,CAC7B;EACH;EAEAM,YAAYA,CAACJ,EAAU,EAAEE,MAAc;IACrCV,OAAO,CAACC,GAAG,CAAC,yBAAyBO,EAAE,GAAG,EAAEE,MAAM,CAAC;IACnD,OAAO,IAAI,CAACb,IAAI,CAACgB,GAAG,CAAS,GAAG,IAAI,CAACf,MAAM,IAAIU,EAAE,EAAE,EAAEE,MAAM,CAAC,CAACN,IAAI,CAC/DZ,GAAG,CAACa,IAAI,IAAIL,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEI,IAAI,CAAC,CAAC,EACzDd,UAAU,CAAC,IAAI,CAACe,WAAW,CAAC,CAC7B;EACH;EAEAQ,YAAYA,CAACN,EAAU;IACrBR,OAAO,CAACC,GAAG,CAAC,yBAAyBO,EAAE,EAAE,CAAC;IAC1CR,OAAO,CAACC,GAAG,CAAC,YAAY,IAAI,CAACH,MAAM,IAAIU,EAAE,EAAE,CAAC;IAE5C,OAAO,IAAI,CAACX,IAAI,CAACkB,MAAM,CAAC,GAAG,IAAI,CAACjB,MAAM,IAAIU,EAAE,EAAE,CAAC,CAACJ,IAAI,CAClDZ,GAAG,CAACa,IAAI,IAAIL,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEI,IAAI,CAAC,CAAC,EACzDd,UAAU,CAACyB,KAAK,IAAG;MACjBhB,OAAO,CAACgB,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5ChB,OAAO,CAACgB,KAAK,CAAC,cAAc,EAAE,GAAG,IAAI,CAAClB,MAAM,IAAIU,EAAE,EAAE,CAAC;MACrD,OAAO,IAAI,CAACF,WAAW,CAACU,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEAC,iBAAiBA,CAACC,MAAc,EAAEC,MAAW;IAC3CnB,OAAO,CAACC,GAAG,CAAC,yBAAyBiB,MAAM,GAAG,EAAEC,MAAM,CAAC;IAEvD;IACA,MAAMC,UAAU,GAAG;MACjBC,MAAM,EAAEF,MAAM,CAACX,EAAE;MACjBc,IAAI,EAAEH,MAAM,CAACG,IAAI,IAAI,QAAQ,CAAC;KAC/B;;IAEDtB,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEmB,UAAU,CAAC;IAC9CpB,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,OAAOiB,MAAM,EAAE,QAAQ,EAAEA,MAAM,CAAC;IAC7DlB,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,OAAOkB,MAAM,CAACX,EAAE,EAAE,QAAQ,EAAEW,MAAM,CAACX,EAAE,CAAC;IAEnE;IACA,OAAO,IAAI,CAACX,IAAI,CAACc,IAAI,CAAM,GAAG,IAAI,CAACb,MAAM,IAAIoB,MAAM,UAAU,EAAEE,UAAU,CAAC,CAAChB,IAAI,CAC7EZ,GAAG,CAACa,IAAI,IAAIL,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEI,IAAI,CAAC,CAAC,EACzDd,UAAU,CAAC,IAAI,CAACe,WAAW,CAAC,CAC7B;EACH;EAEAiB,sBAAsBA,CAACL,MAAc,EAAEM,QAAgB;IACrDxB,OAAO,CAACC,GAAG,CAAC,mBAAmBuB,QAAQ,cAAcN,MAAM,EAAE,CAAC;IAC9DlB,OAAO,CAACC,GAAG,CAAC,YAAY,IAAI,CAACH,MAAM,IAAIoB,MAAM,YAAYM,QAAQ,EAAE,CAAC;IAEpE;IACA,OAAO,IAAI,CAAC3B,IAAI,CAACkB,MAAM,CAAM,GAAG,IAAI,CAACjB,MAAM,IAAIoB,MAAM,YAAYM,QAAQ,EAAE,CAAC,CAACpB,IAAI,CAC/EZ,GAAG,CAACa,IAAI,IAAIL,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEI,IAAI,CAAC,CAAC,EAC3Dd,UAAU,CAACyB,KAAK,IAAG;MACjBhB,OAAO,CAACgB,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9ChB,OAAO,CAACgB,KAAK,CAAC,cAAc,EAAE,GAAG,IAAI,CAAClB,MAAM,IAAIoB,MAAM,YAAYM,QAAQ,EAAE,CAAC;MAC7E,OAAO,IAAI,CAAClB,WAAW,CAACU,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;;;;;EAKAS,cAAcA,CAACP,MAAc;IAC3BlB,OAAO,CAACC,GAAG,CAAC,kCAAkCiB,MAAM,EAAE,CAAC;IACvD;IACA,OAAO,IAAI,CAACrB,IAAI,CAACM,GAAG,CAAM,GAAG,IAAI,CAACL,MAAM,IAAIoB,MAAM,EAAE,CAAC,CAACd,IAAI,CACxDX,GAAG,CAACiC,IAAI,IAAG;MACT1B,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEyB,IAAI,CAAC;MACxC;MACA,IAAIA,IAAI,IAAIA,IAAI,CAACC,OAAO,EAAE;QACxB,OAAOD,IAAI,CAACC,OAAO,CAAClC,GAAG,CAAEmC,QAAgB,KAAM;UAC7CC,IAAI,EAAED,QAAQ;UACdN,IAAI,EAAE,QAAQ;UACdQ,GAAG,EAAEF,QAAQ,CAAG;SACjB,CAAC,CAAC;;;MAEL,OAAO,EAAE;IACX,CAAC,CAAC,EACFpC,GAAG,CAACa,IAAI,IAAIL,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEI,IAAI,CAAC,CAAC,EACzDd,UAAU,CAAC,IAAI,CAACe,WAAW,CAAC,CAC7B;EACH;EAEQA,WAAWA,CAACU,KAAwB;IAC1C,IAAIe,YAAY,GAAG,EAAE;IAErB,IAAIf,KAAK,CAACA,KAAK,YAAYgB,UAAU,EAAE;MACrC;MACAD,YAAY,GAAG,kBAAkBf,KAAK,CAACA,KAAK,CAACiB,OAAO,EAAE;KACvD,MAAM;MACL;MACA,MAAMC,MAAM,GAAGlB,KAAK,CAACkB,MAAM;MAC3B,MAAMD,OAAO,GAAGjB,KAAK,CAACA,KAAK,EAAEiB,OAAO,IAAIjB,KAAK,CAACmB,UAAU;MAExDJ,YAAY,GAAG,wBAAwBG,MAAM,cAAcD,OAAO,EAAE;MAEpE;MACAjC,OAAO,CAACgB,KAAK,CAAC,gBAAgB,EAAE;QAC9BkB,MAAM,EAAElB,KAAK,CAACkB,MAAM;QACpBC,UAAU,EAAEnB,KAAK,CAACmB,UAAU;QAC5BC,GAAG,EAAEpB,KAAK,CAACoB,GAAG;QACdpB,KAAK,EAAEA,KAAK,CAACA;OACd,CAAC;MAEF,IAAIkB,MAAM,KAAK,CAAC,EAAE;QAChBlC,OAAO,CAACgB,KAAK,CAAC,wEAAwE,CAAC;;;IAI3FhB,OAAO,CAACgB,KAAK,CAAC,YAAY,EAAEe,YAAY,CAAC;IACzC,OAAOzC,UAAU,CAAC,MAAM,IAAI+C,KAAK,CAACN,YAAY,CAAC,CAAC;EAClD;;;uBA9IWpC,aAAa,EAAA2C,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAb9C,aAAa;MAAA+C,OAAA,EAAb/C,aAAa,CAAAgD,IAAA;MAAAC,UAAA,EAFZ;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}