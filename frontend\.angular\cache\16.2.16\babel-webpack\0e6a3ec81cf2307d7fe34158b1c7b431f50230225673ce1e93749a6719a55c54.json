{"ast": null, "code": "import { BehaviorSubject, Observable, of, throwError, retry, EMP<PERSON> } from 'rxjs';\nimport { map, catchError, tap, filter, switchMap } from 'rxjs/operators';\nimport { from } from 'rxjs';\nimport { MessageType, CallType, CallStatus } from '../models/message.model';\nimport { GET_CONVERSATIONS_QUERY, GET_NOTIFICATIONS_QUERY, NOTIFICATION_SUBSCRIPTION, GET_CONVERSATION_QUERY, SEND_MESSAGE_MUTATION, MARK_AS_READ_MUTATION, MESSAGE_SENT_SUBSCRIPTION, USER_STATUS_SUBSCRIPTION, GET_USER_QUERY, GET_ALL_USER_QUERY, CONVERSATION_UPDATED_SUBSCRIPTION, SEARCH_MESSAGES_QUERY, GET_UNREAD_MESSAGES_QUERY, SET_USER_ONLINE_MUTATION, SET_USER_OFFLINE_MUTATION, GET_GROUP_QUERY, GET_USER_GROUPS_QUERY, CREATE_GROUP_MUTATION, UPDATE_GROUP_MUTATION, START_TYPING_MUTATION, STOP_TYPING_MUTATION, TYPING_INDICATOR_SUBSCRIPTION, GET_CURRENT_USER_QUERY, REACT_TO_MESSAGE_MUTATION, FORWARD_MESSAGE_MUTATION, PIN_MESSAGE_MUTATION, EDIT_MESSAGE_MUTATION, DELETE_MESSAGE_MUTATION, GET_MESSAGES_QUERY, GET_NOTIFICATIONS_ATTACHAMENTS, MARK_NOTIFICATION_READ_MUTATION, NOTIFICATIONS_READ_SUBSCRIPTION, CREATE_CONVERSATION_MUTATION, DELETE_NOTIFICATION_MUTATION, DELETE_MULTIPLE_NOTIFICATIONS_MUTATION, DELETE_ALL_NOTIFICATIONS_MUTATION, INITIATE_CALL_MUTATION, SEND_CALL_SIGNAL_MUTATION, ACCEPT_CALL_MUTATION, REJECT_CALL_MUTATION, END_CALL_MUTATION, TOGGLE_CALL_MEDIA_MUTATION, CALL_SIGNAL_SUBSCRIPTION, INCOMING_CALL_SUBSCRIPTION, GET_VOICE_MESSAGES_QUERY } from '../graphql/message.graphql';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"apollo-angular\";\nimport * as i2 from \"./logger.service\";\nexport let MessageService = /*#__PURE__*/(() => {\n  class MessageService {\n    constructor(apollo, logger, zone) {\n      this.apollo = apollo;\n      this.logger = logger;\n      this.zone = zone;\n      // État partagé\n      this.activeConversation = new BehaviorSubject(null);\n      this.notifications = new BehaviorSubject([]);\n      this.notificationCache = new Map();\n      this.notificationCount = new BehaviorSubject(0);\n      this.onlineUsers = new Map();\n      this.subscriptions = [];\n      this.CACHE_DURATION = 300000;\n      this.lastFetchTime = 0;\n      // Propriétés pour les appels\n      this.activeCall = new BehaviorSubject(null);\n      this.incomingCall = new BehaviorSubject(null);\n      this.callSignals = new BehaviorSubject(null);\n      this.localStream = null;\n      this.remoteStream = null;\n      this.peerConnection = null;\n      // Observables publics pour les appels\n      this.activeCall$ = this.activeCall.asObservable();\n      this.incomingCall$ = this.incomingCall.asObservable();\n      this.callSignals$ = this.callSignals.asObservable();\n      this.localStream$ = new BehaviorSubject(null);\n      this.remoteStream$ = new BehaviorSubject(null);\n      // Configuration WebRTC\n      this.rtcConfig = {\n        iceServers: [{\n          urls: 'stun:stun.l.google.com:19302'\n        }, {\n          urls: 'stun:stun1.l.google.com:19302'\n        }]\n      };\n      this.usersCache = [];\n      // Pagination metadata for user list\n      this.currentUserPagination = {\n        totalCount: 0,\n        totalPages: 0,\n        currentPage: 1,\n        hasNextPage: false,\n        hasPreviousPage: false\n      };\n      // Observables publics\n      this.activeConversation$ = this.activeConversation.asObservable();\n      this.notifications$ = this.notifications.asObservable();\n      this.notificationCount$ = this.notificationCount.asObservable();\n      // Propriétés pour la gestion des sons\n      this.sounds = {};\n      this.isPlaying = {};\n      this.muted = false;\n      // --------------------------------------------------------------------------\n      // Section 2: Méthodes pour les Notifications\n      // --------------------------------------------------------------------------\n      // Propriétés pour la pagination des notifications\n      this.notificationPagination = {\n        currentPage: 1,\n        limit: 10,\n        hasMoreNotifications: true\n      };\n      this.toSafeISOString = date => {\n        if (!date) return undefined;\n        return typeof date === 'string' ? date : date.toISOString();\n      };\n      this.loadNotificationsFromLocalStorage();\n      this.initSubscriptions();\n      this.startCleanupInterval();\n      this.preloadSounds();\n    }\n    /**\n     * Charge les notifications depuis le localStorage\n     * @private\n     */\n    loadNotificationsFromLocalStorage() {\n      try {\n        const savedNotifications = localStorage.getItem('notifications');\n        if (savedNotifications) {\n          const notifications = JSON.parse(savedNotifications);\n          this.logger.debug('MessageService', `Chargement de ${notifications.length} notifications depuis le localStorage`);\n          // Vider le cache avant de charger les notifications pour éviter les doublons\n          this.notificationCache.clear();\n          // Mettre à jour le cache avec les notifications sauvegardées\n          notifications.forEach(notification => {\n            // Vérifier que la notification a un ID valide\n            if (notification && notification.id) {\n              this.notificationCache.set(notification.id, notification);\n            }\n          });\n          // Mettre à jour le BehaviorSubject avec les notifications chargées\n          this.notifications.next(Array.from(this.notificationCache.values()));\n          this.updateUnreadCount();\n          this.logger.debug('MessageService', `${this.notificationCache.size} notifications chargées dans le cache`);\n        }\n      } catch (error) {\n        this.logger.error('MessageService', 'Erreur lors du chargement des notifications depuis le localStorage:', error);\n      }\n    }\n    initSubscriptions() {\n      this.zone.runOutsideAngular(() => {\n        this.subscribeToNewNotifications().subscribe();\n        this.subscribeToNotificationsRead().subscribe();\n        this.subscribeToIncomingCalls().subscribe();\n      });\n      this.subscribeToUserStatus();\n    }\n    /**\n     * S'abonne aux appels entrants\n     */\n    subscribeToIncomingCalls() {\n      return this.apollo.subscribe({\n        query: INCOMING_CALL_SUBSCRIPTION\n      }).pipe(map(({\n        data\n      }) => {\n        if (!data?.incomingCall) {\n          return null;\n        }\n        // Gérer l'appel entrant\n        this.handleIncomingCall(data.incomingCall);\n        return data.incomingCall;\n      }), catchError(error => {\n        this.logger.error('Error in incoming call subscription', error);\n        return of(null);\n      }));\n    }\n    /**\n     * Gère un appel entrant\n     */\n    handleIncomingCall(call) {\n      this.logger.debug('Incoming call received', call);\n      this.incomingCall.next(call);\n      this.play('ringtone', true);\n    }\n    // --------------------------------------------------------------------------\n    // Section: Gestion des sons (intégré depuis SoundService)\n    // --------------------------------------------------------------------------\n    /**\n     * Précharge les sons utilisés dans l'application\n     */\n    preloadSounds() {\n      this.loadSound('ringtone', 'assets/sounds/ringtone.mp3');\n      this.loadSound('call-end', 'assets/sounds/call-end.mp3');\n      this.loadSound('call-connected', 'assets/sounds/call-connected.mp3');\n      this.loadSound('notification', 'assets/sounds/notification.mp3');\n    }\n    /**\n     * Charge un fichier audio\n     * @param name Nom du son\n     * @param path Chemin du fichier\n     */\n    loadSound(name, path) {\n      try {\n        const audio = new Audio(path);\n        audio.load();\n        this.sounds[name] = audio;\n        this.isPlaying[name] = false;\n        // Gérer la fin de la lecture\n        audio.addEventListener('ended', () => {\n          this.isPlaying[name] = false;\n        });\n        this.logger.debug('MessageService', `Son chargé: ${name} (${path})`);\n      } catch (error) {\n        this.logger.error('MessageService', `Erreur lors du chargement du son ${name}:`, error);\n      }\n    }\n    /**\n     * Joue un son\n     * @param name Nom du son\n     * @param loop Lecture en boucle\n     */\n    play(name, loop = false) {\n      if (this.muted) {\n        this.logger.debug('MessageService', `Son ${name} non joué (muet)`);\n        return;\n      }\n      try {\n        const sound = this.sounds[name];\n        if (!sound) {\n          this.logger.warn('MessageService', `Son ${name} non trouvé`);\n          return;\n        }\n        // Configurer la lecture en boucle\n        sound.loop = loop;\n        // Jouer le son s'il n'est pas déjà en cours de lecture\n        if (!this.isPlaying[name]) {\n          sound.currentTime = 0;\n          sound.play().catch(error => {\n            this.logger.error('MessageService', `Erreur lors de la lecture du son ${name}:`, error);\n          });\n          this.isPlaying[name] = true;\n          this.logger.debug('MessageService', `Lecture du son: ${name}, boucle: ${loop}`);\n        }\n      } catch (error) {\n        this.logger.error('MessageService', `Erreur lors de la lecture du son ${name}:`, error);\n      }\n    }\n    /**\n     * Arrête un son\n     * @param name Nom du son\n     */\n    stop(name) {\n      try {\n        const sound = this.sounds[name];\n        if (!sound) {\n          this.logger.warn('MessageService', `Son ${name} non trouvé`);\n          return;\n        }\n        // Arrêter le son s'il est en cours de lecture\n        if (this.isPlaying[name]) {\n          sound.pause();\n          sound.currentTime = 0;\n          this.isPlaying[name] = false;\n          this.logger.debug('MessageService', `Son arrêté: ${name}`);\n        }\n      } catch (error) {\n        this.logger.error('MessageService', `Erreur lors de l'arrêt du son ${name}:`, error);\n      }\n    }\n    /**\n     * Arrête tous les sons\n     */\n    stopAllSounds() {\n      Object.keys(this.sounds).forEach(name => {\n        this.stop(name);\n      });\n      this.logger.debug('MessageService', 'Tous les sons ont été arrêtés');\n    }\n    /**\n     * Active ou désactive le son\n     * @param muted True pour désactiver le son, false pour l'activer\n     */\n    setMuted(muted) {\n      this.muted = muted;\n      this.logger.info('MessageService', `Son ${muted ? 'désactivé' : 'activé'}`);\n      if (muted) {\n        this.stopAllSounds();\n      }\n    }\n    /**\n     * Vérifie si le son est désactivé\n     * @returns True si le son est désactivé, false sinon\n     */\n    isMuted() {\n      return this.muted;\n    }\n    /**\n     * Joue le son de notification\n     */\n    playNotificationSound() {\n      console.log('MessageService: Tentative de lecture du son de notification');\n      if (this.muted) {\n        console.log('MessageService: Son désactivé, notification ignorée');\n        return;\n      }\n      // Utiliser l'API Web Audio pour générer un son de notification simple\n      try {\n        // Créer un contexte audio\n        const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n        // Créer un oscillateur pour générer un son\n        const oscillator = audioContext.createOscillator();\n        const gainNode = audioContext.createGain();\n        // Configurer l'oscillateur\n        oscillator.type = 'sine';\n        oscillator.frequency.setValueAtTime(880, audioContext.currentTime); // La note A5\n        // Configurer le volume\n        gainNode.gain.setValueAtTime(0, audioContext.currentTime);\n        gainNode.gain.linearRampToValueAtTime(0.5, audioContext.currentTime + 0.01);\n        gainNode.gain.linearRampToValueAtTime(0, audioContext.currentTime + 0.3);\n        // Connecter les nœuds\n        oscillator.connect(gainNode);\n        gainNode.connect(audioContext.destination);\n        // Démarrer et arrêter l'oscillateur\n        oscillator.start(audioContext.currentTime);\n        oscillator.stop(audioContext.currentTime + 0.3);\n        console.log('MessageService: Son de notification généré avec succès');\n      } catch (error) {\n        console.error('MessageService: Erreur lors de la génération du son:', error);\n        // Fallback à la méthode originale en cas d'erreur\n        try {\n          const audio = new Audio('assets/sounds/notification.mp3');\n          audio.volume = 1.0; // Volume maximum\n          audio.play().catch(err => {\n            console.error('MessageService: Erreur lors de la lecture du fichier son:', err);\n          });\n        } catch (audioError) {\n          console.error('MessageService: Exception lors de la lecture du fichier son:', audioError);\n        }\n      }\n    }\n    // --------------------------------------------------------------------------\n    // Section 1: Méthodes pour les Messages\n    // --------------------------------------------------------------------------\n    /**\n     * Envoie un message vocal à un utilisateur\n     * @param receiverId ID de l'utilisateur destinataire\n     * @param audioBlob Blob audio à envoyer\n     * @param conversationId ID de la conversation (optionnel)\n     * @param duration Durée de l'enregistrement en secondes (optionnel)\n     * @returns Observable avec le message envoyé\n     */\n    sendVoiceMessage(receiverId, audioBlob, conversationId, duration) {\n      this.logger.debug(`[MessageService] Sending voice message to user ${receiverId}, duration: ${duration}s`);\n      // Vérifier que le blob audio est valide\n      if (!audioBlob || audioBlob.size === 0) {\n        this.logger.error('[MessageService] Invalid audio blob');\n        return throwError(() => new Error('Invalid audio blob'));\n      }\n      // Créer un fichier à partir du blob audio avec un nom unique\n      const timestamp = Date.now();\n      const audioFile = new File([audioBlob], `voice-message-${timestamp}.webm`, {\n        type: 'audio/webm',\n        lastModified: timestamp\n      });\n      // Vérifier que le fichier a été créé correctement\n      if (!audioFile || audioFile.size === 0) {\n        this.logger.error('[MessageService] Failed to create audio file');\n        return throwError(() => new Error('Failed to create audio file'));\n      }\n      this.logger.debug(`[MessageService] Created audio file: ${audioFile.name}, size: ${audioFile.size} bytes`);\n      // Créer des métadonnées pour le message vocal\n      const metadata = {\n        duration: duration || 0,\n        isVoiceMessage: true,\n        timestamp: timestamp\n      };\n      // Utiliser la méthode sendMessage avec le type VOICE_MESSAGE\n      // Utiliser une chaîne vide comme contenu pour éviter les problèmes de validation\n      return this.sendMessage(receiverId, ' ',\n      // Espace comme contenu minimal pour passer la validation\n      audioFile, MessageType.VOICE_MESSAGE, conversationId, undefined, metadata);\n    }\n    /**\n     * Joue un fichier audio\n     * @param audioUrl URL du fichier audio à jouer\n     * @returns Promise qui se résout lorsque la lecture est terminée\n     */\n    playAudio(audioUrl) {\n      return new Promise((resolve, reject) => {\n        const audio = new Audio(audioUrl);\n        audio.onended = () => {\n          resolve();\n        };\n        audio.onerror = error => {\n          this.logger.error(`[MessageService] Error playing audio:`, error);\n          reject(error);\n        };\n        audio.play().catch(error => {\n          this.logger.error(`[MessageService] Error playing audio:`, error);\n          reject(error);\n        });\n      });\n    }\n    /**\n     * Récupère tous les messages vocaux de l'utilisateur\n     * @returns Observable avec la liste des messages vocaux\n     */\n    getVoiceMessages() {\n      this.logger.debug('[MessageService] Getting voice messages');\n      return this.apollo.watchQuery({\n        query: GET_VOICE_MESSAGES_QUERY,\n        fetchPolicy: 'network-only' // Ne pas utiliser le cache pour cette requête\n      }).valueChanges.pipe(map(result => {\n        const voiceMessages = result.data?.getVoiceMessages || [];\n        this.logger.debug(`[MessageService] Retrieved ${voiceMessages.length} voice messages`);\n        return voiceMessages;\n      }), catchError(error => {\n        this.logger.error('[MessageService] Error fetching voice messages:', error);\n        return throwError(() => new Error('Failed to fetch voice messages'));\n      }));\n    }\n    // Message methods\n    getMessages(senderId, receiverId, conversationId, page = 1, limit = 10) {\n      return this.apollo.watchQuery({\n        query: GET_MESSAGES_QUERY,\n        variables: {\n          senderId,\n          receiverId,\n          conversationId,\n          limit,\n          page\n        },\n        fetchPolicy: 'network-only'\n      }).valueChanges.pipe(map(result => {\n        const messages = result.data?.getMessages || [];\n        return messages.map(msg => this.normalizeMessage(msg));\n      }), catchError(error => {\n        this.logger.error('Error fetching messages:', error);\n        return throwError(() => new Error('Failed to fetch messages'));\n      }));\n    }\n    editMessage(messageId, newContent) {\n      return this.apollo.mutate({\n        mutation: EDIT_MESSAGE_MUTATION,\n        variables: {\n          messageId,\n          newContent\n        }\n      }).pipe(map(result => {\n        if (!result.data?.editMessage) {\n          throw new Error('Failed to edit message');\n        }\n        return this.normalizeMessage(result.data.editMessage);\n      }), catchError(error => {\n        this.logger.error('Error editing message:', error);\n        return throwError(() => new Error('Failed to edit message'));\n      }));\n    }\n    deleteMessage(messageId) {\n      return this.apollo.mutate({\n        mutation: DELETE_MESSAGE_MUTATION,\n        variables: {\n          messageId\n        }\n      }).pipe(map(result => {\n        if (!result.data?.deleteMessage) {\n          throw new Error('Failed to delete message');\n        }\n        return this.normalizeMessage(result.data.deleteMessage);\n      }), catchError(error => {\n        this.logger.error('Error deleting message:', error);\n        return throwError(() => new Error('Failed to delete message'));\n      }));\n    }\n    sendMessage(receiverId, content, file, messageType = MessageType.TEXT, conversationId, replyTo, metadata) {\n      this.logger.info(`[MessageService] Sending message to: ${receiverId}, hasFile: ${!!file}`);\n      this.logger.debug(`[MessageService] Message content: \"${content?.substring(0, 50)}${content?.length > 50 ? '...' : ''}\"`);\n      // Vérifier l'authentification\n      const token = localStorage.getItem('token');\n      this.logger.debug(`[MessageService] Authentication check before sending message: token=${!!token}`);\n      // Utiliser le type de message fourni ou le déterminer automatiquement\n      let finalMessageType = messageType;\n      // Si le type n'est pas explicitement fourni et qu'il y a un fichier, déterminer le type\n      if (file) {\n        // Si le type est déjà VOICE_MESSAGE, le conserver\n        if (messageType === MessageType.VOICE_MESSAGE) {\n          finalMessageType = MessageType.VOICE_MESSAGE;\n          this.logger.debug(`[MessageService] Using explicit VOICE_MESSAGE type`);\n        }\n        // Sinon, déterminer le type en fonction du type de fichier\n        else if (messageType === MessageType.TEXT) {\n          if (file.type.startsWith('image/')) {\n            finalMessageType = MessageType.IMAGE;\n          } else if (file.type.startsWith('video/')) {\n            finalMessageType = MessageType.VIDEO;\n          } else if (file.type.startsWith('audio/')) {\n            // Vérifier si c'est un message vocal basé sur les métadonnées\n            if (metadata && metadata.isVoiceMessage) {\n              finalMessageType = MessageType.VOICE_MESSAGE;\n            } else {\n              finalMessageType = MessageType.AUDIO;\n            }\n          } else {\n            finalMessageType = MessageType.FILE;\n          }\n        }\n      }\n      this.logger.debug(`[MessageService] Message type determined: ${finalMessageType}`);\n      // Ajouter le type de message aux variables\n      // Utiliser directement la valeur de l'énumération sans conversion\n      const variables = {\n        receiverId,\n        content,\n        type: finalMessageType // Ajouter explicitement le type de message\n      };\n      // Forcer le type à être une valeur d'énumération GraphQL\n      // Cela empêche Apollo de convertir la valeur en minuscules\n      if (variables.type) {\n        Object.defineProperty(variables, 'type', {\n          value: finalMessageType,\n          enumerable: true,\n          writable: false\n        });\n      }\n      // Ajouter les métadonnées si elles sont fournies\n      if (metadata) {\n        variables.metadata = metadata;\n        this.logger.debug(`[MessageService] Metadata attached:`, metadata);\n      }\n      if (file) {\n        variables.file = file;\n        this.logger.debug(`[MessageService] File attached: ${file.name}, size: ${file.size}, type: ${file.type}, messageType: ${finalMessageType}`);\n      }\n      if (conversationId) {\n        variables.conversationId = conversationId;\n        this.logger.debug(`[MessageService] Using existing conversation: ${conversationId}`);\n      }\n      if (replyTo) {\n        variables.replyTo = replyTo;\n        this.logger.debug(`[MessageService] Replying to message: ${replyTo}`);\n      }\n      const context = file ? {\n        useMultipart: true,\n        file\n      } : undefined;\n      this.logger.debug(`[MessageService] Sending GraphQL mutation with variables:`, variables);\n      return this.apollo.mutate({\n        mutation: SEND_MESSAGE_MUTATION,\n        variables,\n        context\n      }).pipe(map(result => {\n        this.logger.debug(`[MessageService] Message send response:`, result);\n        if (!result.data?.sendMessage) {\n          this.logger.error(`[MessageService] Failed to send message: No data returned`);\n          throw new Error('Failed to send message');\n        }\n        try {\n          this.logger.debug(`[MessageService] Normalizing sent message`, result.data.sendMessage);\n          const normalizedMessage = this.normalizeMessage(result.data.sendMessage);\n          this.logger.info(`[MessageService] Message sent successfully: ${normalizedMessage.id}`);\n          return normalizedMessage;\n        } catch (normalizationError) {\n          this.logger.error(`[MessageService] Error normalizing message:`, normalizationError);\n          // Retourner un message minimal mais valide plutôt que de lancer une erreur\n          const minimalMessage = {\n            id: result.data.sendMessage.id || 'temp-' + Date.now(),\n            content: result.data.sendMessage.content || '',\n            type: result.data.sendMessage.type || MessageType.TEXT,\n            timestamp: new Date(),\n            isRead: false,\n            sender: {\n              id: this.getCurrentUserId(),\n              username: 'You'\n            }\n          };\n          this.logger.info(`[MessageService] Returning minimal message: ${minimalMessage.id}`);\n          return minimalMessage;\n        }\n      }), catchError(error => {\n        this.logger.error(`[MessageService] Error sending message:`, error);\n        return throwError(() => new Error('Failed to send message'));\n      }));\n    }\n    markMessageAsRead(messageId) {\n      return this.apollo.mutate({\n        mutation: MARK_AS_READ_MUTATION,\n        variables: {\n          messageId\n        }\n      }).pipe(map(result => {\n        if (!result.data?.markMessageAsRead) throw new Error('Failed to mark message as read');\n        return {\n          ...result.data.markMessageAsRead,\n          readAt: new Date()\n        };\n      }), catchError(error => {\n        console.error('Error marking message as read:', error);\n        return throwError(() => new Error('Failed to mark message as read'));\n      }));\n    }\n    reactToMessage(messageId, emoji) {\n      return this.apollo.mutate({\n        mutation: REACT_TO_MESSAGE_MUTATION,\n        variables: {\n          messageId,\n          emoji\n        }\n      }).pipe(map(result => {\n        if (!result.data?.reactToMessage) throw new Error('Failed to react to message');\n        return result.data.reactToMessage;\n      }), catchError(error => {\n        console.error('Error reacting to message:', error);\n        return throwError(() => new Error('Failed to react to message'));\n      }));\n    }\n    forwardMessage(messageId, conversationIds) {\n      return this.apollo.mutate({\n        mutation: FORWARD_MESSAGE_MUTATION,\n        variables: {\n          messageId,\n          conversationIds\n        }\n      }).pipe(map(result => {\n        if (!result.data?.forwardMessage) throw new Error('Failed to forward message');\n        return result.data.forwardMessage.map(msg => ({\n          ...msg,\n          timestamp: msg.timestamp ? this.normalizeDate(msg.timestamp) : new Date()\n        }));\n      }), catchError(error => {\n        console.error('Error forwarding message:', error);\n        return throwError(() => new Error('Failed to forward message'));\n      }));\n    }\n    pinMessage(messageId, conversationId) {\n      return this.apollo.mutate({\n        mutation: PIN_MESSAGE_MUTATION,\n        variables: {\n          messageId,\n          conversationId\n        }\n      }).pipe(map(result => {\n        if (!result.data?.pinMessage) throw new Error('Failed to pin message');\n        return {\n          ...result.data.pinMessage,\n          pinnedAt: new Date()\n        };\n      }), catchError(error => {\n        console.error('Error pinning message:', error);\n        return throwError(() => new Error('Failed to pin message'));\n      }));\n    }\n    searchMessages(query, conversationId, filters = {}) {\n      return this.apollo.watchQuery({\n        query: SEARCH_MESSAGES_QUERY,\n        variables: {\n          query,\n          conversationId,\n          ...filters,\n          dateFrom: this.toSafeISOString(filters.dateFrom),\n          dateTo: this.toSafeISOString(filters.dateTo)\n        },\n        fetchPolicy: 'network-only'\n      }).valueChanges.pipe(map(result => result.data?.searchMessages?.map(msg => ({\n        ...msg,\n        timestamp: this.safeDate(msg.timestamp),\n        sender: this.normalizeUser(msg.sender)\n      })) || []), catchError(error => {\n        console.error('Error searching messages:', error);\n        return throwError(() => new Error('Failed to search messages'));\n      }));\n    }\n    getUnreadMessages(userId) {\n      return this.apollo.watchQuery({\n        query: GET_UNREAD_MESSAGES_QUERY,\n        variables: {\n          userId\n        },\n        fetchPolicy: 'network-only'\n      }).valueChanges.pipe(map(result => result.data?.getUnreadMessages?.map(msg => ({\n        ...msg,\n        timestamp: this.safeDate(msg.timestamp),\n        sender: this.normalizeUser(msg.sender)\n      })) || []), catchError(error => {\n        console.error('Error fetching unread messages:', error);\n        return throwError(() => new Error('Failed to fetch unread messages'));\n      }));\n    }\n    setActiveConversation(conversationId) {\n      this.activeConversation.next(conversationId);\n    }\n    getConversations() {\n      return this.apollo.watchQuery({\n        query: GET_CONVERSATIONS_QUERY,\n        fetchPolicy: 'network-only'\n      }).valueChanges.pipe(map(result => {\n        const conversations = result.data?.getConversations || [];\n        return conversations.map(conv => this.normalizeConversation(conv));\n      }), catchError(error => {\n        console.error('Error fetching conversations:', error);\n        return throwError(() => new Error('Failed to load conversations'));\n      }));\n    }\n    getConversation(conversationId, limit, page) {\n      this.logger.info(`[MessageService] Getting conversation: ${conversationId}, limit: ${limit}, page: ${page}`);\n      const variables = {\n        conversationId\n      };\n      // Ajouter les paramètres de pagination s'ils sont fournis\n      if (limit !== undefined) {\n        variables.limit = limit;\n      } else {\n        variables.limit = 10; // Valeur par défaut\n      }\n      // Calculer l'offset à partir de la page si elle est fournie\n      if (page !== undefined) {\n        // La requête GraphQL utilise offset, donc nous devons convertir la page en offset\n        const offset = (page - 1) * variables.limit;\n        variables.offset = offset;\n        this.logger.debug(`[MessageService] Calculated offset: ${offset} from page: ${page} and limit: ${variables.limit}`);\n      } else {\n        variables.offset = 0; // Valeur par défaut\n      }\n\n      this.logger.debug(`[MessageService] Final pagination parameters: limit=${variables.limit}, offset=${variables.offset}`);\n      return this.apollo.watchQuery({\n        query: GET_CONVERSATION_QUERY,\n        variables: variables,\n        fetchPolicy: 'network-only'\n      }).valueChanges.pipe(map(result => {\n        this.logger.debug(`[MessageService] Conversation response received:`, result);\n        const conv = result.data?.getConversation;\n        if (!conv) {\n          this.logger.error(`[MessageService] Conversation not found: ${conversationId}`);\n          throw new Error('Conversation not found');\n        }\n        this.logger.debug(`[MessageService] Normalizing conversation: ${conversationId}`);\n        const normalizedConversation = this.normalizeConversation(conv);\n        this.logger.info(`[MessageService] Conversation loaded successfully: ${conversationId}, participants: ${normalizedConversation.participants?.length || 0}, messages: ${normalizedConversation.messages?.length || 0}`);\n        return normalizedConversation;\n      }), catchError(error => {\n        this.logger.error(`[MessageService] Error fetching conversation:`, error);\n        return throwError(() => new Error('Failed to load conversation'));\n      }));\n    }\n    createConversation(userId) {\n      this.logger.info(`[MessageService] Creating conversation with user: ${userId}`);\n      if (!userId) {\n        this.logger.error(`[MessageService] Cannot create conversation: userId is undefined`);\n        return throwError(() => new Error('User ID is required to create a conversation'));\n      }\n      return this.apollo.mutate({\n        mutation: CREATE_CONVERSATION_MUTATION,\n        variables: {\n          userId\n        }\n      }).pipe(map(result => {\n        this.logger.debug(`[MessageService] Conversation creation response:`, result);\n        const conversation = result.data?.createConversation;\n        if (!conversation) {\n          this.logger.error(`[MessageService] Failed to create conversation with user: ${userId}`);\n          throw new Error('Failed to create conversation');\n        }\n        try {\n          const normalizedConversation = this.normalizeConversation(conversation);\n          this.logger.info(`[MessageService] Conversation created successfully: ${normalizedConversation.id}`);\n          return normalizedConversation;\n        } catch (error) {\n          this.logger.error(`[MessageService] Error normalizing created conversation:`, error);\n          throw new Error('Error processing created conversation');\n        }\n      }), catchError(error => {\n        this.logger.error(`[MessageService] Error creating conversation with user ${userId}:`, error);\n        return throwError(() => new Error(`Failed to create conversation: ${error.message}`));\n      }));\n    }\n    /**\n     * Récupère une conversation existante ou en crée une nouvelle si elle n'existe pas\n     * @param userId ID de l'utilisateur avec qui créer/récupérer une conversation\n     * @returns Observable avec la conversation\n     */\n    getOrCreateConversation(userId) {\n      this.logger.info(`[MessageService] Getting or creating conversation with user: ${userId}`);\n      if (!userId) {\n        this.logger.error(`[MessageService] Cannot get/create conversation: userId is undefined`);\n        return throwError(() => new Error('User ID is required to get/create a conversation'));\n      }\n      // D'abord, essayons de trouver une conversation existante entre les deux utilisateurs\n      return this.getConversations().pipe(map(conversations => {\n        // Récupérer l'ID de l'utilisateur actuel\n        const currentUserId = this.getCurrentUserId();\n        // Chercher une conversation directe (non groupe) entre les deux utilisateurs\n        const existingConversation = conversations.find(conv => {\n          if (conv.isGroup) return false;\n          // Vérifier si la conversation contient les deux utilisateurs\n          const participantIds = conv.participants?.map(p => p.id || p._id) || [];\n          return participantIds.includes(userId) && participantIds.includes(currentUserId);\n        });\n        if (existingConversation) {\n          this.logger.info(`[MessageService] Found existing conversation: ${existingConversation.id}`);\n          return existingConversation;\n        }\n        // Si aucune conversation n'est trouvée, en créer une nouvelle\n        throw new Error('No existing conversation found');\n      }), catchError(error => {\n        this.logger.info(`[MessageService] No existing conversation found, creating new one: ${error.message}`);\n        return this.createConversation(userId);\n      }));\n    }\n    getNotifications(refresh = false, page = 1, limit = 10) {\n      this.logger.info('MessageService', `Fetching notifications, refresh: ${refresh}, page: ${page}, limit: ${limit}`);\n      this.logger.debug('MessageService', 'Using query', {\n        query: GET_NOTIFICATIONS_QUERY\n      });\n      // Si refresh est true, réinitialiser la pagination mais ne pas vider le cache\n      // pour conserver les suppressions locales\n      if (refresh) {\n        this.logger.debug('MessageService', 'Resetting pagination due to refresh');\n        this.notificationPagination.currentPage = 1;\n        this.notificationPagination.hasMoreNotifications = true;\n      }\n      // Mettre à jour les paramètres de pagination\n      this.notificationPagination.currentPage = page;\n      this.notificationPagination.limit = limit;\n      // Récupérer les IDs des notifications supprimées du localStorage\n      const deletedNotificationIds = this.getDeletedNotificationIds();\n      this.logger.debug('MessageService', `Found ${deletedNotificationIds.size} deleted notification IDs in localStorage`);\n      return this.apollo.watchQuery({\n        query: GET_NOTIFICATIONS_QUERY,\n        variables: {\n          page: page,\n          limit: limit\n        },\n        fetchPolicy: refresh ? 'network-only' : 'cache-first'\n      }).valueChanges.pipe(map(result => {\n        this.logger.debug('MessageService', 'Notifications response received');\n        if (result.errors) {\n          this.logger.error('MessageService', 'GraphQL errors:', result.errors);\n          throw new Error(result.errors.map(e => e.message).join(', '));\n        }\n        const notifications = result.data?.getUserNotifications || [];\n        this.logger.debug('MessageService', `Received ${notifications.length} notifications from server for page ${page}`);\n        // Vérifier s'il y a plus de notifications à charger\n        this.notificationPagination.hasMoreNotifications = notifications.length >= limit;\n        if (notifications.length === 0) {\n          this.logger.info('MessageService', 'No notifications received from server');\n          this.notificationPagination.hasMoreNotifications = false;\n        }\n        // Filtrer les notifications supprimées\n        const filteredNotifications = notifications.filter(notif => !deletedNotificationIds.has(notif.id));\n        this.logger.debug('MessageService', `Filtered out ${notifications.length - filteredNotifications.length} deleted notifications`);\n        // Afficher les notifications reçues pour le débogage\n        filteredNotifications.forEach((notif, index) => {\n          console.log(`Notification ${index + 1} (page ${page}):`, {\n            id: notif.id || notif._id,\n            type: notif.type,\n            content: notif.content,\n            isRead: notif.isRead\n          });\n        });\n        // Vérifier si les notifications existent déjà dans le cache avant de les ajouter\n        // Mettre à jour le cache avec les nouvelles notifications\n        this.updateCache(filteredNotifications);\n        // Récupérer toutes les notifications du cache\n        const cachedNotifications = Array.from(this.notificationCache.values());\n        console.log(`Total notifications in cache after update: ${cachedNotifications.length}`);\n        // Mettre à jour le BehaviorSubject avec toutes les notifications\n        this.notifications.next(cachedNotifications);\n        // Mettre à jour le compteur de notifications non lues\n        this.updateUnreadCount();\n        // Sauvegarder les notifications dans le localStorage\n        this.saveNotificationsToLocalStorage();\n        return cachedNotifications;\n      }), catchError(error => {\n        this.logger.error('MessageService', 'Error loading notifications:', error);\n        if (error.graphQLErrors) {\n          this.logger.error('MessageService', 'GraphQL errors:', error.graphQLErrors);\n        }\n        if (error.networkError) {\n          this.logger.error('MessageService', 'Network error:', error.networkError);\n        }\n        return throwError(() => new Error('Failed to load notifications'));\n      }));\n    }\n    /**\n     * Récupère les IDs des notifications supprimées du localStorage\n     * @private\n     * @returns Set contenant les IDs des notifications supprimées\n     */\n    getDeletedNotificationIds() {\n      try {\n        const deletedIds = new Set();\n        const savedNotifications = localStorage.getItem('notifications');\n        // Si aucune notification n'est sauvegardée, retourner un ensemble vide\n        if (!savedNotifications) {\n          return deletedIds;\n        }\n        // Récupérer les IDs des notifications sauvegardées\n        const savedNotificationIds = new Set(JSON.parse(savedNotifications).map(n => n.id));\n        // Récupérer les notifications du serveur (si disponibles dans le cache Apollo)\n        const serverNotifications = this.apollo.client.readQuery({\n          query: GET_NOTIFICATIONS_QUERY\n        })?.getUserNotifications || [];\n        // Pour chaque notification du serveur, vérifier si elle est dans les notifications sauvegardées\n        serverNotifications.forEach(notification => {\n          if (!savedNotificationIds.has(notification.id)) {\n            deletedIds.add(notification.id);\n          }\n        });\n        return deletedIds;\n      } catch (error) {\n        this.logger.error('MessageService', 'Erreur lors de la récupération des IDs de notifications supprimées:', error);\n        return new Set();\n      }\n    }\n    // Méthode pour vérifier s'il y a plus de notifications à charger\n    hasMoreNotifications() {\n      return this.notificationPagination.hasMoreNotifications;\n    }\n    // Méthode pour charger la page suivante de notifications\n    loadMoreNotifications() {\n      const nextPage = this.notificationPagination.currentPage + 1;\n      return this.getNotifications(false, nextPage, this.notificationPagination.limit);\n    }\n    getNotificationById(id) {\n      return this.notifications$.pipe(map(notifications => notifications.find(n => n.id === id)), catchError(error => {\n        this.logger.error('Error finding notification:', error);\n        return throwError(() => new Error('Failed to find notification'));\n      }));\n    }\n    getNotificationCount() {\n      return this.notifications.value?.length || 0;\n    }\n    getNotificationAttachments(notificationId) {\n      return this.apollo.query({\n        query: GET_NOTIFICATIONS_ATTACHAMENTS,\n        variables: {\n          id: notificationId\n        },\n        fetchPolicy: 'network-only'\n      }).pipe(map(result => result.data?.getNotificationAttachments || []), catchError(error => {\n        this.logger.error('Error fetching notification attachments:', error);\n        return throwError(() => new Error('Failed to fetch attachments'));\n      }));\n    }\n    getUnreadNotifications() {\n      return this.notifications$.pipe(map(notifications => notifications.filter(n => !n.isRead)));\n    }\n    /**\n     * Supprime une notification\n     * @param notificationId ID de la notification à supprimer\n     * @returns Observable avec le résultat de l'opération\n     */\n    deleteNotification(notificationId) {\n      this.logger.debug('MessageService', `Suppression de la notification ${notificationId}`);\n      if (!notificationId) {\n        this.logger.warn('MessageService', 'ID de notification invalide');\n        return throwError(() => new Error('ID de notification invalide'));\n      }\n      // Supprimer localement d'abord pour une meilleure expérience utilisateur\n      this.notificationCache.delete(notificationId);\n      this.notifications.next(Array.from(this.notificationCache.values()));\n      this.updateUnreadCount();\n      this.saveNotificationsToLocalStorage();\n      // Appeler le backend pour supprimer la notification\n      return this.apollo.mutate({\n        mutation: DELETE_NOTIFICATION_MUTATION,\n        variables: {\n          notificationId\n        }\n      }).pipe(map(result => {\n        const response = result.data?.deleteNotification;\n        if (!response) {\n          throw new Error('Réponse de suppression invalide');\n        }\n        this.logger.debug('MessageService', 'Résultat de la suppression:', response);\n        return response;\n      }), catchError(error => {\n        this.logger.error('MessageService', 'Erreur lors de la suppression de la notification:', error);\n        // En cas d'erreur, on garde la suppression locale\n        return of({\n          success: true,\n          message: 'Notification supprimée localement (erreur serveur)'\n        });\n      }));\n    }\n    /**\n     * Sauvegarde les notifications dans le localStorage\n     * @private\n     */\n    saveNotificationsToLocalStorage() {\n      try {\n        const notifications = Array.from(this.notificationCache.values());\n        localStorage.setItem('notifications', JSON.stringify(notifications));\n        this.logger.debug('MessageService', 'Notifications sauvegardées localement');\n      } catch (error) {\n        this.logger.error('MessageService', 'Erreur lors de la sauvegarde des notifications:', error);\n      }\n    }\n    /**\n     * Supprime toutes les notifications de l'utilisateur\n     * @returns Observable avec le résultat de l'opération\n     */\n    deleteAllNotifications() {\n      this.logger.debug('MessageService', 'Suppression de toutes les notifications');\n      // Supprimer localement d'abord pour une meilleure expérience utilisateur\n      const count = this.notificationCache.size;\n      this.notificationCache.clear();\n      this.notifications.next([]);\n      this.notificationCount.next(0);\n      this.saveNotificationsToLocalStorage();\n      // Appeler le backend pour supprimer toutes les notifications\n      return this.apollo.mutate({\n        mutation: DELETE_ALL_NOTIFICATIONS_MUTATION\n      }).pipe(map(result => {\n        const response = result.data?.deleteAllNotifications;\n        if (!response) {\n          throw new Error('Réponse de suppression invalide');\n        }\n        this.logger.debug('MessageService', 'Résultat de la suppression de toutes les notifications:', response);\n        return response;\n      }), catchError(error => {\n        this.logger.error('MessageService', 'Erreur lors de la suppression de toutes les notifications:', error);\n        // En cas d'erreur, on garde la suppression locale\n        return of({\n          success: true,\n          count,\n          message: `${count} notifications supprimées localement (erreur serveur)`\n        });\n      }));\n    }\n    /**\n     * Supprime plusieurs notifications\n     * @param notificationIds IDs des notifications à supprimer\n     * @returns Observable avec le résultat de l'opération\n     */\n    deleteMultipleNotifications(notificationIds) {\n      this.logger.debug('MessageService', `Suppression de ${notificationIds.length} notifications`);\n      if (!notificationIds || notificationIds.length === 0) {\n        this.logger.warn('MessageService', 'Aucun ID de notification fourni');\n        return throwError(() => new Error('Aucun ID de notification fourni'));\n      }\n      // Supprimer localement d'abord pour une meilleure expérience utilisateur\n      let count = 0;\n      notificationIds.forEach(id => {\n        if (this.notificationCache.has(id)) {\n          this.notificationCache.delete(id);\n          count++;\n        }\n      });\n      this.notifications.next(Array.from(this.notificationCache.values()));\n      this.updateUnreadCount();\n      this.saveNotificationsToLocalStorage();\n      // Appeler le backend pour supprimer les notifications\n      return this.apollo.mutate({\n        mutation: DELETE_MULTIPLE_NOTIFICATIONS_MUTATION,\n        variables: {\n          notificationIds\n        }\n      }).pipe(map(result => {\n        const response = result.data?.deleteMultipleNotifications;\n        if (!response) {\n          throw new Error('Réponse de suppression invalide');\n        }\n        this.logger.debug('MessageService', 'Résultat de la suppression multiple:', response);\n        return response;\n      }), catchError(error => {\n        this.logger.error('MessageService', 'Erreur lors de la suppression multiple de notifications:', error);\n        // En cas d'erreur, on garde la suppression locale\n        return of({\n          success: count > 0,\n          count,\n          message: `${count} notifications supprimées localement (erreur serveur)`\n        });\n      }));\n    }\n    groupNotificationsByType() {\n      return this.notifications$.pipe(map(notifications => {\n        const groups = new Map();\n        notifications.forEach(notif => {\n          if (!groups.has(notif.type)) {\n            groups.set(notif.type, []);\n          }\n          groups.get(notif.type)?.push(notif);\n        });\n        return groups;\n      }));\n    }\n    markAsRead(notificationIds) {\n      this.logger.debug('MessageService', `Marking notifications as read: ${notificationIds?.join(', ') || 'none'}`);\n      if (!notificationIds || notificationIds.length === 0) {\n        this.logger.warn('MessageService', 'No notification IDs provided');\n        return of({\n          success: false,\n          readCount: 0,\n          remainingCount: this.notificationCount.value\n        });\n      }\n      // Vérifier que tous les IDs sont valides\n      const validIds = notificationIds.filter(id => id && typeof id === 'string' && id.trim() !== '');\n      if (validIds.length !== notificationIds.length) {\n        this.logger.error('MessageService', 'Some notification IDs are invalid', {\n          provided: notificationIds,\n          valid: validIds\n        });\n        return throwError(() => new Error('Some notification IDs are invalid'));\n      }\n      this.logger.debug('MessageService', 'Sending mutation to mark notifications as read', validIds);\n      // Mettre à jour localement d'abord pour une meilleure expérience utilisateur\n      this.updateNotificationStatus(validIds, true);\n      // Créer une réponse optimiste\n      const optimisticResponse = {\n        markNotificationsAsRead: {\n          success: true,\n          readCount: validIds.length,\n          remainingCount: Math.max(0, this.notificationCount.value - validIds.length)\n        }\n      };\n      // Afficher des informations de débogage supplémentaires\n      console.log('Sending markNotificationsAsRead mutation with variables:', {\n        notificationIds: validIds\n      });\n      console.log('Using mutation:', MARK_NOTIFICATION_READ_MUTATION);\n      return this.apollo.mutate({\n        mutation: MARK_NOTIFICATION_READ_MUTATION,\n        variables: {\n          notificationIds: validIds\n        },\n        optimisticResponse: optimisticResponse,\n        errorPolicy: 'all',\n        fetchPolicy: 'no-cache' // Ne pas utiliser le cache pour cette mutation\n      }).pipe(map(result => {\n        this.logger.debug('MessageService', 'Mutation result', result);\n        console.log('Mutation result:', result);\n        // Si nous avons des erreurs GraphQL, les logger mais continuer\n        if (result.errors) {\n          this.logger.error('MessageService', 'GraphQL errors:', result.errors);\n          console.error('GraphQL errors:', result.errors);\n        }\n        // Utiliser la réponse du serveur ou notre réponse optimiste\n        const response = result.data?.markNotificationsAsRead ?? optimisticResponse.markNotificationsAsRead;\n        return response;\n      }), catchError(error => {\n        this.logger.error('MessageService', 'Error marking notifications as read:', error);\n        console.error('Error in markAsRead:', error);\n        // En cas d'erreur, retourner quand même un succès simulé\n        // puisque nous avons déjà mis à jour l'interface utilisateur\n        return of({\n          success: true,\n          readCount: validIds.length,\n          remainingCount: Math.max(0, this.notificationCount.value - validIds.length)\n        });\n      }));\n    }\n    // --------------------------------------------------------------------------\n    // Section 3: Méthodes pour les Appels\n    // --------------------------------------------------------------------------\n    /**\n     * Initie un appel avec un autre utilisateur\n     * @param recipientId ID de l'utilisateur à appeler\n     * @param callType Type d'appel (audio, vidéo)\n     * @param conversationId ID de la conversation (optionnel)\n     * @param options Options d'appel (optionnel)\n     * @returns Observable avec les informations de l'appel\n     */\n    initiateCall(recipientId, callType, conversationId, options) {\n      return this.setupMediaDevices(callType).pipe(switchMap(stream => {\n        this.localStream = stream;\n        this.localStream$.next(stream);\n        // Créer une connexion peer\n        this.peerConnection = new RTCPeerConnection(this.rtcConfig);\n        // Ajouter les pistes audio/vidéo\n        stream.getTracks().forEach(track => {\n          this.peerConnection.addTrack(track, stream);\n        });\n        // Écouter les candidats ICE\n        this.peerConnection.onicecandidate = event => {\n          if (event.candidate) {\n            this.sendCallSignal(this.generateCallId(), 'ice-candidate', JSON.stringify(event.candidate));\n          }\n        };\n        // Écouter les pistes distantes\n        this.peerConnection.ontrack = event => {\n          if (!this.remoteStream) {\n            this.remoteStream = new MediaStream();\n            this.remoteStream$.next(this.remoteStream);\n          }\n          event.streams[0].getTracks().forEach(track => {\n            this.remoteStream.addTrack(track);\n          });\n        };\n        // Créer l'offre SDP\n        return from(this.peerConnection.createOffer()).pipe(switchMap(offer => {\n          return from(this.peerConnection.setLocalDescription(offer)).pipe(map(() => offer));\n        }));\n      }), switchMap(offer => {\n        // Générer un ID d'appel unique\n        const callId = this.generateCallId();\n        // Envoyer l'offre au serveur\n        return this.apollo.mutate({\n          mutation: INITIATE_CALL_MUTATION,\n          variables: {\n            recipientId,\n            callType,\n            callId,\n            offer: JSON.stringify(offer),\n            conversationId,\n            options\n          }\n        }).pipe(map(result => {\n          const call = result.data?.initiateCall;\n          if (!call) {\n            throw new Error('Failed to initiate call');\n          }\n          // Mettre à jour l'état de l'appel actif\n          this.activeCall.next(call);\n          // S'abonner aux signaux d'appel\n          const signalSub = this.subscribeToCallSignals(call.id).subscribe();\n          this.subscriptions.push(signalSub);\n          return call;\n        }));\n      }), catchError(error => {\n        this.logger.error('Error initiating call', error);\n        this.cleanupCall();\n        return throwError(() => new Error('Failed to initiate call'));\n      }));\n    }\n    /**\n     * Accepte un appel entrant\n     * @param incomingCall Appel entrant à accepter\n     * @returns Observable avec les informations de l'appel\n     */\n    acceptCall(incomingCall) {\n      this.stop('ringtone');\n      return this.setupMediaDevices(incomingCall.type).pipe(switchMap(stream => {\n        this.localStream = stream;\n        this.localStream$.next(stream);\n        // Créer une connexion peer\n        this.peerConnection = new RTCPeerConnection(this.rtcConfig);\n        // Ajouter les pistes audio/vidéo\n        stream.getTracks().forEach(track => {\n          this.peerConnection.addTrack(track, stream);\n        });\n        // Écouter les candidats ICE\n        this.peerConnection.onicecandidate = event => {\n          if (event.candidate) {\n            this.sendCallSignal(incomingCall.id, 'ice-candidate', JSON.stringify(event.candidate));\n          }\n        };\n        // Écouter les pistes distantes\n        this.peerConnection.ontrack = event => {\n          if (!this.remoteStream) {\n            this.remoteStream = new MediaStream();\n            this.remoteStream$.next(this.remoteStream);\n          }\n          event.streams[0].getTracks().forEach(track => {\n            this.remoteStream.addTrack(track);\n          });\n        };\n        // Définir l'offre distante\n        const offer = JSON.parse(incomingCall.offer);\n        return from(this.peerConnection.setRemoteDescription(new RTCSessionDescription(offer))).pipe(switchMap(() => from(this.peerConnection.createAnswer())), switchMap(answer => {\n          return from(this.peerConnection.setLocalDescription(answer)).pipe(map(() => answer));\n        }));\n      }), switchMap(answer => {\n        // Envoyer la réponse au serveur\n        return this.apollo.mutate({\n          mutation: ACCEPT_CALL_MUTATION,\n          variables: {\n            callId: incomingCall.id,\n            answer: JSON.stringify(answer)\n          }\n        }).pipe(map(result => {\n          const call = result.data?.acceptCall;\n          if (!call) {\n            throw new Error('Failed to accept call');\n          }\n          // Jouer le son de connexion\n          this.play('call-connected');\n          // Mettre à jour l'état de l'appel actif\n          this.activeCall.next({\n            ...call,\n            caller: incomingCall.caller,\n            type: incomingCall.type,\n            conversationId: incomingCall.conversationId\n          });\n          // S'abonner aux signaux d'appel\n          const signalSub = this.subscribeToCallSignals(incomingCall.id).subscribe();\n          this.subscriptions.push(signalSub);\n          // Effacer l'appel entrant\n          this.incomingCall.next(null);\n          return call;\n        }));\n      }), catchError(error => {\n        this.logger.error('Error accepting call', error);\n        this.cleanupCall();\n        return throwError(() => new Error('Failed to accept call'));\n      }));\n    }\n    /**\n     * Rejette un appel entrant\n     * @param callId ID de l'appel à rejeter\n     * @param reason Raison du rejet (optionnel)\n     * @returns Observable avec les informations de l'appel\n     */\n    rejectCall(callId, reason) {\n      this.stop('ringtone');\n      return this.apollo.mutate({\n        mutation: REJECT_CALL_MUTATION,\n        variables: {\n          callId,\n          reason\n        }\n      }).pipe(map(result => {\n        const call = result.data?.rejectCall;\n        if (!call) {\n          throw new Error('Failed to reject call');\n        }\n        // Effacer l'appel entrant\n        this.incomingCall.next(null);\n        return call;\n      }), catchError(error => {\n        this.logger.error('Error rejecting call', error);\n        return throwError(() => new Error('Failed to reject call'));\n      }));\n    }\n    /**\n     * Termine un appel en cours\n     * @param callId ID de l'appel à terminer\n     * @param feedback Commentaires sur l'appel (optionnel)\n     * @returns Observable avec les informations de l'appel\n     */\n    endCall(callId, feedback) {\n      this.stop('ringtone');\n      this.play('call-end');\n      return this.apollo.mutate({\n        mutation: END_CALL_MUTATION,\n        variables: {\n          callId,\n          feedback\n        }\n      }).pipe(map(result => {\n        const call = result.data?.endCall;\n        if (!call) {\n          throw new Error('Failed to end call');\n        }\n        // Nettoyer les ressources\n        this.cleanupCall();\n        // Mettre à jour l'état de l'appel actif\n        this.activeCall.next(null);\n        return call;\n      }), catchError(error => {\n        this.logger.error('Error ending call', error);\n        this.cleanupCall();\n        return throwError(() => new Error('Failed to end call'));\n      }));\n    }\n    /**\n     * Active ou désactive la caméra ou le micro\n     * @param callId ID de l'appel\n     * @param video État de la caméra (optionnel)\n     * @param audio État du micro (optionnel)\n     * @returns Observable avec le résultat de l'opération\n     */\n    toggleMedia(callId, video, audio) {\n      if (this.localStream) {\n        // Mettre à jour les pistes locales\n        if (video !== undefined) {\n          this.localStream.getVideoTracks().forEach(track => {\n            track.enabled = video;\n          });\n        }\n        if (audio !== undefined) {\n          this.localStream.getAudioTracks().forEach(track => {\n            track.enabled = audio;\n          });\n        }\n      }\n      return this.apollo.mutate({\n        mutation: TOGGLE_CALL_MEDIA_MUTATION,\n        variables: {\n          callId,\n          video,\n          audio\n        }\n      }).pipe(map(result => {\n        const success = result.data?.toggleCallMedia;\n        if (!success) {\n          throw new Error('Failed to toggle media');\n        }\n        return success;\n      }), catchError(error => {\n        this.logger.error('Error toggling media', error);\n        return throwError(() => new Error('Failed to toggle media'));\n      }));\n    }\n    /**\n     * S'abonne aux signaux d'appel\n     * @param callId ID de l'appel\n     * @returns Observable avec les signaux d'appel\n     */\n    subscribeToCallSignals(callId) {\n      return this.apollo.subscribe({\n        query: CALL_SIGNAL_SUBSCRIPTION,\n        variables: {\n          callId\n        }\n      }).pipe(map(({\n        data\n      }) => {\n        if (!data?.callSignal) {\n          throw new Error('No call signal received');\n        }\n        return data.callSignal;\n      }), tap(signal => {\n        this.callSignals.next(signal);\n        this.handleCallSignal(signal);\n      }), catchError(error => {\n        this.logger.error('Error in call signal subscription', error);\n        return throwError(() => new Error('Call signal subscription failed'));\n      }));\n    }\n    /**\n     * Envoie un signal d'appel\n     * @param callId ID de l'appel\n     * @param signalType Type de signal\n     * @param signalData Données du signal\n     * @returns Observable avec le résultat de l'opération\n     */\n    sendCallSignal(callId, signalType, signalData) {\n      return this.apollo.mutate({\n        mutation: SEND_CALL_SIGNAL_MUTATION,\n        variables: {\n          callId,\n          signalType,\n          signalData\n        }\n      }).pipe(map(result => {\n        const success = result.data?.sendCallSignal;\n        if (!success) {\n          throw new Error('Failed to send call signal');\n        }\n        return success;\n      }), catchError(error => {\n        this.logger.error('Error sending call signal', error);\n        return throwError(() => new Error('Failed to send call signal'));\n      }));\n    }\n    /**\n     * Gère un signal d'appel reçu\n     * @param signal Signal d'appel\n     */\n    handleCallSignal(signal) {\n      switch (signal.type) {\n        case 'ice-candidate':\n          this.handleIceCandidate(signal);\n          break;\n        case 'answer':\n          this.handleAnswer(signal);\n          break;\n        case 'end-call':\n          this.handleEndCall(signal);\n          break;\n        case 'reject':\n          this.handleRejectCall(signal);\n          break;\n        default:\n          this.logger.debug(`Unhandled signal type: ${signal.type}`, signal);\n      }\n    }\n    /**\n     * Gère un candidat ICE reçu\n     * @param signal Signal d'appel contenant un candidat ICE\n     */\n    handleIceCandidate(signal) {\n      if (!this.peerConnection) {\n        this.logger.error('No peer connection available for ICE candidate');\n        return;\n      }\n      try {\n        const candidate = JSON.parse(signal.data);\n        this.peerConnection.addIceCandidate(new RTCIceCandidate(candidate)).catch(error => {\n          this.logger.error('Error adding ICE candidate', error);\n        });\n      } catch (error) {\n        this.logger.error('Error parsing ICE candidate', error);\n      }\n    }\n    /**\n     * Gère une réponse SDP reçue\n     * @param signal Signal d'appel contenant une réponse SDP\n     */\n    handleAnswer(signal) {\n      if (!this.peerConnection) {\n        this.logger.error('No peer connection available for answer');\n        return;\n      }\n      try {\n        const answer = JSON.parse(signal.data);\n        this.peerConnection.setRemoteDescription(new RTCSessionDescription(answer)).catch(error => {\n          this.logger.error('Error setting remote description', error);\n        });\n      } catch (error) {\n        this.logger.error('Error parsing answer', error);\n      }\n    }\n    /**\n     * Gère la fin d'un appel\n     * @param signal Signal d'appel indiquant la fin de l'appel\n     */\n    handleEndCall(signal) {\n      this.stop('ringtone');\n      this.cleanupCall();\n      // Mettre à jour l'état de l'appel actif\n      const currentCall = this.activeCall.value;\n      if (currentCall && currentCall.id === signal.callId) {\n        this.activeCall.next({\n          ...currentCall,\n          status: CallStatus.ENDED,\n          endTime: new Date().toISOString()\n        });\n      }\n    }\n    /**\n     * Gère le rejet d'un appel\n     * @param signal Signal d'appel indiquant le rejet de l'appel\n     */\n    handleRejectCall(signal) {\n      this.stop('ringtone');\n      this.cleanupCall();\n      // Mettre à jour l'état de l'appel actif\n      const currentCall = this.activeCall.value;\n      if (currentCall && currentCall.id === signal.callId) {\n        this.activeCall.next({\n          ...currentCall,\n          status: CallStatus.REJECTED,\n          endTime: new Date().toISOString()\n        });\n      }\n    }\n    /**\n     * Nettoie les ressources d'appel\n     */\n    cleanupCall() {\n      if (this.localStream) {\n        this.localStream.getTracks().forEach(track => track.stop());\n        this.localStream = null;\n        this.localStream$.next(null);\n      }\n      if (this.peerConnection) {\n        this.peerConnection.close();\n        this.peerConnection = null;\n      }\n      this.remoteStream = null;\n      this.remoteStream$.next(null);\n    }\n    /**\n     * Configure les périphériques média pour un appel\n     * @param callType Type d'appel (audio, vidéo)\n     * @returns Observable avec le flux média\n     */\n    setupMediaDevices(callType) {\n      const constraints = {\n        audio: true,\n        video: callType !== CallType.AUDIO ? {\n          width: {\n            ideal: 1280\n          },\n          height: {\n            ideal: 720\n          }\n        } : false\n      };\n      return new Observable(observer => {\n        navigator.mediaDevices.getUserMedia(constraints).then(stream => {\n          observer.next(stream);\n          observer.complete();\n        }).catch(error => {\n          this.logger.error('Error accessing media devices', error);\n          observer.error(new Error('Failed to access media devices'));\n        });\n      });\n    }\n    /**\n     * Génère un ID d'appel unique\n     * @returns ID d'appel unique\n     */\n    generateCallId() {\n      return Date.now().toString() + Math.random().toString(36).substring(2, 9);\n    }\n    // --------------------------------------------------------------------------\n    // Section 4: Méthodes pour les Utilisateurs/Groupes\n    // --------------------------------------------------------------------------\n    // User methods\n    getAllUsers(forceRefresh = false, search, page = 1, limit = 10, sortBy = 'username', sortOrder = 'asc', isOnline) {\n      this.logger.info('MessageService', `Getting users with params: forceRefresh=${forceRefresh}, search=${search || '(empty)'}, page=${page}, limit=${limit}, sortBy=${sortBy}, sortOrder=${sortOrder}, isOnline=${isOnline}`);\n      const now = Date.now();\n      const cacheValid = !forceRefresh && this.usersCache.length > 0 && now - this.lastFetchTime <= this.CACHE_DURATION && !search && page === 1 && limit >= this.usersCache.length;\n      // Use cache only for first page with no filters\n      if (cacheValid) {\n        this.logger.debug('MessageService', `Using cached users (${this.usersCache.length} users)`);\n        return of([...this.usersCache]);\n      }\n      this.logger.debug('MessageService', `Fetching users from server with pagination, fetchPolicy=${forceRefresh ? 'network-only' : 'cache-first'}`);\n      return this.apollo.watchQuery({\n        query: GET_ALL_USER_QUERY,\n        variables: {\n          search,\n          page,\n          limit,\n          sortBy,\n          sortOrder,\n          isOnline: isOnline !== undefined ? isOnline : null\n        },\n        fetchPolicy: forceRefresh ? 'network-only' : 'cache-first'\n      }).valueChanges.pipe(map(result => {\n        this.logger.debug('MessageService', 'Users response received', result);\n        if (result.errors) {\n          this.logger.error('MessageService', 'GraphQL errors in getAllUsers:', result.errors);\n          throw new Error(result.errors.map(e => e.message).join(', '));\n        }\n        if (!result.data?.getAllUsers) {\n          this.logger.warn('MessageService', 'No users data received from server');\n          return [];\n        }\n        const paginatedResponse = result.data.getAllUsers;\n        // Log pagination metadata\n        this.logger.debug('MessageService', 'Pagination metadata:', {\n          totalCount: paginatedResponse.totalCount,\n          totalPages: paginatedResponse.totalPages,\n          currentPage: paginatedResponse.currentPage,\n          hasNextPage: paginatedResponse.hasNextPage,\n          hasPreviousPage: paginatedResponse.hasPreviousPage\n        });\n        // Normalize users with error handling\n        const users = [];\n        for (const user of paginatedResponse.users) {\n          try {\n            if (user) {\n              users.push(this.normalizeUser(user));\n            }\n          } catch (error) {\n            this.logger.warn('MessageService', `Error normalizing user, skipping:`, error);\n          }\n        }\n        this.logger.info('MessageService', `Received ${users.length} users from server (page ${paginatedResponse.currentPage} of ${paginatedResponse.totalPages})`);\n        // Update cache only for first page with no filters\n        if (!search && page === 1 && !isOnline) {\n          this.usersCache = [...users];\n          this.lastFetchTime = Date.now();\n          this.logger.debug('MessageService', `User cache updated with ${users.length} users`);\n        }\n        // Store pagination metadata in a property for component access\n        this.currentUserPagination = {\n          totalCount: paginatedResponse.totalCount,\n          totalPages: paginatedResponse.totalPages,\n          currentPage: paginatedResponse.currentPage,\n          hasNextPage: paginatedResponse.hasNextPage,\n          hasPreviousPage: paginatedResponse.hasPreviousPage\n        };\n        return users;\n      }), catchError(error => {\n        this.logger.error('MessageService', 'Error fetching users:', error);\n        if (error.graphQLErrors) {\n          this.logger.error('MessageService', 'GraphQL errors:', error.graphQLErrors);\n        }\n        if (error.networkError) {\n          this.logger.error('MessageService', 'Network error:', error.networkError);\n        }\n        // Return cache if available (only for first page)\n        if (this.usersCache.length > 0 && page === 1 && !search && !isOnline) {\n          this.logger.warn('MessageService', `Returning ${this.usersCache.length} cached users due to fetch error`);\n          return of([...this.usersCache]);\n        }\n        return throwError(() => new Error(`Failed to fetch users: ${error.message || 'Unknown error'}`));\n      }));\n    }\n    getOneUser(userId) {\n      return this.apollo.watchQuery({\n        query: GET_USER_QUERY,\n        variables: {\n          id: userId\n        },\n        fetchPolicy: 'network-only'\n      }).valueChanges.pipe(map(result => this.normalizeUser(result.data?.getOneUser)), catchError(error => {\n        this.logger.error('MessageService', 'Error fetching user:', error);\n        return throwError(() => new Error('Failed to fetch user'));\n      }));\n    }\n    getCurrentUser() {\n      return this.apollo.watchQuery({\n        query: GET_CURRENT_USER_QUERY,\n        fetchPolicy: 'network-only'\n      }).valueChanges.pipe(map(result => this.normalizeUser(result.data?.getCurrentUser)), catchError(error => {\n        this.logger.error('MessageService', 'Error fetching current user:', error);\n        return throwError(() => new Error('Failed to fetch current user'));\n      }));\n    }\n    setUserOnline(userId) {\n      return this.apollo.mutate({\n        mutation: SET_USER_ONLINE_MUTATION,\n        variables: {\n          userId\n        }\n      }).pipe(map(result => {\n        if (!result.data?.setUserOnline) throw new Error('Failed to set user online');\n        return this.normalizeUser(result.data.setUserOnline);\n      }), catchError(error => {\n        this.logger.error('MessageService', 'Error setting user online:', error);\n        return throwError(() => new Error('Failed to set user online'));\n      }));\n    }\n    setUserOffline(userId) {\n      return this.apollo.mutate({\n        mutation: SET_USER_OFFLINE_MUTATION,\n        variables: {\n          userId\n        }\n      }).pipe(map(result => {\n        if (!result.data?.setUserOffline) throw new Error('Failed to set user offline');\n        return this.normalizeUser(result.data.setUserOffline);\n      }), catchError(error => {\n        this.logger.error('MessageService', 'Error setting user offline:', error);\n        return throwError(() => new Error('Failed to set user offline'));\n      }));\n    }\n    // Group methods\n    getGroup(groupId) {\n      return this.apollo.watchQuery({\n        query: GET_GROUP_QUERY,\n        variables: {\n          id: groupId\n        },\n        fetchPolicy: 'network-only'\n      }).valueChanges.pipe(map(result => {\n        const group = result.data?.getGroup;\n        if (!group) throw new Error('Group not found');\n        return {\n          ...group,\n          participants: group.participants?.map(p => this.normalizeUser(p)) || [],\n          admins: group.admins?.map(a => this.normalizeUser(a)) || [],\n          createdAt: new Date(),\n          updatedAt: new Date()\n        };\n      }), catchError(error => {\n        this.logger.error('MessageService', 'Error fetching group:', error);\n        return throwError(() => new Error('Failed to fetch group'));\n      }));\n    }\n    getUserGroups(userId) {\n      return this.apollo.watchQuery({\n        query: GET_USER_GROUPS_QUERY,\n        variables: {\n          userId\n        },\n        fetchPolicy: 'network-only'\n      }).valueChanges.pipe(map(result => result.data?.getUserGroups?.map(group => ({\n        ...group,\n        participants: group.participants?.map(p => this.normalizeUser(p)) || [],\n        admins: group.admins?.map(a => this.normalizeUser(a)) || [],\n        createdAt: new Date(),\n        updatedAt: new Date()\n      })) || []), catchError(error => {\n        this.logger.error('MessageService', 'Error fetching user groups:', error);\n        return throwError(() => new Error('Failed to fetch user groups'));\n      }));\n    }\n    createGroup(name, participantIds, photo, description) {\n      const variables = photo ? {\n        name,\n        participantIds,\n        photo,\n        description\n      } : {\n        name,\n        participantIds,\n        description\n      };\n      const context = photo ? {\n        useMultipart: true,\n        file: photo\n      } : undefined;\n      return this.apollo.mutate({\n        mutation: CREATE_GROUP_MUTATION,\n        variables,\n        context,\n        refetchQueries: [{\n          query: GET_USER_GROUPS_QUERY,\n          variables: {\n            userId: this.getCurrentUserId()\n          }\n        }]\n      }).pipe(map(result => {\n        if (!result.data?.createGroup) throw new Error('Failed to create group');\n        return {\n          ...result.data.createGroup,\n          participants: result.data.createGroup.participants?.map(p => this.normalizeUser(p)) || [],\n          admins: result.data.createGroup.admins?.map(a => this.normalizeUser(a)) || []\n        };\n      }), catchError(error => {\n        this.logger.error('MessageService', 'Error creating group:', error);\n        return throwError(() => new Error('Failed to create group'));\n      }));\n    }\n    updateGroup(groupId, input) {\n      const context = input.photo ? {\n        useMultipart: true,\n        file: input.photo\n      } : undefined;\n      const {\n        photo,\n        ...inputWithoutPhoto\n      } = input;\n      return this.apollo.mutate({\n        mutation: UPDATE_GROUP_MUTATION,\n        variables: {\n          id: groupId,\n          input: inputWithoutPhoto\n        },\n        context,\n        refetchQueries: [{\n          query: GET_GROUP_QUERY,\n          variables: {\n            id: groupId\n          }\n        }, {\n          query: GET_USER_GROUPS_QUERY,\n          variables: {\n            userId: this.getCurrentUserId()\n          }\n        }]\n      }).pipe(map(result => {\n        if (!result.data?.updateGroup) throw new Error('Failed to update group');\n        return {\n          ...result.data.updateGroup,\n          participants: result.data.updateGroup.participants?.map(p => this.normalizeUser(p)) || [],\n          admins: result.data.updateGroup.admins?.map(a => this.normalizeUser(a)) || []\n        };\n      }), catchError(error => {\n        this.logger.error('MessageService', 'Error updating group:', error);\n        return throwError(() => new Error('Failed to update group'));\n      }));\n    }\n    // --------------------------------------------------------------------------\n    // Section 4: Subscriptions et Gestion Temps Réel\n    // --------------------------------------------------------------------------\n    subscribeToNewMessages(conversationId) {\n      // Vérifier si l'utilisateur est connecté avec un token valide\n      if (!this.isTokenValid()) {\n        this.logger.warn(\"Tentative d'abonnement aux messages avec un token invalide ou expiré\");\n        return of(null);\n      }\n      this.logger.debug(`Démarrage de l'abonnement aux nouveaux messages pour la conversation: ${conversationId}`);\n      const sub$ = this.apollo.subscribe({\n        query: MESSAGE_SENT_SUBSCRIPTION,\n        variables: {\n          conversationId\n        }\n      }).pipe(map(result => {\n        const msg = result.data?.messageSent;\n        if (!msg) {\n          this.logger.warn('No message payload received');\n          throw new Error('No message payload received');\n        }\n        // Vérifier que l'ID est présent\n        if (!msg.id && !msg._id) {\n          this.logger.warn('Message without ID received:', msg);\n          // Générer un ID temporaire si nécessaire\n          msg.id = `temp-${Date.now()}`;\n        }\n        try {\n          // Utiliser normalizeMessage pour une normalisation complète\n          const normalizedMessage = this.normalizeMessage(msg);\n          // Si c'est un message vocal, s'assurer qu'il est correctement traité\n          if (normalizedMessage.type === MessageType.AUDIO || normalizedMessage.attachments && normalizedMessage.attachments.some(att => att.type === 'audio')) {\n            this.logger.debug('MessageService', 'Voice message received in real-time', normalizedMessage);\n            // Mettre à jour la conversation avec le nouveau message\n            this.updateConversationWithNewMessage(conversationId, normalizedMessage);\n          }\n          return normalizedMessage;\n        } catch (err) {\n          this.logger.error('Error normalizing message:', err);\n          // Créer un message minimal mais valide\n          const minimalMessage = {\n            id: msg.id || msg._id || `temp-${Date.now()}`,\n            content: msg.content || '',\n            type: msg.type || MessageType.TEXT,\n            timestamp: this.safeDate(msg.timestamp),\n            isRead: false,\n            sender: msg.sender ? this.normalizeUser(msg.sender) : {\n              id: this.getCurrentUserId(),\n              username: 'Unknown'\n            }\n          };\n          return minimalMessage;\n        }\n      }), catchError(error => {\n        this.logger.error('MessageService', 'Message subscription error:', error);\n        // Retourner un observable vide au lieu de null\n        return EMPTY;\n      }),\n      // Filtrer les valeurs null\n      filter(message => !!message),\n      // Réessayer après un délai en cas d'erreur\n      retry(3));\n      const sub = sub$.subscribe({\n        next: message => {\n          // Traitement supplémentaire pour s'assurer que le message est bien affiché\n          this.logger.debug('MessageService', 'New message received:', message);\n          // Mettre à jour la conversation avec le nouveau message\n          this.updateConversationWithNewMessage(conversationId, message);\n        },\n        error: err => {\n          this.logger.error('Error in message subscription:', err);\n        }\n      });\n      this.subscriptions.push(sub);\n      return sub$;\n    }\n    /**\n     * Met à jour une conversation avec un nouveau message\n     * @param conversationId ID de la conversation\n     * @param message Nouveau message\n     */\n    updateConversationWithNewMessage(conversationId, message) {\n      // Forcer une mise à jour de la conversation en récupérant les données à jour\n      this.getConversation(conversationId).subscribe({\n        next: conversation => {\n          this.logger.debug('MessageService', `Conversation ${conversationId} refreshed with new message ${message.id}, has ${conversation?.messages?.length || 0} messages`);\n          // Émettre un événement pour informer les composants que la conversation a été mise à jour\n          this.activeConversation.next(conversationId);\n        },\n        error: error => {\n          this.logger.error('MessageService', `Error refreshing conversation ${conversationId}:`, error);\n        }\n      });\n    }\n    subscribeToUserStatus() {\n      // Vérifier si l'utilisateur est connecté avec un token valide\n      if (!this.isTokenValid()) {\n        this.logger.warn(\"Tentative d'abonnement au statut utilisateur avec un token invalide ou expiré\");\n        return throwError(() => new Error('Invalid or expired token'));\n      }\n      this.logger.debug(\"Démarrage de l'abonnement au statut utilisateur\");\n      const sub$ = this.apollo.subscribe({\n        query: USER_STATUS_SUBSCRIPTION\n      }).pipe(tap(result => this.logger.debug(\"Données reçues de l'abonnement au statut utilisateur:\", result)), map(result => {\n        const user = result.data?.userStatusChanged;\n        if (!user) {\n          this.logger.error('No status payload received');\n          throw new Error('No status payload received');\n        }\n        return this.normalizeUser(user);\n      }), catchError(error => {\n        this.logger.error('Status subscription error:', error);\n        return throwError(() => new Error('Status subscription failed'));\n      }), retry(3) // Réessayer 3 fois en cas d'erreur\n      );\n\n      const sub = sub$.subscribe();\n      this.subscriptions.push(sub);\n      return sub$;\n    }\n    subscribeToConversationUpdates(conversationId) {\n      const sub$ = this.apollo.subscribe({\n        query: CONVERSATION_UPDATED_SUBSCRIPTION,\n        variables: {\n          conversationId\n        }\n      }).pipe(map(result => {\n        const conv = result.data?.conversationUpdated;\n        if (!conv) throw new Error('No conversation payload received');\n        const normalizedConversation = {\n          ...conv,\n          participants: conv.participants?.map(p => this.normalizeUser(p)) || [],\n          lastMessage: conv.lastMessage ? {\n            ...conv.lastMessage,\n            sender: this.normalizeUser(conv.lastMessage.sender),\n            timestamp: this.safeDate(conv.lastMessage.timestamp),\n            readAt: conv.lastMessage.readAt ? this.safeDate(conv.lastMessage.readAt) : undefined,\n            // Conservez toutes les autres propriétés du message\n            id: conv.lastMessage.id,\n            content: conv.lastMessage.content,\n            type: conv.lastMessage.type,\n            isRead: conv.lastMessage.isRead\n            // ... autres propriétés nécessaires\n          } : null // On conserve null comme dans votre version originale\n        };\n\n        return normalizedConversation; // Assertion de type si nécessaire\n      }), catchError(error => {\n        this.logger.error('MessageService', 'Conversation subscription error:', error);\n        return throwError(() => new Error('Conversation subscription failed'));\n      }));\n      const sub = sub$.subscribe();\n      this.subscriptions.push(sub);\n      return sub$;\n    }\n    subscribeToTypingIndicator(conversationId) {\n      const sub$ = this.apollo.subscribe({\n        query: TYPING_INDICATOR_SUBSCRIPTION,\n        variables: {\n          conversationId\n        }\n      }).pipe(map(result => result.data?.typingIndicator), filter(Boolean), catchError(error => {\n        this.logger.error('MessageService', 'Typing indicator subscription error:', error);\n        return throwError(() => new Error('Typing indicator subscription failed'));\n      }));\n      const sub = sub$.subscribe();\n      this.subscriptions.push(sub);\n      return sub$;\n    }\n    isTokenValid() {\n      const token = localStorage.getItem('token');\n      if (!token) {\n        this.logger.warn('Aucun token trouvé');\n        return false;\n      }\n      try {\n        // Décoder le token JWT (format: header.payload.signature)\n        const parts = token.split('.');\n        if (parts.length !== 3) {\n          this.logger.warn('Format de token invalide');\n          return false;\n        }\n        // Décoder le payload (deuxième partie du token)\n        const payload = JSON.parse(atob(parts[1]));\n        // Vérifier l'expiration\n        if (!payload.exp) {\n          this.logger.warn(\"Token sans date d'expiration\");\n          return false;\n        }\n        const expirationDate = new Date(payload.exp * 1000);\n        const now = new Date();\n        if (expirationDate < now) {\n          this.logger.warn('Token expiré', {\n            expiration: expirationDate.toISOString(),\n            now: now.toISOString()\n          });\n          return false;\n        }\n        return true;\n      } catch (error) {\n        this.logger.error('Erreur lors de la vérification du token:', error);\n        return false;\n      }\n    }\n    subscribeToNotificationsRead() {\n      // Vérifier si l'utilisateur est connecté avec un token valide\n      if (!this.isTokenValid()) {\n        this.logger.warn(\"Tentative d'abonnement aux notifications avec un token invalide ou expiré\");\n        return of([]);\n      }\n      this.logger.debug(\"Démarrage de l'abonnement aux notifications lues\");\n      const sub$ = this.apollo.subscribe({\n        query: NOTIFICATIONS_READ_SUBSCRIPTION\n      }).pipe(tap(result => this.logger.debug(\"Données reçues de l'abonnement aux notifications lues:\", result)), map(result => {\n        const notificationIds = result.data?.notificationsRead || [];\n        this.logger.debug('Notifications marquées comme lues:', notificationIds);\n        this.updateNotificationStatus(notificationIds, true);\n        return notificationIds;\n      }), catchError(err => {\n        this.logger.error('Notifications read subscription error:', err);\n        // Retourner un tableau vide au lieu de propager l'erreur\n        return of([]);\n      }),\n      // Réessayer après un délai en cas d'erreur\n      retry(3) // Réessayer 3 fois en cas d'erreur\n      );\n\n      const sub = sub$.subscribe();\n      this.subscriptions.push(sub);\n      return sub$;\n    }\n    subscribeToNewNotifications() {\n      // Vérifier si l'utilisateur est connecté\n      const token = localStorage.getItem('token');\n      if (!token) {\n        this.logger.warn(\"Tentative d'abonnement aux notifications sans être connecté\");\n        // Créer un Observable vide plutôt que de retourner null\n        return EMPTY;\n      }\n      const source$ = this.apollo.subscribe({\n        query: NOTIFICATION_SUBSCRIPTION\n      });\n      const processed$ = source$.pipe(map(result => {\n        const notification = result.data?.notificationReceived;\n        if (!notification) {\n          throw new Error('No notification payload received');\n        }\n        const normalized = this.normalizeNotification(notification);\n        // Vérifier si cette notification existe déjà dans le cache\n        if (this.notificationCache.has(normalized.id)) {\n          this.logger.debug('MessageService', `Notification ${normalized.id} already exists in cache, skipping`);\n          // Utiliser une technique différente pour ignorer cette notification\n          throw new Error('Notification already exists in cache');\n        }\n        // Jouer le son de notification\n        this.playNotificationSound();\n        // Mettre à jour le cache et émettre immédiatement la nouvelle notification\n        this.updateNotificationCache(normalized);\n        this.logger.debug('MessageService', 'New notification received and processed', normalized);\n        return normalized;\n      }),\n      // Utiliser catchError pour gérer les erreurs spécifiques\n      catchError(err => {\n        // Si c'est l'erreur spécifique pour les notifications déjà existantes, on ignore silencieusement\n        if (err instanceof Error && err.message === 'Notification already exists in cache') {\n          return EMPTY;\n        }\n        this.logger.error('New notification subscription error:', err);\n        // Retourner un Observable vide au lieu de null\n        return EMPTY;\n      }));\n      const sub = processed$.subscribe({\n        next: notification => {\n          this.logger.debug('MessageService', 'Notification subscription next handler', notification);\n        },\n        error: error => {\n          this.logger.error('MessageService', 'Error in notification subscription', error);\n        }\n      });\n      this.subscriptions.push(sub);\n      return processed$;\n    }\n    // --------------------------------------------------------------------------\n    // Helpers et Utilitaires\n    // --------------------------------------------------------------------------\n    startCleanupInterval() {\n      this.cleanupInterval = setInterval(() => {\n        this.cleanupExpiredNotifications();\n      }, 3600000);\n    }\n    cleanupExpiredNotifications() {\n      const now = new Date();\n      const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);\n      let expiredCount = 0;\n      this.notificationCache.forEach((notification, id) => {\n        const notificationDate = new Date(notification.timestamp);\n        if (notificationDate < thirtyDaysAgo) {\n          this.notificationCache.delete(id);\n          expiredCount++;\n        }\n      });\n      if (expiredCount > 0) {\n        this.logger.debug(`Cleaned up ${expiredCount} expired notifications`);\n        this.notifications.next(Array.from(this.notificationCache.values()));\n        this.updateUnreadCount();\n      }\n    }\n    getCurrentUserId() {\n      return localStorage.getItem('userId') || '';\n    }\n    normalizeMessage(message) {\n      if (!message) {\n        this.logger.error('[MessageService] Cannot normalize null or undefined message');\n        throw new Error('Message object is required');\n      }\n      try {\n        // Vérification des champs obligatoires\n        if (!message.id && !message._id) {\n          this.logger.error('[MessageService] Message ID is missing', undefined, message);\n          throw new Error('Message ID is required');\n        }\n        // Normaliser le sender avec gestion d'erreur\n        let normalizedSender;\n        try {\n          normalizedSender = message.sender ? this.normalizeUser(message.sender) : undefined;\n        } catch (error) {\n          this.logger.warn('[MessageService] Error normalizing message sender, using default values', error);\n          normalizedSender = {\n            _id: message.senderId || 'unknown',\n            id: message.senderId || 'unknown',\n            username: 'Unknown User',\n            email: '<EMAIL>',\n            role: 'user',\n            isActive: true\n          };\n        }\n        // Normaliser le receiver si présent\n        let normalizedReceiver;\n        if (message.receiver) {\n          try {\n            normalizedReceiver = this.normalizeUser(message.receiver);\n          } catch (error) {\n            this.logger.warn('[MessageService] Error normalizing message receiver, using default values', error);\n            normalizedReceiver = {\n              _id: message.receiverId || 'unknown',\n              id: message.receiverId || 'unknown',\n              username: 'Unknown User',\n              email: '<EMAIL>',\n              role: 'user',\n              isActive: true\n            };\n          }\n        }\n        // Normaliser les pièces jointes si présentes\n        const normalizedAttachments = message.attachments?.map(att => ({\n          id: att.id || att._id || `attachment-${Date.now()}`,\n          url: att.url || '',\n          type: att.type || 'unknown',\n          name: att.name || 'attachment',\n          size: att.size || 0,\n          duration: att.duration || 0\n        })) || [];\n        // Construire le message normalisé\n        const normalizedMessage = {\n          ...message,\n          _id: message.id || message._id,\n          id: message.id || message._id,\n          content: message.content || '',\n          sender: normalizedSender,\n          timestamp: this.normalizeDate(message.timestamp),\n          readAt: message.readAt ? this.normalizeDate(message.readAt) : undefined,\n          attachments: normalizedAttachments,\n          metadata: message.metadata || null\n        };\n        // Ajouter le receiver seulement s'il existe\n        if (normalizedReceiver) {\n          normalizedMessage.receiver = normalizedReceiver;\n        }\n        this.logger.debug('[MessageService] Message normalized successfully', {\n          messageId: normalizedMessage.id,\n          senderId: normalizedMessage.sender?.id\n        });\n        return normalizedMessage;\n      } catch (error) {\n        this.logger.error('[MessageService] Error normalizing message:', error instanceof Error ? error : new Error(String(error)), message);\n        throw new Error(`Failed to normalize message: ${error instanceof Error ? error.message : String(error)}`);\n      }\n    }\n    normalizeNotMessage(message) {\n      return {\n        ...message,\n        ...(message.attachments && {\n          attachments: message.attachments.map(att => ({\n            url: att.url,\n            type: att.type,\n            ...(att.name && {\n              name: att.name\n            }),\n            ...(att.size && {\n              size: att.size\n            })\n          }))\n        })\n      };\n    }\n    normalizeUser(user) {\n      if (!user) {\n        throw new Error('User object is required');\n      }\n      // Vérification des champs obligatoires avec valeurs par défaut\n      const userId = user.id || user._id;\n      if (!userId) {\n        throw new Error('User ID is required');\n      }\n      // Utiliser des valeurs par défaut pour les champs manquants\n      const username = user.username || 'Unknown User';\n      const email = user.email || `user-${userId}@example.com`;\n      const isActive = user.isActive !== undefined && user.isActive !== null ? user.isActive : true;\n      const role = user.role || 'user';\n      // Construire l'objet utilisateur normalisé\n      return {\n        _id: userId,\n        id: userId,\n        username: username,\n        email: email,\n        role: role,\n        isActive: isActive,\n        // Champs optionnels\n        image: user.image ?? null,\n        bio: user.bio,\n        isOnline: user.isOnline || false,\n        lastActive: user.lastActive ? new Date(user.lastActive) : undefined,\n        createdAt: user.createdAt ? new Date(user.createdAt) : undefined,\n        updatedAt: user.updatedAt ? new Date(user.updatedAt) : undefined,\n        followingCount: user.followingCount,\n        followersCount: user.followersCount,\n        postCount: user.postCount\n      };\n    }\n    normalizeConversation(conv) {\n      if (!conv) {\n        this.logger.error('[MessageService] Cannot normalize null or undefined conversation');\n        throw new Error('Conversation object is required');\n      }\n      try {\n        // Vérification des champs obligatoires\n        if (!conv.id && !conv._id) {\n          this.logger.error('[MessageService] Conversation ID is missing', undefined, conv);\n          throw new Error('Conversation ID is required');\n        }\n        // Normaliser les participants avec gestion d'erreur\n        const normalizedParticipants = [];\n        if (conv.participants && Array.isArray(conv.participants)) {\n          for (const participant of conv.participants) {\n            try {\n              if (participant) {\n                normalizedParticipants.push(this.normalizeUser(participant));\n              }\n            } catch (error) {\n              this.logger.warn('[MessageService] Error normalizing participant, skipping', error);\n            }\n          }\n        } else {\n          this.logger.warn('[MessageService] Conversation has no participants or invalid participants array', conv);\n        }\n        // Normaliser les messages avec gestion d'erreur\n        const normalizedMessages = [];\n        if (conv.messages && Array.isArray(conv.messages)) {\n          this.logger.debug('[MessageService] Processing conversation messages', {\n            count: conv.messages.length\n          });\n          for (const message of conv.messages) {\n            try {\n              if (message) {\n                const normalizedMessage = this.normalizeMessage(message);\n                this.logger.debug('[MessageService] Successfully normalized message', {\n                  messageId: normalizedMessage.id,\n                  content: normalizedMessage.content?.substring(0, 20),\n                  sender: normalizedMessage.sender?.username\n                });\n                normalizedMessages.push(normalizedMessage);\n              }\n            } catch (error) {\n              this.logger.warn('[MessageService] Error normalizing message in conversation, skipping', error);\n            }\n          }\n        } else {\n          this.logger.debug('[MessageService] No messages found in conversation or invalid messages array');\n        }\n        // Normaliser le dernier message avec gestion d'erreur\n        let normalizedLastMessage = null;\n        if (conv.lastMessage) {\n          try {\n            normalizedLastMessage = this.normalizeMessage(conv.lastMessage);\n          } catch (error) {\n            this.logger.warn('[MessageService] Error normalizing last message, using null', error);\n          }\n        }\n        // Construire la conversation normalisée\n        const normalizedConversation = {\n          ...conv,\n          _id: conv.id || conv._id,\n          id: conv.id || conv._id,\n          participants: normalizedParticipants,\n          messages: normalizedMessages,\n          lastMessage: normalizedLastMessage,\n          unreadCount: conv.unreadCount || 0,\n          isGroup: !!conv.isGroup,\n          createdAt: this.normalizeDate(conv.createdAt),\n          updatedAt: this.normalizeDate(conv.updatedAt)\n        };\n        this.logger.debug('[MessageService] Conversation normalized successfully', {\n          conversationId: normalizedConversation.id,\n          participantCount: normalizedParticipants.length,\n          messageCount: normalizedMessages.length\n        });\n        return normalizedConversation;\n      } catch (error) {\n        this.logger.error('[MessageService] Error normalizing conversation:', error instanceof Error ? error : new Error(String(error)), conv);\n        throw new Error(`Failed to normalize conversation: ${error instanceof Error ? error.message : String(error)}`);\n      }\n    }\n    normalizeDate(date) {\n      if (!date) return new Date();\n      try {\n        return typeof date === 'string' ? new Date(date) : date;\n      } catch (error) {\n        this.logger.warn(`Failed to parse date: ${date}`, error);\n        return new Date();\n      }\n    }\n    // Méthode sécurisée pour créer une date à partir d'une valeur potentiellement undefined\n    safeDate(date) {\n      if (!date) return new Date();\n      try {\n        return typeof date === 'string' ? new Date(date) : date;\n      } catch (error) {\n        this.logger.warn(`Failed to create safe date: ${date}`, error);\n        return new Date();\n      }\n    }\n    normalizeNotification(notification) {\n      this.logger.debug('MessageService', 'Normalizing notification', notification);\n      if (!notification) {\n        this.logger.error('MessageService', 'Notification is null or undefined');\n        throw new Error('Notification is required');\n      }\n      // Vérifier et normaliser l'ID\n      const notificationId = notification.id || notification._id;\n      if (!notificationId) {\n        this.logger.error('MessageService', 'Notification ID is missing', notification);\n        throw new Error('Notification ID is required');\n      }\n      if (!notification.timestamp) {\n        this.logger.warn('MessageService', 'Notification timestamp is missing, using current time', notification);\n        notification.timestamp = new Date();\n      }\n      try {\n        const normalized = {\n          ...notification,\n          _id: notificationId,\n          id: notificationId,\n          timestamp: new Date(notification.timestamp),\n          ...(notification.senderId && {\n            senderId: this.normalizeSender(notification.senderId)\n          }),\n          ...(notification.message && {\n            message: this.normalizeNotMessage(notification.message)\n          })\n        };\n        this.logger.debug('MessageService', 'Normalized notification result', normalized);\n        return normalized;\n      } catch (error) {\n        this.logger.error('MessageService', 'Error in normalizeNotification', error);\n        throw error;\n      }\n    }\n    normalizeSender(sender) {\n      return {\n        id: sender.id,\n        username: sender.username,\n        ...(sender.image && {\n          image: sender.image\n        })\n      };\n    }\n    updateCache(notifications) {\n      this.logger.debug('MessageService', `Updating notification cache with ${notifications.length} notifications`);\n      if (notifications.length === 0) {\n        this.logger.warn('MessageService', 'No notifications to update in cache');\n        return;\n      }\n      console.log(`Starting to update cache with ${notifications.length} notifications`);\n      // Vérifier si les notifications ont des IDs valides\n      const validNotifications = notifications.filter(notif => notif && (notif.id || notif._id));\n      if (validNotifications.length !== notifications.length) {\n        console.warn(`Found ${notifications.length - validNotifications.length} notifications without valid IDs`);\n      }\n      // Traiter chaque notification\n      validNotifications.forEach((notif, index) => {\n        try {\n          // S'assurer que la notification a un ID\n          const notifId = notif.id || notif._id;\n          if (!notifId) {\n            console.error('Notification without ID:', notif);\n            return;\n          }\n          // Normaliser la notification\n          const normalized = this.normalizeNotification(notif);\n          // Vérifier si cette notification existe déjà dans le cache\n          if (this.notificationCache.has(normalized.id)) {\n            console.log(`Notification ${normalized.id} already exists in cache, skipping`);\n            return;\n          }\n          // Ajouter au cache\n          this.notificationCache.set(normalized.id, normalized);\n          console.log(`Added notification ${normalized.id} to cache`);\n        } catch (error) {\n          console.error(`Error processing notification ${index + 1}:`, error);\n          console.error('Problematic notification:', notif);\n        }\n      });\n      console.log(`Notification cache updated, now contains ${this.notificationCache.size} notifications`);\n      // Sauvegarder les notifications dans le localStorage après la mise à jour du cache\n      this.saveNotificationsToLocalStorage();\n    }\n    updateUnreadCount() {\n      const count = Array.from(this.notificationCache.values()).filter(n => !n.isRead).length;\n      this.notificationCount.next(count);\n    }\n    updateNotificationCache(notification) {\n      // Vérifier si la notification existe déjà dans le cache (pour éviter les doublons)\n      if (!this.notificationCache.has(notification.id)) {\n        this.notificationCache.set(notification.id, notification);\n        this.notifications.next(Array.from(this.notificationCache.values()));\n        this.updateUnreadCount();\n        // Sauvegarder les notifications dans le localStorage après chaque mise à jour\n        this.saveNotificationsToLocalStorage();\n      } else {\n        this.logger.debug('MessageService', `Notification ${notification.id} already exists in cache, skipping`);\n      }\n    }\n    updateNotificationStatus(ids, isRead) {\n      ids.forEach(id => {\n        const notif = this.notificationCache.get(id);\n        if (notif) {\n          this.notificationCache.set(id, {\n            ...notif,\n            isRead\n          });\n        }\n      });\n      this.notifications.next(Array.from(this.notificationCache.values()));\n      this.updateUnreadCount();\n    }\n    // Typing indicators\n    startTyping(conversationId) {\n      const userId = this.getCurrentUserId();\n      if (!userId) {\n        this.logger.warn('MessageService', 'Cannot start typing: no user ID');\n        return of(false);\n      }\n      return this.apollo.mutate({\n        mutation: START_TYPING_MUTATION,\n        variables: {\n          input: {\n            conversationId,\n            userId\n          }\n        }\n      }).pipe(map(result => result.data?.startTyping || false), catchError(error => {\n        this.logger.error('MessageService', 'Error starting typing indicator', error);\n        return throwError(() => new Error('Failed to start typing indicator'));\n      }));\n    }\n    stopTyping(conversationId) {\n      const userId = this.getCurrentUserId();\n      if (!userId) {\n        this.logger.warn('MessageService', 'Cannot stop typing: no user ID');\n        return of(false);\n      }\n      return this.apollo.mutate({\n        mutation: STOP_TYPING_MUTATION,\n        variables: {\n          input: {\n            conversationId,\n            userId\n          }\n        }\n      }).pipe(map(result => result.data?.stopTyping || false), catchError(error => {\n        this.logger.error('MessageService', 'Error stopping typing indicator', error);\n        return throwError(() => new Error('Failed to stop typing indicator'));\n      }));\n    }\n    // destroy\n    cleanupSubscriptions() {\n      this.subscriptions.forEach(sub => sub.unsubscribe());\n      this.subscriptions = [];\n      if (this.cleanupInterval) {\n        clearInterval(this.cleanupInterval);\n      }\n      this.notificationCache.clear();\n      this.logger.debug('NotificationService destroyed');\n    }\n    ngOnDestroy() {\n      this.cleanupSubscriptions();\n    }\n    static {\n      this.ɵfac = function MessageService_Factory(t) {\n        return new (t || MessageService)(i0.ɵɵinject(i1.Apollo), i0.ɵɵinject(i2.LoggerService), i0.ɵɵinject(i0.NgZone));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: MessageService,\n        factory: MessageService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return MessageService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}