{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/equipe.service\";\nimport * as i2 from \"src/app/services/authuser.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"../ai-chat/ai-chat.component\";\nconst _c0 = function (a0, a1, a2) {\n  return {\n    \"bg-primary\": a0,\n    \"bg-success\": a1,\n    \"bg-secondary\": a2\n  };\n};\nconst _c1 = function (a0, a1, a2) {\n  return {\n    \"bi-mortarboard-fill\": a0,\n    \"bi-briefcase-fill\": a1,\n    \"bi-person-fill\": a2\n  };\n};\nconst _c2 = function (a0, a1) {\n  return {\n    \"bg-success bg-opacity-10 text-success\": a0,\n    \"bg-primary bg-opacity-10 text-primary\": a1\n  };\n};\nconst _c3 = function (a0, a1) {\n  return {\n    \"bi-person-fill-gear\": a0,\n    \"bi-person\": a1\n  };\n};\nfunction EquipeDetailComponent_div_0_div_106_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 74)(1, \"div\", 75)(2, \"div\", 76)(3, \"div\", 77);\n    i0.ɵɵelement(4, \"i\", 78);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\")(6, \"h6\", 79);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 80)(9, \"span\", 81);\n    i0.ɵɵelement(10, \"i\", 78);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"small\", 82);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(14, \"button\", 83);\n    i0.ɵɵlistener(\"click\", function EquipeDetailComponent_div_0_div_106_div_4_Template_button_click_14_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r10);\n      const membre_r8 = restoredCtx.$implicit;\n      const ctx_r9 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r9.removeMembreFromEquipe(membre_r8._id));\n    });\n    i0.ɵɵelement(15, \"i\", 84);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const membre_r8 = ctx.$implicit;\n    const ctx_r5 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(7, _c0, ctx_r5.getUserProfession(membre_r8.user) === \"etudiant\", ctx_r5.getUserProfession(membre_r8.user) === \"professeur\", !ctx_r5.getUserProfession(membre_r8.user)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(11, _c1, ctx_r5.getUserProfession(membre_r8.user) === \"etudiant\", ctx_r5.getUserProfession(membre_r8.user) === \"professeur\", !ctx_r5.getUserProfession(membre_r8.user)));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r5.getUserName(membre_r8.user));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(15, _c2, membre_r8.role === \"admin\", membre_r8.role === \"membre\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(18, _c3, membre_r8.role === \"admin\", membre_r8.role === \"membre\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", membre_r8.role === \"admin\" ? \"Administrateur\" : \"Membre\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r5.getUserProfession(membre_r8.user) === \"etudiant\" ? \"\\u00C9tudiant\" : ctx_r5.getUserProfession(membre_r8.user) === \"professeur\" ? \"Professeur\" : \"Utilisateur\");\n  }\n}\nfunction EquipeDetailComponent_div_0_div_106_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 85);\n    i0.ɵɵelement(1, \"i\", 86);\n    i0.ɵɵelementStart(2, \"div\");\n    i0.ɵɵtext(3, \"Aucun utilisateur disponible. Veuillez d'abord cr\\u00E9er des utilisateurs.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction EquipeDetailComponent_div_0_div_106_div_10_option_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 107);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r15 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", user_r15._id || user_r15.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate4(\" \", user_r15.firstName || \"\", \" \", user_r15.lastName || user_r15.name || user_r15.id, \" \", user_r15.email ? \"- \" + user_r15.email : \"\", \" \", user_r15.profession ? \"(\" + (user_r15.profession === \"etudiant\" ? \"\\u00C9tudiant\" : \"Professeur\") + \")\" : \"\", \" \");\n  }\n}\nfunction EquipeDetailComponent_div_0_div_106_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 87)(1, \"div\", 88)(2, \"label\", 89);\n    i0.ɵɵtext(3, \"Utilisateur\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"select\", 90, 91)(6, \"option\", 92);\n    i0.ɵɵtext(7, \"S\\u00E9lectionnez un utilisateur\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, EquipeDetailComponent_div_0_div_106_div_10_option_8_Template, 2, 5, \"option\", 93);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 88)(10, \"label\", 94);\n    i0.ɵɵtext(11, \"R\\u00F4le dans l'\\u00E9quipe\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 26)(13, \"div\", 95);\n    i0.ɵɵelement(14, \"input\", 96, 97);\n    i0.ɵɵelementStart(16, \"label\", 98);\n    i0.ɵɵelement(17, \"i\", 99);\n    i0.ɵɵtext(18, \" Membre \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 95);\n    i0.ɵɵelement(20, \"input\", 100, 101);\n    i0.ɵɵelementStart(22, \"label\", 102);\n    i0.ɵɵelement(23, \"i\", 103);\n    i0.ɵɵtext(24, \" Admin \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(25, \"div\", 104)(26, \"button\", 105);\n    i0.ɵɵlistener(\"click\", function EquipeDetailComponent_div_0_div_106_div_10_Template_button_click_26_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const _r11 = i0.ɵɵreference(5);\n      const _r13 = i0.ɵɵreference(15);\n      const ctx_r16 = i0.ɵɵnextContext(3);\n      ctx_r16.addMembre(_r11.value, _r13.checked ? \"membre\" : \"admin\");\n      return i0.ɵɵresetView(_r11.value = \"\");\n    });\n    i0.ɵɵelement(27, \"i\", 106);\n    i0.ɵɵtext(28, \" Ajouter \\u00E0 l'\\u00E9quipe \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const _r11 = i0.ɵɵreference(5);\n    const ctx_r7 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r7.availableUsers);\n    i0.ɵɵadvance(18);\n    i0.ɵɵproperty(\"disabled\", !_r11.value);\n  }\n}\nfunction EquipeDetailComponent_div_0_div_106_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65)(1, \"div\", 8)(2, \"div\", 66)(3, \"div\", 67);\n    i0.ɵɵtemplate(4, EquipeDetailComponent_div_0_div_106_div_4_Template, 16, 21, \"div\", 68);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 69)(6, \"h5\", 70);\n    i0.ɵɵelement(7, \"i\", 71);\n    i0.ɵɵtext(8, \" Ajouter un membre \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, EquipeDetailComponent_div_0_div_106_div_9_Template, 4, 0, \"div\", 72);\n    i0.ɵɵtemplate(10, EquipeDetailComponent_div_0_div_106_div_10_Template, 29, 2, \"div\", 73);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.teamMembers);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.availableUsers.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.availableUsers.length > 0);\n  }\n}\nfunction EquipeDetailComponent_div_0_ng_template_107_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 85);\n    i0.ɵɵelement(1, \"i\", 86);\n    i0.ɵɵelementStart(2, \"div\");\n    i0.ɵɵtext(3, \"Aucun utilisateur disponible. Veuillez d'abord cr\\u00E9er des utilisateurs.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction EquipeDetailComponent_div_0_ng_template_107_div_14_option_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 107);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r24 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", user_r24._id || user_r24.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate4(\" \", user_r24.firstName || \"\", \" \", user_r24.lastName || user_r24.name || user_r24.id, \" \", user_r24.email ? \"- \" + user_r24.email : \"\", \" \", user_r24.profession ? \"(\" + (user_r24.profession === \"etudiant\" ? \"\\u00C9tudiant\" : \"Professeur\") + \")\" : \"\", \" \");\n  }\n}\nfunction EquipeDetailComponent_div_0_ng_template_107_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r26 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 87)(1, \"div\", 88)(2, \"label\", 111);\n    i0.ɵɵtext(3, \"Utilisateur\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"select\", 112, 113)(6, \"option\", 92);\n    i0.ɵɵtext(7, \"S\\u00E9lectionnez un utilisateur\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, EquipeDetailComponent_div_0_ng_template_107_div_14_option_8_Template, 2, 5, \"option\", 93);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 88)(10, \"label\", 114);\n    i0.ɵɵtext(11, \"R\\u00F4le dans l'\\u00E9quipe\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 26)(13, \"div\", 95);\n    i0.ɵɵelement(14, \"input\", 115, 116);\n    i0.ɵɵelementStart(16, \"label\", 117);\n    i0.ɵɵelement(17, \"i\", 99);\n    i0.ɵɵtext(18, \" Membre \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 95);\n    i0.ɵɵelement(20, \"input\", 118, 119);\n    i0.ɵɵelementStart(22, \"label\", 120);\n    i0.ɵɵelement(23, \"i\", 103);\n    i0.ɵɵtext(24, \" Admin \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(25, \"div\", 104)(26, \"button\", 105);\n    i0.ɵɵlistener(\"click\", function EquipeDetailComponent_div_0_ng_template_107_div_14_Template_button_click_26_listener() {\n      i0.ɵɵrestoreView(_r26);\n      const _r20 = i0.ɵɵreference(5);\n      const _r22 = i0.ɵɵreference(15);\n      const ctx_r25 = i0.ɵɵnextContext(3);\n      ctx_r25.addMembre(_r20.value, _r22.checked ? \"membre\" : \"admin\");\n      return i0.ɵɵresetView(_r20.value = \"\");\n    });\n    i0.ɵɵelement(27, \"i\", 106);\n    i0.ɵɵtext(28, \" Ajouter \\u00E0 l'\\u00E9quipe \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const _r20 = i0.ɵɵreference(5);\n    const ctx_r19 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r19.availableUsers);\n    i0.ɵɵadvance(18);\n    i0.ɵɵproperty(\"disabled\", !_r20.value);\n  }\n}\nfunction EquipeDetailComponent_div_0_ng_template_107_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 66)(2, \"div\", 108)(3, \"div\", 109);\n    i0.ɵɵelement(4, \"i\", 110);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h5\", 82);\n    i0.ɵɵtext(6, \"Aucun membre dans cette \\u00E9quipe\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 82);\n    i0.ɵɵtext(8, \"Ajoutez des membres \\u00E0 l'\\u00E9quipe en utilisant le formulaire ci-contre.\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 69)(10, \"h5\", 70);\n    i0.ɵɵelement(11, \"i\", 71);\n    i0.ɵɵtext(12, \" Ajouter un membre \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, EquipeDetailComponent_div_0_ng_template_107_div_13_Template, 4, 0, \"div\", 72);\n    i0.ɵɵtemplate(14, EquipeDetailComponent_div_0_ng_template_107_div_14_Template, 29, 2, \"div\", 73);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.availableUsers.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.availableUsers.length > 0);\n  }\n}\nfunction EquipeDetailComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 4)(3, \"div\", 5)(4, \"div\", 6)(5, \"div\", 7)(6, \"div\", 8)(7, \"div\", 9)(8, \"h1\", 10);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"p\", 11);\n    i0.ɵɵtext(11, \"Gestion et collaboration d'\\u00E9quipe\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 12)(13, \"div\", 13);\n    i0.ɵɵelement(14, \"i\", 14);\n    i0.ɵɵelementStart(15, \"span\", 15);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"small\");\n    i0.ɵɵtext(18, \"Membres\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 13);\n    i0.ɵɵelement(20, \"i\", 16);\n    i0.ɵɵelementStart(21, \"span\", 15);\n    i0.ɵɵtext(22, \"0\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"small\");\n    i0.ɵɵtext(24, \"T\\u00E2ches\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 13);\n    i0.ɵɵelement(26, \"i\", 17);\n    i0.ɵɵelementStart(27, \"span\", 15);\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"small\");\n    i0.ɵɵtext(30, \"Cr\\u00E9\\u00E9e le\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(31, \"div\", 18)(32, \"h4\", 19);\n    i0.ɵɵelement(33, \"i\", 20);\n    i0.ɵɵtext(34, \"Actions rapides\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"div\", 21)(36, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function EquipeDetailComponent_div_0_Template_button_click_36_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r27 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r27.navigateToTasks());\n    });\n    i0.ɵɵelement(37, \"i\", 23);\n    i0.ɵɵtext(38, \" G\\u00E9rer les t\\u00E2ches \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function EquipeDetailComponent_div_0_Template_button_click_39_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r29 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r29.navigateToEditEquipe());\n    });\n    i0.ɵɵelement(40, \"i\", 25);\n    i0.ɵɵtext(41, \" Modifier l'\\u00E9quipe \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"div\", 26)(43, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function EquipeDetailComponent_div_0_Template_button_click_43_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r30 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r30.navigateToEquipeList());\n    });\n    i0.ɵɵelement(44, \"i\", 28);\n    i0.ɵɵtext(45, \" Retour \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function EquipeDetailComponent_div_0_Template_button_click_46_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r31 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r31.deleteEquipe());\n    });\n    i0.ɵɵelement(47, \"i\", 30);\n    i0.ɵɵtext(48, \" Supprimer \");\n    i0.ɵɵelementEnd()()()()()()()()();\n    i0.ɵɵelementStart(49, \"div\", 4)(50, \"div\", 5)(51, \"div\", 31)(52, \"div\", 7)(53, \"div\", 8)(54, \"div\", 32)(55, \"div\", 33);\n    i0.ɵɵelement(56, \"i\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(57, \"h3\", 35);\n    i0.ɵɵtext(58, \"\\u00C0 propos\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(59, \"p\", 36);\n    i0.ɵɵtext(60, \"D\\u00E9tails et informations sur l'\\u00E9quipe\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(61, \"div\", 37)(62, \"div\", 38)(63, \"h4\", 39);\n    i0.ɵɵtext(64, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(65, \"span\", 40);\n    i0.ɵɵelement(66, \"i\", 41);\n    i0.ɵɵtext(67);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(68, \"div\", 42)(69, \"p\", 43);\n    i0.ɵɵtext(70);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(71, \"div\", 44)(72, \"span\", 45);\n    i0.ɵɵelement(73, \"i\", 46);\n    i0.ɵɵtext(74);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(75, \"span\", 47);\n    i0.ɵɵelement(76, \"i\", 48);\n    i0.ɵɵtext(77);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(78, \"span\", 49);\n    i0.ɵɵelement(79, \"i\", 50);\n    i0.ɵɵtext(80, \" Gestion de projet \");\n    i0.ɵɵelementEnd()()()()()()()();\n    i0.ɵɵelementStart(81, \"div\", 4)(82, \"div\", 5)(83, \"div\", 31)(84, \"div\", 51)(85, \"div\", 52)(86, \"h3\", 53)(87, \"div\", 54);\n    i0.ɵɵelement(88, \"i\", 55);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(89, \" Assistant IA Gemini \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(90, \"span\", 56);\n    i0.ɵɵelement(91, \"i\", 57);\n    i0.ɵɵtext(92, \" G\\u00E9n\\u00E9ration de t\\u00E2ches intelligente \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(93, \"div\", 7);\n    i0.ɵɵelement(94, \"app-ai-chat\", 58);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(95, \"div\", 4)(96, \"div\", 5)(97, \"div\", 31)(98, \"div\", 59)(99, \"h3\", 53)(100, \"div\", 60);\n    i0.ɵɵelement(101, \"i\", 61);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(102, \" Membres de l'\\u00E9quipe \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(103, \"span\", 62);\n    i0.ɵɵtext(104);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(105, \"div\", 7);\n    i0.ɵɵtemplate(106, EquipeDetailComponent_div_0_div_106_Template, 11, 3, \"div\", 63);\n    i0.ɵɵtemplate(107, EquipeDetailComponent_div_0_ng_template_107_Template, 15, 2, \"ng-template\", null, 64, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const _r3 = i0.ɵɵreference(108);\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r0.equipe.name);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate((ctx_r0.equipe.members == null ? null : ctx_r0.equipe.members.length) || 0);\n    i0.ɵɵadvance(12);\n    i0.ɵɵtextInterpolate(ctx_r0.formatDate(ctx_r0.equipe.createdAt));\n    i0.ɵɵadvance(39);\n    i0.ɵɵtextInterpolate1(\" Admin: \", ctx_r0.equipe.admin ? ctx_r0.getUserName(ctx_r0.equipe.admin) || ctx_r0.equipe.admin : \"Non d\\u00E9fini\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.equipe.description || \"Aucune description disponible pour cette \\u00E9quipe.\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r0.equipe.members == null ? null : ctx_r0.equipe.members.length) || 0, \" membres \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" Cr\\u00E9\\u00E9e le \", ctx_r0.formatDate(ctx_r0.equipe.createdAt), \" \");\n    i0.ɵɵadvance(17);\n    i0.ɵɵproperty(\"team\", ctx_r0.equipe);\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.teamMembers.length || 0, \" membres \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.teamMembers && ctx_r0.teamMembers.length > 0)(\"ngIfElse\", _r3);\n  }\n}\nfunction EquipeDetailComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r33 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 121)(1, \"div\", 3)(2, \"div\", 122)(3, \"div\", 123)(4, \"div\", 124);\n    i0.ɵɵelement(5, \"i\", 125);\n    i0.ɵɵelementStart(6, \"div\", 126);\n    i0.ɵɵtext(7, \" \\u00C9quipe non trouv\\u00E9e ou en cours de chargement... \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 127);\n    i0.ɵɵlistener(\"click\", function EquipeDetailComponent_div_1_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r32 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r32.navigateToEquipeList());\n    });\n    i0.ɵɵelement(9, \"i\", 28);\n    i0.ɵɵtext(10, \" Retour \\u00E0 la liste des \\u00E9quipes \");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nexport let EquipeDetailComponent = /*#__PURE__*/(() => {\n  class EquipeDetailComponent {\n    constructor(equipeService, userService, route, router) {\n      this.equipeService = equipeService;\n      this.userService = userService;\n      this.route = route;\n      this.router = router;\n      this.equipe = null;\n      this.loading = false;\n      this.error = null;\n      this.equipeId = null;\n      this.newMembre = {\n        id: '',\n        role: 'membre'\n      };\n      this.availableUsers = [];\n      this.memberNames = {}; // Map pour stocker les noms des membres\n      this.teamMembers = []; // Liste des membres de l'équipe avec leurs détails\n    }\n\n    ngOnInit() {\n      this.equipeId = this.route.snapshot.paramMap.get('id');\n      // Charger tous les utilisateurs disponibles\n      this.loadUsers();\n      if (this.equipeId) {\n        this.loadEquipe(this.equipeId);\n      } else {\n        this.error = \"ID d'équipe non spécifié\";\n      }\n    }\n    // Méthode pour charger tous les utilisateurs\n    loadUsers() {\n      // TODO: Implémenter l'API pour récupérer les utilisateurs\n      // Pour l'instant, utiliser des données mockées\n      const mockUsers = [{\n        _id: 'user1',\n        username: 'john_doe',\n        email: '<EMAIL>',\n        role: 'admin',\n        isActive: true\n      }, {\n        _id: 'user2',\n        username: 'jane_smith',\n        email: '<EMAIL>',\n        role: 'student',\n        isActive: true\n      }, {\n        _id: 'user3',\n        username: 'bob_wilson',\n        email: '<EMAIL>',\n        role: 'teacher',\n        isActive: true\n      }];\n      // Simuler un délai d'API\n      setTimeout(() => {\n        // Stocker tous les utilisateurs pour la recherche de noms\n        const allUsers = [...mockUsers];\n        console.log('Tous les utilisateurs chargés (mock):', allUsers);\n        // Filtrer les utilisateurs disponibles (non membres de l'équipe)\n        if (this.teamMembers && this.teamMembers.length > 0) {\n          const memberUserIds = this.teamMembers.map(m => m.user);\n          this.availableUsers = mockUsers.filter(user => !memberUserIds.includes(user._id || user.id || ''));\n        } else {\n          this.availableUsers = mockUsers;\n        }\n        console.log('Utilisateurs disponibles:', this.availableUsers);\n        // Si l'équipe est déjà chargée, mettre à jour les noms des membres\n        if (this.equipe && this.equipe.members) {\n          this.updateMemberNames();\n        }\n      }, 500);\n    }\n    // Méthode pour mettre à jour les noms des membres\n    updateMemberNames() {\n      if (!this.equipe || !this.equipe.members) return;\n      this.equipe.members.forEach(membreId => {\n        const user = this.availableUsers.find(u => u._id === membreId || u.id === membreId);\n        if (user && user.name) {\n          this.memberNames[membreId] = user.name;\n        } else {\n          // Si l'utilisateur n'est pas trouvé dans la liste, essayer de le récupérer individuellement\n          // TODO: Implémenter getUser dans AuthuserService\n          // Pour l'instant, utiliser l'ID comme nom par défaut\n          this.memberNames[membreId] = membreId;\n        }\n      });\n    }\n    // Méthode pour obtenir le nom d'un membre\n    getMembreName(membreId) {\n      return this.memberNames[membreId] || membreId;\n    }\n    // Méthode pour obtenir le nom d'un utilisateur à partir de son ID\n    getUserName(userId) {\n      if (!userId) {\n        return 'Non défini';\n      }\n      const user = this.availableUsers.find(u => u._id === userId || u.id === userId);\n      if (user) {\n        if (user.firstName && user.lastName) {\n          return `${user.firstName} ${user.lastName}`;\n        } else if (user.name) {\n          return user.name;\n        }\n      }\n      return userId;\n    }\n    // Méthode pour obtenir la profession d'un utilisateur à partir de son ID\n    getUserProfession(userId) {\n      if (!userId) {\n        return '';\n      }\n      const user = this.availableUsers.find(u => u._id === userId || u.id === userId);\n      if (user) {\n        return user.profession || user.role || '';\n      }\n      return '';\n    }\n    loadEquipe(id) {\n      this.loading = true;\n      this.error = null;\n      this.equipeService.getEquipe(id).subscribe({\n        next: data => {\n          console.log(\"Détails de l'équipe chargés:\", data);\n          this.equipe = data;\n          // Charger les détails des membres de l'équipe\n          this.loadTeamMembers(id);\n          // Mettre à jour les noms des membres\n          if (this.equipe && this.equipe.members && this.equipe.members.length > 0) {\n            this.updateMemberNames();\n          }\n          this.loading = false;\n        },\n        error: error => {\n          console.error(\"Erreur lors du chargement des détails de l'équipe:\", error);\n          this.error = \"Impossible de charger les détails de l'équipe. Veuillez réessayer plus tard.\";\n          this.loading = false;\n        }\n      });\n    }\n    // Méthode pour charger les détails des membres de l'équipe\n    loadTeamMembers(teamId) {\n      this.equipeService.getTeamMembers(teamId).subscribe({\n        next: members => {\n          console.log('Détails des membres chargés:', members);\n          this.teamMembers = members;\n        },\n        error: error => {\n          console.error('Erreur lors du chargement des détails des membres:', error);\n        }\n      });\n    }\n    navigateToEditEquipe() {\n      if (this.equipeId) {\n        this.router.navigate(['/equipes/modifier', this.equipeId]);\n      }\n    }\n    navigateToEquipeList() {\n      this.router.navigate(['/equipes/liste']);\n    }\n    navigateToTasks() {\n      if (this.equipeId) {\n        this.router.navigate(['/equipes/tasks', this.equipeId]);\n      }\n    }\n    // Méthode pour formater les dates\n    formatDate(date) {\n      if (!date) {\n        return 'N/A';\n      }\n      try {\n        let dateObj;\n        if (typeof date === 'string') {\n          dateObj = new Date(date);\n        } else {\n          dateObj = date;\n        }\n        if (isNaN(dateObj.getTime())) {\n          return 'Date invalide';\n        }\n        // Format: JJ/MM/AAAA\n        return dateObj.toLocaleDateString('fr-FR', {\n          day: '2-digit',\n          month: '2-digit',\n          year: 'numeric'\n        });\n      } catch (error) {\n        console.error('Erreur lors du formatage de la date:', error);\n        return 'Erreur de date';\n      }\n    }\n    // Méthode pour ajouter un membre à l'équipe\n    addMembre(userId, role) {\n      console.log(`Ajout de l'utilisateur ${userId} avec le rôle ${role}`);\n      if (!this.equipeId || !userId) {\n        console.error(\"ID d'équipe ou ID d'utilisateur manquant\");\n        this.error = \"ID d'équipe ou ID d'utilisateur manquant\";\n        return;\n      }\n      // Vérifier si l'utilisateur est déjà membre de l'équipe\n      const isAlreadyMember = this.teamMembers.some(m => m.user === userId);\n      if (isAlreadyMember) {\n        this.error = \"Cet utilisateur est déjà membre de l'équipe\";\n        alert(\"Cet utilisateur est déjà membre de l'équipe\");\n        return;\n      }\n      // Créer l'objet membre avec le rôle spécifié\n      const membre = {\n        id: userId,\n        role: role || 'membre'\n      };\n      // Récupérer les informations de l'utilisateur pour afficher un message plus informatif\n      const userName = this.getUserName(userId);\n      const roleName = role === 'admin' ? 'administrateur' : 'membre';\n      this.equipeService.addMembreToEquipe(this.equipeId, membre).subscribe({\n        next: response => {\n          console.log(`Utilisateur \"${userName}\" ajouté comme ${roleName} avec succès:`, response);\n          // Afficher un message de succès\n          alert(`Utilisateur \"${userName}\" ajouté comme ${roleName} avec succès`);\n          // Recharger les membres de l'équipe\n          this.loadTeamMembers(this.equipeId);\n          // Recharger l'équipe pour mettre à jour la liste des membres\n          this.loadEquipe(this.equipeId);\n          // Mettre à jour la liste des utilisateurs disponibles\n          this.updateAvailableUsers();\n        },\n        error: error => {\n          console.error(\"Erreur lors de l'ajout de l'utilisateur comme membre:\", error);\n          this.error = `Impossible d'ajouter l'utilisateur \"${userName}\" comme ${roleName}. Veuillez réessayer plus tard.`;\n          alert(this.error);\n        }\n      });\n    }\n    // Méthode pour mettre à jour la liste des utilisateurs disponibles\n    updateAvailableUsers() {\n      // TODO: Implémenter l'API pour récupérer les utilisateurs\n      // Pour l'instant, utiliser les données mockées de loadUsers()\n      this.loadUsers();\n    }\n    // Ancienne méthode maintenue pour compatibilité\n    addMembreToEquipe() {\n      if (!this.equipeId || !this.newMembre.id) {\n        console.error(\"ID d'équipe ou ID de membre manquant\");\n        return;\n      }\n      this.addMembre(this.newMembre.id, this.newMembre.role || 'membre');\n    }\n    removeMembreFromEquipe(membreId) {\n      console.log('Méthode removeMembreFromEquipe appelée avec ID:', membreId);\n      if (!this.equipeId) {\n        console.error(\"ID d'équipe manquant\");\n        this.error = \"ID d'équipe manquant. Impossible de retirer le membre.\";\n        return;\n      }\n      // Dans notre implémentation actuelle, membre._id est l'ID de l'utilisateur\n      const userId = membreId;\n      // Récupérer le nom de l'utilisateur pour un message plus informatif\n      const userName = this.getUserName(userId);\n      console.log(`Tentative de retrait de l'utilisateur ${userId} (${userName}) de l'équipe ${this.equipeId}`);\n      if (confirm(`Êtes-vous sûr de vouloir retirer l'utilisateur \"${userName}\" de l'équipe?`)) {\n        console.log('Confirmation acceptée, suppression en cours...');\n        this.loading = true;\n        this.error = null;\n        this.equipeService.removeMembreFromEquipe(this.equipeId, userId).subscribe({\n          next: response => {\n            console.log(`Utilisateur \"${userName}\" retiré avec succès de l'équipe:`, response);\n            this.loading = false;\n            // Afficher un message de succès\n            alert(`Utilisateur \"${userName}\" retiré avec succès de l'équipe`);\n            // Recharger les membres de l'équipe\n            this.loadTeamMembers(this.equipeId);\n            // Recharger l'équipe pour mettre à jour la liste des membres\n            this.loadEquipe(this.equipeId);\n            // Mettre à jour la liste des utilisateurs disponibles\n            this.updateAvailableUsers();\n          },\n          error: error => {\n            console.error(`Erreur lors du retrait de l'utilisateur \"${userName}\":`, error);\n            this.loading = false;\n            this.error = `Impossible de retirer l'utilisateur \"${userName}\" de l'équipe: ${error.message || 'Erreur inconnue'}`;\n          }\n        });\n      } else {\n        console.log(\"Suppression annulée par l'utilisateur\");\n      }\n    }\n    deleteEquipe() {\n      console.log('Méthode deleteEquipe appelée');\n      if (!this.equipeId) {\n        console.error(\"ID d'équipe manquant\");\n        this.error = \"ID d'équipe manquant. Impossible de supprimer l'équipe.\";\n        return;\n      }\n      console.log(\"ID de l'équipe à supprimer:\", this.equipeId);\n      if (confirm(`Êtes-vous sûr de vouloir supprimer l'équipe \"${this.equipe?.name}\"? Cette action est irréversible.`)) {\n        console.log('Confirmation acceptée, suppression en cours...');\n        this.loading = true;\n        this.error = null;\n        this.equipeService.deleteEquipe(this.equipeId).subscribe({\n          next: () => {\n            console.log('Équipe supprimée avec succès');\n            this.loading = false;\n            alert('Équipe supprimée avec succès');\n            this.router.navigate(['/equipes/liste']);\n          },\n          error: error => {\n            console.error(\"Erreur lors de la suppression de l'équipe:\", error);\n            this.loading = false;\n            this.error = `Impossible de supprimer l'équipe: ${error.message || 'Erreur inconnue'}`;\n            alert(`Erreur lors de la suppression: ${this.error}`);\n          }\n        });\n      } else {\n        console.log(\"Suppression annulée par l'utilisateur\");\n      }\n    }\n    static {\n      this.ɵfac = function EquipeDetailComponent_Factory(t) {\n        return new (t || EquipeDetailComponent)(i0.ɵɵdirectiveInject(i1.EquipeService), i0.ɵɵdirectiveInject(i2.AuthuserService), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i3.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: EquipeDetailComponent,\n        selectors: [[\"app-equipe-detail\"]],\n        decls: 2,\n        vars: 2,\n        consts: [[\"class\", \"container-fluid py-5 bg-gradient-light\", 4, \"ngIf\"], [\"class\", \"container-fluid py-5 bg-light\", 4, \"ngIf\"], [1, \"container-fluid\", \"py-5\", \"bg-gradient-light\"], [1, \"container\"], [1, \"row\", \"mb-5\"], [1, \"col-12\"], [1, \"card\", \"border-0\", \"shadow-lg\", \"rounded-4\", \"overflow-hidden\"], [1, \"card-body\", \"p-0\"], [1, \"row\", \"g-0\"], [1, \"col-lg-8\", \"p-5\", 2, \"background\", \"linear-gradient(120deg, #6a11cb 0%, #2575fc 100%)\"], [1, \"display-4\", \"fw-bold\", \"text-white\", \"mb-2\"], [1, \"text-white-50\", \"lead\", \"mb-4\"], [1, \"d-flex\", \"gap-4\", \"mt-4\"], [1, \"bg-white\", \"bg-opacity-25\", \"rounded-4\", \"p-3\", \"text-white\", \"text-center\"], [1, \"bi\", \"bi-people-fill\", \"fs-3\", \"mb-2\", \"d-block\"], [1, \"fs-5\", \"fw-bold\", \"d-block\"], [1, \"bi\", \"bi-kanban\", \"fs-3\", \"mb-2\", \"d-block\"], [1, \"bi\", \"bi-calendar-check\", \"fs-3\", \"mb-2\", \"d-block\"], [1, \"col-lg-4\", \"bg-white\", \"p-4\", \"d-flex\", \"flex-column\", \"justify-content-center\"], [1, \"mb-4\", \"text-primary\"], [1, \"bi\", \"bi-lightning-charge-fill\", \"me-2\"], [1, \"d-grid\", \"gap-3\"], [1, \"btn\", \"btn-primary\", \"btn-lg\", \"rounded-3\", \"shadow-sm\", 3, \"click\"], [1, \"bi\", \"bi-kanban\", \"me-2\"], [1, \"btn\", \"btn-outline-primary\", \"rounded-3\", 3, \"click\"], [1, \"bi\", \"bi-pencil\", \"me-2\"], [1, \"d-flex\", \"gap-2\"], [1, \"btn\", \"btn-outline-secondary\", \"flex-grow-1\", \"rounded-3\", 3, \"click\"], [1, \"bi\", \"bi-arrow-left\", \"me-2\"], [1, \"btn\", \"btn-outline-danger\", \"rounded-3\", 3, \"click\"], [1, \"bi\", \"bi-trash\", \"me-2\"], [1, \"card\", \"border-0\", \"shadow-sm\", \"rounded-4\", \"overflow-hidden\", \"hover-card\"], [1, \"col-md-3\", \"bg-primary\", \"text-white\", \"p-4\", \"d-flex\", \"flex-column\", \"justify-content-center\", \"align-items-center\", \"text-center\"], [1, \"icon-circle\", \"bg-white\", \"text-primary\", \"mb-3\"], [1, \"bi\", \"bi-info-circle-fill\", \"fs-1\"], [1, \"mb-2\"], [1, \"mb-0\", \"text-white-50\"], [1, \"col-md-9\", \"p-4\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-4\"], [1, \"text-primary\", \"mb-0\"], [1, \"badge\", \"bg-light\", \"text-primary\", \"rounded-pill\", \"px-3\", \"py-2\"], [1, \"bi\", \"bi-person-fill-gear\", \"me-1\"], [1, \"description-box\", \"p-3\", \"bg-light\", \"rounded-4\", \"mb-4\"], [1, \"lead\", \"mb-0\"], [1, \"d-flex\", \"flex-wrap\", \"gap-2\", \"mt-4\"], [1, \"badge\", \"bg-primary\", \"bg-opacity-10\", \"text-primary\", \"rounded-pill\", \"px-3\", \"py-2\"], [1, \"bi\", \"bi-people-fill\", \"me-1\"], [1, \"badge\", \"bg-success\", \"bg-opacity-10\", \"text-success\", \"rounded-pill\", \"px-3\", \"py-2\"], [1, \"bi\", \"bi-calendar-check\", \"me-1\"], [1, \"badge\", \"bg-info\", \"bg-opacity-10\", \"text-info\", \"rounded-pill\", \"px-3\", \"py-2\"], [1, \"bi\", \"bi-kanban\", \"me-1\"], [1, \"card-header\", \"border-0\", \"py-4\", 2, \"background\", \"linear-gradient(45deg, #8e2de2, #4a00e0)\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"mb-0\", \"text-white\", \"d-flex\", \"align-items-center\"], [1, \"icon-circle\", \"bg-white\", \"text-primary\", \"me-3\"], [1, \"bi\", \"bi-robot\"], [1, \"badge\", \"bg-white\", \"text-primary\", \"rounded-pill\", \"px-3\", \"py-2\"], [1, \"bi\", \"bi-magic\", \"me-1\"], [3, \"team\"], [1, \"card-header\", \"border-0\", \"py-4\", \"d-flex\", \"justify-content-between\", \"align-items-center\", 2, \"background\", \"linear-gradient(45deg, #11998e, #38ef7d)\"], [1, \"icon-circle\", \"bg-white\", \"text-success\", \"me-3\"], [1, \"bi\", \"bi-people-fill\"], [1, \"badge\", \"bg-white\", \"text-success\", \"rounded-pill\", \"px-3\", \"py-2\"], [\"class\", \"p-0\", 4, \"ngIf\", \"ngIfElse\"], [\"noMembers\", \"\"], [1, \"p-0\"], [1, \"col-md-8\"], [1, \"member-grid\", \"p-4\"], [\"class\", \"member-card mb-3 p-3 rounded-4 shadow-sm transition\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-md-4\", \"bg-light\", \"p-4\"], [1, \"d-flex\", \"align-items-center\", \"mb-4\", \"text-success\"], [1, \"bi\", \"bi-person-plus-fill\", \"me-2\"], [\"class\", \"alert alert-info border-0 rounded-4 shadow-sm d-flex align-items-center\", 4, \"ngIf\"], [\"class\", \"add-member-form\", 4, \"ngIf\"], [1, \"member-card\", \"mb-3\", \"p-3\", \"rounded-4\", \"shadow-sm\", \"transition\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-start\"], [1, \"d-flex\", \"align-items-center\"], [1, \"member-avatar\", \"rounded-circle\", \"text-white\", \"me-3\", 3, \"ngClass\"], [1, \"bi\", 3, \"ngClass\"], [1, \"mb-0\", \"fw-bold\"], [1, \"d-flex\", \"align-items-center\", \"mt-1\"], [1, \"badge\", \"rounded-pill\", \"me-2\", 3, \"ngClass\"], [1, \"text-muted\"], [\"title\", \"Retirer de l'\\u00E9quipe\", 1, \"btn\", \"btn-sm\", \"btn-outline-danger\", \"rounded-circle\", 3, \"click\"], [1, \"bi\", \"bi-trash\"], [1, \"alert\", \"alert-info\", \"border-0\", \"rounded-4\", \"shadow-sm\", \"d-flex\", \"align-items-center\"], [1, \"bi\", \"bi-info-circle-fill\", \"fs-4\", \"me-3\", \"text-primary\"], [1, \"add-member-form\"], [1, \"mb-3\"], [\"for\", \"userSelect\", 1, \"form-label\", \"fw-medium\"], [\"id\", \"userSelect\", 1, \"form-select\", \"border-0\", \"shadow-sm\", \"rounded-4\", \"py-2\"], [\"userSelect\", \"\"], [\"value\", \"\", \"selected\", \"\", \"disabled\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"roleSelect\", 1, \"form-label\", \"fw-medium\"], [1, \"form-check\", \"flex-grow-1\"], [\"type\", \"radio\", \"name\", \"roleRadio\", \"id\", \"roleMembre\", \"value\", \"membre\", \"checked\", \"\", 1, \"form-check-input\"], [\"roleMembre\", \"\"], [\"for\", \"roleMembre\", 1, \"form-check-label\", \"w-100\", \"p-2\", \"border\", \"rounded-4\", \"text-center\"], [1, \"bi\", \"bi-person\", \"d-block\", \"fs-4\", \"mb-1\"], [\"type\", \"radio\", \"name\", \"roleRadio\", \"id\", \"roleAdmin\", \"value\", \"admin\", 1, \"form-check-input\"], [\"roleAdmin\", \"\"], [\"for\", \"roleAdmin\", 1, \"form-check-label\", \"w-100\", \"p-2\", \"border\", \"rounded-4\", \"text-center\"], [1, \"bi\", \"bi-person-fill-gear\", \"d-block\", \"fs-4\", \"mb-1\"], [1, \"d-grid\"], [\"type\", \"button\", 1, \"btn\", \"btn-success\", \"rounded-4\", \"py-2\", \"shadow-sm\", 3, \"disabled\", \"click\"], [1, \"bi\", \"bi-plus-circle\", \"me-2\"], [3, \"value\"], [1, \"text-center\", \"py-5\"], [1, \"empty-state-icon\", \"mb-4\"], [1, \"bi\", \"bi-people\", \"fs-1\", \"text-muted\"], [\"for\", \"userSelect2\", 1, \"form-label\", \"fw-medium\"], [\"id\", \"userSelect2\", 1, \"form-select\", \"border-0\", \"shadow-sm\", \"rounded-4\", \"py-2\"], [\"userSelect2\", \"\"], [\"for\", \"roleSelect2\", 1, \"form-label\", \"fw-medium\"], [\"type\", \"radio\", \"name\", \"roleRadio2\", \"id\", \"roleMembre2\", \"value\", \"membre\", \"checked\", \"\", 1, \"form-check-input\"], [\"roleMembre2\", \"\"], [\"for\", \"roleMembre2\", 1, \"form-check-label\", \"w-100\", \"p-2\", \"border\", \"rounded-4\", \"text-center\"], [\"type\", \"radio\", \"name\", \"roleRadio2\", \"id\", \"roleAdmin2\", \"value\", \"admin\", 1, \"form-check-input\"], [\"roleAdmin2\", \"\"], [\"for\", \"roleAdmin2\", 1, \"form-check-label\", \"w-100\", \"p-2\", \"border\", \"rounded-4\", \"text-center\"], [1, \"container-fluid\", \"py-5\", \"bg-light\"], [1, \"row\", \"justify-content-center\"], [1, \"col-md-8\", \"text-center\"], [1, \"alert\", \"alert-warning\", \"shadow-sm\", \"border-0\", \"rounded-3\", \"d-flex\", \"align-items-center\", \"p-4\"], [1, \"bi\", \"bi-exclamation-triangle-fill\", \"fs-1\", \"me-4\", \"text-warning\"], [1, \"fs-5\"], [1, \"btn\", \"btn-outline-primary\", \"rounded-pill\", \"mt-4\", 3, \"click\"]],\n        template: function EquipeDetailComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵtemplate(0, EquipeDetailComponent_div_0_Template, 109, 11, \"div\", 0);\n            i0.ɵɵtemplate(1, EquipeDetailComponent_div_1_Template, 11, 0, \"div\", 1);\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"ngIf\", ctx.equipe);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.equipe);\n          }\n        },\n        dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i5.NgSelectOption, i5.ɵNgSelectMultipleOption, i6.AiChatComponent],\n        styles: [\".cursor-pointer[_ngcontent-%COMP%]{cursor:pointer}summary[_ngcontent-%COMP%]:hover{text-decoration:underline}\", \"\\n\\n  .bg-gradient-primary[_ngcontent-%COMP%] {\\n    background: linear-gradient(45deg, #007bff, #6610f2) !important;\\n  }\\n\\n  .bg-gradient-light[_ngcontent-%COMP%] {\\n    background: linear-gradient(to right, #f8f9fa, #e9ecef) !important;\\n  }\\n\\n  \\n\\n  .transition[_ngcontent-%COMP%] {\\n    transition: all 0.3s ease;\\n  }\\n\\n  \\n\\n  .hover-card[_ngcontent-%COMP%] {\\n    transition: all 0.3s ease;\\n  }\\n\\n  .hover-card[_ngcontent-%COMP%]:hover {\\n    transform: translateY(-5px);\\n    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;\\n  }\\n\\n  \\n\\n  .member-card[_ngcontent-%COMP%] {\\n    background-color: white;\\n    transition: all 0.3s ease;\\n    border-left: 4px solid transparent;\\n  }\\n\\n  .member-card[_ngcontent-%COMP%]:hover {\\n    transform: translateY(-3px);\\n    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1) !important;\\n  }\\n\\n  \\n\\n  .member-avatar[_ngcontent-%COMP%] {\\n    width: 45px;\\n    height: 45px;\\n    display: flex;\\n    align-items: center;\\n    justify-content: center;\\n    font-size: 1.2rem;\\n  }\\n\\n  \\n\\n  .icon-circle[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n    border-radius: 50%;\\n    display: flex;\\n    align-items: center;\\n    justify-content: center;\\n    font-size: 1.2rem;\\n  }\\n\\n  \\n\\n  .description-box[_ngcontent-%COMP%] {\\n    border-left: 4px solid #007bff;\\n  }\\n\\n  \\n\\n  .btn[_ngcontent-%COMP%] {\\n    transition: all 0.3s ease;\\n  }\\n\\n  .btn[_ngcontent-%COMP%]:hover {\\n    transform: translateY(-2px);\\n  }\\n\\n  \\n\\n  .badge[_ngcontent-%COMP%] {\\n    font-weight: 500;\\n    letter-spacing: 0.5px;\\n  }\\n\\n  \\n\\n  .form-select[_ngcontent-%COMP%], .form-control[_ngcontent-%COMP%] {\\n    transition: all 0.2s ease;\\n  }\\n\\n  .form-select[_ngcontent-%COMP%]:focus, .form-control[_ngcontent-%COMP%]:focus {\\n    border-color: #007bff;\\n    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);\\n  }\\n\\n  \\n\\n  .empty-state-icon[_ngcontent-%COMP%] {\\n    width: 80px;\\n    height: 80px;\\n    margin: 0 auto;\\n    background-color: #f8f9fa;\\n    border-radius: 50%;\\n    display: flex;\\n    align-items: center;\\n    justify-content: center;\\n    font-size: 2rem;\\n    color: #adb5bd;\\n  }\\n\\n  \\n\\n  .form-check-label[_ngcontent-%COMP%] {\\n    cursor: pointer;\\n    transition: all 0.2s ease;\\n  }\\n\\n  .form-check-input[_ngcontent-%COMP%]:checked    + .form-check-label[_ngcontent-%COMP%] {\\n    background-color: rgba(13, 110, 253, 0.1);\\n    border-color: #007bff;\\n  }\\n\\n  \\n\\n  .rounded-4[_ngcontent-%COMP%] {\\n    border-radius: 0.75rem !important;\\n  }\\n\\n  \\n\\n  .member-grid[_ngcontent-%COMP%] {\\n    max-height: 500px;\\n    overflow-y: auto;\\n  }\"]\n      });\n    }\n  }\n  return EquipeDetailComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}