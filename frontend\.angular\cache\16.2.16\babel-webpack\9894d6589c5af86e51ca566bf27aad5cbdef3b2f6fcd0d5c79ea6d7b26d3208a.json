{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/equipe.service\";\nimport * as i2 from \"src/app/services/membre.service\";\nimport * as i3 from \"src/app/services/auth.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"src/app/services/notification.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/forms\";\nfunction EquipeFormComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"div\", 14)(2, \"div\", 15)(3, \"span\", 16);\n    i0.ɵɵtext(4, \"Chargement...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 17)(6, \"span\", 16);\n    i0.ɵɵtext(7, \"Chargement...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 15)(9, \"span\", 16);\n    i0.ɵɵtext(10, \"Chargement...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"p\", 18);\n    i0.ɵɵtext(12, \"Chargement des donn\\u00E9es...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction EquipeFormComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"div\", 3)(2, \"div\", 20);\n    i0.ɵɵelement(3, \"i\", 21);\n    i0.ɵɵelementStart(4, \"div\", 22);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction EquipeFormComponent_div_16_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57);\n    i0.ɵɵelement(1, \"i\", 58);\n    i0.ɵɵtext(2, \" Ce nom d'\\u00E9quipe existe d\\u00E9j\\u00E0. Veuillez en choisir un autre. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipeFormComponent_div_16_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57);\n    i0.ɵɵelement(1, \"i\", 58);\n    i0.ɵɵtext(2, \" Le nom de l'\\u00E9quipe doit contenir au moins 3 caract\\u00E8res. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipeFormComponent_div_16_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵelement(1, \"i\", 60);\n    i0.ɵɵtext(2, \" Le nom de l'\\u00E9quipe est requis. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipeFormComponent_div_16_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57);\n    i0.ɵɵelement(1, \"i\", 58);\n    i0.ɵɵtext(2, \" La description doit contenir au moins 10 caract\\u00E8res. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipeFormComponent_div_16_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵelement(1, \"i\", 60);\n    i0.ɵɵtext(2, \" La description de l'\\u00E9quipe est requise. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipeFormComponent_div_16_div_40_div_7_tr_20_small_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 89);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const membreId_r20 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"ID: \", membreId_r20, \"\");\n  }\n}\nfunction EquipeFormComponent_div_16_div_40_div_7_tr_20_a_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 90);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const membreId_r20 = i0.ɵɵnextContext().$implicit;\n    const ctx_r22 = i0.ɵɵnextContext(4);\n    i0.ɵɵpropertyInterpolate1(\"href\", \"mailto:\", ctx_r22.getMembreEmail(membreId_r20), \"\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r22.getMembreEmail(membreId_r20), \" \");\n  }\n}\nfunction EquipeFormComponent_div_16_div_40_div_7_tr_20_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 91);\n    i0.ɵɵtext(1, \"Non renseign\\u00E9\");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c0 = function (a0, a1, a2) {\n  return {\n    \"bg-primary\": a0,\n    \"bg-success\": a1,\n    \"bg-secondary\": a2\n  };\n};\nconst _c1 = function (a0, a1, a2) {\n  return {\n    \"bi-mortarboard-fill\": a0,\n    \"bi-briefcase-fill\": a1,\n    \"bi-question-circle-fill\": a2\n  };\n};\nfunction EquipeFormComponent_div_16_div_40_div_7_tr_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 81)(1, \"td\")(2, \"span\", 82);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, EquipeFormComponent_div_16_div_40_div_7_tr_20_small_4_Template, 2, 1, \"small\", 83);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtemplate(6, EquipeFormComponent_div_16_div_40_div_7_tr_20_a_6_Template, 2, 2, \"a\", 84);\n    i0.ɵɵtemplate(7, EquipeFormComponent_div_16_div_40_div_7_tr_20_span_7_Template, 2, 0, \"span\", 85);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\")(9, \"span\", 86);\n    i0.ɵɵelement(10, \"i\", 28);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"td\", 79)(13, \"button\", 87);\n    i0.ɵɵlistener(\"click\", function EquipeFormComponent_div_16_div_40_div_7_tr_20_Template_button_click_13_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r27);\n      const membreId_r20 = restoredCtx.$implicit;\n      const ctx_r26 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r26.removeMembreFromEquipe(membreId_r20));\n    });\n    i0.ɵɵelement(14, \"i\", 88);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const membreId_r20 = ctx.$implicit;\n    const ctx_r19 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r19.getMembreName(membreId_r20));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r19.getMembreName(membreId_r20) !== membreId_r20);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r19.getMembreEmail(membreId_r20) !== \"Non renseign\\u00E9\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r19.getMembreEmail(membreId_r20) === \"Non renseign\\u00E9\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(7, _c0, ctx_r19.getMembreProfession(membreId_r20) === \"\\u00C9tudiant\", ctx_r19.getMembreProfession(membreId_r20) === \"Professeur\", ctx_r19.getMembreProfession(membreId_r20) === \"Non sp\\u00E9cifi\\u00E9\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(11, _c1, ctx_r19.getMembreProfession(membreId_r20) === \"\\u00C9tudiant\", ctx_r19.getMembreProfession(membreId_r20) === \"Professeur\", ctx_r19.getMembreProfession(membreId_r20) === \"Non sp\\u00E9cifi\\u00E9\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r19.getMembreProfession(membreId_r20), \" \");\n  }\n}\nfunction EquipeFormComponent_div_16_div_40_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 72)(2, \"table\", 73)(3, \"thead\", 74)(4, \"tr\")(5, \"th\")(6, \"div\", 75);\n    i0.ɵɵelement(7, \"i\", 76);\n    i0.ɵɵtext(8, \" Nom et Pr\\u00E9nom \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"th\")(10, \"div\", 75);\n    i0.ɵɵelement(11, \"i\", 77);\n    i0.ɵɵtext(12, \" Email \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"th\")(14, \"div\", 75);\n    i0.ɵɵelement(15, \"i\", 78);\n    i0.ɵɵtext(16, \" Statut \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"th\", 79);\n    i0.ɵɵtext(18, \"Actions\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(19, \"tbody\");\n    i0.ɵɵtemplate(20, EquipeFormComponent_div_16_div_40_div_7_tr_20_Template, 15, 15, \"tr\", 80);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(20);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r14.equipe.members);\n  }\n}\nfunction EquipeFormComponent_div_16_div_40_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 92);\n    i0.ɵɵelement(1, \"i\", 93);\n    i0.ɵɵelementStart(2, \"h5\", 94);\n    i0.ɵɵtext(3, \"Aucun membre dans cette \\u00E9quipe\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 94);\n    i0.ɵɵtext(5, \"Ajoutez des membres \\u00E0 l'\\u00E9quipe en utilisant le formulaire ci-dessous.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction EquipeFormComponent_div_16_div_40_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵelement(1, \"i\", 48);\n    i0.ɵɵelementStart(2, \"div\");\n    i0.ɵɵtext(3, \"Aucun utilisateur disponible. Veuillez d'abord cr\\u00E9er des utilisateurs.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction EquipeFormComponent_div_16_div_40_div_15_option_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 112);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r31 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", user_r31._id || user_r31.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate4(\" \", user_r31.firstName || \"\", \" \", user_r31.lastName || user_r31.name || user_r31.id, \" \", user_r31.email ? \"- \" + user_r31.email : \"\", \" \", user_r31.profession ? \"(\" + (user_r31.profession === \"etudiant\" ? \"\\u00C9tudiant\" : \"Professeur\") + \")\" : user_r31.role ? \"(\" + (user_r31.role === \"etudiant\" ? \"\\u00C9tudiant\" : \"Professeur\") + \")\" : \"\", \" \");\n  }\n}\nfunction EquipeFormComponent_div_16_div_40_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r33 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 95)(1, \"div\", 29)(2, \"div\", 96)(3, \"div\", 97)(4, \"label\", 98);\n    i0.ɵɵtext(5, \"Utilisateur\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"select\", 99, 100)(8, \"option\", 101);\n    i0.ɵɵtext(9, \"S\\u00E9lectionnez un utilisateur\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, EquipeFormComponent_div_16_div_40_div_15_option_10_Template, 2, 5, \"option\", 102);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 103)(12, \"label\", 104);\n    i0.ɵɵtext(13, \"R\\u00F4le dans l'\\u00E9quipe\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"select\", 105, 106)(16, \"option\", 107);\n    i0.ɵɵtext(17, \"Membre\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"option\", 108);\n    i0.ɵɵtext(19, \"Administrateur\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(20, \"div\", 109)(21, \"button\", 110);\n    i0.ɵɵlistener(\"click\", function EquipeFormComponent_div_16_div_40_div_15_Template_button_click_21_listener() {\n      i0.ɵɵrestoreView(_r33);\n      const _r28 = i0.ɵɵreference(7);\n      const _r30 = i0.ɵɵreference(15);\n      const ctx_r32 = i0.ɵɵnextContext(3);\n      ctx_r32.addMembreToEquipe(_r28.value, _r30.value);\n      return i0.ɵɵresetView(_r28.value = \"\");\n    });\n    i0.ɵɵelement(22, \"i\", 111);\n    i0.ɵɵtext(23, \" Ajouter \");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const _r28 = i0.ɵɵreference(7);\n    const ctx_r18 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r18.availableUsers);\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"disabled\", !_r28.value);\n  }\n}\nfunction EquipeFormComponent_div_16_div_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"div\", 61)(2, \"div\", 62)(3, \"h4\", 63);\n    i0.ɵɵelement(4, \"i\", 64);\n    i0.ɵɵtext(5, \" Membres de l'\\u00E9quipe \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 29);\n    i0.ɵɵtemplate(7, EquipeFormComponent_div_16_div_40_div_7_Template, 21, 1, \"div\", 65);\n    i0.ɵɵtemplate(8, EquipeFormComponent_div_16_div_40_ng_template_8_Template, 6, 0, \"ng-template\", null, 66, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementStart(10, \"div\", 67)(11, \"h5\", 68);\n    i0.ɵɵelement(12, \"i\", 69);\n    i0.ɵɵtext(13, \" Ajouter un membre \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, EquipeFormComponent_div_16_div_40_div_14_Template, 4, 0, \"div\", 70);\n    i0.ɵɵtemplate(15, EquipeFormComponent_div_16_div_40_div_15_Template, 24, 2, \"div\", 71);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const _r15 = i0.ɵɵreference(9);\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.equipe.members && ctx_r10.equipe.members.length > 0)(\"ngIfElse\", _r15);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.availableUsers.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.availableUsers.length > 0);\n  }\n}\nfunction EquipeFormComponent_div_16_button_47_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r35 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 113);\n    i0.ɵɵlistener(\"click\", function EquipeFormComponent_div_16_button_47_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r34 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r34.deleteEquipe());\n    });\n    i0.ɵɵelement(1, \"i\", 114);\n    i0.ɵɵtext(2, \" Supprimer \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipeFormComponent_div_16_span_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 115);\n  }\n}\nconst _c2 = function (a0, a1) {\n  return {\n    \"bi-save\": a0,\n    \"bi-plus-circle\": a1\n  };\n};\nfunction EquipeFormComponent_div_16_i_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 28);\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(1, _c2, ctx_r13.isEditMode, !ctx_r13.isEditMode));\n  }\n}\nconst _c3 = function (a0, a1) {\n  return {\n    \"bi-pencil-square\": a0,\n    \"bi-plus-circle\": a1\n  };\n};\nfunction EquipeFormComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r37 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"div\", 24)(2, \"div\", 25)(3, \"div\", 26)(4, \"h3\", 27);\n    i0.ɵɵelement(5, \"i\", 28);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 29)(8, \"form\", 30);\n    i0.ɵɵlistener(\"ngSubmit\", function EquipeFormComponent_div_16_Template_form_ngSubmit_8_listener() {\n      i0.ɵɵrestoreView(_r37);\n      const ctx_r36 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r36.onSubmit());\n    });\n    i0.ɵɵelementStart(9, \"div\", 31)(10, \"label\", 32);\n    i0.ɵɵtext(11, \"Nom de l'\\u00E9quipe \");\n    i0.ɵɵelementStart(12, \"span\", 33);\n    i0.ɵɵtext(13, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 34)(15, \"span\", 35);\n    i0.ɵɵelement(16, \"i\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"input\", 37, 38);\n    i0.ɵɵlistener(\"input\", function EquipeFormComponent_div_16_Template_input_input_17_listener() {\n      i0.ɵɵrestoreView(_r37);\n      const _r3 = i0.ɵɵreference(18);\n      const ctx_r38 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r38.updateName(_r3.value));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(19, EquipeFormComponent_div_16_div_19_Template, 3, 0, \"div\", 39);\n    i0.ɵɵtemplate(20, EquipeFormComponent_div_16_div_20_Template, 3, 0, \"div\", 39);\n    i0.ɵɵtemplate(21, EquipeFormComponent_div_16_div_21_Template, 3, 0, \"div\", 40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 31)(23, \"label\", 41);\n    i0.ɵɵtext(24, \"Description \");\n    i0.ɵɵelementStart(25, \"span\", 33);\n    i0.ɵɵtext(26, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 34)(28, \"span\", 42);\n    i0.ɵɵelement(29, \"i\", 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"textarea\", 44, 45);\n    i0.ɵɵlistener(\"input\", function EquipeFormComponent_div_16_Template_textarea_input_30_listener() {\n      i0.ɵɵrestoreView(_r37);\n      const _r7 = i0.ɵɵreference(31);\n      const ctx_r39 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r39.updateDescription(_r7.value));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(32, EquipeFormComponent_div_16_div_32_Template, 3, 0, \"div\", 39);\n    i0.ɵɵtemplate(33, EquipeFormComponent_div_16_div_33_Template, 3, 0, \"div\", 40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(34, \"input\", 46);\n    i0.ɵɵelementStart(35, \"div\", 31)(36, \"div\", 47);\n    i0.ɵɵelement(37, \"i\", 48);\n    i0.ɵɵelementStart(38, \"div\");\n    i0.ɵɵtext(39, \"Un administrateur par d\\u00E9faut sera assign\\u00E9 \\u00E0 cette \\u00E9quipe.\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(40, EquipeFormComponent_div_16_div_40_Template, 16, 4, \"div\", 49);\n    i0.ɵɵelementStart(41, \"div\", 50)(42, \"div\", 51)(43, \"div\")(44, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function EquipeFormComponent_div_16_Template_button_click_44_listener() {\n      i0.ɵɵrestoreView(_r37);\n      const ctx_r40 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r40.cancel());\n    });\n    i0.ɵɵelement(45, \"i\", 8);\n    i0.ɵɵtext(46, \" Retour \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(47, EquipeFormComponent_div_16_button_47_Template, 3, 0, \"button\", 53);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"button\", 54);\n    i0.ɵɵtemplate(49, EquipeFormComponent_div_16_span_49_Template, 1, 0, \"span\", 55);\n    i0.ɵɵtemplate(50, EquipeFormComponent_div_16_i_50_Template, 1, 4, \"i\", 56);\n    i0.ɵɵtext(51);\n    i0.ɵɵelementEnd()()()()()()()();\n  }\n  if (rf & 2) {\n    const _r3 = i0.ɵɵreference(18);\n    const _r7 = i0.ɵɵreference(31);\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(20, _c3, ctx_r2.isEditMode, !ctx_r2.isEditMode));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.isEditMode ? \"Informations de l'\\u00E9quipe\" : \"D\\u00E9tails de la nouvelle \\u00E9quipe\", \" \");\n    i0.ɵɵadvance(11);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r2.nameExists || ctx_r2.nameError && _r3.value.length > 0);\n    i0.ɵɵproperty(\"value\", ctx_r2.equipe.name || \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.nameExists);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.nameError && _r3.value.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.error && !ctx_r2.equipe.name);\n    i0.ɵɵadvance(9);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r2.descriptionError && _r7.value.length > 0);\n    i0.ɵɵproperty(\"value\", ctx_r2.equipe.description || \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.descriptionError && _r7.value.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.error && !ctx_r2.equipe.description);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"value\", ctx_r2.equipe.admin);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isEditMode && ctx_r2.equipe._id);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isEditMode && ctx_r2.equipeId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.submitting || !ctx_r2.equipe.name || !ctx_r2.equipe.description || ctx_r2.nameExists || ctx_r2.nameError || ctx_r2.descriptionError);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.submitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.submitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.isEditMode ? \"Mettre \\u00E0 jour\" : \"Cr\\u00E9er l'\\u00E9quipe\", \" \");\n  }\n}\nexport class EquipeFormComponent {\n  constructor(equipeService, membreService, userService, route, router, notificationService) {\n    this.equipeService = equipeService;\n    this.membreService = membreService;\n    this.userService = userService;\n    this.route = route;\n    this.router = router;\n    this.notificationService = notificationService;\n    this.equipe = {\n      name: '',\n      description: '',\n      admin: '65f1e5b3a1d8f3c8c0f9e8d7' // ID temporaire\n    };\n\n    this.isEditMode = false;\n    this.loading = false;\n    this.submitting = false;\n    this.error = null;\n    this.equipeId = null;\n    this.nameExists = false;\n    this.nameError = false;\n    this.descriptionError = false;\n    this.checkingName = false;\n    this.existingEquipes = [];\n    this.availableMembers = []; // Liste des membres disponibles\n    this.availableUsers = []; // Liste des utilisateurs disponibles\n  }\n\n  ngOnInit() {\n    console.log('EquipeFormComponent initialized');\n    // Ensure equipe is always defined with admin\n    if (!this.equipe) {\n      this.equipe = {\n        name: '',\n        description: '',\n        admin: '65f1e5b3a1d8f3c8c0f9e8d7' // ID temporaire\n      };\n    }\n    // Charger toutes les équipes pour vérifier les noms existants\n    this.loadAllEquipes();\n    // Charger tous les membres disponibles\n    this.loadAllMembers();\n    // Charger tous les utilisateurs disponibles\n    this.loadAllUsers();\n    try {\n      // Vérifier si nous sommes en mode édition (si un ID est présent dans l'URL)\n      this.equipeId = this.route.snapshot.paramMap.get('id');\n      this.isEditMode = !!this.equipeId;\n      console.log('Edit mode:', this.isEditMode, 'ID:', this.equipeId);\n      if (this.isEditMode && this.equipeId) {\n        this.loadEquipe(this.equipeId);\n        // Ajouter un délai pour s'assurer que l'équipe est chargée\n        setTimeout(() => {\n          console.log('Après délai - this.equipeId:', this.equipeId);\n          console.log('Après délai - this.equipe:', this.equipe);\n        }, 1000);\n      }\n    } catch (error) {\n      console.error('Error in ngOnInit:', error);\n      this.error = \"Erreur d'initialisation\";\n    }\n    // Ajouter un gestionnaire d'événements pour le bouton d'ajout de membre\n    setTimeout(() => {\n      const addButton = document.getElementById('addMembreButton');\n      if (addButton) {\n        console.log(\"Bouton d'ajout de membre trouvé\");\n        addButton.addEventListener('click', () => {\n          console.log(\"Bouton d'ajout de membre cliqué\");\n        });\n      } else {\n        console.log(\"Bouton d'ajout de membre non trouvé\");\n      }\n    }, 2000);\n  }\n  loadAllMembers() {\n    this.membreService.getMembres().subscribe({\n      next: membres => {\n        this.availableMembers = membres;\n        console.log('Membres disponibles chargés:', membres);\n      },\n      error: error => {\n        console.error('Erreur lors du chargement des membres:', error);\n        this.error = 'Impossible de charger la liste des membres. Veuillez réessayer plus tard.';\n      }\n    });\n  }\n  loadAllUsers() {\n    this.userService().subscribe({\n      next: users => {\n        this.availableUsers = users;\n        console.log('Utilisateurs disponibles chargés:', users);\n      },\n      error: error => {\n        console.error('Erreur lors du chargement des utilisateurs:', error);\n        this.error = 'Impossible de charger la liste des utilisateurs. Veuillez réessayer plus tard.';\n      }\n    });\n  }\n  loadAllEquipes() {\n    this.equipeService.getEquipes().subscribe({\n      next: equipes => {\n        this.existingEquipes = equipes;\n        console.log('Équipes existantes chargées:', equipes);\n      },\n      error: error => {\n        console.error('Erreur lors du chargement des équipes:', error);\n      }\n    });\n  }\n  loadEquipe(id) {\n    console.log('Loading equipe with ID:', id);\n    this.loading = true;\n    this.error = null;\n    this.equipeService.getEquipe(id).subscribe({\n      next: data => {\n        console.log('Équipe chargée:', data);\n        this.equipe = data;\n        // Vérifier que l'ID est correctement défini\n        console.log(\"ID de l'équipe après chargement:\", this.equipe._id);\n        console.log('this.equipeId:', this.equipeId);\n        // Si l'équipe a des membres, récupérer les informations de chaque membre\n        if (this.equipe.members && this.equipe.members.length > 0) {\n          this.loadMembersDetails();\n        }\n        this.loading = false;\n      },\n      error: error => {\n        console.error(\"Erreur lors du chargement de l'équipe:\", error);\n        this.error = \"Impossible de charger les détails de l'équipe. Veuillez réessayer plus tard.\";\n        this.loading = false;\n      }\n    });\n  }\n  // Méthode pour récupérer les détails des membres de l'équipe\n  loadMembersDetails() {\n    if (!this.equipe.members || this.equipe.members.length === 0) {\n      return;\n    }\n    console.log(\"Chargement des détails des membres de l'équipe...\");\n    // Pour chaque membre de l'équipe, essayer de trouver ses informations dans la liste des utilisateurs\n    this.equipe.members.forEach(membreId => {\n      // Chercher d'abord dans la liste des utilisateurs\n      const user = this.availableUsers.find(u => u._id === membreId || u.id === membreId);\n      if (user) {\n        console.log(`Membre ${membreId} trouvé dans la liste des utilisateurs:`, user);\n        // Vérifier si toutes les informations nécessaires sont présentes\n        if (!user.email || !user.profession && !user.role) {\n          // Si des informations manquent, essayer de les récupérer depuis l'API\n          this.userService.getUser(membreId).subscribe({\n            next: userData => {\n              console.log(`Détails supplémentaires de l'utilisateur ${membreId} récupérés:`, userData);\n              // Mettre à jour l'utilisateur dans la liste avec les nouvelles informations\n              const index = this.availableUsers.findIndex(u => u._id === membreId || u.id === membreId);\n              if (index !== -1) {\n                this.availableUsers[index] = {\n                  ...this.availableUsers[index],\n                  ...userData\n                };\n              }\n            },\n            error: error => {\n              console.error(`Erreur lors de la récupération des détails supplémentaires de l'utilisateur ${membreId}:`, error);\n            }\n          });\n        }\n      } else {\n        // Si non trouvé, essayer de récupérer l'utilisateur depuis l'API\n        this.userService.getUser(membreId).subscribe({\n          next: userData => {\n            console.log(`Détails de l'utilisateur ${membreId} récupérés:`, userData);\n            // Ajouter l'utilisateur à la liste des utilisateurs disponibles s'il n'y est pas déjà\n            if (!this.availableUsers.some(u => u._id === userData._id || u.id === userData.id)) {\n              this.availableUsers.push(userData);\n            }\n          },\n          error: error => {\n            console.error(`Erreur lors de la récupération des détails de l'utilisateur ${membreId}:`, error);\n          }\n        });\n      }\n    });\n  }\n  checkNameExists(name) {\n    // En mode édition, ignorer l'équipe actuelle\n    if (this.isEditMode && this.equipeId) {\n      return this.existingEquipes.some(e => e.name === name && e._id !== this.equipeId);\n    }\n    // En mode création, vérifier tous les noms\n    return this.existingEquipes.some(e => e.name === name);\n  }\n  updateName(value) {\n    console.log('Name updated:', value);\n    this.equipe.name = value;\n    // Vérifier si le nom existe déjà\n    this.nameExists = this.checkNameExists(value);\n    if (this.nameExists) {\n      console.warn(\"Ce nom d'équipe existe déjà\");\n    }\n    // Vérifier si le nom a au moins 3 caractères\n    this.nameError = value.length > 0 && value.length < 3;\n    if (this.nameError) {\n      console.warn('Le nom doit contenir au moins 3 caractères');\n    }\n  }\n  updateDescription(value) {\n    console.log('Description updated:', value);\n    this.equipe.description = value;\n    // Vérifier si la description a au moins 10 caractères\n    this.descriptionError = value.length > 0 && value.length < 10;\n    if (this.descriptionError) {\n      console.warn('La description doit contenir au moins 10 caractères');\n    }\n  }\n  onSubmit() {\n    console.log('Form submitted with:', this.equipe);\n    // Vérifier si le nom est présent et valide\n    if (!this.equipe.name) {\n      this.error = \"Le nom de l'équipe est requis.\";\n      return;\n    }\n    if (this.equipe.name.length < 3) {\n      this.nameError = true;\n      this.error = \"Le nom de l'équipe doit contenir au moins 3 caractères.\";\n      return;\n    }\n    // Vérifier si la description est présente et valide\n    if (!this.equipe.description) {\n      this.error = \"La description de l'équipe est requise.\";\n      return;\n    }\n    if (this.equipe.description.length < 10) {\n      this.descriptionError = true;\n      this.error = \"La description de l'équipe doit contenir au moins 10 caractères.\";\n      return;\n    }\n    // Vérifier si le nom existe déjà avant de soumettre\n    if (this.checkNameExists(this.equipe.name)) {\n      this.nameExists = true;\n      this.error = 'Une équipe avec ce nom existe déjà. Veuillez choisir un autre nom.';\n      return;\n    }\n    this.submitting = true;\n    this.error = null;\n    // Créer une copie de l'objet équipe pour éviter les problèmes de référence\n    const equipeToSave = {\n      name: this.equipe.name,\n      description: this.equipe.description || '',\n      admin: this.equipe.admin\n    };\n    // Ajouter l'ID si nous sommes en mode édition\n    if (this.isEditMode && this.equipeId) {\n      equipeToSave._id = this.equipeId;\n    }\n    console.log('Données à envoyer:', equipeToSave);\n    if (this.isEditMode && this.equipeId) {\n      // Mode édition\n      this.equipeService.updateEquipe(this.equipeId, equipeToSave).subscribe({\n        next: response => {\n          console.log('Équipe mise à jour avec succès:', response);\n          this.submitting = false;\n          this.notificationService.showSuccess(`L'équipe \"${response.name}\" a été mise à jour avec succès.`);\n          this.router.navigate(['/equipes/liste']);\n        },\n        error: error => {\n          console.error(\"Erreur lors de la mise à jour de l'équipe:\", error);\n          this.error = `Impossible de mettre à jour l'équipe: ${error.message}`;\n          this.submitting = false;\n          this.notificationService.showError(`Erreur: ${error.message}`);\n        }\n      });\n    } else {\n      // Mode ajout\n      this.equipeService.addEquipe(equipeToSave).subscribe({\n        next: response => {\n          console.log('Équipe ajoutée avec succès:', response);\n          this.submitting = false;\n          this.notificationService.showSuccess(`L'équipe \"${response.name}\" a été créée avec succès.`);\n          this.router.navigate(['/equipes/liste']);\n        },\n        error: error => {\n          console.error(\"Erreur lors de l'ajout de l'équipe:\", error);\n          this.error = `Impossible d'ajouter l'équipe: ${error.message}`;\n          this.submitting = false;\n          this.notificationService.showError(`Erreur: ${error.message}`);\n        }\n      });\n    }\n  }\n  cancel() {\n    console.log('Form cancelled');\n    this.router.navigate(['/equipes/liste']);\n  }\n  // Méthodes pour gérer les membres\n  addMembreToEquipe(membreId, role = 'membre') {\n    console.log('Début de addMembreToEquipe avec membreId:', membreId, 'et rôle:', role);\n    console.log('État actuel - this.equipeId:', this.equipeId);\n    console.log('État actuel - this.equipe:', this.equipe);\n    // Utiliser this.equipe._id si this.equipeId n'est pas défini\n    const equipeId = this.equipeId || this.equipe && this.equipe._id;\n    console.log('equipeId calculé:', equipeId);\n    if (!equipeId || !membreId) {\n      console.error(\"ID d'équipe ou ID de membre manquant\");\n      this.error = \"ID d'équipe ou ID de membre manquant\";\n      console.log('equipeId:', equipeId, 'membreId:', membreId);\n      // Afficher un message à l'utilisateur\n      this.notificationService.showError(\"Impossible d'ajouter le membre: ID d'équipe ou ID de membre manquant\");\n      return;\n    }\n    // Vérifier si le membre est déjà dans l'équipe\n    if (this.equipe.members && this.equipe.members.includes(membreId)) {\n      this.notificationService.showError(\"Ce membre fait déjà partie de l'équipe\");\n      return;\n    }\n    // Récupérer les informations de l'utilisateur pour afficher un message plus informatif\n    const user = this.availableUsers.find(u => u._id === membreId || u.id === membreId);\n    const userName = user ? user.firstName && user.lastName ? `${user.firstName} ${user.lastName}` : user.name || membreId : membreId;\n    // Créer l'objet membre avec le rôle spécifié\n    const membre = {\n      id: membreId,\n      role: role\n    };\n    this.loading = true;\n    console.log(`Ajout de l'utilisateur \"${userName}\" comme ${role} à l'équipe ${equipeId}`);\n    this.equipeService.addMembreToEquipe(equipeId, membre).subscribe({\n      next: response => {\n        console.log('Membre ajouté avec succès:', response);\n        this.notificationService.showSuccess(`${userName} a été ajouté comme ${role === 'admin' ? 'administrateur' : 'membre'} à l'équipe`);\n        // Recharger l'équipe pour mettre à jour la liste des membres\n        this.loadEquipe(equipeId);\n        this.loading = false;\n      },\n      error: error => {\n        console.error(\"Erreur lors de l'ajout du membre:\", error);\n        this.error = \"Impossible d'ajouter le membre. Veuillez réessayer plus tard.\";\n        this.notificationService.showError(\"Erreur lors de l'ajout du membre: \" + error.message);\n        this.loading = false;\n      }\n    });\n  }\n  // Méthode pour obtenir le nom complet d'un membre à partir de son ID\n  getMembreName(membreId) {\n    // Chercher d'abord dans la liste des utilisateurs\n    const user = this.availableUsers.find(u => u._id === membreId || u.id === membreId);\n    if (user) {\n      if (user.firstName && user.lastName) {\n        return `${user.firstName} ${user.lastName}`;\n      } else if (user.name) {\n        return user.name;\n      }\n    }\n    // Chercher ensuite dans la liste des membres\n    const membre = this.availableMembers.find(m => m._id === membreId || m.id === membreId);\n    if (membre && membre.name) {\n      return membre.name;\n    }\n    // Si aucun nom n'est trouvé, retourner l'ID\n    return membreId;\n  }\n  // Méthode pour obtenir l'email d'un membre\n  getMembreEmail(membreId) {\n    const user = this.availableUsers.find(u => u._id === membreId || u.id === membreId);\n    if (user && user.email) {\n      return user.email;\n    }\n    return 'Non renseigné';\n  }\n  // Méthode pour obtenir la profession d'un membre\n  getMembreProfession(membreId) {\n    const user = this.availableUsers.find(u => u._id === membreId || u.id === membreId);\n    if (user) {\n      if (user.profession) {\n        return user.profession === 'etudiant' ? 'Étudiant' : 'Professeur';\n      } else if (user.role) {\n        return user.role === 'etudiant' ? 'Étudiant' : 'Professeur';\n      }\n    }\n    return 'Non spécifié';\n  }\n  // Méthode pour obtenir le rôle d'un membre dans l'équipe\n  getMembreRole(membreId) {\n    // Cette méthode nécessiterait d'avoir accès aux rôles des membres dans l'équipe\n    // Pour l'instant, nous retournons une valeur par défaut\n    return 'Membre';\n  }\n  removeMembreFromEquipe(membreId) {\n    console.log('Méthode removeMembreFromEquipe appelée avec ID:', membreId);\n    console.log('État actuel - this.equipeId:', this.equipeId);\n    console.log('État actuel - this.equipe:', this.equipe);\n    // Utiliser this.equipe._id si this.equipeId n'est pas défini\n    const equipeId = this.equipeId || this.equipe && this.equipe._id;\n    if (!equipeId) {\n      console.error(\"ID d'équipe manquant\");\n      this.error = \"ID d'équipe manquant. Impossible de retirer le membre.\";\n      this.notificationService.showError(\"ID d'équipe manquant. Impossible de retirer le membre.\");\n      return;\n    }\n    if (!membreId) {\n      console.error('ID de membre manquant');\n      this.error = 'ID de membre manquant. Impossible de retirer le membre.';\n      this.notificationService.showError('ID de membre manquant. Impossible de retirer le membre.');\n      return;\n    }\n    // Obtenir le nom du membre pour l'afficher dans le message de confirmation\n    const membreName = this.getMembreName(membreId);\n    console.log(`Tentative de retrait de l'utilisateur ${membreId} (${membreName}) de l'équipe ${equipeId}`);\n    try {\n      if (confirm(`Êtes-vous sûr de vouloir retirer ${membreName} de l'équipe?`)) {\n        console.log('Confirmation acceptée, suppression en cours...');\n        this.loading = true;\n        this.error = null;\n        // Ajouter un délai pour s'assurer que l'utilisateur voit le chargement\n        setTimeout(() => {\n          this.equipeService.removeMembreFromEquipe(equipeId, membreId).subscribe({\n            next: response => {\n              console.log(`Utilisateur \"${membreName}\" retiré avec succès de l'équipe:`, response);\n              this.loading = false;\n              this.notificationService.showSuccess(`${membreName} a été retiré avec succès de l'équipe`);\n              // Recharger l'équipe pour mettre à jour la liste des membres\n              this.loadEquipe(equipeId);\n            },\n            error: error => {\n              console.error(`Erreur lors du retrait de l'utilisateur \"${membreName}\":`, error);\n              console.error(\"Détails de l'erreur:\", {\n                status: error.status,\n                message: error.message,\n                error: error\n              });\n              this.loading = false;\n              this.error = `Impossible de retirer l'utilisateur \"${membreName}\" de l'équipe: ${error.message || 'Erreur inconnue'}`;\n              this.notificationService.showError(`Erreur lors du retrait du membre: ${this.error}`);\n            }\n          });\n        }, 500);\n      } else {\n        console.log(\"Suppression annulée par l'utilisateur\");\n      }\n    } catch (error) {\n      console.error('Exception lors du retrait du membre:', error);\n      this.error = `Exception: ${error?.message || 'Erreur inconnue'}`;\n      this.notificationService.showError(`Exception: ${this.error}`);\n    }\n  }\n  // Méthode pour supprimer l'équipe\n  deleteEquipe() {\n    console.log('Méthode deleteEquipe appelée dans equipe-form.component.ts');\n    console.log('État actuel - this.equipeId:', this.equipeId);\n    console.log('État actuel - this.equipe:', this.equipe);\n    // Utiliser this.equipe._id si this.equipeId n'est pas défini\n    const equipeId = this.equipeId || this.equipe && this.equipe._id;\n    if (!equipeId) {\n      console.error(\"ID d'équipe manquant\");\n      this.error = \"ID d'équipe manquant. Impossible de supprimer l'équipe.\";\n      this.notificationService.showError(\"ID d'équipe manquant. Impossible de supprimer l'équipe.\");\n      return;\n    }\n    console.log(\"ID de l'équipe à supprimer (final):\", equipeId);\n    try {\n      if (confirm(`Êtes-vous sûr de vouloir supprimer l'équipe \"${this.equipe.name}\"? Cette action est irréversible.`)) {\n        console.log('Confirmation acceptée, suppression en cours...');\n        this.loading = true;\n        this.error = null;\n        // Ajouter un délai pour s'assurer que l'utilisateur voit le chargement\n        setTimeout(() => {\n          this.equipeService.deleteEquipe(equipeId).subscribe({\n            next: response => {\n              console.log('Équipe supprimée avec succès, réponse:', response);\n              this.loading = false;\n              this.notificationService.showSuccess(`L'équipe \"${this.equipe.name}\" a été supprimée avec succès.`);\n              // Ajouter un délai avant la redirection\n              setTimeout(() => {\n                this.router.navigate(['/equipes/liste']);\n              }, 500);\n            },\n            error: error => {\n              console.error(\"Erreur lors de la suppression de l'équipe:\", error);\n              console.error(\"Détails de l'erreur:\", {\n                status: error.status,\n                message: error.message,\n                error: error\n              });\n              this.loading = false;\n              this.error = `Impossible de supprimer l'équipe: ${error.message || 'Erreur inconnue'}`;\n              this.notificationService.showError(`Erreur lors de la suppression: ${this.error}`);\n            }\n          });\n        }, 500);\n      } else {\n        console.log(\"Suppression annulée par l'utilisateur\");\n      }\n    } catch (error) {\n      console.error('Exception lors de la suppression:', error);\n      this.error = `Exception: ${error?.message || 'Erreur inconnue'}`;\n      this.notificationService.showError(`Exception: ${this.error}`);\n    }\n  }\n  static {\n    this.ɵfac = function EquipeFormComponent_Factory(t) {\n      return new (t || EquipeFormComponent)(i0.ɵɵdirectiveInject(i1.EquipeService), i0.ɵɵdirectiveInject(i2.MembreService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.ActivatedRoute), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i5.NotificationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: EquipeFormComponent,\n      selectors: [[\"app-equipe-form\"]],\n      decls: 17,\n      vars: 5,\n      consts: [[1, \"container-fluid\", \"py-5\", \"bg-light\"], [1, \"container\"], [1, \"row\", \"mb-5\"], [1, \"col-12\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"display-4\", \"fw-bold\", \"text-primary\"], [1, \"text-muted\", \"lead\"], [1, \"btn\", \"btn-outline-primary\", \"rounded-pill\", \"px-4\", \"py-2\", \"shadow-sm\", 3, \"click\"], [1, \"bi\", \"bi-arrow-left\", \"me-2\"], [1, \"my-4\"], [\"class\", \"row justify-content-center my-5\", 4, \"ngIf\"], [\"class\", \"row mb-4\", 4, \"ngIf\"], [\"class\", \"row justify-content-center\", 4, \"ngIf\"], [1, \"row\", \"justify-content-center\", \"my-5\"], [1, \"col-md-6\", \"text-center\"], [\"role\", \"status\", 1, \"spinner-grow\", \"text-primary\", \"mx-1\"], [1, \"visually-hidden\"], [\"role\", \"status\", 1, \"spinner-grow\", \"text-secondary\", \"mx-1\"], [1, \"mt-3\", \"text-muted\"], [1, \"row\", \"mb-4\"], [1, \"alert\", \"alert-danger\", \"shadow-sm\", \"border-0\", \"rounded-3\", \"d-flex\", \"align-items-center\"], [1, \"bi\", \"bi-exclamation-triangle-fill\", \"fs-3\", \"me-3\"], [1, \"flex-grow-1\"], [1, \"row\", \"justify-content-center\"], [1, \"col-lg-8\"], [1, \"card\", \"border-0\", \"shadow-sm\", \"rounded-3\", \"overflow-hidden\"], [1, \"card-header\", \"bg-gradient-primary\", \"text-white\", \"border-0\", \"py-4\"], [1, \"mb-0\"], [1, \"bi\", 3, \"ngClass\"], [1, \"card-body\", \"p-4\"], [1, \"row\", \"g-3\", 3, \"ngSubmit\"], [1, \"col-12\", \"mb-3\"], [\"for\", \"name\", 1, \"form-label\", \"fw-medium\"], [1, \"text-danger\"], [1, \"input-group\"], [1, \"input-group-text\", \"bg-light\", \"border-0\"], [1, \"bi\", \"bi-people-fill\", \"text-primary\"], [\"type\", \"text\", \"id\", \"name\", \"required\", \"\", \"minlength\", \"3\", \"placeholder\", \"Entrez le nom de l'\\u00E9quipe\", 1, \"form-control\", \"bg-light\", \"border-0\", 3, \"value\", \"input\"], [\"nameInput\", \"\"], [\"class\", \"invalid-feedback d-block small mt-1\", 4, \"ngIf\"], [\"class\", \"text-danger small mt-1\", 4, \"ngIf\"], [\"for\", \"description\", 1, \"form-label\", \"fw-medium\"], [1, \"input-group-text\", \"bg-light\", \"border-0\", \"align-self-start\"], [1, \"bi\", \"bi-card-text\", \"text-primary\"], [\"id\", \"description\", \"rows\", \"4\", \"required\", \"\", \"minlength\", \"10\", \"placeholder\", \"D\\u00E9crivez l'objectif et les activit\\u00E9s de cette \\u00E9quipe\", 1, \"form-control\", \"bg-light\", \"border-0\", 3, \"value\", \"input\"], [\"descInput\", \"\"], [\"type\", \"hidden\", 3, \"value\"], [1, \"alert\", \"alert-info\", \"border-0\", \"rounded-3\", \"shadow-sm\", \"d-flex\", \"align-items-center\"], [1, \"bi\", \"bi-info-circle-fill\", \"fs-4\", \"me-3\", \"text-primary\"], [\"class\", \"col-12 mt-4\", 4, \"ngIf\"], [1, \"col-12\", \"mt-4\"], [1, \"d-flex\", \"gap-3\", \"justify-content-between\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-secondary\", \"rounded-pill\", \"px-4\", \"py-2\", 3, \"click\"], [\"type\", \"button\", \"class\", \"btn btn-outline-danger rounded-pill px-4 py-2 ms-2\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"rounded-pill\", \"px-4\", \"py-2\", \"shadow-sm\", 3, \"disabled\"], [\"class\", \"spinner-border spinner-border-sm me-2\", 4, \"ngIf\"], [\"class\", \"bi\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"invalid-feedback\", \"d-block\", \"small\", \"mt-1\"], [1, \"bi\", \"bi-exclamation-triangle-fill\", \"me-1\"], [1, \"text-danger\", \"small\", \"mt-1\"], [1, \"bi\", \"bi-exclamation-circle-fill\", \"me-1\"], [1, \"card\", \"border-0\", \"shadow-sm\", \"rounded-3\", \"mb-4\"], [1, \"card-header\", \"bg-light\", \"border-0\", \"py-3\"], [1, \"mb-0\", \"d-flex\", \"align-items-center\"], [1, \"bi\", \"bi-people-fill\", \"text-primary\", \"me-2\"], [4, \"ngIf\", \"ngIfElse\"], [\"noMembers\", \"\"], [1, \"mt-4\"], [1, \"d-flex\", \"align-items-center\", \"mb-3\"], [1, \"bi\", \"bi-person-plus-fill\", \"text-primary\", \"me-2\"], [\"class\", \"alert alert-info border-0 rounded-3 shadow-sm d-flex align-items-center\", 4, \"ngIf\"], [\"class\", \"card border-0 bg-light rounded-3 mb-3\", 4, \"ngIf\"], [1, \"table-responsive\"], [1, \"table\", \"table-hover\", \"align-middle\"], [1, \"table-light\"], [1, \"d-flex\", \"align-items-center\"], [1, \"bi\", \"bi-person\", \"text-primary\", \"me-2\"], [1, \"bi\", \"bi-envelope\", \"text-primary\", \"me-2\"], [1, \"bi\", \"bi-briefcase\", \"text-primary\", \"me-2\"], [1, \"text-center\"], [\"class\", \"transition hover-row\", 4, \"ngFor\", \"ngForOf\"], [1, \"transition\", \"hover-row\"], [1, \"fw-medium\"], [\"class\", \"text-muted d-block\", 4, \"ngIf\"], [\"class\", \"text-decoration-none\", 3, \"href\", 4, \"ngIf\"], [\"class\", \"text-muted fst-italic\", 4, \"ngIf\"], [1, \"badge\", \"rounded-pill\", \"px-3\", \"py-2\", \"shadow-sm\", 3, \"ngClass\"], [\"type\", \"button\", \"title\", \"Retirer de l'\\u00E9quipe\", 1, \"btn\", \"btn-sm\", \"btn-outline-danger\", \"rounded-circle\", 3, \"click\"], [1, \"bi\", \"bi-trash\"], [1, \"text-muted\", \"d-block\"], [1, \"text-decoration-none\", 3, \"href\"], [1, \"text-muted\", \"fst-italic\"], [1, \"text-center\", \"py-4\"], [1, \"bi\", \"bi-people\", \"fs-1\", \"text-muted\", \"mb-3\", \"d-block\"], [1, \"text-muted\"], [1, \"card\", \"border-0\", \"bg-light\", \"rounded-3\", \"mb-3\"], [1, \"row\", \"g-3\"], [1, \"col-md-6\"], [\"for\", \"userSelect\", 1, \"form-label\", \"fw-medium\"], [\"id\", \"userSelect\", 1, \"form-select\", \"border-0\", \"shadow-sm\"], [\"userSelect\", \"\"], [\"value\", \"\", \"selected\", \"\", \"disabled\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-md-4\"], [\"for\", \"roleSelect\", 1, \"form-label\", \"fw-medium\"], [\"id\", \"roleSelect\", 1, \"form-select\", \"border-0\", \"shadow-sm\"], [\"roleSelect\", \"\"], [\"value\", \"membre\", \"selected\", \"\"], [\"value\", \"admin\"], [1, \"col-md-2\", \"d-flex\", \"align-items-end\"], [\"type\", \"button\", \"id\", \"addMembreButton\", 1, \"btn\", \"btn-primary\", \"rounded-pill\", \"w-100\", \"shadow-sm\", 3, \"disabled\", \"click\"], [1, \"bi\", \"bi-plus-circle\", \"me-1\"], [3, \"value\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-danger\", \"rounded-pill\", \"px-4\", \"py-2\", \"ms-2\", 3, \"click\"], [1, \"bi\", \"bi-trash\", \"me-2\"], [1, \"spinner-border\", \"spinner-border-sm\", \"me-2\"]],\n      template: function EquipeFormComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"div\")(6, \"h1\", 5);\n          i0.ɵɵtext(7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"p\", 6);\n          i0.ɵɵtext(9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"button\", 7);\n          i0.ɵɵlistener(\"click\", function EquipeFormComponent_Template_button_click_10_listener() {\n            return ctx.cancel();\n          });\n          i0.ɵɵelement(11, \"i\", 8);\n          i0.ɵɵtext(12, \" Retour \\u00E0 la liste \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(13, \"hr\", 9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(14, EquipeFormComponent_div_14_Template, 13, 0, \"div\", 10);\n          i0.ɵɵtemplate(15, EquipeFormComponent_div_15_Template, 6, 1, \"div\", 11);\n          i0.ɵɵtemplate(16, EquipeFormComponent_div_16_Template, 52, 23, \"div\", 12);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate(ctx.isEditMode ? \"Modifier l'\\u00E9quipe\" : \"Nouvelle \\u00E9quipe\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.isEditMode ? \"Modifiez les informations et les membres de votre \\u00E9quipe\" : \"Cr\\u00E9ez une nouvelle \\u00E9quipe pour organiser vos projets et membres\", \" \");\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n        }\n      },\n      dependencies: [i6.NgClass, i6.NgForOf, i6.NgIf, i7.ɵNgNoValidate, i7.NgSelectOption, i7.ɵNgSelectMultipleOption, i7.NgControlStatusGroup, i7.NgForm],\n      styles: [\".cursor-pointer[_ngcontent-%COMP%] {\\n      cursor: pointer;\\n    }\\n    summary[_ngcontent-%COMP%]:hover {\\n      text-decoration: underline;\\n    }\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImVxdWlwZS1mb3JtLmNvbXBvbmVudC5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkdBQUc7TUFDRyxlQUFlO0lBQ2pCO0lBQ0E7TUFDRSwwQkFBMEI7SUFDNUIiLCJmaWxlIjoiZXF1aXBlLWZvcm0uY29tcG9uZW50LmNzcyIsInNvdXJjZXNDb250ZW50IjpbIiAgIC5jdXJzb3ItcG9pbnRlciB7XHJcbiAgICAgIGN1cnNvcjogcG9pbnRlcjtcclxuICAgIH1cclxuICAgIHN1bW1hcnk6aG92ZXIge1xyXG4gICAgICB0ZXh0LWRlY29yYXRpb246IHVuZGVybGluZTtcclxuICAgIH0iXX0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvZnJvbnQvZXF1aXBlcy9lcXVpcGUtZm9ybS9lcXVpcGUtZm9ybS5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJHQUFHO01BQ0csZUFBZTtJQUNqQjtJQUNBO01BQ0UsMEJBQTBCO0lBQzVCO0FBQ0osd2NBQXdjIiwic291cmNlc0NvbnRlbnQiOlsiICAgLmN1cnNvci1wb2ludGVyIHtcclxuICAgICAgY3Vyc29yOiBwb2ludGVyO1xyXG4gICAgfVxyXG4gICAgc3VtbWFyeTpob3ZlciB7XHJcbiAgICAgIHRleHQtZGVjb3JhdGlvbjogdW5kZXJsaW5lO1xyXG4gICAgfSJdLCJzb3VyY2VSb290IjoiIn0= */\", \"\\n\\n  .bg-gradient-primary[_ngcontent-%COMP%] {\\n    background: linear-gradient(45deg, #007bff, #6610f2) !important;\\n  }\\n\\n  \\n\\n  .transition[_ngcontent-%COMP%] {\\n    transition: all 0.2s ease;\\n  }\\n\\n  .hover-row[_ngcontent-%COMP%]:hover {\\n    background-color: rgba(13, 110, 253, 0.05) !important;\\n    transform: translateY(-2px);\\n    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);\\n  }\\n\\n  \\n\\n  .form-control[_ngcontent-%COMP%]:focus, .form-select[_ngcontent-%COMP%]:focus {\\n    border-color: #86b7fe;\\n    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);\\n  }\\n\\n  \\n\\n  .btn[_ngcontent-%COMP%] {\\n    transition: all 0.3s ease;\\n  }\\n\\n  .btn[_ngcontent-%COMP%]:hover {\\n    transform: translateY(-2px);\\n  }\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r1", "error", "ɵɵtextInterpolate1", "membreId_r20", "ɵɵpropertyInterpolate1", "ctx_r22", "getMembreEmail", "ɵɵsanitizeUrl", "ɵɵtemplate", "EquipeFormComponent_div_16_div_40_div_7_tr_20_small_4_Template", "EquipeFormComponent_div_16_div_40_div_7_tr_20_a_6_Template", "EquipeFormComponent_div_16_div_40_div_7_tr_20_span_7_Template", "ɵɵlistener", "EquipeFormComponent_div_16_div_40_div_7_tr_20_Template_button_click_13_listener", "restoredCtx", "ɵɵrestoreView", "_r27", "$implicit", "ctx_r26", "ɵɵnextContext", "ɵɵresetView", "removeMembreFromEquipe", "ctx_r19", "getMembreName", "ɵɵproperty", "ɵɵpureFunction3", "_c0", "getMembreProfession", "_c1", "EquipeFormComponent_div_16_div_40_div_7_tr_20_Template", "ctx_r14", "equipe", "members", "user_r31", "_id", "id", "ɵɵtextInterpolate4", "firstName", "lastName", "name", "email", "profession", "role", "EquipeFormComponent_div_16_div_40_div_15_option_10_Template", "EquipeFormComponent_div_16_div_40_div_15_Template_button_click_21_listener", "_r33", "_r28", "ɵɵreference", "_r30", "ctx_r32", "addMembreToEquipe", "value", "ctx_r18", "availableUsers", "EquipeFormComponent_div_16_div_40_div_7_Template", "EquipeFormComponent_div_16_div_40_ng_template_8_Template", "ɵɵtemplateRefExtractor", "EquipeFormComponent_div_16_div_40_div_14_Template", "EquipeFormComponent_div_16_div_40_div_15_Template", "ctx_r10", "length", "_r15", "EquipeFormComponent_div_16_button_47_Template_button_click_0_listener", "_r35", "ctx_r34", "deleteEquipe", "ɵɵpureFunction2", "_c2", "ctx_r13", "isEditMode", "EquipeFormComponent_div_16_Template_form_ngSubmit_8_listener", "_r37", "ctx_r36", "onSubmit", "EquipeFormComponent_div_16_Template_input_input_17_listener", "_r3", "ctx_r38", "updateName", "EquipeFormComponent_div_16_div_19_Template", "EquipeFormComponent_div_16_div_20_Template", "EquipeFormComponent_div_16_div_21_Template", "EquipeFormComponent_div_16_Template_textarea_input_30_listener", "_r7", "ctx_r39", "updateDescription", "EquipeFormComponent_div_16_div_32_Template", "EquipeFormComponent_div_16_div_33_Template", "EquipeFormComponent_div_16_div_40_Template", "EquipeFormComponent_div_16_Template_button_click_44_listener", "ctx_r40", "cancel", "EquipeFormComponent_div_16_button_47_Template", "EquipeFormComponent_div_16_span_49_Template", "EquipeFormComponent_div_16_i_50_Template", "_c3", "ctx_r2", "ɵɵclassProp", "nameExists", "nameError", "descriptionError", "description", "admin", "equipeId", "submitting", "EquipeFormComponent", "constructor", "equipeService", "membreService", "userService", "route", "router", "notificationService", "loading", "checkingName", "existingEquipes", "availableMembers", "ngOnInit", "console", "log", "loadAllEquipes", "loadAllMembers", "loadAllUsers", "snapshot", "paramMap", "get", "loadEquipe", "setTimeout", "addButton", "document", "getElementById", "addEventListener", "getMembres", "subscribe", "next", "membres", "users", "getEquipes", "equipes", "getEquipe", "data", "loadMembersDetails", "for<PERSON>ach", "membreId", "user", "find", "u", "getUser", "userData", "index", "findIndex", "some", "push", "checkNameExists", "e", "warn", "equipeToSave", "updateEquipe", "response", "showSuccess", "navigate", "message", "showError", "addEquipe", "includes", "userName", "membre", "m", "getMembreRole", "membreName", "confirm", "status", "ɵɵdirectiveInject", "i1", "EquipeService", "i2", "MembreService", "i3", "AuthService", "i4", "ActivatedRoute", "Router", "i5", "NotificationService", "selectors", "decls", "vars", "consts", "template", "EquipeFormComponent_Template", "rf", "ctx", "EquipeFormComponent_Template_button_click_10_listener", "EquipeFormComponent_div_14_Template", "EquipeFormComponent_div_15_Template", "EquipeFormComponent_div_16_Template"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\equipes\\equipe-form\\equipe-form.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\equipes\\equipe-form\\equipe-form.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { EquipeService } from 'src/app/services/equipe.service';\nimport { MembreService } from 'src/app/services/membre.service';\nimport { AuthService } from 'src/app/services/auth.service';\nimport { NotificationService } from 'src/app/services/notification.service';\nimport { Equipe } from 'src/app/models/equipe.model';\nimport { Membre } from 'src/app/models/membre.model';\nimport { User } from 'src/app/models/user.model';\nimport {\n  debounceTime,\n  distinctUntilChanged,\n  switchMap,\n  catchError,\n  of,\n} from 'rxjs';\n\n@Component({\n  selector: 'app-equipe-form',\n  templateUrl: './equipe-form.component.html',\n  styleUrls: ['./equipe-form.component.css'],\n})\nexport class EquipeFormComponent implements OnInit {\n  equipe: Equipe = {\n    name: '',\n    description: '',\n    admin: '65f1e5b3a1d8f3c8c0f9e8d7', // ID temporaire\n  };\n  isEditMode = false;\n  loading = false;\n  submitting = false;\n  error: string | null = null;\n  equipeId: string | null = null;\n  nameExists = false;\n  nameError = false;\n  descriptionError = false;\n  checkingName = false;\n  existingEquipes: Equipe[] = [];\n  availableMembers: Membre[] = []; // Liste des membres disponibles\n  availableUsers: User[] = []; // Liste des utilisateurs disponibles\n\n  constructor(\n    private equipeService: EquipeService,\n    private membreService: MembreService,\n    private userService: AuthService,\n    private route: ActivatedRoute,\n    private router: Router,\n    private notificationService: NotificationService\n  ) {}\n\n  ngOnInit(): void {\n    console.log('EquipeFormComponent initialized');\n\n    // Ensure equipe is always defined with admin\n    if (!this.equipe) {\n      this.equipe = {\n        name: '',\n        description: '',\n        admin: '65f1e5b3a1d8f3c8c0f9e8d7', // ID temporaire\n      };\n    }\n\n    // Charger toutes les équipes pour vérifier les noms existants\n    this.loadAllEquipes();\n\n    // Charger tous les membres disponibles\n    this.loadAllMembers();\n\n    // Charger tous les utilisateurs disponibles\n    this.loadAllUsers();\n\n    try {\n      // Vérifier si nous sommes en mode édition (si un ID est présent dans l'URL)\n      this.equipeId = this.route.snapshot.paramMap.get('id');\n      this.isEditMode = !!this.equipeId;\n      console.log('Edit mode:', this.isEditMode, 'ID:', this.equipeId);\n\n      if (this.isEditMode && this.equipeId) {\n        this.loadEquipe(this.equipeId);\n\n        // Ajouter un délai pour s'assurer que l'équipe est chargée\n        setTimeout(() => {\n          console.log('Après délai - this.equipeId:', this.equipeId);\n          console.log('Après délai - this.equipe:', this.equipe);\n        }, 1000);\n      }\n    } catch (error) {\n      console.error('Error in ngOnInit:', error);\n      this.error = \"Erreur d'initialisation\";\n    }\n\n    // Ajouter un gestionnaire d'événements pour le bouton d'ajout de membre\n    setTimeout(() => {\n      const addButton = document.getElementById('addMembreButton');\n      if (addButton) {\n        console.log(\"Bouton d'ajout de membre trouvé\");\n        addButton.addEventListener('click', () => {\n          console.log(\"Bouton d'ajout de membre cliqué\");\n        });\n      } else {\n        console.log(\"Bouton d'ajout de membre non trouvé\");\n      }\n    }, 2000);\n  }\n\n  loadAllMembers(): void {\n    this.membreService.getMembres().subscribe({\n      next: (membres) => {\n        this.availableMembers = membres;\n        console.log('Membres disponibles chargés:', membres);\n      },\n      error: (error) => {\n        console.error('Erreur lors du chargement des membres:', error);\n        this.error =\n          'Impossible de charger la liste des membres. Veuillez réessayer plus tard.';\n      },\n    });\n  }\n\n  loadAllUsers(): void {\n    this.userService().subscribe({\n      next: (users) => {\n        this.availableUsers = users;\n        console.log('Utilisateurs disponibles chargés:', users);\n      },\n      error: (error) => {\n        console.error('Erreur lors du chargement des utilisateurs:', error);\n        this.error =\n          'Impossible de charger la liste des utilisateurs. Veuillez réessayer plus tard.';\n      },\n    });\n  }\n\n  loadAllEquipes(): void {\n    this.equipeService.getEquipes().subscribe({\n      next: (equipes) => {\n        this.existingEquipes = equipes;\n        console.log('Équipes existantes chargées:', equipes);\n      },\n      error: (error) => {\n        console.error('Erreur lors du chargement des équipes:', error);\n      },\n    });\n  }\n\n  loadEquipe(id: string): void {\n    console.log('Loading equipe with ID:', id);\n    this.loading = true;\n    this.error = null;\n\n    this.equipeService.getEquipe(id).subscribe({\n      next: (data) => {\n        console.log('Équipe chargée:', data);\n        this.equipe = data;\n\n        // Vérifier que l'ID est correctement défini\n        console.log(\"ID de l'équipe après chargement:\", this.equipe._id);\n        console.log('this.equipeId:', this.equipeId);\n\n        // Si l'équipe a des membres, récupérer les informations de chaque membre\n        if (this.equipe.members && this.equipe.members.length > 0) {\n          this.loadMembersDetails();\n        }\n\n        this.loading = false;\n      },\n      error: (error) => {\n        console.error(\"Erreur lors du chargement de l'équipe:\", error);\n        this.error =\n          \"Impossible de charger les détails de l'équipe. Veuillez réessayer plus tard.\";\n        this.loading = false;\n      },\n    });\n  }\n\n  // Méthode pour récupérer les détails des membres de l'équipe\n  loadMembersDetails(): void {\n    if (!this.equipe.members || this.equipe.members.length === 0) {\n      return;\n    }\n\n    console.log(\"Chargement des détails des membres de l'équipe...\");\n\n    // Pour chaque membre de l'équipe, essayer de trouver ses informations dans la liste des utilisateurs\n    this.equipe.members.forEach((membreId) => {\n      // Chercher d'abord dans la liste des utilisateurs\n      const user = this.availableUsers.find(\n        (u) => u._id === membreId || u.id === membreId\n      );\n      if (user) {\n        console.log(\n          `Membre ${membreId} trouvé dans la liste des utilisateurs:`,\n          user\n        );\n\n        // Vérifier si toutes les informations nécessaires sont présentes\n        if (!user.email || (!user.profession && !user.role)) {\n          // Si des informations manquent, essayer de les récupérer depuis l'API\n          this.userService.getUser(membreId).subscribe({\n            next: (userData) => {\n              console.log(\n                `Détails supplémentaires de l'utilisateur ${membreId} récupérés:`,\n                userData\n              );\n\n              // Mettre à jour l'utilisateur dans la liste avec les nouvelles informations\n              const index = this.availableUsers.findIndex(\n                (u) => u._id === membreId || u.id === membreId\n              );\n              if (index !== -1) {\n                this.availableUsers[index] = {\n                  ...this.availableUsers[index],\n                  ...userData,\n                };\n              }\n            },\n            error: (error) => {\n              console.error(\n                `Erreur lors de la récupération des détails supplémentaires de l'utilisateur ${membreId}:`,\n                error\n              );\n            },\n          });\n        }\n      } else {\n        // Si non trouvé, essayer de récupérer l'utilisateur depuis l'API\n        this.userService.getUser(membreId).subscribe({\n          next: (userData) => {\n            console.log(\n              `Détails de l'utilisateur ${membreId} récupérés:`,\n              userData\n            );\n            // Ajouter l'utilisateur à la liste des utilisateurs disponibles s'il n'y est pas déjà\n            if (\n              !this.availableUsers.some(\n                (u) => u._id === userData._id || u.id === userData.id\n              )\n            ) {\n              this.availableUsers.push(userData);\n            }\n          },\n          error: (error) => {\n            console.error(\n              `Erreur lors de la récupération des détails de l'utilisateur ${membreId}:`,\n              error\n            );\n          },\n        });\n      }\n    });\n  }\n\n  checkNameExists(name: string): boolean {\n    // En mode édition, ignorer l'équipe actuelle\n    if (this.isEditMode && this.equipeId) {\n      return this.existingEquipes.some(\n        (e) => e.name === name && e._id !== this.equipeId\n      );\n    }\n    // En mode création, vérifier tous les noms\n    return this.existingEquipes.some((e) => e.name === name);\n  }\n\n  updateName(value: string): void {\n    console.log('Name updated:', value);\n    this.equipe.name = value;\n\n    // Vérifier si le nom existe déjà\n    this.nameExists = this.checkNameExists(value);\n    if (this.nameExists) {\n      console.warn(\"Ce nom d'équipe existe déjà\");\n    }\n\n    // Vérifier si le nom a au moins 3 caractères\n    this.nameError = value.length > 0 && value.length < 3;\n    if (this.nameError) {\n      console.warn('Le nom doit contenir au moins 3 caractères');\n    }\n  }\n\n  updateDescription(value: string): void {\n    console.log('Description updated:', value);\n    this.equipe.description = value;\n\n    // Vérifier si la description a au moins 10 caractères\n    this.descriptionError = value.length > 0 && value.length < 10;\n    if (this.descriptionError) {\n      console.warn('La description doit contenir au moins 10 caractères');\n    }\n  }\n\n  onSubmit(): void {\n    console.log('Form submitted with:', this.equipe);\n\n    // Vérifier si le nom est présent et valide\n    if (!this.equipe.name) {\n      this.error = \"Le nom de l'équipe est requis.\";\n      return;\n    }\n\n    if (this.equipe.name.length < 3) {\n      this.nameError = true;\n      this.error = \"Le nom de l'équipe doit contenir au moins 3 caractères.\";\n      return;\n    }\n\n    // Vérifier si la description est présente et valide\n    if (!this.equipe.description) {\n      this.error = \"La description de l'équipe est requise.\";\n      return;\n    }\n\n    if (this.equipe.description.length < 10) {\n      this.descriptionError = true;\n      this.error =\n        \"La description de l'équipe doit contenir au moins 10 caractères.\";\n      return;\n    }\n\n    // Vérifier si le nom existe déjà avant de soumettre\n    if (this.checkNameExists(this.equipe.name)) {\n      this.nameExists = true;\n      this.error =\n        'Une équipe avec ce nom existe déjà. Veuillez choisir un autre nom.';\n      return;\n    }\n\n    this.submitting = true;\n    this.error = null;\n\n    // Créer une copie de l'objet équipe pour éviter les problèmes de référence\n    const equipeToSave: Equipe = {\n      name: this.equipe.name,\n      description: this.equipe.description || '',\n      admin: this.equipe.admin,\n    };\n\n    // Ajouter l'ID si nous sommes en mode édition\n    if (this.isEditMode && this.equipeId) {\n      equipeToSave._id = this.equipeId;\n    }\n\n    console.log('Données à envoyer:', equipeToSave);\n\n    if (this.isEditMode && this.equipeId) {\n      // Mode édition\n      this.equipeService.updateEquipe(this.equipeId, equipeToSave).subscribe({\n        next: (response) => {\n          console.log('Équipe mise à jour avec succès:', response);\n          this.submitting = false;\n          this.notificationService.showSuccess(\n            `L'équipe \"${response.name}\" a été mise à jour avec succès.`\n          );\n          this.router.navigate(['/equipes/liste']);\n        },\n        error: (error) => {\n          console.error(\"Erreur lors de la mise à jour de l'équipe:\", error);\n          this.error = `Impossible de mettre à jour l'équipe: ${error.message}`;\n          this.submitting = false;\n          this.notificationService.showError(`Erreur: ${error.message}`);\n        },\n      });\n    } else {\n      // Mode ajout\n      this.equipeService.addEquipe(equipeToSave).subscribe({\n        next: (response) => {\n          console.log('Équipe ajoutée avec succès:', response);\n          this.submitting = false;\n          this.notificationService.showSuccess(\n            `L'équipe \"${response.name}\" a été créée avec succès.`\n          );\n          this.router.navigate(['/equipes/liste']);\n        },\n        error: (error) => {\n          console.error(\"Erreur lors de l'ajout de l'équipe:\", error);\n          this.error = `Impossible d'ajouter l'équipe: ${error.message}`;\n          this.submitting = false;\n          this.notificationService.showError(`Erreur: ${error.message}`);\n        },\n      });\n    }\n  }\n\n  cancel(): void {\n    console.log('Form cancelled');\n    this.router.navigate(['/equipes/liste']);\n  }\n\n  // Méthodes pour gérer les membres\n  addMembreToEquipe(membreId: string, role: string = 'membre'): void {\n    console.log(\n      'Début de addMembreToEquipe avec membreId:',\n      membreId,\n      'et rôle:',\n      role\n    );\n    console.log('État actuel - this.equipeId:', this.equipeId);\n    console.log('État actuel - this.equipe:', this.equipe);\n\n    // Utiliser this.equipe._id si this.equipeId n'est pas défini\n    const equipeId = this.equipeId || (this.equipe && this.equipe._id);\n\n    console.log('equipeId calculé:', equipeId);\n\n    if (!equipeId || !membreId) {\n      console.error(\"ID d'équipe ou ID de membre manquant\");\n      this.error = \"ID d'équipe ou ID de membre manquant\";\n      console.log('equipeId:', equipeId, 'membreId:', membreId);\n\n      // Afficher un message à l'utilisateur\n      this.notificationService.showError(\n        \"Impossible d'ajouter le membre: ID d'équipe ou ID de membre manquant\"\n      );\n      return;\n    }\n\n    // Vérifier si le membre est déjà dans l'équipe\n    if (this.equipe.members && this.equipe.members.includes(membreId)) {\n      this.notificationService.showError(\n        \"Ce membre fait déjà partie de l'équipe\"\n      );\n      return;\n    }\n\n    // Récupérer les informations de l'utilisateur pour afficher un message plus informatif\n    const user = this.availableUsers.find(\n      (u) => u._id === membreId || u.id === membreId\n    );\n    const userName = user\n      ? user.firstName && user.lastName\n        ? `${user.firstName} ${user.lastName}`\n        : user.name || membreId\n      : membreId;\n\n    // Créer l'objet membre avec le rôle spécifié\n    const membre: Membre = {\n      id: membreId,\n      role: role,\n    };\n\n    this.loading = true;\n\n    console.log(\n      `Ajout de l'utilisateur \"${userName}\" comme ${role} à l'équipe ${equipeId}`\n    );\n\n    this.equipeService.addMembreToEquipe(equipeId, membre).subscribe({\n      next: (response) => {\n        console.log('Membre ajouté avec succès:', response);\n        this.notificationService.showSuccess(\n          `${userName} a été ajouté comme ${\n            role === 'admin' ? 'administrateur' : 'membre'\n          } à l'équipe`\n        );\n        // Recharger l'équipe pour mettre à jour la liste des membres\n        this.loadEquipe(equipeId);\n        this.loading = false;\n      },\n      error: (error) => {\n        console.error(\"Erreur lors de l'ajout du membre:\", error);\n        this.error =\n          \"Impossible d'ajouter le membre. Veuillez réessayer plus tard.\";\n        this.notificationService.showError(\n          \"Erreur lors de l'ajout du membre: \" + error.message\n        );\n        this.loading = false;\n      },\n    });\n  }\n\n  // Méthode pour obtenir le nom complet d'un membre à partir de son ID\n  getMembreName(membreId: string): string {\n    // Chercher d'abord dans la liste des utilisateurs\n    const user = this.availableUsers.find(\n      (u) => u._id === membreId || u.id === membreId\n    );\n    if (user) {\n      if (user.firstName && user.lastName) {\n        return `${user.firstName} ${user.lastName}`;\n      } else if (user.name) {\n        return user.name;\n      }\n    }\n\n    // Chercher ensuite dans la liste des membres\n    const membre = this.availableMembers.find(\n      (m) => m._id === membreId || m.id === membreId\n    );\n    if (membre && membre.name) {\n      return membre.name;\n    }\n\n    // Si aucun nom n'est trouvé, retourner l'ID\n    return membreId;\n  }\n\n  // Méthode pour obtenir l'email d'un membre\n  getMembreEmail(membreId: string): string {\n    const user = this.availableUsers.find(\n      (u) => u._id === membreId || u.id === membreId\n    );\n    if (user && user.email) {\n      return user.email;\n    }\n    return 'Non renseigné';\n  }\n\n  // Méthode pour obtenir la profession d'un membre\n  getMembreProfession(membreId: string): string {\n    const user = this.availableUsers.find(\n      (u) => u._id === membreId || u.id === membreId\n    );\n    if (user) {\n      if (user.profession) {\n        return user.profession === 'etudiant' ? 'Étudiant' : 'Professeur';\n      } else if (user.role) {\n        return user.role === 'etudiant' ? 'Étudiant' : 'Professeur';\n      }\n    }\n    return 'Non spécifié';\n  }\n\n  // Méthode pour obtenir le rôle d'un membre dans l'équipe\n  getMembreRole(membreId: string): string {\n    // Cette méthode nécessiterait d'avoir accès aux rôles des membres dans l'équipe\n    // Pour l'instant, nous retournons une valeur par défaut\n    return 'Membre';\n  }\n\n  removeMembreFromEquipe(membreId: string): void {\n    console.log('Méthode removeMembreFromEquipe appelée avec ID:', membreId);\n    console.log('État actuel - this.equipeId:', this.equipeId);\n    console.log('État actuel - this.equipe:', this.equipe);\n\n    // Utiliser this.equipe._id si this.equipeId n'est pas défini\n    const equipeId = this.equipeId || (this.equipe && this.equipe._id);\n\n    if (!equipeId) {\n      console.error(\"ID d'équipe manquant\");\n      this.error = \"ID d'équipe manquant. Impossible de retirer le membre.\";\n      this.notificationService.showError(\n        \"ID d'équipe manquant. Impossible de retirer le membre.\"\n      );\n      return;\n    }\n\n    if (!membreId) {\n      console.error('ID de membre manquant');\n      this.error = 'ID de membre manquant. Impossible de retirer le membre.';\n      this.notificationService.showError(\n        'ID de membre manquant. Impossible de retirer le membre.'\n      );\n      return;\n    }\n\n    // Obtenir le nom du membre pour l'afficher dans le message de confirmation\n    const membreName = this.getMembreName(membreId);\n\n    console.log(\n      `Tentative de retrait de l'utilisateur ${membreId} (${membreName}) de l'équipe ${equipeId}`\n    );\n\n    try {\n      if (\n        confirm(`Êtes-vous sûr de vouloir retirer ${membreName} de l'équipe?`)\n      ) {\n        console.log('Confirmation acceptée, suppression en cours...');\n\n        this.loading = true;\n        this.error = null;\n\n        // Ajouter un délai pour s'assurer que l'utilisateur voit le chargement\n        setTimeout(() => {\n          this.equipeService\n            .removeMembreFromEquipe(equipeId, membreId)\n            .subscribe({\n              next: (response) => {\n                console.log(\n                  `Utilisateur \"${membreName}\" retiré avec succès de l'équipe:`,\n                  response\n                );\n                this.loading = false;\n                this.notificationService.showSuccess(\n                  `${membreName} a été retiré avec succès de l'équipe`\n                );\n\n                // Recharger l'équipe pour mettre à jour la liste des membres\n                this.loadEquipe(equipeId);\n              },\n              error: (error) => {\n                console.error(\n                  `Erreur lors du retrait de l'utilisateur \"${membreName}\":`,\n                  error\n                );\n                console.error(\"Détails de l'erreur:\", {\n                  status: error.status,\n                  message: error.message,\n                  error: error,\n                });\n\n                this.loading = false;\n                this.error = `Impossible de retirer l'utilisateur \"${membreName}\" de l'équipe: ${\n                  error.message || 'Erreur inconnue'\n                }`;\n                this.notificationService.showError(\n                  `Erreur lors du retrait du membre: ${this.error}`\n                );\n              },\n            });\n        }, 500);\n      } else {\n        console.log(\"Suppression annulée par l'utilisateur\");\n      }\n    } catch (error: any) {\n      console.error('Exception lors du retrait du membre:', error);\n      this.error = `Exception: ${error?.message || 'Erreur inconnue'}`;\n      this.notificationService.showError(`Exception: ${this.error}`);\n    }\n  }\n\n  // Méthode pour supprimer l'équipe\n  deleteEquipe(): void {\n    console.log('Méthode deleteEquipe appelée dans equipe-form.component.ts');\n    console.log('État actuel - this.equipeId:', this.equipeId);\n    console.log('État actuel - this.equipe:', this.equipe);\n\n    // Utiliser this.equipe._id si this.equipeId n'est pas défini\n    const equipeId = this.equipeId || (this.equipe && this.equipe._id);\n\n    if (!equipeId) {\n      console.error(\"ID d'équipe manquant\");\n      this.error = \"ID d'équipe manquant. Impossible de supprimer l'équipe.\";\n      this.notificationService.showError(\n        \"ID d'équipe manquant. Impossible de supprimer l'équipe.\"\n      );\n      return;\n    }\n\n    console.log(\"ID de l'équipe à supprimer (final):\", equipeId);\n\n    try {\n      if (\n        confirm(\n          `Êtes-vous sûr de vouloir supprimer l'équipe \"${this.equipe.name}\"? Cette action est irréversible.`\n        )\n      ) {\n        console.log('Confirmation acceptée, suppression en cours...');\n\n        this.loading = true;\n        this.error = null;\n\n        // Ajouter un délai pour s'assurer que l'utilisateur voit le chargement\n        setTimeout(() => {\n          this.equipeService.deleteEquipe(equipeId).subscribe({\n            next: (response) => {\n              console.log('Équipe supprimée avec succès, réponse:', response);\n              this.loading = false;\n              this.notificationService.showSuccess(\n                `L'équipe \"${this.equipe.name}\" a été supprimée avec succès.`\n              );\n\n              // Ajouter un délai avant la redirection\n              setTimeout(() => {\n                this.router.navigate(['/equipes/liste']);\n              }, 500);\n            },\n            error: (error) => {\n              console.error(\n                \"Erreur lors de la suppression de l'équipe:\",\n                error\n              );\n              console.error(\"Détails de l'erreur:\", {\n                status: error.status,\n                message: error.message,\n                error: error,\n              });\n\n              this.loading = false;\n              this.error = `Impossible de supprimer l'équipe: ${\n                error.message || 'Erreur inconnue'\n              }`;\n              this.notificationService.showError(\n                `Erreur lors de la suppression: ${this.error}`\n              );\n            },\n          });\n        }, 500);\n      } else {\n        console.log(\"Suppression annulée par l'utilisateur\");\n      }\n    } catch (error: any) {\n      console.error('Exception lors de la suppression:', error);\n      this.error = `Exception: ${error?.message || 'Erreur inconnue'}`;\n      this.notificationService.showError(`Exception: ${this.error}`);\n    }\n  }\n}\n", "<div class=\"container-fluid py-5 bg-light\">\n  <div class=\"container\">\n    <!-- En-tête avec titre et description -->\n    <div class=\"row mb-5\">\n      <div class=\"col-12\">\n        <div class=\"d-flex justify-content-between align-items-center\">\n          <div>\n            <h1 class=\"display-4 fw-bold text-primary\">{{ isEditMode ? 'Modifier l\\'équipe' : 'Nouvelle équipe' }}</h1>\n            <p class=\"text-muted lead\">\n              {{ isEditMode ? 'Modifiez les informations et les membres de votre équipe' : 'Créez une nouvelle équipe pour organiser vos projets et membres' }}\n            </p>\n          </div>\n          <button class=\"btn btn-outline-primary rounded-pill px-4 py-2 shadow-sm\" (click)=\"cancel()\">\n            <i class=\"bi bi-arrow-left me-2\"></i> Retour à la liste\n          </button>\n        </div>\n        <hr class=\"my-4\">\n      </div>\n    </div>\n\n    <!-- Loading message avec animation moderne -->\n    <div *ngIf=\"loading\" class=\"row justify-content-center my-5\">\n      <div class=\"col-md-6 text-center\">\n        <div class=\"spinner-grow text-primary mx-1\" role=\"status\">\n          <span class=\"visually-hidden\">Chargement...</span>\n        </div>\n        <div class=\"spinner-grow text-secondary mx-1\" role=\"status\">\n          <span class=\"visually-hidden\">Chargement...</span>\n        </div>\n        <div class=\"spinner-grow text-primary mx-1\" role=\"status\">\n          <span class=\"visually-hidden\">Chargement...</span>\n        </div>\n        <p class=\"mt-3 text-muted\">Chargement des données...</p>\n      </div>\n    </div>\n\n    <!-- Error message avec style moderne -->\n    <div *ngIf=\"error\" class=\"row mb-4\">\n      <div class=\"col-12\">\n        <div class=\"alert alert-danger shadow-sm border-0 rounded-3 d-flex align-items-center\">\n          <i class=\"bi bi-exclamation-triangle-fill fs-3 me-3\"></i>\n          <div class=\"flex-grow-1\">{{ error }}</div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Formulaire avec design moderne -->\n    <div class=\"row justify-content-center\" *ngIf=\"!loading\">\n      <div class=\"col-lg-8\">\n        <div class=\"card border-0 shadow-sm rounded-3 overflow-hidden\">\n          <div class=\"card-header bg-gradient-primary text-white border-0 py-4\">\n            <h3 class=\"mb-0\">\n              <i class=\"bi\" [ngClass]=\"{'bi-pencil-square': isEditMode, 'bi-plus-circle': !isEditMode}\"></i>\n              {{ isEditMode ? 'Informations de l\\'équipe' : 'Détails de la nouvelle équipe' }}\n            </h3>\n          </div>\n\n          <div class=\"card-body p-4\">\n            <form (ngSubmit)=\"onSubmit()\" class=\"row g-3\">\n              <!-- Nom de l'équipe -->\n              <div class=\"col-12 mb-3\">\n                <label for=\"name\" class=\"form-label fw-medium\">Nom de l'équipe <span class=\"text-danger\">*</span></label>\n                <div class=\"input-group\">\n                  <span class=\"input-group-text bg-light border-0\">\n                    <i class=\"bi bi-people-fill text-primary\"></i>\n                  </span>\n                  <input\n                    #nameInput\n                    type=\"text\"\n                    class=\"form-control bg-light border-0\"\n                    [class.is-invalid]=\"nameExists || (nameError && nameInput.value.length > 0)\"\n                    id=\"name\"\n                    [value]=\"equipe.name || ''\"\n                    (input)=\"updateName(nameInput.value)\"\n                    required\n                    minlength=\"3\"\n                    placeholder=\"Entrez le nom de l'équipe\"\n                  >\n                </div>\n                <div *ngIf=\"nameExists\" class=\"invalid-feedback d-block small mt-1\">\n                  <i class=\"bi bi-exclamation-triangle-fill me-1\"></i>\n                  Ce nom d'équipe existe déjà. Veuillez en choisir un autre.\n                </div>\n                <div *ngIf=\"nameError && nameInput.value.length > 0\" class=\"invalid-feedback d-block small mt-1\">\n                  <i class=\"bi bi-exclamation-triangle-fill me-1\"></i>\n                  Le nom de l'équipe doit contenir au moins 3 caractères.\n                </div>\n                <div *ngIf=\"error && !equipe.name\" class=\"text-danger small mt-1\">\n                  <i class=\"bi bi-exclamation-circle-fill me-1\"></i>\n                  Le nom de l'équipe est requis.\n                </div>\n              </div>\n\n              <!-- Description de l'équipe -->\n              <div class=\"col-12 mb-3\">\n                <label for=\"description\" class=\"form-label fw-medium\">Description <span class=\"text-danger\">*</span></label>\n                <div class=\"input-group\">\n                  <span class=\"input-group-text bg-light border-0 align-self-start\">\n                    <i class=\"bi bi-card-text text-primary\"></i>\n                  </span>\n                  <textarea\n                    #descInput\n                    class=\"form-control bg-light border-0\"\n                    id=\"description\"\n                    rows=\"4\"\n                    [class.is-invalid]=\"descriptionError && descInput.value.length > 0\"\n                    [value]=\"equipe.description || ''\"\n                    (input)=\"updateDescription(descInput.value)\"\n                    required\n                    minlength=\"10\"\n                    placeholder=\"Décrivez l'objectif et les activités de cette équipe\"\n                  ></textarea>\n                </div>\n                <div *ngIf=\"descriptionError && descInput.value.length > 0\" class=\"invalid-feedback d-block small mt-1\">\n                  <i class=\"bi bi-exclamation-triangle-fill me-1\"></i>\n                  La description doit contenir au moins 10 caractères.\n                </div>\n                <div *ngIf=\"error && !equipe.description\" class=\"text-danger small mt-1\">\n                  <i class=\"bi bi-exclamation-circle-fill me-1\"></i>\n                  La description de l'équipe est requise.\n                </div>\n              </div>\n\n              <!-- Admin field - hidden for now, using default value -->\n              <input type=\"hidden\" [value]=\"equipe.admin\">\n              <div class=\"col-12 mb-3\">\n                <div class=\"alert alert-info border-0 rounded-3 shadow-sm d-flex align-items-center\">\n                  <i class=\"bi bi-info-circle-fill fs-4 me-3 text-primary\"></i>\n                  <div>Un administrateur par défaut sera assigné à cette équipe.</div>\n                </div>\n              </div>\n\n              <!-- Utilisateurs membres de l'équipe (visible uniquement en mode édition) -->\n              <div *ngIf=\"isEditMode && equipe._id\" class=\"col-12 mt-4\">\n                <div class=\"card border-0 shadow-sm rounded-3 mb-4\">\n                  <div class=\"card-header bg-light border-0 py-3\">\n                    <h4 class=\"mb-0 d-flex align-items-center\">\n                      <i class=\"bi bi-people-fill text-primary me-2\"></i>\n                      Membres de l'équipe\n                    </h4>\n                  </div>\n\n                  <div class=\"card-body p-4\">\n                    <!-- Liste des membres actuels -->\n                    <div *ngIf=\"equipe.members && equipe.members.length > 0; else noMembers\">\n                      <div class=\"table-responsive\">\n                        <table class=\"table table-hover align-middle\">\n                          <thead class=\"table-light\">\n                            <tr>\n                              <th>\n                                <div class=\"d-flex align-items-center\">\n                                  <i class=\"bi bi-person text-primary me-2\"></i> Nom et Prénom\n                                </div>\n                              </th>\n                              <th>\n                                <div class=\"d-flex align-items-center\">\n                                  <i class=\"bi bi-envelope text-primary me-2\"></i> Email\n                                </div>\n                              </th>\n                              <th>\n                                <div class=\"d-flex align-items-center\">\n                                  <i class=\"bi bi-briefcase text-primary me-2\"></i> Statut\n                                </div>\n                              </th>\n                              <th class=\"text-center\">Actions</th>\n                            </tr>\n                          </thead>\n                          <tbody>\n                            <tr *ngFor=\"let membreId of equipe.members\" class=\"transition hover-row\">\n                              <td>\n                                <span class=\"fw-medium\">{{ getMembreName(membreId) }}</span>\n                                <small class=\"text-muted d-block\" *ngIf=\"getMembreName(membreId) !== membreId\">ID: {{ membreId }}</small>\n                              </td>\n                              <td>\n                                <a *ngIf=\"getMembreEmail(membreId) !== 'Non renseigné'\" href=\"mailto:{{ getMembreEmail(membreId) }}\" class=\"text-decoration-none\">\n                                  {{ getMembreEmail(membreId) }}\n                                </a>\n                                <span *ngIf=\"getMembreEmail(membreId) === 'Non renseigné'\" class=\"text-muted fst-italic\">Non renseigné</span>\n                              </td>\n                              <td>\n                                <span class=\"badge rounded-pill px-3 py-2 shadow-sm\" [ngClass]=\"{\n                                  'bg-primary': getMembreProfession(membreId) === 'Étudiant',\n                                  'bg-success': getMembreProfession(membreId) === 'Professeur',\n                                  'bg-secondary': getMembreProfession(membreId) === 'Non spécifié'\n                                }\">\n                                  <i class=\"bi\" [ngClass]=\"{\n                                    'bi-mortarboard-fill': getMembreProfession(membreId) === 'Étudiant',\n                                    'bi-briefcase-fill': getMembreProfession(membreId) === 'Professeur',\n                                    'bi-question-circle-fill': getMembreProfession(membreId) === 'Non spécifié'\n                                  }\"></i>\n                                  {{ getMembreProfession(membreId) }}\n                                </span>\n                              </td>\n                              <td class=\"text-center\">\n                                <button type=\"button\" class=\"btn btn-sm btn-outline-danger rounded-circle\"\n                                        title=\"Retirer de l'équipe\"\n                                        (click)=\"removeMembreFromEquipe(membreId)\">\n                                  <i class=\"bi bi-trash\"></i>\n                                </button>\n                              </td>\n                            </tr>\n                          </tbody>\n                        </table>\n                      </div>\n                    </div>\n\n                    <ng-template #noMembers>\n                      <div class=\"text-center py-4\">\n                        <i class=\"bi bi-people fs-1 text-muted mb-3 d-block\"></i>\n                        <h5 class=\"text-muted\">Aucun membre dans cette équipe</h5>\n                        <p class=\"text-muted\">Ajoutez des membres à l'équipe en utilisant le formulaire ci-dessous.</p>\n                      </div>\n                    </ng-template>\n\n                    <!-- Formulaire pour ajouter un utilisateur comme membre -->\n                    <div class=\"mt-4\">\n                      <h5 class=\"d-flex align-items-center mb-3\">\n                        <i class=\"bi bi-person-plus-fill text-primary me-2\"></i>\n                        Ajouter un membre\n                      </h5>\n\n                      <!-- Afficher un message si aucun utilisateur n'est disponible -->\n                      <div *ngIf=\"availableUsers.length === 0\" class=\"alert alert-info border-0 rounded-3 shadow-sm d-flex align-items-center\">\n                        <i class=\"bi bi-info-circle-fill fs-4 me-3 text-primary\"></i>\n                        <div>Aucun utilisateur disponible. Veuillez d'abord créer des utilisateurs.</div>\n                      </div>\n\n                      <!-- Formulaire d'ajout d'utilisateur avec rôle -->\n                      <div *ngIf=\"availableUsers.length > 0\" class=\"card border-0 bg-light rounded-3 mb-3\">\n                        <div class=\"card-body p-4\">\n                          <div class=\"row g-3\">\n                            <!-- Sélection de l'utilisateur -->\n                            <div class=\"col-md-6\">\n                              <label for=\"userSelect\" class=\"form-label fw-medium\">Utilisateur</label>\n                              <select #userSelect id=\"userSelect\" class=\"form-select border-0 shadow-sm\">\n                                <option value=\"\" selected disabled>Sélectionnez un utilisateur</option>\n                                <option *ngFor=\"let user of availableUsers\" [value]=\"user._id || user.id\">\n                                  {{ user.firstName || '' }} {{ user.lastName || user.name || user.id }}\n                                  {{ user.email ? '- ' + user.email : '' }}\n                                  {{ user.profession ? '(' + (user.profession === 'etudiant' ? 'Étudiant' : 'Professeur') + ')' :\n                                     user.role ? '(' + (user.role === 'etudiant' ? 'Étudiant' : 'Professeur') + ')' : '' }}\n                                </option>\n                              </select>\n                            </div>\n\n                            <!-- Sélection du rôle dans l'équipe -->\n                            <div class=\"col-md-4\">\n                              <label for=\"roleSelect\" class=\"form-label fw-medium\">Rôle dans l'équipe</label>\n                              <select #roleSelect id=\"roleSelect\" class=\"form-select border-0 shadow-sm\">\n                                <option value=\"membre\" selected>Membre</option>\n                                <option value=\"admin\">Administrateur</option>\n                              </select>\n                            </div>\n\n                            <!-- Bouton d'ajout -->\n                            <div class=\"col-md-2 d-flex align-items-end\">\n                              <button type=\"button\" class=\"btn btn-primary rounded-pill w-100 shadow-sm\"\n                                      [disabled]=\"!userSelect.value\"\n                                      (click)=\"addMembreToEquipe(userSelect.value, roleSelect.value); userSelect.value = ''\"\n                                      id=\"addMembreButton\">\n                                <i class=\"bi bi-plus-circle me-1\"></i> Ajouter\n                              </button>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <!-- Boutons d'action -->\n              <div class=\"col-12 mt-4\">\n                <div class=\"d-flex gap-3 justify-content-between\">\n                  <div>\n                    <button\n                      type=\"button\"\n                      class=\"btn btn-outline-secondary rounded-pill px-4 py-2\"\n                      (click)=\"cancel()\"\n                    >\n                      <i class=\"bi bi-arrow-left me-2\"></i>\n                      Retour\n                    </button>\n\n                    <!-- Bouton de suppression (visible uniquement en mode édition) -->\n                    <button\n                      *ngIf=\"isEditMode && equipeId\"\n                      type=\"button\"\n                      class=\"btn btn-outline-danger rounded-pill px-4 py-2 ms-2\"\n                      (click)=\"deleteEquipe()\"\n                    >\n                      <i class=\"bi bi-trash me-2\"></i>\n                      Supprimer\n                    </button>\n                  </div>\n\n                  <button\n                    type=\"submit\"\n                    class=\"btn btn-primary rounded-pill px-4 py-2 shadow-sm\"\n                    [disabled]=\"submitting || !equipe.name || !equipe.description || nameExists || nameError || descriptionError\"\n                  >\n                    <span *ngIf=\"submitting\" class=\"spinner-border spinner-border-sm me-2\"></span>\n                    <i *ngIf=\"!submitting\" class=\"bi\" [ngClass]=\"{'bi-save': isEditMode, 'bi-plus-circle': !isEditMode}\"></i>\n                    {{ isEditMode ? 'Mettre à jour' : 'Créer l\\'équipe' }}\n                  </button>\n                </div>\n              </div>\n            </form>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n\n<!-- Styles spécifiques pour cette page -->\n<style>\n  /* Fond dégradé pour l'en-tête du formulaire */\n  .bg-gradient-primary {\n    background: linear-gradient(45deg, #007bff, #6610f2) !important;\n  }\n\n  /* Animation au survol des lignes du tableau */\n  .transition {\n    transition: all 0.2s ease;\n  }\n\n  .hover-row:hover {\n    background-color: rgba(13, 110, 253, 0.05) !important;\n    transform: translateY(-2px);\n    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);\n  }\n\n  /* Style pour les inputs */\n  .form-control:focus, .form-select:focus {\n    border-color: #86b7fe;\n    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);\n  }\n\n  /* Animation pour les boutons */\n  .btn {\n    transition: all 0.3s ease;\n  }\n\n  .btn:hover {\n    transform: translateY(-2px);\n  }\n</style>"], "mappings": ";;;;;;;;;;ICqBIA,EAAA,CAAAC,cAAA,cAA6D;IAGzBD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEpDH,EAAA,CAAAC,cAAA,cAA4D;IAC5BD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEpDH,EAAA,CAAAC,cAAA,cAA0D;IAC1BD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEpDH,EAAA,CAAAC,cAAA,aAA2B;IAAAD,EAAA,CAAAE,MAAA,sCAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAK5DH,EAAA,CAAAC,cAAA,cAAoC;IAG9BD,EAAA,CAAAI,SAAA,YAAyD;IACzDJ,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAE,MAAA,GAAW;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAAjBH,EAAA,CAAAK,SAAA,GAAW;IAAXL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,KAAA,CAAW;;;;;IAsC9BR,EAAA,CAAAC,cAAA,cAAoE;IAClED,EAAA,CAAAI,SAAA,YAAoD;IACpDJ,EAAA,CAAAE,MAAA,kFACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IACNH,EAAA,CAAAC,cAAA,cAAiG;IAC/FD,EAAA,CAAAI,SAAA,YAAoD;IACpDJ,EAAA,CAAAE,MAAA,0EACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IACNH,EAAA,CAAAC,cAAA,cAAkE;IAChED,EAAA,CAAAI,SAAA,YAAkD;IAClDJ,EAAA,CAAAE,MAAA,4CACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAuBNH,EAAA,CAAAC,cAAA,cAAwG;IACtGD,EAAA,CAAAI,SAAA,YAAoD;IACpDJ,EAAA,CAAAE,MAAA,kEACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IACNH,EAAA,CAAAC,cAAA,cAAyE;IACvED,EAAA,CAAAI,SAAA,YAAkD;IAClDJ,EAAA,CAAAE,MAAA,qDACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAmDUH,EAAA,CAAAC,cAAA,gBAA+E;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAA1BH,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAS,kBAAA,SAAAC,YAAA,KAAkB;;;;;IAGjGV,EAAA,CAAAC,cAAA,YAAkI;IAChID,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAFoDH,EAAA,CAAAW,sBAAA,oBAAAC,OAAA,CAAAC,cAAA,CAAAH,YAAA,OAAAV,EAAA,CAAAc,aAAA,CAA4C;IAClGd,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAG,OAAA,CAAAC,cAAA,CAAAH,YAAA,OACF;;;;;IACAV,EAAA,CAAAC,cAAA,eAAyF;IAAAD,EAAA,CAAAE,MAAA,yBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;;;;;;;;;;;;;;;IATjHH,EAAA,CAAAC,cAAA,aAAyE;IAE7CD,EAAA,CAAAE,MAAA,GAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5DH,EAAA,CAAAe,UAAA,IAAAC,8DAAA,oBAAyG;IAC3GhB,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAe,UAAA,IAAAE,0DAAA,gBAEI;IACJjB,EAAA,CAAAe,UAAA,IAAAG,6DAAA,mBAA6G;IAC/GlB,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IAMAD,EAAA,CAAAI,SAAA,aAIO;IACPJ,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAETH,EAAA,CAAAC,cAAA,cAAwB;IAGdD,EAAA,CAAAmB,UAAA,mBAAAC,gFAAA;MAAA,MAAAC,WAAA,GAAArB,EAAA,CAAAsB,aAAA,CAAAC,IAAA;MAAA,MAAAb,YAAA,GAAAW,WAAA,CAAAG,SAAA;MAAA,MAAAC,OAAA,GAAAzB,EAAA,CAAA0B,aAAA;MAAA,OAAS1B,EAAA,CAAA2B,WAAA,CAAAF,OAAA,CAAAG,sBAAA,CAAAlB,YAAA,CAAgC;IAAA,EAAC;IAChDV,EAAA,CAAAI,SAAA,aAA2B;IAC7BJ,EAAA,CAAAG,YAAA,EAAS;;;;;IA5BeH,EAAA,CAAAK,SAAA,GAA6B;IAA7BL,EAAA,CAAAM,iBAAA,CAAAuB,OAAA,CAAAC,aAAA,CAAApB,YAAA,EAA6B;IAClBV,EAAA,CAAAK,SAAA,GAA0C;IAA1CL,EAAA,CAAA+B,UAAA,SAAAF,OAAA,CAAAC,aAAA,CAAApB,YAAA,MAAAA,YAAA,CAA0C;IAGzEV,EAAA,CAAAK,SAAA,GAAkD;IAAlDL,EAAA,CAAA+B,UAAA,SAAAF,OAAA,CAAAhB,cAAA,CAAAH,YAAA,2BAAkD;IAG/CV,EAAA,CAAAK,SAAA,GAAkD;IAAlDL,EAAA,CAAA+B,UAAA,SAAAF,OAAA,CAAAhB,cAAA,CAAAH,YAAA,2BAAkD;IAGJV,EAAA,CAAAK,SAAA,GAInD;IAJmDL,EAAA,CAAA+B,UAAA,YAAA/B,EAAA,CAAAgC,eAAA,IAAAC,GAAA,EAAAJ,OAAA,CAAAK,mBAAA,CAAAxB,YAAA,uBAAAmB,OAAA,CAAAK,mBAAA,CAAAxB,YAAA,oBAAAmB,OAAA,CAAAK,mBAAA,CAAAxB,YAAA,gCAInD;IACcV,EAAA,CAAAK,SAAA,GAIZ;IAJYL,EAAA,CAAA+B,UAAA,YAAA/B,EAAA,CAAAgC,eAAA,KAAAG,GAAA,EAAAN,OAAA,CAAAK,mBAAA,CAAAxB,YAAA,uBAAAmB,OAAA,CAAAK,mBAAA,CAAAxB,YAAA,oBAAAmB,OAAA,CAAAK,mBAAA,CAAAxB,YAAA,gCAIZ;IACFV,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAoB,OAAA,CAAAK,mBAAA,CAAAxB,YAAA,OACF;;;;;IA/CZV,EAAA,CAAAC,cAAA,UAAyE;IAO3DD,EAAA,CAAAI,SAAA,YAA8C;IAACJ,EAAA,CAAAE,MAAA,2BACjD;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,SAAI;IAEAD,EAAA,CAAAI,SAAA,aAAgD;IAACJ,EAAA,CAAAE,MAAA,eACnD;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,UAAI;IAEAD,EAAA,CAAAI,SAAA,aAAiD;IAACJ,EAAA,CAAAE,MAAA,gBACpD;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,cAAwB;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAGxCH,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAAe,UAAA,KAAAqB,sDAAA,mBAgCK;IACPpC,EAAA,CAAAG,YAAA,EAAQ;;;;IAjCmBH,EAAA,CAAAK,SAAA,IAAiB;IAAjBL,EAAA,CAAA+B,UAAA,YAAAM,OAAA,CAAAC,MAAA,CAAAC,OAAA,CAAiB;;;;;IAuChDvC,EAAA,CAAAC,cAAA,cAA8B;IAC5BD,EAAA,CAAAI,SAAA,YAAyD;IACzDJ,EAAA,CAAAC,cAAA,aAAuB;IAAAD,EAAA,CAAAE,MAAA,0CAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1DH,EAAA,CAAAC,cAAA,YAAsB;IAAAD,EAAA,CAAAE,MAAA,sFAAqE;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAYjGH,EAAA,CAAAC,cAAA,cAAyH;IACvHD,EAAA,CAAAI,SAAA,YAA6D;IAC7DJ,EAAA,CAAAC,cAAA,UAAK;IAAAD,EAAA,CAAAE,MAAA,kFAAsE;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAYzEH,EAAA,CAAAC,cAAA,kBAA0E;IACxED,EAAA,CAAAE,MAAA,GAIF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IALmCH,EAAA,CAAA+B,UAAA,UAAAS,QAAA,CAAAC,GAAA,IAAAD,QAAA,CAAAE,EAAA,CAA6B;IACvE1C,EAAA,CAAAK,SAAA,GAIF;IAJEL,EAAA,CAAA2C,kBAAA,MAAAH,QAAA,CAAAI,SAAA,aAAAJ,QAAA,CAAAK,QAAA,IAAAL,QAAA,CAAAM,IAAA,IAAAN,QAAA,CAAAE,EAAA,OAAAF,QAAA,CAAAO,KAAA,UAAAP,QAAA,CAAAO,KAAA,YAAAP,QAAA,CAAAQ,UAAA,UAAAR,QAAA,CAAAQ,UAAA,0DAAAR,QAAA,CAAAS,IAAA,UAAAT,QAAA,CAAAS,IAAA,kEAIF;;;;;;IAbVjD,EAAA,CAAAC,cAAA,cAAqF;IAKxBD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACxEH,EAAA,CAAAC,cAAA,sBAA2E;IACtCD,EAAA,CAAAE,MAAA,uCAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACvEH,EAAA,CAAAe,UAAA,KAAAmC,2DAAA,sBAKS;IACXlD,EAAA,CAAAG,YAAA,EAAS;IAIXH,EAAA,CAAAC,cAAA,gBAAsB;IACiCD,EAAA,CAAAE,MAAA,oCAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC/EH,EAAA,CAAAC,cAAA,wBAA2E;IACzCD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC/CH,EAAA,CAAAC,cAAA,mBAAsB;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAKjDH,EAAA,CAAAC,cAAA,gBAA6C;IAGnCD,EAAA,CAAAmB,UAAA,mBAAAgC,2EAAA;MAAAnD,EAAA,CAAAsB,aAAA,CAAA8B,IAAA;MAAA,MAAAC,IAAA,GAAArD,EAAA,CAAAsD,WAAA;MAAA,MAAAC,IAAA,GAAAvD,EAAA,CAAAsD,WAAA;MAAA,MAAAE,OAAA,GAAAxD,EAAA,CAAA0B,aAAA;MAAS8B,OAAA,CAAAC,iBAAA,CAAAJ,IAAA,CAAAK,KAAA,EAAAH,IAAA,CAAAG,KAAA,CAAqD;MAAA,OAAE1D,EAAA,CAAA2B,WAAA,CAAA0B,IAAA,CAAAK,KAAA,GAAmB,EAAE;IAAA,EAAC;IAE5F1D,EAAA,CAAAI,SAAA,cAAsC;IAACJ,EAAA,CAAAE,MAAA,iBACzC;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAzBkBH,EAAA,CAAAK,SAAA,IAAiB;IAAjBL,EAAA,CAAA+B,UAAA,YAAA4B,OAAA,CAAAC,cAAA,CAAiB;IAqBpC5D,EAAA,CAAAK,SAAA,IAA8B;IAA9BL,EAAA,CAAA+B,UAAA,cAAAsB,IAAA,CAAAK,KAAA,CAA8B;;;;;IA5HtD1D,EAAA,CAAAC,cAAA,cAA0D;IAIlDD,EAAA,CAAAI,SAAA,YAAmD;IACnDJ,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAGPH,EAAA,CAAAC,cAAA,cAA2B;IAEzBD,EAAA,CAAAe,UAAA,IAAA8C,gDAAA,mBA4DM;IAEN7D,EAAA,CAAAe,UAAA,IAAA+C,wDAAA,iCAAA9D,EAAA,CAAA+D,sBAAA,CAMc;IAGd/D,EAAA,CAAAC,cAAA,eAAkB;IAEdD,EAAA,CAAAI,SAAA,aAAwD;IACxDJ,EAAA,CAAAE,MAAA,2BACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAGLH,EAAA,CAAAe,UAAA,KAAAiD,iDAAA,kBAGM;IAGNhE,EAAA,CAAAe,UAAA,KAAAkD,iDAAA,mBAqCM;IACRjE,EAAA,CAAAG,YAAA,EAAM;;;;;IA1HAH,EAAA,CAAAK,SAAA,GAAmD;IAAnDL,EAAA,CAAA+B,UAAA,SAAAmC,OAAA,CAAA5B,MAAA,CAAAC,OAAA,IAAA2B,OAAA,CAAA5B,MAAA,CAAAC,OAAA,CAAA4B,MAAA,KAAmD,aAAAC,IAAA;IA8EjDpE,EAAA,CAAAK,SAAA,GAAiC;IAAjCL,EAAA,CAAA+B,UAAA,SAAAmC,OAAA,CAAAN,cAAA,CAAAO,MAAA,OAAiC;IAMjCnE,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAA+B,UAAA,SAAAmC,OAAA,CAAAN,cAAA,CAAAO,MAAA,KAA+B;;;;;;IAyDvCnE,EAAA,CAAAC,cAAA,kBAKC;IADCD,EAAA,CAAAmB,UAAA,mBAAAkD,sEAAA;MAAArE,EAAA,CAAAsB,aAAA,CAAAgD,IAAA;MAAA,MAAAC,OAAA,GAAAvE,EAAA,CAAA0B,aAAA;MAAA,OAAS1B,EAAA,CAAA2B,WAAA,CAAA4C,OAAA,CAAAC,YAAA,EAAc;IAAA,EAAC;IAExBxE,EAAA,CAAAI,SAAA,aAAgC;IAChCJ,EAAA,CAAAE,MAAA,kBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAQTH,EAAA,CAAAI,SAAA,gBAA8E;;;;;;;;;;;IAC9EJ,EAAA,CAAAI,SAAA,YAAyG;;;;IAAvEJ,EAAA,CAAA+B,UAAA,YAAA/B,EAAA,CAAAyE,eAAA,IAAAC,GAAA,EAAAC,OAAA,CAAAC,UAAA,GAAAD,OAAA,CAAAC,UAAA,EAAkE;;;;;;;;;;;;IA/PpH5E,EAAA,CAAAC,cAAA,cAAyD;IAK/CD,EAAA,CAAAI,SAAA,YAA8F;IAC9FJ,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAGPH,EAAA,CAAAC,cAAA,cAA2B;IACnBD,EAAA,CAAAmB,UAAA,sBAAA0D,6DAAA;MAAA7E,EAAA,CAAAsB,aAAA,CAAAwD,IAAA;MAAA,MAAAC,OAAA,GAAA/E,EAAA,CAAA0B,aAAA;MAAA,OAAY1B,EAAA,CAAA2B,WAAA,CAAAoD,OAAA,CAAAC,QAAA,EAAU;IAAA,EAAC;IAE3BhF,EAAA,CAAAC,cAAA,cAAyB;IACwBD,EAAA,CAAAE,MAAA,6BAAgB;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACjGH,EAAA,CAAAC,cAAA,eAAyB;IAErBD,EAAA,CAAAI,SAAA,aAA8C;IAChDJ,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,qBAWC;IAJCD,EAAA,CAAAmB,UAAA,mBAAA8D,4DAAA;MAAAjF,EAAA,CAAAsB,aAAA,CAAAwD,IAAA;MAAA,MAAAI,GAAA,GAAAlF,EAAA,CAAAsD,WAAA;MAAA,MAAA6B,OAAA,GAAAnF,EAAA,CAAA0B,aAAA;MAAA,OAAS1B,EAAA,CAAA2B,WAAA,CAAAwD,OAAA,CAAAC,UAAA,CAAAF,GAAA,CAAAxB,KAAA,CAA2B;IAAA,EAAC;IAPvC1D,EAAA,CAAAG,YAAA,EAWC;IAEHH,EAAA,CAAAe,UAAA,KAAAsE,0CAAA,kBAGM;IACNrF,EAAA,CAAAe,UAAA,KAAAuE,0CAAA,kBAGM;IACNtF,EAAA,CAAAe,UAAA,KAAAwE,0CAAA,kBAGM;IACRvF,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAAyB;IAC+BD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACpGH,EAAA,CAAAC,cAAA,eAAyB;IAErBD,EAAA,CAAAI,SAAA,aAA4C;IAC9CJ,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,wBAWC;IAJCD,EAAA,CAAAmB,UAAA,mBAAAqE,+DAAA;MAAAxF,EAAA,CAAAsB,aAAA,CAAAwD,IAAA;MAAA,MAAAW,GAAA,GAAAzF,EAAA,CAAAsD,WAAA;MAAA,MAAAoC,OAAA,GAAA1F,EAAA,CAAA0B,aAAA;MAAA,OAAS1B,EAAA,CAAA2B,WAAA,CAAA+D,OAAA,CAAAC,iBAAA,CAAAF,GAAA,CAAA/B,KAAA,CAAkC;IAAA,EAAC;IAI7C1D,EAAA,CAAAG,YAAA,EAAW;IAEdH,EAAA,CAAAe,UAAA,KAAA6E,0CAAA,kBAGM;IACN5F,EAAA,CAAAe,UAAA,KAAA8E,0CAAA,kBAGM;IACR7F,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAI,SAAA,iBAA4C;IAC5CJ,EAAA,CAAAC,cAAA,eAAyB;IAErBD,EAAA,CAAAI,SAAA,aAA6D;IAC7DJ,EAAA,CAAAC,cAAA,WAAK;IAAAD,EAAA,CAAAE,MAAA,qFAAyD;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAKxEH,EAAA,CAAAe,UAAA,KAAA+E,0CAAA,mBAwIM;IAGN9F,EAAA,CAAAC,cAAA,eAAyB;IAMjBD,EAAA,CAAAmB,UAAA,mBAAA4E,6DAAA;MAAA/F,EAAA,CAAAsB,aAAA,CAAAwD,IAAA;MAAA,MAAAkB,OAAA,GAAAhG,EAAA,CAAA0B,aAAA;MAAA,OAAS1B,EAAA,CAAA2B,WAAA,CAAAqE,OAAA,CAAAC,MAAA,EAAQ;IAAA,EAAC;IAElBjG,EAAA,CAAAI,SAAA,YAAqC;IACrCJ,EAAA,CAAAE,MAAA,gBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAGTH,EAAA,CAAAe,UAAA,KAAAmF,6CAAA,qBAQS;IACXlG,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,kBAIC;IACCD,EAAA,CAAAe,UAAA,KAAAoF,2CAAA,mBAA8E;IAC9EnG,EAAA,CAAAe,UAAA,KAAAqF,wCAAA,gBAAyG;IACzGpG,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IA5PCH,EAAA,CAAAK,SAAA,GAA2E;IAA3EL,EAAA,CAAA+B,UAAA,YAAA/B,EAAA,CAAAyE,eAAA,KAAA4B,GAAA,EAAAC,MAAA,CAAA1B,UAAA,GAAA0B,MAAA,CAAA1B,UAAA,EAA2E;IACzF5E,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAA6F,MAAA,CAAA1B,UAAA,oFACF;IAgBQ5E,EAAA,CAAAK,SAAA,IAA4E;IAA5EL,EAAA,CAAAuG,WAAA,eAAAD,MAAA,CAAAE,UAAA,IAAAF,MAAA,CAAAG,SAAA,IAAAvB,GAAA,CAAAxB,KAAA,CAAAS,MAAA,KAA4E;IAE5EnE,EAAA,CAAA+B,UAAA,UAAAuE,MAAA,CAAAhE,MAAA,CAAAQ,IAAA,OAA2B;IAOzB9C,EAAA,CAAAK,SAAA,GAAgB;IAAhBL,EAAA,CAAA+B,UAAA,SAAAuE,MAAA,CAAAE,UAAA,CAAgB;IAIhBxG,EAAA,CAAAK,SAAA,GAA6C;IAA7CL,EAAA,CAAA+B,UAAA,SAAAuE,MAAA,CAAAG,SAAA,IAAAvB,GAAA,CAAAxB,KAAA,CAAAS,MAAA,KAA6C;IAI7CnE,EAAA,CAAAK,SAAA,GAA2B;IAA3BL,EAAA,CAAA+B,UAAA,SAAAuE,MAAA,CAAA9F,KAAA,KAAA8F,MAAA,CAAAhE,MAAA,CAAAQ,IAAA,CAA2B;IAkB7B9C,EAAA,CAAAK,SAAA,GAAmE;IAAnEL,EAAA,CAAAuG,WAAA,eAAAD,MAAA,CAAAI,gBAAA,IAAAjB,GAAA,CAAA/B,KAAA,CAAAS,MAAA,KAAmE;IACnEnE,EAAA,CAAA+B,UAAA,UAAAuE,MAAA,CAAAhE,MAAA,CAAAqE,WAAA,OAAkC;IAOhC3G,EAAA,CAAAK,SAAA,GAAoD;IAApDL,EAAA,CAAA+B,UAAA,SAAAuE,MAAA,CAAAI,gBAAA,IAAAjB,GAAA,CAAA/B,KAAA,CAAAS,MAAA,KAAoD;IAIpDnE,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAA+B,UAAA,SAAAuE,MAAA,CAAA9F,KAAA,KAAA8F,MAAA,CAAAhE,MAAA,CAAAqE,WAAA,CAAkC;IAOrB3G,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAA+B,UAAA,UAAAuE,MAAA,CAAAhE,MAAA,CAAAsE,KAAA,CAAsB;IASrC5G,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAA+B,UAAA,SAAAuE,MAAA,CAAA1B,UAAA,IAAA0B,MAAA,CAAAhE,MAAA,CAAAG,GAAA,CAA8B;IAyJ3BzC,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAA+B,UAAA,SAAAuE,MAAA,CAAA1B,UAAA,IAAA0B,MAAA,CAAAO,QAAA,CAA4B;IAa/B7G,EAAA,CAAAK,SAAA,GAA6G;IAA7GL,EAAA,CAAA+B,UAAA,aAAAuE,MAAA,CAAAQ,UAAA,KAAAR,MAAA,CAAAhE,MAAA,CAAAQ,IAAA,KAAAwD,MAAA,CAAAhE,MAAA,CAAAqE,WAAA,IAAAL,MAAA,CAAAE,UAAA,IAAAF,MAAA,CAAAG,SAAA,IAAAH,MAAA,CAAAI,gBAAA,CAA6G;IAEtG1G,EAAA,CAAAK,SAAA,GAAgB;IAAhBL,EAAA,CAAA+B,UAAA,SAAAuE,MAAA,CAAAQ,UAAA,CAAgB;IACnB9G,EAAA,CAAAK,SAAA,GAAiB;IAAjBL,EAAA,CAAA+B,UAAA,UAAAuE,MAAA,CAAAQ,UAAA,CAAiB;IACrB9G,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAA6F,MAAA,CAAA1B,UAAA,0DACF;;;AD1RlB,OAAM,MAAOmC,mBAAmB;EAmB9BC,YACUC,aAA4B,EAC5BC,aAA4B,EAC5BC,WAAwB,EACxBC,KAAqB,EACrBC,MAAc,EACdC,mBAAwC;IALxC,KAAAL,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,mBAAmB,GAAnBA,mBAAmB;IAxB7B,KAAAhF,MAAM,GAAW;MACfQ,IAAI,EAAE,EAAE;MACR6D,WAAW,EAAE,EAAE;MACfC,KAAK,EAAE,0BAA0B,CAAE;KACpC;;IACD,KAAAhC,UAAU,GAAG,KAAK;IAClB,KAAA2C,OAAO,GAAG,KAAK;IACf,KAAAT,UAAU,GAAG,KAAK;IAClB,KAAAtG,KAAK,GAAkB,IAAI;IAC3B,KAAAqG,QAAQ,GAAkB,IAAI;IAC9B,KAAAL,UAAU,GAAG,KAAK;IAClB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAc,YAAY,GAAG,KAAK;IACpB,KAAAC,eAAe,GAAa,EAAE;IAC9B,KAAAC,gBAAgB,GAAa,EAAE,CAAC,CAAC;IACjC,KAAA9D,cAAc,GAAW,EAAE,CAAC,CAAC;EAS1B;;EAEH+D,QAAQA,CAAA;IACNC,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAE9C;IACA,IAAI,CAAC,IAAI,CAACvF,MAAM,EAAE;MAChB,IAAI,CAACA,MAAM,GAAG;QACZQ,IAAI,EAAE,EAAE;QACR6D,WAAW,EAAE,EAAE;QACfC,KAAK,EAAE,0BAA0B,CAAE;OACpC;;IAGH;IACA,IAAI,CAACkB,cAAc,EAAE;IAErB;IACA,IAAI,CAACC,cAAc,EAAE;IAErB;IACA,IAAI,CAACC,YAAY,EAAE;IAEnB,IAAI;MACF;MACA,IAAI,CAACnB,QAAQ,GAAG,IAAI,CAACO,KAAK,CAACa,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC;MACtD,IAAI,CAACvD,UAAU,GAAG,CAAC,CAAC,IAAI,CAACiC,QAAQ;MACjCe,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE,IAAI,CAACjD,UAAU,EAAE,KAAK,EAAE,IAAI,CAACiC,QAAQ,CAAC;MAEhE,IAAI,IAAI,CAACjC,UAAU,IAAI,IAAI,CAACiC,QAAQ,EAAE;QACpC,IAAI,CAACuB,UAAU,CAAC,IAAI,CAACvB,QAAQ,CAAC;QAE9B;QACAwB,UAAU,CAAC,MAAK;UACdT,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAAChB,QAAQ,CAAC;UAC1De,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAACvF,MAAM,CAAC;QACxD,CAAC,EAAE,IAAI,CAAC;;KAEX,CAAC,OAAO9B,KAAK,EAAE;MACdoH,OAAO,CAACpH,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,IAAI,CAACA,KAAK,GAAG,yBAAyB;;IAGxC;IACA6H,UAAU,CAAC,MAAK;MACd,MAAMC,SAAS,GAAGC,QAAQ,CAACC,cAAc,CAAC,iBAAiB,CAAC;MAC5D,IAAIF,SAAS,EAAE;QACbV,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAC9CS,SAAS,CAACG,gBAAgB,CAAC,OAAO,EAAE,MAAK;UACvCb,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAChD,CAAC,CAAC;OACH,MAAM;QACLD,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;;IAEtD,CAAC,EAAE,IAAI,CAAC;EACV;EAEAE,cAAcA,CAAA;IACZ,IAAI,CAACb,aAAa,CAACwB,UAAU,EAAE,CAACC,SAAS,CAAC;MACxCC,IAAI,EAAGC,OAAO,IAAI;QAChB,IAAI,CAACnB,gBAAgB,GAAGmB,OAAO;QAC/BjB,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEgB,OAAO,CAAC;MACtD,CAAC;MACDrI,KAAK,EAAGA,KAAK,IAAI;QACfoH,OAAO,CAACpH,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;QAC9D,IAAI,CAACA,KAAK,GACR,2EAA2E;MAC/E;KACD,CAAC;EACJ;EAEAwH,YAAYA,CAAA;IACV,IAAI,CAACb,WAAW,EAAE,CAACwB,SAAS,CAAC;MAC3BC,IAAI,EAAGE,KAAK,IAAI;QACd,IAAI,CAAClF,cAAc,GAAGkF,KAAK;QAC3BlB,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEiB,KAAK,CAAC;MACzD,CAAC;MACDtI,KAAK,EAAGA,KAAK,IAAI;QACfoH,OAAO,CAACpH,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;QACnE,IAAI,CAACA,KAAK,GACR,gFAAgF;MACpF;KACD,CAAC;EACJ;EAEAsH,cAAcA,CAAA;IACZ,IAAI,CAACb,aAAa,CAAC8B,UAAU,EAAE,CAACJ,SAAS,CAAC;MACxCC,IAAI,EAAGI,OAAO,IAAI;QAChB,IAAI,CAACvB,eAAe,GAAGuB,OAAO;QAC9BpB,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEmB,OAAO,CAAC;MACtD,CAAC;MACDxI,KAAK,EAAGA,KAAK,IAAI;QACfoH,OAAO,CAACpH,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAChE;KACD,CAAC;EACJ;EAEA4H,UAAUA,CAAC1F,EAAU;IACnBkF,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEnF,EAAE,CAAC;IAC1C,IAAI,CAAC6E,OAAO,GAAG,IAAI;IACnB,IAAI,CAAC/G,KAAK,GAAG,IAAI;IAEjB,IAAI,CAACyG,aAAa,CAACgC,SAAS,CAACvG,EAAE,CAAC,CAACiG,SAAS,CAAC;MACzCC,IAAI,EAAGM,IAAI,IAAI;QACbtB,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEqB,IAAI,CAAC;QACpC,IAAI,CAAC5G,MAAM,GAAG4G,IAAI;QAElB;QACAtB,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE,IAAI,CAACvF,MAAM,CAACG,GAAG,CAAC;QAChEmF,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAAChB,QAAQ,CAAC;QAE5C;QACA,IAAI,IAAI,CAACvE,MAAM,CAACC,OAAO,IAAI,IAAI,CAACD,MAAM,CAACC,OAAO,CAAC4B,MAAM,GAAG,CAAC,EAAE;UACzD,IAAI,CAACgF,kBAAkB,EAAE;;QAG3B,IAAI,CAAC5B,OAAO,GAAG,KAAK;MACtB,CAAC;MACD/G,KAAK,EAAGA,KAAK,IAAI;QACfoH,OAAO,CAACpH,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;QAC9D,IAAI,CAACA,KAAK,GACR,8EAA8E;QAChF,IAAI,CAAC+G,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEA;EACA4B,kBAAkBA,CAAA;IAChB,IAAI,CAAC,IAAI,CAAC7G,MAAM,CAACC,OAAO,IAAI,IAAI,CAACD,MAAM,CAACC,OAAO,CAAC4B,MAAM,KAAK,CAAC,EAAE;MAC5D;;IAGFyD,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;IAEhE;IACA,IAAI,CAACvF,MAAM,CAACC,OAAO,CAAC6G,OAAO,CAAEC,QAAQ,IAAI;MACvC;MACA,MAAMC,IAAI,GAAG,IAAI,CAAC1F,cAAc,CAAC2F,IAAI,CAClCC,CAAC,IAAKA,CAAC,CAAC/G,GAAG,KAAK4G,QAAQ,IAAIG,CAAC,CAAC9G,EAAE,KAAK2G,QAAQ,CAC/C;MACD,IAAIC,IAAI,EAAE;QACR1B,OAAO,CAACC,GAAG,CACT,UAAUwB,QAAQ,yCAAyC,EAC3DC,IAAI,CACL;QAED;QACA,IAAI,CAACA,IAAI,CAACvG,KAAK,IAAK,CAACuG,IAAI,CAACtG,UAAU,IAAI,CAACsG,IAAI,CAACrG,IAAK,EAAE;UACnD;UACA,IAAI,CAACkE,WAAW,CAACsC,OAAO,CAACJ,QAAQ,CAAC,CAACV,SAAS,CAAC;YAC3CC,IAAI,EAAGc,QAAQ,IAAI;cACjB9B,OAAO,CAACC,GAAG,CACT,4CAA4CwB,QAAQ,aAAa,EACjEK,QAAQ,CACT;cAED;cACA,MAAMC,KAAK,GAAG,IAAI,CAAC/F,cAAc,CAACgG,SAAS,CACxCJ,CAAC,IAAKA,CAAC,CAAC/G,GAAG,KAAK4G,QAAQ,IAAIG,CAAC,CAAC9G,EAAE,KAAK2G,QAAQ,CAC/C;cACD,IAAIM,KAAK,KAAK,CAAC,CAAC,EAAE;gBAChB,IAAI,CAAC/F,cAAc,CAAC+F,KAAK,CAAC,GAAG;kBAC3B,GAAG,IAAI,CAAC/F,cAAc,CAAC+F,KAAK,CAAC;kBAC7B,GAAGD;iBACJ;;YAEL,CAAC;YACDlJ,KAAK,EAAGA,KAAK,IAAI;cACfoH,OAAO,CAACpH,KAAK,CACX,+EAA+E6I,QAAQ,GAAG,EAC1F7I,KAAK,CACN;YACH;WACD,CAAC;;OAEL,MAAM;QACL;QACA,IAAI,CAAC2G,WAAW,CAACsC,OAAO,CAACJ,QAAQ,CAAC,CAACV,SAAS,CAAC;UAC3CC,IAAI,EAAGc,QAAQ,IAAI;YACjB9B,OAAO,CAACC,GAAG,CACT,4BAA4BwB,QAAQ,aAAa,EACjDK,QAAQ,CACT;YACD;YACA,IACE,CAAC,IAAI,CAAC9F,cAAc,CAACiG,IAAI,CACtBL,CAAC,IAAKA,CAAC,CAAC/G,GAAG,KAAKiH,QAAQ,CAACjH,GAAG,IAAI+G,CAAC,CAAC9G,EAAE,KAAKgH,QAAQ,CAAChH,EAAE,CACtD,EACD;cACA,IAAI,CAACkB,cAAc,CAACkG,IAAI,CAACJ,QAAQ,CAAC;;UAEtC,CAAC;UACDlJ,KAAK,EAAGA,KAAK,IAAI;YACfoH,OAAO,CAACpH,KAAK,CACX,+DAA+D6I,QAAQ,GAAG,EAC1E7I,KAAK,CACN;UACH;SACD,CAAC;;IAEN,CAAC,CAAC;EACJ;EAEAuJ,eAAeA,CAACjH,IAAY;IAC1B;IACA,IAAI,IAAI,CAAC8B,UAAU,IAAI,IAAI,CAACiC,QAAQ,EAAE;MACpC,OAAO,IAAI,CAACY,eAAe,CAACoC,IAAI,CAC7BG,CAAC,IAAKA,CAAC,CAAClH,IAAI,KAAKA,IAAI,IAAIkH,CAAC,CAACvH,GAAG,KAAK,IAAI,CAACoE,QAAQ,CAClD;;IAEH;IACA,OAAO,IAAI,CAACY,eAAe,CAACoC,IAAI,CAAEG,CAAC,IAAKA,CAAC,CAAClH,IAAI,KAAKA,IAAI,CAAC;EAC1D;EAEAsC,UAAUA,CAAC1B,KAAa;IACtBkE,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEnE,KAAK,CAAC;IACnC,IAAI,CAACpB,MAAM,CAACQ,IAAI,GAAGY,KAAK;IAExB;IACA,IAAI,CAAC8C,UAAU,GAAG,IAAI,CAACuD,eAAe,CAACrG,KAAK,CAAC;IAC7C,IAAI,IAAI,CAAC8C,UAAU,EAAE;MACnBoB,OAAO,CAACqC,IAAI,CAAC,6BAA6B,CAAC;;IAG7C;IACA,IAAI,CAACxD,SAAS,GAAG/C,KAAK,CAACS,MAAM,GAAG,CAAC,IAAIT,KAAK,CAACS,MAAM,GAAG,CAAC;IACrD,IAAI,IAAI,CAACsC,SAAS,EAAE;MAClBmB,OAAO,CAACqC,IAAI,CAAC,4CAA4C,CAAC;;EAE9D;EAEAtE,iBAAiBA,CAACjC,KAAa;IAC7BkE,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEnE,KAAK,CAAC;IAC1C,IAAI,CAACpB,MAAM,CAACqE,WAAW,GAAGjD,KAAK;IAE/B;IACA,IAAI,CAACgD,gBAAgB,GAAGhD,KAAK,CAACS,MAAM,GAAG,CAAC,IAAIT,KAAK,CAACS,MAAM,GAAG,EAAE;IAC7D,IAAI,IAAI,CAACuC,gBAAgB,EAAE;MACzBkB,OAAO,CAACqC,IAAI,CAAC,qDAAqD,CAAC;;EAEvE;EAEAjF,QAAQA,CAAA;IACN4C,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAACvF,MAAM,CAAC;IAEhD;IACA,IAAI,CAAC,IAAI,CAACA,MAAM,CAACQ,IAAI,EAAE;MACrB,IAAI,CAACtC,KAAK,GAAG,gCAAgC;MAC7C;;IAGF,IAAI,IAAI,CAAC8B,MAAM,CAACQ,IAAI,CAACqB,MAAM,GAAG,CAAC,EAAE;MAC/B,IAAI,CAACsC,SAAS,GAAG,IAAI;MACrB,IAAI,CAACjG,KAAK,GAAG,yDAAyD;MACtE;;IAGF;IACA,IAAI,CAAC,IAAI,CAAC8B,MAAM,CAACqE,WAAW,EAAE;MAC5B,IAAI,CAACnG,KAAK,GAAG,yCAAyC;MACtD;;IAGF,IAAI,IAAI,CAAC8B,MAAM,CAACqE,WAAW,CAACxC,MAAM,GAAG,EAAE,EAAE;MACvC,IAAI,CAACuC,gBAAgB,GAAG,IAAI;MAC5B,IAAI,CAAClG,KAAK,GACR,kEAAkE;MACpE;;IAGF;IACA,IAAI,IAAI,CAACuJ,eAAe,CAAC,IAAI,CAACzH,MAAM,CAACQ,IAAI,CAAC,EAAE;MAC1C,IAAI,CAAC0D,UAAU,GAAG,IAAI;MACtB,IAAI,CAAChG,KAAK,GACR,oEAAoE;MACtE;;IAGF,IAAI,CAACsG,UAAU,GAAG,IAAI;IACtB,IAAI,CAACtG,KAAK,GAAG,IAAI;IAEjB;IACA,MAAM0J,YAAY,GAAW;MAC3BpH,IAAI,EAAE,IAAI,CAACR,MAAM,CAACQ,IAAI;MACtB6D,WAAW,EAAE,IAAI,CAACrE,MAAM,CAACqE,WAAW,IAAI,EAAE;MAC1CC,KAAK,EAAE,IAAI,CAACtE,MAAM,CAACsE;KACpB;IAED;IACA,IAAI,IAAI,CAAChC,UAAU,IAAI,IAAI,CAACiC,QAAQ,EAAE;MACpCqD,YAAY,CAACzH,GAAG,GAAG,IAAI,CAACoE,QAAQ;;IAGlCe,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEqC,YAAY,CAAC;IAE/C,IAAI,IAAI,CAACtF,UAAU,IAAI,IAAI,CAACiC,QAAQ,EAAE;MACpC;MACA,IAAI,CAACI,aAAa,CAACkD,YAAY,CAAC,IAAI,CAACtD,QAAQ,EAAEqD,YAAY,CAAC,CAACvB,SAAS,CAAC;QACrEC,IAAI,EAAGwB,QAAQ,IAAI;UACjBxC,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEuC,QAAQ,CAAC;UACxD,IAAI,CAACtD,UAAU,GAAG,KAAK;UACvB,IAAI,CAACQ,mBAAmB,CAAC+C,WAAW,CAClC,aAAaD,QAAQ,CAACtH,IAAI,kCAAkC,CAC7D;UACD,IAAI,CAACuE,MAAM,CAACiD,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;QAC1C,CAAC;QACD9J,KAAK,EAAGA,KAAK,IAAI;UACfoH,OAAO,CAACpH,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;UAClE,IAAI,CAACA,KAAK,GAAG,yCAAyCA,KAAK,CAAC+J,OAAO,EAAE;UACrE,IAAI,CAACzD,UAAU,GAAG,KAAK;UACvB,IAAI,CAACQ,mBAAmB,CAACkD,SAAS,CAAC,WAAWhK,KAAK,CAAC+J,OAAO,EAAE,CAAC;QAChE;OACD,CAAC;KACH,MAAM;MACL;MACA,IAAI,CAACtD,aAAa,CAACwD,SAAS,CAACP,YAAY,CAAC,CAACvB,SAAS,CAAC;QACnDC,IAAI,EAAGwB,QAAQ,IAAI;UACjBxC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEuC,QAAQ,CAAC;UACpD,IAAI,CAACtD,UAAU,GAAG,KAAK;UACvB,IAAI,CAACQ,mBAAmB,CAAC+C,WAAW,CAClC,aAAaD,QAAQ,CAACtH,IAAI,4BAA4B,CACvD;UACD,IAAI,CAACuE,MAAM,CAACiD,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;QAC1C,CAAC;QACD9J,KAAK,EAAGA,KAAK,IAAI;UACfoH,OAAO,CAACpH,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;UAC3D,IAAI,CAACA,KAAK,GAAG,kCAAkCA,KAAK,CAAC+J,OAAO,EAAE;UAC9D,IAAI,CAACzD,UAAU,GAAG,KAAK;UACvB,IAAI,CAACQ,mBAAmB,CAACkD,SAAS,CAAC,WAAWhK,KAAK,CAAC+J,OAAO,EAAE,CAAC;QAChE;OACD,CAAC;;EAEN;EAEAtE,MAAMA,CAAA;IACJ2B,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAC7B,IAAI,CAACR,MAAM,CAACiD,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;EAC1C;EAEA;EACA7G,iBAAiBA,CAAC4F,QAAgB,EAAEpG,IAAA,GAAe,QAAQ;IACzD2E,OAAO,CAACC,GAAG,CACT,2CAA2C,EAC3CwB,QAAQ,EACR,UAAU,EACVpG,IAAI,CACL;IACD2E,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAAChB,QAAQ,CAAC;IAC1De,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAACvF,MAAM,CAAC;IAEtD;IACA,MAAMuE,QAAQ,GAAG,IAAI,CAACA,QAAQ,IAAK,IAAI,CAACvE,MAAM,IAAI,IAAI,CAACA,MAAM,CAACG,GAAI;IAElEmF,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEhB,QAAQ,CAAC;IAE1C,IAAI,CAACA,QAAQ,IAAI,CAACwC,QAAQ,EAAE;MAC1BzB,OAAO,CAACpH,KAAK,CAAC,sCAAsC,CAAC;MACrD,IAAI,CAACA,KAAK,GAAG,sCAAsC;MACnDoH,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEhB,QAAQ,EAAE,WAAW,EAAEwC,QAAQ,CAAC;MAEzD;MACA,IAAI,CAAC/B,mBAAmB,CAACkD,SAAS,CAChC,sEAAsE,CACvE;MACD;;IAGF;IACA,IAAI,IAAI,CAAClI,MAAM,CAACC,OAAO,IAAI,IAAI,CAACD,MAAM,CAACC,OAAO,CAACmI,QAAQ,CAACrB,QAAQ,CAAC,EAAE;MACjE,IAAI,CAAC/B,mBAAmB,CAACkD,SAAS,CAChC,wCAAwC,CACzC;MACD;;IAGF;IACA,MAAMlB,IAAI,GAAG,IAAI,CAAC1F,cAAc,CAAC2F,IAAI,CAClCC,CAAC,IAAKA,CAAC,CAAC/G,GAAG,KAAK4G,QAAQ,IAAIG,CAAC,CAAC9G,EAAE,KAAK2G,QAAQ,CAC/C;IACD,MAAMsB,QAAQ,GAAGrB,IAAI,GACjBA,IAAI,CAAC1G,SAAS,IAAI0G,IAAI,CAACzG,QAAQ,GAC7B,GAAGyG,IAAI,CAAC1G,SAAS,IAAI0G,IAAI,CAACzG,QAAQ,EAAE,GACpCyG,IAAI,CAACxG,IAAI,IAAIuG,QAAQ,GACvBA,QAAQ;IAEZ;IACA,MAAMuB,MAAM,GAAW;MACrBlI,EAAE,EAAE2G,QAAQ;MACZpG,IAAI,EAAEA;KACP;IAED,IAAI,CAACsE,OAAO,GAAG,IAAI;IAEnBK,OAAO,CAACC,GAAG,CACT,2BAA2B8C,QAAQ,WAAW1H,IAAI,eAAe4D,QAAQ,EAAE,CAC5E;IAED,IAAI,CAACI,aAAa,CAACxD,iBAAiB,CAACoD,QAAQ,EAAE+D,MAAM,CAAC,CAACjC,SAAS,CAAC;MAC/DC,IAAI,EAAGwB,QAAQ,IAAI;QACjBxC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEuC,QAAQ,CAAC;QACnD,IAAI,CAAC9C,mBAAmB,CAAC+C,WAAW,CAClC,GAAGM,QAAQ,uBACT1H,IAAI,KAAK,OAAO,GAAG,gBAAgB,GAAG,QACxC,aAAa,CACd;QACD;QACA,IAAI,CAACmF,UAAU,CAACvB,QAAQ,CAAC;QACzB,IAAI,CAACU,OAAO,GAAG,KAAK;MACtB,CAAC;MACD/G,KAAK,EAAGA,KAAK,IAAI;QACfoH,OAAO,CAACpH,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QACzD,IAAI,CAACA,KAAK,GACR,+DAA+D;QACjE,IAAI,CAAC8G,mBAAmB,CAACkD,SAAS,CAChC,oCAAoC,GAAGhK,KAAK,CAAC+J,OAAO,CACrD;QACD,IAAI,CAAChD,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEA;EACAzF,aAAaA,CAACuH,QAAgB;IAC5B;IACA,MAAMC,IAAI,GAAG,IAAI,CAAC1F,cAAc,CAAC2F,IAAI,CAClCC,CAAC,IAAKA,CAAC,CAAC/G,GAAG,KAAK4G,QAAQ,IAAIG,CAAC,CAAC9G,EAAE,KAAK2G,QAAQ,CAC/C;IACD,IAAIC,IAAI,EAAE;MACR,IAAIA,IAAI,CAAC1G,SAAS,IAAI0G,IAAI,CAACzG,QAAQ,EAAE;QACnC,OAAO,GAAGyG,IAAI,CAAC1G,SAAS,IAAI0G,IAAI,CAACzG,QAAQ,EAAE;OAC5C,MAAM,IAAIyG,IAAI,CAACxG,IAAI,EAAE;QACpB,OAAOwG,IAAI,CAACxG,IAAI;;;IAIpB;IACA,MAAM8H,MAAM,GAAG,IAAI,CAAClD,gBAAgB,CAAC6B,IAAI,CACtCsB,CAAC,IAAKA,CAAC,CAACpI,GAAG,KAAK4G,QAAQ,IAAIwB,CAAC,CAACnI,EAAE,KAAK2G,QAAQ,CAC/C;IACD,IAAIuB,MAAM,IAAIA,MAAM,CAAC9H,IAAI,EAAE;MACzB,OAAO8H,MAAM,CAAC9H,IAAI;;IAGpB;IACA,OAAOuG,QAAQ;EACjB;EAEA;EACAxI,cAAcA,CAACwI,QAAgB;IAC7B,MAAMC,IAAI,GAAG,IAAI,CAAC1F,cAAc,CAAC2F,IAAI,CAClCC,CAAC,IAAKA,CAAC,CAAC/G,GAAG,KAAK4G,QAAQ,IAAIG,CAAC,CAAC9G,EAAE,KAAK2G,QAAQ,CAC/C;IACD,IAAIC,IAAI,IAAIA,IAAI,CAACvG,KAAK,EAAE;MACtB,OAAOuG,IAAI,CAACvG,KAAK;;IAEnB,OAAO,eAAe;EACxB;EAEA;EACAb,mBAAmBA,CAACmH,QAAgB;IAClC,MAAMC,IAAI,GAAG,IAAI,CAAC1F,cAAc,CAAC2F,IAAI,CAClCC,CAAC,IAAKA,CAAC,CAAC/G,GAAG,KAAK4G,QAAQ,IAAIG,CAAC,CAAC9G,EAAE,KAAK2G,QAAQ,CAC/C;IACD,IAAIC,IAAI,EAAE;MACR,IAAIA,IAAI,CAACtG,UAAU,EAAE;QACnB,OAAOsG,IAAI,CAACtG,UAAU,KAAK,UAAU,GAAG,UAAU,GAAG,YAAY;OAClE,MAAM,IAAIsG,IAAI,CAACrG,IAAI,EAAE;QACpB,OAAOqG,IAAI,CAACrG,IAAI,KAAK,UAAU,GAAG,UAAU,GAAG,YAAY;;;IAG/D,OAAO,cAAc;EACvB;EAEA;EACA6H,aAAaA,CAACzB,QAAgB;IAC5B;IACA;IACA,OAAO,QAAQ;EACjB;EAEAzH,sBAAsBA,CAACyH,QAAgB;IACrCzB,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAEwB,QAAQ,CAAC;IACxEzB,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAAChB,QAAQ,CAAC;IAC1De,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAACvF,MAAM,CAAC;IAEtD;IACA,MAAMuE,QAAQ,GAAG,IAAI,CAACA,QAAQ,IAAK,IAAI,CAACvE,MAAM,IAAI,IAAI,CAACA,MAAM,CAACG,GAAI;IAElE,IAAI,CAACoE,QAAQ,EAAE;MACbe,OAAO,CAACpH,KAAK,CAAC,sBAAsB,CAAC;MACrC,IAAI,CAACA,KAAK,GAAG,wDAAwD;MACrE,IAAI,CAAC8G,mBAAmB,CAACkD,SAAS,CAChC,wDAAwD,CACzD;MACD;;IAGF,IAAI,CAACnB,QAAQ,EAAE;MACbzB,OAAO,CAACpH,KAAK,CAAC,uBAAuB,CAAC;MACtC,IAAI,CAACA,KAAK,GAAG,yDAAyD;MACtE,IAAI,CAAC8G,mBAAmB,CAACkD,SAAS,CAChC,yDAAyD,CAC1D;MACD;;IAGF;IACA,MAAMO,UAAU,GAAG,IAAI,CAACjJ,aAAa,CAACuH,QAAQ,CAAC;IAE/CzB,OAAO,CAACC,GAAG,CACT,yCAAyCwB,QAAQ,KAAK0B,UAAU,iBAAiBlE,QAAQ,EAAE,CAC5F;IAED,IAAI;MACF,IACEmE,OAAO,CAAC,oCAAoCD,UAAU,eAAe,CAAC,EACtE;QACAnD,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;QAE7D,IAAI,CAACN,OAAO,GAAG,IAAI;QACnB,IAAI,CAAC/G,KAAK,GAAG,IAAI;QAEjB;QACA6H,UAAU,CAAC,MAAK;UACd,IAAI,CAACpB,aAAa,CACfrF,sBAAsB,CAACiF,QAAQ,EAAEwC,QAAQ,CAAC,CAC1CV,SAAS,CAAC;YACTC,IAAI,EAAGwB,QAAQ,IAAI;cACjBxC,OAAO,CAACC,GAAG,CACT,gBAAgBkD,UAAU,mCAAmC,EAC7DX,QAAQ,CACT;cACD,IAAI,CAAC7C,OAAO,GAAG,KAAK;cACpB,IAAI,CAACD,mBAAmB,CAAC+C,WAAW,CAClC,GAAGU,UAAU,uCAAuC,CACrD;cAED;cACA,IAAI,CAAC3C,UAAU,CAACvB,QAAQ,CAAC;YAC3B,CAAC;YACDrG,KAAK,EAAGA,KAAK,IAAI;cACfoH,OAAO,CAACpH,KAAK,CACX,4CAA4CuK,UAAU,IAAI,EAC1DvK,KAAK,CACN;cACDoH,OAAO,CAACpH,KAAK,CAAC,sBAAsB,EAAE;gBACpCyK,MAAM,EAAEzK,KAAK,CAACyK,MAAM;gBACpBV,OAAO,EAAE/J,KAAK,CAAC+J,OAAO;gBACtB/J,KAAK,EAAEA;eACR,CAAC;cAEF,IAAI,CAAC+G,OAAO,GAAG,KAAK;cACpB,IAAI,CAAC/G,KAAK,GAAG,wCAAwCuK,UAAU,kBAC7DvK,KAAK,CAAC+J,OAAO,IAAI,iBACnB,EAAE;cACF,IAAI,CAACjD,mBAAmB,CAACkD,SAAS,CAChC,qCAAqC,IAAI,CAAChK,KAAK,EAAE,CAClD;YACH;WACD,CAAC;QACN,CAAC,EAAE,GAAG,CAAC;OACR,MAAM;QACLoH,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;;KAEvD,CAAC,OAAOrH,KAAU,EAAE;MACnBoH,OAAO,CAACpH,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D,IAAI,CAACA,KAAK,GAAG,cAAcA,KAAK,EAAE+J,OAAO,IAAI,iBAAiB,EAAE;MAChE,IAAI,CAACjD,mBAAmB,CAACkD,SAAS,CAAC,cAAc,IAAI,CAAChK,KAAK,EAAE,CAAC;;EAElE;EAEA;EACAgE,YAAYA,CAAA;IACVoD,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;IACzED,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAAChB,QAAQ,CAAC;IAC1De,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAACvF,MAAM,CAAC;IAEtD;IACA,MAAMuE,QAAQ,GAAG,IAAI,CAACA,QAAQ,IAAK,IAAI,CAACvE,MAAM,IAAI,IAAI,CAACA,MAAM,CAACG,GAAI;IAElE,IAAI,CAACoE,QAAQ,EAAE;MACbe,OAAO,CAACpH,KAAK,CAAC,sBAAsB,CAAC;MACrC,IAAI,CAACA,KAAK,GAAG,yDAAyD;MACtE,IAAI,CAAC8G,mBAAmB,CAACkD,SAAS,CAChC,yDAAyD,CAC1D;MACD;;IAGF5C,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEhB,QAAQ,CAAC;IAE5D,IAAI;MACF,IACEmE,OAAO,CACL,gDAAgD,IAAI,CAAC1I,MAAM,CAACQ,IAAI,mCAAmC,CACpG,EACD;QACA8E,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;QAE7D,IAAI,CAACN,OAAO,GAAG,IAAI;QACnB,IAAI,CAAC/G,KAAK,GAAG,IAAI;QAEjB;QACA6H,UAAU,CAAC,MAAK;UACd,IAAI,CAACpB,aAAa,CAACzC,YAAY,CAACqC,QAAQ,CAAC,CAAC8B,SAAS,CAAC;YAClDC,IAAI,EAAGwB,QAAQ,IAAI;cACjBxC,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEuC,QAAQ,CAAC;cAC/D,IAAI,CAAC7C,OAAO,GAAG,KAAK;cACpB,IAAI,CAACD,mBAAmB,CAAC+C,WAAW,CAClC,aAAa,IAAI,CAAC/H,MAAM,CAACQ,IAAI,gCAAgC,CAC9D;cAED;cACAuF,UAAU,CAAC,MAAK;gBACd,IAAI,CAAChB,MAAM,CAACiD,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;cAC1C,CAAC,EAAE,GAAG,CAAC;YACT,CAAC;YACD9J,KAAK,EAAGA,KAAK,IAAI;cACfoH,OAAO,CAACpH,KAAK,CACX,4CAA4C,EAC5CA,KAAK,CACN;cACDoH,OAAO,CAACpH,KAAK,CAAC,sBAAsB,EAAE;gBACpCyK,MAAM,EAAEzK,KAAK,CAACyK,MAAM;gBACpBV,OAAO,EAAE/J,KAAK,CAAC+J,OAAO;gBACtB/J,KAAK,EAAEA;eACR,CAAC;cAEF,IAAI,CAAC+G,OAAO,GAAG,KAAK;cACpB,IAAI,CAAC/G,KAAK,GAAG,qCACXA,KAAK,CAAC+J,OAAO,IAAI,iBACnB,EAAE;cACF,IAAI,CAACjD,mBAAmB,CAACkD,SAAS,CAChC,kCAAkC,IAAI,CAAChK,KAAK,EAAE,CAC/C;YACH;WACD,CAAC;QACJ,CAAC,EAAE,GAAG,CAAC;OACR,MAAM;QACLoH,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;;KAEvD,CAAC,OAAOrH,KAAU,EAAE;MACnBoH,OAAO,CAACpH,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,IAAI,CAACA,KAAK,GAAG,cAAcA,KAAK,EAAE+J,OAAO,IAAI,iBAAiB,EAAE;MAChE,IAAI,CAACjD,mBAAmB,CAACkD,SAAS,CAAC,cAAc,IAAI,CAAChK,KAAK,EAAE,CAAC;;EAElE;;;uBAjqBWuG,mBAAmB,EAAA/G,EAAA,CAAAkL,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAApL,EAAA,CAAAkL,iBAAA,CAAAG,EAAA,CAAAC,aAAA,GAAAtL,EAAA,CAAAkL,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAAxL,EAAA,CAAAkL,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAA1L,EAAA,CAAAkL,iBAAA,CAAAO,EAAA,CAAAE,MAAA,GAAA3L,EAAA,CAAAkL,iBAAA,CAAAU,EAAA,CAAAC,mBAAA;IAAA;EAAA;;;YAAnB9E,mBAAmB;MAAA+E,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCtBhCpM,EAAA,CAAAC,cAAA,aAA2C;UAOYD,EAAA,CAAAE,MAAA,GAA2D;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC3GH,EAAA,CAAAC,cAAA,WAA2B;UACzBD,EAAA,CAAAE,MAAA,GACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAENH,EAAA,CAAAC,cAAA,iBAA4F;UAAnBD,EAAA,CAAAmB,UAAA,mBAAAmL,sDAAA;YAAA,OAASD,GAAA,CAAApG,MAAA,EAAQ;UAAA,EAAC;UACzFjG,EAAA,CAAAI,SAAA,YAAqC;UAACJ,EAAA,CAAAE,MAAA,gCACxC;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAEXH,EAAA,CAAAI,SAAA,aAAiB;UACnBJ,EAAA,CAAAG,YAAA,EAAM;UAIRH,EAAA,CAAAe,UAAA,KAAAwL,mCAAA,mBAaM;UAGNvM,EAAA,CAAAe,UAAA,KAAAyL,mCAAA,kBAOM;UAGNxM,EAAA,CAAAe,UAAA,KAAA0L,mCAAA,oBAwQM;UACRzM,EAAA,CAAAG,YAAA,EAAM;;;UAjT+CH,EAAA,CAAAK,SAAA,GAA2D;UAA3DL,EAAA,CAAAM,iBAAA,CAAA+L,GAAA,CAAAzH,UAAA,qDAA2D;UAEpG5E,EAAA,CAAAK,SAAA,GACF;UADEL,EAAA,CAAAS,kBAAA,MAAA4L,GAAA,CAAAzH,UAAA,sJACF;UAWF5E,EAAA,CAAAK,SAAA,GAAa;UAAbL,EAAA,CAAA+B,UAAA,SAAAsK,GAAA,CAAA9E,OAAA,CAAa;UAgBbvH,EAAA,CAAAK,SAAA,GAAW;UAAXL,EAAA,CAAA+B,UAAA,SAAAsK,GAAA,CAAA7L,KAAA,CAAW;UAUwBR,EAAA,CAAAK,SAAA,GAAc;UAAdL,EAAA,CAAA+B,UAAA,UAAAsK,GAAA,CAAA9E,OAAA,CAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}