{"ast": null, "code": "import { throwError } from 'rxjs';\nimport { catchError, tap } from 'rxjs/operators';\nimport { environment } from '../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let TaskService = /*#__PURE__*/(() => {\n  class TaskService {\n    constructor(http) {\n      this.http = http;\n      this.apiUrl = `${environment.urlBackend}tasks`;\n      console.log('Task API URL:', this.apiUrl);\n    }\n    // Récupérer toutes les tâches\n    getTasks() {\n      return this.http.get(this.apiUrl).pipe(tap(data => console.log('Tasks received:', data)), catchError(this.handleError));\n    }\n    // Récupérer les tâches d'une équipe spécifique\n    getTasksByTeam(teamId) {\n      return this.http.get(`${this.apiUrl}/team/${teamId}`).pipe(tap(data => console.log(`Tasks for team ${teamId} received:`, data)), catchError(this.handleError));\n    }\n    // Récupérer une tâche par son ID\n    getTask(id) {\n      return this.http.get(`${this.apiUrl}/${id}`).pipe(tap(data => console.log('Task received:', data)), catchError(this.handleError));\n    }\n    // Créer une nouvelle tâche\n    createTask(task) {\n      return this.http.post(this.apiUrl, task).pipe(tap(data => console.log('Task created:', data)), catchError(this.handleError));\n    }\n    // Mettre à jour une tâche existante\n    updateTask(id, task) {\n      return this.http.put(`${this.apiUrl}/${id}`, task).pipe(tap(data => console.log('Task updated:', data)), catchError(this.handleError));\n    }\n    // Supprimer une tâche\n    deleteTask(id) {\n      return this.http.delete(`${this.apiUrl}/${id}`).pipe(tap(data => console.log('Task deleted:', data)), catchError(this.handleError));\n    }\n    // Mettre à jour le statut d'une tâche\n    updateTaskStatus(id, status) {\n      return this.http.patch(`${this.apiUrl}/${id}/status`, {\n        status\n      }).pipe(tap(data => console.log('Task status updated:', data)), catchError(this.handleError));\n    }\n    // Gérer les erreurs HTTP\n    handleError(error) {\n      let errorMessage = '';\n      if (error.error instanceof ErrorEvent) {\n        // Erreur côté client\n        errorMessage = `Error: ${error.error.message}`;\n      } else {\n        // Erreur côté serveur\n        errorMessage = `Error Code: ${error.status}\\nMessage: ${error.message}`;\n      }\n      console.error(errorMessage);\n      return throwError(() => new Error(errorMessage));\n    }\n    static {\n      this.ɵfac = function TaskService_Factory(t) {\n        return new (t || TaskService)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: TaskService,\n        factory: TaskService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return TaskService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}