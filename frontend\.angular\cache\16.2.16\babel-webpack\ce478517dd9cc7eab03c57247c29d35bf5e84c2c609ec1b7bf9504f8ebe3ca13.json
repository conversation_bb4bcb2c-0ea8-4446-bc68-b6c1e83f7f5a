{"ast": null, "code": "import { NavigationEnd } from '@angular/router';\nimport { Subject } from 'rxjs';\nimport { filter, takeUntil, distinctUntilChanged } from 'rxjs/operators';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/authuser.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/services/user-status.service\";\nimport * as i4 from \"src/app/services/message.service\";\nimport * as i5 from \"@app/services/theme.service\";\nimport * as i6 from \"src/app/services/data.service\";\nimport * as i7 from \"@angular/common\";\nconst _c0 = function () {\n  return {\n    exact: true\n  };\n};\nfunction FrontLayoutComponent_a_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 63);\n    i0.ɵɵelement(1, \"span\", 64);\n    i0.ɵɵelementStart(2, \"div\", 65)(3, \"div\", 8);\n    i0.ɵɵelement(4, \"i\", 66)(5, \"div\", 67);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 8);\n    i0.ɵɵtext(7, \"Accueil\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"routerLinkActiveOptions\", i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nfunction FrontLayoutComponent_a_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 68);\n    i0.ɵɵelement(1, \"span\", 64);\n    i0.ɵɵelementStart(2, \"div\", 65)(3, \"div\", 8);\n    i0.ɵɵelement(4, \"i\", 69)(5, \"div\", 67);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 8);\n    i0.ɵɵtext(7, \"Projects\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction FrontLayoutComponent_a_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 70);\n    i0.ɵɵelement(1, \"span\", 64);\n    i0.ɵɵelementStart(2, \"div\", 65)(3, \"div\", 8);\n    i0.ɵɵelement(4, \"i\", 71)(5, \"div\", 67);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 8);\n    i0.ɵɵtext(7, \"Plannings\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction FrontLayoutComponent_a_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 72);\n    i0.ɵɵelement(1, \"span\", 64);\n    i0.ɵɵelementStart(2, \"div\", 65)(3, \"div\", 8);\n    i0.ɵɵelement(4, \"i\", 73)(5, \"div\", 67);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 8);\n    i0.ɵɵtext(7, \"R\\u00E9unions\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction FrontLayoutComponent_a_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 74);\n    i0.ɵɵelement(1, \"span\", 64);\n    i0.ɵɵelementStart(2, \"div\", 65)(3, \"div\", 8);\n    i0.ɵɵelement(4, \"i\", 75)(5, \"div\", 67);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 8);\n    i0.ɵɵtext(7, \"Messages\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction FrontLayoutComponent_a_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 74);\n    i0.ɵɵelement(1, \"span\", 64);\n    i0.ɵɵelementStart(2, \"div\", 65)(3, \"div\", 8);\n    i0.ɵɵelement(4, \"i\", 75)(5, \"div\", 67);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 8);\n    i0.ɵɵtext(7, \"Equipes\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction FrontLayoutComponent_a_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 76);\n    i0.ɵɵelement(1, \"span\", 77);\n    i0.ɵɵelementStart(2, \"div\", 65)(3, \"div\", 8);\n    i0.ɵɵelement(4, \"i\", 78)(5, \"div\", 79);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 8);\n    i0.ɵɵtext(7, \"Go to Dashboard\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction FrontLayoutComponent_ng_container_47_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 101)(1, \"div\", 29);\n    i0.ɵɵelement(2, \"i\", 102);\n    i0.ɵɵelementStart(3, \"div\", 103);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r15.unreadNotificationsCount > 0 ? ctx_r15.unreadNotificationsCount > 99 ? \"99+\" : ctx_r15.unreadNotificationsCount : \"0\", \" \");\n  }\n}\nfunction FrontLayoutComponent_ng_container_47_i_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 104);\n  }\n}\nfunction FrontLayoutComponent_ng_container_47_i_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 105);\n  }\n}\nconst _c1 = function (a0) {\n  return {\n    \"rotate-180\": a0\n  };\n};\nfunction FrontLayoutComponent_ng_container_47_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 80);\n    i0.ɵɵtemplate(2, FrontLayoutComponent_ng_container_47_a_2_Template, 5, 1, \"a\", 81);\n    i0.ɵɵelementStart(3, \"button\", 82);\n    i0.ɵɵlistener(\"click\", function FrontLayoutComponent_ng_container_47_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.toggleDarkMode());\n    });\n    i0.ɵɵelementStart(4, \"div\", 83);\n    i0.ɵɵelement(5, \"div\", 84)(6, \"div\", 85);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"div\", 86);\n    i0.ɵɵelementStart(8, \"div\", 87);\n    i0.ɵɵpipe(9, \"async\");\n    i0.ɵɵtemplate(10, FrontLayoutComponent_ng_container_47_i_10_Template, 1, 0, \"i\", 88);\n    i0.ɵɵpipe(11, \"async\");\n    i0.ɵɵtemplate(12, FrontLayoutComponent_ng_container_47_i_12_Template, 1, 0, \"i\", 89);\n    i0.ɵɵpipe(13, \"async\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"button\", 90);\n    i0.ɵɵlistener(\"click\", function FrontLayoutComponent_ng_container_47_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.logout());\n    });\n    i0.ɵɵelementStart(15, \"div\", 91);\n    i0.ɵɵelement(16, \"div\", 92)(17, \"div\", 93);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(18, \"div\", 94)(19, \"i\", 95);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"a\", 96)(21, \"span\", 97);\n    i0.ɵɵtext(22, \"Profile\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"span\", 98);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"div\", 99);\n    i0.ɵɵelement(26, \"div\", 86)(27, \"img\", 100);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.authService.userLoggedIn());\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(12, _c1, i0.ɵɵpipeBind1(9, 6, ctx_r7.isDarkMode$)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !i0.ɵɵpipeBind1(11, 8, ctx_r7.isDarkMode$));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(13, 10, ctx_r7.isDarkMode$));\n    i0.ɵɵadvance(12);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r7.username, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", ctx_r7.imageProfile, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction FrontLayoutComponent_ng_template_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 106)(1, \"a\", 107);\n    i0.ɵɵelement(2, \"div\", 108)(3, \"div\", 109);\n    i0.ɵɵelementStart(4, \"span\", 110);\n    i0.ɵɵelement(5, \"i\", 111);\n    i0.ɵɵtext(6, \" Connexion \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"a\", 112);\n    i0.ɵɵelement(8, \"div\", 113)(9, \"div\", 114);\n    i0.ɵɵelementStart(10, \"span\", 110);\n    i0.ɵɵelement(11, \"i\", 115);\n    i0.ɵɵtext(12, \" Inscription \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction FrontLayoutComponent_a_69_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 119);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r21 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", item_r21.badge > 99 ? \"99+\" : item_r21.badge, \" \");\n  }\n}\nconst _c2 = function (a0) {\n  return {\n    exact: a0\n  };\n};\nfunction FrontLayoutComponent_a_69_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 116);\n    i0.ɵɵlistener(\"click\", function FrontLayoutComponent_a_69_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r24 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r24.toggleSidebar());\n    });\n    i0.ɵɵelement(1, \"span\", 64);\n    i0.ɵɵelementStart(2, \"div\", 117)(3, \"div\", 8);\n    i0.ɵɵelement(4, \"i\")(5, \"div\", 67);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, FrontLayoutComponent_a_69_div_8_Template, 2, 1, \"div\", 118);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r21 = ctx.$implicit;\n    i0.ɵɵpropertyInterpolate(\"routerLink\", item_r21.route);\n    i0.ɵɵproperty(\"routerLinkActiveOptions\", i0.ɵɵpureFunction1(7, _c2, item_r21.route === \"/\" ? true : false));\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassMapInterpolate1(\"\", item_r21.icon, \" h-5 w-5 mr-3 text-[#4f5fad] dark:text-[#6d78c9] group-hover:text-[#3d4a85] dark:group-hover:text-[#4f5fad] transition-colors\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r21.text);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r21.badge && item_r21.badge > 0);\n  }\n}\nfunction FrontLayoutComponent_a_70_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 120);\n    i0.ɵɵlistener(\"click\", function FrontLayoutComponent_a_70_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r26 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r26.toggleSidebar());\n    });\n    i0.ɵɵelement(1, \"span\", 77);\n    i0.ɵɵelementStart(2, \"div\", 117)(3, \"div\", 8);\n    i0.ɵɵelement(4, \"i\", 121)(5, \"div\", 79);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\");\n    i0.ɵɵtext(7, \"Go to Dashboard\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction FrontLayoutComponent_ng_container_72_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"a\", 122);\n    i0.ɵɵlistener(\"click\", function FrontLayoutComponent_ng_container_72_Template_a_click_1_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r28 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r28.toggleSidebar());\n    });\n    i0.ɵɵelement(2, \"span\", 64);\n    i0.ɵɵelementStart(3, \"div\", 65)(4, \"div\", 8);\n    i0.ɵɵelement(5, \"i\", 123)(6, \"div\", 67);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\");\n    i0.ɵɵtext(8, \"Connexion\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"a\", 124);\n    i0.ɵɵlistener(\"click\", function FrontLayoutComponent_ng_container_72_Template_a_click_9_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r30 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r30.toggleSidebar());\n    });\n    i0.ɵɵelement(10, \"span\", 77);\n    i0.ɵɵelementStart(11, \"div\", 65)(12, \"div\", 8);\n    i0.ɵɵelement(13, \"i\", 125)(14, \"div\", 79);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\");\n    i0.ɵɵtext(16, \"Inscription\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction FrontLayoutComponent_ng_container_73_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r32 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"a\", 126);\n    i0.ɵɵlistener(\"click\", function FrontLayoutComponent_ng_container_73_Template_a_click_1_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r31 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r31.toggleSidebar());\n    });\n    i0.ɵɵelement(2, \"span\", 64);\n    i0.ɵɵelementStart(3, \"div\", 65)(4, \"div\", 8);\n    i0.ɵɵelement(5, \"i\", 127)(6, \"div\", 67);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\");\n    i0.ɵɵtext(8, \"Mon Profil\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"a\", 128);\n    i0.ɵɵlistener(\"click\", function FrontLayoutComponent_ng_container_73_Template_a_click_9_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r33 = i0.ɵɵnextContext();\n      ctx_r33.logout();\n      return i0.ɵɵresetView(ctx_r33.toggleSidebar());\n    });\n    i0.ɵɵelement(10, \"span\", 129);\n    i0.ɵɵelementStart(11, \"div\", 65)(12, \"div\", 8);\n    i0.ɵɵelement(13, \"i\", 130)(14, \"div\", 131);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\");\n    i0.ɵɵtext(16, \"D\\u00E9connexion\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction FrontLayoutComponent_div_79_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 132)(1, \"div\", 133)(2, \"div\", 29)(3, \"div\", 8);\n    i0.ɵɵelement(4, \"i\", 134)(5, \"div\", 135);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 136);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r14.messageFromRedirect, \" \");\n  }\n}\nconst _c3 = function () {\n  return {\n    route: \"/\",\n    icon: \"fas fa-home\",\n    text: \"Accueil\"\n  };\n};\nconst _c4 = function () {\n  return {\n    route: \"/projects\",\n    icon: \"fas fa-rocket\",\n    text: \"Projects\"\n  };\n};\nconst _c5 = function () {\n  return {\n    route: \"/plannings\",\n    icon: \"far fa-calendar-check\",\n    text: \"Plannings\"\n  };\n};\nconst _c6 = function () {\n  return {\n    route: \"/reunions\",\n    icon: \"fas fa-users-cog\",\n    text: \"R\\u00E9unions\"\n  };\n};\nconst _c7 = function () {\n  return {\n    route: \"/messages\",\n    icon: \"far fa-comment-dots\",\n    text: \"Messages\"\n  };\n};\nconst _c8 = function (a3) {\n  return {\n    route: \"/notifications\",\n    icon: \"far fa-bell\",\n    text: \"Notifications\",\n    badge: a3\n  };\n};\nconst _c9 = function (a0, a1, a2, a3, a4, a5) {\n  return [a0, a1, a2, a3, a4, a5];\n};\nexport class FrontLayoutComponent {\n  constructor(authService, route, router, statusService, MessageService, themeService, dataService) {\n    this.authService = authService;\n    this.route = route;\n    this.router = router;\n    this.statusService = statusService;\n    this.MessageService = MessageService;\n    this.themeService = themeService;\n    this.dataService = dataService;\n    this.sidebarOpen = false;\n    this.profileMenuOpen = false;\n    this.currentUser = null;\n    this.messageFromRedirect = '';\n    this.unreadNotificationsCount = 0;\n    this.isMobileView = false;\n    this.username = '';\n    this.imageProfile = '';\n    this.destroy$ = new Subject();\n    this.MOBILE_BREAKPOINT = 768;\n    this.subscriptions = [];\n    this.checkViewport();\n    this.loadUserProfile();\n    this.isDarkMode$ = this.themeService.darkMode$;\n  }\n  loadUserProfile() {\n    // Try to get user from both services for maximum reliability\n    const authUser = this.authService.getCurrentUser();\n    const dataUser = this.dataService.currentUserValue;\n    console.log('Auth User:', authUser);\n    console.log('Data User:', dataUser);\n    // Prefer dataUser if available, otherwise use authUser\n    const user = dataUser || authUser;\n    if (user) {\n      this.updateProfileDisplay(user);\n      // Forcer une synchronisation complète des données utilisateur\n      if (this.authService.userLoggedIn()) {\n        this.dataService.syncCurrentUser().subscribe({\n          next: updatedUser => {\n            console.log('User profile synced:', updatedUser);\n            this.updateProfileDisplay(updatedUser);\n          },\n          error: err => {\n            console.error('Failed to sync user profile:', err);\n          }\n        });\n      }\n    } else {\n      // Default values if no user is found\n      this.username = '';\n      this.imageProfile = 'assets/images/default-profile.png';\n    }\n    console.log('Front layout - Image profile loaded:', this.imageProfile);\n    // Sync user data between services if needed\n    if (authUser && !dataUser) {\n      this.dataService.updateCurrentUser(authUser);\n    } else if (!authUser && dataUser) {\n      this.authService.setCurrentUser(dataUser);\n    }\n  }\n  ngOnInit() {\n    this.subscribeToQueryParams();\n    this.subscribeToCurrentUser();\n    this.subscribeToRouterEvents();\n    this.setupNotificationSystem();\n  }\n  checkViewport() {\n    this.isMobileView = window.innerWidth < this.MOBILE_BREAKPOINT;\n    if (!this.isMobileView) {\n      this.sidebarOpen = false;\n    }\n  }\n  setupNotificationSystem() {\n    // Real-time count updates\n    this.MessageService.notificationCount$.pipe(takeUntil(this.destroy$), distinctUntilChanged()).subscribe(count => {\n      this.unreadNotificationsCount = count;\n    });\n    // Charger les notifications initiales si l'utilisateur est connecté\n    if (this.authService.userLoggedIn()) {\n      this.MessageService.getNotifications(true).subscribe();\n    }\n  }\n  subscribeToCurrentUser() {\n    // S'abonner aux changements d'image de profil via AuthUserService\n    const authProfileSub = this.authService.currentUser$.pipe(takeUntil(this.destroy$)).subscribe(user => {\n      this.currentUser = user;\n      this.updateProfileDisplay(user);\n    });\n    // S'abonner aux changements d'image de profil via DataService\n    const dataProfileSub = this.dataService.currentUser$.pipe(takeUntil(this.destroy$)).subscribe(user => {\n      if (user) {\n        this.currentUser = user;\n        this.updateProfileDisplay(user);\n      }\n    });\n    this.subscriptions.push(authProfileSub, dataProfileSub);\n  }\n  updateProfileDisplay(user) {\n    if (user) {\n      this.username = user.fullName || user.username || '';\n      // Vérification plus robuste pour l'image de profil\n      let imageFound = false;\n      // Vérifier profileImage en premier\n      if (user.profileImage && user.profileImage !== 'null' && user.profileImage.trim() !== '' && user.profileImage !== 'undefined') {\n        this.imageProfile = user.profileImage;\n        imageFound = true;\n        console.log('Using profileImage:', this.imageProfile);\n      }\n      // Ensuite vérifier image si profileImage n'est pas valide\n      if (!imageFound && user.image && user.image !== 'null' && user.image.trim() !== '' && user.image !== 'undefined') {\n        this.imageProfile = user.image;\n        imageFound = true;\n        console.log('Using image:', this.imageProfile);\n      }\n      // Vérifier si l'image est une URL relative au backend\n      if (imageFound && !this.imageProfile.startsWith('http') && !this.imageProfile.startsWith('assets/')) {\n        // Si c'est une URL relative au backend, ajouter le préfixe du backend\n        if (this.imageProfile.startsWith('/')) {\n          this.imageProfile = `${environment.urlBackend.replace(/\\/$/, '')}${this.imageProfile}`;\n        } else {\n          this.imageProfile = `${environment.urlBackend.replace(/\\/$/, '')}/${this.imageProfile}`;\n        }\n        console.log('Converted to absolute URL:', this.imageProfile);\n      }\n      // Si aucune image valide n'est trouvée, utiliser l'image par défaut\n      if (!imageFound) {\n        this.imageProfile = 'assets/images/default-profile.png';\n        console.log('Using default image');\n      }\n      console.log('Front layout - Image profile updated:', this.imageProfile);\n    }\n  }\n  subscribeToQueryParams() {\n    this.route.queryParams.pipe(takeUntil(this.destroy$)).subscribe(params => {\n      this.messageFromRedirect = params['message'] || '';\n    });\n  }\n  subscribeToRouterEvents() {\n    this.router.events.pipe(filter(event => event instanceof NavigationEnd), takeUntil(this.destroy$)).subscribe(() => {\n      this.sidebarOpen = false;\n      this.profileMenuOpen = false;\n    });\n  }\n  toggleSidebar() {\n    this.sidebarOpen = !this.sidebarOpen;\n  }\n  toggleProfileMenu() {\n    this.profileMenuOpen = !this.profileMenuOpen;\n  }\n  toggleDarkMode() {\n    this.themeService.toggleDarkMode();\n  }\n  logout() {\n    this.authService.logout().subscribe({\n      next: () => {\n        this.profileMenuOpen = false;\n        this.sidebarOpen = false;\n        this.currentUser = null;\n        // Reset image to default\n        this.imageProfile = 'assets/images/default-profile.png';\n        // Clear data in both services\n        this.dataService.updateCurrentUser({});\n        this.router.navigate(['/login']);\n      },\n      error: err => {\n        console.error('Logout error:', err);\n        this.authService.clearAuthData();\n        this.currentUser = null;\n        // Reset image to default\n        this.imageProfile = 'assets/images/default-profile.png';\n        // Clear data in both services\n        this.dataService.updateCurrentUser({});\n        this.router.navigate(['/login']);\n      }\n    });\n  }\n  ngOnDestroy() {\n    // Désabonner de tous les observables pour éviter les fuites de mémoire\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function FrontLayoutComponent_Factory(t) {\n      return new (t || FrontLayoutComponent)(i0.ɵɵdirectiveInject(i1.AuthuserService), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.UserStatusService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i5.ThemeService), i0.ɵɵdirectiveInject(i6.DataService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: FrontLayoutComponent,\n      selectors: [[\"app-front-layout\"]],\n      hostBindings: function FrontLayoutComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"resize\", function FrontLayoutComponent_resize_HostBindingHandler() {\n            return ctx.checkViewport();\n          }, false, i0.ɵɵresolveWindow);\n        }\n      },\n      decls: 82,\n      vars: 38,\n      consts: [[1, \"flex\", \"h-screen\", \"main-grid-container\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"futuristic-layout\"], [1, \"background-grid\"], [1, \"hidden\", \"md:flex\", \"md:flex-shrink-0\"], [1, \"flex\", \"flex-col\", \"w-64\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"border-r\", \"border-[#edf1f4]\", \"dark:border-[#2a2a2a]\", \"backdrop-blur-sm\"], [1, \"flex\", \"items-center\", \"justify-center\", \"h-16\", \"px-4\", \"relative\", \"overflow-hidden\"], [1, \"absolute\", \"-top-6\", \"-left-6\", \"w-12\", \"h-12\", \"bg-gradient-to-br\", \"from-[#4f5fad]/20\", \"to-transparent\", \"rounded-full\"], [1, \"absolute\", \"-bottom-6\", \"-right-6\", \"w-12\", \"h-12\", \"bg-gradient-to-tl\", \"from-[#4f5fad]/20\", \"to-transparent\", \"rounded-full\"], [1, \"flex\", \"items-center\", \"relative\", \"z-10\"], [1, \"relative\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-8\", \"w-8\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"transform\", \"rotate-12\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/20\", \"dark:bg-[#6d78c9]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"ml-2\", \"text-xl\", \"font-bold\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\"], [1, \"flex\", \"flex-col\", \"flex-grow\", \"px-4\", \"py-4\"], [1, \"flex-1\", \"space-y-2\"], [\"routerLink\", \"/\", \"routerLinkActive\", \"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\", \"class\", \"sidebar-nav-link group flex items-center px-4 py-3 text-sm font-medium rounded-l-md text-[#6d6870] dark:text-[#a0a0a0] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-all\", 3, \"routerLinkActiveOptions\", 4, \"ngIf\"], [\"routerLink\", \"/projects\", \"routerLinkActive\", \"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\", \"class\", \"sidebar-nav-link group flex items-center px-4 py-3 text-sm font-medium rounded-l-md text-[#6d6870] dark:text-[#a0a0a0] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-all\", 4, \"ngIf\"], [\"routerLink\", \"/plannings\", \"routerLinkActive\", \"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\", \"class\", \"sidebar-nav-link group flex items-center px-4 py-3 text-sm font-medium rounded-l-md text-[#6d6870] dark:text-[#a0a0a0] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-all\", 4, \"ngIf\"], [\"routerLink\", \"/reunions\", \"routerLinkActive\", \"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\", \"class\", \"sidebar-nav-link group flex items-center px-4 py-3 text-sm font-medium rounded-l-md text-[#6d6870] dark:text-[#a0a0a0] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-all\", 4, \"ngIf\"], [\"routerLink\", \"/messages\", \"routerLinkActive\", \"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\", \"class\", \"sidebar-nav-link group flex items-center px-4 py-3 text-sm font-medium rounded-l-md text-[#6d6870] dark:text-[#a0a0a0] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-all\", 4, \"ngIf\"], [1, \"border-t\", \"border-[#edf1f4]\", \"dark:border-[#2a2a2a]\", \"my-2\"], [\"routerLink\", \"/admin/dashboard\", \"routerLinkActive\", \"active bg-gradient-to-r from-[#7826b5]/10 to-[#9d4edd]/10 dark:from-[#7826b5]/20 dark:to-[#9d4edd]/20 text-[#7826b5] dark:text-[#9d4edd] font-medium\", \"class\", \"sidebar-nav-link dashboard-link group flex items-center px-4 py-3 text-sm font-medium rounded-l-md text-[#6d6870] dark:text-[#a0a0a0] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] hover:text-[#7826b5] dark:hover:text-[#9d4edd] transition-all\", 4, \"ngIf\"], [1, \"fixed\", \"top-0\", \"left-0\", \"right-0\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"shadow-md\", \"dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)]\", \"z-50\", \"backdrop-blur-sm\", \"border-b\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\"], [1, \"max-w-7xl\", \"mx-auto\", \"px-4\", \"sm:px-6\", \"lg:px-8\", \"relative\"], [1, \"absolute\", \"inset-0\", \"overflow-hidden\", \"pointer-events-none\"], [1, \"absolute\", \"top-0\", \"left-1/4\", \"w-px\", \"h-full\", \"bg-gradient-to-b\", \"from-transparent\", \"via-[#4f5fad]/10\", \"dark:via-[#6d78c9]/5\", \"to-transparent\"], [1, \"absolute\", \"top-0\", \"right-1/3\", \"w-px\", \"h-full\", \"bg-gradient-to-b\", \"from-transparent\", \"via-[#4f5fad]/5\", \"dark:via-[#6d78c9]/3\", \"to-transparent\"], [1, \"flex\", \"items-center\", \"justify-between\", \"h-16\", \"relative\", \"z-10\"], [1, \"flex\", \"items-center\"], [\"aria-label\", \"Toggle menu\", 1, \"md:hidden\", \"flex\", \"items-center\", \"justify-center\", \"h-8\", \"px-3\", \"rounded-md\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"focus:outline-none\", \"transition-colors\", \"relative\", \"group\", \"overflow-hidden\", 3, \"click\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/10\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\", \"rounded-md\", \"blur-md\"], [\"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-5\", \"w-5\", \"relative\", \"z-10\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M4 6h16M4 12h16M4 18h16\"], [1, \"text-sm\", \"font-medium\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"ml-2\", \"relative\", \"z-10\"], [\"routerLink\", \"/\", 1, \"flex-shrink-0\", \"flex\", \"items-center\", \"group\"], [1, \"text-xl\", \"font-bold\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\", \"group-hover:scale-105\", \"transition-transform\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/10\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\"], [1, \"ml-2\", \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hidden\", \"md:block\"], [4, \"ngIf\", \"ngIfElse\"], [\"authButtons\", \"\"], [1, \"fixed\", \"inset-0\", \"z-40\", \"md:hidden\", 3, \"click\"], [1, \"absolute\", \"inset-0\", \"bg-black/30\", \"dark:bg-black/50\", \"backdrop-blur-sm\"], [1, \"fixed\", \"inset-y-0\", \"left-0\", \"max-w-xs\", \"w-full\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"shadow-lg\", \"dark:shadow-[0_0_30px_rgba(0,0,0,0.3)]\", \"transform\", \"transition-transform\", \"duration-300\", \"ease-in-out\", \"border-r\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", 3, \"click\"], [1, \"flex\", \"flex-col\", \"h-full\", \"relative\"], [1, \"absolute\", \"top-[10%]\", \"left-[5%]\", \"w-32\", \"h-32\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-2xl\"], [1, \"absolute\", \"bottom-[10%]\", \"right-[5%]\", \"w-40\", \"h-40\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-2xl\"], [1, \"flex\", \"items-center\", \"justify-between\", \"px-4\", \"py-3\", \"border-b\", \"border-[#edf1f4]\", \"dark:border-[#2a2a2a]\", \"relative\", \"z-10\"], [1, \"text-xl\", \"font-bold\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [\"aria-label\", \"Close menu\", 1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"p-2\", \"rounded-full\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"transition-colors\", \"relative\", \"group\", 3, \"click\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/10\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\", \"rounded-full\", \"blur-md\"], [1, \"fas\", \"fa-times\", \"relative\", \"z-10\"], [1, \"flex-1\", \"px-2\", \"py-4\", \"space-y-1\", \"overflow-y-auto\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"relative\", \"z-10\"], [\"class\", \"flex items-center px-3 py-2 text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] rounded-lg transition-all relative group overflow-hidden\", \"routerLinkActive\", \"bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium border-r-2 border-[#4f5fad] dark:border-[#6d78c9]\", 3, \"routerLink\", \"routerLinkActiveOptions\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"routerLink\", \"/admin/dashboard\", \"class\", \"flex items-center px-3 py-2 text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#7826b5] dark:hover:text-[#9d4edd] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] rounded-lg transition-all relative group overflow-hidden mt-2\", 3, \"click\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"flex-1\", \"flex\", \"flex-col\", \"overflow-hidden\"], [1, \"flex-1\", \"overflow-y-auto\", \"bg-[#edf1f4]\", \"dark:bg-[#121212]\", \"pt-16\", \"pb-6\", \"relative\"], [1, \"absolute\", \"top-[15%]\", \"left-[10%]\", \"w-64\", \"h-64\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"bottom-[20%]\", \"right-[10%]\", \"w-80\", \"h-80\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [\"class\", \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10 mt-4\", 4, \"ngIf\"], [1, \"max-w-7xl\", \"mx-auto\", \"px-4\", \"sm:px-6\", \"lg:px-8\", \"relative\", \"z-10\"], [\"routerLink\", \"/\", \"routerLinkActive\", \"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\", 1, \"sidebar-nav-link\", \"group\", \"flex\", \"items-center\", \"px-4\", \"py-3\", \"text-sm\", \"font-medium\", \"rounded-l-md\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"transition-all\", 3, \"routerLinkActiveOptions\"], [1, \"absolute\", \"inset-0\", \"w-1\", \"bg-gradient-to-b\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\"], [1, \"relative\", \"z-10\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-home\", \"h-5\", \"w-5\", \"mr-3\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"group-hover:text-[#3d4a85]\", \"dark:group-hover:text-[#4f5fad]\", \"transition-all\", \"group-hover:scale-110\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/20\", \"dark:bg-[#6d78c9]/20\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\", \"blur-md\", \"rounded-full\"], [\"routerLink\", \"/projects\", \"routerLinkActive\", \"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\", 1, \"sidebar-nav-link\", \"group\", \"flex\", \"items-center\", \"px-4\", \"py-3\", \"text-sm\", \"font-medium\", \"rounded-l-md\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"transition-all\"], [1, \"fas\", \"fa-rocket\", \"h-5\", \"w-5\", \"mr-3\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"group-hover:text-[#3d4a85]\", \"dark:group-hover:text-[#4f5fad]\", \"transition-all\", \"group-hover:scale-110\"], [\"routerLink\", \"/plannings\", \"routerLinkActive\", \"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\", 1, \"sidebar-nav-link\", \"group\", \"flex\", \"items-center\", \"px-4\", \"py-3\", \"text-sm\", \"font-medium\", \"rounded-l-md\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"transition-all\"], [1, \"far\", \"fa-calendar-check\", \"h-5\", \"w-5\", \"mr-3\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"group-hover:text-[#3d4a85]\", \"dark:group-hover:text-[#4f5fad]\", \"transition-all\", \"group-hover:scale-110\"], [\"routerLink\", \"/reunions\", \"routerLinkActive\", \"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\", 1, \"sidebar-nav-link\", \"group\", \"flex\", \"items-center\", \"px-4\", \"py-3\", \"text-sm\", \"font-medium\", \"rounded-l-md\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"transition-all\"], [1, \"fas\", \"fa-users-cog\", \"h-5\", \"w-5\", \"mr-3\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"group-hover:text-[#3d4a85]\", \"dark:group-hover:text-[#4f5fad]\", \"transition-all\", \"group-hover:scale-110\"], [\"routerLink\", \"/messages\", \"routerLinkActive\", \"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\", 1, \"sidebar-nav-link\", \"group\", \"flex\", \"items-center\", \"px-4\", \"py-3\", \"text-sm\", \"font-medium\", \"rounded-l-md\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"transition-all\"], [1, \"far\", \"fa-comment-dots\", \"h-5\", \"w-5\", \"mr-3\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"group-hover:text-[#3d4a85]\", \"dark:group-hover:text-[#4f5fad]\", \"transition-all\", \"group-hover:scale-110\"], [\"routerLink\", \"/admin/dashboard\", \"routerLinkActive\", \"active bg-gradient-to-r from-[#7826b5]/10 to-[#9d4edd]/10 dark:from-[#7826b5]/20 dark:to-[#9d4edd]/20 text-[#7826b5] dark:text-[#9d4edd] font-medium\", 1, \"sidebar-nav-link\", \"dashboard-link\", \"group\", \"flex\", \"items-center\", \"px-4\", \"py-3\", \"text-sm\", \"font-medium\", \"rounded-l-md\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"hover:text-[#7826b5]\", \"dark:hover:text-[#9d4edd]\", \"transition-all\"], [1, \"absolute\", \"inset-0\", \"w-1\", \"bg-gradient-to-b\", \"from-[#7826b5]\", \"to-[#9d4edd]\", \"dark:from-[#7826b5]\", \"dark:to-[#9d4edd]\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\"], [1, \"fas\", \"fa-tachometer-alt\", \"h-5\", \"w-5\", \"mr-3\", \"text-[#7826b5]\", \"dark:text-[#9d4edd]\", \"group-hover:text-[#7826b5]\", \"dark:group-hover:text-[#9d4edd]\", \"transition-all\", \"group-hover:scale-110\"], [1, \"absolute\", \"inset-0\", \"bg-[#7826b5]/20\", \"dark:bg-[#9d4edd]/20\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\", \"blur-md\", \"rounded-full\"], [1, \"ml-4\", \"flex\", \"items-center\", \"md:ml-6\"], [\"routerLink\", \"/notifications\", \"class\", \"flex items-center justify-center h-10 px-4 rounded-xl bg-white dark:bg-[#1e1e1e] border border-[#4f5fad]/20 dark:border-[#6d78c9]/20 text-[#4f5fad] dark:text-[#6d78c9] mr-2 transition-all duration-300 hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a]\", \"aria-label\", \"Notifications\", 4, \"ngIf\"], [\"aria-label\", \"Toggle dark mode\", 1, \"flex\", \"items-center\", \"justify-center\", \"h-9\", \"w-9\", \"rounded-xl\", \"bg-[#edf1f4]\", \"dark:bg-[#2a2a2a]\", \"hover:bg-[#dce4ec]\", \"dark:hover:bg-[#3a3a3a]\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"mr-3\", \"transition-all\", \"duration-300\", \"relative\", \"overflow-hidden\", \"group\", 3, \"click\"], [1, \"absolute\", \"inset-0\", \"rounded-xl\", \"overflow-hidden\"], [1, \"absolute\", \"inset-0\", \"rounded-xl\", \"border-2\", \"border-[#4f5fad]/30\", \"dark:border-[#6d78c9]/30\", \"opacity-100\"], [1, \"absolute\", \"-inset-1\", \"bg-gradient-to-r\", \"from-[#4f5fad]/0\", \"via-[#4f5fad]/30\", \"to-[#4f5fad]/0\", \"dark:from-[#6d78c9]/0\", \"dark:via-[#6d78c9]/30\", \"dark:to-[#6d78c9]/0\", \"opacity-0\", \"group-hover:opacity-100\", \"blur-sm\", \"animate-shine\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/10\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\", \"blur-md\"], [1, \"relative\", \"z-10\", \"transition-all\", \"duration-500\", \"ease-in-out\", 3, \"ngClass\"], [\"class\", \"far fa-moon text-lg group-hover:scale-110 transition-transform\", 4, \"ngIf\"], [\"class\", \"far fa-sun text-lg group-hover:scale-110 transition-transform\", 4, \"ngIf\"], [\"aria-label\", \"Logout\", 1, \"flex\", \"items-center\", \"justify-center\", \"h-8\", \"w-8\", \"rounded-full\", \"bg-[#edf1f4]\", \"dark:bg-[#2a2a2a]\", \"hover:bg-[#dce4ec]\", \"dark:hover:bg-[#3a3a3a]\", \"text-[#ff6b69]\", \"dark:text-[#ff8785]\", \"mr-3\", \"transition-all\", \"duration-300\", \"relative\", \"overflow-hidden\", \"group\", 3, \"click\"], [1, \"absolute\", \"inset-0\", \"rounded-full\", \"overflow-hidden\"], [1, \"absolute\", \"inset-0\", \"rounded-full\", \"border\", \"border-[#ff6b69]/20\", \"dark:border-[#ff8785]/20\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\"], [1, \"absolute\", \"-inset-1\", \"bg-gradient-to-r\", \"from-[#ff6b69]/0\", \"via-[#ff6b69]/30\", \"to-[#ff6b69]/0\", \"dark:from-[#ff8785]/0\", \"dark:via-[#ff8785]/30\", \"dark:to-[#ff8785]/0\", \"opacity-0\", \"group-hover:opacity-100\", \"blur-sm\", \"animate-shine\"], [1, \"absolute\", \"inset-0\", \"bg-[#ff6b69]/10\", \"dark:bg-[#ff8785]/10\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\", \"blur-md\"], [1, \"fas\", \"fa-sign-out-alt\", \"relative\", \"z-10\", \"group-hover:scale-110\", \"transition-transform\"], [\"routerLink\", \"/profile\", 1, \"flex\", \"items-center\", \"bg-[#edf1f4]\", \"dark:bg-[#2a2a2a]\", \"hover:bg-[#dce4ec]\", \"dark:hover:bg-[#3a3a3a]\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"px-3\", \"py-2\", \"rounded-lg\", \"transition-all\", \"group\"], [1, \"sr-only\"], [1, \"hidden\", \"md:inline-block\", \"mr-2\", \"text-sm\", \"font-medium\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"group-hover:scale-105\", \"transition-transform\"], [1, \"h-8\", \"w-8\", \"rounded-full\", \"overflow-hidden\", \"border-2\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]\", \"flex\", \"items-center\", \"justify-center\", \"relative\", \"group-hover:border-[#3d4a85]\", \"dark:group-hover:border-[#4f5fad]\", \"transition-colors\"], [\"alt\", \"Profile\", 1, \"h-full\", \"w-full\", \"object-cover\", 3, \"src\"], [\"routerLink\", \"/notifications\", \"aria-label\", \"Notifications\", 1, \"flex\", \"items-center\", \"justify-center\", \"h-10\", \"px-4\", \"rounded-xl\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"border\", \"border-[#4f5fad]/20\", \"dark:border-[#6d78c9]/20\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"mr-2\", \"transition-all\", \"duration-300\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\"], [1, \"far\", \"fa-bell\", \"text-lg\", \"transition-transform\", \"mr-2\"], [1, \"flex\", \"items-center\", \"justify-center\", \"h-6\", \"min-w-6\", \"px-1.5\", \"rounded-md\", \"bg-gradient-to-r\", \"from-[#ff8c00]\", \"to-[#ff6b00]\", \"text-white\", \"font-bold\", \"text-xs\", \"shadow-md\", \"animate-pulse\"], [1, \"far\", \"fa-moon\", \"text-lg\", \"group-hover:scale-110\", \"transition-transform\"], [1, \"far\", \"fa-sun\", \"text-lg\", \"group-hover:scale-110\", \"transition-transform\"], [1, \"flex\", \"space-x-4\"], [\"routerLink\", \"/login\", 1, \"inline-flex\", \"items-center\", \"relative\", \"overflow-hidden\", \"group\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#3d4a85]\", \"dark:to-[#6d78c9]\", \"rounded-lg\", \"transition-transform\", \"duration-300\", \"group-hover:scale-105\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#3d4a85]\", \"dark:to-[#6d78c9]\", \"rounded-lg\", \"opacity-0\", \"group-hover:opacity-100\", \"blur-xl\", \"transition-opacity\", \"duration-300\"], [1, \"relative\", \"flex\", \"items-center\", \"text-white\", \"font-medium\", \"py-2\", \"px-4\", \"rounded-lg\", \"transition-all\"], [1, \"fas\", \"fa-sign-in-alt\", \"mr-2\"], [\"routerLink\", \"/signup\", 1, \"inline-flex\", \"items-center\", \"relative\", \"overflow-hidden\", \"group\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#7826b5]\", \"to-[#9d4edd]\", \"dark:from-[#7826b5]\", \"dark:to-[#9d4edd]\", \"rounded-lg\", \"transition-transform\", \"duration-300\", \"group-hover:scale-105\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#7826b5]\", \"to-[#9d4edd]\", \"dark:from-[#7826b5]\", \"dark:to-[#9d4edd]\", \"rounded-lg\", \"opacity-0\", \"group-hover:opacity-100\", \"blur-xl\", \"transition-opacity\", \"duration-300\"], [1, \"fas\", \"fa-user-plus\", \"mr-2\"], [\"routerLinkActive\", \"bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium border-r-2 border-[#4f5fad] dark:border-[#6d78c9]\", 1, \"flex\", \"items-center\", \"px-3\", \"py-2\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"rounded-lg\", \"transition-all\", \"relative\", \"group\", \"overflow-hidden\", 3, \"routerLink\", \"routerLinkActiveOptions\", \"click\"], [1, \"relative\", \"z-10\", \"flex\", \"items-center\", \"w-full\"], [\"class\", \"ml-auto bg-gradient-to-r from-[#ff8c00] to-[#ff6b00] text-white text-xs rounded-full h-5 min-w-5 px-1 flex items-center justify-center shadow-md animate-pulse\", 4, \"ngIf\"], [1, \"ml-auto\", \"bg-gradient-to-r\", \"from-[#ff8c00]\", \"to-[#ff6b00]\", \"text-white\", \"text-xs\", \"rounded-full\", \"h-5\", \"min-w-5\", \"px-1\", \"flex\", \"items-center\", \"justify-center\", \"shadow-md\", \"animate-pulse\"], [\"routerLink\", \"/admin/dashboard\", 1, \"flex\", \"items-center\", \"px-3\", \"py-2\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:text-[#7826b5]\", \"dark:hover:text-[#9d4edd]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"rounded-lg\", \"transition-all\", \"relative\", \"group\", \"overflow-hidden\", \"mt-2\", 3, \"click\"], [1, \"fas\", \"fa-tachometer-alt\", \"h-5\", \"w-5\", \"mr-3\", \"text-[#7826b5]\", \"dark:text-[#9d4edd]\", \"group-hover:text-[#7826b5]\", \"dark:group-hover:text-[#9d4edd]\", \"transition-colors\"], [\"routerLink\", \"/login\", 1, \"flex\", \"items-center\", \"px-3\", \"py-2\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"rounded-lg\", \"transition-all\", \"relative\", \"group\", \"overflow-hidden\", 3, \"click\"], [1, \"fas\", \"fa-sign-in-alt\", \"mr-3\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"group-hover:scale-110\", \"transition-transform\"], [\"routerLink\", \"/signup\", 1, \"flex\", \"items-center\", \"px-3\", \"py-2\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"rounded-lg\", \"transition-all\", \"relative\", \"group\", \"overflow-hidden\", 3, \"click\"], [1, \"fas\", \"fa-user-plus\", \"mr-3\", \"text-[#7826b5]\", \"dark:text-[#9d4edd]\", \"group-hover:scale-110\", \"transition-transform\"], [\"routerLink\", \"/profile\", 1, \"flex\", \"items-center\", \"px-3\", \"py-2\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"rounded-lg\", \"transition-all\", \"relative\", \"group\", \"overflow-hidden\", 3, \"click\"], [1, \"fas\", \"fa-user\", \"mr-3\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"group-hover:scale-110\", \"transition-transform\"], [1, \"flex\", \"items-center\", \"px-3\", \"py-2\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:text-[#ff6b69]\", \"dark:hover:text-[#ff8785]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"rounded-lg\", \"transition-all\", \"relative\", \"group\", \"overflow-hidden\", \"cursor-pointer\", 3, \"click\"], [1, \"absolute\", \"inset-0\", \"w-1\", \"bg-gradient-to-b\", \"from-[#ff6b69]\", \"to-[#ff8785]\", \"dark:from-[#ff6b69]\", \"dark:to-[#ff8785]\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\"], [1, \"fas\", \"fa-sign-out-alt\", \"mr-3\", \"text-[#ff6b69]\", \"dark:text-[#ff8785]\", \"group-hover:scale-110\", \"transition-transform\"], [1, \"absolute\", \"inset-0\", \"bg-[#ff6b69]/20\", \"dark:bg-[#ff8785]/20\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\", \"blur-md\", \"rounded-full\"], [1, \"max-w-7xl\", \"mx-auto\", \"px-4\", \"sm:px-6\", \"lg:px-8\", \"relative\", \"z-10\", \"mt-4\"], [1, \"bg-white\", \"dark:bg-[#1e1e1e]\", \"border-l-4\", \"border-[#ff8c00]\", \"dark:border-[#ff6b00]\", \"rounded-lg\", \"p-4\", \"mb-6\", \"shadow-md\", \"dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)]\", \"backdrop-blur-sm\"], [1, \"fas\", \"fa-info-circle\", \"text-[#ff8c00]\", \"dark:text-[#ff6b00]\", \"text-lg\", \"mr-3\"], [1, \"absolute\", \"inset-0\", \"bg-[#ff8c00]/30\", \"dark:bg-[#ff6b00]/30\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\", \"animate-pulse\"], [1, \"ml-3\", \"text-sm\", \"font-medium\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"]],\n      template: function FrontLayoutComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵpipe(1, \"async\");\n          i0.ɵɵelement(2, \"div\", 1);\n          i0.ɵɵelementStart(3, \"div\", 2)(4, \"div\", 3)(5, \"div\", 4);\n          i0.ɵɵelement(6, \"div\", 5)(7, \"div\", 6);\n          i0.ɵɵelementStart(8, \"div\", 7)(9, \"div\", 8);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(10, \"svg\", 9);\n          i0.ɵɵelement(11, \"path\", 10)(12, \"path\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelement(13, \"div\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"span\", 13);\n          i0.ɵɵtext(15, \"DevBridge\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(16, \"div\", 14)(17, \"nav\", 15);\n          i0.ɵɵtemplate(18, FrontLayoutComponent_a_18_Template, 8, 2, \"a\", 16);\n          i0.ɵɵtemplate(19, FrontLayoutComponent_a_19_Template, 8, 0, \"a\", 17);\n          i0.ɵɵtemplate(20, FrontLayoutComponent_a_20_Template, 8, 0, \"a\", 18);\n          i0.ɵɵtemplate(21, FrontLayoutComponent_a_21_Template, 8, 0, \"a\", 19);\n          i0.ɵɵtemplate(22, FrontLayoutComponent_a_22_Template, 8, 0, \"a\", 20);\n          i0.ɵɵtemplate(23, FrontLayoutComponent_a_23_Template, 8, 0, \"a\", 20);\n          i0.ɵɵelement(24, \"div\", 21);\n          i0.ɵɵtemplate(25, FrontLayoutComponent_a_25_Template, 8, 0, \"a\", 22);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(26, \"header\", 23)(27, \"div\", 24)(28, \"div\", 25);\n          i0.ɵɵelement(29, \"div\", 26)(30, \"div\", 27);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"div\", 28)(32, \"div\", 29)(33, \"button\", 30);\n          i0.ɵɵlistener(\"click\", function FrontLayoutComponent_Template_button_click_33_listener() {\n            return ctx.toggleSidebar();\n          });\n          i0.ɵɵelement(34, \"div\", 31);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(35, \"svg\", 32);\n          i0.ɵɵelement(36, \"path\", 33);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(37, \"span\", 34);\n          i0.ɵɵtext(38, \"Menu\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(39, \"a\", 35)(40, \"div\", 8)(41, \"h1\", 36);\n          i0.ɵɵtext(42, \" DevBridge \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(43, \"div\", 37);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"span\", 38);\n          i0.ɵɵtext(45, \"Project Management Suite\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(46, \"div\", 29);\n          i0.ɵɵtemplate(47, FrontLayoutComponent_ng_container_47_Template, 28, 14, \"ng-container\", 39);\n          i0.ɵɵtemplate(48, FrontLayoutComponent_ng_template_48_Template, 13, 0, \"ng-template\", null, 40, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(50, \"div\", 41);\n          i0.ɵɵlistener(\"click\", function FrontLayoutComponent_Template_div_click_50_listener() {\n            return ctx.sidebarOpen && ctx.toggleSidebar();\n          });\n          i0.ɵɵelement(51, \"div\", 42);\n          i0.ɵɵelementStart(52, \"div\", 43);\n          i0.ɵɵlistener(\"click\", function FrontLayoutComponent_Template_div_click_52_listener($event) {\n            return $event.stopPropagation();\n          });\n          i0.ɵɵelementStart(53, \"div\", 44)(54, \"div\", 25);\n          i0.ɵɵelement(55, \"div\", 26)(56, \"div\", 27)(57, \"div\", 45)(58, \"div\", 46);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(59, \"div\", 47)(60, \"div\")(61, \"h3\", 48);\n          i0.ɵɵtext(62, \" DevBridge \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"p\", 49);\n          i0.ɵɵtext(64, \" Project Management Suite \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(65, \"button\", 50);\n          i0.ɵɵlistener(\"click\", function FrontLayoutComponent_Template_button_click_65_listener() {\n            return ctx.toggleSidebar();\n          });\n          i0.ɵɵelement(66, \"div\", 51)(67, \"i\", 52);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(68, \"nav\", 53);\n          i0.ɵɵtemplate(69, FrontLayoutComponent_a_69_Template, 9, 9, \"a\", 54);\n          i0.ɵɵtemplate(70, FrontLayoutComponent_a_70_Template, 8, 0, \"a\", 55);\n          i0.ɵɵelement(71, \"div\", 21);\n          i0.ɵɵtemplate(72, FrontLayoutComponent_ng_container_72_Template, 17, 0, \"ng-container\", 56);\n          i0.ɵɵtemplate(73, FrontLayoutComponent_ng_container_73_Template, 17, 0, \"ng-container\", 56);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(74, \"div\", 57)(75, \"main\", 58)(76, \"div\", 25);\n          i0.ɵɵelement(77, \"div\", 59)(78, \"div\", 60);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(79, FrontLayoutComponent_div_79_Template, 8, 1, \"div\", 61);\n          i0.ɵɵelementStart(80, \"div\", 62);\n          i0.ɵɵelement(81, \"router-outlet\");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          const _r8 = i0.ɵɵreference(49);\n          i0.ɵɵclassProp(\"dark\", i0.ɵɵpipeBind1(1, 22, ctx.isDarkMode$));\n          i0.ɵɵadvance(18);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.userLoggedIn());\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.userLoggedIn());\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.userLoggedIn());\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.userLoggedIn());\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.userLoggedIn());\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.userLoggedIn());\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.userLoggedIn() && ((ctx.currentUser == null ? null : ctx.currentUser.role) === \"admin\" || (ctx.currentUser == null ? null : ctx.currentUser.role) === \"teacher\"));\n          i0.ɵɵadvance(22);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.userLoggedIn())(\"ngIfElse\", _r8);\n          i0.ɵɵadvance(3);\n          i0.ɵɵclassProp(\"hidden\", !ctx.sidebarOpen);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"translate-x-0\", ctx.sidebarOpen)(\"-translate-x-full\", !ctx.sidebarOpen);\n          i0.ɵɵadvance(17);\n          i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction6(31, _c9, i0.ɵɵpureFunction0(24, _c3), i0.ɵɵpureFunction0(25, _c4), i0.ɵɵpureFunction0(26, _c5), i0.ɵɵpureFunction0(27, _c6), i0.ɵɵpureFunction0(28, _c7), i0.ɵɵpureFunction1(29, _c8, ctx.unreadNotificationsCount)));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.userLoggedIn() && ((ctx.currentUser == null ? null : ctx.currentUser.role) === \"admin\" || (ctx.currentUser == null ? null : ctx.currentUser.role) === \"teacher\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.authService.userLoggedIn());\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.userLoggedIn());\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.messageFromRedirect);\n        }\n      },\n      dependencies: [i7.NgClass, i7.NgForOf, i7.NgIf, i2.RouterOutlet, i2.RouterLink, i2.RouterLinkActive, i7.AsyncPipe],\n      styles: [\"@charset \\\"UTF-8\\\";\\n\\n\\n\\n\\n.futuristic-layout[_ngcontent-%COMP%] {\\n  background-color: var(--dark-bg);\\n  color: var(--text-light);\\n  min-height: 100vh;\\n  position: relative;\\n}\\n\\n\\n\\n.futuristic-layout[_ngcontent-%COMP%]:not(.dark) {\\n  background-color: #f0f4f8;\\n  color: #6d6870;\\n}\\n\\n\\n\\n.dark[_ngcontent-%COMP%]   .futuristic-layout[_ngcontent-%COMP%] {\\n  background-color: #121212;\\n}\\n\\n\\n\\n.futuristic-layout[_ngcontent-%COMP%]:not(.dark) {\\n  background-color: #f0f4f8;\\n}\\n\\n\\n\\n.futuristic-header[_ngcontent-%COMP%] {\\n  background-color: var(--medium-bg);\\n  border-bottom: 1px solid rgba(0, 247, 255, 0.2);\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);\\n}\\n\\n\\n\\n.futuristic-logo[_ngcontent-%COMP%] {\\n  background: linear-gradient(\\n    135deg,\\n    var(--accent-color),\\n    var(--secondary-color)\\n  );\\n  -webkit-background-clip: text;\\n  background-clip: text;\\n  color: transparent;\\n  text-shadow: 0 0 10px rgba(0, 247, 255, 0.5);\\n}\\n\\n.futuristic-subtitle[_ngcontent-%COMP%] {\\n  color: var(--text-dim);\\n}\\n\\n\\n\\n.futuristic-nav-link[_ngcontent-%COMP%] {\\n  color: var(--text-dim);\\n  padding: 0.75rem 1rem;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  transition: all var(--transition-fast);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.futuristic-nav-link[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: 0;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  width: 0;\\n  height: 2px;\\n  background: linear-gradient(\\n    90deg,\\n    var(--accent-color),\\n    var(--secondary-color)\\n  );\\n  transition: width var(--transition-fast);\\n}\\n\\n.futuristic-nav-link[_ngcontent-%COMP%]:hover {\\n  color: var(--text-light);\\n}\\n\\n.futuristic-nav-link[_ngcontent-%COMP%]:hover::before {\\n  width: 80%;\\n}\\n\\n.futuristic-nav-link-active[_ngcontent-%COMP%] {\\n  color: var(--accent-color);\\n}\\n\\n.futuristic-nav-link-active[_ngcontent-%COMP%]::before {\\n  width: 80%;\\n}\\n\\n\\n\\n.futuristic-profile-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  background: transparent;\\n  border: none;\\n  cursor: pointer;\\n  padding: 0.5rem;\\n  border-radius: var(--border-radius-md);\\n  transition: all var(--transition-fast);\\n}\\n\\n.futuristic-profile-button[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(0, 247, 255, 0.1);\\n}\\n\\n.futuristic-username[_ngcontent-%COMP%] {\\n  color: var(--text-light);\\n}\\n\\n\\n\\n.futuristic-dropdown-menu[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 0;\\n  top: 100%;\\n  margin-top: 0.5rem;\\n  width: 12rem;\\n  background-color: var(--medium-bg);\\n  border: 1px solid rgba(0, 247, 255, 0.2);\\n  border-radius: var(--border-radius-md);\\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);\\n  overflow: hidden;\\n  z-index: 50;\\n  animation: _ngcontent-%COMP%_fadeIn 0.2s ease-out;\\n}\\n\\n.futuristic-dropdown-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 0.75rem 1rem;\\n  color: var(--text-dim);\\n  font-size: 0.875rem;\\n  transition: all var(--transition-fast);\\n  cursor: pointer;\\n}\\n\\n.futuristic-dropdown-item[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(0, 247, 255, 0.1);\\n  color: var(--text-light);\\n}\\n\\n\\n\\n.futuristic-login-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 0.5rem 1rem;\\n  color: var(--accent-color);\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  border-radius: var(--border-radius-md);\\n  transition: all var(--transition-fast);\\n  border: 1px solid rgba(0, 247, 255, 0.3);\\n}\\n\\n.futuristic-login-button[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(0, 247, 255, 0.1);\\n  transform: translateY(-2px);\\n  box-shadow: var(--glow-effect);\\n}\\n\\n.futuristic-register-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 0.5rem 1rem;\\n  background: linear-gradient(\\n    135deg,\\n    var(--accent-color),\\n    var(--secondary-color)\\n  );\\n  color: var(--text-light);\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  border-radius: var(--border-radius-md);\\n  transition: all var(--transition-fast);\\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);\\n}\\n\\n.futuristic-register-button[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: var(--glow-effect);\\n}\\n\\n\\n\\n.futuristic-sidebar[_ngcontent-%COMP%] {\\n  background-color: var(--medium-bg);\\n  border-right: 1px solid rgba(0, 247, 255, 0.2);\\n  box-shadow: 4px 0 20px rgba(0, 0, 0, 0.3);\\n}\\n\\n.futuristic-sidebar-header[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid rgba(0, 247, 255, 0.2);\\n}\\n\\n.futuristic-close-button[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background-color: rgba(0, 247, 255, 0.1);\\n  color: var(--accent-color);\\n  border: none;\\n  border-radius: 50%;\\n  cursor: pointer;\\n  transition: all var(--transition-fast);\\n}\\n\\n.futuristic-close-button[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(0, 247, 255, 0.2);\\n  transform: rotate(90deg);\\n}\\n\\n.futuristic-sidebar-nav[_ngcontent-%COMP%] {\\n  scrollbar-width: thin;\\n  scrollbar-color: var(--accent-color) transparent;\\n}\\n\\n.futuristic-sidebar-nav[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 4px;\\n}\\n\\n.futuristic-sidebar-nav[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: transparent;\\n}\\n\\n.futuristic-sidebar-nav[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background-color: var(--accent-color);\\n  border-radius: 10px;\\n}\\n\\n.futuristic-sidebar-link[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 0.75rem 1rem;\\n  color: var(--text-dim);\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  border-radius: var(--border-radius-md);\\n  transition: all var(--transition-fast);\\n  position: relative;\\n}\\n\\n.futuristic-sidebar-link[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(0, 247, 255, 0.1);\\n  color: var(--text-light);\\n}\\n\\n.futuristic-sidebar-link-active[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 247, 255, 0.15);\\n  color: var(--accent-color);\\n  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);\\n}\\n\\n.futuristic-sidebar-icon[_ngcontent-%COMP%] {\\n  margin-right: 0.75rem;\\n  width: 1.5rem;\\n  height: 1.5rem;\\n  color: var(--text-dim);\\n  transition: color var(--transition-fast);\\n}\\n\\n.futuristic-sidebar-icon-fa[_ngcontent-%COMP%] {\\n  margin-right: 0.75rem;\\n  width: 1.5rem;\\n  font-size: 1.25rem;\\n  color: var(--text-dim);\\n  transition: color var(--transition-fast);\\n  text-align: center;\\n}\\n\\n.futuristic-sidebar-link[_ngcontent-%COMP%]:hover   .futuristic-sidebar-icon[_ngcontent-%COMP%], .futuristic-sidebar-link[_ngcontent-%COMP%]:hover   .futuristic-sidebar-icon-fa[_ngcontent-%COMP%], .futuristic-sidebar-link-active[_ngcontent-%COMP%]   .futuristic-sidebar-icon[_ngcontent-%COMP%], .futuristic-sidebar-link-active[_ngcontent-%COMP%]   .futuristic-sidebar-icon-fa[_ngcontent-%COMP%] {\\n  color: var(--accent-color);\\n}\\n\\n.futuristic-separator[_ngcontent-%COMP%] {\\n  height: 1px;\\n  background: linear-gradient(\\n    to right,\\n    transparent,\\n    rgba(0, 247, 255, 0.2),\\n    transparent\\n  );\\n}\\n\\n\\n\\n.futuristic-badge[_ngcontent-%COMP%] {\\n  background: linear-gradient(\\n    135deg,\\n    var(--accent-color),\\n    var(--secondary-color)\\n  );\\n  color: var(--text-light);\\n  font-size: 0.75rem;\\n  font-weight: 500;\\n  border-radius: 9999px;\\n  padding: 2px 8px;\\n  min-width: 1.5rem;\\n  text-align: center;\\n  box-shadow: var(--glow-effect);\\n}\\n\\n\\n\\n.futuristic-main-content[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 1;\\n}\\n\\n\\n\\n.futuristic-status-message[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 247, 255, 0.1);\\n  border-left: 4px solid var(--accent-color);\\n  border-radius: var(--border-radius-md);\\n  padding: 1rem;\\n  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);\\n}\\n\\n.futuristic-status-icon[_ngcontent-%COMP%] {\\n  color: var(--accent-color);\\n  font-size: 1.25rem;\\n}\\n\\n.futuristic-status-text[_ngcontent-%COMP%] {\\n  color: var(--text-light);\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(10px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n\\n\\n.main-content-grid[_ngcontent-%COMP%] {\\n  z-index: 0;\\n  pointer-events: none;\\n}\\n\\n\\n\\n.main-content-grid[_ngcontent-%COMP%] {\\n  pointer-events: none;\\n  z-index: 0;\\n}\\n\\n\\n\\n\\n\\n\\n.sidebar-nav-link[_ngcontent-%COMP%] {\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n\\n\\n.sidebar-nav-link[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  inset: 0;\\n  border-radius: 0.375rem 0 0 0.375rem;\\n  border: 2px solid rgba(79, 95, 173, 0.1);\\n  pointer-events: none;\\n}\\n\\n.dark[_ngcontent-%COMP%]   .sidebar-nav-link[_ngcontent-%COMP%]::before {\\n  border-color: rgba(109, 120, 201, 0.1);\\n}\\n\\n\\n\\n.sidebar-nav-link.active[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  bottom: 0;\\n  right: 0;\\n  width: 0.5rem;\\n  background: linear-gradient(to bottom, #4f5fad, #00f7ff, #4f5fad);\\n  border-radius: 0 0.375rem 0.375rem 0;\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n  box-shadow: 0 0 15px rgba(0, 247, 255, 0.7);\\n}\\n\\n.dark[_ngcontent-%COMP%]   .sidebar-nav-link.active[_ngcontent-%COMP%]::after {\\n  background: linear-gradient(to bottom, #6d78c9, #00f7ff, #6d78c9);\\n  box-shadow: 0 0 15px rgba(0, 247, 255, 0.7);\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0% {\\n    opacity: 0.7;\\n  }\\n  50% {\\n    opacity: 1;\\n  }\\n  100% {\\n    opacity: 0.7;\\n  }\\n}\\n\\n\\n\\n.sidebar-nav-link.active[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  bottom: 0;\\n  right: 0;\\n  width: 0.5rem;\\n  background: linear-gradient(to bottom, #4f5fad, #00f7ff, #4f5fad);\\n  border-radius: 0 0.375rem 0.375rem 0;\\n  filter: blur(8px);\\n  transform: scale(1.5);\\n  opacity: 0.5;\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n\\n.dark[_ngcontent-%COMP%]   .sidebar-nav-link.active[_ngcontent-%COMP%]::before {\\n  background: linear-gradient(to bottom, #6d78c9, #00f7ff, #6d78c9);\\n}\\n\\n\\n\\n.dashboard-link.active[_ngcontent-%COMP%]::after {\\n  background: linear-gradient(to bottom, #7826b5, #9d4edd, #7826b5);\\n  box-shadow: 0 0 15px rgba(157, 78, 221, 0.7);\\n}\\n\\n.dark[_ngcontent-%COMP%]   .dashboard-link.active[_ngcontent-%COMP%]::after {\\n  background: linear-gradient(to bottom, #7826b5, #9d4edd, #7826b5);\\n}\\n\\n.dashboard-link.active[_ngcontent-%COMP%]::before {\\n  background: linear-gradient(to bottom, #7826b5, #9d4edd, #7826b5);\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["NavigationEnd", "Subject", "filter", "takeUntil", "distinctUntilChanged", "environment", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵproperty", "ɵɵpureFunction0", "_c0", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r15", "unreadNotificationsCount", "ɵɵelementContainerStart", "ɵɵtemplate", "FrontLayoutComponent_ng_container_47_a_2_Template", "ɵɵlistener", "FrontLayoutComponent_ng_container_47_Template_button_click_3_listener", "ɵɵrestoreView", "_r19", "ctx_r18", "ɵɵnextContext", "ɵɵresetView", "toggleDarkMode", "FrontLayoutComponent_ng_container_47_i_10_Template", "FrontLayoutComponent_ng_container_47_i_12_Template", "FrontLayoutComponent_ng_container_47_Template_button_click_14_listener", "ctx_r20", "logout", "ɵɵelementContainerEnd", "ctx_r7", "authService", "userLoggedIn", "ɵɵpureFunction1", "_c1", "ɵɵpipeBind1", "isDarkMode$", "username", "imageProfile", "ɵɵsanitizeUrl", "item_r21", "badge", "FrontLayoutComponent_a_69_Template_a_click_0_listener", "_r25", "ctx_r24", "toggleSidebar", "FrontLayoutComponent_a_69_div_8_Template", "ɵɵpropertyInterpolate", "route", "_c2", "ɵɵclassMapInterpolate1", "icon", "ɵɵtextInterpolate", "text", "FrontLayoutComponent_a_70_Template_a_click_0_listener", "_r27", "ctx_r26", "FrontLayoutComponent_ng_container_72_Template_a_click_1_listener", "_r29", "ctx_r28", "FrontLayoutComponent_ng_container_72_Template_a_click_9_listener", "ctx_r30", "FrontLayoutComponent_ng_container_73_Template_a_click_1_listener", "_r32", "ctx_r31", "FrontLayoutComponent_ng_container_73_Template_a_click_9_listener", "ctx_r33", "ctx_r14", "messageFromRedirect", "FrontLayoutComponent", "constructor", "router", "statusService", "MessageService", "themeService", "dataService", "sidebarOpen", "profileMenuOpen", "currentUser", "isMobile<PERSON>iew", "destroy$", "MOBILE_BREAKPOINT", "subscriptions", "checkViewport", "loadUserProfile", "darkMode$", "authUser", "getCurrentUser", "dataUser", "currentUserValue", "console", "log", "user", "updateProfileDisplay", "syncCurrentUser", "subscribe", "next", "updatedUser", "error", "err", "updateCurrentUser", "setCurrentUser", "ngOnInit", "subscribeToQueryParams", "subscribeToCurrentUser", "subscribeToRouterEvents", "setupNotificationSystem", "window", "innerWidth", "notificationCount$", "pipe", "count", "getNotifications", "authProfileSub", "currentUser$", "dataProfileSub", "push", "fullName", "imageFound", "profileImage", "trim", "image", "startsWith", "urlBackend", "replace", "queryParams", "params", "events", "event", "toggleProfileMenu", "navigate", "clearAuthData", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "complete", "ɵɵdirectiveInject", "i1", "AuthuserService", "i2", "ActivatedRoute", "Router", "i3", "UserStatusService", "i4", "i5", "ThemeService", "i6", "DataService", "selectors", "hostBindings", "FrontLayoutComponent_HostBindings", "rf", "ctx", "ɵɵresolveWindow", "ɵɵnamespaceSVG", "ɵɵnamespaceHTML", "FrontLayoutComponent_a_18_Template", "FrontLayoutComponent_a_19_Template", "FrontLayoutComponent_a_20_Template", "FrontLayoutComponent_a_21_Template", "FrontLayoutComponent_a_22_Template", "FrontLayoutComponent_a_23_Template", "FrontLayoutComponent_a_25_Template", "FrontLayoutComponent_Template_button_click_33_listener", "FrontLayoutComponent_ng_container_47_Template", "FrontLayoutComponent_ng_template_48_Template", "ɵɵtemplateRefExtractor", "FrontLayoutComponent_Template_div_click_50_listener", "FrontLayoutComponent_Template_div_click_52_listener", "$event", "stopPropagation", "FrontLayoutComponent_Template_button_click_65_listener", "FrontLayoutComponent_a_69_Template", "FrontLayoutComponent_a_70_Template", "FrontLayoutComponent_ng_container_72_Template", "FrontLayoutComponent_ng_container_73_Template", "FrontLayoutComponent_div_79_Template", "ɵɵclassProp", "role", "_r8", "ɵɵpureFunction6", "_c9", "_c3", "_c4", "_c5", "_c6", "_c7", "_c8"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\layouts\\front-layout\\front-layout.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\layouts\\front-layout\\front-layout.component.html"], "sourcesContent": ["import { Component, OnIni<PERSON>, On<PERSON><PERSON>roy, HostListener } from '@angular/core';\nimport { ActivatedRoute, Router, NavigationEnd } from '@angular/router';\nimport { Subject, Subscription, Observable } from 'rxjs';\nimport { filter, takeUntil, distinctUntilChanged } from 'rxjs/operators';\nimport { AuthuserService } from 'src/app/services/authuser.service';\nimport { UserStatusService } from 'src/app/services/user-status.service';\nimport { MessageService } from 'src/app/services/message.service';\nimport { User } from 'src/app/models/user.model';\nimport { ThemeService } from '@app/services/theme.service';\nimport { DataService } from 'src/app/services/data.service';\nimport { environment } from 'src/environments/environment';\n@Component({\n  selector: 'app-front-layout',\n  templateUrl: './front-layout.component.html',\n  styleUrls: ['./front-layout.component.css'],\n})\nexport class FrontLayoutComponent implements OnInit, On<PERSON><PERSON>roy {\n  sidebarOpen = false;\n  profileMenuOpen = false;\n  currentUser: User | null = null;\n  messageFromRedirect: string = '';\n  unreadNotificationsCount = 0;\n  isMobileView = false;\n  username: string = '';\n  imageProfile: string = '';\n  isDarkMode$: Observable<boolean>;\n\n  private destroy$ = new Subject<void>();\n  private readonly MOBILE_BREAKPOINT = 768;\n  private subscriptions: Subscription[] = [];\n\n  constructor(\n    public authService: AuthuserService,\n    private route: ActivatedRoute,\n    private router: Router,\n    public statusService: UserStatusService,\n    private MessageService: MessageService,\n    private themeService: ThemeService,\n    private dataService: DataService\n  ) {\n    this.checkViewport();\n    this.loadUserProfile();\n    this.isDarkMode$ = this.themeService.darkMode$;\n  }\n\n  private loadUserProfile(): void {\n    // Try to get user from both services for maximum reliability\n    const authUser = this.authService.getCurrentUser();\n    const dataUser = this.dataService.currentUserValue;\n\n    console.log('Auth User:', authUser);\n    console.log('Data User:', dataUser);\n\n    // Prefer dataUser if available, otherwise use authUser\n    const user = dataUser || authUser;\n\n    if (user) {\n      this.updateProfileDisplay(user);\n\n      // Forcer une synchronisation complète des données utilisateur\n      if (this.authService.userLoggedIn()) {\n        this.dataService.syncCurrentUser().subscribe({\n          next: (updatedUser) => {\n            console.log('User profile synced:', updatedUser);\n            this.updateProfileDisplay(updatedUser);\n          },\n          error: (err) => {\n            console.error('Failed to sync user profile:', err);\n          },\n        });\n      }\n    } else {\n      // Default values if no user is found\n      this.username = '';\n      this.imageProfile = 'assets/images/default-profile.png';\n    }\n\n    console.log('Front layout - Image profile loaded:', this.imageProfile);\n\n    // Sync user data between services if needed\n    if (authUser && !dataUser) {\n      this.dataService.updateCurrentUser(authUser);\n    } else if (!authUser && dataUser) {\n      this.authService.setCurrentUser(dataUser);\n    }\n  }\n  ngOnInit(): void {\n    this.subscribeToQueryParams();\n    this.subscribeToCurrentUser();\n    this.subscribeToRouterEvents();\n    this.setupNotificationSystem();\n  }\n  @HostListener('window:resize')\n  private checkViewport(): void {\n    this.isMobileView = window.innerWidth < this.MOBILE_BREAKPOINT;\n    if (!this.isMobileView) {\n      this.sidebarOpen = false;\n    }\n  }\n  private setupNotificationSystem(): void {\n    // Real-time count updates\n    this.MessageService.notificationCount$\n      .pipe(takeUntil(this.destroy$), distinctUntilChanged())\n      .subscribe((count) => {\n        this.unreadNotificationsCount = count;\n      });\n    // Charger les notifications initiales si l'utilisateur est connecté\n    if (this.authService.userLoggedIn()) {\n      this.MessageService.getNotifications(true).subscribe();\n    }\n  }\n  private subscribeToCurrentUser(): void {\n    // S'abonner aux changements d'image de profil via AuthUserService\n    const authProfileSub = this.authService.currentUser$\n      .pipe(takeUntil(this.destroy$))\n      .subscribe((user) => {\n        this.currentUser = user;\n        this.updateProfileDisplay(user);\n      });\n\n    // S'abonner aux changements d'image de profil via DataService\n    const dataProfileSub = this.dataService.currentUser$\n      .pipe(takeUntil(this.destroy$))\n      .subscribe((user) => {\n        if (user) {\n          this.currentUser = user;\n          this.updateProfileDisplay(user);\n        }\n      });\n\n    this.subscriptions.push(authProfileSub, dataProfileSub);\n  }\n\n  private updateProfileDisplay(user: any): void {\n    if (user) {\n      this.username = user.fullName || user.username || '';\n\n      // Vérification plus robuste pour l'image de profil\n      let imageFound = false;\n\n      // Vérifier profileImage en premier\n      if (\n        user.profileImage &&\n        user.profileImage !== 'null' &&\n        user.profileImage.trim() !== '' &&\n        user.profileImage !== 'undefined'\n      ) {\n        this.imageProfile = user.profileImage;\n        imageFound = true;\n        console.log('Using profileImage:', this.imageProfile);\n      }\n\n      // Ensuite vérifier image si profileImage n'est pas valide\n      if (\n        !imageFound &&\n        user.image &&\n        user.image !== 'null' &&\n        user.image.trim() !== '' &&\n        user.image !== 'undefined'\n      ) {\n        this.imageProfile = user.image;\n        imageFound = true;\n        console.log('Using image:', this.imageProfile);\n      }\n\n      // Vérifier si l'image est une URL relative au backend\n      if (\n        imageFound &&\n        !this.imageProfile.startsWith('http') &&\n        !this.imageProfile.startsWith('assets/')\n      ) {\n        // Si c'est une URL relative au backend, ajouter le préfixe du backend\n        if (this.imageProfile.startsWith('/')) {\n          this.imageProfile = `${environment.urlBackend.replace(/\\/$/, '')}${\n            this.imageProfile\n          }`;\n        } else {\n          this.imageProfile = `${environment.urlBackend.replace(/\\/$/, '')}/${\n            this.imageProfile\n          }`;\n        }\n        console.log('Converted to absolute URL:', this.imageProfile);\n      }\n\n      // Si aucune image valide n'est trouvée, utiliser l'image par défaut\n      if (!imageFound) {\n        this.imageProfile = 'assets/images/default-profile.png';\n        console.log('Using default image');\n      }\n\n      console.log('Front layout - Image profile updated:', this.imageProfile);\n    }\n  }\n  private subscribeToQueryParams(): void {\n    this.route.queryParams\n      .pipe(takeUntil(this.destroy$))\n      .subscribe((params) => {\n        this.messageFromRedirect = params['message'] || '';\n      });\n  }\n  private subscribeToRouterEvents(): void {\n    this.router.events\n      .pipe(\n        filter((event) => event instanceof NavigationEnd),\n        takeUntil(this.destroy$)\n      )\n      .subscribe(() => {\n        this.sidebarOpen = false;\n        this.profileMenuOpen = false;\n      });\n  }\n  toggleSidebar(): void {\n    this.sidebarOpen = !this.sidebarOpen;\n  }\n  toggleProfileMenu(): void {\n    this.profileMenuOpen = !this.profileMenuOpen;\n  }\n\n  toggleDarkMode(): void {\n    this.themeService.toggleDarkMode();\n  }\n\n  logout(): void {\n    this.authService.logout().subscribe({\n      next: () => {\n        this.profileMenuOpen = false;\n        this.sidebarOpen = false;\n        this.currentUser = null;\n        // Reset image to default\n        this.imageProfile = 'assets/images/default-profile.png';\n        // Clear data in both services\n        this.dataService.updateCurrentUser({});\n        this.router.navigate(['/login']);\n      },\n      error: (err) => {\n        console.error('Logout error:', err);\n        this.authService.clearAuthData();\n        this.currentUser = null;\n        // Reset image to default\n        this.imageProfile = 'assets/images/default-profile.png';\n        // Clear data in both services\n        this.dataService.updateCurrentUser({});\n        this.router.navigate(['/login']);\n      },\n    });\n  }\n  ngOnDestroy(): void {\n    // Désabonner de tous les observables pour éviter les fuites de mémoire\n    this.subscriptions.forEach((sub) => sub.unsubscribe());\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n}\n", "<div\n  class=\"flex h-screen main-grid-container text-[#6d6870] dark:text-[#a0a0a0] futuristic-layout\"\n  [class.dark]=\"isDarkMode$ | async\"\n>\n  <!-- Background Grid -->\n  <div class=\"background-grid\"></div>\n\n  <!-- Sidebar -->\n  <div class=\"hidden md:flex md:flex-shrink-0\">\n    <div\n      class=\"flex flex-col w-64 bg-white dark:bg-[#1e1e1e] border-r border-[#edf1f4] dark:border-[#2a2a2a] backdrop-blur-sm\"\n    >\n      <div\n        class=\"flex items-center justify-center h-16 px-4 relative overflow-hidden\"\n      >\n        <!-- Decorative elements -->\n        <div\n          class=\"absolute -top-6 -left-6 w-12 h-12 bg-gradient-to-br from-[#4f5fad]/20 to-transparent rounded-full\"\n        ></div>\n        <div\n          class=\"absolute -bottom-6 -right-6 w-12 h-12 bg-gradient-to-tl from-[#4f5fad]/20 to-transparent rounded-full\"\n        ></div>\n\n        <div class=\"flex items-center relative z-10\">\n          <div class=\"relative\">\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              class=\"h-8 w-8 text-[#4f5fad] dark:text-[#6d78c9] transform rotate-12\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke=\"currentColor\"\n            >\n              <path\n                stroke-linecap=\"round\"\n                stroke-linejoin=\"round\"\n                stroke-width=\"2\"\n                d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n              />\n              <path\n                stroke-linecap=\"round\"\n                stroke-linejoin=\"round\"\n                stroke-width=\"2\"\n                d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n              />\n            </svg>\n            <!-- Glow effect -->\n            <div\n              class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 blur-xl rounded-full transform scale-150 -z-10\"\n            ></div>\n          </div>\n          <span\n            class=\"ml-2 text-xl font-bold bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent\"\n            >DevBridge</span\n          >\n        </div>\n      </div>\n      <div class=\"flex flex-col flex-grow px-4 py-4\">\n        <nav class=\"flex-1 space-y-2\">\n          <!-- Navigation Items - Réorganisés avec Accueil en premier -->\n\n          <!-- Accueil button -->\n          <a\n            *ngIf=\"authService.userLoggedIn()\"\n            routerLink=\"/\"\n            routerLinkActive=\"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\"\n            [routerLinkActiveOptions]=\"{ exact: true }\"\n            class=\"sidebar-nav-link group flex items-center px-4 py-3 text-sm font-medium rounded-l-md text-[#6d6870] dark:text-[#a0a0a0] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-all\"\n          >\n            <!-- Hover effect -->\n            <span\n              class=\"absolute inset-0 w-1 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] opacity-0 group-hover:opacity-100 transition-opacity\"\n            ></span>\n\n            <div class=\"relative z-10 flex items-center\">\n              <div class=\"relative\">\n                <i\n                  class=\"fas fa-home h-5 w-5 mr-3 text-[#4f5fad] dark:text-[#6d78c9] group-hover:text-[#3d4a85] dark:group-hover:text-[#4f5fad] transition-all group-hover:scale-110\"\n                ></i>\n                <!-- Glow effect -->\n                <div\n                  class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 opacity-0 group-hover:opacity-100 transition-opacity blur-md rounded-full\"\n                ></div>\n              </div>\n              <span class=\"relative\">Accueil</span>\n            </div>\n          </a>\n\n          <!-- Projects -->\n          <a\n            *ngIf=\"authService.userLoggedIn()\"\n            routerLink=\"/projects\"\n            routerLinkActive=\"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\"\n            class=\"sidebar-nav-link group flex items-center px-4 py-3 text-sm font-medium rounded-l-md text-[#6d6870] dark:text-[#a0a0a0] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-all\"\n          >\n            <!-- Hover effect -->\n            <span\n              class=\"absolute inset-0 w-1 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] opacity-0 group-hover:opacity-100 transition-opacity\"\n            ></span>\n\n            <div class=\"relative z-10 flex items-center\">\n              <div class=\"relative\">\n                <i\n                  class=\"fas fa-rocket h-5 w-5 mr-3 text-[#4f5fad] dark:text-[#6d78c9] group-hover:text-[#3d4a85] dark:group-hover:text-[#4f5fad] transition-all group-hover:scale-110\"\n                ></i>\n                <!-- Glow effect -->\n                <div\n                  class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 opacity-0 group-hover:opacity-100 transition-opacity blur-md rounded-full\"\n                ></div>\n              </div>\n              <span class=\"relative\">Projects</span>\n            </div>\n          </a>\n\n          <!-- Plannings -->\n          <a\n            *ngIf=\"authService.userLoggedIn()\"\n            routerLink=\"/plannings\"\n            routerLinkActive=\"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\"\n            class=\"sidebar-nav-link group flex items-center px-4 py-3 text-sm font-medium rounded-l-md text-[#6d6870] dark:text-[#a0a0a0] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-all\"\n          >\n            <!-- Hover effect -->\n            <span\n              class=\"absolute inset-0 w-1 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] opacity-0 group-hover:opacity-100 transition-opacity\"\n            ></span>\n\n            <div class=\"relative z-10 flex items-center\">\n              <div class=\"relative\">\n                <i\n                  class=\"far fa-calendar-check h-5 w-5 mr-3 text-[#4f5fad] dark:text-[#6d78c9] group-hover:text-[#3d4a85] dark:group-hover:text-[#4f5fad] transition-all group-hover:scale-110\"\n                ></i>\n                <!-- Glow effect -->\n                <div\n                  class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 opacity-0 group-hover:opacity-100 transition-opacity blur-md rounded-full\"\n                ></div>\n              </div>\n              <span class=\"relative\">Plannings</span>\n            </div>\n          </a>\n\n          <!-- Réunions -->\n          <a\n            *ngIf=\"authService.userLoggedIn()\"\n            routerLink=\"/reunions\"\n            routerLinkActive=\"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\"\n            class=\"sidebar-nav-link group flex items-center px-4 py-3 text-sm font-medium rounded-l-md text-[#6d6870] dark:text-[#a0a0a0] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-all\"\n          >\n            <!-- Hover effect -->\n            <span\n              class=\"absolute inset-0 w-1 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] opacity-0 group-hover:opacity-100 transition-opacity\"\n            ></span>\n\n            <div class=\"relative z-10 flex items-center\">\n              <div class=\"relative\">\n                <i\n                  class=\"fas fa-users-cog h-5 w-5 mr-3 text-[#4f5fad] dark:text-[#6d78c9] group-hover:text-[#3d4a85] dark:group-hover:text-[#4f5fad] transition-all group-hover:scale-110\"\n                ></i>\n                <!-- Glow effect -->\n                <div\n                  class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 opacity-0 group-hover:opacity-100 transition-opacity blur-md rounded-full\"\n                ></div>\n              </div>\n              <span class=\"relative\">Réunions</span>\n            </div>\n          </a>\n\n          <!-- Messages -->\n          <a\n            *ngIf=\"authService.userLoggedIn()\"\n            routerLink=\"/messages\"\n            routerLinkActive=\"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\"\n            class=\"sidebar-nav-link group flex items-center px-4 py-3 text-sm font-medium rounded-l-md text-[#6d6870] dark:text-[#a0a0a0] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-all\"\n          >\n            <!-- Hover effect -->\n            <span\n              class=\"absolute inset-0 w-1 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] opacity-0 group-hover:opacity-100 transition-opacity\"\n            ></span>\n\n            <div class=\"relative z-10 flex items-center\">\n              <div class=\"relative\">\n                <i\n                  class=\"far fa-comment-dots h-5 w-5 mr-3 text-[#4f5fad] dark:text-[#6d78c9] group-hover:text-[#3d4a85] dark:group-hover:text-[#4f5fad] transition-all group-hover:scale-110\"\n                ></i>\n                <!-- Glow effect -->\n                <div\n                  class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 opacity-0 group-hover:opacity-100 transition-opacity blur-md rounded-full\"\n                ></div>\n              </div>\n              <span class=\"relative\">Messages</span>\n            </div>\n          </a>\n   <!-- Messages -->\n          <a\n            *ngIf=\"authService.userLoggedIn()\"\n            routerLink=\"/messages\"\n            routerLinkActive=\"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\"\n            class=\"sidebar-nav-link group flex items-center px-4 py-3 text-sm font-medium rounded-l-md text-[#6d6870] dark:text-[#a0a0a0] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-all\"\n          >\n            <!-- Hover effect -->\n            <span\n              class=\"absolute inset-0 w-1 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] opacity-0 group-hover:opacity-100 transition-opacity\"\n            ></span>\n\n            <div class=\"relative z-10 flex items-center\">\n              <div class=\"relative\">\n                <i\n                  class=\"far fa-comment-dots h-5 w-5 mr-3 text-[#4f5fad] dark:text-[#6d78c9] group-hover:text-[#3d4a85] dark:group-hover:text-[#4f5fad] transition-all group-hover:scale-110\"\n                ></i>\n                <!-- Glow effect -->\n                <div\n                  class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 opacity-0 group-hover:opacity-100 transition-opacity blur-md rounded-full\"\n                ></div>\n              </div>\n              <span class=\"relative\">Equipes</span>\n            </div>\n          </a>\n          <!-- Séparateur -->\n          <div\n            class=\"border-t border-[#edf1f4] dark:border-[#2a2a2a] my-2\"\n          ></div>\n\n          <!-- Go to Dashboard button - uniquement pour admin et teacher -->\n          <a\n            *ngIf=\"\n              authService.userLoggedIn() &&\n              (currentUser?.role === 'admin' || currentUser?.role === 'teacher')\n            \"\n            routerLink=\"/admin/dashboard\"\n            routerLinkActive=\"active bg-gradient-to-r from-[#7826b5]/10 to-[#9d4edd]/10 dark:from-[#7826b5]/20 dark:to-[#9d4edd]/20 text-[#7826b5] dark:text-[#9d4edd] font-medium\"\n            class=\"sidebar-nav-link dashboard-link group flex items-center px-4 py-3 text-sm font-medium rounded-l-md text-[#6d6870] dark:text-[#a0a0a0] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] hover:text-[#7826b5] dark:hover:text-[#9d4edd] transition-all\"\n          >\n            <!-- Hover effect -->\n            <span\n              class=\"absolute inset-0 w-1 bg-gradient-to-b from-[#7826b5] to-[#9d4edd] dark:from-[#7826b5] dark:to-[#9d4edd] opacity-0 group-hover:opacity-100 transition-opacity\"\n            ></span>\n\n            <div class=\"relative z-10 flex items-center\">\n              <div class=\"relative\">\n                <i\n                  class=\"fas fa-tachometer-alt h-5 w-5 mr-3 text-[#7826b5] dark:text-[#9d4edd] group-hover:text-[#7826b5] dark:group-hover:text-[#9d4edd] transition-all group-hover:scale-110\"\n                ></i>\n                <!-- Glow effect -->\n                <div\n                  class=\"absolute inset-0 bg-[#7826b5]/20 dark:bg-[#9d4edd]/20 opacity-0 group-hover:opacity-100 transition-opacity blur-md rounded-full\"\n                ></div>\n              </div>\n              <span class=\"relative\">Go to Dashboard</span>\n            </div>\n          </a>\n        </nav>\n      </div>\n    </div>\n  </div>\n\n  <!-- La grille est maintenant gérée par la classe background-grid -->\n  <!-- Header -->\n  <header\n    class=\"fixed top-0 left-0 right-0 bg-white dark:bg-[#1e1e1e] shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)] z-50 backdrop-blur-sm border-b border-[#edf1f4]/50 dark:border-[#2a2a2a]\"\n  >\n    <div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative\">\n      <!-- Decorative elements -->\n      <div class=\"absolute inset-0 overflow-hidden pointer-events-none\">\n        <div\n          class=\"absolute top-0 left-1/4 w-px h-full bg-gradient-to-b from-transparent via-[#4f5fad]/10 dark:via-[#6d78c9]/5 to-transparent\"\n        ></div>\n        <div\n          class=\"absolute top-0 right-1/3 w-px h-full bg-gradient-to-b from-transparent via-[#4f5fad]/5 dark:via-[#6d78c9]/3 to-transparent\"\n        ></div>\n      </div>\n\n      <div class=\"flex items-center justify-between h-16 relative z-10\">\n        <!-- Logo and main nav (left side) -->\n        <div class=\"flex items-center\">\n          <!-- Mobile menu button -->\n          <button\n            (click)=\"toggleSidebar()\"\n            class=\"md:hidden flex items-center justify-center h-8 px-3 rounded-md text-[#6d6870] dark:text-[#a0a0a0] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] focus:outline-none transition-colors relative group overflow-hidden\"\n            aria-label=\"Toggle menu\"\n          >\n            <div\n              class=\"absolute inset-0 bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 opacity-0 group-hover:opacity-100 transition-opacity rounded-md blur-md\"\n            ></div>\n            <svg\n              class=\"h-5 w-5 relative z-10\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke=\"currentColor\"\n            >\n              <path\n                stroke-linecap=\"round\"\n                stroke-linejoin=\"round\"\n                stroke-width=\"2\"\n                d=\"M4 6h16M4 12h16M4 18h16\"\n              />\n            </svg>\n            <span\n              class=\"text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] ml-2 relative z-10\"\n              >Menu</span\n            >\n          </button>\n\n          <!-- Logo -->\n          <a routerLink=\"/\" class=\"flex-shrink-0 flex items-center group\">\n            <div class=\"relative\">\n              <h1\n                class=\"text-xl font-bold bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent group-hover:scale-105 transition-transform\"\n              >\n                DevBridge\n              </h1>\n              <!-- Glow effect -->\n              <div\n                class=\"absolute inset-0 bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 blur-xl rounded-full transform scale-150 -z-10 opacity-0 group-hover:opacity-100 transition-opacity\"\n              ></div>\n            </div>\n            <span\n              class=\"ml-2 text-sm text-[#6d6870] dark:text-[#a0a0a0] hidden md:block\"\n              >Project Management Suite</span\n            >\n          </a>\n\n          <!-- Navigation - Removed and moved to sidebar -->\n        </div>\n\n        <!-- Right side -->\n        <div class=\"flex items-center\">\n          <ng-container *ngIf=\"authService.userLoggedIn(); else authButtons\">\n            <div class=\"ml-4 flex items-center md:ml-6\">\n              <!-- Bouton de notification simplifié -->\n              <a\n                *ngIf=\"authService.userLoggedIn()\"\n                routerLink=\"/notifications\"\n                class=\"flex items-center justify-center h-10 px-4 rounded-xl bg-white dark:bg-[#1e1e1e] border border-[#4f5fad]/20 dark:border-[#6d78c9]/20 text-[#4f5fad] dark:text-[#6d78c9] mr-2 transition-all duration-300 hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a]\"\n                aria-label=\"Notifications\"\n              >\n                <!-- Contenu du bouton avec icône et compteur -->\n                <div class=\"flex items-center\">\n                  <i class=\"far fa-bell text-lg transition-transform mr-2\"></i>\n\n                  <!-- Compteur de notifications avec couleur orange fluo -->\n                  <div\n                    class=\"flex items-center justify-center h-6 min-w-6 px-1.5 rounded-md bg-gradient-to-r from-[#ff8c00] to-[#ff6b00] text-white font-bold text-xs shadow-md animate-pulse\"\n                  >\n                    {{\n                      unreadNotificationsCount > 0\n                        ? unreadNotificationsCount > 99\n                          ? \"99+\"\n                          : unreadNotificationsCount\n                        : \"0\"\n                    }}\n                  </div>\n                </div>\n              </a>\n\n              <!-- Dark Mode Toggle Button -->\n              <button\n                (click)=\"toggleDarkMode()\"\n                class=\"flex items-center justify-center h-9 w-9 rounded-xl bg-[#edf1f4] dark:bg-[#2a2a2a] hover:bg-[#dce4ec] dark:hover:bg-[#3a3a3a] text-[#4f5fad] dark:text-[#6d78c9] mr-3 transition-all duration-300 relative overflow-hidden group\"\n                aria-label=\"Toggle dark mode\"\n              >\n                <!-- Modern animated border with glow -->\n                <div class=\"absolute inset-0 rounded-xl overflow-hidden\">\n                  <div\n                    class=\"absolute inset-0 rounded-xl border-2 border-[#4f5fad]/30 dark:border-[#6d78c9]/30 opacity-100\"\n                  ></div>\n                  <div\n                    class=\"absolute -inset-1 bg-gradient-to-r from-[#4f5fad]/0 via-[#4f5fad]/30 to-[#4f5fad]/0 dark:from-[#6d78c9]/0 dark:via-[#6d78c9]/30 dark:to-[#6d78c9]/0 opacity-0 group-hover:opacity-100 blur-sm animate-shine\"\n                  ></div>\n                </div>\n                <!-- Glow effect -->\n                <div\n                  class=\"absolute inset-0 bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 opacity-0 group-hover:opacity-100 transition-opacity blur-md\"\n                ></div>\n                <div\n                  class=\"relative z-10 transition-all duration-500 ease-in-out\"\n                  [ngClass]=\"{ 'rotate-180': isDarkMode$ | async }\"\n                >\n                  <i\n                    *ngIf=\"!(isDarkMode$ | async)\"\n                    class=\"far fa-moon text-lg group-hover:scale-110 transition-transform\"\n                  ></i>\n                  <i\n                    *ngIf=\"isDarkMode$ | async\"\n                    class=\"far fa-sun text-lg group-hover:scale-110 transition-transform\"\n                  ></i>\n                </div>\n              </button>\n\n              <!-- Logout Button -->\n              <button\n                (click)=\"logout()\"\n                class=\"flex items-center justify-center h-8 w-8 rounded-full bg-[#edf1f4] dark:bg-[#2a2a2a] hover:bg-[#dce4ec] dark:hover:bg-[#3a3a3a] text-[#ff6b69] dark:text-[#ff8785] mr-3 transition-all duration-300 relative overflow-hidden group\"\n                aria-label=\"Logout\"\n              >\n                <!-- Animated border -->\n                <div class=\"absolute inset-0 rounded-full overflow-hidden\">\n                  <div\n                    class=\"absolute inset-0 rounded-full border border-[#ff6b69]/20 dark:border-[#ff8785]/20 opacity-0 group-hover:opacity-100 transition-opacity\"\n                  ></div>\n                  <div\n                    class=\"absolute -inset-1 bg-gradient-to-r from-[#ff6b69]/0 via-[#ff6b69]/30 to-[#ff6b69]/0 dark:from-[#ff8785]/0 dark:via-[#ff8785]/30 dark:to-[#ff8785]/0 opacity-0 group-hover:opacity-100 blur-sm animate-shine\"\n                  ></div>\n                </div>\n                <!-- Glow effect -->\n                <div\n                  class=\"absolute inset-0 bg-[#ff6b69]/10 dark:bg-[#ff8785]/10 opacity-0 group-hover:opacity-100 transition-opacity blur-md\"\n                ></div>\n                <i\n                  class=\"fas fa-sign-out-alt relative z-10 group-hover:scale-110 transition-transform\"\n                ></i>\n              </button>\n\n              <!-- Profile Button -->\n              <a\n                routerLink=\"/profile\"\n                class=\"flex items-center bg-[#edf1f4] dark:bg-[#2a2a2a] hover:bg-[#dce4ec] dark:hover:bg-[#3a3a3a] text-[#4f5fad] dark:text-[#6d78c9] px-3 py-2 rounded-lg transition-all group\"\n              >\n                <span class=\"sr-only\">Profile</span>\n                <span\n                  class=\"hidden md:inline-block mr-2 text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] group-hover:scale-105 transition-transform\"\n                >\n                  {{ username }}\n                </span>\n                <div\n                  class=\"h-8 w-8 rounded-full overflow-hidden border-2 border-[#4f5fad] dark:border-[#6d78c9] flex items-center justify-center relative group-hover:border-[#3d4a85] dark:group-hover:border-[#4f5fad] transition-colors\"\n                >\n                  <!-- Glow effect -->\n                  <div\n                    class=\"absolute inset-0 bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 opacity-0 group-hover:opacity-100 transition-opacity blur-md\"\n                  ></div>\n                  <img\n                    [src]=\"imageProfile\"\n                    alt=\"Profile\"\n                    class=\"h-full w-full object-cover\"\n                  />\n                </div>\n              </a>\n            </div>\n          </ng-container>\n\n          <!-- Auth Buttons for non-logged in users -->\n          <ng-template #authButtons>\n            <div class=\"flex space-x-4\">\n              <a\n                routerLink=\"/login\"\n                class=\"inline-flex items-center relative overflow-hidden group\"\n              >\n                <div\n                  class=\"absolute inset-0 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#3d4a85] dark:to-[#6d78c9] rounded-lg transition-transform duration-300 group-hover:scale-105\"\n                ></div>\n                <div\n                  class=\"absolute inset-0 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#3d4a85] dark:to-[#6d78c9] rounded-lg opacity-0 group-hover:opacity-100 blur-xl transition-opacity duration-300\"\n                ></div>\n                <span\n                  class=\"relative flex items-center text-white font-medium py-2 px-4 rounded-lg transition-all\"\n                >\n                  <i class=\"fas fa-sign-in-alt mr-2\"></i>\n                  Connexion\n                </span>\n              </a>\n\n              <a\n                routerLink=\"/signup\"\n                class=\"inline-flex items-center relative overflow-hidden group\"\n              >\n                <div\n                  class=\"absolute inset-0 bg-gradient-to-r from-[#7826b5] to-[#9d4edd] dark:from-[#7826b5] dark:to-[#9d4edd] rounded-lg transition-transform duration-300 group-hover:scale-105\"\n                ></div>\n                <div\n                  class=\"absolute inset-0 bg-gradient-to-r from-[#7826b5] to-[#9d4edd] dark:from-[#7826b5] dark:to-[#9d4edd] rounded-lg opacity-0 group-hover:opacity-100 blur-xl transition-opacity duration-300\"\n                ></div>\n                <span\n                  class=\"relative flex items-center text-white font-medium py-2 px-4 rounded-lg transition-all\"\n                >\n                  <i class=\"fas fa-user-plus mr-2\"></i>\n                  Inscription\n                </span>\n              </a>\n            </div>\n          </ng-template>\n        </div>\n      </div>\n    </div>\n  </header>\n\n  <!-- Barre latérale mobile -->\n  <div\n    class=\"fixed inset-0 z-40 md:hidden\"\n    [class.hidden]=\"!sidebarOpen\"\n    (click)=\"sidebarOpen && toggleSidebar()\"\n  >\n    <!-- Overlay backdrop with blur effect -->\n    <div\n      class=\"absolute inset-0 bg-black/30 dark:bg-black/50 backdrop-blur-sm\"\n    ></div>\n\n    <!-- Contenu de la barre latérale -->\n    <div\n      class=\"fixed inset-y-0 left-0 max-w-xs w-full bg-white dark:bg-[#1e1e1e] shadow-lg dark:shadow-[0_0_30px_rgba(0,0,0,0.3)] transform transition-transform duration-300 ease-in-out border-r border-[#edf1f4]/50 dark:border-[#2a2a2a]\"\n      [class.translate-x-0]=\"sidebarOpen\"\n      [class.-translate-x-full]=\"!sidebarOpen\"\n      (click)=\"$event.stopPropagation()\"\n    >\n      <div class=\"flex flex-col h-full relative\">\n        <!-- Decorative elements -->\n        <div class=\"absolute inset-0 overflow-hidden pointer-events-none\">\n          <div\n            class=\"absolute top-0 left-1/4 w-px h-full bg-gradient-to-b from-transparent via-[#4f5fad]/10 dark:via-[#6d78c9]/5 to-transparent\"\n          ></div>\n          <div\n            class=\"absolute top-0 right-1/3 w-px h-full bg-gradient-to-b from-transparent via-[#4f5fad]/5 dark:via-[#6d78c9]/3 to-transparent\"\n          ></div>\n          <div\n            class=\"absolute top-[10%] left-[5%] w-32 h-32 rounded-full bg-gradient-to-br from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-2xl\"\n          ></div>\n          <div\n            class=\"absolute bottom-[10%] right-[5%] w-40 h-40 rounded-full bg-gradient-to-tl from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-2xl\"\n          ></div>\n        </div>\n\n        <!-- En-tête de la barre latérale -->\n        <div\n          class=\"flex items-center justify-between px-4 py-3 border-b border-[#edf1f4] dark:border-[#2a2a2a] relative z-10\"\n        >\n          <div>\n            <h3\n              class=\"text-xl font-bold bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent\"\n            >\n              DevBridge\n            </h3>\n            <p class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0]\">\n              Project Management Suite\n            </p>\n          </div>\n          <button\n            (click)=\"toggleSidebar()\"\n            class=\"text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] p-2 rounded-full hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] transition-colors relative group\"\n            aria-label=\"Close menu\"\n          >\n            <div\n              class=\"absolute inset-0 bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 opacity-0 group-hover:opacity-100 transition-opacity rounded-full blur-md\"\n            ></div>\n            <i class=\"fas fa-times relative z-10\"></i>\n          </button>\n        </div>\n\n        <!-- Navigation de la barre latérale -->\n        <nav\n          class=\"flex-1 px-2 py-4 space-y-1 overflow-y-auto bg-white dark:bg-[#1e1e1e] relative z-10\"\n        >\n          <!-- Navigation Items - Réorganisés avec Accueil en premier -->\n          <a\n            *ngFor=\"\n              let item of [\n                {\n                  route: '/',\n                  icon: 'fas fa-home',\n                  text: 'Accueil'\n                },\n                {\n                  route: '/projects',\n                  icon: 'fas fa-rocket',\n                  text: 'Projects'\n                },\n                {\n                  route: '/plannings',\n                  icon: 'far fa-calendar-check',\n                  text: 'Plannings'\n                },\n                {\n                  route: '/reunions',\n                  icon: 'fas fa-users-cog',\n                  text: 'Réunions'\n                },\n                {\n                  route: '/messages',\n                  icon: 'far fa-comment-dots',\n                  text: 'Messages'\n                },\n                {\n                  route: '/notifications',\n                  icon: 'far fa-bell',\n                  text: 'Notifications',\n                  badge: unreadNotificationsCount\n                }\n              ]\n            \"\n            routerLink=\"{{ item.route }}\"\n            (click)=\"toggleSidebar()\"\n            class=\"flex items-center px-3 py-2 text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] rounded-lg transition-all relative group overflow-hidden\"\n            routerLinkActive=\"bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium border-r-2 border-[#4f5fad] dark:border-[#6d78c9]\"\n            [routerLinkActiveOptions]=\"{\n              exact: item.route === '/' ? true : false\n            }\"\n          >\n            <!-- Hover effect -->\n            <span\n              class=\"absolute inset-0 w-1 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] opacity-0 group-hover:opacity-100 transition-opacity\"\n            ></span>\n\n            <div class=\"relative z-10 flex items-center w-full\">\n              <div class=\"relative\">\n                <i\n                  class=\"{{\n                    item.icon\n                  }} h-5 w-5 mr-3 text-[#4f5fad] dark:text-[#6d78c9] group-hover:text-[#3d4a85] dark:group-hover:text-[#4f5fad] transition-colors\"\n                ></i>\n                <!-- Glow effect -->\n                <div\n                  class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 opacity-0 group-hover:opacity-100 transition-opacity blur-md rounded-full\"\n                ></div>\n              </div>\n              <span>{{ item.text }}</span>\n              <div\n                *ngIf=\"item.badge && item.badge > 0\"\n                class=\"ml-auto bg-gradient-to-r from-[#ff8c00] to-[#ff6b00] text-white text-xs rounded-full h-5 min-w-5 px-1 flex items-center justify-center shadow-md animate-pulse\"\n              >\n                {{ item.badge > 99 ? \"99+\" : item.badge }}\n              </div>\n            </div>\n          </a>\n\n          <!-- Go to Dashboard button - uniquement pour admin et teacher -->\n          <a\n            *ngIf=\"\n              authService.userLoggedIn() &&\n              (currentUser?.role === 'admin' || currentUser?.role === 'teacher')\n            \"\n            routerLink=\"/admin/dashboard\"\n            (click)=\"toggleSidebar()\"\n            class=\"flex items-center px-3 py-2 text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#7826b5] dark:hover:text-[#9d4edd] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] rounded-lg transition-all relative group overflow-hidden mt-2\"\n          >\n            <!-- Hover effect -->\n            <span\n              class=\"absolute inset-0 w-1 bg-gradient-to-b from-[#7826b5] to-[#9d4edd] dark:from-[#7826b5] dark:to-[#9d4edd] opacity-0 group-hover:opacity-100 transition-opacity\"\n            ></span>\n\n            <div class=\"relative z-10 flex items-center w-full\">\n              <div class=\"relative\">\n                <i\n                  class=\"fas fa-tachometer-alt h-5 w-5 mr-3 text-[#7826b5] dark:text-[#9d4edd] group-hover:text-[#7826b5] dark:group-hover:text-[#9d4edd] transition-colors\"\n                ></i>\n                <!-- Glow effect -->\n                <div\n                  class=\"absolute inset-0 bg-[#7826b5]/20 dark:bg-[#9d4edd]/20 opacity-0 group-hover:opacity-100 transition-opacity blur-md rounded-full\"\n                ></div>\n              </div>\n              <span>Go to Dashboard</span>\n            </div>\n          </a>\n\n          <div\n            class=\"border-t border-[#edf1f4] dark:border-[#2a2a2a] my-2\"\n          ></div>\n\n          <ng-container *ngIf=\"!authService.userLoggedIn()\">\n            <a\n              routerLink=\"/login\"\n              (click)=\"toggleSidebar()\"\n              class=\"flex items-center px-3 py-2 text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] rounded-lg transition-all relative group overflow-hidden\"\n            >\n              <!-- Hover effect -->\n              <span\n                class=\"absolute inset-0 w-1 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] opacity-0 group-hover:opacity-100 transition-opacity\"\n              ></span>\n\n              <div class=\"relative z-10 flex items-center\">\n                <div class=\"relative\">\n                  <i\n                    class=\"fas fa-sign-in-alt mr-3 text-[#4f5fad] dark:text-[#6d78c9] group-hover:scale-110 transition-transform\"\n                  ></i>\n                  <!-- Glow effect -->\n                  <div\n                    class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 opacity-0 group-hover:opacity-100 transition-opacity blur-md rounded-full\"\n                  ></div>\n                </div>\n                <span>Connexion</span>\n              </div>\n            </a>\n\n            <a\n              routerLink=\"/signup\"\n              (click)=\"toggleSidebar()\"\n              class=\"flex items-center px-3 py-2 text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] rounded-lg transition-all relative group overflow-hidden\"\n            >\n              <!-- Hover effect -->\n              <span\n                class=\"absolute inset-0 w-1 bg-gradient-to-b from-[#7826b5] to-[#9d4edd] dark:from-[#7826b5] dark:to-[#9d4edd] opacity-0 group-hover:opacity-100 transition-opacity\"\n              ></span>\n\n              <div class=\"relative z-10 flex items-center\">\n                <div class=\"relative\">\n                  <i\n                    class=\"fas fa-user-plus mr-3 text-[#7826b5] dark:text-[#9d4edd] group-hover:scale-110 transition-transform\"\n                  ></i>\n                  <!-- Glow effect -->\n                  <div\n                    class=\"absolute inset-0 bg-[#7826b5]/20 dark:bg-[#9d4edd]/20 opacity-0 group-hover:opacity-100 transition-opacity blur-md rounded-full\"\n                  ></div>\n                </div>\n                <span>Inscription</span>\n              </div>\n            </a>\n          </ng-container>\n\n          <ng-container *ngIf=\"authService.userLoggedIn()\">\n            <a\n              routerLink=\"/profile\"\n              (click)=\"toggleSidebar()\"\n              class=\"flex items-center px-3 py-2 text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] rounded-lg transition-all relative group overflow-hidden\"\n            >\n              <!-- Hover effect -->\n              <span\n                class=\"absolute inset-0 w-1 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] opacity-0 group-hover:opacity-100 transition-opacity\"\n              ></span>\n\n              <div class=\"relative z-10 flex items-center\">\n                <div class=\"relative\">\n                  <i\n                    class=\"fas fa-user mr-3 text-[#4f5fad] dark:text-[#6d78c9] group-hover:scale-110 transition-transform\"\n                  ></i>\n                  <!-- Glow effect -->\n                  <div\n                    class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 opacity-0 group-hover:opacity-100 transition-opacity blur-md rounded-full\"\n                  ></div>\n                </div>\n                <span>Mon Profil</span>\n              </div>\n            </a>\n\n            <a\n              (click)=\"logout(); toggleSidebar()\"\n              class=\"flex items-center px-3 py-2 text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#ff6b69] dark:hover:text-[#ff8785] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] rounded-lg transition-all relative group overflow-hidden cursor-pointer\"\n            >\n              <!-- Hover effect -->\n              <span\n                class=\"absolute inset-0 w-1 bg-gradient-to-b from-[#ff6b69] to-[#ff8785] dark:from-[#ff6b69] dark:to-[#ff8785] opacity-0 group-hover:opacity-100 transition-opacity\"\n              ></span>\n\n              <div class=\"relative z-10 flex items-center\">\n                <div class=\"relative\">\n                  <i\n                    class=\"fas fa-sign-out-alt mr-3 text-[#ff6b69] dark:text-[#ff8785] group-hover:scale-110 transition-transform\"\n                  ></i>\n                  <!-- Glow effect -->\n                  <div\n                    class=\"absolute inset-0 bg-[#ff6b69]/20 dark:bg-[#ff8785]/20 opacity-0 group-hover:opacity-100 transition-opacity blur-md rounded-full\"\n                  ></div>\n                </div>\n                <span>Déconnexion</span>\n              </div>\n            </a>\n          </ng-container>\n        </nav>\n      </div>\n    </div>\n  </div>\n\n  <!-- Main Content -->\n  <div class=\"flex-1 flex flex-col overflow-hidden\">\n    <!-- Contenu principal -->\n    <main\n      class=\"flex-1 overflow-y-auto bg-[#edf1f4] dark:bg-[#121212] pt-16 pb-6 relative\"\n    >\n      <!-- Background decorative elements -->\n      <div class=\"absolute inset-0 overflow-hidden pointer-events-none\">\n        <div\n          class=\"absolute top-[15%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-br from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl\"\n        ></div>\n        <div\n          class=\"absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl\"\n        ></div>\n      </div>\n\n      <!-- Message de statut -->\n      <div\n        *ngIf=\"messageFromRedirect\"\n        class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10 mt-4\"\n      >\n        <div\n          class=\"bg-white dark:bg-[#1e1e1e] border-l-4 border-[#ff8c00] dark:border-[#ff6b00] rounded-lg p-4 mb-6 shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)] backdrop-blur-sm\"\n        >\n          <div class=\"flex items-center\">\n            <div class=\"relative\">\n              <i\n                class=\"fas fa-info-circle text-[#ff8c00] dark:text-[#ff6b00] text-lg mr-3\"\n              ></i>\n              <!-- Glow effect -->\n              <div\n                class=\"absolute inset-0 bg-[#ff8c00]/30 dark:bg-[#ff6b00]/30 blur-xl rounded-full transform scale-150 -z-10 animate-pulse\"\n              ></div>\n            </div>\n            <p\n              class=\"ml-3 text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0]\"\n            >\n              {{ messageFromRedirect }}\n            </p>\n          </div>\n        </div>\n      </div>\n\n      <!-- Router outlet -->\n      <div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\n        <router-outlet></router-outlet>\n      </div>\n    </main>\n  </div>\n</div>\n"], "mappings": "AACA,SAAiCA,aAAa,QAAQ,iBAAiB;AACvE,SAASC,OAAO,QAAkC,MAAM;AACxD,SAASC,MAAM,EAAEC,SAAS,EAAEC,oBAAoB,QAAQ,gBAAgB;AAOxE,SAASC,WAAW,QAAQ,8BAA8B;;;;;;;;;;;;;;;;ICmDhDC,EAAA,CAAAC,cAAA,YAMC;IAECD,EAAA,CAAAE,SAAA,eAEQ;IAERF,EAAA,CAAAC,cAAA,cAA6C;IAEzCD,EAAA,CAAAE,SAAA,YAEK;IAKPF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAI,MAAA,cAAO;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;IAlBvCH,EAAA,CAAAK,UAAA,4BAAAL,EAAA,CAAAM,eAAA,IAAAC,GAAA,EAA2C;;;;;IAuB7CP,EAAA,CAAAC,cAAA,YAKC;IAECD,EAAA,CAAAE,SAAA,eAEQ;IAERF,EAAA,CAAAC,cAAA,cAA6C;IAEzCD,EAAA,CAAAE,SAAA,YAEK;IAKPF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAI,MAAA,eAAQ;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;IAK1CH,EAAA,CAAAC,cAAA,YAKC;IAECD,EAAA,CAAAE,SAAA,eAEQ;IAERF,EAAA,CAAAC,cAAA,cAA6C;IAEzCD,EAAA,CAAAE,SAAA,YAEK;IAKPF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAI,MAAA,gBAAS;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;IAK3CH,EAAA,CAAAC,cAAA,YAKC;IAECD,EAAA,CAAAE,SAAA,eAEQ;IAERF,EAAA,CAAAC,cAAA,cAA6C;IAEzCD,EAAA,CAAAE,SAAA,YAEK;IAKPF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAI,MAAA,oBAAQ;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;IAK1CH,EAAA,CAAAC,cAAA,YAKC;IAECD,EAAA,CAAAE,SAAA,eAEQ;IAERF,EAAA,CAAAC,cAAA,cAA6C;IAEzCD,EAAA,CAAAE,SAAA,YAEK;IAKPF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAI,MAAA,eAAQ;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;IAI1CH,EAAA,CAAAC,cAAA,YAKC;IAECD,EAAA,CAAAE,SAAA,eAEQ;IAERF,EAAA,CAAAC,cAAA,cAA6C;IAEzCD,EAAA,CAAAE,SAAA,YAEK;IAKPF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAI,MAAA,cAAO;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;IASzCH,EAAA,CAAAC,cAAA,YAQC;IAECD,EAAA,CAAAE,SAAA,eAEQ;IAERF,EAAA,CAAAC,cAAA,cAA6C;IAEzCD,EAAA,CAAAE,SAAA,YAEK;IAKPF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAI,MAAA,sBAAe;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;IAkF7CH,EAAA,CAAAC,cAAA,aAKC;IAGGD,EAAA,CAAAE,SAAA,aAA6D;IAG7DF,EAAA,CAAAC,cAAA,eAEC;IACCD,EAAA,CAAAI,MAAA,GAOF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;IAPJH,EAAA,CAAAQ,SAAA,GAOF;IAPER,EAAA,CAAAS,kBAAA,MAAAC,OAAA,CAAAC,wBAAA,OAAAD,OAAA,CAAAC,wBAAA,gBAAAD,OAAA,CAAAC,wBAAA,YAOF;;;;;IA2BAX,EAAA,CAAAE,SAAA,aAGK;;;;;IACLF,EAAA,CAAAE,SAAA,aAGK;;;;;;;;;;;IA1DbF,EAAA,CAAAY,uBAAA,GAAmE;IACjEZ,EAAA,CAAAC,cAAA,cAA4C;IAE1CD,EAAA,CAAAa,UAAA,IAAAC,iDAAA,gBAuBI;IAGJd,EAAA,CAAAC,cAAA,iBAIC;IAHCD,EAAA,CAAAe,UAAA,mBAAAC,sEAAA;MAAAhB,EAAA,CAAAiB,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAnB,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAF,OAAA,CAAAG,cAAA,EAAgB;IAAA,EAAC;IAK1BtB,EAAA,CAAAC,cAAA,cAAyD;IACvDD,EAAA,CAAAE,SAAA,cAEO;IAITF,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAE,SAAA,cAEO;IACPF,EAAA,CAAAC,cAAA,cAGC;;IACCD,EAAA,CAAAa,UAAA,KAAAU,kDAAA,gBAGK;;IACLvB,EAAA,CAAAa,UAAA,KAAAW,kDAAA,gBAGK;;IACPxB,EAAA,CAAAG,YAAA,EAAM;IAIRH,EAAA,CAAAC,cAAA,kBAIC;IAHCD,EAAA,CAAAe,UAAA,mBAAAU,uEAAA;MAAAzB,EAAA,CAAAiB,aAAA,CAAAC,IAAA;MAAA,MAAAQ,OAAA,GAAA1B,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAK,OAAA,CAAAC,MAAA,EAAQ;IAAA,EAAC;IAKlB3B,EAAA,CAAAC,cAAA,eAA2D;IACzDD,EAAA,CAAAE,SAAA,eAEO;IAITF,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAE,SAAA,eAEO;IAITF,EAAA,CAAAG,YAAA,EAAS;IAGTH,EAAA,CAAAC,cAAA,aAGC;IACuBD,EAAA,CAAAI,MAAA,eAAO;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACpCH,EAAA,CAAAC,cAAA,gBAEC;IACCD,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,eAEC;IAECD,EAAA,CAAAE,SAAA,eAEO;IAMTF,EAAA,CAAAG,YAAA,EAAM;IAGZH,EAAA,CAAA4B,qBAAA,EAAe;;;;IA5GR5B,EAAA,CAAAQ,SAAA,GAAgC;IAAhCR,EAAA,CAAAK,UAAA,SAAAwB,MAAA,CAAAC,WAAA,CAAAC,YAAA,GAAgC;IA6C/B/B,EAAA,CAAAQ,SAAA,GAAiD;IAAjDR,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAAgC,eAAA,KAAAC,GAAA,EAAAjC,EAAA,CAAAkC,WAAA,OAAAL,MAAA,CAAAM,WAAA,GAAiD;IAG9CnC,EAAA,CAAAQ,SAAA,GAA4B;IAA5BR,EAAA,CAAAK,UAAA,UAAAL,EAAA,CAAAkC,WAAA,QAAAL,MAAA,CAAAM,WAAA,EAA4B;IAI5BnC,EAAA,CAAAQ,SAAA,GAAyB;IAAzBR,EAAA,CAAAK,UAAA,SAAAL,EAAA,CAAAkC,WAAA,SAAAL,MAAA,CAAAM,WAAA,EAAyB;IAuC5BnC,EAAA,CAAAQ,SAAA,IACF;IADER,EAAA,CAAAS,kBAAA,MAAAoB,MAAA,CAAAO,QAAA,MACF;IASIpC,EAAA,CAAAQ,SAAA,GAAoB;IAApBR,EAAA,CAAAK,UAAA,QAAAwB,MAAA,CAAAQ,YAAA,EAAArC,EAAA,CAAAsC,aAAA,CAAoB;;;;;IAW5BtC,EAAA,CAAAC,cAAA,eAA4B;IAKxBD,EAAA,CAAAE,SAAA,eAEO;IAIPF,EAAA,CAAAC,cAAA,gBAEC;IACCD,EAAA,CAAAE,SAAA,aAAuC;IACvCF,EAAA,CAAAI,MAAA,kBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAGTH,EAAA,CAAAC,cAAA,aAGC;IACCD,EAAA,CAAAE,SAAA,eAEO;IAIPF,EAAA,CAAAC,cAAA,iBAEC;IACCD,EAAA,CAAAE,SAAA,cAAqC;IACrCF,EAAA,CAAAI,MAAA,qBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;IAyITH,EAAA,CAAAC,cAAA,eAGC;IACCD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAQ,SAAA,GACF;IADER,EAAA,CAAAS,kBAAA,MAAA8B,QAAA,CAAAC,KAAA,gBAAAD,QAAA,CAAAC,KAAA,MACF;;;;;;;;;;;IAnEJxC,EAAA,CAAAC,cAAA,aA2CC;IANCD,EAAA,CAAAe,UAAA,mBAAA0B,sDAAA;MAAAzC,EAAA,CAAAiB,aAAA,CAAAyB,IAAA;MAAA,MAAAC,OAAA,GAAA3C,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAsB,OAAA,CAAAC,aAAA,EAAe;IAAA,EAAC;IAQzB5C,EAAA,CAAAE,SAAA,eAEQ;IAERF,EAAA,CAAAC,cAAA,eAAoD;IAEhDD,EAAA,CAAAE,SAAA,QAIK;IAKPF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAI,MAAA,GAAe;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAC5BH,EAAA,CAAAa,UAAA,IAAAgC,wCAAA,mBAKM;IACR7C,EAAA,CAAAG,YAAA,EAAM;;;;IAhCNH,EAAA,CAAA8C,qBAAA,eAAAP,QAAA,CAAAQ,KAAA,CAA6B;IAI7B/C,EAAA,CAAAK,UAAA,4BAAAL,EAAA,CAAAgC,eAAA,IAAAgB,GAAA,EAAAT,QAAA,CAAAQ,KAAA,yBAEE;IAUI/C,EAAA,CAAAQ,SAAA,GAEgI;IAFhIR,EAAA,CAAAiD,sBAAA,KAAAV,QAAA,CAAAW,IAAA,kIAEgI;IAO9HlD,EAAA,CAAAQ,SAAA,GAAe;IAAfR,EAAA,CAAAmD,iBAAA,CAAAZ,QAAA,CAAAa,IAAA,CAAe;IAElBpD,EAAA,CAAAQ,SAAA,GAAkC;IAAlCR,EAAA,CAAAK,UAAA,SAAAkC,QAAA,CAAAC,KAAA,IAAAD,QAAA,CAAAC,KAAA,KAAkC;;;;;;IASzCxC,EAAA,CAAAC,cAAA,aAQC;IAFCD,EAAA,CAAAe,UAAA,mBAAAsC,sDAAA;MAAArD,EAAA,CAAAiB,aAAA,CAAAqC,IAAA;MAAA,MAAAC,OAAA,GAAAvD,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAkC,OAAA,CAAAX,aAAA,EAAe;IAAA,EAAC;IAIzB5C,EAAA,CAAAE,SAAA,eAEQ;IAERF,EAAA,CAAAC,cAAA,eAAoD;IAEhDD,EAAA,CAAAE,SAAA,aAEK;IAKPF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAI,MAAA,sBAAe;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;;IAQhCH,EAAA,CAAAY,uBAAA,GAAkD;IAChDZ,EAAA,CAAAC,cAAA,aAIC;IAFCD,EAAA,CAAAe,UAAA,mBAAAyC,iEAAA;MAAAxD,EAAA,CAAAiB,aAAA,CAAAwC,IAAA;MAAA,MAAAC,OAAA,GAAA1D,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAqC,OAAA,CAAAd,aAAA,EAAe;IAAA,EAAC;IAIzB5C,EAAA,CAAAE,SAAA,eAEQ;IAERF,EAAA,CAAAC,cAAA,cAA6C;IAEzCD,EAAA,CAAAE,SAAA,aAEK;IAKPF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAI,MAAA,gBAAS;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAI1BH,EAAA,CAAAC,cAAA,aAIC;IAFCD,EAAA,CAAAe,UAAA,mBAAA4C,iEAAA;MAAA3D,EAAA,CAAAiB,aAAA,CAAAwC,IAAA;MAAA,MAAAG,OAAA,GAAA5D,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAuC,OAAA,CAAAhB,aAAA,EAAe;IAAA,EAAC;IAIzB5C,EAAA,CAAAE,SAAA,gBAEQ;IAERF,EAAA,CAAAC,cAAA,eAA6C;IAEzCD,EAAA,CAAAE,SAAA,cAEK;IAKPF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAI,MAAA,mBAAW;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAG9BH,EAAA,CAAA4B,qBAAA,EAAe;;;;;;IAEf5B,EAAA,CAAAY,uBAAA,GAAiD;IAC/CZ,EAAA,CAAAC,cAAA,aAIC;IAFCD,EAAA,CAAAe,UAAA,mBAAA8C,iEAAA;MAAA7D,EAAA,CAAAiB,aAAA,CAAA6C,IAAA;MAAA,MAAAC,OAAA,GAAA/D,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAA0C,OAAA,CAAAnB,aAAA,EAAe;IAAA,EAAC;IAIzB5C,EAAA,CAAAE,SAAA,eAEQ;IAERF,EAAA,CAAAC,cAAA,cAA6C;IAEzCD,EAAA,CAAAE,SAAA,aAEK;IAKPF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAI,MAAA,iBAAU;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAI3BH,EAAA,CAAAC,cAAA,aAGC;IAFCD,EAAA,CAAAe,UAAA,mBAAAiD,iEAAA;MAAAhE,EAAA,CAAAiB,aAAA,CAAA6C,IAAA;MAAA,MAAAG,OAAA,GAAAjE,EAAA,CAAAoB,aAAA;MAAS6C,OAAA,CAAAtC,MAAA,EAAQ;MAAA,OAAE3B,EAAA,CAAAqB,WAAA,CAAA4C,OAAA,CAAArB,aAAA,EAAe;IAAA,EAAC;IAInC5C,EAAA,CAAAE,SAAA,iBAEQ;IAERF,EAAA,CAAAC,cAAA,eAA6C;IAEzCD,EAAA,CAAAE,SAAA,cAEK;IAKPF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAI,MAAA,wBAAW;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAG9BH,EAAA,CAAA4B,qBAAA,EAAe;;;;;IAuBnB5B,EAAA,CAAAC,cAAA,eAGC;IAMOD,EAAA,CAAAE,SAAA,aAEK;IAKPF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,aAEC;IACCD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAQ,SAAA,GACF;IADER,EAAA,CAAAS,kBAAA,MAAAyD,OAAA,CAAAC,mBAAA,MACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AD1wBZ,OAAM,MAAOC,oBAAoB;EAe/BC,YACSvC,WAA4B,EAC3BiB,KAAqB,EACrBuB,MAAc,EACfC,aAAgC,EAC/BC,cAA8B,EAC9BC,YAA0B,EAC1BC,WAAwB;IANzB,KAAA5C,WAAW,GAAXA,WAAW;IACV,KAAAiB,KAAK,GAALA,KAAK;IACL,KAAAuB,MAAM,GAANA,MAAM;IACP,KAAAC,aAAa,GAAbA,aAAa;IACZ,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,WAAW,GAAXA,WAAW;IArBrB,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,WAAW,GAAgB,IAAI;IAC/B,KAAAV,mBAAmB,GAAW,EAAE;IAChC,KAAAxD,wBAAwB,GAAG,CAAC;IAC5B,KAAAmE,YAAY,GAAG,KAAK;IACpB,KAAA1C,QAAQ,GAAW,EAAE;IACrB,KAAAC,YAAY,GAAW,EAAE;IAGjB,KAAA0C,QAAQ,GAAG,IAAIpF,OAAO,EAAQ;IACrB,KAAAqF,iBAAiB,GAAG,GAAG;IAChC,KAAAC,aAAa,GAAmB,EAAE;IAWxC,IAAI,CAACC,aAAa,EAAE;IACpB,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAAChD,WAAW,GAAG,IAAI,CAACsC,YAAY,CAACW,SAAS;EAChD;EAEQD,eAAeA,CAAA;IACrB;IACA,MAAME,QAAQ,GAAG,IAAI,CAACvD,WAAW,CAACwD,cAAc,EAAE;IAClD,MAAMC,QAAQ,GAAG,IAAI,CAACb,WAAW,CAACc,gBAAgB;IAElDC,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEL,QAAQ,CAAC;IACnCI,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEH,QAAQ,CAAC;IAEnC;IACA,MAAMI,IAAI,GAAGJ,QAAQ,IAAIF,QAAQ;IAEjC,IAAIM,IAAI,EAAE;MACR,IAAI,CAACC,oBAAoB,CAACD,IAAI,CAAC;MAE/B;MACA,IAAI,IAAI,CAAC7D,WAAW,CAACC,YAAY,EAAE,EAAE;QACnC,IAAI,CAAC2C,WAAW,CAACmB,eAAe,EAAE,CAACC,SAAS,CAAC;UAC3CC,IAAI,EAAGC,WAAW,IAAI;YACpBP,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEM,WAAW,CAAC;YAChD,IAAI,CAACJ,oBAAoB,CAACI,WAAW,CAAC;UACxC,CAAC;UACDC,KAAK,EAAGC,GAAG,IAAI;YACbT,OAAO,CAACQ,KAAK,CAAC,8BAA8B,EAAEC,GAAG,CAAC;UACpD;SACD,CAAC;;KAEL,MAAM;MACL;MACA,IAAI,CAAC9D,QAAQ,GAAG,EAAE;MAClB,IAAI,CAACC,YAAY,GAAG,mCAAmC;;IAGzDoD,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE,IAAI,CAACrD,YAAY,CAAC;IAEtE;IACA,IAAIgD,QAAQ,IAAI,CAACE,QAAQ,EAAE;MACzB,IAAI,CAACb,WAAW,CAACyB,iBAAiB,CAACd,QAAQ,CAAC;KAC7C,MAAM,IAAI,CAACA,QAAQ,IAAIE,QAAQ,EAAE;MAChC,IAAI,CAACzD,WAAW,CAACsE,cAAc,CAACb,QAAQ,CAAC;;EAE7C;EACAc,QAAQA,CAAA;IACN,IAAI,CAACC,sBAAsB,EAAE;IAC7B,IAAI,CAACC,sBAAsB,EAAE;IAC7B,IAAI,CAACC,uBAAuB,EAAE;IAC9B,IAAI,CAACC,uBAAuB,EAAE;EAChC;EAEQvB,aAAaA,CAAA;IACnB,IAAI,CAACJ,YAAY,GAAG4B,MAAM,CAACC,UAAU,GAAG,IAAI,CAAC3B,iBAAiB;IAC9D,IAAI,CAAC,IAAI,CAACF,YAAY,EAAE;MACtB,IAAI,CAACH,WAAW,GAAG,KAAK;;EAE5B;EACQ8B,uBAAuBA,CAAA;IAC7B;IACA,IAAI,CAACjC,cAAc,CAACoC,kBAAkB,CACnCC,IAAI,CAAChH,SAAS,CAAC,IAAI,CAACkF,QAAQ,CAAC,EAAEjF,oBAAoB,EAAE,CAAC,CACtDgG,SAAS,CAAEgB,KAAK,IAAI;MACnB,IAAI,CAACnG,wBAAwB,GAAGmG,KAAK;IACvC,CAAC,CAAC;IACJ;IACA,IAAI,IAAI,CAAChF,WAAW,CAACC,YAAY,EAAE,EAAE;MACnC,IAAI,CAACyC,cAAc,CAACuC,gBAAgB,CAAC,IAAI,CAAC,CAACjB,SAAS,EAAE;;EAE1D;EACQS,sBAAsBA,CAAA;IAC5B;IACA,MAAMS,cAAc,GAAG,IAAI,CAAClF,WAAW,CAACmF,YAAY,CACjDJ,IAAI,CAAChH,SAAS,CAAC,IAAI,CAACkF,QAAQ,CAAC,CAAC,CAC9Be,SAAS,CAAEH,IAAI,IAAI;MAClB,IAAI,CAACd,WAAW,GAAGc,IAAI;MACvB,IAAI,CAACC,oBAAoB,CAACD,IAAI,CAAC;IACjC,CAAC,CAAC;IAEJ;IACA,MAAMuB,cAAc,GAAG,IAAI,CAACxC,WAAW,CAACuC,YAAY,CACjDJ,IAAI,CAAChH,SAAS,CAAC,IAAI,CAACkF,QAAQ,CAAC,CAAC,CAC9Be,SAAS,CAAEH,IAAI,IAAI;MAClB,IAAIA,IAAI,EAAE;QACR,IAAI,CAACd,WAAW,GAAGc,IAAI;QACvB,IAAI,CAACC,oBAAoB,CAACD,IAAI,CAAC;;IAEnC,CAAC,CAAC;IAEJ,IAAI,CAACV,aAAa,CAACkC,IAAI,CAACH,cAAc,EAAEE,cAAc,CAAC;EACzD;EAEQtB,oBAAoBA,CAACD,IAAS;IACpC,IAAIA,IAAI,EAAE;MACR,IAAI,CAACvD,QAAQ,GAAGuD,IAAI,CAACyB,QAAQ,IAAIzB,IAAI,CAACvD,QAAQ,IAAI,EAAE;MAEpD;MACA,IAAIiF,UAAU,GAAG,KAAK;MAEtB;MACA,IACE1B,IAAI,CAAC2B,YAAY,IACjB3B,IAAI,CAAC2B,YAAY,KAAK,MAAM,IAC5B3B,IAAI,CAAC2B,YAAY,CAACC,IAAI,EAAE,KAAK,EAAE,IAC/B5B,IAAI,CAAC2B,YAAY,KAAK,WAAW,EACjC;QACA,IAAI,CAACjF,YAAY,GAAGsD,IAAI,CAAC2B,YAAY;QACrCD,UAAU,GAAG,IAAI;QACjB5B,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAACrD,YAAY,CAAC;;MAGvD;MACA,IACE,CAACgF,UAAU,IACX1B,IAAI,CAAC6B,KAAK,IACV7B,IAAI,CAAC6B,KAAK,KAAK,MAAM,IACrB7B,IAAI,CAAC6B,KAAK,CAACD,IAAI,EAAE,KAAK,EAAE,IACxB5B,IAAI,CAAC6B,KAAK,KAAK,WAAW,EAC1B;QACA,IAAI,CAACnF,YAAY,GAAGsD,IAAI,CAAC6B,KAAK;QAC9BH,UAAU,GAAG,IAAI;QACjB5B,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE,IAAI,CAACrD,YAAY,CAAC;;MAGhD;MACA,IACEgF,UAAU,IACV,CAAC,IAAI,CAAChF,YAAY,CAACoF,UAAU,CAAC,MAAM,CAAC,IACrC,CAAC,IAAI,CAACpF,YAAY,CAACoF,UAAU,CAAC,SAAS,CAAC,EACxC;QACA;QACA,IAAI,IAAI,CAACpF,YAAY,CAACoF,UAAU,CAAC,GAAG,CAAC,EAAE;UACrC,IAAI,CAACpF,YAAY,GAAG,GAAGtC,WAAW,CAAC2H,UAAU,CAACC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,GAC9D,IAAI,CAACtF,YACP,EAAE;SACH,MAAM;UACL,IAAI,CAACA,YAAY,GAAG,GAAGtC,WAAW,CAAC2H,UAAU,CAACC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,IAC9D,IAAI,CAACtF,YACP,EAAE;;QAEJoD,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAACrD,YAAY,CAAC;;MAG9D;MACA,IAAI,CAACgF,UAAU,EAAE;QACf,IAAI,CAAChF,YAAY,GAAG,mCAAmC;QACvDoD,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;;MAGpCD,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE,IAAI,CAACrD,YAAY,CAAC;;EAE3E;EACQiE,sBAAsBA,CAAA;IAC5B,IAAI,CAACvD,KAAK,CAAC6E,WAAW,CACnBf,IAAI,CAAChH,SAAS,CAAC,IAAI,CAACkF,QAAQ,CAAC,CAAC,CAC9Be,SAAS,CAAE+B,MAAM,IAAI;MACpB,IAAI,CAAC1D,mBAAmB,GAAG0D,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE;IACpD,CAAC,CAAC;EACN;EACQrB,uBAAuBA,CAAA;IAC7B,IAAI,CAAClC,MAAM,CAACwD,MAAM,CACfjB,IAAI,CACHjH,MAAM,CAAEmI,KAAK,IAAKA,KAAK,YAAYrI,aAAa,CAAC,EACjDG,SAAS,CAAC,IAAI,CAACkF,QAAQ,CAAC,CACzB,CACAe,SAAS,CAAC,MAAK;MACd,IAAI,CAACnB,WAAW,GAAG,KAAK;MACxB,IAAI,CAACC,eAAe,GAAG,KAAK;IAC9B,CAAC,CAAC;EACN;EACAhC,aAAaA,CAAA;IACX,IAAI,CAAC+B,WAAW,GAAG,CAAC,IAAI,CAACA,WAAW;EACtC;EACAqD,iBAAiBA,CAAA;IACf,IAAI,CAACpD,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;EAC9C;EAEAtD,cAAcA,CAAA;IACZ,IAAI,CAACmD,YAAY,CAACnD,cAAc,EAAE;EACpC;EAEAK,MAAMA,CAAA;IACJ,IAAI,CAACG,WAAW,CAACH,MAAM,EAAE,CAACmE,SAAS,CAAC;MAClCC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACnB,eAAe,GAAG,KAAK;QAC5B,IAAI,CAACD,WAAW,GAAG,KAAK;QACxB,IAAI,CAACE,WAAW,GAAG,IAAI;QACvB;QACA,IAAI,CAACxC,YAAY,GAAG,mCAAmC;QACvD;QACA,IAAI,CAACqC,WAAW,CAACyB,iBAAiB,CAAC,EAAE,CAAC;QACtC,IAAI,CAAC7B,MAAM,CAAC2D,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;MAClC,CAAC;MACDhC,KAAK,EAAGC,GAAG,IAAI;QACbT,OAAO,CAACQ,KAAK,CAAC,eAAe,EAAEC,GAAG,CAAC;QACnC,IAAI,CAACpE,WAAW,CAACoG,aAAa,EAAE;QAChC,IAAI,CAACrD,WAAW,GAAG,IAAI;QACvB;QACA,IAAI,CAACxC,YAAY,GAAG,mCAAmC;QACvD;QACA,IAAI,CAACqC,WAAW,CAACyB,iBAAiB,CAAC,EAAE,CAAC;QACtC,IAAI,CAAC7B,MAAM,CAAC2D,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;MAClC;KACD,CAAC;EACJ;EACAE,WAAWA,CAAA;IACT;IACA,IAAI,CAAClD,aAAa,CAACmD,OAAO,CAAEC,GAAG,IAAKA,GAAG,CAACC,WAAW,EAAE,CAAC;IACtD,IAAI,CAACvD,QAAQ,CAACgB,IAAI,EAAE;IACpB,IAAI,CAAChB,QAAQ,CAACwD,QAAQ,EAAE;EAC1B;;;uBA3OWnE,oBAAoB,EAAApE,EAAA,CAAAwI,iBAAA,CAAAC,EAAA,CAAAC,eAAA,GAAA1I,EAAA,CAAAwI,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAA5I,EAAA,CAAAwI,iBAAA,CAAAG,EAAA,CAAAE,MAAA,GAAA7I,EAAA,CAAAwI,iBAAA,CAAAM,EAAA,CAAAC,iBAAA,GAAA/I,EAAA,CAAAwI,iBAAA,CAAAQ,EAAA,CAAAxE,cAAA,GAAAxE,EAAA,CAAAwI,iBAAA,CAAAS,EAAA,CAAAC,YAAA,GAAAlJ,EAAA,CAAAwI,iBAAA,CAAAW,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAApBhF,oBAAoB;MAAAiF,SAAA;MAAAC,YAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;mBAApBC,GAAA,CAAAvE,aAAA,EAAe;UAAA,UAAAlF,EAAA,CAAA0J,eAAA;;;;;;;;UChB5B1J,EAAA,CAAAC,cAAA,aAGC;;UAECD,EAAA,CAAAE,SAAA,aAAmC;UAGnCF,EAAA,CAAAC,cAAA,aAA6C;UAQvCD,EAAA,CAAAE,SAAA,aAEO;UAKPF,EAAA,CAAAC,cAAA,aAA6C;UAEzCD,EAAA,CAAA2J,cAAA,EAMC;UAND3J,EAAA,CAAAC,cAAA,cAMC;UACCD,EAAA,CAAAE,SAAA,gBAKE;UAOJF,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAA4J,eAAA,EAEC;UAFD5J,EAAA,CAAAE,SAAA,eAEO;UACTF,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAEG;UAAAD,EAAA,CAAAI,MAAA,iBAAS;UAAAJ,EAAA,CAAAG,YAAA,EACX;UAGLH,EAAA,CAAAC,cAAA,eAA+C;UAK3CD,EAAA,CAAAa,UAAA,KAAAgJ,kCAAA,gBAwBI;UAGJ7J,EAAA,CAAAa,UAAA,KAAAiJ,kCAAA,gBAuBI;UAGJ9J,EAAA,CAAAa,UAAA,KAAAkJ,kCAAA,gBAuBI;UAGJ/J,EAAA,CAAAa,UAAA,KAAAmJ,kCAAA,gBAuBI;UAGJhK,EAAA,CAAAa,UAAA,KAAAoJ,kCAAA,gBAuBI;UAEJjK,EAAA,CAAAa,UAAA,KAAAqJ,kCAAA,gBAuBI;UAEJlK,EAAA,CAAAE,SAAA,eAEO;UAGPF,EAAA,CAAAa,UAAA,KAAAsJ,kCAAA,gBA0BI;UACNnK,EAAA,CAAAG,YAAA,EAAM;UAOZH,EAAA,CAAAC,cAAA,kBAEC;UAIKD,EAAA,CAAAE,SAAA,eAEO;UAITF,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAAkE;UAK5DD,EAAA,CAAAe,UAAA,mBAAAqJ,uDAAA;YAAA,OAASX,GAAA,CAAA7G,aAAA,EAAe;UAAA,EAAC;UAIzB5C,EAAA,CAAAE,SAAA,eAEO;UACPF,EAAA,CAAA2J,cAAA,EAKC;UALD3J,EAAA,CAAAC,cAAA,eAKC;UACCD,EAAA,CAAAE,SAAA,gBAKE;UACJF,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAA4J,eAAA,EAEG;UAFH5J,EAAA,CAAAC,cAAA,gBAEG;UAAAD,EAAA,CAAAI,MAAA,YAAI;UAAAJ,EAAA,CAAAG,YAAA,EACN;UAIHH,EAAA,CAAAC,cAAA,aAAgE;UAK1DD,EAAA,CAAAI,MAAA,mBACF;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UAELH,EAAA,CAAAE,SAAA,eAEO;UACTF,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAEG;UAAAD,EAAA,CAAAI,MAAA,gCAAwB;UAAAJ,EAAA,CAAAG,YAAA,EAC1B;UAOLH,EAAA,CAAAC,cAAA,eAA+B;UAC7BD,EAAA,CAAAa,UAAA,KAAAwJ,6CAAA,6BAgHe;UAGfrK,EAAA,CAAAa,UAAA,KAAAyJ,4CAAA,kCAAAtK,EAAA,CAAAuK,sBAAA,CAsCc;UAChBvK,EAAA,CAAAG,YAAA,EAAM;UAMZH,EAAA,CAAAC,cAAA,eAIC;UADCD,EAAA,CAAAe,UAAA,mBAAAyJ,oDAAA;YAAA,OAAAf,GAAA,CAAA9E,WAAA,IAAwB8E,GAAA,CAAA7G,aAAA,EAAe;UAAA,EAAC;UAGxC5C,EAAA,CAAAE,SAAA,eAEO;UAGPF,EAAA,CAAAC,cAAA,eAKC;UADCD,EAAA,CAAAe,UAAA,mBAAA0J,oDAAAC,MAAA;YAAA,OAASA,MAAA,CAAAC,eAAA,EAAwB;UAAA,EAAC;UAElC3K,EAAA,CAAAC,cAAA,eAA2C;UAGvCD,EAAA,CAAAE,SAAA,eAEO;UAUTF,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,eAEC;UAKKD,EAAA,CAAAI,MAAA,mBACF;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,aAAsD;UACpDD,EAAA,CAAAI,MAAA,kCACF;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAENH,EAAA,CAAAC,cAAA,kBAIC;UAHCD,EAAA,CAAAe,UAAA,mBAAA6J,uDAAA;YAAA,OAASnB,GAAA,CAAA7G,aAAA,EAAe;UAAA,EAAC;UAIzB5C,EAAA,CAAAE,SAAA,eAEO;UAETF,EAAA,CAAAG,YAAA,EAAS;UAIXH,EAAA,CAAAC,cAAA,eAEC;UAECD,EAAA,CAAAa,UAAA,KAAAgK,kCAAA,gBAqEI;UAGJ7K,EAAA,CAAAa,UAAA,KAAAiK,kCAAA,gBA0BI;UAEJ9K,EAAA,CAAAE,SAAA,eAEO;UAEPF,EAAA,CAAAa,UAAA,KAAAkK,6CAAA,4BAgDe;UAEf/K,EAAA,CAAAa,UAAA,KAAAmK,6CAAA,4BA+Ce;UACjBhL,EAAA,CAAAG,YAAA,EAAM;UAMZH,EAAA,CAAAC,cAAA,eAAkD;UAO5CD,EAAA,CAAAE,SAAA,eAEO;UAITF,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAa,UAAA,KAAAoK,oCAAA,kBAwBM;UAGNjL,EAAA,CAAAC,cAAA,eAAkE;UAChED,EAAA,CAAAE,SAAA,qBAA+B;UACjCF,EAAA,CAAAG,YAAA,EAAM;;;;UAhyBVH,EAAA,CAAAkL,WAAA,SAAAlL,EAAA,CAAAkC,WAAA,QAAAuH,GAAA,CAAAtH,WAAA,EAAkC;UA4DvBnC,EAAA,CAAAQ,SAAA,IAAgC;UAAhCR,EAAA,CAAAK,UAAA,SAAAoJ,GAAA,CAAA3H,WAAA,CAAAC,YAAA,GAAgC;UA2BhC/B,EAAA,CAAAQ,SAAA,GAAgC;UAAhCR,EAAA,CAAAK,UAAA,SAAAoJ,GAAA,CAAA3H,WAAA,CAAAC,YAAA,GAAgC;UA0BhC/B,EAAA,CAAAQ,SAAA,GAAgC;UAAhCR,EAAA,CAAAK,UAAA,SAAAoJ,GAAA,CAAA3H,WAAA,CAAAC,YAAA,GAAgC;UA0BhC/B,EAAA,CAAAQ,SAAA,GAAgC;UAAhCR,EAAA,CAAAK,UAAA,SAAAoJ,GAAA,CAAA3H,WAAA,CAAAC,YAAA,GAAgC;UA0BhC/B,EAAA,CAAAQ,SAAA,GAAgC;UAAhCR,EAAA,CAAAK,UAAA,SAAAoJ,GAAA,CAAA3H,WAAA,CAAAC,YAAA,GAAgC;UAyBhC/B,EAAA,CAAAQ,SAAA,GAAgC;UAAhCR,EAAA,CAAAK,UAAA,SAAAoJ,GAAA,CAAA3H,WAAA,CAAAC,YAAA,GAAgC;UA8BhC/B,EAAA,CAAAQ,SAAA,GAIb;UAJaR,EAAA,CAAAK,UAAA,SAAAoJ,GAAA,CAAA3H,WAAA,CAAAC,YAAA,QAAA0H,GAAA,CAAA5E,WAAA,kBAAA4E,GAAA,CAAA5E,WAAA,CAAAsG,IAAA,kBAAA1B,GAAA,CAAA5E,WAAA,kBAAA4E,GAAA,CAAA5E,WAAA,CAAAsG,IAAA,iBAIb;UAkGyBnL,EAAA,CAAAQ,SAAA,IAAkC;UAAlCR,EAAA,CAAAK,UAAA,SAAAoJ,GAAA,CAAA3H,WAAA,CAAAC,YAAA,GAAkC,aAAAqJ,GAAA;UAkKvDpL,EAAA,CAAAQ,SAAA,GAA6B;UAA7BR,EAAA,CAAAkL,WAAA,YAAAzB,GAAA,CAAA9E,WAAA,CAA6B;UAW3B3E,EAAA,CAAAQ,SAAA,GAAmC;UAAnCR,EAAA,CAAAkL,WAAA,kBAAAzB,GAAA,CAAA9E,WAAA,CAAmC,uBAAA8E,GAAA,CAAA9E,WAAA;UAuD/B3E,EAAA,CAAAQ,SAAA,IAiCV;UAjCUR,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAAqL,eAAA,KAAAC,GAAA,EAAAtL,EAAA,CAAAM,eAAA,KAAAiL,GAAA,GAAAvL,EAAA,CAAAM,eAAA,KAAAkL,GAAA,GAAAxL,EAAA,CAAAM,eAAA,KAAAmL,GAAA,GAAAzL,EAAA,CAAAM,eAAA,KAAAoL,GAAA,GAAA1L,EAAA,CAAAM,eAAA,KAAAqL,GAAA,GAAA3L,EAAA,CAAAgC,eAAA,KAAA4J,GAAA,EAAAnC,GAAA,CAAA9I,wBAAA,GAiCV;UAqCaX,EAAA,CAAAQ,SAAA,GAIb;UAJaR,EAAA,CAAAK,UAAA,SAAAoJ,GAAA,CAAA3H,WAAA,CAAAC,YAAA,QAAA0H,GAAA,CAAA5E,WAAA,kBAAA4E,GAAA,CAAA5E,WAAA,CAAAsG,IAAA,kBAAA1B,GAAA,CAAA5E,WAAA,kBAAA4E,GAAA,CAAA5E,WAAA,CAAAsG,IAAA,iBAIb;UA2ByBnL,EAAA,CAAAQ,SAAA,GAAiC;UAAjCR,EAAA,CAAAK,UAAA,UAAAoJ,GAAA,CAAA3H,WAAA,CAAAC,YAAA,GAAiC;UAkDjC/B,EAAA,CAAAQ,SAAA,GAAgC;UAAhCR,EAAA,CAAAK,UAAA,SAAAoJ,GAAA,CAAA3H,WAAA,CAAAC,YAAA,GAAgC;UAuEhD/B,EAAA,CAAAQ,SAAA,GAAyB;UAAzBR,EAAA,CAAAK,UAAA,SAAAoJ,GAAA,CAAAtF,mBAAA,CAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}