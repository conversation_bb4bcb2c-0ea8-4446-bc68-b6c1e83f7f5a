{"ast": null, "code": "import { fromEvent, merge, of } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/logger.service\";\nimport * as i2 from \"@angular/common\";\nfunction ConnectionStatusComponent_div_0_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 7);\n  }\n}\nconst _c0 = function (a0, a1) {\n  return {\n    online: a0,\n    offline: a1\n  };\n};\nfunction ConnectionStatusComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"div\", 3);\n    i0.ɵɵelement(3, \"i\", 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 5);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, ConnectionStatusComponent_div_0_div_6_Template, 1, 0, \"div\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(4, _c0, ctx_r0.isOnline, !ctx_r0.isOnline));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.isOnline ? \"fa-wifi\" : \"fa-exclamation-triangle\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.isOnline ? \"Connexion r\\u00E9tablie\" : \"Connexion perdue\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isOnline);\n  }\n}\nexport let ConnectionStatusComponent = /*#__PURE__*/(() => {\n  class ConnectionStatusComponent {\n    constructor(logger) {\n      this.logger = logger;\n      this.isOnline = navigator.onLine;\n      this.showStatus = false;\n      this.subscriptions = [];\n    }\n    ngOnInit() {\n      // Créer un observable qui combine les événements online et offline\n      const online$ = fromEvent(window, 'online').pipe(map(() => true));\n      const offline$ = fromEvent(window, 'offline').pipe(map(() => false));\n      const initialStatus$ = of(navigator.onLine);\n      // S'abonner aux changements d'état de connexion\n      const connectionSub = merge(initialStatus$, online$, offline$).subscribe(isOnline => {\n        this.isOnline = isOnline;\n        this.showStatus = true;\n        this.logger.debug(`Connection status changed: ${isOnline ? 'online' : 'offline'}`);\n        // Masquer le statut après 5 secondes\n        setTimeout(() => {\n          this.showStatus = false;\n        }, 5000);\n      });\n      this.subscriptions.push(connectionSub);\n    }\n    ngOnDestroy() {\n      // Nettoyer les abonnements\n      this.subscriptions.forEach(sub => sub.unsubscribe());\n    }\n    static {\n      this.ɵfac = function ConnectionStatusComponent_Factory(t) {\n        return new (t || ConnectionStatusComponent)(i0.ɵɵdirectiveInject(i1.LoggerService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ConnectionStatusComponent,\n        selectors: [[\"app-connection-status\"]],\n        decls: 1,\n        vars: 1,\n        consts: [[\"class\", \"connection-status\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"connection-status\", 3, \"ngClass\"], [1, \"status-content\"], [1, \"status-icon\"], [1, \"fas\", 3, \"ngClass\"], [1, \"status-text\"], [\"class\", \"glow-effect\", 4, \"ngIf\"], [1, \"glow-effect\"]],\n        template: function ConnectionStatusComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵtemplate(0, ConnectionStatusComponent_div_0_Template, 7, 7, \"div\", 0);\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"ngIf\", ctx.showStatus);\n          }\n        },\n        dependencies: [i2.NgClass, i2.NgIf],\n        styles: [\".connection-status[_ngcontent-%COMP%]{position:fixed;top:20px;left:50%;transform:translate(-50%);z-index:1000;display:flex;align-items:center;padding:10px 20px;border-radius:30px;box-shadow:0 4px 12px #00000026;animation:_ngcontent-%COMP%_slideDown .3s ease-out,fadeOut .3s ease-out 4.7s;overflow:hidden;-webkit-backdrop-filter:blur(5px);backdrop-filter:blur(5px);position:relative}.online[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ff8c00,#ff6b00);color:#fff;box-shadow:0 0 15px #ff8c0080;animation:_ngcontent-%COMP%_pulse 2s infinite}@keyframes _ngcontent-%COMP%_pulse{0%{box-shadow:0 0 15px #ff8c0080}50%{box-shadow:0 0 25px #ff8c00cc}to{box-shadow:0 0 15px #ff8c0080}}.offline[_ngcontent-%COMP%]{background-color:#f44336;color:#fff}.status-content[_ngcontent-%COMP%]{display:flex;align-items:center;position:relative;z-index:2}.status-icon[_ngcontent-%COMP%]{margin-right:10px;font-size:18px}.status-text[_ngcontent-%COMP%]{font-weight:600;font-size:14px;text-shadow:0 1px 2px rgba(0,0,0,.2)}.glow-effect[_ngcontent-%COMP%]{position:absolute;top:-50%;left:-50%;width:200%;height:200%;background:radial-gradient(circle,rgba(255,140,0,.4) 0%,rgba(255,107,0,0) 70%);animation:_ngcontent-%COMP%_rotate 8s linear infinite;z-index:1}@keyframes _ngcontent-%COMP%_rotate{0%{transform:rotate(0)}to{transform:rotate(360deg)}}@keyframes _ngcontent-%COMP%_slideDown{0%{transform:translate(-50%,-100%);opacity:0}to{transform:translate(-50%);opacity:1}}@keyframes _ngcontent-%COMP%_fadeOut{0%{opacity:1}to{opacity:0}}\"]\n      });\n    }\n  }\n  return ConnectionStatusComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}