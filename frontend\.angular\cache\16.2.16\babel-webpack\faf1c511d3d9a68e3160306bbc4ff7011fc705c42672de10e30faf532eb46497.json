{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/equipe.service\";\nimport * as i2 from \"src/app/services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"../ai-chat/ai-chat.component\";\nconst _c0 = function (a0, a1, a2) {\n  return {\n    \"bg-gradient-to-br from-[#4f5fad] to-[#7826b5]\": a0,\n    \"bg-gradient-to-br from-[#00ff9d] to-[#38ef7d]\": a1,\n    \"bg-gradient-to-br from-[#6d6870] to-[#a0a0a0]\": a2\n  };\n};\nconst _c1 = function (a0, a1, a2) {\n  return {\n    \"fa-graduation-cap\": a0,\n    \"fa-chalkboard-teacher\": a1,\n    \"fa-user\": a2\n  };\n};\nconst _c2 = function (a0, a1) {\n  return {\n    \"bg-[#00ff9d]/10 text-[#00ff9d]\": a0,\n    \"bg-[#4f5fad]/10 text-[#4f5fad]\": a1\n  };\n};\nconst _c3 = function (a0, a1) {\n  return {\n    \"fa-user-shield\": a0,\n    \"fa-user\": a1\n  };\n};\nfunction EquipeDetailComponent_div_0_div_133_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 86)(1, \"div\", 61)(2, \"div\", 62)(3, \"div\", 87);\n    i0.ɵɵelement(4, \"i\", 88);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\")(6, \"h6\", 89);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 90)(9, \"span\", 91);\n    i0.ɵɵelement(10, \"i\", 92);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 93);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(14, \"button\", 94);\n    i0.ɵɵlistener(\"click\", function EquipeDetailComponent_div_0_div_133_div_1_Template_button_click_14_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r10);\n      const membre_r8 = restoredCtx.$implicit;\n      const ctx_r9 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r9.removeMembreFromEquipe(membre_r8._id));\n    });\n    i0.ɵɵelement(15, \"i\", 95);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const membre_r8 = ctx.$implicit;\n    const ctx_r7 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(7, _c0, ctx_r7.getUserProfession(membre_r8.user) === \"etudiant\", ctx_r7.getUserProfession(membre_r8.user) === \"professeur\", !ctx_r7.getUserProfession(membre_r8.user)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(11, _c1, ctx_r7.getUserProfession(membre_r8.user) === \"etudiant\", ctx_r7.getUserProfession(membre_r8.user) === \"professeur\", !ctx_r7.getUserProfession(membre_r8.user)));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r7.getUserName(membre_r8.user), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(15, _c2, membre_r8.role === \"admin\", membre_r8.role === \"membre\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(18, _c3, membre_r8.role === \"admin\", membre_r8.role === \"membre\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", membre_r8.role === \"admin\" ? \"Administrateur\" : \"Membre\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r7.getUserProfession(membre_r8.user) === \"etudiant\" ? \"\\u00C9tudiant\" : ctx_r7.getUserProfession(membre_r8.user) === \"professeur\" ? \"Professeur\" : \"Utilisateur\", \" \");\n  }\n}\nfunction EquipeDetailComponent_div_0_div_133_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 84);\n    i0.ɵɵtemplate(1, EquipeDetailComponent_div_0_div_133_div_1_Template, 16, 21, \"div\", 85);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.teamMembers);\n  }\n}\nfunction EquipeDetailComponent_div_0_ng_template_134_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 96)(1, \"div\", 97);\n    i0.ɵɵelement(2, \"i\", 98);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h5\", 99);\n    i0.ɵɵtext(4, \" Aucun membre \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 100);\n    i0.ɵɵtext(6, \" Ajoutez des membres \\u00E0 l'\\u00E9quipe \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction EquipeDetailComponent_div_0_div_140_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 101)(1, \"div\", 62)(2, \"div\", 102);\n    i0.ɵɵelement(3, \"i\", 103);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 104);\n    i0.ɵɵtext(5, \" Aucun utilisateur disponible. Veuillez d'abord cr\\u00E9er des utilisateurs. \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction EquipeDetailComponent_div_0_div_141_option_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 122);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r15 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", user_r15._id || user_r15.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate4(\" \", user_r15.firstName || \"\", \" \", user_r15.lastName || user_r15.name || user_r15.id, \" \", user_r15.email ? \"- \" + user_r15.email : \"\", \" \", user_r15.profession ? \"(\" + (user_r15.profession === \"etudiant\" ? \"\\u00C9tudiant\" : \"Professeur\") + \")\" : \"\", \" \");\n  }\n}\nfunction EquipeDetailComponent_div_0_div_141_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 105)(1, \"div\")(2, \"label\", 106);\n    i0.ɵɵtext(3, \"Utilisateur\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"select\", 107, 108)(6, \"option\", 109);\n    i0.ɵɵtext(7, \" S\\u00E9lectionnez un utilisateur \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, EquipeDetailComponent_div_0_div_141_option_8_Template, 2, 5, \"option\", 110);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\")(10, \"label\", 106);\n    i0.ɵɵtext(11, \"R\\u00F4le dans l'\\u00E9quipe\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 111)(13, \"label\", 112);\n    i0.ɵɵelement(14, \"input\", 113, 114)(16, \"i\", 115);\n    i0.ɵɵelementStart(17, \"span\", 116);\n    i0.ɵɵtext(18, \"Membre\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"label\", 112);\n    i0.ɵɵelement(20, \"input\", 117, 118)(22, \"i\", 119);\n    i0.ɵɵelementStart(23, \"span\", 116);\n    i0.ɵɵtext(24, \"Admin\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(25, \"button\", 120);\n    i0.ɵɵlistener(\"click\", function EquipeDetailComponent_div_0_div_141_Template_button_click_25_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const _r11 = i0.ɵɵreference(5);\n      const _r13 = i0.ɵɵreference(15);\n      const ctx_r16 = i0.ɵɵnextContext(2);\n      ctx_r16.addMembre(_r11.value, _r13.checked ? \"membre\" : \"admin\");\n      return i0.ɵɵresetView(_r11.value = \"\");\n    });\n    i0.ɵɵelement(26, \"i\", 121);\n    i0.ɵɵtext(27, \" Ajouter \\u00E0 l'\\u00E9quipe \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const _r11 = i0.ɵɵreference(5);\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.availableUsers);\n    i0.ɵɵadvance(17);\n    i0.ɵɵproperty(\"disabled\", !_r11.value);\n  }\n}\nfunction EquipeDetailComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3);\n    i0.ɵɵelement(2, \"div\", 4)(3, \"div\", 5);\n    i0.ɵɵelementStart(4, \"div\", 6)(5, \"div\", 7);\n    i0.ɵɵelement(6, \"div\", 8)(7, \"div\", 8)(8, \"div\", 8)(9, \"div\", 8)(10, \"div\", 8)(11, \"div\", 8)(12, \"div\", 8)(13, \"div\", 8)(14, \"div\", 8)(15, \"div\", 8)(16, \"div\", 8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"div\", 9)(18, \"div\", 10);\n    i0.ɵɵelement(19, \"div\", 11)(20, \"div\", 12);\n    i0.ɵɵelementStart(21, \"div\", 13)(22, \"div\", 14)(23, \"div\", 15)(24, \"h1\", 16);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"p\", 17);\n    i0.ɵɵtext(27, \" Gestion et collaboration d'\\u00E9quipe \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 18)(29, \"div\", 19);\n    i0.ɵɵelement(30, \"i\", 20);\n    i0.ɵɵelementStart(31, \"div\", 21);\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"div\", 22);\n    i0.ɵɵtext(34, \"Membres\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(35, \"div\", 19);\n    i0.ɵɵelement(36, \"i\", 23);\n    i0.ɵɵelementStart(37, \"div\", 21);\n    i0.ɵɵtext(38, \"0\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"div\", 22);\n    i0.ɵɵtext(40, \"T\\u00E2ches\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(41, \"div\", 19);\n    i0.ɵɵelement(42, \"i\", 24);\n    i0.ɵɵelementStart(43, \"div\", 21);\n    i0.ɵɵtext(44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"div\", 22);\n    i0.ɵɵtext(46, \"Cr\\u00E9\\u00E9e le\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(47, \"div\", 25)(48, \"h3\", 26);\n    i0.ɵɵelement(49, \"i\", 27);\n    i0.ɵɵtext(50, \" Actions rapides \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(51, \"div\", 28)(52, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function EquipeDetailComponent_div_0_Template_button_click_52_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.navigateToTasks());\n    });\n    i0.ɵɵelement(53, \"i\", 30);\n    i0.ɵɵtext(54, \" G\\u00E9rer les t\\u00E2ches \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(55, \"button\", 31);\n    i0.ɵɵlistener(\"click\", function EquipeDetailComponent_div_0_Template_button_click_55_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.navigateToEditEquipe());\n    });\n    i0.ɵɵelement(56, \"i\", 32);\n    i0.ɵɵtext(57, \" Modifier l'\\u00E9quipe \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(58, \"div\", 33)(59, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function EquipeDetailComponent_div_0_Template_button_click_59_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r21 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r21.navigateToEquipeList());\n    });\n    i0.ɵɵelement(60, \"i\", 35);\n    i0.ɵɵtext(61, \" Retour \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(62, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function EquipeDetailComponent_div_0_Template_button_click_62_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r22 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r22.deleteEquipe());\n    });\n    i0.ɵɵelement(63, \"i\", 37);\n    i0.ɵɵelementEnd()()()()()()();\n    i0.ɵɵelementStart(64, \"div\", 10);\n    i0.ɵɵelement(65, \"div\", 11)(66, \"div\", 12);\n    i0.ɵɵelementStart(67, \"div\", 13)(68, \"div\", 38)(69, \"div\", 39)(70, \"div\", 40);\n    i0.ɵɵelement(71, \"i\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(72, \"h3\", 42);\n    i0.ɵɵtext(73, \"\\u00C0 propos\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(74, \"p\", 43);\n    i0.ɵɵtext(75, \" D\\u00E9tails et informations \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(76, \"div\", 44)(77, \"div\", 45)(78, \"h4\", 46);\n    i0.ɵɵtext(79, \" Description \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(80, \"span\", 47);\n    i0.ɵɵelement(81, \"i\", 48);\n    i0.ɵɵtext(82);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(83, \"div\", 49)(84, \"p\", 50);\n    i0.ɵɵtext(85);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(86, \"div\", 51)(87, \"span\", 47);\n    i0.ɵɵelement(88, \"i\", 52);\n    i0.ɵɵtext(89);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(90, \"span\", 53);\n    i0.ɵɵelement(91, \"i\", 54);\n    i0.ɵɵtext(92);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(93, \"span\", 55);\n    i0.ɵɵelement(94, \"i\", 56);\n    i0.ɵɵtext(95, \" Gestion de projet \");\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(96, \"div\", 10);\n    i0.ɵɵelement(97, \"div\", 57)(98, \"div\", 58);\n    i0.ɵɵelementStart(99, \"div\", 59)(100, \"div\", 60)(101, \"div\", 61)(102, \"div\", 62)(103, \"div\", 63);\n    i0.ɵɵelement(104, \"i\", 64);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(105, \"div\")(106, \"h3\", 65);\n    i0.ɵɵtext(107, \" Assistant IA Gemini \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(108, \"p\", 66);\n    i0.ɵɵtext(109, \" G\\u00E9n\\u00E9ration de t\\u00E2ches intelligente \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(110, \"span\", 67);\n    i0.ɵɵelement(111, \"i\", 68);\n    i0.ɵɵtext(112, \" IA \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(113, \"div\", 69);\n    i0.ɵɵelement(114, \"app-ai-chat\", 70);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(115, \"div\", 10);\n    i0.ɵɵelement(116, \"div\", 71)(117, \"div\", 72);\n    i0.ɵɵelementStart(118, \"div\", 73)(119, \"div\", 74)(120, \"div\", 61)(121, \"div\", 62)(122, \"div\", 63);\n    i0.ɵɵelement(123, \"i\", 75);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(124, \"div\")(125, \"h3\", 65);\n    i0.ɵɵtext(126, \" Membres de l'\\u00E9quipe \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(127, \"p\", 66);\n    i0.ɵɵtext(128, \" G\\u00E9rez les membres et leurs r\\u00F4les \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(129, \"span\", 67);\n    i0.ɵɵtext(130);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(131, \"div\", 14)(132, \"div\", 76);\n    i0.ɵɵtemplate(133, EquipeDetailComponent_div_0_div_133_Template, 2, 1, \"div\", 77);\n    i0.ɵɵtemplate(134, EquipeDetailComponent_div_0_ng_template_134_Template, 7, 0, \"ng-template\", null, 78, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(136, \"div\", 79)(137, \"h5\", 80);\n    i0.ɵɵelement(138, \"i\", 81);\n    i0.ɵɵtext(139, \" Ajouter un membre \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(140, EquipeDetailComponent_div_0_div_140_Template, 6, 0, \"div\", 82);\n    i0.ɵɵtemplate(141, EquipeDetailComponent_div_0_div_141_Template, 28, 2, \"div\", 83);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const _r3 = i0.ɵɵreference(135);\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(25);\n    i0.ɵɵtextInterpolate(ctx_r0.equipe.name);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r0.equipe.members == null ? null : ctx_r0.equipe.members.length) || 0, \" \");\n    i0.ɵɵadvance(12);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.formatDate(ctx_r0.equipe.createdAt), \" \");\n    i0.ɵɵadvance(38);\n    i0.ɵɵtextInterpolate1(\" Admin: \", ctx_r0.equipe.admin ? ctx_r0.getUserName(ctx_r0.equipe.admin) || ctx_r0.equipe.admin : \"Non d\\u00E9fini\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.equipe.description || \"Aucune description disponible pour cette \\u00E9quipe.\", \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r0.equipe.members == null ? null : ctx_r0.equipe.members.length) || 0, \" membres \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" Cr\\u00E9\\u00E9e le \", ctx_r0.formatDate(ctx_r0.equipe.createdAt), \" \");\n    i0.ɵɵadvance(22);\n    i0.ɵɵproperty(\"team\", ctx_r0.equipe);\n    i0.ɵɵadvance(16);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r0.teamMembers == null ? null : ctx_r0.teamMembers.length) || 0, \" membres \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.teamMembers && ctx_r0.teamMembers.length > 0)(\"ngIfElse\", _r3);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r0.availableUsers == null ? null : ctx_r0.availableUsers.length) === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.availableUsers && ctx_r0.availableUsers.length > 0);\n  }\n}\nfunction EquipeDetailComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 123)(1, \"div\", 124)(2, \"div\", 125);\n    i0.ɵɵelement(3, \"i\", 126);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\", 127);\n    i0.ɵɵtext(5, \" \\u00C9quipe non trouv\\u00E9e \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 128);\n    i0.ɵɵtext(7, \" L'\\u00E9quipe est en cours de chargement ou n'existe pas \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 129);\n    i0.ɵɵlistener(\"click\", function EquipeDetailComponent_div_1_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r23 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r23.navigateToEquipeList());\n    });\n    i0.ɵɵelement(9, \"i\", 35);\n    i0.ɵɵtext(10, \" Retour \\u00E0 la liste des \\u00E9quipes \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nexport class EquipeDetailComponent {\n  constructor(equipeService, userService,\n  // TODO: Will be used when implementing real user API calls\n  route, router) {\n    this.equipeService = equipeService;\n    this.userService = userService;\n    this.route = route;\n    this.router = router;\n    this.equipe = null;\n    this.loading = false;\n    this.error = null;\n    this.equipeId = null;\n    this.newMembre = {\n      id: '',\n      role: 'membre'\n    };\n    this.availableUsers = [];\n    this.memberNames = {}; // Map pour stocker les noms des membres\n    this.teamMembers = []; // Liste des membres de l'équipe avec leurs détails\n  }\n\n  ngOnInit() {\n    this.equipeId = this.route.snapshot.paramMap.get('id');\n    // Charger tous les utilisateurs disponibles\n    this.loadUsers();\n    if (this.equipeId) {\n      this.loadEquipe(this.equipeId);\n    } else {\n      this.error = \"ID d'équipe non spécifié\";\n    }\n  }\n  // Méthode pour charger tous les utilisateurs\n  loadUsers() {\n    // TODO: Implémenter l'API pour récupérer les utilisateurs\n    // Pour l'instant, utiliser des données mockées\n    const mockUsers = [{\n      _id: 'user1',\n      username: 'john_doe',\n      email: '<EMAIL>',\n      role: 'admin',\n      isActive: true\n    }, {\n      _id: 'user2',\n      username: 'jane_smith',\n      email: '<EMAIL>',\n      role: 'student',\n      isActive: true\n    }, {\n      _id: 'user3',\n      username: 'bob_wilson',\n      email: '<EMAIL>',\n      role: 'teacher',\n      isActive: true\n    }];\n    // Simuler un délai d'API\n    setTimeout(() => {\n      // Stocker tous les utilisateurs pour la recherche de noms\n      const allUsers = [...mockUsers];\n      console.log('Tous les utilisateurs chargés (mock):', allUsers);\n      // Filtrer les utilisateurs disponibles (non membres de l'équipe)\n      if (this.teamMembers && this.teamMembers.length > 0) {\n        const memberUserIds = this.teamMembers.map(m => m.user);\n        this.availableUsers = mockUsers.filter(user => !memberUserIds.includes(user._id || user.id || ''));\n      } else {\n        this.availableUsers = mockUsers;\n      }\n      console.log('Utilisateurs disponibles:', this.availableUsers);\n      // Si l'équipe est déjà chargée, mettre à jour les noms des membres\n      if (this.equipe && this.equipe.members) {\n        this.updateMemberNames();\n      }\n    }, 500);\n  }\n  // Méthode pour mettre à jour les noms des membres\n  updateMemberNames() {\n    if (!this.equipe || !this.equipe.members) return;\n    this.equipe.members.forEach(membreId => {\n      const user = this.availableUsers.find(u => u._id === membreId || u.id === membreId);\n      if (user && user.name) {\n        this.memberNames[membreId] = user.name;\n      } else {\n        // Si l'utilisateur n'est pas trouvé dans la liste, essayer de le récupérer individuellement\n        // TODO: Implémenter getUser dans AuthuserService\n        // Pour l'instant, utiliser l'ID comme nom par défaut\n        this.memberNames[membreId] = membreId;\n      }\n    });\n  }\n  // Méthode pour obtenir le nom d'un membre\n  getMembreName(membreId) {\n    return this.memberNames[membreId] || membreId;\n  }\n  // Méthode pour obtenir le nom d'un utilisateur à partir de son ID\n  getUserName(userId) {\n    if (!userId) {\n      return 'Non défini';\n    }\n    const user = this.availableUsers.find(u => u._id === userId || u.id === userId);\n    if (user) {\n      if (user.firstName && user.lastName) {\n        return `${user.firstName} ${user.lastName}`;\n      } else if (user.name) {\n        return user.name;\n      }\n    }\n    return userId;\n  }\n  // Méthode pour obtenir la profession d'un utilisateur à partir de son ID\n  getUserProfession(userId) {\n    if (!userId) {\n      return '';\n    }\n    const user = this.availableUsers.find(u => u._id === userId || u.id === userId);\n    if (user) {\n      return user.profession || user.role || '';\n    }\n    return '';\n  }\n  loadEquipe(id) {\n    this.loading = true;\n    this.error = null;\n    this.equipeService.getEquipe(id).subscribe({\n      next: data => {\n        console.log(\"Détails de l'équipe chargés:\", data);\n        this.equipe = data;\n        // Charger les détails des membres de l'équipe\n        this.loadTeamMembers(id);\n        // Mettre à jour les noms des membres\n        if (this.equipe && this.equipe.members && this.equipe.members.length > 0) {\n          this.updateMemberNames();\n        }\n        this.loading = false;\n      },\n      error: error => {\n        console.error(\"Erreur lors du chargement des détails de l'équipe:\", error);\n        this.error = \"Impossible de charger les détails de l'équipe. Veuillez réessayer plus tard.\";\n        this.loading = false;\n      }\n    });\n  }\n  // Méthode pour charger les détails des membres de l'équipe\n  loadTeamMembers(teamId) {\n    this.equipeService.getTeamMembers(teamId).subscribe({\n      next: members => {\n        console.log('Détails des membres chargés:', members);\n        this.teamMembers = members;\n      },\n      error: error => {\n        console.error('Erreur lors du chargement des détails des membres:', error);\n      }\n    });\n  }\n  navigateToEditEquipe() {\n    if (this.equipeId) {\n      this.router.navigate(['/equipes/modifier', this.equipeId]);\n    }\n  }\n  navigateToEquipeList() {\n    this.router.navigate(['/equipes/liste']);\n  }\n  navigateToTasks() {\n    if (this.equipeId) {\n      this.router.navigate(['/equipes/tasks', this.equipeId]);\n    }\n  }\n  // Méthode pour formater les dates\n  formatDate(date) {\n    if (!date) {\n      return 'N/A';\n    }\n    try {\n      let dateObj;\n      if (typeof date === 'string') {\n        dateObj = new Date(date);\n      } else {\n        dateObj = date;\n      }\n      if (isNaN(dateObj.getTime())) {\n        return 'Date invalide';\n      }\n      // Format: JJ/MM/AAAA\n      return dateObj.toLocaleDateString('fr-FR', {\n        day: '2-digit',\n        month: '2-digit',\n        year: 'numeric'\n      });\n    } catch (error) {\n      console.error('Erreur lors du formatage de la date:', error);\n      return 'Erreur de date';\n    }\n  }\n  // Méthode pour ajouter un membre à l'équipe\n  addMembre(userId, role) {\n    console.log(`Ajout de l'utilisateur ${userId} avec le rôle ${role}`);\n    if (!this.equipeId || !userId) {\n      console.error(\"ID d'équipe ou ID d'utilisateur manquant\");\n      this.error = \"ID d'équipe ou ID d'utilisateur manquant\";\n      return;\n    }\n    // Vérifier si l'utilisateur est déjà membre de l'équipe\n    const isAlreadyMember = this.teamMembers.some(m => m.user === userId);\n    if (isAlreadyMember) {\n      this.error = \"Cet utilisateur est déjà membre de l'équipe\";\n      alert(\"Cet utilisateur est déjà membre de l'équipe\");\n      return;\n    }\n    // Créer l'objet membre avec le rôle spécifié\n    const membre = {\n      id: userId,\n      role: role || 'membre'\n    };\n    // Récupérer les informations de l'utilisateur pour afficher un message plus informatif\n    const userName = this.getUserName(userId);\n    const roleName = role === 'admin' ? 'administrateur' : 'membre';\n    this.equipeService.addMembreToEquipe(this.equipeId, membre).subscribe({\n      next: response => {\n        console.log(`Utilisateur \"${userName}\" ajouté comme ${roleName} avec succès:`, response);\n        // Afficher un message de succès\n        alert(`Utilisateur \"${userName}\" ajouté comme ${roleName} avec succès`);\n        // Recharger les membres de l'équipe\n        this.loadTeamMembers(this.equipeId);\n        // Recharger l'équipe pour mettre à jour la liste des membres\n        this.loadEquipe(this.equipeId);\n        // Mettre à jour la liste des utilisateurs disponibles\n        this.updateAvailableUsers();\n      },\n      error: error => {\n        console.error(\"Erreur lors de l'ajout de l'utilisateur comme membre:\", error);\n        this.error = `Impossible d'ajouter l'utilisateur \"${userName}\" comme ${roleName}. Veuillez réessayer plus tard.`;\n        alert(this.error);\n      }\n    });\n  }\n  // Méthode pour mettre à jour la liste des utilisateurs disponibles\n  updateAvailableUsers() {\n    // TODO: Implémenter l'API pour récupérer les utilisateurs\n    // Pour l'instant, utiliser les données mockées de loadUsers()\n    this.loadUsers();\n  }\n  // Ancienne méthode maintenue pour compatibilité\n  addMembreToEquipe() {\n    if (!this.equipeId || !this.newMembre.id) {\n      console.error(\"ID d'équipe ou ID de membre manquant\");\n      return;\n    }\n    this.addMembre(this.newMembre.id, this.newMembre.role || 'membre');\n  }\n  removeMembreFromEquipe(membreId) {\n    console.log('Méthode removeMembreFromEquipe appelée avec ID:', membreId);\n    if (!this.equipeId) {\n      console.error(\"ID d'équipe manquant\");\n      this.error = \"ID d'équipe manquant. Impossible de retirer le membre.\";\n      return;\n    }\n    // Dans notre implémentation actuelle, membre._id est l'ID de l'utilisateur\n    const userId = membreId;\n    // Récupérer le nom de l'utilisateur pour un message plus informatif\n    const userName = this.getUserName(userId);\n    console.log(`Tentative de retrait de l'utilisateur ${userId} (${userName}) de l'équipe ${this.equipeId}`);\n    if (confirm(`Êtes-vous sûr de vouloir retirer l'utilisateur \"${userName}\" de l'équipe?`)) {\n      console.log('Confirmation acceptée, suppression en cours...');\n      this.loading = true;\n      this.error = null;\n      this.equipeService.removeMembreFromEquipe(this.equipeId, userId).subscribe({\n        next: response => {\n          console.log(`Utilisateur \"${userName}\" retiré avec succès de l'équipe:`, response);\n          this.loading = false;\n          // Afficher un message de succès\n          alert(`Utilisateur \"${userName}\" retiré avec succès de l'équipe`);\n          // Recharger les membres de l'équipe\n          this.loadTeamMembers(this.equipeId);\n          // Recharger l'équipe pour mettre à jour la liste des membres\n          this.loadEquipe(this.equipeId);\n          // Mettre à jour la liste des utilisateurs disponibles\n          this.updateAvailableUsers();\n        },\n        error: error => {\n          console.error(`Erreur lors du retrait de l'utilisateur \"${userName}\":`, error);\n          this.loading = false;\n          this.error = `Impossible de retirer l'utilisateur \"${userName}\" de l'équipe: ${error.message || 'Erreur inconnue'}`;\n        }\n      });\n    } else {\n      console.log(\"Suppression annulée par l'utilisateur\");\n    }\n  }\n  deleteEquipe() {\n    console.log('Méthode deleteEquipe appelée');\n    if (!this.equipeId) {\n      console.error(\"ID d'équipe manquant\");\n      this.error = \"ID d'équipe manquant. Impossible de supprimer l'équipe.\";\n      return;\n    }\n    console.log(\"ID de l'équipe à supprimer:\", this.equipeId);\n    if (confirm(`Êtes-vous sûr de vouloir supprimer l'équipe \"${this.equipe?.name}\"? Cette action est irréversible.`)) {\n      console.log('Confirmation acceptée, suppression en cours...');\n      this.loading = true;\n      this.error = null;\n      this.equipeService.deleteEquipe(this.equipeId).subscribe({\n        next: () => {\n          console.log('Équipe supprimée avec succès');\n          this.loading = false;\n          alert('Équipe supprimée avec succès');\n          this.router.navigate(['/equipes/liste']);\n        },\n        error: error => {\n          console.error(\"Erreur lors de la suppression de l'équipe:\", error);\n          this.loading = false;\n          this.error = `Impossible de supprimer l'équipe: ${error.message || 'Erreur inconnue'}`;\n          alert(`Erreur lors de la suppression: ${this.error}`);\n        }\n      });\n    } else {\n      console.log(\"Suppression annulée par l'utilisateur\");\n    }\n  }\n  static {\n    this.ɵfac = function EquipeDetailComponent_Factory(t) {\n      return new (t || EquipeDetailComponent)(i0.ɵɵdirectiveInject(i1.EquipeService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: EquipeDetailComponent,\n      selectors: [[\"app-equipe-detail\"]],\n      decls: 2,\n      vars: 2,\n      consts: [[\"class\", \"min-h-screen bg-[#f0f4f8] dark:bg-[#0a0a0a] relative overflow-hidden\", 4, \"ngIf\"], [\"class\", \"min-h-screen bg-[#f0f4f8] dark:bg-[#0a0a0a] flex items-center justify-center\", 4, \"ngIf\"], [1, \"min-h-screen\", \"bg-[#f0f4f8]\", \"dark:bg-[#0a0a0a]\", \"relative\", \"overflow-hidden\"], [1, \"absolute\", \"inset-0\", \"overflow-hidden\", \"pointer-events-none\"], [1, \"absolute\", \"top-[15%]\", \"left-[10%]\", \"w-64\", \"h-64\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#dac4ea]/5\", \"to-transparent\", \"dark:from-[#00f7ff]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"bottom-[20%]\", \"right-[10%]\", \"w-80\", \"h-80\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#dac4ea]/5\", \"to-transparent\", \"dark:from-[#00f7ff]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"inset-0\", \"opacity-5\", \"dark:opacity-[0.03]\"], [1, \"h-full\", \"grid\", \"grid-cols-12\"], [1, \"border-r\", \"border-[#dac4ea]\", \"dark:border-[#00f7ff]\"], [1, \"max-w-7xl\", \"mx-auto\", \"p-6\", \"relative\", \"z-10\"], [1, \"mb-8\", \"relative\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#dac4ea]\", \"to-[#8b5a9f]\", \"dark:from-[#00f7ff]\", \"dark:to-[#dac4ea]\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#dac4ea]\", \"to-[#8b5a9f]\", \"dark:from-[#00f7ff]\", \"dark:to-[#dac4ea]\", \"blur-md\"], [1, \"bg-white\", \"dark:bg-[#1a1a1a]\", \"rounded-xl\", \"shadow-lg\", \"dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)]\", \"overflow-hidden\", \"border\", \"border-[#dac4ea]/20\", \"dark:border-[#00f7ff]/20\"], [1, \"grid\", \"grid-cols-1\", \"lg:grid-cols-3\"], [1, \"lg:col-span-2\", \"bg-gradient-to-r\", \"from-[#dac4ea]\", \"to-[#8b5a9f]\", \"dark:from-[#00f7ff]\", \"dark:to-[#dac4ea]\", \"p-8\", \"text-white\"], [1, \"text-4xl\", \"font-bold\", \"mb-2\"], [1, \"text-white/80\", \"text-lg\", \"mb-6\"], [1, \"grid\", \"grid-cols-3\", \"gap-4\"], [1, \"bg-white/20\", \"backdrop-blur-sm\", \"rounded-lg\", \"p-4\", \"text-center\"], [1, \"fas\", \"fa-users\", \"text-2xl\", \"mb-2\", \"block\"], [1, \"text-xl\", \"font-bold\"], [1, \"text-sm\", \"text-white/80\"], [1, \"fas\", \"fa-tasks\", \"text-2xl\", \"mb-2\", \"block\"], [1, \"fas\", \"fa-calendar-check\", \"text-2xl\", \"mb-2\", \"block\"], [1, \"p-8\", \"bg-white\", \"dark:bg-[#1a1a1a]\"], [1, \"text-lg\", \"font-bold\", \"text-[#dac4ea]\", \"dark:text-[#00f7ff]\", \"mb-6\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-bolt\", \"mr-2\"], [1, \"space-y-3\"], [1, \"w-full\", \"bg-gradient-to-r\", \"from-[#dac4ea]\", \"to-[#8b5a9f]\", \"dark:from-[#00f7ff]\", \"dark:to-[#dac4ea]\", \"text-white\", \"px-4\", \"py-3\", \"rounded-lg\", \"font-medium\", \"transition-all\", \"duration-300\", \"hover:scale-105\", \"shadow-lg\", 3, \"click\"], [1, \"fas\", \"fa-tasks\", \"mr-2\"], [1, \"w-full\", \"bg-[#dac4ea]/20\", \"dark:bg-[#00f7ff]/20\", \"text-[#dac4ea]\", \"dark:text-[#00f7ff]\", \"px-4\", \"py-3\", \"rounded-lg\", \"font-medium\", \"transition-all\", \"duration-300\", \"hover:scale-105\", 3, \"click\"], [1, \"fas\", \"fa-edit\", \"mr-2\"], [1, \"flex\", \"space-x-2\"], [1, \"flex-1\", \"bg-[#6d6870]/20\", \"dark:bg-[#a0a0a0]/20\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"px-4\", \"py-3\", \"rounded-lg\", \"font-medium\", \"transition-all\", \"duration-300\", \"hover:scale-105\", 3, \"click\"], [1, \"fas\", \"fa-arrow-left\", \"mr-2\"], [1, \"bg-[#ff6b69]/20\", \"dark:bg-[#ff3b30]/20\", \"text-[#ff6b69]\", \"dark:text-[#ff3b30]\", \"px-4\", \"py-3\", \"rounded-lg\", \"font-medium\", \"transition-all\", \"duration-300\", \"hover:scale-105\", 3, \"click\"], [1, \"fas\", \"fa-trash\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-4\"], [1, \"bg-gradient-to-br\", \"from-[#dac4ea]\", \"to-[#8b5a9f]\", \"dark:from-[#00f7ff]\", \"dark:to-[#dac4ea]\", \"p-6\", \"flex\", \"flex-col\", \"items-center\", \"justify-center\", \"text-white\"], [1, \"w-16\", \"h-16\", \"bg-white/20\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\", \"mb-4\"], [1, \"fas\", \"fa-info-circle\", \"text-2xl\"], [1, \"text-lg\", \"font-bold\", \"mb-1\"], [1, \"text-white/80\", \"text-sm\", \"text-center\"], [1, \"md:col-span-3\", \"p-6\"], [1, \"flex\", \"justify-between\", \"items-center\", \"mb-4\"], [1, \"text-lg\", \"font-bold\", \"text-[#dac4ea]\", \"dark:text-[#00f7ff]\"], [1, \"bg-[#dac4ea]/10\", \"dark:bg-[#00f7ff]/10\", \"text-[#dac4ea]\", \"dark:text-[#00f7ff]\", \"px-3\", \"py-1\", \"rounded-full\", \"text-sm\", \"font-medium\"], [1, \"fas\", \"fa-user-shield\", \"mr-1\"], [1, \"bg-[#f0f4f8]\", \"dark:bg-[#0a0a0a]\", \"border-l-4\", \"border-[#dac4ea]\", \"dark:border-[#00f7ff]\", \"rounded-lg\", \"p-4\", \"mb-4\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"leading-relaxed\"], [1, \"flex\", \"flex-wrap\", \"gap-2\"], [1, \"fas\", \"fa-users\", \"mr-1\"], [1, \"bg-[#00ff9d]/10\", \"text-[#00ff9d]\", \"px-3\", \"py-1\", \"rounded-full\", \"text-sm\", \"font-medium\"], [1, \"fas\", \"fa-calendar-check\", \"mr-1\"], [1, \"bg-[#4f5fad]/10\", \"text-[#4f5fad]\", \"px-3\", \"py-1\", \"rounded-full\", \"text-sm\", \"font-medium\"], [1, \"fas\", \"fa-project-diagram\", \"mr-1\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#8b5a9f]\", \"to-[#4a00e0]\", \"dark:from-[#00f7ff]\", \"dark:to-[#8b5a9f]\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#8b5a9f]\", \"to-[#4a00e0]\", \"dark:from-[#00f7ff]\", \"dark:to-[#8b5a9f]\", \"blur-md\"], [1, \"bg-white\", \"dark:bg-[#1a1a1a]\", \"rounded-xl\", \"shadow-lg\", \"dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)]\", \"overflow-hidden\", \"border\", \"border-[#8b5a9f]/20\", \"dark:border-[#00f7ff]/20\"], [1, \"bg-gradient-to-r\", \"from-[#8b5a9f]\", \"to-[#4a00e0]\", \"dark:from-[#00f7ff]\", \"dark:to-[#8b5a9f]\", \"p-6\"], [1, \"flex\", \"items-center\", \"justify-between\"], [1, \"flex\", \"items-center\"], [1, \"w-12\", \"h-12\", \"bg-white/20\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\", \"mr-4\"], [1, \"fas\", \"fa-robot\", \"text-white\", \"text-xl\"], [1, \"text-xl\", \"font-bold\", \"text-white\"], [1, \"text-white/80\", \"text-sm\"], [1, \"bg-white/20\", \"text-white\", \"px-3\", \"py-1\", \"rounded-full\", \"text-sm\", \"font-medium\"], [1, \"fas\", \"fa-magic\", \"mr-1\"], [1, \"p-0\"], [3, \"team\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#00ff9d]\", \"to-[#38ef7d]\", \"dark:from-[#00f7ff]\", \"dark:to-[#00ff9d]\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#00ff9d]\", \"to-[#38ef7d]\", \"dark:from-[#00f7ff]\", \"dark:to-[#00ff9d]\", \"blur-md\"], [1, \"bg-white\", \"dark:bg-[#1a1a1a]\", \"rounded-xl\", \"shadow-lg\", \"dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)]\", \"overflow-hidden\", \"border\", \"border-[#00ff9d]/20\", \"dark:border-[#00f7ff]/20\"], [1, \"bg-gradient-to-r\", \"from-[#00ff9d]\", \"to-[#38ef7d]\", \"dark:from-[#00f7ff]\", \"dark:to-[#00ff9d]\", \"p-6\"], [1, \"fas\", \"fa-users\", \"text-white\", \"text-xl\"], [1, \"lg:col-span-2\", \"p-6\"], [\"class\", \"space-y-4 max-h-96 overflow-y-auto\", 4, \"ngIf\", \"ngIfElse\"], [\"noMembers\", \"\"], [1, \"bg-[#f0f4f8]\", \"dark:bg-[#0a0a0a]\", \"p-6\"], [1, \"text-lg\", \"font-bold\", \"text-[#00ff9d]\", \"dark:text-[#00f7ff]\", \"mb-4\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-user-plus\", \"mr-2\"], [\"class\", \"bg-[#4f5fad]/10 border-l-4 border-[#4f5fad] rounded-lg p-4\", 4, \"ngIf\"], [\"class\", \"space-y-4\", 4, \"ngIf\"], [1, \"space-y-4\", \"max-h-96\", \"overflow-y-auto\"], [\"class\", \"bg-[#f0f4f8] dark:bg-[#0a0a0a] rounded-lg p-4 border border-[#00ff9d]/10 dark:border-[#00f7ff]/10 hover:shadow-lg transition-all duration-300\", 4, \"ngFor\", \"ngForOf\"], [1, \"bg-[#f0f4f8]\", \"dark:bg-[#0a0a0a]\", \"rounded-lg\", \"p-4\", \"border\", \"border-[#00ff9d]/10\", \"dark:border-[#00f7ff]/10\", \"hover:shadow-lg\", \"transition-all\", \"duration-300\"], [1, \"w-12\", \"h-12\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\", \"text-white\", \"mr-4\", 3, \"ngClass\"], [1, \"fas\", 3, \"ngClass\"], [1, \"font-bold\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mb-1\"], [1, \"flex\", \"items-center\", \"space-x-2\"], [1, \"px-2\", \"py-1\", \"rounded-full\", \"text-xs\", \"font-medium\", 3, \"ngClass\"], [1, \"fas\", \"mr-1\", 3, \"ngClass\"], [1, \"text-xs\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [\"title\", \"Retirer de l'\\u00E9quipe\", 1, \"p-2\", \"text-[#ff6b69]\", \"dark:text-[#ff3b30]\", \"hover:bg-[#ff6b69]/10\", \"dark:hover:bg-[#ff3b30]/10\", \"rounded-lg\", \"transition-all\", 3, \"click\"], [1, \"fas\", \"fa-user-minus\"], [1, \"text-center\", \"py-12\"], [1, \"w-16\", \"h-16\", \"mx-auto\", \"mb-4\", \"text-[#00ff9d]\", \"dark:text-[#00f7ff]\", \"opacity-50\"], [1, \"fas\", \"fa-users\", \"text-4xl\"], [1, \"text-lg\", \"font-semibold\", \"text-[#00ff9d]\", \"dark:text-[#00f7ff]\", \"mb-2\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"text-sm\"], [1, \"bg-[#4f5fad]/10\", \"border-l-4\", \"border-[#4f5fad]\", \"rounded-lg\", \"p-4\"], [1, \"text-[#4f5fad]\", \"mr-3\", \"text-lg\"], [1, \"fas\", \"fa-info-circle\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [1, \"space-y-4\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-[#00ff9d]\", \"dark:text-[#00f7ff]\", \"mb-2\"], [1, \"w-full\", \"px-3\", \"py-2\", \"bg-white\", \"dark:bg-[#1a1a1a]\", \"border\", \"border-[#00ff9d]/20\", \"dark:border-[#00f7ff]/20\", \"rounded-lg\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-[#00ff9d]\", \"dark:focus:ring-[#00f7ff]\", \"focus:border-transparent\"], [\"userSelect\", \"\"], [\"value\", \"\", \"selected\", \"\", \"disabled\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"grid\", \"grid-cols-2\", \"gap-2\"], [1, \"flex\", \"flex-col\", \"items-center\", \"p-3\", \"border\", \"border-[#00ff9d]/20\", \"dark:border-[#00f7ff]/20\", \"rounded-lg\", \"cursor-pointer\", \"hover:bg-[#00ff9d]/5\", \"dark:hover:bg-[#00f7ff]/5\", \"transition-all\"], [\"type\", \"radio\", \"name\", \"roleRadio\", \"value\", \"membre\", \"checked\", \"\", 1, \"sr-only\"], [\"roleMembre\", \"\"], [1, \"fas\", \"fa-user\", \"text-xl\", \"mb-1\", \"text-[#4f5fad]\"], [1, \"text-sm\", \"font-medium\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [\"type\", \"radio\", \"name\", \"roleRadio\", \"value\", \"admin\", 1, \"sr-only\"], [\"roleAdmin\", \"\"], [1, \"fas\", \"fa-user-shield\", \"text-xl\", \"mb-1\", \"text-[#00ff9d]\"], [\"type\", \"button\", 1, \"w-full\", \"bg-gradient-to-r\", \"from-[#00ff9d]\", \"to-[#38ef7d]\", \"dark:from-[#00f7ff]\", \"dark:to-[#00ff9d]\", \"text-white\", \"px-4\", \"py-3\", \"rounded-lg\", \"font-medium\", \"transition-all\", \"duration-300\", \"hover:scale-105\", \"shadow-lg\", \"disabled:opacity-50\", \"disabled:cursor-not-allowed\", \"disabled:hover:scale-100\", 3, \"disabled\", \"click\"], [1, \"fas\", \"fa-plus-circle\", \"mr-2\"], [3, \"value\"], [1, \"min-h-screen\", \"bg-[#f0f4f8]\", \"dark:bg-[#0a0a0a]\", \"flex\", \"items-center\", \"justify-center\"], [1, \"text-center\"], [1, \"w-20\", \"h-20\", \"mx-auto\", \"mb-6\", \"text-[#dac4ea]\", \"dark:text-[#00f7ff]\", \"opacity-70\"], [1, \"fas\", \"fa-exclamation-triangle\", \"text-5xl\"], [1, \"text-xl\", \"font-semibold\", \"text-[#dac4ea]\", \"dark:text-[#00f7ff]\", \"mb-2\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"text-sm\", \"mb-6\"], [1, \"bg-gradient-to-r\", \"from-[#dac4ea]\", \"to-[#8b5a9f]\", \"dark:from-[#00f7ff]\", \"dark:to-[#dac4ea]\", \"text-white\", \"px-6\", \"py-3\", \"rounded-xl\", \"font-medium\", \"transition-all\", \"duration-300\", \"hover:scale-105\", 3, \"click\"]],\n      template: function EquipeDetailComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, EquipeDetailComponent_div_0_Template, 142, 13, \"div\", 0);\n          i0.ɵɵtemplate(1, EquipeDetailComponent_div_1_Template, 11, 0, \"div\", 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.equipe);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.equipe);\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i5.NgSelectOption, i5.ɵNgSelectMultipleOption, i6.AiChatComponent],\n      styles: [\"\\n\\n.cursor-pointer[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n}\\n\\nsummary[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImVxdWlwZS1kZXRhaWwuY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQSwyQ0FBMkM7QUFDM0M7RUFDRSxlQUFlO0FBQ2pCOztBQUVBO0VBQ0UsMEJBQTBCO0FBQzVCIiwiZmlsZSI6ImVxdWlwZS1kZXRhaWwuY29tcG9uZW50LmNzcyIsInNvdXJjZXNDb250ZW50IjpbIi8qIFN0eWxlcyBwb3VyIGxlIGNvbXBvc2FudCBlcXVpcGUtZGV0YWlsICovXHJcbi5jdXJzb3ItcG9pbnRlciB7XHJcbiAgY3Vyc29yOiBwb2ludGVyO1xyXG59XHJcblxyXG5zdW1tYXJ5OmhvdmVyIHtcclxuICB0ZXh0LWRlY29yYXRpb246IHVuZGVybGluZTtcclxufSJdfQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvYWRtaW4vZXF1aXBlcy9lcXVpcGUtZGV0YWlsL2VxdWlwZS1kZXRhaWwuY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQSwyQ0FBMkM7QUFDM0M7RUFDRSxlQUFlO0FBQ2pCOztBQUVBO0VBQ0UsMEJBQTBCO0FBQzVCO0FBQ0Esd2dCQUF3Z0IiLCJzb3VyY2VzQ29udGVudCI6WyIvKiBTdHlsZXMgcG91ciBsZSBjb21wb3NhbnQgZXF1aXBlLWRldGFpbCAqL1xyXG4uY3Vyc29yLXBvaW50ZXIge1xyXG4gIGN1cnNvcjogcG9pbnRlcjtcclxufVxyXG5cclxuc3VtbWFyeTpob3ZlciB7XHJcbiAgdGV4dC1kZWNvcmF0aW9uOiB1bmRlcmxpbmU7XHJcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "EquipeDetailComponent_div_0_div_133_div_1_Template_button_click_14_listener", "restoredCtx", "ɵɵrestoreView", "_r10", "membre_r8", "$implicit", "ctx_r9", "ɵɵnextContext", "ɵɵresetView", "removeMembreFromEquipe", "_id", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction3", "_c0", "ctx_r7", "getUserProfession", "user", "_c1", "ɵɵtextInterpolate1", "getUserName", "ɵɵpureFunction2", "_c2", "role", "_c3", "ɵɵtemplate", "EquipeDetailComponent_div_0_div_133_div_1_Template", "ctx_r2", "teamMembers", "user_r15", "id", "ɵɵtextInterpolate4", "firstName", "lastName", "name", "email", "profession", "EquipeDetailComponent_div_0_div_141_option_8_Template", "EquipeDetailComponent_div_0_div_141_Template_button_click_25_listener", "_r17", "_r11", "ɵɵreference", "_r13", "ctx_r16", "addMembre", "value", "checked", "ctx_r6", "availableUsers", "EquipeDetailComponent_div_0_Template_button_click_52_listener", "_r19", "ctx_r18", "navigateToTasks", "EquipeDetailComponent_div_0_Template_button_click_55_listener", "ctx_r20", "navigateToEditEquipe", "EquipeDetailComponent_div_0_Template_button_click_59_listener", "ctx_r21", "navigateToEquipeList", "EquipeDetailComponent_div_0_Template_button_click_62_listener", "ctx_r22", "deleteEquipe", "EquipeDetailComponent_div_0_div_133_Template", "EquipeDetailComponent_div_0_ng_template_134_Template", "ɵɵtemplateRefExtractor", "EquipeDetailComponent_div_0_div_140_Template", "EquipeDetailComponent_div_0_div_141_Template", "ɵɵtextInterpolate", "ctx_r0", "equipe", "members", "length", "formatDate", "createdAt", "admin", "description", "_r3", "EquipeDetailComponent_div_1_Template_button_click_8_listener", "_r24", "ctx_r23", "EquipeDetailComponent", "constructor", "equipeService", "userService", "route", "router", "loading", "error", "equipeId", "newMembre", "memberNames", "ngOnInit", "snapshot", "paramMap", "get", "loadUsers", "loadEquipe", "mockUsers", "username", "isActive", "setTimeout", "allUsers", "console", "log", "memberUserIds", "map", "m", "filter", "includes", "updateMemberNames", "for<PERSON>ach", "membreId", "find", "u", "getMembreName", "userId", "getEquipe", "subscribe", "next", "data", "loadTeamMembers", "teamId", "getTeamMembers", "navigate", "date", "date<PERSON><PERSON>j", "Date", "isNaN", "getTime", "toLocaleDateString", "day", "month", "year", "isAlreadyMember", "some", "alert", "membre", "userName", "<PERSON><PERSON><PERSON>", "addMembreToEquipe", "response", "updateAvailableUsers", "confirm", "message", "ɵɵdirectiveInject", "i1", "EquipeService", "i2", "AuthService", "i3", "ActivatedRoute", "Router", "selectors", "decls", "vars", "consts", "template", "EquipeDetailComponent_Template", "rf", "ctx", "EquipeDetailComponent_div_0_Template", "EquipeDetailComponent_div_1_Template"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\admin\\equipes\\equipe-detail\\equipe-detail.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\admin\\equipes\\equipe-detail\\equipe-detail.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { EquipeService } from 'src/app/services/equipe.service';\nimport { AuthService } from 'src/app/services/auth.service';\nimport { Equipe } from 'src/app/models/equipe.model';\nimport { Membre } from 'src/app/models/membre.model';\nimport { User } from 'src/app/models/user.model';\n@Component({\n  selector: 'app-equipe-detail',\n  templateUrl: './equipe-detail.component.html',\n  styleUrls: ['./equipe-detail.component.css'],\n})\nexport class EquipeDetailComponent implements OnInit {\n  equipe: Equipe | null = null;\n  loading = false;\n  error: string | null = null;\n  equipeId: string | null = null;\n  newMembre: any = { id: '', role: 'membre' };\n  availableUsers: User[] = [];\n  memberNames: { [key: string]: string } = {}; // Map pour stocker les noms des membres\n  teamMembers: any[] = []; // Liste des membres de l'équipe avec leurs détails\n\n  constructor(\n    private equipeService: EquipeService,\n    private userService: AuthService, // TODO: Will be used when implementing real user API calls\n    private route: ActivatedRoute,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    this.equipeId = this.route.snapshot.paramMap.get('id');\n\n    // Charger tous les utilisateurs disponibles\n    this.loadUsers();\n\n    if (this.equipeId) {\n      this.loadEquipe(this.equipeId);\n    } else {\n      this.error = \"ID d'équipe non spécifié\";\n    }\n  }\n\n  // Méthode pour charger tous les utilisateurs\n  loadUsers(): void {\n    // TODO: Implémenter l'API pour récupérer les utilisateurs\n    // Pour l'instant, utiliser des données mockées\n    const mockUsers: User[] = [\n      {\n        _id: 'user1',\n        username: 'john_doe',\n        email: '<EMAIL>',\n        role: 'admin',\n        isActive: true,\n      },\n      {\n        _id: 'user2',\n        username: 'jane_smith',\n        email: '<EMAIL>',\n        role: 'student',\n        isActive: true,\n      },\n      {\n        _id: 'user3',\n        username: 'bob_wilson',\n        email: '<EMAIL>',\n        role: 'teacher',\n        isActive: true,\n      },\n    ];\n\n    // Simuler un délai d'API\n    setTimeout(() => {\n      // Stocker tous les utilisateurs pour la recherche de noms\n      const allUsers = [...mockUsers];\n      console.log('Tous les utilisateurs chargés (mock):', allUsers);\n\n      // Filtrer les utilisateurs disponibles (non membres de l'équipe)\n      if (this.teamMembers && this.teamMembers.length > 0) {\n        const memberUserIds = this.teamMembers.map((m) => m.user);\n        this.availableUsers = mockUsers.filter(\n          (user) => !memberUserIds.includes(user._id || user.id || '')\n        );\n      } else {\n        this.availableUsers = mockUsers;\n      }\n\n      console.log('Utilisateurs disponibles:', this.availableUsers);\n\n      // Si l'équipe est déjà chargée, mettre à jour les noms des membres\n      if (this.equipe && this.equipe.members) {\n        this.updateMemberNames();\n      }\n    }, 500);\n  }\n\n  // Méthode pour mettre à jour les noms des membres\n  updateMemberNames(): void {\n    if (!this.equipe || !this.equipe.members) return;\n\n    this.equipe.members.forEach((membreId) => {\n      const user = this.availableUsers.find(\n        (u) => u._id === membreId || u.id === membreId\n      );\n      if (user && user.name) {\n        this.memberNames[membreId] = user.name;\n      } else {\n        // Si l'utilisateur n'est pas trouvé dans la liste, essayer de le récupérer individuellement\n        // TODO: Implémenter getUser dans AuthuserService\n        // Pour l'instant, utiliser l'ID comme nom par défaut\n        this.memberNames[membreId] = membreId;\n      }\n    });\n  }\n\n  // Méthode pour obtenir le nom d'un membre\n  getMembreName(membreId: string): string {\n    return this.memberNames[membreId] || membreId;\n  }\n\n  // Méthode pour obtenir le nom d'un utilisateur à partir de son ID\n  getUserName(userId: string | undefined): string {\n    if (!userId) {\n      return 'Non défini';\n    }\n\n    const user = this.availableUsers.find(\n      (u) => u._id === userId || u.id === userId\n    );\n    if (user) {\n      if (user.firstName && user.lastName) {\n        return `${user.firstName} ${user.lastName}`;\n      } else if (user.name) {\n        return user.name;\n      }\n    }\n    return userId;\n  }\n\n  // Méthode pour obtenir la profession d'un utilisateur à partir de son ID\n  getUserProfession(userId: string | undefined): string {\n    if (!userId) {\n      return '';\n    }\n\n    const user = this.availableUsers.find(\n      (u) => u._id === userId || u.id === userId\n    );\n    if (user) {\n      return user.profession || user.role || '';\n    }\n    return '';\n  }\n\n  loadEquipe(id: string): void {\n    this.loading = true;\n    this.error = null;\n\n    this.equipeService.getEquipe(id).subscribe({\n      next: (data) => {\n        console.log(\"Détails de l'équipe chargés:\", data);\n        this.equipe = data;\n\n        // Charger les détails des membres de l'équipe\n        this.loadTeamMembers(id);\n\n        // Mettre à jour les noms des membres\n        if (\n          this.equipe &&\n          this.equipe.members &&\n          this.equipe.members.length > 0\n        ) {\n          this.updateMemberNames();\n        }\n\n        this.loading = false;\n      },\n      error: (error) => {\n        console.error(\n          \"Erreur lors du chargement des détails de l'équipe:\",\n          error\n        );\n        this.error =\n          \"Impossible de charger les détails de l'équipe. Veuillez réessayer plus tard.\";\n        this.loading = false;\n      },\n    });\n  }\n\n  // Méthode pour charger les détails des membres de l'équipe\n  loadTeamMembers(teamId: string): void {\n    this.equipeService.getTeamMembers(teamId).subscribe({\n      next: (members) => {\n        console.log('Détails des membres chargés:', members);\n        this.teamMembers = members;\n      },\n      error: (error) => {\n        console.error(\n          'Erreur lors du chargement des détails des membres:',\n          error\n        );\n      },\n    });\n  }\n\n  navigateToEditEquipe(): void {\n    if (this.equipeId) {\n      this.router.navigate(['/equipes/modifier', this.equipeId]);\n    }\n  }\n\n  navigateToEquipeList(): void {\n    this.router.navigate(['/equipes/liste']);\n  }\n\n  navigateToTasks(): void {\n    if (this.equipeId) {\n      this.router.navigate(['/equipes/tasks', this.equipeId]);\n    }\n  }\n\n  // Méthode pour formater les dates\n  formatDate(date: Date | string | undefined): string {\n    if (!date) {\n      return 'N/A';\n    }\n\n    try {\n      let dateObj: Date;\n\n      if (typeof date === 'string') {\n        dateObj = new Date(date);\n      } else {\n        dateObj = date;\n      }\n\n      if (isNaN(dateObj.getTime())) {\n        return 'Date invalide';\n      }\n\n      // Format: JJ/MM/AAAA\n      return dateObj.toLocaleDateString('fr-FR', {\n        day: '2-digit',\n        month: '2-digit',\n        year: 'numeric',\n      });\n    } catch (error) {\n      console.error('Erreur lors du formatage de la date:', error);\n      return 'Erreur de date';\n    }\n  }\n\n  // Méthode pour ajouter un membre à l'équipe\n  addMembre(userId: string, role: string): void {\n    console.log(`Ajout de l'utilisateur ${userId} avec le rôle ${role}`);\n\n    if (!this.equipeId || !userId) {\n      console.error(\"ID d'équipe ou ID d'utilisateur manquant\");\n      this.error = \"ID d'équipe ou ID d'utilisateur manquant\";\n      return;\n    }\n\n    // Vérifier si l'utilisateur est déjà membre de l'équipe\n    const isAlreadyMember = this.teamMembers.some((m) => m.user === userId);\n    if (isAlreadyMember) {\n      this.error = \"Cet utilisateur est déjà membre de l'équipe\";\n      alert(\"Cet utilisateur est déjà membre de l'équipe\");\n      return;\n    }\n\n    // Créer l'objet membre avec le rôle spécifié\n    const membre: Membre = {\n      id: userId,\n      role: role || 'membre',\n    };\n\n    // Récupérer les informations de l'utilisateur pour afficher un message plus informatif\n    const userName = this.getUserName(userId);\n    const roleName = role === 'admin' ? 'administrateur' : 'membre';\n\n    this.equipeService.addMembreToEquipe(this.equipeId, membre).subscribe({\n      next: (response) => {\n        console.log(\n          `Utilisateur \"${userName}\" ajouté comme ${roleName} avec succès:`,\n          response\n        );\n\n        // Afficher un message de succès\n        alert(`Utilisateur \"${userName}\" ajouté comme ${roleName} avec succès`);\n\n        // Recharger les membres de l'équipe\n        this.loadTeamMembers(this.equipeId!);\n\n        // Recharger l'équipe pour mettre à jour la liste des membres\n        this.loadEquipe(this.equipeId!);\n\n        // Mettre à jour la liste des utilisateurs disponibles\n        this.updateAvailableUsers();\n      },\n      error: (error) => {\n        console.error(\n          \"Erreur lors de l'ajout de l'utilisateur comme membre:\",\n          error\n        );\n        this.error = `Impossible d'ajouter l'utilisateur \"${userName}\" comme ${roleName}. Veuillez réessayer plus tard.`;\n        alert(this.error);\n      },\n    });\n  }\n\n  // Méthode pour mettre à jour la liste des utilisateurs disponibles\n  updateAvailableUsers(): void {\n    // TODO: Implémenter l'API pour récupérer les utilisateurs\n    // Pour l'instant, utiliser les données mockées de loadUsers()\n    this.loadUsers();\n  }\n\n  // Ancienne méthode maintenue pour compatibilité\n  addMembreToEquipe(): void {\n    if (!this.equipeId || !this.newMembre.id) {\n      console.error(\"ID d'équipe ou ID de membre manquant\");\n      return;\n    }\n\n    this.addMembre(this.newMembre.id, this.newMembre.role || 'membre');\n  }\n\n  removeMembreFromEquipe(membreId: string): void {\n    console.log('Méthode removeMembreFromEquipe appelée avec ID:', membreId);\n\n    if (!this.equipeId) {\n      console.error(\"ID d'équipe manquant\");\n      this.error = \"ID d'équipe manquant. Impossible de retirer le membre.\";\n      return;\n    }\n\n    // Dans notre implémentation actuelle, membre._id est l'ID de l'utilisateur\n    const userId = membreId;\n\n    // Récupérer le nom de l'utilisateur pour un message plus informatif\n    const userName = this.getUserName(userId);\n\n    console.log(\n      `Tentative de retrait de l'utilisateur ${userId} (${userName}) de l'équipe ${this.equipeId}`\n    );\n\n    if (\n      confirm(\n        `Êtes-vous sûr de vouloir retirer l'utilisateur \"${userName}\" de l'équipe?`\n      )\n    ) {\n      console.log('Confirmation acceptée, suppression en cours...');\n\n      this.loading = true;\n      this.error = null;\n\n      this.equipeService\n        .removeMembreFromEquipe(this.equipeId, userId)\n        .subscribe({\n          next: (response) => {\n            console.log(\n              `Utilisateur \"${userName}\" retiré avec succès de l'équipe:`,\n              response\n            );\n            this.loading = false;\n\n            // Afficher un message de succès\n            alert(`Utilisateur \"${userName}\" retiré avec succès de l'équipe`);\n\n            // Recharger les membres de l'équipe\n            this.loadTeamMembers(this.equipeId!);\n\n            // Recharger l'équipe pour mettre à jour la liste des membres\n            this.loadEquipe(this.equipeId!);\n\n            // Mettre à jour la liste des utilisateurs disponibles\n            this.updateAvailableUsers();\n          },\n          error: (error) => {\n            console.error(\n              `Erreur lors du retrait de l'utilisateur \"${userName}\":`,\n              error\n            );\n            this.loading = false;\n            this.error = `Impossible de retirer l'utilisateur \"${userName}\" de l'équipe: ${\n              error.message || 'Erreur inconnue'\n            }`;\n          },\n        });\n    } else {\n      console.log(\"Suppression annulée par l'utilisateur\");\n    }\n  }\n\n  deleteEquipe(): void {\n    console.log('Méthode deleteEquipe appelée');\n\n    if (!this.equipeId) {\n      console.error(\"ID d'équipe manquant\");\n      this.error = \"ID d'équipe manquant. Impossible de supprimer l'équipe.\";\n      return;\n    }\n\n    console.log(\"ID de l'équipe à supprimer:\", this.equipeId);\n\n    if (\n      confirm(\n        `Êtes-vous sûr de vouloir supprimer l'équipe \"${this.equipe?.name}\"? Cette action est irréversible.`\n      )\n    ) {\n      console.log('Confirmation acceptée, suppression en cours...');\n\n      this.loading = true;\n      this.error = null;\n\n      this.equipeService.deleteEquipe(this.equipeId).subscribe({\n        next: () => {\n          console.log('Équipe supprimée avec succès');\n          this.loading = false;\n          alert('Équipe supprimée avec succès');\n          this.router.navigate(['/equipes/liste']);\n        },\n        error: (error) => {\n          console.error(\"Erreur lors de la suppression de l'équipe:\", error);\n          this.loading = false;\n          this.error = `Impossible de supprimer l'équipe: ${\n            error.message || 'Erreur inconnue'\n          }`;\n          alert(`Erreur lors de la suppression: ${this.error}`);\n        },\n      });\n    } else {\n      console.log(\"Suppression annulée par l'utilisateur\");\n    }\n  }\n}\n", "<div\n  class=\"min-h-screen bg-[#f0f4f8] dark:bg-[#0a0a0a] relative overflow-hidden\"\n  *ngIf=\"equipe\"\n>\n  <!-- Background decorative elements -->\n  <div class=\"absolute inset-0 overflow-hidden pointer-events-none\">\n    <div\n      class=\"absolute top-[15%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-br from-[#dac4ea]/5 to-transparent dark:from-[#00f7ff]/3 dark:to-transparent blur-3xl\"\n    ></div>\n    <div\n      class=\"absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#dac4ea]/5 to-transparent dark:from-[#00f7ff]/3 dark:to-transparent blur-3xl\"\n    ></div>\n\n    <!-- Grid pattern -->\n    <div class=\"absolute inset-0 opacity-5 dark:opacity-[0.03]\">\n      <div class=\"h-full grid grid-cols-12\">\n        <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n      </div>\n    </div>\n  </div>\n\n  <div class=\"max-w-7xl mx-auto p-6 relative z-10\">\n    <!-- Header Hero Section -->\n    <div class=\"mb-8 relative\">\n      <!-- Decorative top border -->\n      <div\n        class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea]\"\n      ></div>\n      <div\n        class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea] blur-md\"\n      ></div>\n\n      <div\n        class=\"bg-white dark:bg-[#1a1a1a] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] overflow-hidden border border-[#dac4ea]/20 dark:border-[#00f7ff]/20\"\n      >\n        <div class=\"grid grid-cols-1 lg:grid-cols-3\">\n          <!-- Team Info -->\n          <div\n            class=\"lg:col-span-2 bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea] p-8 text-white\"\n          >\n            <h1 class=\"text-4xl font-bold mb-2\">{{ equipe.name }}</h1>\n            <p class=\"text-white/80 text-lg mb-6\">\n              Gestion et collaboration d'équipe\n            </p>\n\n            <!-- Stats -->\n            <div class=\"grid grid-cols-3 gap-4\">\n              <div\n                class=\"bg-white/20 backdrop-blur-sm rounded-lg p-4 text-center\"\n              >\n                <i class=\"fas fa-users text-2xl mb-2 block\"></i>\n                <div class=\"text-xl font-bold\">\n                  {{ equipe.members?.length || 0 }}\n                </div>\n                <div class=\"text-sm text-white/80\">Membres</div>\n              </div>\n              <div\n                class=\"bg-white/20 backdrop-blur-sm rounded-lg p-4 text-center\"\n              >\n                <i class=\"fas fa-tasks text-2xl mb-2 block\"></i>\n                <div class=\"text-xl font-bold\">0</div>\n                <div class=\"text-sm text-white/80\">Tâches</div>\n              </div>\n              <div\n                class=\"bg-white/20 backdrop-blur-sm rounded-lg p-4 text-center\"\n              >\n                <i class=\"fas fa-calendar-check text-2xl mb-2 block\"></i>\n                <div class=\"text-xl font-bold\">\n                  {{ formatDate(equipe.createdAt) }}\n                </div>\n                <div class=\"text-sm text-white/80\">Créée le</div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Quick Actions -->\n          <div class=\"p-8 bg-white dark:bg-[#1a1a1a]\">\n            <h3\n              class=\"text-lg font-bold text-[#dac4ea] dark:text-[#00f7ff] mb-6 flex items-center\"\n            >\n              <i class=\"fas fa-bolt mr-2\"></i>\n              Actions rapides\n            </h3>\n            <div class=\"space-y-3\">\n              <button\n                (click)=\"navigateToTasks()\"\n                class=\"w-full bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea] text-white px-4 py-3 rounded-lg font-medium transition-all duration-300 hover:scale-105 shadow-lg\"\n              >\n                <i class=\"fas fa-tasks mr-2\"></i>\n                Gérer les tâches\n              </button>\n              <button\n                (click)=\"navigateToEditEquipe()\"\n                class=\"w-full bg-[#dac4ea]/20 dark:bg-[#00f7ff]/20 text-[#dac4ea] dark:text-[#00f7ff] px-4 py-3 rounded-lg font-medium transition-all duration-300 hover:scale-105\"\n              >\n                <i class=\"fas fa-edit mr-2\"></i>\n                Modifier l'équipe\n              </button>\n              <div class=\"flex space-x-2\">\n                <button\n                  (click)=\"navigateToEquipeList()\"\n                  class=\"flex-1 bg-[#6d6870]/20 dark:bg-[#a0a0a0]/20 text-[#6d6870] dark:text-[#a0a0a0] px-4 py-3 rounded-lg font-medium transition-all duration-300 hover:scale-105\"\n                >\n                  <i class=\"fas fa-arrow-left mr-2\"></i>\n                  Retour\n                </button>\n                <button\n                  (click)=\"deleteEquipe()\"\n                  class=\"bg-[#ff6b69]/20 dark:bg-[#ff3b30]/20 text-[#ff6b69] dark:text-[#ff3b30] px-4 py-3 rounded-lg font-medium transition-all duration-300 hover:scale-105\"\n                >\n                  <i class=\"fas fa-trash\"></i>\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Team Information -->\n    <div class=\"mb-8 relative\">\n      <!-- Decorative top border -->\n      <div\n        class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea]\"\n      ></div>\n      <div\n        class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea] blur-md\"\n      ></div>\n\n      <div\n        class=\"bg-white dark:bg-[#1a1a1a] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] overflow-hidden border border-[#dac4ea]/20 dark:border-[#00f7ff]/20\"\n      >\n        <div class=\"grid grid-cols-1 md:grid-cols-4\">\n          <!-- Icon Section -->\n          <div\n            class=\"bg-gradient-to-br from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea] p-6 flex flex-col items-center justify-center text-white\"\n          >\n            <div\n              class=\"w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mb-4\"\n            >\n              <i class=\"fas fa-info-circle text-2xl\"></i>\n            </div>\n            <h3 class=\"text-lg font-bold mb-1\">À propos</h3>\n            <p class=\"text-white/80 text-sm text-center\">\n              Détails et informations\n            </p>\n          </div>\n\n          <!-- Content Section -->\n          <div class=\"md:col-span-3 p-6\">\n            <div class=\"flex justify-between items-center mb-4\">\n              <h4 class=\"text-lg font-bold text-[#dac4ea] dark:text-[#00f7ff]\">\n                Description\n              </h4>\n              <span\n                class=\"bg-[#dac4ea]/10 dark:bg-[#00f7ff]/10 text-[#dac4ea] dark:text-[#00f7ff] px-3 py-1 rounded-full text-sm font-medium\"\n              >\n                <i class=\"fas fa-user-shield mr-1\"></i>\n                Admin:\n                {{\n                  equipe.admin\n                    ? getUserName(equipe.admin) || equipe.admin\n                    : \"Non défini\"\n                }}\n              </span>\n            </div>\n\n            <div\n              class=\"bg-[#f0f4f8] dark:bg-[#0a0a0a] border-l-4 border-[#dac4ea] dark:border-[#00f7ff] rounded-lg p-4 mb-4\"\n            >\n              <p class=\"text-[#6d6870] dark:text-[#a0a0a0] leading-relaxed\">\n                {{\n                  equipe.description ||\n                    \"Aucune description disponible pour cette équipe.\"\n                }}\n              </p>\n            </div>\n\n            <!-- Tags -->\n            <div class=\"flex flex-wrap gap-2\">\n              <span\n                class=\"bg-[#dac4ea]/10 dark:bg-[#00f7ff]/10 text-[#dac4ea] dark:text-[#00f7ff] px-3 py-1 rounded-full text-sm font-medium\"\n              >\n                <i class=\"fas fa-users mr-1\"></i>\n                {{ equipe.members?.length || 0 }} membres\n              </span>\n              <span\n                class=\"bg-[#00ff9d]/10 text-[#00ff9d] px-3 py-1 rounded-full text-sm font-medium\"\n              >\n                <i class=\"fas fa-calendar-check mr-1\"></i>\n                Créée le {{ formatDate(equipe.createdAt) }}\n              </span>\n              <span\n                class=\"bg-[#4f5fad]/10 text-[#4f5fad] px-3 py-1 rounded-full text-sm font-medium\"\n              >\n                <i class=\"fas fa-project-diagram mr-1\"></i>\n                Gestion de projet\n              </span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- AI Assistant -->\n    <div class=\"mb-8 relative\">\n      <!-- Decorative top border -->\n      <div\n        class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#8b5a9f] to-[#4a00e0] dark:from-[#00f7ff] dark:to-[#8b5a9f]\"\n      ></div>\n      <div\n        class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#8b5a9f] to-[#4a00e0] dark:from-[#00f7ff] dark:to-[#8b5a9f] blur-md\"\n      ></div>\n\n      <div\n        class=\"bg-white dark:bg-[#1a1a1a] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] overflow-hidden border border-[#8b5a9f]/20 dark:border-[#00f7ff]/20\"\n      >\n        <!-- Header -->\n        <div\n          class=\"bg-gradient-to-r from-[#8b5a9f] to-[#4a00e0] dark:from-[#00f7ff] dark:to-[#8b5a9f] p-6\"\n        >\n          <div class=\"flex items-center justify-between\">\n            <div class=\"flex items-center\">\n              <div\n                class=\"w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mr-4\"\n              >\n                <i class=\"fas fa-robot text-white text-xl\"></i>\n              </div>\n              <div>\n                <h3 class=\"text-xl font-bold text-white\">\n                  Assistant IA Gemini\n                </h3>\n                <p class=\"text-white/80 text-sm\">\n                  Génération de tâches intelligente\n                </p>\n              </div>\n            </div>\n            <span\n              class=\"bg-white/20 text-white px-3 py-1 rounded-full text-sm font-medium\"\n            >\n              <i class=\"fas fa-magic mr-1\"></i>\n              IA\n            </span>\n          </div>\n        </div>\n\n        <!-- AI Chat Component -->\n        <div class=\"p-0\">\n          <app-ai-chat [team]=\"equipe\"></app-ai-chat>\n        </div>\n      </div>\n    </div>\n\n    <!-- Team Members Section -->\n    <div class=\"mb-8 relative\">\n      <!-- Decorative top border -->\n      <div\n        class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#00ff9d] to-[#38ef7d] dark:from-[#00f7ff] dark:to-[#00ff9d]\"\n      ></div>\n      <div\n        class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#00ff9d] to-[#38ef7d] dark:from-[#00f7ff] dark:to-[#00ff9d] blur-md\"\n      ></div>\n\n      <div\n        class=\"bg-white dark:bg-[#1a1a1a] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] overflow-hidden border border-[#00ff9d]/20 dark:border-[#00f7ff]/20\"\n      >\n        <!-- Header -->\n        <div\n          class=\"bg-gradient-to-r from-[#00ff9d] to-[#38ef7d] dark:from-[#00f7ff] dark:to-[#00ff9d] p-6\"\n        >\n          <div class=\"flex items-center justify-between\">\n            <div class=\"flex items-center\">\n              <div\n                class=\"w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mr-4\"\n              >\n                <i class=\"fas fa-users text-white text-xl\"></i>\n              </div>\n              <div>\n                <h3 class=\"text-xl font-bold text-white\">\n                  Membres de l'équipe\n                </h3>\n                <p class=\"text-white/80 text-sm\">\n                  Gérez les membres et leurs rôles\n                </p>\n              </div>\n            </div>\n            <span\n              class=\"bg-white/20 text-white px-3 py-1 rounded-full text-sm font-medium\"\n            >\n              {{ teamMembers?.length || 0 }} membres\n            </span>\n          </div>\n        </div>\n\n        <!-- Members Content -->\n        <div class=\"grid grid-cols-1 lg:grid-cols-3\">\n          <!-- Members List -->\n          <div class=\"lg:col-span-2 p-6\">\n            <div\n              *ngIf=\"teamMembers && teamMembers.length > 0; else noMembers\"\n              class=\"space-y-4 max-h-96 overflow-y-auto\"\n            >\n              <div\n                *ngFor=\"let membre of teamMembers\"\n                class=\"bg-[#f0f4f8] dark:bg-[#0a0a0a] rounded-lg p-4 border border-[#00ff9d]/10 dark:border-[#00f7ff]/10 hover:shadow-lg transition-all duration-300\"\n              >\n                <div class=\"flex items-center justify-between\">\n                  <!-- Member Info -->\n                  <div class=\"flex items-center\">\n                    <div\n                      class=\"w-12 h-12 rounded-full flex items-center justify-center text-white mr-4\"\n                      [ngClass]=\"{\n                        'bg-gradient-to-br from-[#4f5fad] to-[#7826b5]':\n                          getUserProfession(membre.user) === 'etudiant',\n                        'bg-gradient-to-br from-[#00ff9d] to-[#38ef7d]':\n                          getUserProfession(membre.user) === 'professeur',\n                        'bg-gradient-to-br from-[#6d6870] to-[#a0a0a0]':\n                          !getUserProfession(membre.user)\n                      }\"\n                    >\n                      <i\n                        class=\"fas\"\n                        [ngClass]=\"{\n                          'fa-graduation-cap':\n                            getUserProfession(membre.user) === 'etudiant',\n                          'fa-chalkboard-teacher':\n                            getUserProfession(membre.user) === 'professeur',\n                          'fa-user': !getUserProfession(membre.user)\n                        }\"\n                      ></i>\n                    </div>\n                    <div>\n                      <h6\n                        class=\"font-bold text-[#6d6870] dark:text-[#a0a0a0] mb-1\"\n                      >\n                        {{ getUserName(membre.user) }}\n                      </h6>\n                      <div class=\"flex items-center space-x-2\">\n                        <span\n                          class=\"px-2 py-1 rounded-full text-xs font-medium\"\n                          [ngClass]=\"{\n                            'bg-[#00ff9d]/10 text-[#00ff9d]':\n                              membre.role === 'admin',\n                            'bg-[#4f5fad]/10 text-[#4f5fad]':\n                              membre.role === 'membre'\n                          }\"\n                        >\n                          <i\n                            class=\"fas mr-1\"\n                            [ngClass]=\"{\n                              'fa-user-shield': membre.role === 'admin',\n                              'fa-user': membre.role === 'membre'\n                            }\"\n                          ></i>\n                          {{\n                            membre.role === \"admin\"\n                              ? \"Administrateur\"\n                              : \"Membre\"\n                          }}\n                        </span>\n                        <span\n                          class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0]\"\n                        >\n                          {{\n                            getUserProfession(membre.user) === \"etudiant\"\n                              ? \"Étudiant\"\n                              : getUserProfession(membre.user) === \"professeur\"\n                              ? \"Professeur\"\n                              : \"Utilisateur\"\n                          }}\n                        </span>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- Remove Button -->\n                  <button\n                    (click)=\"removeMembreFromEquipe(membre._id)\"\n                    class=\"p-2 text-[#ff6b69] dark:text-[#ff3b30] hover:bg-[#ff6b69]/10 dark:hover:bg-[#ff3b30]/10 rounded-lg transition-all\"\n                    title=\"Retirer de l'équipe\"\n                  >\n                    <i class=\"fas fa-user-minus\"></i>\n                  </button>\n                </div>\n              </div>\n            </div>\n\n            <ng-template #noMembers>\n              <div class=\"text-center py-12\">\n                <div\n                  class=\"w-16 h-16 mx-auto mb-4 text-[#00ff9d] dark:text-[#00f7ff] opacity-50\"\n                >\n                  <i class=\"fas fa-users text-4xl\"></i>\n                </div>\n                <h5\n                  class=\"text-lg font-semibold text-[#00ff9d] dark:text-[#00f7ff] mb-2\"\n                >\n                  Aucun membre\n                </h5>\n                <p class=\"text-[#6d6870] dark:text-[#a0a0a0] text-sm\">\n                  Ajoutez des membres à l'équipe\n                </p>\n              </div>\n            </ng-template>\n          </div>\n\n          <!-- Add Member Form -->\n          <div class=\"bg-[#f0f4f8] dark:bg-[#0a0a0a] p-6\">\n            <h5\n              class=\"text-lg font-bold text-[#00ff9d] dark:text-[#00f7ff] mb-4 flex items-center\"\n            >\n              <i class=\"fas fa-user-plus mr-2\"></i>\n              Ajouter un membre\n            </h5>\n\n            <!-- No users available -->\n            <div\n              *ngIf=\"availableUsers?.length === 0\"\n              class=\"bg-[#4f5fad]/10 border-l-4 border-[#4f5fad] rounded-lg p-4\"\n            >\n              <div class=\"flex items-center\">\n                <div class=\"text-[#4f5fad] mr-3 text-lg\">\n                  <i class=\"fas fa-info-circle\"></i>\n                </div>\n                <div class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0]\">\n                  Aucun utilisateur disponible. Veuillez d'abord créer des\n                  utilisateurs.\n                </div>\n              </div>\n            </div>\n\n            <!-- Add member form -->\n            <div\n              *ngIf=\"availableUsers && availableUsers.length > 0\"\n              class=\"space-y-4\"\n            >\n              <div>\n                <label\n                  class=\"block text-sm font-medium text-[#00ff9d] dark:text-[#00f7ff] mb-2\"\n                  >Utilisateur</label\n                >\n                <select\n                  #userSelect\n                  class=\"w-full px-3 py-2 bg-white dark:bg-[#1a1a1a] border border-[#00ff9d]/20 dark:border-[#00f7ff]/20 rounded-lg text-[#6d6870] dark:text-[#a0a0a0] focus:outline-none focus:ring-2 focus:ring-[#00ff9d] dark:focus:ring-[#00f7ff] focus:border-transparent\"\n                >\n                  <option value=\"\" selected disabled>\n                    Sélectionnez un utilisateur\n                  </option>\n                  <option\n                    *ngFor=\"let user of availableUsers\"\n                    [value]=\"user._id || user.id\"\n                  >\n                    {{ user.firstName || \"\" }}\n                    {{ user.lastName || user.name || user.id }}\n                    {{ user.email ? \"- \" + user.email : \"\" }}\n                    {{\n                      user.profession\n                        ? \"(\" +\n                          (user.profession === \"etudiant\"\n                            ? \"Étudiant\"\n                            : \"Professeur\") +\n                          \")\"\n                        : \"\"\n                    }}\n                  </option>\n                </select>\n              </div>\n\n              <div>\n                <label\n                  class=\"block text-sm font-medium text-[#00ff9d] dark:text-[#00f7ff] mb-2\"\n                  >Rôle dans l'équipe</label\n                >\n                <div class=\"grid grid-cols-2 gap-2\">\n                  <label\n                    class=\"flex flex-col items-center p-3 border border-[#00ff9d]/20 dark:border-[#00f7ff]/20 rounded-lg cursor-pointer hover:bg-[#00ff9d]/5 dark:hover:bg-[#00f7ff]/5 transition-all\"\n                  >\n                    <input\n                      type=\"radio\"\n                      name=\"roleRadio\"\n                      value=\"membre\"\n                      checked\n                      #roleMembre\n                      class=\"sr-only\"\n                    />\n                    <i class=\"fas fa-user text-xl mb-1 text-[#4f5fad]\"></i>\n                    <span\n                      class=\"text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0]\"\n                      >Membre</span\n                    >\n                  </label>\n                  <label\n                    class=\"flex flex-col items-center p-3 border border-[#00ff9d]/20 dark:border-[#00f7ff]/20 rounded-lg cursor-pointer hover:bg-[#00ff9d]/5 dark:hover:bg-[#00f7ff]/5 transition-all\"\n                  >\n                    <input\n                      type=\"radio\"\n                      name=\"roleRadio\"\n                      value=\"admin\"\n                      #roleAdmin\n                      class=\"sr-only\"\n                    />\n                    <i\n                      class=\"fas fa-user-shield text-xl mb-1 text-[#00ff9d]\"\n                    ></i>\n                    <span\n                      class=\"text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0]\"\n                      >Admin</span\n                    >\n                  </label>\n                </div>\n              </div>\n\n              <button\n                type=\"button\"\n                [disabled]=\"!userSelect.value\"\n                (click)=\"\n                  addMembre(\n                    userSelect.value,\n                    roleMembre.checked ? 'membre' : 'admin'\n                  );\n                  userSelect.value = ''\n                \"\n                class=\"w-full bg-gradient-to-r from-[#00ff9d] to-[#38ef7d] dark:from-[#00f7ff] dark:to-[#00ff9d] text-white px-4 py-3 rounded-lg font-medium transition-all duration-300 hover:scale-105 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100\"\n              >\n                <i class=\"fas fa-plus-circle mr-2\"></i>\n                Ajouter à l'équipe\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n\n<!-- Loading/Error State -->\n<div\n  class=\"min-h-screen bg-[#f0f4f8] dark:bg-[#0a0a0a] flex items-center justify-center\"\n  *ngIf=\"!equipe\"\n>\n  <div class=\"text-center\">\n    <div\n      class=\"w-20 h-20 mx-auto mb-6 text-[#dac4ea] dark:text-[#00f7ff] opacity-70\"\n    >\n      <i class=\"fas fa-exclamation-triangle text-5xl\"></i>\n    </div>\n    <h3 class=\"text-xl font-semibold text-[#dac4ea] dark:text-[#00f7ff] mb-2\">\n      Équipe non trouvée\n    </h3>\n    <p class=\"text-[#6d6870] dark:text-[#a0a0a0] text-sm mb-6\">\n      L'équipe est en cours de chargement ou n'existe pas\n    </p>\n    <button\n      (click)=\"navigateToEquipeList()\"\n      class=\"bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea] text-white px-6 py-3 rounded-xl font-medium transition-all duration-300 hover:scale-105\"\n    >\n      <i class=\"fas fa-arrow-left mr-2\"></i>\n      Retour à la liste des équipes\n    </button>\n  </div>\n</div>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICwTcA,EAAA,CAAAC,cAAA,cAGC;IAeOD,EAAA,CAAAE,SAAA,YASK;IACPF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,UAAK;IAIDD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAAyC;IAUrCD,EAAA,CAAAE,SAAA,aAMK;IACLF,EAAA,CAAAI,MAAA,IAKF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,gBAEC;IACCD,EAAA,CAAAI,MAAA,IAOF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAMbH,EAAA,CAAAC,cAAA,kBAIC;IAHCD,EAAA,CAAAK,UAAA,mBAAAC,4EAAA;MAAA,MAAAC,WAAA,GAAAP,EAAA,CAAAQ,aAAA,CAAAC,IAAA;MAAA,MAAAC,SAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,MAAA,GAAAZ,EAAA,CAAAa,aAAA;MAAA,OAASb,EAAA,CAAAc,WAAA,CAAAF,MAAA,CAAAG,sBAAA,CAAAL,SAAA,CAAAM,GAAA,CAAkC;IAAA,EAAC;IAI5ChB,EAAA,CAAAE,SAAA,aAAiC;IACnCF,EAAA,CAAAG,YAAA,EAAS;;;;;IAvELH,EAAA,CAAAiB,SAAA,GAOE;IAPFjB,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAAmB,eAAA,IAAAC,GAAA,EAAAC,MAAA,CAAAC,iBAAA,CAAAZ,SAAA,CAAAa,IAAA,kBAAAF,MAAA,CAAAC,iBAAA,CAAAZ,SAAA,CAAAa,IAAA,qBAAAF,MAAA,CAAAC,iBAAA,CAAAZ,SAAA,CAAAa,IAAA,GAOE;IAIAvB,EAAA,CAAAiB,SAAA,GAME;IANFjB,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAAmB,eAAA,KAAAK,GAAA,EAAAH,MAAA,CAAAC,iBAAA,CAAAZ,SAAA,CAAAa,IAAA,kBAAAF,MAAA,CAAAC,iBAAA,CAAAZ,SAAA,CAAAa,IAAA,qBAAAF,MAAA,CAAAC,iBAAA,CAAAZ,SAAA,CAAAa,IAAA,GAME;IAOFvB,EAAA,CAAAiB,SAAA,GACF;IADEjB,EAAA,CAAAyB,kBAAA,MAAAJ,MAAA,CAAAK,WAAA,CAAAhB,SAAA,CAAAa,IAAA,OACF;IAIIvB,EAAA,CAAAiB,SAAA,GAKE;IALFjB,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAA2B,eAAA,KAAAC,GAAA,EAAAlB,SAAA,CAAAmB,IAAA,cAAAnB,SAAA,CAAAmB,IAAA,eAKE;IAIA7B,EAAA,CAAAiB,SAAA,GAGE;IAHFjB,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAA2B,eAAA,KAAAG,GAAA,EAAApB,SAAA,CAAAmB,IAAA,cAAAnB,SAAA,CAAAmB,IAAA,eAGE;IAEJ7B,EAAA,CAAAiB,SAAA,GAKF;IALEjB,EAAA,CAAAyB,kBAAA,MAAAf,SAAA,CAAAmB,IAAA,gDAKF;IAIE7B,EAAA,CAAAiB,SAAA,GAOF;IAPEjB,EAAA,CAAAyB,kBAAA,MAAAJ,MAAA,CAAAC,iBAAA,CAAAZ,SAAA,CAAAa,IAAA,qCAAAF,MAAA,CAAAC,iBAAA,CAAAZ,SAAA,CAAAa,IAAA,uDAOF;;;;;IAxEZvB,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAA+B,UAAA,IAAAC,kDAAA,oBAkFM;IACRhC,EAAA,CAAAG,YAAA,EAAM;;;;IAlFiBH,EAAA,CAAAiB,SAAA,GAAc;IAAdjB,EAAA,CAAAkB,UAAA,YAAAe,MAAA,CAAAC,WAAA,CAAc;;;;;IAqFnClC,EAAA,CAAAC,cAAA,cAA+B;IAI3BD,EAAA,CAAAE,SAAA,YAAqC;IACvCF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,aAEC;IACCD,EAAA,CAAAI,MAAA,qBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,aAAsD;IACpDD,EAAA,CAAAI,MAAA,iDACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;;IAeRH,EAAA,CAAAC,cAAA,eAGC;IAGKD,EAAA,CAAAE,SAAA,aAAkC;IACpCF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAwD;IACtDD,EAAA,CAAAI,MAAA,oFAEF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;;IAqBJH,EAAA,CAAAC,cAAA,kBAGC;IACCD,EAAA,CAAAI,MAAA,GAYF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;IAdPH,EAAA,CAAAkB,UAAA,UAAAiB,QAAA,CAAAnB,GAAA,IAAAmB,QAAA,CAAAC,EAAA,CAA6B;IAE7BpC,EAAA,CAAAiB,SAAA,GAYF;IAZEjB,EAAA,CAAAqC,kBAAA,MAAAF,QAAA,CAAAG,SAAA,aAAAH,QAAA,CAAAI,QAAA,IAAAJ,QAAA,CAAAK,IAAA,IAAAL,QAAA,CAAAC,EAAA,OAAAD,QAAA,CAAAM,KAAA,UAAAN,QAAA,CAAAM,KAAA,YAAAN,QAAA,CAAAO,UAAA,UAAAP,QAAA,CAAAO,UAAA,kEAYF;;;;;;IAhCN1C,EAAA,CAAAC,cAAA,eAGC;IAIMD,EAAA,CAAAI,MAAA,kBAAW;IAAAJ,EAAA,CAAAG,YAAA,EACb;IACDH,EAAA,CAAAC,cAAA,uBAGC;IAEGD,EAAA,CAAAI,MAAA,yCACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAA+B,UAAA,IAAAY,qDAAA,sBAgBS;IACX3C,EAAA,CAAAG,YAAA,EAAS;IAGXH,EAAA,CAAAC,cAAA,UAAK;IAGAD,EAAA,CAAAI,MAAA,oCAAkB;IAAAJ,EAAA,CAAAG,YAAA,EACpB;IACDH,EAAA,CAAAC,cAAA,gBAAoC;IAIhCD,EAAA,CAAAE,SAAA,uBAOE;IAEFF,EAAA,CAAAC,cAAA,iBAEG;IAAAD,EAAA,CAAAI,MAAA,cAAM;IAAAJ,EAAA,CAAAG,YAAA,EACR;IAEHH,EAAA,CAAAC,cAAA,kBAEC;IACCD,EAAA,CAAAE,SAAA,uBAME;IAIFF,EAAA,CAAAC,cAAA,iBAEG;IAAAD,EAAA,CAAAI,MAAA,aAAK;IAAAJ,EAAA,CAAAG,YAAA,EACP;IAKPH,EAAA,CAAAC,cAAA,mBAWC;IARCD,EAAA,CAAAK,UAAA,mBAAAuC,sEAAA;MAAA5C,EAAA,CAAAQ,aAAA,CAAAqC,IAAA;MAAA,MAAAC,IAAA,GAAA9C,EAAA,CAAA+C,WAAA;MAAA,MAAAC,IAAA,GAAAhD,EAAA,CAAA+C,WAAA;MAAA,MAAAE,OAAA,GAAAjD,EAAA,CAAAa,aAAA;MAERoC,OAAA,CAAAC,SAAA,CAAAJ,IAAA,CAAAK,KAAA,EAAAH,IAAA,CAAAI,OAAA,GAER,QAAQ,GAAG,OAAO,CACD;MAAA,OAAoBpD,EAAA,CAAAc,WAAA,CAAAgC,IAAA,CAAAK,KAAA,GACrB,EAChB;IAAA,EADiB;IAGDnD,EAAA,CAAAE,SAAA,cAAuC;IACvCF,EAAA,CAAAI,MAAA,sCACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;;IA7EcH,EAAA,CAAAiB,SAAA,GAAiB;IAAjBjB,EAAA,CAAAkB,UAAA,YAAAmC,MAAA,CAAAC,cAAA,CAAiB;IAiEtCtD,EAAA,CAAAiB,SAAA,IAA8B;IAA9BjB,EAAA,CAAAkB,UAAA,cAAA4B,IAAA,CAAAK,KAAA,CAA8B;;;;;;IA5gB9CnD,EAAA,CAAAC,cAAA,aAGC;IAGGD,EAAA,CAAAE,SAAA,aAEO;IAMPF,EAAA,CAAAC,cAAA,aAA4D;IAExDD,EAAA,CAAAE,SAAA,aAAmE;IAWrEF,EAAA,CAAAG,YAAA,EAAM;IAIVH,EAAA,CAAAC,cAAA,cAAiD;IAI7CD,EAAA,CAAAE,SAAA,eAEO;IAKPF,EAAA,CAAAC,cAAA,eAEC;IAMyCD,EAAA,CAAAI,MAAA,IAAiB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAC1DH,EAAA,CAAAC,cAAA,aAAsC;IACpCD,EAAA,CAAAI,MAAA,gDACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAGJH,EAAA,CAAAC,cAAA,eAAoC;IAIhCD,EAAA,CAAAE,SAAA,aAAgD;IAChDF,EAAA,CAAAC,cAAA,eAA+B;IAC7BD,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAmC;IAAAD,EAAA,CAAAI,MAAA,eAAO;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAElDH,EAAA,CAAAC,cAAA,eAEC;IACCD,EAAA,CAAAE,SAAA,aAAgD;IAChDF,EAAA,CAAAC,cAAA,eAA+B;IAAAD,EAAA,CAAAI,MAAA,SAAC;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IACtCH,EAAA,CAAAC,cAAA,eAAmC;IAAAD,EAAA,CAAAI,MAAA,mBAAM;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAEjDH,EAAA,CAAAC,cAAA,eAEC;IACCD,EAAA,CAAAE,SAAA,aAAyD;IACzDF,EAAA,CAAAC,cAAA,eAA+B;IAC7BD,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAmC;IAAAD,EAAA,CAAAI,MAAA,0BAAQ;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAMvDH,EAAA,CAAAC,cAAA,eAA4C;IAIxCD,EAAA,CAAAE,SAAA,aAAgC;IAChCF,EAAA,CAAAI,MAAA,yBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,eAAuB;IAEnBD,EAAA,CAAAK,UAAA,mBAAAkD,8DAAA;MAAAvD,EAAA,CAAAQ,aAAA,CAAAgD,IAAA;MAAA,MAAAC,OAAA,GAAAzD,EAAA,CAAAa,aAAA;MAAA,OAASb,EAAA,CAAAc,WAAA,CAAA2C,OAAA,CAAAC,eAAA,EAAiB;IAAA,EAAC;IAG3B1D,EAAA,CAAAE,SAAA,aAAiC;IACjCF,EAAA,CAAAI,MAAA,oCACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAGC;IAFCD,EAAA,CAAAK,UAAA,mBAAAsD,8DAAA;MAAA3D,EAAA,CAAAQ,aAAA,CAAAgD,IAAA;MAAA,MAAAI,OAAA,GAAA5D,EAAA,CAAAa,aAAA;MAAA,OAASb,EAAA,CAAAc,WAAA,CAAA8C,OAAA,CAAAC,oBAAA,EAAsB;IAAA,EAAC;IAGhC7D,EAAA,CAAAE,SAAA,aAAgC;IAChCF,EAAA,CAAAI,MAAA,gCACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,eAA4B;IAExBD,EAAA,CAAAK,UAAA,mBAAAyD,8DAAA;MAAA9D,EAAA,CAAAQ,aAAA,CAAAgD,IAAA;MAAA,MAAAO,OAAA,GAAA/D,EAAA,CAAAa,aAAA;MAAA,OAASb,EAAA,CAAAc,WAAA,CAAAiD,OAAA,CAAAC,oBAAA,EAAsB;IAAA,EAAC;IAGhChE,EAAA,CAAAE,SAAA,aAAsC;IACtCF,EAAA,CAAAI,MAAA,gBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAGC;IAFCD,EAAA,CAAAK,UAAA,mBAAA4D,8DAAA;MAAAjE,EAAA,CAAAQ,aAAA,CAAAgD,IAAA;MAAA,MAAAU,OAAA,GAAAlE,EAAA,CAAAa,aAAA;MAAA,OAASb,EAAA,CAAAc,WAAA,CAAAoD,OAAA,CAAAC,YAAA,EAAc;IAAA,EAAC;IAGxBnE,EAAA,CAAAE,SAAA,aAA4B;IAC9BF,EAAA,CAAAG,YAAA,EAAS;IASrBH,EAAA,CAAAC,cAAA,eAA2B;IAEzBD,EAAA,CAAAE,SAAA,eAEO;IAKPF,EAAA,CAAAC,cAAA,eAEC;IASOD,EAAA,CAAAE,SAAA,aAA2C;IAC7CF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAmC;IAAAD,EAAA,CAAAI,MAAA,qBAAQ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAChDH,EAAA,CAAAC,cAAA,aAA6C;IAC3CD,EAAA,CAAAI,MAAA,sCACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAINH,EAAA,CAAAC,cAAA,eAA+B;IAGzBD,EAAA,CAAAI,MAAA,qBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,gBAEC;IACCD,EAAA,CAAAE,SAAA,aAAuC;IACvCF,EAAA,CAAAI,MAAA,IAMF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAGTH,EAAA,CAAAC,cAAA,eAEC;IAEGD,EAAA,CAAAI,MAAA,IAIF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAINH,EAAA,CAAAC,cAAA,eAAkC;IAI9BD,EAAA,CAAAE,SAAA,aAAiC;IACjCF,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,gBAEC;IACCD,EAAA,CAAAE,SAAA,aAA0C;IAC1CF,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,gBAEC;IACCD,EAAA,CAAAE,SAAA,aAA2C;IAC3CF,EAAA,CAAAI,MAAA,2BACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAQjBH,EAAA,CAAAC,cAAA,eAA2B;IAEzBD,EAAA,CAAAE,SAAA,eAEO;IAKPF,EAAA,CAAAC,cAAA,eAEC;IAUSD,EAAA,CAAAE,SAAA,cAA+C;IACjDF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,YAAK;IAEDD,EAAA,CAAAI,MAAA,8BACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAAiC;IAC/BD,EAAA,CAAAI,MAAA,2DACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAGRH,EAAA,CAAAC,cAAA,iBAEC;IACCD,EAAA,CAAAE,SAAA,cAAiC;IACjCF,EAAA,CAAAI,MAAA,aACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAKXH,EAAA,CAAAC,cAAA,gBAAiB;IACfD,EAAA,CAAAE,SAAA,wBAA2C;IAC7CF,EAAA,CAAAG,YAAA,EAAM;IAKVH,EAAA,CAAAC,cAAA,gBAA2B;IAEzBD,EAAA,CAAAE,SAAA,gBAEO;IAKPF,EAAA,CAAAC,cAAA,gBAEC;IAUSD,EAAA,CAAAE,SAAA,cAA+C;IACjDF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,YAAK;IAEDD,EAAA,CAAAI,MAAA,mCACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAAiC;IAC/BD,EAAA,CAAAI,MAAA,qDACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAGRH,EAAA,CAAAC,cAAA,iBAEC;IACCD,EAAA,CAAAI,MAAA,KACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAKXH,EAAA,CAAAC,cAAA,gBAA6C;IAGzCD,EAAA,CAAA+B,UAAA,MAAAqC,4CAAA,kBAuFM;IAENpE,EAAA,CAAA+B,UAAA,MAAAsC,oDAAA,iCAAArE,EAAA,CAAAsE,sBAAA,CAgBc;IAChBtE,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,gBAAgD;IAI5CD,EAAA,CAAAE,SAAA,cAAqC;IACrCF,EAAA,CAAAI,MAAA,4BACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAGLH,EAAA,CAAA+B,UAAA,MAAAwC,4CAAA,kBAaM;IAGNvE,EAAA,CAAA+B,UAAA,MAAAyC,4CAAA,mBA+FM;IACRxE,EAAA,CAAAG,YAAA,EAAM;;;;;IAxegCH,EAAA,CAAAiB,SAAA,IAAiB;IAAjBjB,EAAA,CAAAyE,iBAAA,CAAAC,MAAA,CAAAC,MAAA,CAAAnC,IAAA,CAAiB;IAY/CxC,EAAA,CAAAiB,SAAA,GACF;IADEjB,EAAA,CAAAyB,kBAAA,OAAAiD,MAAA,CAAAC,MAAA,CAAAC,OAAA,kBAAAF,MAAA,CAAAC,MAAA,CAAAC,OAAA,CAAAC,MAAA,YACF;IAeE7E,EAAA,CAAAiB,SAAA,IACF;IADEjB,EAAA,CAAAyB,kBAAA,MAAAiD,MAAA,CAAAI,UAAA,CAAAJ,MAAA,CAAAC,MAAA,CAAAI,SAAA,OACF;IAyFA/E,EAAA,CAAAiB,SAAA,IAMF;IANEjB,EAAA,CAAAyB,kBAAA,aAAAiD,MAAA,CAAAC,MAAA,CAAAK,KAAA,GAAAN,MAAA,CAAAhD,WAAA,CAAAgD,MAAA,CAAAC,MAAA,CAAAK,KAAA,KAAAN,MAAA,CAAAC,MAAA,CAAAK,KAAA,0BAMF;IAOEhF,EAAA,CAAAiB,SAAA,GAIF;IAJEjB,EAAA,CAAAyB,kBAAA,MAAAiD,MAAA,CAAAC,MAAA,CAAAM,WAAA,iEAIF;IASEjF,EAAA,CAAAiB,SAAA,GACF;IADEjB,EAAA,CAAAyB,kBAAA,OAAAiD,MAAA,CAAAC,MAAA,CAAAC,OAAA,kBAAAF,MAAA,CAAAC,MAAA,CAAAC,OAAA,CAAAC,MAAA,oBACF;IAKE7E,EAAA,CAAAiB,SAAA,GACF;IADEjB,EAAA,CAAAyB,kBAAA,yBAAAiD,MAAA,CAAAI,UAAA,CAAAJ,MAAA,CAAAC,MAAA,CAAAI,SAAA,OACF;IAyDS/E,EAAA,CAAAiB,SAAA,IAAe;IAAfjB,EAAA,CAAAkB,UAAA,SAAAwD,MAAA,CAAAC,MAAA,CAAe;IAyCxB3E,EAAA,CAAAiB,SAAA,IACF;IADEjB,EAAA,CAAAyB,kBAAA,OAAAiD,MAAA,CAAAxC,WAAA,kBAAAwC,MAAA,CAAAxC,WAAA,CAAA2C,MAAA,oBACF;IASG7E,EAAA,CAAAiB,SAAA,GAA6C;IAA7CjB,EAAA,CAAAkB,UAAA,SAAAwD,MAAA,CAAAxC,WAAA,IAAAwC,MAAA,CAAAxC,WAAA,CAAA2C,MAAA,KAA6C,aAAAK,GAAA;IAsH7ClF,EAAA,CAAAiB,SAAA,GAAkC;IAAlCjB,EAAA,CAAAkB,UAAA,UAAAwD,MAAA,CAAApB,cAAA,kBAAAoB,MAAA,CAAApB,cAAA,CAAAuB,MAAA,QAAkC;IAgBlC7E,EAAA,CAAAiB,SAAA,GAAiD;IAAjDjB,EAAA,CAAAkB,UAAA,SAAAwD,MAAA,CAAApB,cAAA,IAAAoB,MAAA,CAAApB,cAAA,CAAAuB,MAAA,KAAiD;;;;;;IAuGhE7E,EAAA,CAAAC,cAAA,eAGC;IAKKD,EAAA,CAAAE,SAAA,aAAoD;IACtDF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAA0E;IACxED,EAAA,CAAAI,MAAA,qCACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,aAA2D;IACzDD,EAAA,CAAAI,MAAA,iEACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAC,cAAA,kBAGC;IAFCD,EAAA,CAAAK,UAAA,mBAAA8E,6DAAA;MAAAnF,EAAA,CAAAQ,aAAA,CAAA4E,IAAA;MAAA,MAAAC,OAAA,GAAArF,EAAA,CAAAa,aAAA;MAAA,OAASb,EAAA,CAAAc,WAAA,CAAAuE,OAAA,CAAArB,oBAAA,EAAsB;IAAA,EAAC;IAGhChE,EAAA,CAAAE,SAAA,YAAsC;IACtCF,EAAA,CAAAI,MAAA,iDACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;AD5iBb,OAAM,MAAOmF,qBAAqB;EAUhCC,YACUC,aAA4B,EAC5BC,WAAwB;EAAE;EAC1BC,KAAqB,EACrBC,MAAc;IAHd,KAAAH,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IAbhB,KAAAhB,MAAM,GAAkB,IAAI;IAC5B,KAAAiB,OAAO,GAAG,KAAK;IACf,KAAAC,KAAK,GAAkB,IAAI;IAC3B,KAAAC,QAAQ,GAAkB,IAAI;IAC9B,KAAAC,SAAS,GAAQ;MAAE3D,EAAE,EAAE,EAAE;MAAEP,IAAI,EAAE;IAAQ,CAAE;IAC3C,KAAAyB,cAAc,GAAW,EAAE;IAC3B,KAAA0C,WAAW,GAA8B,EAAE,CAAC,CAAC;IAC7C,KAAA9D,WAAW,GAAU,EAAE,CAAC,CAAC;EAOtB;;EAEH+D,QAAQA,CAAA;IACN,IAAI,CAACH,QAAQ,GAAG,IAAI,CAACJ,KAAK,CAACQ,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC;IAEtD;IACA,IAAI,CAACC,SAAS,EAAE;IAEhB,IAAI,IAAI,CAACP,QAAQ,EAAE;MACjB,IAAI,CAACQ,UAAU,CAAC,IAAI,CAACR,QAAQ,CAAC;KAC/B,MAAM;MACL,IAAI,CAACD,KAAK,GAAG,0BAA0B;;EAE3C;EAEA;EACAQ,SAASA,CAAA;IACP;IACA;IACA,MAAME,SAAS,GAAW,CACxB;MACEvF,GAAG,EAAE,OAAO;MACZwF,QAAQ,EAAE,UAAU;MACpB/D,KAAK,EAAE,kBAAkB;MACzBZ,IAAI,EAAE,OAAO;MACb4E,QAAQ,EAAE;KACX,EACD;MACEzF,GAAG,EAAE,OAAO;MACZwF,QAAQ,EAAE,YAAY;MACtB/D,KAAK,EAAE,kBAAkB;MACzBZ,IAAI,EAAE,SAAS;MACf4E,QAAQ,EAAE;KACX,EACD;MACEzF,GAAG,EAAE,OAAO;MACZwF,QAAQ,EAAE,YAAY;MACtB/D,KAAK,EAAE,iBAAiB;MACxBZ,IAAI,EAAE,SAAS;MACf4E,QAAQ,EAAE;KACX,CACF;IAED;IACAC,UAAU,CAAC,MAAK;MACd;MACA,MAAMC,QAAQ,GAAG,CAAC,GAAGJ,SAAS,CAAC;MAC/BK,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEF,QAAQ,CAAC;MAE9D;MACA,IAAI,IAAI,CAACzE,WAAW,IAAI,IAAI,CAACA,WAAW,CAAC2C,MAAM,GAAG,CAAC,EAAE;QACnD,MAAMiC,aAAa,GAAG,IAAI,CAAC5E,WAAW,CAAC6E,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACzF,IAAI,CAAC;QACzD,IAAI,CAAC+B,cAAc,GAAGiD,SAAS,CAACU,MAAM,CACnC1F,IAAI,IAAK,CAACuF,aAAa,CAACI,QAAQ,CAAC3F,IAAI,CAACP,GAAG,IAAIO,IAAI,CAACa,EAAE,IAAI,EAAE,CAAC,CAC7D;OACF,MAAM;QACL,IAAI,CAACkB,cAAc,GAAGiD,SAAS;;MAGjCK,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE,IAAI,CAACvD,cAAc,CAAC;MAE7D;MACA,IAAI,IAAI,CAACqB,MAAM,IAAI,IAAI,CAACA,MAAM,CAACC,OAAO,EAAE;QACtC,IAAI,CAACuC,iBAAiB,EAAE;;IAE5B,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;EACAA,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAACxC,MAAM,IAAI,CAAC,IAAI,CAACA,MAAM,CAACC,OAAO,EAAE;IAE1C,IAAI,CAACD,MAAM,CAACC,OAAO,CAACwC,OAAO,CAAEC,QAAQ,IAAI;MACvC,MAAM9F,IAAI,GAAG,IAAI,CAAC+B,cAAc,CAACgE,IAAI,CAClCC,CAAC,IAAKA,CAAC,CAACvG,GAAG,KAAKqG,QAAQ,IAAIE,CAAC,CAACnF,EAAE,KAAKiF,QAAQ,CAC/C;MACD,IAAI9F,IAAI,IAAIA,IAAI,CAACiB,IAAI,EAAE;QACrB,IAAI,CAACwD,WAAW,CAACqB,QAAQ,CAAC,GAAG9F,IAAI,CAACiB,IAAI;OACvC,MAAM;QACL;QACA;QACA;QACA,IAAI,CAACwD,WAAW,CAACqB,QAAQ,CAAC,GAAGA,QAAQ;;IAEzC,CAAC,CAAC;EACJ;EAEA;EACAG,aAAaA,CAACH,QAAgB;IAC5B,OAAO,IAAI,CAACrB,WAAW,CAACqB,QAAQ,CAAC,IAAIA,QAAQ;EAC/C;EAEA;EACA3F,WAAWA,CAAC+F,MAA0B;IACpC,IAAI,CAACA,MAAM,EAAE;MACX,OAAO,YAAY;;IAGrB,MAAMlG,IAAI,GAAG,IAAI,CAAC+B,cAAc,CAACgE,IAAI,CAClCC,CAAC,IAAKA,CAAC,CAACvG,GAAG,KAAKyG,MAAM,IAAIF,CAAC,CAACnF,EAAE,KAAKqF,MAAM,CAC3C;IACD,IAAIlG,IAAI,EAAE;MACR,IAAIA,IAAI,CAACe,SAAS,IAAIf,IAAI,CAACgB,QAAQ,EAAE;QACnC,OAAO,GAAGhB,IAAI,CAACe,SAAS,IAAIf,IAAI,CAACgB,QAAQ,EAAE;OAC5C,MAAM,IAAIhB,IAAI,CAACiB,IAAI,EAAE;QACpB,OAAOjB,IAAI,CAACiB,IAAI;;;IAGpB,OAAOiF,MAAM;EACf;EAEA;EACAnG,iBAAiBA,CAACmG,MAA0B;IAC1C,IAAI,CAACA,MAAM,EAAE;MACX,OAAO,EAAE;;IAGX,MAAMlG,IAAI,GAAG,IAAI,CAAC+B,cAAc,CAACgE,IAAI,CAClCC,CAAC,IAAKA,CAAC,CAACvG,GAAG,KAAKyG,MAAM,IAAIF,CAAC,CAACnF,EAAE,KAAKqF,MAAM,CAC3C;IACD,IAAIlG,IAAI,EAAE;MACR,OAAOA,IAAI,CAACmB,UAAU,IAAInB,IAAI,CAACM,IAAI,IAAI,EAAE;;IAE3C,OAAO,EAAE;EACX;EAEAyE,UAAUA,CAAClE,EAAU;IACnB,IAAI,CAACwD,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,KAAK,GAAG,IAAI;IAEjB,IAAI,CAACL,aAAa,CAACkC,SAAS,CAACtF,EAAE,CAAC,CAACuF,SAAS,CAAC;MACzCC,IAAI,EAAGC,IAAI,IAAI;QACbjB,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEgB,IAAI,CAAC;QACjD,IAAI,CAAClD,MAAM,GAAGkD,IAAI;QAElB;QACA,IAAI,CAACC,eAAe,CAAC1F,EAAE,CAAC;QAExB;QACA,IACE,IAAI,CAACuC,MAAM,IACX,IAAI,CAACA,MAAM,CAACC,OAAO,IACnB,IAAI,CAACD,MAAM,CAACC,OAAO,CAACC,MAAM,GAAG,CAAC,EAC9B;UACA,IAAI,CAACsC,iBAAiB,EAAE;;QAG1B,IAAI,CAACvB,OAAO,GAAG,KAAK;MACtB,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfe,OAAO,CAACf,KAAK,CACX,oDAAoD,EACpDA,KAAK,CACN;QACD,IAAI,CAACA,KAAK,GACR,8EAA8E;QAChF,IAAI,CAACD,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEA;EACAkC,eAAeA,CAACC,MAAc;IAC5B,IAAI,CAACvC,aAAa,CAACwC,cAAc,CAACD,MAAM,CAAC,CAACJ,SAAS,CAAC;MAClDC,IAAI,EAAGhD,OAAO,IAAI;QAChBgC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEjC,OAAO,CAAC;QACpD,IAAI,CAAC1C,WAAW,GAAG0C,OAAO;MAC5B,CAAC;MACDiB,KAAK,EAAGA,KAAK,IAAI;QACfe,OAAO,CAACf,KAAK,CACX,oDAAoD,EACpDA,KAAK,CACN;MACH;KACD,CAAC;EACJ;EAEAhC,oBAAoBA,CAAA;IAClB,IAAI,IAAI,CAACiC,QAAQ,EAAE;MACjB,IAAI,CAACH,MAAM,CAACsC,QAAQ,CAAC,CAAC,mBAAmB,EAAE,IAAI,CAACnC,QAAQ,CAAC,CAAC;;EAE9D;EAEA9B,oBAAoBA,CAAA;IAClB,IAAI,CAAC2B,MAAM,CAACsC,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;EAC1C;EAEAvE,eAAeA,CAAA;IACb,IAAI,IAAI,CAACoC,QAAQ,EAAE;MACjB,IAAI,CAACH,MAAM,CAACsC,QAAQ,CAAC,CAAC,gBAAgB,EAAE,IAAI,CAACnC,QAAQ,CAAC,CAAC;;EAE3D;EAEA;EACAhB,UAAUA,CAACoD,IAA+B;IACxC,IAAI,CAACA,IAAI,EAAE;MACT,OAAO,KAAK;;IAGd,IAAI;MACF,IAAIC,OAAa;MAEjB,IAAI,OAAOD,IAAI,KAAK,QAAQ,EAAE;QAC5BC,OAAO,GAAG,IAAIC,IAAI,CAACF,IAAI,CAAC;OACzB,MAAM;QACLC,OAAO,GAAGD,IAAI;;MAGhB,IAAIG,KAAK,CAACF,OAAO,CAACG,OAAO,EAAE,CAAC,EAAE;QAC5B,OAAO,eAAe;;MAGxB;MACA,OAAOH,OAAO,CAACI,kBAAkB,CAAC,OAAO,EAAE;QACzCC,GAAG,EAAE,SAAS;QACdC,KAAK,EAAE,SAAS;QAChBC,IAAI,EAAE;OACP,CAAC;KACH,CAAC,OAAO7C,KAAK,EAAE;MACde,OAAO,CAACf,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D,OAAO,gBAAgB;;EAE3B;EAEA;EACA3C,SAASA,CAACuE,MAAc,EAAE5F,IAAY;IACpC+E,OAAO,CAACC,GAAG,CAAC,0BAA0BY,MAAM,iBAAiB5F,IAAI,EAAE,CAAC;IAEpE,IAAI,CAAC,IAAI,CAACiE,QAAQ,IAAI,CAAC2B,MAAM,EAAE;MAC7Bb,OAAO,CAACf,KAAK,CAAC,0CAA0C,CAAC;MACzD,IAAI,CAACA,KAAK,GAAG,0CAA0C;MACvD;;IAGF;IACA,MAAM8C,eAAe,GAAG,IAAI,CAACzG,WAAW,CAAC0G,IAAI,CAAE5B,CAAC,IAAKA,CAAC,CAACzF,IAAI,KAAKkG,MAAM,CAAC;IACvE,IAAIkB,eAAe,EAAE;MACnB,IAAI,CAAC9C,KAAK,GAAG,6CAA6C;MAC1DgD,KAAK,CAAC,6CAA6C,CAAC;MACpD;;IAGF;IACA,MAAMC,MAAM,GAAW;MACrB1G,EAAE,EAAEqF,MAAM;MACV5F,IAAI,EAAEA,IAAI,IAAI;KACf;IAED;IACA,MAAMkH,QAAQ,GAAG,IAAI,CAACrH,WAAW,CAAC+F,MAAM,CAAC;IACzC,MAAMuB,QAAQ,GAAGnH,IAAI,KAAK,OAAO,GAAG,gBAAgB,GAAG,QAAQ;IAE/D,IAAI,CAAC2D,aAAa,CAACyD,iBAAiB,CAAC,IAAI,CAACnD,QAAQ,EAAEgD,MAAM,CAAC,CAACnB,SAAS,CAAC;MACpEC,IAAI,EAAGsB,QAAQ,IAAI;QACjBtC,OAAO,CAACC,GAAG,CACT,gBAAgBkC,QAAQ,kBAAkBC,QAAQ,eAAe,EACjEE,QAAQ,CACT;QAED;QACAL,KAAK,CAAC,gBAAgBE,QAAQ,kBAAkBC,QAAQ,cAAc,CAAC;QAEvE;QACA,IAAI,CAAClB,eAAe,CAAC,IAAI,CAAChC,QAAS,CAAC;QAEpC;QACA,IAAI,CAACQ,UAAU,CAAC,IAAI,CAACR,QAAS,CAAC;QAE/B;QACA,IAAI,CAACqD,oBAAoB,EAAE;MAC7B,CAAC;MACDtD,KAAK,EAAGA,KAAK,IAAI;QACfe,OAAO,CAACf,KAAK,CACX,uDAAuD,EACvDA,KAAK,CACN;QACD,IAAI,CAACA,KAAK,GAAG,uCAAuCkD,QAAQ,WAAWC,QAAQ,iCAAiC;QAChHH,KAAK,CAAC,IAAI,CAAChD,KAAK,CAAC;MACnB;KACD,CAAC;EACJ;EAEA;EACAsD,oBAAoBA,CAAA;IAClB;IACA;IACA,IAAI,CAAC9C,SAAS,EAAE;EAClB;EAEA;EACA4C,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAACnD,QAAQ,IAAI,CAAC,IAAI,CAACC,SAAS,CAAC3D,EAAE,EAAE;MACxCwE,OAAO,CAACf,KAAK,CAAC,sCAAsC,CAAC;MACrD;;IAGF,IAAI,CAAC3C,SAAS,CAAC,IAAI,CAAC6C,SAAS,CAAC3D,EAAE,EAAE,IAAI,CAAC2D,SAAS,CAAClE,IAAI,IAAI,QAAQ,CAAC;EACpE;EAEAd,sBAAsBA,CAACsG,QAAgB;IACrCT,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAEQ,QAAQ,CAAC;IAExE,IAAI,CAAC,IAAI,CAACvB,QAAQ,EAAE;MAClBc,OAAO,CAACf,KAAK,CAAC,sBAAsB,CAAC;MACrC,IAAI,CAACA,KAAK,GAAG,wDAAwD;MACrE;;IAGF;IACA,MAAM4B,MAAM,GAAGJ,QAAQ;IAEvB;IACA,MAAM0B,QAAQ,GAAG,IAAI,CAACrH,WAAW,CAAC+F,MAAM,CAAC;IAEzCb,OAAO,CAACC,GAAG,CACT,yCAAyCY,MAAM,KAAKsB,QAAQ,iBAAiB,IAAI,CAACjD,QAAQ,EAAE,CAC7F;IAED,IACEsD,OAAO,CACL,mDAAmDL,QAAQ,gBAAgB,CAC5E,EACD;MACAnC,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;MAE7D,IAAI,CAACjB,OAAO,GAAG,IAAI;MACnB,IAAI,CAACC,KAAK,GAAG,IAAI;MAEjB,IAAI,CAACL,aAAa,CACfzE,sBAAsB,CAAC,IAAI,CAAC+E,QAAQ,EAAE2B,MAAM,CAAC,CAC7CE,SAAS,CAAC;QACTC,IAAI,EAAGsB,QAAQ,IAAI;UACjBtC,OAAO,CAACC,GAAG,CACT,gBAAgBkC,QAAQ,mCAAmC,EAC3DG,QAAQ,CACT;UACD,IAAI,CAACtD,OAAO,GAAG,KAAK;UAEpB;UACAiD,KAAK,CAAC,gBAAgBE,QAAQ,kCAAkC,CAAC;UAEjE;UACA,IAAI,CAACjB,eAAe,CAAC,IAAI,CAAChC,QAAS,CAAC;UAEpC;UACA,IAAI,CAACQ,UAAU,CAAC,IAAI,CAACR,QAAS,CAAC;UAE/B;UACA,IAAI,CAACqD,oBAAoB,EAAE;QAC7B,CAAC;QACDtD,KAAK,EAAGA,KAAK,IAAI;UACfe,OAAO,CAACf,KAAK,CACX,4CAA4CkD,QAAQ,IAAI,EACxDlD,KAAK,CACN;UACD,IAAI,CAACD,OAAO,GAAG,KAAK;UACpB,IAAI,CAACC,KAAK,GAAG,wCAAwCkD,QAAQ,kBAC3DlD,KAAK,CAACwD,OAAO,IAAI,iBACnB,EAAE;QACJ;OACD,CAAC;KACL,MAAM;MACLzC,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;;EAExD;EAEA1C,YAAYA,CAAA;IACVyC,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;IAE3C,IAAI,CAAC,IAAI,CAACf,QAAQ,EAAE;MAClBc,OAAO,CAACf,KAAK,CAAC,sBAAsB,CAAC;MACrC,IAAI,CAACA,KAAK,GAAG,yDAAyD;MACtE;;IAGFe,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE,IAAI,CAACf,QAAQ,CAAC;IAEzD,IACEsD,OAAO,CACL,gDAAgD,IAAI,CAACzE,MAAM,EAAEnC,IAAI,mCAAmC,CACrG,EACD;MACAoE,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;MAE7D,IAAI,CAACjB,OAAO,GAAG,IAAI;MACnB,IAAI,CAACC,KAAK,GAAG,IAAI;MAEjB,IAAI,CAACL,aAAa,CAACrB,YAAY,CAAC,IAAI,CAAC2B,QAAQ,CAAC,CAAC6B,SAAS,CAAC;QACvDC,IAAI,EAAEA,CAAA,KAAK;UACThB,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;UAC3C,IAAI,CAACjB,OAAO,GAAG,KAAK;UACpBiD,KAAK,CAAC,8BAA8B,CAAC;UACrC,IAAI,CAAClD,MAAM,CAACsC,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;QAC1C,CAAC;QACDpC,KAAK,EAAGA,KAAK,IAAI;UACfe,OAAO,CAACf,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;UAClE,IAAI,CAACD,OAAO,GAAG,KAAK;UACpB,IAAI,CAACC,KAAK,GAAG,qCACXA,KAAK,CAACwD,OAAO,IAAI,iBACnB,EAAE;UACFR,KAAK,CAAC,kCAAkC,IAAI,CAAChD,KAAK,EAAE,CAAC;QACvD;OACD,CAAC;KACH,MAAM;MACLe,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;;EAExD;;;uBAraWvB,qBAAqB,EAAAtF,EAAA,CAAAsJ,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAAxJ,EAAA,CAAAsJ,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA1J,EAAA,CAAAsJ,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAA5J,EAAA,CAAAsJ,iBAAA,CAAAK,EAAA,CAAAE,MAAA;IAAA;EAAA;;;YAArBvE,qBAAqB;MAAAwE,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZlCpK,EAAA,CAAA+B,UAAA,IAAAuI,oCAAA,oBA+hBM;UAGNtK,EAAA,CAAA+B,UAAA,IAAAwI,oCAAA,kBAwBM;;;UAxjBHvK,EAAA,CAAAkB,UAAA,SAAAmJ,GAAA,CAAA1F,MAAA,CAAY;UAkiBZ3E,EAAA,CAAAiB,SAAA,GAAa;UAAbjB,EAAA,CAAAkB,UAAA,UAAAmJ,GAAA,CAAA1F,MAAA,CAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}