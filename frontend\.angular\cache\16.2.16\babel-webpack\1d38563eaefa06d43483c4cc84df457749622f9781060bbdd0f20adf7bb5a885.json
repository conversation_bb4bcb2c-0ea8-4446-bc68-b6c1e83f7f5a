{"ast": null, "code": "import { trigger, transition, style, animate } from '@angular/animations';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"src/app/services/authadmin.service\";\nimport * as i3 from \"src/app/services/authuser.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@app/services/theme.service\";\nimport * as i6 from \"src/app/services/data.service\";\nfunction AdminLayoutComponent_div_75_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 84)(1, \"div\", 85);\n    i0.ɵɵlistener(\"click\", function AdminLayoutComponent_div_75_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.toggleMobileMenu());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"div\", 86)(3, \"div\", 87)(4, \"div\", 88);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(5, \"svg\", 89);\n    i0.ɵɵelement(6, \"path\", 10)(7, \"path\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(8, \"span\", 90);\n    i0.ɵɵtext(9, \"DevBridge\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"button\", 91);\n    i0.ɵɵlistener(\"click\", function AdminLayoutComponent_div_75_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.toggleMobileMenu());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(11, \"svg\", 92);\n    i0.ɵɵelement(12, \"path\", 93);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(13, \"div\", 14)(14, \"nav\", 15)(15, \"a\", 94);\n    i0.ɵɵlistener(\"click\", function AdminLayoutComponent_div_75_Template_a_click_15_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.toggleMobileMenu());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(16, \"svg\", 95);\n    i0.ɵɵelement(17, \"path\", 96);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(18, \" Dashboard \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(19, \"a\", 97);\n    i0.ɵɵlistener(\"click\", function AdminLayoutComponent_div_75_Template_a_click_19_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.toggleMobileMenu());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(20, \"svg\", 95);\n    i0.ɵɵelement(21, \"path\", 98);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(22, \" profile \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(23, \"a\", 99);\n    i0.ɵɵlistener(\"click\", function AdminLayoutComponent_div_75_Template_a_click_23_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.toggleMobileMenu());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(24, \"svg\", 95);\n    i0.ɵɵelement(25, \"path\", 100);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(26, \" Reunions \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(27, \"a\", 101);\n    i0.ɵɵlistener(\"click\", function AdminLayoutComponent_div_75_Template_a_click_27_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.toggleMobileMenu());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(28, \"svg\", 95);\n    i0.ɵɵelement(29, \"path\", 102);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(30, \" Planning \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(31, \"a\", 103);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(32, \"svg\", 95);\n    i0.ɵɵelement(33, \"path\", 104);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(34, \" Projects \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(35, \"a\", 105);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(36, \"svg\", 95);\n    i0.ɵɵelement(37, \"path\", 106);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(38, \" Student Rendus \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(39, \"a\", 107);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(40, \"svg\", 95);\n    i0.ɵɵelement(41, \"path\", 108);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(42, \" \\u00C9valuations \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(43, \"a\", 109);\n    i0.ɵɵlistener(\"click\", function AdminLayoutComponent_div_75_Template_a_click_43_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.toggleMobileMenu());\n    });\n    i0.ɵɵelement(44, \"i\", 110);\n    i0.ɵɵtext(45, \" Back Home \");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction AdminLayoutComponent_i_111_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 111);\n  }\n}\nfunction AdminLayoutComponent_i_113_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 112);\n  }\n}\nfunction AdminLayoutComponent_div_125_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 113)(1, \"a\", 114);\n    i0.ɵɵlistener(\"click\", function AdminLayoutComponent_div_125_Template_a_click_1_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.openLogoutModal());\n    });\n    i0.ɵɵelementStart(2, \"div\", 88)(3, \"div\", 8);\n    i0.ɵɵelement(4, \"i\", 115)(5, \"div\", 116);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\");\n    i0.ɵɵtext(7, \"Logout\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"@fadeIn\", undefined);\n  }\n}\nfunction AdminLayoutComponent_div_145_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 117)(1, \"div\", 118)(2, \"div\", 119);\n    i0.ɵɵelement(3, \"div\", 120);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 121)(5, \"div\", 39);\n    i0.ɵɵelement(6, \"div\", 122)(7, \"div\", 123);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 124)(9, \"div\", 125)(10, \"div\", 126);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(11, \"svg\", 127);\n    i0.ɵɵelement(12, \"path\", 128);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelement(13, \"div\", 129);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 130)(15, \"h3\", 131);\n    i0.ɵɵtext(16, \" Ready to Leave? \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 132)(18, \"p\", 133);\n    i0.ɵɵtext(19, \" Are you sure you want to logout? \");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(20, \"div\", 134)(21, \"button\", 135);\n    i0.ɵɵlistener(\"click\", function AdminLayoutComponent_div_145_Template_button_click_21_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.logout());\n    });\n    i0.ɵɵelement(22, \"div\", 136)(23, \"div\", 137);\n    i0.ɵɵelementStart(24, \"span\", 138);\n    i0.ɵɵelement(25, \"i\", 139);\n    i0.ɵɵtext(26, \" Logout \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"button\", 140);\n    i0.ɵɵlistener(\"click\", function AdminLayoutComponent_div_145_Template_button_click_27_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.closeLogoutModal());\n    });\n    i0.ɵɵelementStart(28, \"span\", 141);\n    i0.ɵɵelement(29, \"i\", 142);\n    i0.ɵɵtext(30, \" Cancel \");\n    i0.ɵɵelementEnd()()()()()();\n  }\n}\nfunction AdminLayoutComponent_button_146_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 143);\n    i0.ɵɵlistener(\"click\", function AdminLayoutComponent_button_146_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r19 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r19.scrollToTop());\n    });\n    i0.ɵɵelement(1, \"div\", 144)(2, \"div\", 145);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 146);\n    i0.ɵɵelement(4, \"path\", 147);\n    i0.ɵɵelementEnd()();\n  }\n}\nconst _c0 = function () {\n  return {\n    exact: true\n  };\n};\nconst _c1 = function (a0) {\n  return {\n    \"rotate-180\": a0\n  };\n};\nexport let AdminLayoutComponent = /*#__PURE__*/(() => {\n  class AdminLayoutComponent {\n    constructor(location, authAdminService, authService, router, themeService, dataService) {\n      this.location = location;\n      this.authAdminService = authAdminService;\n      this.authService = authService;\n      this.router = router;\n      this.themeService = themeService;\n      this.dataService = dataService;\n      this.username = '';\n      this.imageProfile = '';\n      this.mobileMenuOpen = false;\n      this.userMenuOpen = false;\n      this.showLogoutModal = false;\n      this.showScrollButton = false;\n      this.currentYear = new Date().getFullYear();\n      this.subscriptions = [];\n      this.loadUserProfile();\n      this.isDarkMode$ = this.themeService.darkMode$;\n    }\n    loadUserProfile() {\n      const user = this.authAdminService.getUser();\n      this.username = user?.fullName || user?.username || '';\n      // Toujours utiliser l'image par défaut si l'image de profil est null, 'null' ou vide\n      if (user?.profileImage && user.profileImage !== 'null' && user.profileImage.trim() !== '') {\n        this.imageProfile = user.profileImage;\n      } else if (user?.image && user.image !== 'null' && user.image.trim() !== '') {\n        this.imageProfile = user.image;\n      } else {\n        this.imageProfile = 'assets/images/default-profile.png';\n      }\n      console.log('Admin layout - Image profile loaded:', this.imageProfile);\n    }\n    ngOnInit() {\n      this.checkScrollPosition();\n      // S'abonner aux changements d'image de profil\n      const profileSub = this.dataService.currentUser$.subscribe(user => {\n        if (user) {\n          this.username = user.fullName || user.username || '';\n          // Toujours utiliser l'image par défaut si l'image de profil est null, 'null' ou vide\n          if (user.profileImage && user.profileImage !== 'null' && user.profileImage.trim() !== '') {\n            this.imageProfile = user.profileImage;\n          } else if (user.image && user.image !== 'null' && user.image.trim() !== '') {\n            this.imageProfile = user.image;\n          } else {\n            this.imageProfile = 'assets/images/default-profile.png';\n          }\n          console.log('Admin layout - Image profile updated:', this.imageProfile);\n        }\n      });\n      this.subscriptions.push(profileSub);\n    }\n    ngOnDestroy() {\n      // Désabonner de tous les observables pour éviter les fuites de mémoire\n      this.subscriptions.forEach(sub => sub.unsubscribe());\n    }\n    checkScrollPosition() {\n      this.showScrollButton = window.pageYOffset > 300;\n    }\n    scrollToTop() {\n      window.scrollTo({\n        top: 0,\n        behavior: 'smooth'\n      });\n    }\n    toggleMobileMenu() {\n      this.mobileMenuOpen = !this.mobileMenuOpen;\n    }\n    toggleUserMenu() {\n      this.userMenuOpen = !this.userMenuOpen;\n    }\n    openLogoutModal() {\n      this.showLogoutModal = true;\n      this.userMenuOpen = false;\n    }\n    closeLogoutModal() {\n      this.showLogoutModal = false;\n    }\n    logout() {\n      this.authService.logout().subscribe({\n        next: () => {\n          this.userMenuOpen = false;\n          this.showLogoutModal = false;\n          this.authService.clearAuthData();\n          this.authAdminService.clearAuthData();\n          setTimeout(() => {\n            this.router.navigate(['/admin/login'], {\n              queryParams: {\n                message: 'Déconnexion réussie'\n              },\n              replaceUrl: true\n            });\n          }, 100);\n        },\n        error: err => {\n          console.error('Logout error:', err);\n          this.authService.clearAuthData();\n          this.authAdminService.clearAuthData();\n          setTimeout(() => {\n            this.router.navigate(['/admin/login'], {\n              queryParams: {\n                message: 'Déconnexion effectuée'\n              },\n              replaceUrl: true\n            });\n          }, 100);\n        }\n      });\n    }\n    goBack() {\n      this.location.back();\n    }\n    toggleDarkMode() {\n      this.themeService.toggleDarkMode();\n    }\n    static {\n      this.ɵfac = function AdminLayoutComponent_Factory(t) {\n        return new (t || AdminLayoutComponent)(i0.ɵɵdirectiveInject(i1.Location), i0.ɵɵdirectiveInject(i2.AuthadminService), i0.ɵɵdirectiveInject(i3.AuthuserService), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i5.ThemeService), i0.ɵɵdirectiveInject(i6.DataService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AdminLayoutComponent,\n        selectors: [[\"app-admin-layout\"]],\n        hostBindings: function AdminLayoutComponent_HostBindings(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵlistener(\"scroll\", function AdminLayoutComponent_scroll_HostBindingHandler() {\n              return ctx.checkScrollPosition();\n            }, false, i0.ɵɵresolveWindow);\n          }\n        },\n        decls: 147,\n        vars: 24,\n        consts: [[1, \"flex\", \"h-screen\", \"main-grid-container\", \"futuristic-layout\"], [1, \"background-grid\"], [1, \"hidden\", \"md:flex\", \"md:flex-shrink-0\"], [1, \"flex\", \"flex-col\", \"w-64\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"border-r\", \"border-[#edf1f4]\", \"dark:border-[#2a2a2a]\", \"backdrop-blur-sm\"], [1, \"flex\", \"items-center\", \"justify-center\", \"h-16\", \"px-4\", \"relative\", \"overflow-hidden\"], [1, \"absolute\", \"-top-6\", \"-left-6\", \"w-12\", \"h-12\", \"bg-gradient-to-br\", \"from-[#4f5fad]/20\", \"to-transparent\", \"rounded-full\"], [1, \"absolute\", \"-bottom-6\", \"-right-6\", \"w-12\", \"h-12\", \"bg-gradient-to-tl\", \"from-[#4f5fad]/20\", \"to-transparent\", \"rounded-full\"], [1, \"flex\", \"items-center\", \"relative\", \"z-10\"], [1, \"relative\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-8\", \"w-8\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"transform\", \"rotate-12\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/20\", \"dark:bg-[#6d78c9]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"ml-2\", \"text-xl\", \"font-bold\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\"], [1, \"flex\", \"flex-col\", \"flex-grow\", \"px-4\", \"py-4\"], [1, \"flex-1\", \"space-y-2\"], [\"routerLink\", \"/admin/dashboard\", \"routerLinkActive\", \"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\", 1, \"sidebar-nav-link\", \"group\", \"flex\", \"items-center\", \"px-4\", \"py-3\", \"text-sm\", \"font-medium\", \"rounded-l-md\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"transition-all\"], [1, \"absolute\", \"inset-0\", \"w-1\", \"bg-gradient-to-b\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\"], [1, \"relative\", \"z-10\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-th-large\", \"h-5\", \"w-5\", \"mr-3\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"group-hover:text-[#3d4a85]\", \"dark:group-hover:text-[#4f5fad]\", \"transition-all\", \"group-hover:scale-110\"], [\"routerLink\", \"/admin/profile\", \"routerLinkActive\", \"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\", 1, \"sidebar-nav-link\", \"group\", \"flex\", \"items-center\", \"px-4\", \"py-3\", \"text-sm\", \"font-medium\", \"rounded-l-md\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"transition-all\"], [1, \"fas\", \"fa-user-shield\", \"h-5\", \"w-5\", \"mr-3\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"group-hover:text-[#3d4a85]\", \"dark:group-hover:text-[#4f5fad]\", \"transition-all\", \"group-hover:scale-110\"], [\"routerLink\", \"/admin/reunions\", \"routerLinkActive\", \"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\", 1, \"sidebar-nav-link\", \"group\", \"flex\", \"items-center\", \"px-4\", \"py-3\", \"text-sm\", \"font-medium\", \"rounded-l-md\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"transition-all\"], [1, \"fas\", \"fa-users-cog\", \"h-5\", \"w-5\", \"mr-3\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"group-hover:text-[#3d4a85]\", \"dark:group-hover:text-[#4f5fad]\", \"transition-all\", \"group-hover:scale-110\"], [\"routerLink\", \"/admin/plannings\", \"routerLinkActive\", \"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\", 1, \"sidebar-nav-link\", \"group\", \"flex\", \"items-center\", \"px-4\", \"py-3\", \"text-sm\", \"font-medium\", \"rounded-l-md\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"transition-all\"], [1, \"far\", \"fa-calendar-check\", \"h-5\", \"w-5\", \"mr-3\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"group-hover:text-[#3d4a85]\", \"dark:group-hover:text-[#4f5fad]\", \"transition-all\", \"group-hover:scale-110\"], [\"routerLink\", \"/admin/projects\", \"routerLinkActive\", \"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\", 1, \"sidebar-nav-link\", \"group\", \"flex\", \"items-center\", \"px-4\", \"py-3\", \"text-sm\", \"font-medium\", \"rounded-l-md\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"transition-all\"], [1, \"fas\", \"fa-rocket\", \"h-5\", \"w-5\", \"mr-3\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"group-hover:text-[#3d4a85]\", \"dark:group-hover:text-[#4f5fad]\", \"transition-all\", \"group-hover:scale-110\"], [\"routerLink\", \"/admin/projects/rendus\", \"routerLinkActive\", \"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\", 1, \"sidebar-nav-link\", \"group\", \"flex\", \"items-center\", \"px-4\", \"py-3\", \"text-sm\", \"font-medium\", \"rounded-l-md\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"transition-all\"], [1, \"fas\", \"fa-file-upload\", \"h-5\", \"w-5\", \"mr-3\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"group-hover:text-[#3d4a85]\", \"dark:group-hover:text-[#4f5fad]\", \"transition-all\", \"group-hover:scale-110\"], [\"routerLink\", \"/admin/projects/evaluations\", \"routerLinkActive\", \"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\", 1, \"sidebar-nav-link\", \"group\", \"flex\", \"items-center\", \"px-4\", \"py-3\", \"text-sm\", \"font-medium\", \"rounded-l-md\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"transition-all\"], [1, \"fas\", \"fa-clipboard-check\", \"h-5\", \"w-5\", \"mr-3\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"group-hover:text-[#3d4a85]\", \"dark:group-hover:text-[#4f5fad]\", \"transition-all\", \"group-hover:scale-110\"], [\"routerLink\", \"/\", \"routerLinkActive\", \"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\", 1, \"sidebar-nav-link\", \"group\", \"flex\", \"items-center\", \"px-4\", \"py-3\", \"text-sm\", \"font-medium\", \"rounded-l-md\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"transition-all\", 3, \"routerLinkActiveOptions\"], [1, \"fas\", \"fa-home\", \"h-5\", \"w-5\", \"mr-3\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"group-hover:text-[#3d4a85]\", \"dark:group-hover:text-[#4f5fad]\", \"transition-colors\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/20\", \"dark:bg-[#6d78c9]/20\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\", \"blur-md\", \"rounded-full\"], [\"class\", \"md:hidden fixed inset-0 z-40\", 4, \"ngIf\"], [1, \"flex-1\", \"flex\", \"flex-col\", \"overflow-hidden\"], [1, \"bg-white\", \"dark:bg-[#1e1e1e]\", \"shadow-md\", \"dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)]\", \"z-10\", \"border-b\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"backdrop-blur-sm\"], [1, \"flex\", \"items-center\", \"justify-between\", \"h-16\", \"px-4\", \"relative\"], [1, \"absolute\", \"inset-0\", \"overflow-hidden\", \"pointer-events-none\"], [1, \"absolute\", \"top-0\", \"left-1/4\", \"w-px\", \"h-full\", \"bg-gradient-to-b\", \"from-transparent\", \"via-[#4f5fad]/10\", \"dark:via-[#6d78c9]/5\", \"to-transparent\"], [1, \"absolute\", \"top-0\", \"right-1/3\", \"w-px\", \"h-full\", \"bg-gradient-to-b\", \"from-transparent\", \"via-[#4f5fad]/5\", \"dark:via-[#6d78c9]/3\", \"to-transparent\"], [1, \"md:hidden\", \"flex\", \"items-center\", \"justify-center\", \"h-8\", \"w-8\", \"rounded-md\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"focus:outline-none\", \"transition-colors\", \"relative\", \"group\", 3, \"click\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/10\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\", \"rounded-md\", \"blur-md\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-5\", \"w-5\", \"relative\", \"z-10\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M4 6h16M4 12h16M4 18h16\"], [1, \"text-sm\", \"font-medium\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"ml-2\", \"relative\", \"z-10\"], [1, \"hidden\", \"md:flex\", \"items-center\", \"px-3\", \"py-1\", \"rounded-md\", \"text-sm\", \"font-medium\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"transition-all\", \"duration-200\", \"relative\", \"group\", \"overflow-hidden\", 3, \"click\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-5\", \"w-5\", \"mr-1\", \"group-hover:scale-110\", \"transition-transform\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M10 19l-7-7m0 0l7-7m-7 7h18\"], [1, \"flex-1\", \"max-w-md\", \"ml-4\", \"md:ml-6\"], [1, \"relative\", \"group\"], [1, \"absolute\", \"inset-y-0\", \"left-0\", \"pl-3\", \"flex\", \"items-center\", \"pointer-events-none\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-5\", \"w-5\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"group-focus-within:text-[#4f5fad]\", \"dark:group-focus-within:text-[#6d78c9]\", \"transition-colors\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"], [\"type\", \"text\", \"placeholder\", \"Search...\", 1, \"block\", \"w-full\", \"pl-10\", \"pr-3\", \"py-2\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"rounded-md\", \"leading-5\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"placeholder-[#bdc6cc]\", \"dark:placeholder-[#6d6870]\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-[#4f5fad]\", \"dark:focus:ring-[#6d78c9]\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"transition-all\"], [1, \"absolute\", \"inset-y-0\", \"left-0\", \"pl-3\", \"flex\", \"items-center\", \"pointer-events-none\", \"opacity-0\", \"group-focus-within:opacity-100\", \"transition-opacity\"], [1, \"w-0.5\", \"h-4\", \"bg-gradient-to-b\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"rounded-full\"], [1, \"ml-4\", \"flex\", \"items-center\", \"md:ml-6\"], [\"aria-label\", \"Toggle dark mode\", 1, \"flex\", \"items-center\", \"justify-center\", \"h-8\", \"w-8\", \"rounded-full\", \"bg-[#edf1f4]\", \"dark:bg-[#2a2a2a]\", \"hover:bg-[#dce4ec]\", \"dark:hover:bg-[#3a3a3a]\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"mr-3\", \"transition-all\", \"duration-300\", \"relative\", \"overflow-hidden\", \"group\", 3, \"click\"], [1, \"absolute\", \"inset-0\", \"rounded-full\", \"overflow-hidden\"], [1, \"absolute\", \"inset-0\", \"rounded-full\", \"border\", \"border-[#4f5fad]/20\", \"dark:border-[#6d78c9]/20\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\"], [1, \"absolute\", \"-inset-1\", \"bg-gradient-to-r\", \"from-[#4f5fad]/0\", \"via-[#4f5fad]/30\", \"to-[#4f5fad]/0\", \"dark:from-[#6d78c9]/0\", \"dark:via-[#6d78c9]/30\", \"dark:to-[#6d78c9]/0\", \"opacity-0\", \"group-hover:opacity-100\", \"blur-sm\", \"animate-shine\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/10\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\", \"blur-md\"], [1, \"relative\", \"z-10\", \"transition-all\", \"duration-500\", \"ease-in-out\", 3, \"ngClass\"], [\"class\", \"far fa-moon group-hover:scale-110 transition-transform\", 4, \"ngIf\"], [\"class\", \"far fa-sun group-hover:scale-110 transition-transform\", 4, \"ngIf\"], [1, \"flex\", \"items-center\", \"text-sm\", \"rounded-full\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-[#4f5fad]\", \"dark:focus:ring-[#6d78c9]\", \"transition-all\", \"group\", 3, \"click\"], [1, \"sr-only\"], [1, \"hidden\", \"md:inline-block\", \"mr-2\", \"text-sm\", \"font-medium\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"group-hover:text-[#4f5fad]\", \"dark:group-hover:text-[#6d78c9]\", \"transition-colors\"], [1, \"h-8\", \"w-8\", \"rounded-full\", \"overflow-hidden\", \"flex\", \"items-center\", \"justify-center\", \"border-2\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]\", \"group-hover:border-[#3d4a85]\", \"dark:group-hover:border-[#4f5fad]\", \"transition-colors\", \"relative\"], [\"alt\", \"Profile\", 1, \"h-full\", \"w-full\", \"object-cover\", 3, \"src\"], [\"class\", \"origin-top-right absolute right-0 mt-2 w-48 rounded-lg shadow-lg dark:shadow-[0_10px_25px_rgba(0,0,0,0.3)] bg-white dark:bg-[#1e1e1e] border border-[#edf1f4]/50 dark:border-[#2a2a2a] py-1 z-50 backdrop-blur-sm\", 4, \"ngIf\"], [1, \"flex-1\", \"overflow-y-auto\", \"bg-[#edf1f4]\", \"dark:bg-[#121212]\", \"p-4\", \"md:p-6\", \"relative\"], [1, \"absolute\", \"top-[15%]\", \"left-[10%]\", \"w-64\", \"h-64\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"bottom-[20%]\", \"right-[10%]\", \"w-80\", \"h-80\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"relative\", \"z-10\"], [1, \"bg-white\", \"dark:bg-[#1e1e1e]\", \"border-t\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"py-4\", \"relative\", \"overflow-hidden\"], [1, \"container\", \"mx-auto\", \"px-4\", \"text-center\", \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"relative\", \"z-10\"], [1, \"flex\", \"items-center\", \"justify-center\"], [1, \"relative\", \"mr-2\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-4\", \"w-4\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\"], [\"class\", \"fixed inset-0 overflow-y-auto z-50\", 4, \"ngIf\"], [\"class\", \"fixed bottom-6 right-6 p-3 rounded-full shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#4f5fad] dark:focus:ring-[#6d78c9] overflow-hidden group\", 3, \"click\", 4, \"ngIf\"], [1, \"md:hidden\", \"fixed\", \"inset-0\", \"z-40\"], [1, \"fixed\", \"inset-0\", \"bg-gray-600\", \"bg-opacity-75\", 3, \"click\"], [1, \"relative\", \"flex\", \"flex-col\", \"w-72\", \"bg-white\", \"h-full\"], [1, \"flex\", \"items-center\", \"justify-between\", \"h-16\", \"px-4\"], [1, \"flex\", \"items-center\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-8\", \"w-8\", \"text-[#4f5fad]\"], [1, \"ml-2\", \"text-xl\", \"font-bold\", \"text-[#4f5fad]\"], [1, \"text-[#6d6870]\", 3, \"click\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-6\", \"w-6\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M6 18L18 6M6 6l12 12\"], [\"routerLink\", \"/admin/dashboard\", 1, \"group\", \"flex\", \"items-center\", \"px-4\", \"py-3\", \"text-sm\", \"font-medium\", \"rounded-md\", \"text-[#6d6870]\", \"hover:bg-[#edf1f4]\", \"hover:text-[#4f5fad]\", \"transition-colors\", 3, \"click\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-5\", \"w-5\", \"mr-3\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z\"], [\"routerLink\", \"/admin/profile\", 1, \"group\", \"flex\", \"items-center\", \"px-4\", \"py-3\", \"text-sm\", \"font-medium\", \"rounded-md\", \"text-[#6d6870]\", \"hover:bg-[#edf1f4]\", \"hover:text-[#4f5fad]\", \"transition-colors\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z\"], [\"routerLink\", \"/admin/reunions\", 1, \"group\", \"flex\", \"items-center\", \"px-4\", \"py-3\", \"text-sm\", \"font-medium\", \"rounded-md\", \"text-[#6d6870]\", \"hover:bg-[#edf1f4]\", \"hover:text-[#4f5fad]\", \"transition-colors\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"], [\"routerLink\", \"/admin/plannings\", 1, \"group\", \"flex\", \"items-center\", \"px-4\", \"py-3\", \"text-sm\", \"font-medium\", \"rounded-md\", \"text-[#6d6870]\", \"hover:bg-[#edf1f4]\", \"hover:text-[#4f5fad]\", \"transition-colors\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"], [\"routerLink\", \"/admin/projects\", \"routerLinkActive\", \"bg-[#edf1f4] text-[#4f5fad] font-medium\", 1, \"group\", \"flex\", \"items-center\", \"px-4\", \"py-3\", \"text-sm\", \"font-medium\", \"rounded-md\", \"text-[#6d6870]\", \"hover:bg-[#edf1f4]\", \"hover:text-[#4f5fad]\", \"transition-colors\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z\"], [\"routerLink\", \"/admin/projects/rendus\", \"routerLinkActive\", \"bg-[#edf1f4] text-[#4f5fad] font-medium\", 1, \"group\", \"flex\", \"items-center\", \"px-4\", \"py-3\", \"text-sm\", \"font-medium\", \"rounded-md\", \"text-[#6d6870]\", \"hover:bg-[#edf1f4]\", \"hover:text-[#4f5fad]\", \"transition-colors\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4\"], [\"routerLink\", \"/admin/projects/evaluations\", \"routerLinkActive\", \"bg-[#edf1f4] text-[#4f5fad] font-medium\", 1, \"group\", \"flex\", \"items-center\", \"px-4\", \"py-3\", \"text-sm\", \"font-medium\", \"rounded-md\", \"text-[#6d6870]\", \"hover:bg-[#edf1f4]\", \"hover:text-[#4f5fad]\", \"transition-colors\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\"], [\"routerLink\", \"/\", 1, \"group\", \"flex\", \"items-center\", \"px-4\", \"py-3\", \"text-sm\", \"font-medium\", \"rounded-md\", \"text-[#6d6870]\", \"hover:bg-[#edf1f4]\", \"hover:text-[#4f5fad]\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-home\", \"h-5\", \"w-5\", \"mr-3\", \"text-[#4f5fad]\"], [1, \"far\", \"fa-moon\", \"group-hover:scale-110\", \"transition-transform\"], [1, \"far\", \"fa-sun\", \"group-hover:scale-110\", \"transition-transform\"], [1, \"origin-top-right\", \"absolute\", \"right-0\", \"mt-2\", \"w-48\", \"rounded-lg\", \"shadow-lg\", \"dark:shadow-[0_10px_25px_rgba(0,0,0,0.3)]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"py-1\", \"z-50\", \"backdrop-blur-sm\"], [1, \"block\", \"px-4\", \"py-2\", \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"transition-colors\", \"group\", \"cursor-pointer\", 3, \"click\"], [1, \"fas\", \"fa-sign-out-alt\", \"mr-2\", \"text-[#ff6b69]\", \"dark:text-[#ff8785]\", \"group-hover:scale-110\", \"transition-transform\"], [1, \"absolute\", \"inset-0\", \"bg-[#ff6b69]/20\", \"dark:bg-[#ff8785]/20\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\", \"blur-md\", \"rounded-full\"], [1, \"fixed\", \"inset-0\", \"overflow-y-auto\", \"z-50\"], [1, \"flex\", \"items-center\", \"justify-center\", \"min-h-screen\", \"pt-4\", \"px-4\", \"pb-20\", \"text-center\", \"sm:block\", \"sm:p-0\"], [\"aria-hidden\", \"true\", 1, \"fixed\", \"inset-0\", \"transition-opacity\"], [1, \"absolute\", \"inset-0\", \"bg-black/50\", \"dark:bg-black/70\", \"backdrop-blur-sm\"], [1, \"inline-block\", \"align-bottom\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"rounded-lg\", \"text-left\", \"overflow-hidden\", \"shadow-xl\", \"dark:shadow-[0_10px_25px_rgba(0,0,0,0.3)]\", \"transform\", \"transition-all\", \"sm:my-8\", \"sm:align-middle\", \"sm:max-w-lg\", \"sm:w-full\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"relative\"], [1, \"absolute\", \"top-[10%]\", \"left-[5%]\", \"w-32\", \"h-32\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-2xl\"], [1, \"absolute\", \"bottom-[10%]\", \"right-[5%]\", \"w-40\", \"h-40\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#ff6b69]/5\", \"to-transparent\", \"dark:from-[#ff8785]/3\", \"dark:to-transparent\", \"blur-2xl\"], [1, \"bg-white\", \"dark:bg-[#1e1e1e]\", \"px-4\", \"pt-5\", \"pb-4\", \"sm:p-6\", \"sm:pb-4\", \"relative\", \"z-10\"], [1, \"sm:flex\", \"sm:items-start\"], [1, \"mx-auto\", \"flex-shrink-0\", \"flex\", \"items-center\", \"justify-center\", \"h-12\", \"w-12\", \"rounded-full\", \"bg-[#ff6b69]/10\", \"dark:bg-[#ff6b69]/5\", \"sm:mx-0\", \"sm:h-10\", \"sm:w-10\", \"relative\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-6\", \"w-6\", \"text-[#ff6b69]\", \"dark:text-[#ff8785]\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z\"], [1, \"absolute\", \"inset-0\", \"bg-[#ff6b69]/20\", \"dark:bg-[#ff8785]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"mt-3\", \"text-center\", \"sm:mt-0\", \"sm:ml-4\", \"sm:text-left\"], [1, \"text-lg\", \"leading-6\", \"font-medium\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\"], [1, \"mt-2\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [1, \"bg-[#edf1f4]\", \"dark:bg-[#161616]\", \"px-4\", \"py-3\", \"sm:px-6\", \"sm:flex\", \"sm:flex-row-reverse\", \"border-t\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"relative\", \"z-10\"], [\"type\", \"button\", 1, \"w-full\", \"inline-flex\", \"justify-center\", \"rounded-md\", \"px-4\", \"py-2\", \"text-base\", \"font-medium\", \"text-white\", \"sm:ml-3\", \"sm:w-auto\", \"sm:text-sm\", \"relative\", \"overflow-hidden\", \"group\", 3, \"click\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#ff6b69]\", \"to-[#ff8785]\", \"dark:from-[#ff6b69]\", \"dark:to-[#ff8785]\", \"rounded-md\", \"transition-transform\", \"duration-300\", \"group-hover:scale-105\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#ff6b69]\", \"to-[#ff8785]\", \"dark:from-[#ff6b69]\", \"dark:to-[#ff8785]\", \"rounded-md\", \"opacity-0\", \"group-hover:opacity-100\", \"blur-xl\", \"transition-opacity\", \"duration-300\"], [1, \"relative\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-sign-out-alt\", \"mr-1.5\"], [\"type\", \"button\", 1, \"mt-3\", \"w-full\", \"inline-flex\", \"justify-center\", \"rounded-md\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"px-4\", \"py-2\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-base\", \"font-medium\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-[#4f5fad]\", \"dark:focus:ring-[#6d78c9]\", \"sm:mt-0\", \"sm:ml-3\", \"sm:w-auto\", \"sm:text-sm\", \"transition-all\", \"group\", 3, \"click\"], [1, \"relative\", \"flex\", \"items-center\", \"group-hover:text-[#4f5fad]\", \"dark:group-hover:text-[#6d78c9]\", \"transition-colors\"], [1, \"fas\", \"fa-times\", \"mr-1.5\"], [1, \"fixed\", \"bottom-6\", \"right-6\", \"p-3\", \"rounded-full\", \"shadow-lg\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-offset-2\", \"focus:ring-[#4f5fad]\", \"dark:focus:ring-[#6d78c9]\", \"overflow-hidden\", \"group\", 3, \"click\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#3d4a85]\", \"dark:to-[#6d78c9]\", \"rounded-full\", \"transition-transform\", \"duration-300\", \"group-hover:scale-110\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#3d4a85]\", \"dark:to-[#6d78c9]\", \"rounded-full\", \"opacity-0\", \"group-hover:opacity-100\", \"blur-xl\", \"transition-opacity\", \"duration-300\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-5\", \"w-5\", \"text-white\", \"relative\", \"z-10\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M5 10l7-7m0 0l7 7m-7-7v18\"]],\n        template: function AdminLayoutComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0);\n            i0.ɵɵpipe(1, \"async\");\n            i0.ɵɵelement(2, \"div\", 1);\n            i0.ɵɵelementStart(3, \"div\", 2)(4, \"div\", 3)(5, \"div\", 4);\n            i0.ɵɵelement(6, \"div\", 5)(7, \"div\", 6);\n            i0.ɵɵelementStart(8, \"div\", 7)(9, \"div\", 8);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(10, \"svg\", 9);\n            i0.ɵɵelement(11, \"path\", 10)(12, \"path\", 11);\n            i0.ɵɵelementEnd();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelement(13, \"div\", 12);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(14, \"span\", 13);\n            i0.ɵɵtext(15, \"DevBridge\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(16, \"div\", 14)(17, \"nav\", 15)(18, \"a\", 16);\n            i0.ɵɵelement(19, \"span\", 17);\n            i0.ɵɵelementStart(20, \"div\", 18)(21, \"div\", 8);\n            i0.ɵɵelement(22, \"i\", 19);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(23, \"span\", 8);\n            i0.ɵɵtext(24, \"Dashboard\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(25, \"a\", 20);\n            i0.ɵɵelement(26, \"span\", 17);\n            i0.ɵɵelementStart(27, \"div\", 18)(28, \"div\", 8);\n            i0.ɵɵelement(29, \"i\", 21);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(30, \"span\", 8);\n            i0.ɵɵtext(31, \"Profile\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(32, \"a\", 22);\n            i0.ɵɵelement(33, \"span\", 17);\n            i0.ɵɵelementStart(34, \"div\", 18)(35, \"div\", 8);\n            i0.ɵɵelement(36, \"i\", 23);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(37, \"span\", 8);\n            i0.ɵɵtext(38, \"Reunions\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(39, \"a\", 24);\n            i0.ɵɵelement(40, \"span\", 17);\n            i0.ɵɵelementStart(41, \"div\", 18)(42, \"div\", 8);\n            i0.ɵɵelement(43, \"i\", 25);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(44, \"span\", 8);\n            i0.ɵɵtext(45, \"Plannings\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(46, \"a\", 26);\n            i0.ɵɵelement(47, \"span\", 17);\n            i0.ɵɵelementStart(48, \"div\", 18)(49, \"div\", 8);\n            i0.ɵɵelement(50, \"i\", 27);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(51, \"span\", 8);\n            i0.ɵɵtext(52, \"Projects\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(53, \"a\", 28);\n            i0.ɵɵelement(54, \"span\", 17);\n            i0.ɵɵelementStart(55, \"div\", 18)(56, \"div\", 8);\n            i0.ɵɵelement(57, \"i\", 29);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(58, \"span\", 8);\n            i0.ɵɵtext(59, \"Student Rendus\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(60, \"a\", 30);\n            i0.ɵɵelement(61, \"span\", 17);\n            i0.ɵɵelementStart(62, \"div\", 18)(63, \"div\", 8);\n            i0.ɵɵelement(64, \"i\", 31);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(65, \"span\", 8);\n            i0.ɵɵtext(66, \"\\u00C9valuations\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(67, \"a\", 32);\n            i0.ɵɵelement(68, \"span\", 17);\n            i0.ɵɵelementStart(69, \"div\", 18)(70, \"div\", 8);\n            i0.ɵɵelement(71, \"i\", 33)(72, \"div\", 34);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(73, \"span\", 8);\n            i0.ɵɵtext(74, \"Back Home\");\n            i0.ɵɵelementEnd()()()()()()();\n            i0.ɵɵtemplate(75, AdminLayoutComponent_div_75_Template, 46, 0, \"div\", 35);\n            i0.ɵɵelementStart(76, \"div\", 36)(77, \"header\", 37)(78, \"div\", 38)(79, \"div\", 39);\n            i0.ɵɵelement(80, \"div\", 40)(81, \"div\", 41);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(82, \"button\", 42);\n            i0.ɵɵlistener(\"click\", function AdminLayoutComponent_Template_button_click_82_listener() {\n              return ctx.toggleMobileMenu();\n            });\n            i0.ɵɵelement(83, \"div\", 43);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(84, \"svg\", 44);\n            i0.ɵɵelement(85, \"path\", 45);\n            i0.ɵɵelementEnd();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(86, \"span\", 46);\n            i0.ɵɵtext(87, \"Menu\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(88, \"button\", 47);\n            i0.ɵɵlistener(\"click\", function AdminLayoutComponent_Template_button_click_88_listener() {\n              return ctx.goBack();\n            });\n            i0.ɵɵelement(89, \"div\", 43);\n            i0.ɵɵelementStart(90, \"div\", 18);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(91, \"svg\", 48);\n            i0.ɵɵelement(92, \"path\", 49);\n            i0.ɵɵelementEnd();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(93, \"span\");\n            i0.ɵɵtext(94, \"Back\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(95, \"div\", 50)(96, \"div\", 51)(97, \"div\", 52);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(98, \"svg\", 53);\n            i0.ɵɵelement(99, \"path\", 54);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelement(100, \"input\", 55);\n            i0.ɵɵelementStart(101, \"div\", 56);\n            i0.ɵɵelement(102, \"div\", 57);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(103, \"div\", 58)(104, \"button\", 59);\n            i0.ɵɵlistener(\"click\", function AdminLayoutComponent_Template_button_click_104_listener() {\n              return ctx.toggleDarkMode();\n            });\n            i0.ɵɵelementStart(105, \"div\", 60);\n            i0.ɵɵelement(106, \"div\", 61)(107, \"div\", 62);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(108, \"div\", 63);\n            i0.ɵɵelementStart(109, \"div\", 64);\n            i0.ɵɵpipe(110, \"async\");\n            i0.ɵɵtemplate(111, AdminLayoutComponent_i_111_Template, 1, 0, \"i\", 65);\n            i0.ɵɵpipe(112, \"async\");\n            i0.ɵɵtemplate(113, AdminLayoutComponent_i_113_Template, 1, 0, \"i\", 66);\n            i0.ɵɵpipe(114, \"async\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(115, \"div\", 58)(116, \"div\", 8)(117, \"button\", 67);\n            i0.ɵɵlistener(\"click\", function AdminLayoutComponent_Template_button_click_117_listener() {\n              return ctx.toggleUserMenu();\n            });\n            i0.ɵɵelementStart(118, \"span\", 68);\n            i0.ɵɵtext(119, \"Open user menu\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(120, \"span\", 69);\n            i0.ɵɵtext(121);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(122, \"div\", 70);\n            i0.ɵɵelement(123, \"div\", 63)(124, \"img\", 71);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(125, AdminLayoutComponent_div_125_Template, 8, 1, \"div\", 72);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(126, \"main\", 73)(127, \"div\", 39);\n            i0.ɵɵelement(128, \"div\", 74)(129, \"div\", 75);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(130, \"div\", 76);\n            i0.ɵɵelement(131, \"router-outlet\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(132, \"footer\", 77)(133, \"div\", 39);\n            i0.ɵɵelement(134, \"div\", 40)(135, \"div\", 41);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(136, \"div\", 78)(137, \"div\", 79)(138, \"div\", 80);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(139, \"svg\", 81);\n            i0.ɵɵelement(140, \"path\", 10)(141, \"path\", 11);\n            i0.ɵɵelementEnd();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelement(142, \"div\", 12);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(143, \"span\");\n            i0.ɵɵtext(144);\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵtemplate(145, AdminLayoutComponent_div_145_Template, 31, 0, \"div\", 82);\n            i0.ɵɵtemplate(146, AdminLayoutComponent_button_146_Template, 5, 0, \"button\", 83);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵclassProp(\"dark\", i0.ɵɵpipeBind1(1, 13, ctx.isDarkMode$));\n            i0.ɵɵadvance(67);\n            i0.ɵɵproperty(\"routerLinkActiveOptions\", i0.ɵɵpureFunction0(21, _c0));\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"ngIf\", ctx.mobileMenuOpen);\n            i0.ɵɵadvance(34);\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(22, _c1, i0.ɵɵpipeBind1(110, 15, ctx.isDarkMode$)));\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", !i0.ɵɵpipeBind1(112, 17, ctx.isDarkMode$));\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(114, 19, ctx.isDarkMode$));\n            i0.ɵɵadvance(8);\n            i0.ɵɵtextInterpolate(ctx.username);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"src\", ctx.imageProfile, i0.ɵɵsanitizeUrl);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.userMenuOpen);\n            i0.ɵɵadvance(19);\n            i0.ɵɵtextInterpolate1(\"\\u00A9 \", ctx.currentYear, \" DevBridge. All rights reserved.\");\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.showLogoutModal);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.showScrollButton);\n          }\n        },\n        dependencies: [i1.NgClass, i1.NgIf, i4.RouterOutlet, i4.RouterLink, i4.RouterLinkActive, i1.AsyncPipe],\n        styles: [\".notification-message[_ngcontent-%COMP%]{position:fixed;top:20px;left:50%;transform:translate(-50%);background-color:#4caf50;color:#fff;padding:15px 25px;border-radius:4px;box-shadow:0 2px 10px #0000001a;z-index:1000;animation:_ngcontent-%COMP%_fadeInOut 5s forwards}@keyframes _ngcontent-%COMP%_fadeInOut{0%{opacity:0;top:0}10%{opacity:1;top:20px}90%{opacity:1;top:20px}to{opacity:0;top:0}}.back-button[_ngcontent-%COMP%]{transition:all .2s ease}.back-button[_ngcontent-%COMP%]:hover{transform:translate(-2px)}.back-button[_ngcontent-%COMP%]:active{transform:translate(-4px)}.sidebar-nav-link[_ngcontent-%COMP%]{position:relative;overflow:hidden}.sidebar-nav-link[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;inset:0;border-radius:.375rem 0 0 .375rem;border:2px solid rgba(79,95,173,.1);pointer-events:none}.dark[_ngcontent-%COMP%]   .sidebar-nav-link[_ngcontent-%COMP%]:before{border-color:#6d78c91a}.sidebar-nav-link.active[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;top:0;bottom:0;right:0;width:.5rem;background:linear-gradient(to bottom,#4f5fad,#00f7ff,#4f5fad);border-radius:0 .375rem .375rem 0;animation:_ngcontent-%COMP%_pulse 2s infinite;box-shadow:0 0 15px #00f7ffb3}.dark[_ngcontent-%COMP%]   .sidebar-nav-link.active[_ngcontent-%COMP%]:after{background:linear-gradient(to bottom,#6d78c9,#00f7ff,#6d78c9);box-shadow:0 0 15px #00f7ffb3}@keyframes _ngcontent-%COMP%_pulse{0%{opacity:.7}50%{opacity:1}to{opacity:.7}}.sidebar-nav-link.active[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;bottom:0;right:0;width:.5rem;background:linear-gradient(to bottom,#4f5fad,#00f7ff,#4f5fad);border-radius:0 .375rem .375rem 0;filter:blur(8px);transform:scale(1.5);opacity:.5;animation:_ngcontent-%COMP%_pulse 2s infinite}.dark[_ngcontent-%COMP%]   .sidebar-nav-link.active[_ngcontent-%COMP%]:before{background:linear-gradient(to bottom,#6d78c9,#00f7ff,#6d78c9)}.conversation-item[_ngcontent-%COMP%], .user-item[_ngcontent-%COMP%]{position:relative;overflow:hidden;transition:all .3s ease}.conversation-item.active[_ngcontent-%COMP%], .conversation-item[_ngcontent-%COMP%]:hover, .user-item.active[_ngcontent-%COMP%], .user-item[_ngcontent-%COMP%]:hover{border-color:#ffc10780!important}.conversation-item.active[_ngcontent-%COMP%]:after, .user-item.active[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;top:0;bottom:0;right:0;width:.25rem;background:linear-gradient(to bottom,#ffc107,#ffeb3b,#ffc107);border-radius:0 .375rem .375rem 0;animation:_ngcontent-%COMP%_pulse 2s infinite;box-shadow:0 0 15px #ffc107b3}.conversation-item.active[_ngcontent-%COMP%]:before, .user-item.active[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;bottom:0;right:0;width:.25rem;background:linear-gradient(to bottom,#ffc107,#ffeb3b,#ffc107);border-radius:0 .375rem .375rem 0;filter:blur(8px);transform:scale(1.5);opacity:.5;animation:_ngcontent-%COMP%_pulse 2s infinite}.conversation-item.unread[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;top:50%;right:1rem;transform:translateY(-50%);width:.5rem;height:.5rem;background-color:#ffc107;border-radius:50%;box-shadow:0 0 10px #ffc107b3}\"],\n        data: {\n          animation: [trigger('fadeIn', [transition(':enter', [style({\n            opacity: 0,\n            transform: 'translateY(-10px)'\n          }), animate('150ms ease-out', style({\n            opacity: 1,\n            transform: 'translateY(0)'\n          }))]), transition(':leave', [animate('100ms ease-in', style({\n            opacity: 0,\n            transform: 'translateY(-10px)'\n          }))])])]\n        }\n      });\n    }\n  }\n  return AdminLayoutComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}