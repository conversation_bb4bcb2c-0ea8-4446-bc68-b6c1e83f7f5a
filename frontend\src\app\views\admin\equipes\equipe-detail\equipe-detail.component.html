<div class="min-h-screen bg-[#f0f4f8] dark:bg-[#0a0a0a] relative overflow-hidden" *ngIf="equipe">
  <!-- Background decorative elements -->
  <div class="absolute inset-0 overflow-hidden pointer-events-none">
    <div
      class="absolute top-[15%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-br from-[#dac4ea]/5 to-transparent dark:from-[#00f7ff]/3 dark:to-transparent blur-3xl"
    ></div>
    <div
      class="absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#dac4ea]/5 to-transparent dark:from-[#00f7ff]/3 dark:to-transparent blur-3xl"
    ></div>

    <!-- Grid pattern -->
    <div class="absolute inset-0 opacity-5 dark:opacity-[0.03]">
      <div class="h-full grid grid-cols-12">
        <div class="border-r border-[#dac4ea] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#dac4ea] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#dac4ea] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#dac4ea] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#dac4ea] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#dac4ea] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#dac4ea] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#dac4ea] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#dac4ea] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#dac4ea] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#dac4ea] dark:border-[#00f7ff]"></div>
      </div>
    </div>
  </div>

  <div class="max-w-7xl mx-auto p-6 relative z-10">
    <!-- Header Hero Section -->
    <div class="mb-8 relative">
      <!-- Decorative top border -->
      <div class="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea]"></div>
      <div class="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea] blur-md"></div>
      
      <div class="bg-white dark:bg-[#1a1a1a] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] overflow-hidden border border-[#dac4ea]/20 dark:border-[#00f7ff]/20">
        <div class="grid grid-cols-1 lg:grid-cols-3">
          <!-- Team Info -->
          <div class="lg:col-span-2 bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea] p-8 text-white">
            <h1 class="text-4xl font-bold mb-2">{{ equipe.name }}</h1>
            <p class="text-white/80 text-lg mb-6">Gestion et collaboration d'équipe</p>
            
            <!-- Stats -->
            <div class="grid grid-cols-3 gap-4">
              <div class="bg-white/20 backdrop-blur-sm rounded-lg p-4 text-center">
                <i class="fas fa-users text-2xl mb-2 block"></i>
                <div class="text-xl font-bold">{{ equipe.members?.length || 0 }}</div>
                <div class="text-sm text-white/80">Membres</div>
              </div>
              <div class="bg-white/20 backdrop-blur-sm rounded-lg p-4 text-center">
                <i class="fas fa-tasks text-2xl mb-2 block"></i>
                <div class="text-xl font-bold">0</div>
                <div class="text-sm text-white/80">Tâches</div>
              </div>
              <div class="bg-white/20 backdrop-blur-sm rounded-lg p-4 text-center">
                <i class="fas fa-calendar-check text-2xl mb-2 block"></i>
                <div class="text-xl font-bold">{{ formatDate(equipe.createdAt) }}</div>
                <div class="text-sm text-white/80">Créée le</div>
              </div>
            </div>
          </div>
          
          <!-- Quick Actions -->
          <div class="p-8 bg-white dark:bg-[#1a1a1a]">
            <h3 class="text-lg font-bold text-[#dac4ea] dark:text-[#00f7ff] mb-6 flex items-center">
              <i class="fas fa-bolt mr-2"></i>
              Actions rapides
            </h3>
            <div class="space-y-3">
              <button 
                (click)="navigateToTasks()"
                class="w-full bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea] text-white px-4 py-3 rounded-lg font-medium transition-all duration-300 hover:scale-105 shadow-lg"
              >
                <i class="fas fa-tasks mr-2"></i>
                Gérer les tâches
              </button>
              <button 
                (click)="navigateToEditEquipe()"
                class="w-full bg-[#dac4ea]/20 dark:bg-[#00f7ff]/20 text-[#dac4ea] dark:text-[#00f7ff] px-4 py-3 rounded-lg font-medium transition-all duration-300 hover:scale-105"
              >
                <i class="fas fa-edit mr-2"></i>
                Modifier l'équipe
              </button>
              <div class="flex space-x-2">
                <button 
                  (click)="navigateToEquipeList()"
                  class="flex-1 bg-[#6d6870]/20 dark:bg-[#a0a0a0]/20 text-[#6d6870] dark:text-[#a0a0a0] px-4 py-3 rounded-lg font-medium transition-all duration-300 hover:scale-105"
                >
                  <i class="fas fa-arrow-left mr-2"></i>
                  Retour
                </button>
                <button 
                  (click)="deleteEquipe()"
                  class="bg-[#ff6b69]/20 dark:bg-[#ff3b30]/20 text-[#ff6b69] dark:text-[#ff3b30] px-4 py-3 rounded-lg font-medium transition-all duration-300 hover:scale-105"
                >
                  <i class="fas fa-trash"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Team Information -->
    <div class="mb-8 relative">
      <!-- Decorative top border -->
      <div class="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea]"></div>
      <div class="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea] blur-md"></div>
      
      <div class="bg-white dark:bg-[#1a1a1a] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] overflow-hidden border border-[#dac4ea]/20 dark:border-[#00f7ff]/20">
        <div class="grid grid-cols-1 md:grid-cols-4">
          <!-- Icon Section -->
          <div class="bg-gradient-to-br from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea] p-6 flex flex-col items-center justify-center text-white">
            <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mb-4">
              <i class="fas fa-info-circle text-2xl"></i>
            </div>
            <h3 class="text-lg font-bold mb-1">À propos</h3>
            <p class="text-white/80 text-sm text-center">Détails et informations</p>
          </div>
          
          <!-- Content Section -->
          <div class="md:col-span-3 p-6">
            <div class="flex justify-between items-center mb-4">
              <h4 class="text-lg font-bold text-[#dac4ea] dark:text-[#00f7ff]">Description</h4>
              <span class="bg-[#dac4ea]/10 dark:bg-[#00f7ff]/10 text-[#dac4ea] dark:text-[#00f7ff] px-3 py-1 rounded-full text-sm font-medium">
                <i class="fas fa-user-shield mr-1"></i>
                Admin: {{ equipe.admin ? (getUserName(equipe.admin) || equipe.admin) : 'Non défini' }}
              </span>
            </div>
            
            <div class="bg-[#f0f4f8] dark:bg-[#0a0a0a] border-l-4 border-[#dac4ea] dark:border-[#00f7ff] rounded-lg p-4 mb-4">
              <p class="text-[#6d6870] dark:text-[#a0a0a0] leading-relaxed">
                {{ equipe.description || 'Aucune description disponible pour cette équipe.' }}
              </p>
            </div>
            
            <!-- Tags -->
            <div class="flex flex-wrap gap-2">
              <span class="bg-[#dac4ea]/10 dark:bg-[#00f7ff]/10 text-[#dac4ea] dark:text-[#00f7ff] px-3 py-1 rounded-full text-sm font-medium">
                <i class="fas fa-users mr-1"></i>
                {{ equipe.members?.length || 0 }} membres
              </span>
              <span class="bg-[#00ff9d]/10 text-[#00ff9d] px-3 py-1 rounded-full text-sm font-medium">
                <i class="fas fa-calendar-check mr-1"></i>
                Créée le {{ formatDate(equipe.createdAt) }}
              </span>
              <span class="bg-[#4f5fad]/10 text-[#4f5fad] px-3 py-1 rounded-full text-sm font-medium">
                <i class="fas fa-project-diagram mr-1"></i>
                Gestion de projet
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- AI Assistant -->
    <div class="mb-8 relative">
      <!-- Decorative top border -->
      <div class="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#8b5a9f] to-[#4a00e0] dark:from-[#00f7ff] dark:to-[#8b5a9f]"></div>
      <div class="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#8b5a9f] to-[#4a00e0] dark:from-[#00f7ff] dark:to-[#8b5a9f] blur-md"></div>
      
      <div class="bg-white dark:bg-[#1a1a1a] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] overflow-hidden border border-[#8b5a9f]/20 dark:border-[#00f7ff]/20">
        <!-- Header -->
        <div class="bg-gradient-to-r from-[#8b5a9f] to-[#4a00e0] dark:from-[#00f7ff] dark:to-[#8b5a9f] p-6">
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <div class="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mr-4">
                <i class="fas fa-robot text-white text-xl"></i>
              </div>
              <div>
                <h3 class="text-xl font-bold text-white">Assistant IA Gemini</h3>
                <p class="text-white/80 text-sm">Génération de tâches intelligente</p>
              </div>
            </div>
            <span class="bg-white/20 text-white px-3 py-1 rounded-full text-sm font-medium">
              <i class="fas fa-magic mr-1"></i>
              IA
            </span>
          </div>
        </div>
        
        <!-- AI Chat Component -->
        <div class="p-0">
          <app-ai-chat [team]="equipe"></app-ai-chat>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Loading/Error State -->
<div class="min-h-screen bg-[#f0f4f8] dark:bg-[#0a0a0a] flex items-center justify-center" *ngIf="!equipe">
  <div class="text-center">
    <div class="w-20 h-20 mx-auto mb-6 text-[#dac4ea] dark:text-[#00f7ff] opacity-70">
      <i class="fas fa-exclamation-triangle text-5xl"></i>
    </div>
    <h3 class="text-xl font-semibold text-[#dac4ea] dark:text-[#00f7ff] mb-2">
      Équipe non trouvée
    </h3>
    <p class="text-[#6d6870] dark:text-[#a0a0a0] text-sm mb-6">
      L'équipe est en cours de chargement ou n'existe pas
    </p>
    <button 
      (click)="navigateToEquipeList()"
      class="bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea] text-white px-6 py-3 rounded-xl font-medium transition-all duration-300 hover:scale-105"
    >
      <i class="fas fa-arrow-left mr-2"></i>
      Retour à la liste des équipes
    </button>
  </div>
</div>
