{"ast": null, "code": "import { finalize } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/equipe.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/services/notification.service\";\nimport * as i4 from \"@angular/common\";\nfunction EquipeListComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"div\", 13)(2, \"div\", 14)(3, \"span\", 15);\n    i0.ɵɵtext(4, \"Chargement...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 16)(6, \"span\", 15);\n    i0.ɵɵtext(7, \"Chargement...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 14)(9, \"span\", 15);\n    i0.ɵɵtext(10, \"Chargement...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"p\", 17);\n    i0.ɵɵtext(12, \"Chargement des \\u00E9quipes...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction EquipeListComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"div\", 18)(2, \"div\", 19);\n    i0.ɵɵelement(3, \"i\", 20);\n    i0.ɵɵelementStart(4, \"div\", 21);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function EquipeListComponent_div_15_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.loadEquipes());\n    });\n    i0.ɵɵelement(7, \"i\", 23);\n    i0.ɵɵtext(8, \" R\\u00E9essayer \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.error, \" \");\n  }\n}\nfunction EquipeListComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"div\", 24)(2, \"div\", 25);\n    i0.ɵɵelement(3, \"i\", 26);\n    i0.ɵɵelementStart(4, \"h3\", 27);\n    i0.ɵɵtext(5, \"Aucune \\u00E9quipe trouv\\u00E9e\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 28);\n    i0.ɵɵtext(7, \"Commencez par cr\\u00E9er une nouvelle \\u00E9quipe pour organiser vos projets et membres.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function EquipeListComponent_div_16_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.navigateToAddEquipe());\n    });\n    i0.ɵɵelement(9, \"i\", 8);\n    i0.ɵɵtext(10, \" Cr\\u00E9er une \\u00E9quipe \");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction EquipeListComponent_div_17_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"div\", 33)(2, \"div\", 34)(3, \"h5\", 35);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 36)(6, \"p\", 37);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"slice\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 38);\n    i0.ɵɵelement(10, \"i\", 39);\n    i0.ɵɵelementStart(11, \"span\", 40);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(13, \"div\", 41)(14, \"div\", 42)(15, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function EquipeListComponent_div_17_div_1_Template_button_click_15_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r11);\n      const equipe_r9 = restoredCtx.$implicit;\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(equipe_r9._id && ctx_r10.navigateToEquipeDetail(equipe_r9._id));\n    });\n    i0.ɵɵelement(16, \"i\", 44);\n    i0.ɵɵtext(17, \" D\\u00E9tails \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\")(19, \"button\", 45);\n    i0.ɵɵlistener(\"click\", function EquipeListComponent_div_17_div_1_Template_button_click_19_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r11);\n      const equipe_r9 = restoredCtx.$implicit;\n      const ctx_r12 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(equipe_r9._id && ctx_r12.navigateToEditEquipe(equipe_r9._id));\n    });\n    i0.ɵɵelement(20, \"i\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function EquipeListComponent_div_17_div_1_Template_button_click_21_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r11);\n      const equipe_r9 = restoredCtx.$implicit;\n      const ctx_r13 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(equipe_r9._id && ctx_r13.deleteEquipe(equipe_r9._id));\n    });\n    i0.ɵɵelement(22, \"i\", 48);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(23, \"button\", 49);\n    i0.ɵɵlistener(\"click\", function EquipeListComponent_div_17_div_1_Template_button_click_23_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r11);\n      const equipe_r9 = restoredCtx.$implicit;\n      const ctx_r14 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(equipe_r9._id && ctx_r14.navigateToTasks(equipe_r9._id));\n    });\n    i0.ɵɵelement(24, \"i\", 50);\n    i0.ɵɵtext(25, \" Tasks \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const equipe_r9 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(equipe_r9.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", equipe_r9.description && equipe_r9.description.length > 100 ? i0.ɵɵpipeBind3(8, 3, equipe_r9.description, 0, 100) + \"...\" : equipe_r9.description || \"Aucune description\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", (equipe_r9.members == null ? null : equipe_r9.members.length) || 0, \" membre(s) \");\n  }\n}\nfunction EquipeListComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵtemplate(1, EquipeListComponent_div_17_div_1_Template, 26, 7, \"div\", 31);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.equipes);\n  }\n}\nexport let EquipeListComponent = /*#__PURE__*/(() => {\n  class EquipeListComponent {\n    constructor(equipeService, router, notificationService) {\n      this.equipeService = equipeService;\n      this.router = router;\n      this.notificationService = notificationService;\n      this.equipes = [];\n      this.loading = false;\n      this.error = null;\n    }\n    ngOnInit() {\n      this.loadEquipes();\n    }\n    loadEquipes() {\n      this.loading = true;\n      this.error = null;\n      this.equipeService.getEquipes().pipe(finalize(() => this.loading = false)).subscribe({\n        next: data => {\n          console.log('Équipes chargées:', data);\n          this.equipes = data;\n          // Trier les équipes par nom\n          this.equipes.sort((a, b) => {\n            if (a.name && b.name) {\n              return a.name.localeCompare(b.name);\n            }\n            return 0;\n          });\n        },\n        error: error => {\n          console.error('Erreur lors du chargement des équipes:', error);\n          this.error = 'Impossible de charger les équipes. Veuillez réessayer plus tard.';\n          this.notificationService.showError('Erreur lors du chargement des équipes');\n        }\n      });\n    }\n    navigateToAddEquipe() {\n      this.router.navigate(['/equipes/ajouter']);\n    }\n    navigateToEditEquipe(id) {\n      this.router.navigate(['/equipes/modifier', id]);\n    }\n    navigateToEquipeDetail(id) {\n      this.router.navigate(['/equipes/detail', id]);\n    }\n    deleteEquipe(id) {\n      if (!id) {\n        console.error('ID est indéfini');\n        this.notificationService.showError('ID d\\'équipe invalide');\n        return;\n      }\n      // Trouver le nom de l'équipe pour l'afficher dans le message de confirmation\n      const equipe = this.equipes.find(e => e._id === id);\n      const equipeName = equipe?.name || 'cette équipe';\n      if (confirm(`Êtes-vous sûr de vouloir supprimer l'équipe \"${equipeName}\" ?`)) {\n        this.loading = true;\n        this.equipeService.deleteEquipe(id).pipe(finalize(() => this.loading = false)).subscribe({\n          next: () => {\n            console.log('Équipe supprimée avec succès');\n            this.notificationService.showSuccess(`L'équipe \"${equipeName}\" a été supprimée avec succès`);\n            this.loadEquipes();\n          },\n          error: error => {\n            console.error('Erreur lors de la suppression de l\\'équipe:', error);\n            this.error = 'Impossible de supprimer l\\'équipe. Veuillez réessayer plus tard.';\n            this.notificationService.showError(`Erreur lors de la suppression de l'équipe \"${equipeName}\"`);\n          }\n        });\n      }\n    }\n    navigateToTasks(id) {\n      if (!id) {\n        console.error('ID est indéfini');\n        this.notificationService.showError('ID d\\'équipe invalide');\n        return;\n      }\n      const equipe = this.equipes.find(e => e._id === id);\n      const equipeName = equipe?.name || 'cette équipe';\n      // Naviguer vers la page des tâches de l'équipe\n      this.router.navigate(['/tasks', id]);\n    }\n    static {\n      this.ɵfac = function EquipeListComponent_Factory(t) {\n        return new (t || EquipeListComponent)(i0.ɵɵdirectiveInject(i1.EquipeService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.NotificationService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: EquipeListComponent,\n        selectors: [[\"app-equipe-list\"]],\n        decls: 18,\n        vars: 4,\n        consts: [[1, \"container-fluid\", \"py-5\", \"bg-light\"], [1, \"container\"], [1, \"row\", \"mb-5\"], [1, \"col-12\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"display-4\", \"fw-bold\", \"text-primary\"], [1, \"text-muted\", \"lead\"], [1, \"btn\", \"btn-primary\", \"btn-lg\", \"rounded-pill\", \"shadow-sm\", 3, \"click\"], [1, \"bi\", \"bi-plus-circle\", \"me-2\"], [1, \"my-4\"], [\"class\", \"row justify-content-center my-5\", 4, \"ngIf\"], [\"class\", \"row g-4\", 4, \"ngIf\"], [1, \"row\", \"justify-content-center\", \"my-5\"], [1, \"col-md-6\", \"text-center\"], [\"role\", \"status\", 1, \"spinner-grow\", \"text-primary\", \"mx-1\"], [1, \"visually-hidden\"], [\"role\", \"status\", 1, \"spinner-grow\", \"text-secondary\", \"mx-1\"], [1, \"mt-3\", \"text-muted\"], [1, \"col-md-8\"], [1, \"alert\", \"alert-danger\", \"shadow-sm\", \"border-0\", \"rounded-3\", \"d-flex\", \"align-items-center\"], [1, \"bi\", \"bi-exclamation-triangle-fill\", \"fs-3\", \"me-3\"], [1, \"flex-grow-1\"], [1, \"btn\", \"btn-danger\", \"rounded-pill\", \"ms-3\", 3, \"click\"], [1, \"bi\", \"bi-arrow-clockwise\", \"me-1\"], [1, \"col-md-8\", \"text-center\"], [1, \"p-5\", \"bg-white\", \"rounded-3\", \"shadow-sm\"], [1, \"bi\", \"bi-people\", \"fs-1\", \"text-muted\", \"mb-3\"], [1, \"mb-3\"], [1, \"text-muted\", \"mb-4\"], [1, \"btn\", \"btn-primary\", \"rounded-pill\", \"px-4\", \"py-2\", 3, \"click\"], [1, \"row\", \"g-4\"], [\"class\", \"col-md-4 col-lg-3\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-md-4\", \"col-lg-3\"], [1, \"card\", \"h-100\", \"border-0\", \"shadow-sm\", \"rounded-3\", \"hover-shadow\", \"transition\"], [1, \"card-header\", \"bg-primary\", \"text-white\", \"rounded-top\", \"p-3\"], [1, \"card-title\", \"mb-0\", \"text-truncate\"], [1, \"card-body\", \"p-4\"], [1, \"card-text\", \"mb-4\", 2, \"min-height\", \"60px\"], [1, \"d-flex\", \"align-items-center\", \"mb-3\"], [1, \"bi\", \"bi-people-fill\", \"text-primary\", \"me-2\"], [1, \"badge\", \"bg-light\", \"text-dark\", \"rounded-pill\", \"px-3\", \"py-2\", \"shadow-sm\"], [1, \"card-footer\", \"bg-white\", \"border-0\", \"p-3\"], [1, \"d-flex\", \"justify-content-between\", \"mb-2\"], [\"title\", \"Voir les d\\u00E9tails\", 1, \"btn\", \"btn-sm\", \"btn-outline-primary\", \"rounded-pill\", 3, \"click\"], [1, \"bi\", \"bi-eye\", \"me-1\"], [\"title\", \"Modifier l'\\u00E9quipe\", 1, \"btn\", \"btn-sm\", \"btn-outline-secondary\", \"rounded-circle\", \"me-2\", 3, \"click\"], [1, \"bi\", \"bi-pencil\"], [\"title\", \"Supprimer l'\\u00E9quipe\", 1, \"btn\", \"btn-sm\", \"btn-outline-danger\", \"rounded-circle\", 3, \"click\"], [1, \"bi\", \"bi-trash\"], [\"title\", \"G\\u00E9rer les t\\u00E2ches de l'\\u00E9quipe\", 1, \"btn\", \"btn-sm\", \"btn-success\", \"w-100\", \"rounded-pill\", 3, \"click\"], [1, \"bi\", \"bi-list-check\", \"me-1\"]],\n        template: function EquipeListComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"div\")(6, \"h1\", 5);\n            i0.ɵɵtext(7, \"\\u00C9quipes\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(8, \"p\", 6);\n            i0.ɵɵtext(9, \"G\\u00E9rez vos \\u00E9quipes et leurs membres\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(10, \"button\", 7);\n            i0.ɵɵlistener(\"click\", function EquipeListComponent_Template_button_click_10_listener() {\n              return ctx.navigateToAddEquipe();\n            });\n            i0.ɵɵelement(11, \"i\", 8);\n            i0.ɵɵtext(12, \" Nouvelle \\u00C9quipe \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(13, \"hr\", 9);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(14, EquipeListComponent_div_14_Template, 13, 0, \"div\", 10);\n            i0.ɵɵtemplate(15, EquipeListComponent_div_15_Template, 9, 1, \"div\", 10);\n            i0.ɵɵtemplate(16, EquipeListComponent_div_16_Template, 11, 0, \"div\", 10);\n            i0.ɵɵtemplate(17, EquipeListComponent_div_17_Template, 2, 1, \"div\", 11);\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(14);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && !ctx.error && ctx.equipes.length === 0);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.equipes.length > 0);\n          }\n        },\n        dependencies: [i4.NgForOf, i4.NgIf, i4.SlicePipe],\n        styles: [\".hover-shadow[_ngcontent-%COMP%]:hover{transform:translateY(-5px);box-shadow:0 10px 20px #0000001a!important}.transition[_ngcontent-%COMP%]{transition:all .3s ease}.card-header.bg-primary[_ngcontent-%COMP%]{background:linear-gradient(45deg,#007bff,#6610f2)!important}\"]\n      });\n    }\n  }\n  return EquipeListComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}