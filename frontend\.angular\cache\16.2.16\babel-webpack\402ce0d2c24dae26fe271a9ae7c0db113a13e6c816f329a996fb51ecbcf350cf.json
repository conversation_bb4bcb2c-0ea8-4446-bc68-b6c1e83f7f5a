{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class EquipeLayoutComponent {\n  static {\n    this.ɵfac = function EquipeLayoutComponent_Factory(t) {\n      return new (t || EquipeLayoutComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: EquipeLayoutComponent,\n      selectors: [[\"app-equipe-layout\"]],\n      decls: 0,\n      vars: 0,\n      template: function EquipeLayoutComponent_Template(rf, ctx) {},\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJlcXVpcGUtbGF5b3V0LmNvbXBvbmVudC5jc3MifQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvZnJvbnQvZXF1aXBlcy9lcXVpcGUtbGF5b3V0L2VxdWlwZS1sYXlvdXQuY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0FBQ0EsNEtBQTRLIiwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EquipeLayoutComponent", "selectors", "decls", "vars", "template", "EquipeLayoutComponent_Template", "rf", "ctx", "styles"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\equipes\\equipe-layout\\equipe-layout.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-equipe-layout',\n  templateUrl: './equipe-layout.component.html',\n  styleUrls: ['./equipe-layout.component.css']\n})\nexport class EquipeLayoutComponent {\n\n}\n"], "mappings": ";AAOA,OAAM,MAAOA,qBAAqB;;;uBAArBA,qBAAqB;IAAA;EAAA;;;YAArBA,qBAAqB;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}