{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/equipe.service\";\nimport * as i2 from \"src/app/services/membre.service\";\nimport * as i3 from \"src/app/services/auth.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"src/app/services/notification.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/forms\";\nfunction EquipeFormComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"div\", 22);\n    i0.ɵɵelement(2, \"div\", 23)(3, \"div\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 25);\n    i0.ɵɵtext(5, \" Chargement des donn\\u00E9es... \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction EquipeFormComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"div\", 27)(2, \"div\", 28)(3, \"div\", 29);\n    i0.ɵɵelement(4, \"i\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 31)(6, \"h3\", 32);\n    i0.ɵɵtext(7, \" Erreur \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 33);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.error, \" \");\n  }\n}\nfunction EquipeFormComponent_div_33_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64);\n    i0.ɵɵelement(1, \"i\", 65);\n    i0.ɵɵtext(2, \" Ce nom d'\\u00E9quipe existe d\\u00E9j\\u00E0. Veuillez en choisir un autre. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipeFormComponent_div_33_button_45_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 66);\n    i0.ɵɵlistener(\"click\", function EquipeFormComponent_div_33_button_45_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r9.deleteEquipe());\n    });\n    i0.ɵɵelement(1, \"i\", 67);\n    i0.ɵɵtext(2, \" Supprimer \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipeFormComponent_div_33_span_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 68);\n  }\n}\nconst _c0 = function (a0, a1) {\n  return {\n    \"fa-save\": a0,\n    \"fa-plus-circle\": a1\n  };\n};\nfunction EquipeFormComponent_div_33_i_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 37);\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(1, _c0, ctx_r8.isEditMode, !ctx_r8.isEditMode));\n  }\n}\nconst _c1 = function (a0, a1) {\n  return {\n    \"fa-edit\": a0,\n    \"fa-plus-circle\": a1\n  };\n};\nfunction EquipeFormComponent_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵelement(1, \"div\", 9)(2, \"div\", 10);\n    i0.ɵɵelementStart(3, \"div\", 34)(4, \"div\", 35)(5, \"h3\", 36);\n    i0.ɵɵelement(6, \"i\", 37);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 38);\n    i0.ɵɵtext(9, \" Remplissez les informations de base de l'\\u00E9quipe \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 39)(11, \"form\", 40);\n    i0.ɵɵlistener(\"ngSubmit\", function EquipeFormComponent_div_33_Template_form_ngSubmit_11_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.onSubmit());\n    });\n    i0.ɵɵelementStart(12, \"div\")(13, \"label\", 41);\n    i0.ɵɵtext(14, \" Nom de l'\\u00E9quipe \");\n    i0.ɵɵelementStart(15, \"span\", 42);\n    i0.ɵɵtext(16, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 22)(18, \"div\", 43);\n    i0.ɵɵelement(19, \"i\", 44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"input\", 45, 46);\n    i0.ɵɵlistener(\"input\", function EquipeFormComponent_div_33_Template_input_input_20_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const _r3 = i0.ɵɵreference(21);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.updateName(_r3.value));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(22, EquipeFormComponent_div_33_div_22_Template, 3, 0, \"div\", 47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\")(24, \"label\", 41);\n    i0.ɵɵtext(25, \" Description \");\n    i0.ɵɵelementStart(26, \"span\", 42);\n    i0.ɵɵtext(27, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 22)(29, \"div\", 48);\n    i0.ɵɵelement(30, \"i\", 49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"textarea\", 50, 51);\n    i0.ɵɵlistener(\"input\", function EquipeFormComponent_div_33_Template_textarea_input_31_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const _r5 = i0.ɵɵreference(32);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.updateDescription(_r5.value));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelement(33, \"input\", 52);\n    i0.ɵɵelementStart(34, \"div\", 53)(35, \"div\", 54)(36, \"div\", 55);\n    i0.ɵɵelement(37, \"i\", 56);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"div\", 33);\n    i0.ɵɵtext(39, \" Un administrateur par d\\u00E9faut sera assign\\u00E9 \\u00E0 cette \\u00E9quipe. \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(40, \"div\", 57)(41, \"div\", 58)(42, \"button\", 59);\n    i0.ɵɵlistener(\"click\", function EquipeFormComponent_div_33_Template_button_click_42_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.cancel());\n    });\n    i0.ɵɵelement(43, \"i\", 17);\n    i0.ɵɵtext(44, \" Retour \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(45, EquipeFormComponent_div_33_button_45_Template, 3, 0, \"button\", 60);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"button\", 61);\n    i0.ɵɵtemplate(47, EquipeFormComponent_div_33_span_47_Template, 1, 0, \"span\", 62);\n    i0.ɵɵtemplate(48, EquipeFormComponent_div_33_i_48_Template, 1, 4, \"i\", 63);\n    i0.ɵɵtext(49);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(11, _c1, ctx_r2.isEditMode, !ctx_r2.isEditMode));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.isEditMode ? \"Informations de l'\\u00E9quipe\" : \"D\\u00E9tails de la nouvelle \\u00E9quipe\", \" \");\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"value\", ctx_r2.equipe.name || \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.nameExists);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"value\", ctx_r2.equipe.description || \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", ctx_r2.equipe.admin);\n    i0.ɵɵadvance(12);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isEditMode && ctx_r2.equipeId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.submitting || !ctx_r2.equipe.name || !ctx_r2.equipe.description || ctx_r2.nameExists || ctx_r2.nameError || ctx_r2.descriptionError);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.submitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.submitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.isEditMode ? \"Mettre \\u00E0 jour\" : \"Cr\\u00E9er l'\\u00E9quipe\", \" \");\n  }\n}\nexport class EquipeFormComponent {\n  constructor(equipeService, membreService, userService, route, router, notificationService) {\n    this.equipeService = equipeService;\n    this.membreService = membreService;\n    this.userService = userService;\n    this.route = route;\n    this.router = router;\n    this.notificationService = notificationService;\n    this.equipe = {\n      name: '',\n      description: '',\n      admin: '65f1e5b3a1d8f3c8c0f9e8d7' // ID temporaire\n    };\n\n    this.isEditMode = false;\n    this.loading = false;\n    this.submitting = false;\n    this.error = null;\n    this.equipeId = null;\n    this.nameExists = false;\n    this.nameError = false;\n    this.descriptionError = false;\n    this.checkingName = false;\n    this.existingEquipes = [];\n    this.availableMembers = []; // Liste des membres disponibles\n    this.availableUsers = []; // Liste des utilisateurs disponibles\n  }\n\n  ngOnInit() {\n    console.log('EquipeFormComponent initialized');\n    // Ensure equipe is always defined with admin\n    if (!this.equipe) {\n      this.equipe = {\n        name: '',\n        description: '',\n        admin: '65f1e5b3a1d8f3c8c0f9e8d7' // ID temporaire\n      };\n    }\n    // Charger toutes les équipes pour vérifier les noms existants\n    this.loadAllEquipes();\n    // Charger tous les membres disponibles\n    this.loadAllMembers();\n    // Charger tous les utilisateurs disponibles\n    this.loadAllUsers();\n    try {\n      // Vérifier si nous sommes en mode édition (si un ID est présent dans l'URL)\n      this.equipeId = this.route.snapshot.paramMap.get('id');\n      this.isEditMode = !!this.equipeId;\n      console.log('Edit mode:', this.isEditMode, 'ID:', this.equipeId);\n      if (this.isEditMode && this.equipeId) {\n        this.loadEquipe(this.equipeId);\n        // Ajouter un délai pour s'assurer que l'équipe est chargée\n        setTimeout(() => {\n          console.log('Après délai - this.equipeId:', this.equipeId);\n          console.log('Après délai - this.equipe:', this.equipe);\n        }, 1000);\n      }\n    } catch (error) {\n      console.error('Error in ngOnInit:', error);\n      this.error = \"Erreur d'initialisation\";\n    }\n    // Ajouter un gestionnaire d'événements pour le bouton d'ajout de membre\n    setTimeout(() => {\n      const addButton = document.getElementById('addMembreButton');\n      if (addButton) {\n        console.log(\"Bouton d'ajout de membre trouvé\");\n        addButton.addEventListener('click', () => {\n          console.log(\"Bouton d'ajout de membre cliqué\");\n        });\n      } else {\n        console.log(\"Bouton d'ajout de membre non trouvé\");\n      }\n    }, 2000);\n  }\n  loadAllMembers() {\n    this.membreService.getMembres().subscribe({\n      next: membres => {\n        this.availableMembers = membres;\n        console.log('Membres disponibles chargés:', membres);\n      },\n      error: error => {\n        console.error('Erreur lors du chargement des membres:', error);\n        this.error = 'Impossible de charger la liste des membres. Veuillez réessayer plus tard.';\n      }\n    });\n  }\n  loadAllUsers() {\n    const token = localStorage.getItem('token');\n    if (token) {\n      this.userService.getAllUsers(token).subscribe({\n        next: users => {\n          this.availableUsers = users;\n          console.log('Utilisateurs disponibles chargés:', users);\n        },\n        error: error => {\n          console.error('Erreur lors du chargement des utilisateurs:', error);\n          this.error = 'Impossible de charger la liste des utilisateurs. Veuillez réessayer plus tard.';\n        }\n      });\n    }\n  }\n  loadAllEquipes() {\n    this.equipeService.getEquipes().subscribe({\n      next: equipes => {\n        this.existingEquipes = equipes;\n        console.log('Équipes existantes chargées:', equipes);\n      },\n      error: error => {\n        console.error('Erreur lors du chargement des équipes:', error);\n      }\n    });\n  }\n  loadEquipe(id) {\n    console.log('Loading equipe with ID:', id);\n    this.loading = true;\n    this.error = null;\n    this.equipeService.getEquipe(id).subscribe({\n      next: data => {\n        console.log('Équipe chargée:', data);\n        this.equipe = data;\n        // Vérifier que l'ID est correctement défini\n        console.log(\"ID de l'équipe après chargement:\", this.equipe._id);\n        console.log('this.equipeId:', this.equipeId);\n        // Si l'équipe a des membres, récupérer les informations de chaque membre\n        if (this.equipe.members && this.equipe.members.length > 0) {\n          this.loadMembersDetails();\n        }\n        this.loading = false;\n      },\n      error: error => {\n        console.error(\"Erreur lors du chargement de l'équipe:\", error);\n        this.error = \"Impossible de charger les détails de l'équipe. Veuillez réessayer plus tard.\";\n        this.loading = false;\n      }\n    });\n  }\n  // Méthode pour récupérer les détails des membres de l'équipe\n  loadMembersDetails() {\n    if (!this.equipe.members || this.equipe.members.length === 0) {\n      return;\n    }\n    console.log(\"Chargement des détails des membres de l'équipe...\");\n    // Pour chaque membre de l'équipe, essayer de trouver ses informations dans la liste des utilisateurs\n    this.equipe.members.forEach(membreId => {\n      // Chercher d'abord dans la liste des utilisateurs\n      const user = this.availableUsers.find(u => u._id === membreId || u.id === membreId);\n      if (user) {\n        console.log(`Membre ${membreId} trouvé dans la liste des utilisateurs:`, user);\n        // Vérifier si toutes les informations nécessaires sont présentes\n        if (!user.email || !user.profession && !user.role) {\n          // Si des informations manquent, essayer de les récupérer depuis l'API\n          const token = localStorage.getItem('token');\n          if (token) {\n            this.userService.getUserById(membreId, token).subscribe({\n              next: userData => {\n                console.log(`Détails supplémentaires de l'utilisateur ${membreId} récupérés:`, userData);\n                // Mettre à jour l'utilisateur dans la liste avec les nouvelles informations\n                const index = this.availableUsers.findIndex(u => u._id === membreId || u.id === membreId);\n                if (index !== -1) {\n                  this.availableUsers[index] = {\n                    ...this.availableUsers[index],\n                    ...userData\n                  };\n                }\n              },\n              error: error => {\n                console.error(`Erreur lors de la récupération des détails supplémentaires de l'utilisateur ${membreId}:`, error);\n              }\n            });\n          }\n        }\n      } else {\n        // Si non trouvé, essayer de récupérer l'utilisateur depuis l'API\n        const token = localStorage.getItem('token');\n        if (token) {\n          this.userService.getUserById(membreId, token).subscribe({\n            next: userData => {\n              console.log(`Détails de l'utilisateur ${membreId} récupérés:`, userData);\n              // Ajouter l'utilisateur à la liste des utilisateurs disponibles s'il n'y est pas déjà\n              if (!this.availableUsers.some(u => u._id === userData._id || u.id === userData.id)) {\n                this.availableUsers.push(userData);\n              }\n            },\n            error: error => {\n              console.error(`Erreur lors de la récupération des détails de l'utilisateur ${membreId}:`, error);\n            }\n          });\n        }\n      }\n    });\n  }\n  checkNameExists(name) {\n    // En mode édition, ignorer l'équipe actuelle\n    if (this.isEditMode && this.equipeId) {\n      return this.existingEquipes.some(e => e.name === name && e._id !== this.equipeId);\n    }\n    // En mode création, vérifier tous les noms\n    return this.existingEquipes.some(e => e.name === name);\n  }\n  updateName(value) {\n    console.log('Name updated:', value);\n    this.equipe.name = value;\n    // Vérifier si le nom existe déjà\n    this.nameExists = this.checkNameExists(value);\n    if (this.nameExists) {\n      console.warn(\"Ce nom d'équipe existe déjà\");\n    }\n    // Vérifier si le nom a au moins 3 caractères\n    this.nameError = value.length > 0 && value.length < 3;\n    if (this.nameError) {\n      console.warn('Le nom doit contenir au moins 3 caractères');\n    }\n  }\n  updateDescription(value) {\n    console.log('Description updated:', value);\n    this.equipe.description = value;\n    // Vérifier si la description a au moins 10 caractères\n    this.descriptionError = value.length > 0 && value.length < 10;\n    if (this.descriptionError) {\n      console.warn('La description doit contenir au moins 10 caractères');\n    }\n  }\n  onSubmit() {\n    console.log('Form submitted with:', this.equipe);\n    // Vérifier si le nom est présent et valide\n    if (!this.equipe.name) {\n      this.error = \"Le nom de l'équipe est requis.\";\n      return;\n    }\n    if (this.equipe.name.length < 3) {\n      this.nameError = true;\n      this.error = \"Le nom de l'équipe doit contenir au moins 3 caractères.\";\n      return;\n    }\n    // Vérifier si la description est présente et valide\n    if (!this.equipe.description) {\n      this.error = \"La description de l'équipe est requise.\";\n      return;\n    }\n    if (this.equipe.description.length < 10) {\n      this.descriptionError = true;\n      this.error = \"La description de l'équipe doit contenir au moins 10 caractères.\";\n      return;\n    }\n    // Vérifier si le nom existe déjà avant de soumettre\n    if (this.checkNameExists(this.equipe.name)) {\n      this.nameExists = true;\n      this.error = 'Une équipe avec ce nom existe déjà. Veuillez choisir un autre nom.';\n      return;\n    }\n    this.submitting = true;\n    this.error = null;\n    // Créer une copie de l'objet équipe pour éviter les problèmes de référence\n    const equipeToSave = {\n      name: this.equipe.name,\n      description: this.equipe.description || '',\n      admin: this.equipe.admin\n    };\n    // Ajouter l'ID si nous sommes en mode édition\n    if (this.isEditMode && this.equipeId) {\n      equipeToSave._id = this.equipeId;\n    }\n    console.log('Données à envoyer:', equipeToSave);\n    if (this.isEditMode && this.equipeId) {\n      // Mode édition\n      this.equipeService.updateEquipe(this.equipeId, equipeToSave).subscribe({\n        next: response => {\n          console.log('Équipe mise à jour avec succès:', response);\n          this.submitting = false;\n          this.notificationService.showSuccess(`L'équipe \"${response.name}\" a été mise à jour avec succès.`);\n          this.router.navigate(['/equipes/liste']);\n        },\n        error: error => {\n          console.error(\"Erreur lors de la mise à jour de l'équipe:\", error);\n          this.error = `Impossible de mettre à jour l'équipe: ${error.message}`;\n          this.submitting = false;\n          this.notificationService.showError(`Erreur: ${error.message}`);\n        }\n      });\n    } else {\n      // Mode ajout\n      this.equipeService.addEquipe(equipeToSave).subscribe({\n        next: response => {\n          console.log('Équipe ajoutée avec succès:', response);\n          this.submitting = false;\n          this.notificationService.showSuccess(`L'équipe \"${response.name}\" a été créée avec succès.`);\n          this.router.navigate(['/equipes/liste']);\n        },\n        error: error => {\n          console.error(\"Erreur lors de l'ajout de l'équipe:\", error);\n          this.error = `Impossible d'ajouter l'équipe: ${error.message}`;\n          this.submitting = false;\n          this.notificationService.showError(`Erreur: ${error.message}`);\n        }\n      });\n    }\n  }\n  cancel() {\n    console.log('Form cancelled');\n    this.router.navigate(['/admin/equipes']);\n  }\n  deleteEquipe() {\n    if (!this.equipeId) {\n      this.error = \"Impossible de supprimer l'équipe: ID manquant\";\n      return;\n    }\n    if (confirm(`Êtes-vous sûr de vouloir supprimer l'équipe \"${this.equipe.name}\" ? Cette action est irréversible.`)) {\n      this.loading = true;\n      this.error = null;\n      this.equipeService.deleteEquipe(this.equipeId).subscribe({\n        next: () => {\n          console.log('Équipe supprimée avec succès');\n          this.loading = false;\n          this.notificationService.showSuccess(`L'équipe \"${this.equipe.name}\" a été supprimée avec succès.`);\n          this.router.navigate(['/admin/equipes']);\n        },\n        error: error => {\n          console.error(\"Erreur lors de la suppression de l'équipe:\", error);\n          this.error = `Impossible de supprimer l'équipe: ${error.message}`;\n          this.loading = false;\n          this.notificationService.showError(`Erreur: ${error.message}`);\n        }\n      });\n    }\n  }\n  // Méthodes pour gérer les membres\n  addMembreToEquipe(membreId, role = 'membre') {\n    console.log('Début de addMembreToEquipe avec membreId:', membreId, 'et rôle:', role);\n    console.log('État actuel - this.equipeId:', this.equipeId);\n    console.log('État actuel - this.equipe:', this.equipe);\n    // Utiliser this.equipe._id si this.equipeId n'est pas défini\n    const equipeId = this.equipeId || this.equipe && this.equipe._id;\n    console.log('equipeId calculé:', equipeId);\n    if (!equipeId || !membreId) {\n      console.error(\"ID d'équipe ou ID de membre manquant\");\n      this.error = \"ID d'équipe ou ID de membre manquant\";\n      console.log('equipeId:', equipeId, 'membreId:', membreId);\n      // Afficher un message à l'utilisateur\n      this.notificationService.showError(\"Impossible d'ajouter le membre: ID d'équipe ou ID de membre manquant\");\n      return;\n    }\n    // Vérifier si le membre est déjà dans l'équipe\n    if (this.equipe.members && this.equipe.members.includes(membreId)) {\n      this.notificationService.showError(\"Ce membre fait déjà partie de l'équipe\");\n      return;\n    }\n    // Récupérer les informations de l'utilisateur pour afficher un message plus informatif\n    const user = this.availableUsers.find(u => u._id === membreId || u.id === membreId);\n    const userName = user ? user.firstName && user.lastName ? `${user.firstName} ${user.lastName}` : user.name || membreId : membreId;\n    // Créer l'objet membre avec le rôle spécifié\n    const membre = {\n      id: membreId,\n      role: role\n    };\n    this.loading = true;\n    console.log(`Ajout de l'utilisateur \"${userName}\" comme ${role} à l'équipe ${equipeId}`);\n    this.equipeService.addMembreToEquipe(equipeId, membre).subscribe({\n      next: response => {\n        console.log('Membre ajouté avec succès:', response);\n        this.notificationService.showSuccess(`${userName} a été ajouté comme ${role === 'admin' ? 'administrateur' : 'membre'} à l'équipe`);\n        // Recharger l'équipe pour mettre à jour la liste des membres\n        this.loadEquipe(equipeId);\n        this.loading = false;\n      },\n      error: error => {\n        console.error(\"Erreur lors de l'ajout du membre:\", error);\n        this.error = \"Impossible d'ajouter le membre. Veuillez réessayer plus tard.\";\n        this.notificationService.showError(\"Erreur lors de l'ajout du membre: \" + error.message);\n        this.loading = false;\n      }\n    });\n  }\n  // Méthode pour obtenir le nom complet d'un membre à partir de son ID\n  getMembreName(membreId) {\n    // Chercher d'abord dans la liste des utilisateurs\n    const user = this.availableUsers.find(u => u._id === membreId || u.id === membreId);\n    if (user) {\n      if (user.firstName && user.lastName) {\n        return `${user.firstName} ${user.lastName}`;\n      } else if (user.name) {\n        return user.name;\n      }\n    }\n    // Chercher ensuite dans la liste des membres\n    const membre = this.availableMembers.find(m => m._id === membreId || m.id === membreId);\n    if (membre && membre.name) {\n      return membre.name;\n    }\n    // Si aucun nom n'est trouvé, retourner l'ID\n    return membreId;\n  }\n  // Méthode pour obtenir l'email d'un membre\n  getMembreEmail(membreId) {\n    const user = this.availableUsers.find(u => u._id === membreId || u.id === membreId);\n    if (user && user.email) {\n      return user.email;\n    }\n    return 'Non renseigné';\n  }\n  // Méthode pour obtenir la profession d'un membre\n  getMembreProfession(membreId) {\n    const user = this.availableUsers.find(u => u._id === membreId || u.id === membreId);\n    if (user) {\n      if (user.profession) {\n        return user.profession === 'etudiant' ? 'Étudiant' : 'Professeur';\n      } else if (user.role) {\n        return user.role === 'etudiant' ? 'Étudiant' : 'Professeur';\n      }\n    }\n    return 'Non spécifié';\n  }\n  // Méthode pour obtenir le rôle d'un membre dans l'équipe\n  getMembreRole(_membreId) {\n    // Cette méthode nécessiterait d'avoir accès aux rôles des membres dans l'équipe\n    // Pour l'instant, nous retournons une valeur par défaut\n    return 'Membre';\n  }\n  removeMembreFromEquipe(membreId) {\n    console.log('Méthode removeMembreFromEquipe appelée avec ID:', membreId);\n    console.log('État actuel - this.equipeId:', this.equipeId);\n    console.log('État actuel - this.equipe:', this.equipe);\n    // Utiliser this.equipe._id si this.equipeId n'est pas défini\n    const equipeId = this.equipeId || this.equipe && this.equipe._id;\n    if (!equipeId) {\n      console.error(\"ID d'équipe manquant\");\n      this.error = \"ID d'équipe manquant. Impossible de retirer le membre.\";\n      this.notificationService.showError(\"ID d'équipe manquant. Impossible de retirer le membre.\");\n      return;\n    }\n    if (!membreId) {\n      console.error('ID de membre manquant');\n      this.error = 'ID de membre manquant. Impossible de retirer le membre.';\n      this.notificationService.showError('ID de membre manquant. Impossible de retirer le membre.');\n      return;\n    }\n    // Obtenir le nom du membre pour l'afficher dans le message de confirmation\n    const membreName = this.getMembreName(membreId);\n    console.log(`Tentative de retrait de l'utilisateur ${membreId} (${membreName}) de l'équipe ${equipeId}`);\n    try {\n      if (confirm(`Êtes-vous sûr de vouloir retirer ${membreName} de l'équipe?`)) {\n        console.log('Confirmation acceptée, suppression en cours...');\n        this.loading = true;\n        this.error = null;\n        // Ajouter un délai pour s'assurer que l'utilisateur voit le chargement\n        setTimeout(() => {\n          this.equipeService.removeMembreFromEquipe(equipeId, membreId).subscribe({\n            next: response => {\n              console.log(`Utilisateur \"${membreName}\" retiré avec succès de l'équipe:`, response);\n              this.loading = false;\n              this.notificationService.showSuccess(`${membreName} a été retiré avec succès de l'équipe`);\n              // Recharger l'équipe pour mettre à jour la liste des membres\n              this.loadEquipe(equipeId);\n            },\n            error: error => {\n              console.error(`Erreur lors du retrait de l'utilisateur \"${membreName}\":`, error);\n              console.error(\"Détails de l'erreur:\", {\n                status: error.status,\n                message: error.message,\n                error: error\n              });\n              this.loading = false;\n              this.error = `Impossible de retirer l'utilisateur \"${membreName}\" de l'équipe: ${error.message || 'Erreur inconnue'}`;\n              this.notificationService.showError(`Erreur lors du retrait du membre: ${this.error}`);\n            }\n          });\n        }, 500);\n      } else {\n        console.log(\"Suppression annulée par l'utilisateur\");\n      }\n    } catch (error) {\n      console.error('Exception lors du retrait du membre:', error);\n      this.error = `Exception: ${error?.message || 'Erreur inconnue'}`;\n      this.notificationService.showError(`Exception: ${this.error}`);\n    }\n  }\n  // Méthode pour supprimer l'équipe\n  deleteEquipe() {\n    console.log('Méthode deleteEquipe appelée dans equipe-form.component.ts');\n    console.log('État actuel - this.equipeId:', this.equipeId);\n    console.log('État actuel - this.equipe:', this.equipe);\n    // Utiliser this.equipe._id si this.equipeId n'est pas défini\n    const equipeId = this.equipeId || this.equipe && this.equipe._id;\n    if (!equipeId) {\n      console.error(\"ID d'équipe manquant\");\n      this.error = \"ID d'équipe manquant. Impossible de supprimer l'équipe.\";\n      this.notificationService.showError(\"ID d'équipe manquant. Impossible de supprimer l'équipe.\");\n      return;\n    }\n    console.log(\"ID de l'équipe à supprimer (final):\", equipeId);\n    try {\n      if (confirm(`Êtes-vous sûr de vouloir supprimer l'équipe \"${this.equipe.name}\"? Cette action est irréversible.`)) {\n        console.log('Confirmation acceptée, suppression en cours...');\n        this.loading = true;\n        this.error = null;\n        // Ajouter un délai pour s'assurer que l'utilisateur voit le chargement\n        setTimeout(() => {\n          this.equipeService.deleteEquipe(equipeId).subscribe({\n            next: response => {\n              console.log('Équipe supprimée avec succès, réponse:', response);\n              this.loading = false;\n              this.notificationService.showSuccess(`L'équipe \"${this.equipe.name}\" a été supprimée avec succès.`);\n              // Ajouter un délai avant la redirection\n              setTimeout(() => {\n                this.router.navigate(['/equipes/liste']);\n              }, 500);\n            },\n            error: error => {\n              console.error(\"Erreur lors de la suppression de l'équipe:\", error);\n              console.error(\"Détails de l'erreur:\", {\n                status: error.status,\n                message: error.message,\n                error: error\n              });\n              this.loading = false;\n              this.error = `Impossible de supprimer l'équipe: ${error.message || 'Erreur inconnue'}`;\n              this.notificationService.showError(`Erreur lors de la suppression: ${this.error}`);\n            }\n          });\n        }, 500);\n      } else {\n        console.log(\"Suppression annulée par l'utilisateur\");\n      }\n    } catch (error) {\n      console.error('Exception lors de la suppression:', error);\n      this.error = `Exception: ${error?.message || 'Erreur inconnue'}`;\n      this.notificationService.showError(`Exception: ${this.error}`);\n    }\n  }\n  static {\n    this.ɵfac = function EquipeFormComponent_Factory(t) {\n      return new (t || EquipeFormComponent)(i0.ɵɵdirectiveInject(i1.EquipeService), i0.ɵɵdirectiveInject(i2.MembreService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.ActivatedRoute), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i5.NotificationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: EquipeFormComponent,\n      selectors: [[\"app-equipe-form\"]],\n      decls: 34,\n      vars: 5,\n      consts: [[1, \"min-h-screen\", \"bg-[#f0f4f8]\", \"dark:bg-[#0a0a0a]\", \"relative\", \"overflow-hidden\"], [1, \"absolute\", \"inset-0\", \"overflow-hidden\", \"pointer-events-none\"], [1, \"absolute\", \"top-[15%]\", \"left-[10%]\", \"w-64\", \"h-64\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#dac4ea]/5\", \"to-transparent\", \"dark:from-[#00f7ff]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"bottom-[20%]\", \"right-[10%]\", \"w-80\", \"h-80\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#dac4ea]/5\", \"to-transparent\", \"dark:from-[#00f7ff]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"inset-0\", \"opacity-5\", \"dark:opacity-[0.03]\"], [1, \"h-full\", \"grid\", \"grid-cols-12\"], [1, \"border-r\", \"border-[#dac4ea]\", \"dark:border-[#00f7ff]\"], [1, \"max-w-4xl\", \"mx-auto\", \"p-6\", \"relative\", \"z-10\"], [1, \"mb-8\", \"relative\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#dac4ea]\", \"to-[#8b5a9f]\", \"dark:from-[#00f7ff]\", \"dark:to-[#dac4ea]\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#dac4ea]\", \"to-[#8b5a9f]\", \"dark:from-[#00f7ff]\", \"dark:to-[#dac4ea]\", \"blur-md\"], [1, \"bg-white\", \"dark:bg-[#1a1a1a]\", \"rounded-xl\", \"shadow-lg\", \"dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)]\", \"p-6\", \"backdrop-blur-sm\", \"border\", \"border-[#dac4ea]/20\", \"dark:border-[#00f7ff]/20\"], [1, \"flex\", \"flex-col\", \"lg:flex-row\", \"lg:items-center\", \"lg:justify-between\"], [1, \"mb-4\", \"lg:mb-0\"], [1, \"text-3xl\", \"font-bold\", \"text-[#dac4ea]\", \"dark:text-[#00f7ff]\", \"mb-2\", \"tracking-wide\"], [1, \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"text-sm\"], [1, \"bg-[#dac4ea]/20\", \"dark:bg-[#00f7ff]/20\", \"text-[#dac4ea]\", \"dark:text-[#00f7ff]\", \"px-6\", \"py-3\", \"rounded-xl\", \"font-medium\", \"transition-all\", \"duration-300\", \"hover:scale-105\", \"hover:bg-[#dac4ea]/30\", \"dark:hover:bg-[#00f7ff]/30\", 3, \"click\"], [1, \"fas\", \"fa-arrow-left\", \"mr-2\"], [\"class\", \"flex flex-col items-center justify-center py-16\", 4, \"ngIf\"], [\"class\", \"mb-6\", 4, \"ngIf\"], [\"class\", \"mb-8 relative\", 4, \"ngIf\"], [1, \"flex\", \"flex-col\", \"items-center\", \"justify-center\", \"py-16\"], [1, \"relative\"], [1, \"w-12\", \"h-12\", \"border-3\", \"border-[#dac4ea]/20\", \"dark:border-[#00f7ff]/20\", \"border-t-[#dac4ea]\", \"dark:border-t-[#00f7ff]\", \"rounded-full\", \"animate-spin\"], [1, \"absolute\", \"inset-0\", \"bg-[#dac4ea]/20\", \"dark:bg-[#00f7ff]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"mt-4\", \"text-[#dac4ea]\", \"dark:text-[#00f7ff]\", \"text-sm\", \"font-medium\", \"tracking-wide\"], [1, \"mb-6\"], [1, \"bg-[#ff6b69]/10\", \"dark:bg-[#ff3b30]/10\", \"border-l-4\", \"border-[#ff6b69]\", \"dark:border-[#ff3b30]\", \"rounded-lg\", \"p-4\", \"backdrop-blur-sm\"], [1, \"flex\", \"items-start\"], [1, \"text-[#ff6b69]\", \"dark:text-[#ff3b30]\", \"mr-3\", \"text-xl\"], [1, \"fas\", \"fa-exclamation-triangle\"], [1, \"flex-1\"], [1, \"font-semibold\", \"text-[#ff6b69]\", \"dark:text-[#ff3b30]\", \"mb-1\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\"], [1, \"bg-white\", \"dark:bg-[#1a1a1a]\", \"rounded-xl\", \"shadow-lg\", \"dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)]\", \"overflow-hidden\", \"border\", \"border-[#dac4ea]/20\", \"dark:border-[#00f7ff]/20\"], [1, \"bg-gradient-to-r\", \"from-[#dac4ea]\", \"to-[#8b5a9f]\", \"dark:from-[#00f7ff]\", \"dark:to-[#dac4ea]\", \"p-6\"], [1, \"text-xl\", \"font-bold\", \"text-white\", \"mb-1\", \"flex\", \"items-center\"], [1, \"fas\", \"mr-2\", 3, \"ngClass\"], [1, \"text-white/80\", \"text-sm\"], [1, \"p-6\"], [1, \"space-y-6\", 3, \"ngSubmit\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-[#dac4ea]\", \"dark:text-[#00f7ff]\", \"mb-2\"], [1, \"text-[#ff6b69]\", \"dark:text-[#ff3b30]\"], [1, \"absolute\", \"inset-y-0\", \"left-0\", \"pl-3\", \"flex\", \"items-center\", \"pointer-events-none\"], [1, \"fas\", \"fa-users\", \"text-[#dac4ea]\", \"dark:text-[#00f7ff]\"], [\"type\", \"text\", \"placeholder\", \"Entrez le nom de l'\\u00E9quipe\", \"required\", \"\", \"minlength\", \"3\", 1, \"w-full\", \"pl-10\", \"pr-4\", \"py-3\", \"bg-[#f0f4f8]\", \"dark:bg-[#0a0a0a]\", \"border\", \"border-[#dac4ea]/20\", \"dark:border-[#00f7ff]/20\", \"rounded-lg\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"placeholder-[#6d6870]/50\", \"dark:placeholder-[#a0a0a0]\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-[#dac4ea]\", \"dark:focus:ring-[#00f7ff]\", \"focus:border-transparent\", \"transition-all\", 3, \"value\", \"input\"], [\"nameInput\", \"\"], [\"class\", \"mt-1 text-sm text-[#ff6b69] dark:text-[#ff3b30] flex items-center\", 4, \"ngIf\"], [1, \"absolute\", \"top-3\", \"left-3\", \"pointer-events-none\"], [1, \"fas\", \"fa-file-alt\", \"text-[#dac4ea]\", \"dark:text-[#00f7ff]\"], [\"rows\", \"4\", \"placeholder\", \"D\\u00E9crivez l'objectif et les activit\\u00E9s de cette \\u00E9quipe\", \"required\", \"\", \"minlength\", \"10\", 1, \"w-full\", \"pl-10\", \"pr-4\", \"py-3\", \"bg-[#f0f4f8]\", \"dark:bg-[#0a0a0a]\", \"border\", \"border-[#dac4ea]/20\", \"dark:border-[#00f7ff]/20\", \"rounded-lg\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"placeholder-[#6d6870]/50\", \"dark:placeholder-[#a0a0a0]\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-[#dac4ea]\", \"dark:focus:ring-[#00f7ff]\", \"focus:border-transparent\", \"transition-all\", \"resize-none\", 3, \"value\", \"input\"], [\"descInput\", \"\"], [\"type\", \"hidden\", 3, \"value\"], [1, \"bg-[#dac4ea]/10\", \"dark:bg-[#00f7ff]/10\", \"border-l-4\", \"border-[#dac4ea]\", \"dark:border-[#00f7ff]\", \"rounded-lg\", \"p-4\"], [1, \"flex\", \"items-center\"], [1, \"text-[#dac4ea]\", \"dark:text-[#00f7ff]\", \"mr-3\", \"text-lg\"], [1, \"fas\", \"fa-info-circle\"], [1, \"flex\", \"items-center\", \"justify-between\", \"pt-4\"], [1, \"flex\", \"items-center\", \"space-x-4\"], [\"type\", \"button\", 1, \"bg-[#6d6870]/20\", \"dark:bg-[#a0a0a0]/20\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"px-6\", \"py-3\", \"rounded-xl\", \"font-medium\", \"transition-all\", \"duration-300\", \"hover:scale-105\", \"hover:bg-[#6d6870]/30\", \"dark:hover:bg-[#a0a0a0]/30\", 3, \"click\"], [\"type\", \"button\", \"class\", \"bg-[#ff6b69]/20 dark:bg-[#ff3b30]/20 text-[#ff6b69] dark:text-[#ff3b30] px-6 py-3 rounded-xl font-medium transition-all duration-300 hover:scale-105 hover:bg-[#ff6b69]/30 dark:hover:bg-[#ff3b30]/30\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"submit\", 1, \"relative\", \"overflow-hidden\", \"group\", \"bg-gradient-to-r\", \"from-[#dac4ea]\", \"to-[#8b5a9f]\", \"dark:from-[#00f7ff]\", \"dark:to-[#dac4ea]\", \"text-white\", \"px-6\", \"py-3\", \"rounded-xl\", \"font-medium\", \"transition-all\", \"duration-300\", \"hover:scale-105\", \"shadow-lg\", \"hover:shadow-[0_0_25px_rgba(218,196,234,0.4)]\", \"dark:hover:shadow-[0_0_25px_rgba(0,247,255,0.4)]\", \"disabled:opacity-50\", \"disabled:cursor-not-allowed\", \"disabled:hover:scale-100\", 3, \"disabled\"], [\"class\", \"inline-block w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2\", 4, \"ngIf\"], [\"class\", \"fas mr-2\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"mt-1\", \"text-sm\", \"text-[#ff6b69]\", \"dark:text-[#ff3b30]\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-exclamation-triangle\", \"mr-1\"], [\"type\", \"button\", 1, \"bg-[#ff6b69]/20\", \"dark:bg-[#ff3b30]/20\", \"text-[#ff6b69]\", \"dark:text-[#ff3b30]\", \"px-6\", \"py-3\", \"rounded-xl\", \"font-medium\", \"transition-all\", \"duration-300\", \"hover:scale-105\", \"hover:bg-[#ff6b69]/30\", \"dark:hover:bg-[#ff3b30]/30\", 3, \"click\"], [1, \"fas\", \"fa-trash\", \"mr-2\"], [1, \"inline-block\", \"w-4\", \"h-4\", \"border-2\", \"border-white/30\", \"border-t-white\", \"rounded-full\", \"animate-spin\", \"mr-2\"]],\n      template: function EquipeFormComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵelement(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5);\n          i0.ɵɵelement(6, \"div\", 6)(7, \"div\", 6)(8, \"div\", 6)(9, \"div\", 6)(10, \"div\", 6)(11, \"div\", 6)(12, \"div\", 6)(13, \"div\", 6)(14, \"div\", 6)(15, \"div\", 6)(16, \"div\", 6);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(17, \"div\", 7)(18, \"div\", 8);\n          i0.ɵɵelement(19, \"div\", 9)(20, \"div\", 10);\n          i0.ɵɵelementStart(21, \"div\", 11)(22, \"div\", 12)(23, \"div\", 13)(24, \"h1\", 14);\n          i0.ɵɵtext(25);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"p\", 15);\n          i0.ɵɵtext(27);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(28, \"button\", 16);\n          i0.ɵɵlistener(\"click\", function EquipeFormComponent_Template_button_click_28_listener() {\n            return ctx.cancel();\n          });\n          i0.ɵɵelement(29, \"i\", 17);\n          i0.ɵɵtext(30, \" Retour \\u00E0 la liste \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(31, EquipeFormComponent_div_31_Template, 6, 0, \"div\", 18);\n          i0.ɵɵtemplate(32, EquipeFormComponent_div_32_Template, 10, 1, \"div\", 19);\n          i0.ɵɵtemplate(33, EquipeFormComponent_div_33_Template, 50, 14, \"div\", 20);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(25);\n          i0.ɵɵtextInterpolate1(\" \", ctx.isEditMode ? \"Modifier l'\\u00E9quipe\" : \"Nouvelle \\u00E9quipe\", \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.isEditMode ? \"Modifiez les informations et les membres de votre \\u00E9quipe\" : \"Cr\\u00E9ez une nouvelle \\u00E9quipe pour organiser vos projets et membres\", \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n        }\n      },\n      dependencies: [i6.NgClass, i6.NgIf, i7.ɵNgNoValidate, i7.NgControlStatusGroup, i7.NgForm],\n      styles: [\".cursor-pointer[_ngcontent-%COMP%] {\\n      cursor: pointer;\\n    }\\n    summary[_ngcontent-%COMP%]:hover {\\n      text-decoration: underline;\\n    }\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImVxdWlwZS1mb3JtLmNvbXBvbmVudC5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkdBQUc7TUFDRyxlQUFlO0lBQ2pCO0lBQ0E7TUFDRSwwQkFBMEI7SUFDNUIiLCJmaWxlIjoiZXF1aXBlLWZvcm0uY29tcG9uZW50LmNzcyIsInNvdXJjZXNDb250ZW50IjpbIiAgIC5jdXJzb3ItcG9pbnRlciB7XHJcbiAgICAgIGN1cnNvcjogcG9pbnRlcjtcclxuICAgIH1cclxuICAgIHN1bW1hcnk6aG92ZXIge1xyXG4gICAgICB0ZXh0LWRlY29yYXRpb246IHVuZGVybGluZTtcclxuICAgIH0iXX0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvYWRtaW4vZXF1aXBlcy9lcXVpcGUtZm9ybS9lcXVpcGUtZm9ybS5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJHQUFHO01BQ0csZUFBZTtJQUNqQjtJQUNBO01BQ0UsMEJBQTBCO0lBQzVCO0FBQ0osd2NBQXdjIiwic291cmNlc0NvbnRlbnQiOlsiICAgLmN1cnNvci1wb2ludGVyIHtcclxuICAgICAgY3Vyc29yOiBwb2ludGVyO1xyXG4gICAgfVxyXG4gICAgc3VtbWFyeTpob3ZlciB7XHJcbiAgICAgIHRleHQtZGVjb3JhdGlvbjogdW5kZXJsaW5lO1xyXG4gICAgfSJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r1", "error", "ɵɵlistener", "EquipeFormComponent_div_33_button_45_Template_button_click_0_listener", "ɵɵrestoreView", "_r10", "ctx_r9", "ɵɵnextContext", "ɵɵresetView", "deleteEquipe", "ɵɵproperty", "ɵɵpureFunction2", "_c0", "ctx_r8", "isEditMode", "EquipeFormComponent_div_33_Template_form_ngSubmit_11_listener", "_r12", "ctx_r11", "onSubmit", "EquipeFormComponent_div_33_Template_input_input_20_listener", "_r3", "ɵɵreference", "ctx_r13", "updateName", "value", "ɵɵtemplate", "EquipeFormComponent_div_33_div_22_Template", "EquipeFormComponent_div_33_Template_textarea_input_31_listener", "_r5", "ctx_r14", "updateDescription", "EquipeFormComponent_div_33_Template_button_click_42_listener", "ctx_r15", "cancel", "EquipeFormComponent_div_33_button_45_Template", "EquipeFormComponent_div_33_span_47_Template", "EquipeFormComponent_div_33_i_48_Template", "_c1", "ctx_r2", "equipe", "name", "nameExists", "description", "admin", "equipeId", "submitting", "nameError", "descriptionError", "EquipeFormComponent", "constructor", "equipeService", "membreService", "userService", "route", "router", "notificationService", "loading", "checkingName", "existingEquipes", "availableMembers", "availableUsers", "ngOnInit", "console", "log", "loadAllEquipes", "loadAllMembers", "loadAllUsers", "snapshot", "paramMap", "get", "loadEquipe", "setTimeout", "addButton", "document", "getElementById", "addEventListener", "getMembres", "subscribe", "next", "membres", "token", "localStorage", "getItem", "getAllUsers", "users", "getEquipes", "equipes", "id", "getEquipe", "data", "_id", "members", "length", "loadMembersDetails", "for<PERSON>ach", "membreId", "user", "find", "u", "email", "profession", "role", "getUserById", "userData", "index", "findIndex", "some", "push", "checkNameExists", "e", "warn", "equipeToSave", "updateEquipe", "response", "showSuccess", "navigate", "message", "showError", "addEquipe", "confirm", "addMembreToEquipe", "includes", "userName", "firstName", "lastName", "membre", "getMembreName", "m", "getMembreEmail", "getMembreProfession", "getMembreRole", "_membreId", "removeMembreFromEquipe", "membreName", "status", "ɵɵdirectiveInject", "i1", "EquipeService", "i2", "MembreService", "i3", "AuthService", "i4", "ActivatedRoute", "Router", "i5", "NotificationService", "selectors", "decls", "vars", "consts", "template", "EquipeFormComponent_Template", "rf", "ctx", "EquipeFormComponent_Template_button_click_28_listener", "EquipeFormComponent_div_31_Template", "EquipeFormComponent_div_32_Template", "EquipeFormComponent_div_33_Template"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\admin\\equipes\\equipe-form\\equipe-form.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { EquipeService } from 'src/app/services/equipe.service';\nimport { MembreService } from 'src/app/services/membre.service';\nimport { AuthService } from 'src/app/services/auth.service';\nimport { NotificationService } from 'src/app/services/notification.service';\nimport { Equipe } from 'src/app/models/equipe.model';\nimport { Membre } from 'src/app/models/membre.model';\nimport { User } from 'src/app/models/user.model';\n\n@Component({\n  selector: 'app-equipe-form',\n  template: `\n    <div\n      class=\"min-h-screen bg-[#f0f4f8] dark:bg-[#0a0a0a] relative overflow-hidden\"\n    >\n      <!-- Background decorative elements -->\n      <div class=\"absolute inset-0 overflow-hidden pointer-events-none\">\n        <div\n          class=\"absolute top-[15%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-br from-[#dac4ea]/5 to-transparent dark:from-[#00f7ff]/3 dark:to-transparent blur-3xl\"\n        ></div>\n        <div\n          class=\"absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#dac4ea]/5 to-transparent dark:from-[#00f7ff]/3 dark:to-transparent blur-3xl\"\n        ></div>\n\n        <!-- Grid pattern -->\n        <div class=\"absolute inset-0 opacity-5 dark:opacity-[0.03]\">\n          <div class=\"h-full grid grid-cols-12\">\n            <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n            <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n            <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n            <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n            <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n            <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n            <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n            <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n            <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n            <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n            <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"max-w-4xl mx-auto p-6 relative z-10\">\n        <!-- Header -->\n        <div class=\"mb-8 relative\">\n          <div\n            class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea]\"\n          ></div>\n          <div\n            class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea] blur-md\"\n          ></div>\n\n          <div\n            class=\"bg-white dark:bg-[#1a1a1a] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] p-6 backdrop-blur-sm border border-[#dac4ea]/20 dark:border-[#00f7ff]/20\"\n          >\n            <div\n              class=\"flex flex-col lg:flex-row lg:items-center lg:justify-between\"\n            >\n              <div class=\"mb-4 lg:mb-0\">\n                <h1\n                  class=\"text-3xl font-bold text-[#dac4ea] dark:text-[#00f7ff] mb-2 tracking-wide\"\n                >\n                  {{ isEditMode ? \"Modifier l'équipe\" : 'Nouvelle équipe' }}\n                </h1>\n                <p class=\"text-[#6d6870] dark:text-[#e0e0e0] text-sm\">\n                  {{\n                    isEditMode\n                      ? 'Modifiez les informations et les membres de votre équipe'\n                      : 'Créez une nouvelle équipe pour organiser vos projets et membres'\n                  }}\n                </p>\n              </div>\n\n              <button\n                (click)=\"cancel()\"\n                class=\"bg-[#dac4ea]/20 dark:bg-[#00f7ff]/20 text-[#dac4ea] dark:text-[#00f7ff] px-6 py-3 rounded-xl font-medium transition-all duration-300 hover:scale-105 hover:bg-[#dac4ea]/30 dark:hover:bg-[#00f7ff]/30\"\n              >\n                <i class=\"fas fa-arrow-left mr-2\"></i>\n                Retour à la liste\n              </button>\n            </div>\n          </div>\n        </div>\n\n        <!-- Loading Indicator -->\n        <div\n          *ngIf=\"loading\"\n          class=\"flex flex-col items-center justify-center py-16\"\n        >\n          <div class=\"relative\">\n            <div\n              class=\"w-12 h-12 border-3 border-[#dac4ea]/20 dark:border-[#00f7ff]/20 border-t-[#dac4ea] dark:border-t-[#00f7ff] rounded-full animate-spin\"\n            ></div>\n            <div\n              class=\"absolute inset-0 bg-[#dac4ea]/20 dark:bg-[#00f7ff]/20 blur-xl rounded-full transform scale-150 -z-10\"\n            ></div>\n          </div>\n          <p\n            class=\"mt-4 text-[#dac4ea] dark:text-[#00f7ff] text-sm font-medium tracking-wide\"\n          >\n            Chargement des données...\n          </p>\n        </div>\n\n        <!-- Error Alert -->\n        <div *ngIf=\"error\" class=\"mb-6\">\n          <div\n            class=\"bg-[#ff6b69]/10 dark:bg-[#ff3b30]/10 border-l-4 border-[#ff6b69] dark:border-[#ff3b30] rounded-lg p-4 backdrop-blur-sm\"\n          >\n            <div class=\"flex items-start\">\n              <div class=\"text-[#ff6b69] dark:text-[#ff3b30] mr-3 text-xl\">\n                <i class=\"fas fa-exclamation-triangle\"></i>\n              </div>\n              <div class=\"flex-1\">\n                <h3\n                  class=\"font-semibold text-[#ff6b69] dark:text-[#ff3b30] mb-1\"\n                >\n                  Erreur\n                </h3>\n                <p class=\"text-sm text-[#6d6870] dark:text-[#e0e0e0]\">\n                  {{ error }}\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Formulaire principal -->\n        <div *ngIf=\"!loading\" class=\"mb-8 relative\">\n          <div\n            class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea]\"\n          ></div>\n          <div\n            class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea] blur-md\"\n          ></div>\n\n          <div\n            class=\"bg-white dark:bg-[#1a1a1a] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] overflow-hidden border border-[#dac4ea]/20 dark:border-[#00f7ff]/20\"\n          >\n            <!-- Header -->\n            <div\n              class=\"bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea] p-6\"\n            >\n              <h3 class=\"text-xl font-bold text-white mb-1 flex items-center\">\n                <i\n                  class=\"fas mr-2\"\n                  [ngClass]=\"{\n                    'fa-edit': isEditMode,\n                    'fa-plus-circle': !isEditMode\n                  }\"\n                ></i>\n                {{\n                  isEditMode\n                    ? \"Informations de l'équipe\"\n                    : 'Détails de la nouvelle équipe'\n                }}\n              </h3>\n              <p class=\"text-white/80 text-sm\">\n                Remplissez les informations de base de l'équipe\n              </p>\n            </div>\n\n            <!-- Form -->\n            <div class=\"p-6\">\n              <form (ngSubmit)=\"onSubmit()\" class=\"space-y-6\">\n                <!-- Nom de l'équipe -->\n                <div>\n                  <label\n                    class=\"block text-sm font-medium text-[#dac4ea] dark:text-[#00f7ff] mb-2\"\n                  >\n                    Nom de l'équipe\n                    <span class=\"text-[#ff6b69] dark:text-[#ff3b30]\">*</span>\n                  </label>\n                  <div class=\"relative\">\n                    <div\n                      class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\"\n                    >\n                      <i\n                        class=\"fas fa-users text-[#dac4ea] dark:text-[#00f7ff]\"\n                      ></i>\n                    </div>\n                    <input\n                      #nameInput\n                      type=\"text\"\n                      [value]=\"equipe.name || ''\"\n                      (input)=\"updateName(nameInput.value)\"\n                      class=\"w-full pl-10 pr-4 py-3 bg-[#f0f4f8] dark:bg-[#0a0a0a] border border-[#dac4ea]/20 dark:border-[#00f7ff]/20 rounded-lg text-[#6d6870] dark:text-[#e0e0e0] placeholder-[#6d6870]/50 dark:placeholder-[#a0a0a0] focus:outline-none focus:ring-2 focus:ring-[#dac4ea] dark:focus:ring-[#00f7ff] focus:border-transparent transition-all\"\n                      placeholder=\"Entrez le nom de l'équipe\"\n                      required\n                      minlength=\"3\"\n                    />\n                  </div>\n                  <div\n                    *ngIf=\"nameExists\"\n                    class=\"mt-1 text-sm text-[#ff6b69] dark:text-[#ff3b30] flex items-center\"\n                  >\n                    <i class=\"fas fa-exclamation-triangle mr-1\"></i>\n                    Ce nom d'équipe existe déjà. Veuillez en choisir un autre.\n                  </div>\n                </div>\n\n                <!-- Description -->\n                <div>\n                  <label\n                    class=\"block text-sm font-medium text-[#dac4ea] dark:text-[#00f7ff] mb-2\"\n                  >\n                    Description\n                    <span class=\"text-[#ff6b69] dark:text-[#ff3b30]\">*</span>\n                  </label>\n                  <div class=\"relative\">\n                    <div class=\"absolute top-3 left-3 pointer-events-none\">\n                      <i\n                        class=\"fas fa-file-alt text-[#dac4ea] dark:text-[#00f7ff]\"\n                      ></i>\n                    </div>\n                    <textarea\n                      #descInput\n                      [value]=\"equipe.description || ''\"\n                      (input)=\"updateDescription(descInput.value)\"\n                      rows=\"4\"\n                      class=\"w-full pl-10 pr-4 py-3 bg-[#f0f4f8] dark:bg-[#0a0a0a] border border-[#dac4ea]/20 dark:border-[#00f7ff]/20 rounded-lg text-[#6d6870] dark:text-[#e0e0e0] placeholder-[#6d6870]/50 dark:placeholder-[#a0a0a0] focus:outline-none focus:ring-2 focus:ring-[#dac4ea] dark:focus:ring-[#00f7ff] focus:border-transparent transition-all resize-none\"\n                      placeholder=\"Décrivez l'objectif et les activités de cette équipe\"\n                      required\n                      minlength=\"10\"\n                    ></textarea>\n                  </div>\n                </div>\n\n                <!-- Admin info -->\n                <input type=\"hidden\" [value]=\"equipe.admin\" />\n                <div\n                  class=\"bg-[#dac4ea]/10 dark:bg-[#00f7ff]/10 border-l-4 border-[#dac4ea] dark:border-[#00f7ff] rounded-lg p-4\"\n                >\n                  <div class=\"flex items-center\">\n                    <div\n                      class=\"text-[#dac4ea] dark:text-[#00f7ff] mr-3 text-lg\"\n                    >\n                      <i class=\"fas fa-info-circle\"></i>\n                    </div>\n                    <div class=\"text-sm text-[#6d6870] dark:text-[#e0e0e0]\">\n                      Un administrateur par défaut sera assigné à cette équipe.\n                    </div>\n                  </div>\n                </div>\n\n                <!-- Buttons -->\n                <div class=\"flex items-center justify-between pt-4\">\n                  <div class=\"flex items-center space-x-4\">\n                    <button\n                      type=\"button\"\n                      (click)=\"cancel()\"\n                      class=\"bg-[#6d6870]/20 dark:bg-[#a0a0a0]/20 text-[#6d6870] dark:text-[#e0e0e0] px-6 py-3 rounded-xl font-medium transition-all duration-300 hover:scale-105 hover:bg-[#6d6870]/30 dark:hover:bg-[#a0a0a0]/30\"\n                    >\n                      <i class=\"fas fa-arrow-left mr-2\"></i>\n                      Retour\n                    </button>\n\n                    <button\n                      *ngIf=\"isEditMode && equipeId\"\n                      type=\"button\"\n                      (click)=\"deleteEquipe()\"\n                      class=\"bg-[#ff6b69]/20 dark:bg-[#ff3b30]/20 text-[#ff6b69] dark:text-[#ff3b30] px-6 py-3 rounded-xl font-medium transition-all duration-300 hover:scale-105 hover:bg-[#ff6b69]/30 dark:hover:bg-[#ff3b30]/30\"\n                    >\n                      <i class=\"fas fa-trash mr-2\"></i>\n                      Supprimer\n                    </button>\n                  </div>\n\n                  <button\n                    type=\"submit\"\n                    [disabled]=\"\n                      submitting ||\n                      !equipe.name ||\n                      !equipe.description ||\n                      nameExists ||\n                      nameError ||\n                      descriptionError\n                    \"\n                    class=\"relative overflow-hidden group bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea] text-white px-6 py-3 rounded-xl font-medium transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-[0_0_25px_rgba(218,196,234,0.4)] dark:hover:shadow-[0_0_25px_rgba(0,247,255,0.4)] disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100\"\n                  >\n                    <span\n                      *ngIf=\"submitting\"\n                      class=\"inline-block w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2\"\n                    ></span>\n                    <i\n                      *ngIf=\"!submitting\"\n                      class=\"fas mr-2\"\n                      [ngClass]=\"{\n                        'fa-save': isEditMode,\n                        'fa-plus-circle': !isEditMode\n                      }\"\n                    ></i>\n                    {{ isEditMode ? 'Mettre à jour' : \"Créer l'équipe\" }}\n                  </button>\n                </div>\n              </form>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  `,\n  styleUrls: ['./equipe-form.component.css'],\n})\nexport class EquipeFormComponent implements OnInit {\n  equipe: Equipe = {\n    name: '',\n    description: '',\n    admin: '65f1e5b3a1d8f3c8c0f9e8d7', // ID temporaire\n  };\n  isEditMode = false;\n  loading = false;\n  submitting = false;\n  error: string | null = null;\n  equipeId: string | null = null;\n  nameExists = false;\n  nameError = false;\n  descriptionError = false;\n  checkingName = false;\n  existingEquipes: Equipe[] = [];\n  availableMembers: Membre[] = []; // Liste des membres disponibles\n  availableUsers: User[] = []; // Liste des utilisateurs disponibles\n\n  constructor(\n    private equipeService: EquipeService,\n    private membreService: MembreService,\n    private userService: AuthService,\n    private route: ActivatedRoute,\n    private router: Router,\n    private notificationService: NotificationService\n  ) {}\n\n  ngOnInit(): void {\n    console.log('EquipeFormComponent initialized');\n\n    // Ensure equipe is always defined with admin\n    if (!this.equipe) {\n      this.equipe = {\n        name: '',\n        description: '',\n        admin: '65f1e5b3a1d8f3c8c0f9e8d7', // ID temporaire\n      };\n    }\n\n    // Charger toutes les équipes pour vérifier les noms existants\n    this.loadAllEquipes();\n\n    // Charger tous les membres disponibles\n    this.loadAllMembers();\n\n    // Charger tous les utilisateurs disponibles\n    this.loadAllUsers();\n\n    try {\n      // Vérifier si nous sommes en mode édition (si un ID est présent dans l'URL)\n      this.equipeId = this.route.snapshot.paramMap.get('id');\n      this.isEditMode = !!this.equipeId;\n      console.log('Edit mode:', this.isEditMode, 'ID:', this.equipeId);\n\n      if (this.isEditMode && this.equipeId) {\n        this.loadEquipe(this.equipeId);\n\n        // Ajouter un délai pour s'assurer que l'équipe est chargée\n        setTimeout(() => {\n          console.log('Après délai - this.equipeId:', this.equipeId);\n          console.log('Après délai - this.equipe:', this.equipe);\n        }, 1000);\n      }\n    } catch (error) {\n      console.error('Error in ngOnInit:', error);\n      this.error = \"Erreur d'initialisation\";\n    }\n\n    // Ajouter un gestionnaire d'événements pour le bouton d'ajout de membre\n    setTimeout(() => {\n      const addButton = document.getElementById('addMembreButton');\n      if (addButton) {\n        console.log(\"Bouton d'ajout de membre trouvé\");\n        addButton.addEventListener('click', () => {\n          console.log(\"Bouton d'ajout de membre cliqué\");\n        });\n      } else {\n        console.log(\"Bouton d'ajout de membre non trouvé\");\n      }\n    }, 2000);\n  }\n\n  loadAllMembers(): void {\n    this.membreService.getMembres().subscribe({\n      next: (membres) => {\n        this.availableMembers = membres;\n        console.log('Membres disponibles chargés:', membres);\n      },\n      error: (error) => {\n        console.error('Erreur lors du chargement des membres:', error);\n        this.error =\n          'Impossible de charger la liste des membres. Veuillez réessayer plus tard.';\n      },\n    });\n  }\n\n  loadAllUsers(): void {\n    const token = localStorage.getItem('token');\n    if (token) {\n      this.userService.getAllUsers(token).subscribe({\n        next: (users: any) => {\n          this.availableUsers = users;\n          console.log('Utilisateurs disponibles chargés:', users);\n        },\n        error: (error) => {\n          console.error('Erreur lors du chargement des utilisateurs:', error);\n          this.error =\n            'Impossible de charger la liste des utilisateurs. Veuillez réessayer plus tard.';\n        },\n      });\n    }\n  }\n\n  loadAllEquipes(): void {\n    this.equipeService.getEquipes().subscribe({\n      next: (equipes) => {\n        this.existingEquipes = equipes;\n        console.log('Équipes existantes chargées:', equipes);\n      },\n      error: (error) => {\n        console.error('Erreur lors du chargement des équipes:', error);\n      },\n    });\n  }\n\n  loadEquipe(id: string): void {\n    console.log('Loading equipe with ID:', id);\n    this.loading = true;\n    this.error = null;\n\n    this.equipeService.getEquipe(id).subscribe({\n      next: (data) => {\n        console.log('Équipe chargée:', data);\n        this.equipe = data;\n\n        // Vérifier que l'ID est correctement défini\n        console.log(\"ID de l'équipe après chargement:\", this.equipe._id);\n        console.log('this.equipeId:', this.equipeId);\n\n        // Si l'équipe a des membres, récupérer les informations de chaque membre\n        if (this.equipe.members && this.equipe.members.length > 0) {\n          this.loadMembersDetails();\n        }\n\n        this.loading = false;\n      },\n      error: (error) => {\n        console.error(\"Erreur lors du chargement de l'équipe:\", error);\n        this.error =\n          \"Impossible de charger les détails de l'équipe. Veuillez réessayer plus tard.\";\n        this.loading = false;\n      },\n    });\n  }\n\n  // Méthode pour récupérer les détails des membres de l'équipe\n  loadMembersDetails(): void {\n    if (!this.equipe.members || this.equipe.members.length === 0) {\n      return;\n    }\n\n    console.log(\"Chargement des détails des membres de l'équipe...\");\n\n    // Pour chaque membre de l'équipe, essayer de trouver ses informations dans la liste des utilisateurs\n    this.equipe.members.forEach((membreId) => {\n      // Chercher d'abord dans la liste des utilisateurs\n      const user = this.availableUsers.find(\n        (u) => u._id === membreId || u.id === membreId\n      );\n      if (user) {\n        console.log(\n          `Membre ${membreId} trouvé dans la liste des utilisateurs:`,\n          user\n        );\n\n        // Vérifier si toutes les informations nécessaires sont présentes\n        if (!user.email || (!user.profession && !user.role)) {\n          // Si des informations manquent, essayer de les récupérer depuis l'API\n          const token = localStorage.getItem('token');\n          if (token) {\n            this.userService.getUserById(membreId, token).subscribe({\n              next: (userData: any) => {\n                console.log(\n                  `Détails supplémentaires de l'utilisateur ${membreId} récupérés:`,\n                  userData\n                );\n\n                // Mettre à jour l'utilisateur dans la liste avec les nouvelles informations\n                const index = this.availableUsers.findIndex(\n                  (u) => u._id === membreId || u.id === membreId\n                );\n                if (index !== -1) {\n                  this.availableUsers[index] = {\n                    ...this.availableUsers[index],\n                    ...userData,\n                  };\n                }\n              },\n              error: (error) => {\n                console.error(\n                  `Erreur lors de la récupération des détails supplémentaires de l'utilisateur ${membreId}:`,\n                  error\n                );\n              },\n            });\n          }\n        }\n      } else {\n        // Si non trouvé, essayer de récupérer l'utilisateur depuis l'API\n        const token = localStorage.getItem('token');\n        if (token) {\n          this.userService.getUserById(membreId, token).subscribe({\n            next: (userData: any) => {\n              console.log(\n                `Détails de l'utilisateur ${membreId} récupérés:`,\n                userData\n              );\n              // Ajouter l'utilisateur à la liste des utilisateurs disponibles s'il n'y est pas déjà\n              if (\n                !this.availableUsers.some(\n                  (u) => u._id === userData._id || u.id === userData.id\n                )\n              ) {\n                this.availableUsers.push(userData);\n              }\n            },\n            error: (error) => {\n              console.error(\n                `Erreur lors de la récupération des détails de l'utilisateur ${membreId}:`,\n                error\n              );\n            },\n          });\n        }\n      }\n    });\n  }\n\n  checkNameExists(name: string): boolean {\n    // En mode édition, ignorer l'équipe actuelle\n    if (this.isEditMode && this.equipeId) {\n      return this.existingEquipes.some(\n        (e) => e.name === name && e._id !== this.equipeId\n      );\n    }\n    // En mode création, vérifier tous les noms\n    return this.existingEquipes.some((e) => e.name === name);\n  }\n\n  updateName(value: string): void {\n    console.log('Name updated:', value);\n    this.equipe.name = value;\n\n    // Vérifier si le nom existe déjà\n    this.nameExists = this.checkNameExists(value);\n    if (this.nameExists) {\n      console.warn(\"Ce nom d'équipe existe déjà\");\n    }\n\n    // Vérifier si le nom a au moins 3 caractères\n    this.nameError = value.length > 0 && value.length < 3;\n    if (this.nameError) {\n      console.warn('Le nom doit contenir au moins 3 caractères');\n    }\n  }\n\n  updateDescription(value: string): void {\n    console.log('Description updated:', value);\n    this.equipe.description = value;\n\n    // Vérifier si la description a au moins 10 caractères\n    this.descriptionError = value.length > 0 && value.length < 10;\n    if (this.descriptionError) {\n      console.warn('La description doit contenir au moins 10 caractères');\n    }\n  }\n\n  onSubmit(): void {\n    console.log('Form submitted with:', this.equipe);\n\n    // Vérifier si le nom est présent et valide\n    if (!this.equipe.name) {\n      this.error = \"Le nom de l'équipe est requis.\";\n      return;\n    }\n\n    if (this.equipe.name.length < 3) {\n      this.nameError = true;\n      this.error = \"Le nom de l'équipe doit contenir au moins 3 caractères.\";\n      return;\n    }\n\n    // Vérifier si la description est présente et valide\n    if (!this.equipe.description) {\n      this.error = \"La description de l'équipe est requise.\";\n      return;\n    }\n\n    if (this.equipe.description.length < 10) {\n      this.descriptionError = true;\n      this.error =\n        \"La description de l'équipe doit contenir au moins 10 caractères.\";\n      return;\n    }\n\n    // Vérifier si le nom existe déjà avant de soumettre\n    if (this.checkNameExists(this.equipe.name)) {\n      this.nameExists = true;\n      this.error =\n        'Une équipe avec ce nom existe déjà. Veuillez choisir un autre nom.';\n      return;\n    }\n\n    this.submitting = true;\n    this.error = null;\n\n    // Créer une copie de l'objet équipe pour éviter les problèmes de référence\n    const equipeToSave: Equipe = {\n      name: this.equipe.name,\n      description: this.equipe.description || '',\n      admin: this.equipe.admin,\n    };\n\n    // Ajouter l'ID si nous sommes en mode édition\n    if (this.isEditMode && this.equipeId) {\n      equipeToSave._id = this.equipeId;\n    }\n\n    console.log('Données à envoyer:', equipeToSave);\n\n    if (this.isEditMode && this.equipeId) {\n      // Mode édition\n      this.equipeService.updateEquipe(this.equipeId, equipeToSave).subscribe({\n        next: (response) => {\n          console.log('Équipe mise à jour avec succès:', response);\n          this.submitting = false;\n          this.notificationService.showSuccess(\n            `L'équipe \"${response.name}\" a été mise à jour avec succès.`\n          );\n          this.router.navigate(['/equipes/liste']);\n        },\n        error: (error) => {\n          console.error(\"Erreur lors de la mise à jour de l'équipe:\", error);\n          this.error = `Impossible de mettre à jour l'équipe: ${error.message}`;\n          this.submitting = false;\n          this.notificationService.showError(`Erreur: ${error.message}`);\n        },\n      });\n    } else {\n      // Mode ajout\n      this.equipeService.addEquipe(equipeToSave).subscribe({\n        next: (response) => {\n          console.log('Équipe ajoutée avec succès:', response);\n          this.submitting = false;\n          this.notificationService.showSuccess(\n            `L'équipe \"${response.name}\" a été créée avec succès.`\n          );\n          this.router.navigate(['/equipes/liste']);\n        },\n        error: (error) => {\n          console.error(\"Erreur lors de l'ajout de l'équipe:\", error);\n          this.error = `Impossible d'ajouter l'équipe: ${error.message}`;\n          this.submitting = false;\n          this.notificationService.showError(`Erreur: ${error.message}`);\n        },\n      });\n    }\n  }\n\n  cancel(): void {\n    console.log('Form cancelled');\n    this.router.navigate(['/admin/equipes']);\n  }\n\n  deleteEquipe(): void {\n    if (!this.equipeId) {\n      this.error = \"Impossible de supprimer l'équipe: ID manquant\";\n      return;\n    }\n\n    if (\n      confirm(\n        `Êtes-vous sûr de vouloir supprimer l'équipe \"${this.equipe.name}\" ? Cette action est irréversible.`\n      )\n    ) {\n      this.loading = true;\n      this.error = null;\n\n      this.equipeService.deleteEquipe(this.equipeId).subscribe({\n        next: () => {\n          console.log('Équipe supprimée avec succès');\n          this.loading = false;\n          this.notificationService.showSuccess(\n            `L'équipe \"${this.equipe.name}\" a été supprimée avec succès.`\n          );\n          this.router.navigate(['/admin/equipes']);\n        },\n        error: (error) => {\n          console.error(\"Erreur lors de la suppression de l'équipe:\", error);\n          this.error = `Impossible de supprimer l'équipe: ${error.message}`;\n          this.loading = false;\n          this.notificationService.showError(`Erreur: ${error.message}`);\n        },\n      });\n    }\n  }\n\n  // Méthodes pour gérer les membres\n  addMembreToEquipe(membreId: string, role: string = 'membre'): void {\n    console.log(\n      'Début de addMembreToEquipe avec membreId:',\n      membreId,\n      'et rôle:',\n      role\n    );\n    console.log('État actuel - this.equipeId:', this.equipeId);\n    console.log('État actuel - this.equipe:', this.equipe);\n\n    // Utiliser this.equipe._id si this.equipeId n'est pas défini\n    const equipeId = this.equipeId || (this.equipe && this.equipe._id);\n\n    console.log('equipeId calculé:', equipeId);\n\n    if (!equipeId || !membreId) {\n      console.error(\"ID d'équipe ou ID de membre manquant\");\n      this.error = \"ID d'équipe ou ID de membre manquant\";\n      console.log('equipeId:', equipeId, 'membreId:', membreId);\n\n      // Afficher un message à l'utilisateur\n      this.notificationService.showError(\n        \"Impossible d'ajouter le membre: ID d'équipe ou ID de membre manquant\"\n      );\n      return;\n    }\n\n    // Vérifier si le membre est déjà dans l'équipe\n    if (this.equipe.members && this.equipe.members.includes(membreId)) {\n      this.notificationService.showError(\n        \"Ce membre fait déjà partie de l'équipe\"\n      );\n      return;\n    }\n\n    // Récupérer les informations de l'utilisateur pour afficher un message plus informatif\n    const user = this.availableUsers.find(\n      (u) => u._id === membreId || u.id === membreId\n    );\n    const userName = user\n      ? user.firstName && user.lastName\n        ? `${user.firstName} ${user.lastName}`\n        : user.name || membreId\n      : membreId;\n\n    // Créer l'objet membre avec le rôle spécifié\n    const membre: Membre = {\n      id: membreId,\n      role: role,\n    };\n\n    this.loading = true;\n\n    console.log(\n      `Ajout de l'utilisateur \"${userName}\" comme ${role} à l'équipe ${equipeId}`\n    );\n\n    this.equipeService.addMembreToEquipe(equipeId, membre).subscribe({\n      next: (response) => {\n        console.log('Membre ajouté avec succès:', response);\n        this.notificationService.showSuccess(\n          `${userName} a été ajouté comme ${\n            role === 'admin' ? 'administrateur' : 'membre'\n          } à l'équipe`\n        );\n        // Recharger l'équipe pour mettre à jour la liste des membres\n        this.loadEquipe(equipeId);\n        this.loading = false;\n      },\n      error: (error) => {\n        console.error(\"Erreur lors de l'ajout du membre:\", error);\n        this.error =\n          \"Impossible d'ajouter le membre. Veuillez réessayer plus tard.\";\n        this.notificationService.showError(\n          \"Erreur lors de l'ajout du membre: \" + error.message\n        );\n        this.loading = false;\n      },\n    });\n  }\n\n  // Méthode pour obtenir le nom complet d'un membre à partir de son ID\n  getMembreName(membreId: string): string {\n    // Chercher d'abord dans la liste des utilisateurs\n    const user = this.availableUsers.find(\n      (u) => u._id === membreId || u.id === membreId\n    );\n    if (user) {\n      if (user.firstName && user.lastName) {\n        return `${user.firstName} ${user.lastName}`;\n      } else if (user.name) {\n        return user.name;\n      }\n    }\n\n    // Chercher ensuite dans la liste des membres\n    const membre = this.availableMembers.find(\n      (m) => m._id === membreId || m.id === membreId\n    );\n    if (membre && membre.name) {\n      return membre.name;\n    }\n\n    // Si aucun nom n'est trouvé, retourner l'ID\n    return membreId;\n  }\n\n  // Méthode pour obtenir l'email d'un membre\n  getMembreEmail(membreId: string): string {\n    const user = this.availableUsers.find(\n      (u) => u._id === membreId || u.id === membreId\n    );\n    if (user && user.email) {\n      return user.email;\n    }\n    return 'Non renseigné';\n  }\n\n  // Méthode pour obtenir la profession d'un membre\n  getMembreProfession(membreId: string): string {\n    const user = this.availableUsers.find(\n      (u) => u._id === membreId || u.id === membreId\n    );\n    if (user) {\n      if (user.profession) {\n        return user.profession === 'etudiant' ? 'Étudiant' : 'Professeur';\n      } else if (user.role) {\n        return user.role === 'etudiant' ? 'Étudiant' : 'Professeur';\n      }\n    }\n    return 'Non spécifié';\n  }\n\n  // Méthode pour obtenir le rôle d'un membre dans l'équipe\n  getMembreRole(_membreId: string): string {\n    // Cette méthode nécessiterait d'avoir accès aux rôles des membres dans l'équipe\n    // Pour l'instant, nous retournons une valeur par défaut\n    return 'Membre';\n  }\n\n  removeMembreFromEquipe(membreId: string): void {\n    console.log('Méthode removeMembreFromEquipe appelée avec ID:', membreId);\n    console.log('État actuel - this.equipeId:', this.equipeId);\n    console.log('État actuel - this.equipe:', this.equipe);\n\n    // Utiliser this.equipe._id si this.equipeId n'est pas défini\n    const equipeId = this.equipeId || (this.equipe && this.equipe._id);\n\n    if (!equipeId) {\n      console.error(\"ID d'équipe manquant\");\n      this.error = \"ID d'équipe manquant. Impossible de retirer le membre.\";\n      this.notificationService.showError(\n        \"ID d'équipe manquant. Impossible de retirer le membre.\"\n      );\n      return;\n    }\n\n    if (!membreId) {\n      console.error('ID de membre manquant');\n      this.error = 'ID de membre manquant. Impossible de retirer le membre.';\n      this.notificationService.showError(\n        'ID de membre manquant. Impossible de retirer le membre.'\n      );\n      return;\n    }\n\n    // Obtenir le nom du membre pour l'afficher dans le message de confirmation\n    const membreName = this.getMembreName(membreId);\n\n    console.log(\n      `Tentative de retrait de l'utilisateur ${membreId} (${membreName}) de l'équipe ${equipeId}`\n    );\n\n    try {\n      if (\n        confirm(`Êtes-vous sûr de vouloir retirer ${membreName} de l'équipe?`)\n      ) {\n        console.log('Confirmation acceptée, suppression en cours...');\n\n        this.loading = true;\n        this.error = null;\n\n        // Ajouter un délai pour s'assurer que l'utilisateur voit le chargement\n        setTimeout(() => {\n          this.equipeService\n            .removeMembreFromEquipe(equipeId, membreId)\n            .subscribe({\n              next: (response) => {\n                console.log(\n                  `Utilisateur \"${membreName}\" retiré avec succès de l'équipe:`,\n                  response\n                );\n                this.loading = false;\n                this.notificationService.showSuccess(\n                  `${membreName} a été retiré avec succès de l'équipe`\n                );\n\n                // Recharger l'équipe pour mettre à jour la liste des membres\n                this.loadEquipe(equipeId);\n              },\n              error: (error) => {\n                console.error(\n                  `Erreur lors du retrait de l'utilisateur \"${membreName}\":`,\n                  error\n                );\n                console.error(\"Détails de l'erreur:\", {\n                  status: error.status,\n                  message: error.message,\n                  error: error,\n                });\n\n                this.loading = false;\n                this.error = `Impossible de retirer l'utilisateur \"${membreName}\" de l'équipe: ${\n                  error.message || 'Erreur inconnue'\n                }`;\n                this.notificationService.showError(\n                  `Erreur lors du retrait du membre: ${this.error}`\n                );\n              },\n            });\n        }, 500);\n      } else {\n        console.log(\"Suppression annulée par l'utilisateur\");\n      }\n    } catch (error: any) {\n      console.error('Exception lors du retrait du membre:', error);\n      this.error = `Exception: ${error?.message || 'Erreur inconnue'}`;\n      this.notificationService.showError(`Exception: ${this.error}`);\n    }\n  }\n\n  // Méthode pour supprimer l'équipe\n  deleteEquipe(): void {\n    console.log('Méthode deleteEquipe appelée dans equipe-form.component.ts');\n    console.log('État actuel - this.equipeId:', this.equipeId);\n    console.log('État actuel - this.equipe:', this.equipe);\n\n    // Utiliser this.equipe._id si this.equipeId n'est pas défini\n    const equipeId = this.equipeId || (this.equipe && this.equipe._id);\n\n    if (!equipeId) {\n      console.error(\"ID d'équipe manquant\");\n      this.error = \"ID d'équipe manquant. Impossible de supprimer l'équipe.\";\n      this.notificationService.showError(\n        \"ID d'équipe manquant. Impossible de supprimer l'équipe.\"\n      );\n      return;\n    }\n\n    console.log(\"ID de l'équipe à supprimer (final):\", equipeId);\n\n    try {\n      if (\n        confirm(\n          `Êtes-vous sûr de vouloir supprimer l'équipe \"${this.equipe.name}\"? Cette action est irréversible.`\n        )\n      ) {\n        console.log('Confirmation acceptée, suppression en cours...');\n\n        this.loading = true;\n        this.error = null;\n\n        // Ajouter un délai pour s'assurer que l'utilisateur voit le chargement\n        setTimeout(() => {\n          this.equipeService.deleteEquipe(equipeId).subscribe({\n            next: (response) => {\n              console.log('Équipe supprimée avec succès, réponse:', response);\n              this.loading = false;\n              this.notificationService.showSuccess(\n                `L'équipe \"${this.equipe.name}\" a été supprimée avec succès.`\n              );\n\n              // Ajouter un délai avant la redirection\n              setTimeout(() => {\n                this.router.navigate(['/equipes/liste']);\n              }, 500);\n            },\n            error: (error) => {\n              console.error(\n                \"Erreur lors de la suppression de l'équipe:\",\n                error\n              );\n              console.error(\"Détails de l'erreur:\", {\n                status: error.status,\n                message: error.message,\n                error: error,\n              });\n\n              this.loading = false;\n              this.error = `Impossible de supprimer l'équipe: ${\n                error.message || 'Erreur inconnue'\n              }`;\n              this.notificationService.showError(\n                `Erreur lors de la suppression: ${this.error}`\n              );\n            },\n          });\n        }, 500);\n      } else {\n        console.log(\"Suppression annulée par l'utilisateur\");\n      }\n    } catch (error: any) {\n      console.error('Exception lors de la suppression:', error);\n      this.error = `Exception: ${error?.message || 'Erreur inconnue'}`;\n      this.notificationService.showError(`Exception: ${this.error}`);\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;;IAsFQA,EAAA,CAAAC,cAAA,cAGC;IAEGD,EAAA,CAAAE,SAAA,cAEO;IAITF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,YAEC;IACCD,EAAA,CAAAI,MAAA,uCACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;;IAINH,EAAA,CAAAC,cAAA,cAAgC;IAMxBD,EAAA,CAAAE,SAAA,YAA2C;IAC7CF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAoB;IAIhBD,EAAA,CAAAI,MAAA,eACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,YAAsD;IACpDD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,KAAA,MACF;;;;;IAuEER,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAE,SAAA,YAAgD;IAChDF,EAAA,CAAAI,MAAA,kFACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;;;IA2DJH,EAAA,CAAAC,cAAA,iBAKC;IAFCD,EAAA,CAAAS,UAAA,mBAAAC,sEAAA;MAAAV,EAAA,CAAAW,aAAA,CAAAC,IAAA;MAAA,MAAAC,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAASd,EAAA,CAAAe,WAAA,CAAAF,MAAA,CAAAG,YAAA,EAAc;IAAA,EAAC;IAGxBhB,EAAA,CAAAE,SAAA,YAAiC;IACjCF,EAAA,CAAAI,MAAA,kBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;;IAeTH,EAAA,CAAAE,SAAA,eAGQ;;;;;;;;;;;IACRF,EAAA,CAAAE,SAAA,YAOK;;;;IAJHF,EAAA,CAAAiB,UAAA,YAAAjB,EAAA,CAAAkB,eAAA,IAAAC,GAAA,EAAAC,MAAA,CAAAC,UAAA,GAAAD,MAAA,CAAAC,UAAA,EAGE;;;;;;;;;;;;IAlKhBrB,EAAA,CAAAC,cAAA,aAA4C;IAC1CD,EAAA,CAAAE,SAAA,aAEO;IAKPF,EAAA,CAAAC,cAAA,cAEC;IAMKD,EAAA,CAAAE,SAAA,YAMK;IACLF,EAAA,CAAAI,MAAA,GAKF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,YAAiC;IAC/BD,EAAA,CAAAI,MAAA,6DACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAINH,EAAA,CAAAC,cAAA,eAAiB;IACTD,EAAA,CAAAS,UAAA,sBAAAa,8DAAA;MAAAtB,EAAA,CAAAW,aAAA,CAAAY,IAAA;MAAA,MAAAC,OAAA,GAAAxB,EAAA,CAAAc,aAAA;MAAA,OAAYd,EAAA,CAAAe,WAAA,CAAAS,OAAA,CAAAC,QAAA,EAAU;IAAA,EAAC;IAE3BzB,EAAA,CAAAC,cAAA,WAAK;IAIDD,EAAA,CAAAI,MAAA,8BACA;IAAAJ,EAAA,CAAAC,cAAA,gBAAiD;IAAAD,EAAA,CAAAI,MAAA,SAAC;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAE3DH,EAAA,CAAAC,cAAA,eAAsB;IAIlBD,EAAA,CAAAE,SAAA,aAEK;IACPF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,qBASE;IALAD,EAAA,CAAAS,UAAA,mBAAAiB,4DAAA;MAAA1B,EAAA,CAAAW,aAAA,CAAAY,IAAA;MAAA,MAAAI,GAAA,GAAA3B,EAAA,CAAA4B,WAAA;MAAA,MAAAC,OAAA,GAAA7B,EAAA,CAAAc,aAAA;MAAA,OAASd,EAAA,CAAAe,WAAA,CAAAc,OAAA,CAAAC,UAAA,CAAAH,GAAA,CAAAI,KAAA,CAA2B;IAAA,EAAC;IAJvC/B,EAAA,CAAAG,YAAA,EASE;IAEJH,EAAA,CAAAgC,UAAA,KAAAC,0CAAA,kBAMM;IACRjC,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,WAAK;IAIDD,EAAA,CAAAI,MAAA,qBACA;IAAAJ,EAAA,CAAAC,cAAA,gBAAiD;IAAAD,EAAA,CAAAI,MAAA,SAAC;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAE3DH,EAAA,CAAAC,cAAA,eAAsB;IAElBD,EAAA,CAAAE,SAAA,aAEK;IACPF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,wBASC;IANCD,EAAA,CAAAS,UAAA,mBAAAyB,+DAAA;MAAAlC,EAAA,CAAAW,aAAA,CAAAY,IAAA;MAAA,MAAAY,GAAA,GAAAnC,EAAA,CAAA4B,WAAA;MAAA,MAAAQ,OAAA,GAAApC,EAAA,CAAAc,aAAA;MAAA,OAASd,EAAA,CAAAe,WAAA,CAAAqB,OAAA,CAAAC,iBAAA,CAAAF,GAAA,CAAAJ,KAAA,CAAkC;IAAA,EAAC;IAM7C/B,EAAA,CAAAG,YAAA,EAAW;IAKhBH,EAAA,CAAAE,SAAA,iBAA8C;IAC9CF,EAAA,CAAAC,cAAA,eAEC;IAKKD,EAAA,CAAAE,SAAA,aAAkC;IACpCF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAwD;IACtDD,EAAA,CAAAI,MAAA,uFACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAKVH,EAAA,CAAAC,cAAA,eAAoD;IAI9CD,EAAA,CAAAS,UAAA,mBAAA6B,6DAAA;MAAAtC,EAAA,CAAAW,aAAA,CAAAY,IAAA;MAAA,MAAAgB,OAAA,GAAAvC,EAAA,CAAAc,aAAA;MAAA,OAASd,EAAA,CAAAe,WAAA,CAAAwB,OAAA,CAAAC,MAAA,EAAQ;IAAA,EAAC;IAGlBxC,EAAA,CAAAE,SAAA,aAAsC;IACtCF,EAAA,CAAAI,MAAA,gBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAAgC,UAAA,KAAAS,6CAAA,qBAQS;IACXzC,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,kBAWC;IACCD,EAAA,CAAAgC,UAAA,KAAAU,2CAAA,mBAGQ;IACR1C,EAAA,CAAAgC,UAAA,KAAAW,wCAAA,gBAOK;IACL3C,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;IAnJTH,EAAA,CAAAK,SAAA,GAGE;IAHFL,EAAA,CAAAiB,UAAA,YAAAjB,EAAA,CAAAkB,eAAA,KAAA0B,GAAA,EAAAC,MAAA,CAAAxB,UAAA,GAAAwB,MAAA,CAAAxB,UAAA,EAGE;IAEJrB,EAAA,CAAAK,SAAA,GAKF;IALEL,EAAA,CAAAM,kBAAA,MAAAuC,MAAA,CAAAxB,UAAA,oFAKF;IA4BQrB,EAAA,CAAAK,SAAA,IAA2B;IAA3BL,EAAA,CAAAiB,UAAA,UAAA4B,MAAA,CAAAC,MAAA,CAAAC,IAAA,OAA2B;IAS5B/C,EAAA,CAAAK,SAAA,GAAgB;IAAhBL,EAAA,CAAAiB,UAAA,SAAA4B,MAAA,CAAAG,UAAA,CAAgB;IAwBfhD,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAiB,UAAA,UAAA4B,MAAA,CAAAC,MAAA,CAAAG,WAAA,OAAkC;IAYnBjD,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAAiB,UAAA,UAAA4B,MAAA,CAAAC,MAAA,CAAAI,KAAA,CAAsB;IA6BpClD,EAAA,CAAAK,SAAA,IAA4B;IAA5BL,EAAA,CAAAiB,UAAA,SAAA4B,MAAA,CAAAxB,UAAA,IAAAwB,MAAA,CAAAM,QAAA,CAA4B;IAY/BnD,EAAA,CAAAK,SAAA,GAOC;IAPDL,EAAA,CAAAiB,UAAA,aAAA4B,MAAA,CAAAO,UAAA,KAAAP,MAAA,CAAAC,MAAA,CAAAC,IAAA,KAAAF,MAAA,CAAAC,MAAA,CAAAG,WAAA,IAAAJ,MAAA,CAAAG,UAAA,IAAAH,MAAA,CAAAQ,SAAA,IAAAR,MAAA,CAAAS,gBAAA,CAOC;IAIEtD,EAAA,CAAAK,SAAA,GAAgB;IAAhBL,EAAA,CAAAiB,UAAA,SAAA4B,MAAA,CAAAO,UAAA,CAAgB;IAIhBpD,EAAA,CAAAK,SAAA,GAAiB;IAAjBL,EAAA,CAAAiB,UAAA,UAAA4B,MAAA,CAAAO,UAAA,CAAiB;IAOpBpD,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAuC,MAAA,CAAAxB,UAAA,0DACF;;;AAWlB,OAAM,MAAOkC,mBAAmB;EAmB9BC,YACUC,aAA4B,EAC5BC,aAA4B,EAC5BC,WAAwB,EACxBC,KAAqB,EACrBC,MAAc,EACdC,mBAAwC;IALxC,KAAAL,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,mBAAmB,GAAnBA,mBAAmB;IAxB7B,KAAAhB,MAAM,GAAW;MACfC,IAAI,EAAE,EAAE;MACRE,WAAW,EAAE,EAAE;MACfC,KAAK,EAAE,0BAA0B,CAAE;KACpC;;IACD,KAAA7B,UAAU,GAAG,KAAK;IAClB,KAAA0C,OAAO,GAAG,KAAK;IACf,KAAAX,UAAU,GAAG,KAAK;IAClB,KAAA5C,KAAK,GAAkB,IAAI;IAC3B,KAAA2C,QAAQ,GAAkB,IAAI;IAC9B,KAAAH,UAAU,GAAG,KAAK;IAClB,KAAAK,SAAS,GAAG,KAAK;IACjB,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAU,YAAY,GAAG,KAAK;IACpB,KAAAC,eAAe,GAAa,EAAE;IAC9B,KAAAC,gBAAgB,GAAa,EAAE,CAAC,CAAC;IACjC,KAAAC,cAAc,GAAW,EAAE,CAAC,CAAC;EAS1B;;EAEHC,QAAQA,CAAA;IACNC,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAE9C;IACA,IAAI,CAAC,IAAI,CAACxB,MAAM,EAAE;MAChB,IAAI,CAACA,MAAM,GAAG;QACZC,IAAI,EAAE,EAAE;QACRE,WAAW,EAAE,EAAE;QACfC,KAAK,EAAE,0BAA0B,CAAE;OACpC;;IAGH;IACA,IAAI,CAACqB,cAAc,EAAE;IAErB;IACA,IAAI,CAACC,cAAc,EAAE;IAErB;IACA,IAAI,CAACC,YAAY,EAAE;IAEnB,IAAI;MACF;MACA,IAAI,CAACtB,QAAQ,GAAG,IAAI,CAACS,KAAK,CAACc,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC;MACtD,IAAI,CAACvD,UAAU,GAAG,CAAC,CAAC,IAAI,CAAC8B,QAAQ;MACjCkB,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE,IAAI,CAACjD,UAAU,EAAE,KAAK,EAAE,IAAI,CAAC8B,QAAQ,CAAC;MAEhE,IAAI,IAAI,CAAC9B,UAAU,IAAI,IAAI,CAAC8B,QAAQ,EAAE;QACpC,IAAI,CAAC0B,UAAU,CAAC,IAAI,CAAC1B,QAAQ,CAAC;QAE9B;QACA2B,UAAU,CAAC,MAAK;UACdT,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAACnB,QAAQ,CAAC;UAC1DkB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAACxB,MAAM,CAAC;QACxD,CAAC,EAAE,IAAI,CAAC;;KAEX,CAAC,OAAOtC,KAAK,EAAE;MACd6D,OAAO,CAAC7D,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,IAAI,CAACA,KAAK,GAAG,yBAAyB;;IAGxC;IACAsE,UAAU,CAAC,MAAK;MACd,MAAMC,SAAS,GAAGC,QAAQ,CAACC,cAAc,CAAC,iBAAiB,CAAC;MAC5D,IAAIF,SAAS,EAAE;QACbV,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAC9CS,SAAS,CAACG,gBAAgB,CAAC,OAAO,EAAE,MAAK;UACvCb,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAChD,CAAC,CAAC;OACH,MAAM;QACLD,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;;IAEtD,CAAC,EAAE,IAAI,CAAC;EACV;EAEAE,cAAcA,CAAA;IACZ,IAAI,CAACd,aAAa,CAACyB,UAAU,EAAE,CAACC,SAAS,CAAC;MACxCC,IAAI,EAAGC,OAAO,IAAI;QAChB,IAAI,CAACpB,gBAAgB,GAAGoB,OAAO;QAC/BjB,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEgB,OAAO,CAAC;MACtD,CAAC;MACD9E,KAAK,EAAGA,KAAK,IAAI;QACf6D,OAAO,CAAC7D,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;QAC9D,IAAI,CAACA,KAAK,GACR,2EAA2E;MAC/E;KACD,CAAC;EACJ;EAEAiE,YAAYA,CAAA;IACV,MAAMc,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAIF,KAAK,EAAE;MACT,IAAI,CAAC5B,WAAW,CAAC+B,WAAW,CAACH,KAAK,CAAC,CAACH,SAAS,CAAC;QAC5CC,IAAI,EAAGM,KAAU,IAAI;UACnB,IAAI,CAACxB,cAAc,GAAGwB,KAAK;UAC3BtB,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEqB,KAAK,CAAC;QACzD,CAAC;QACDnF,KAAK,EAAGA,KAAK,IAAI;UACf6D,OAAO,CAAC7D,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;UACnE,IAAI,CAACA,KAAK,GACR,gFAAgF;QACpF;OACD,CAAC;;EAEN;EAEA+D,cAAcA,CAAA;IACZ,IAAI,CAACd,aAAa,CAACmC,UAAU,EAAE,CAACR,SAAS,CAAC;MACxCC,IAAI,EAAGQ,OAAO,IAAI;QAChB,IAAI,CAAC5B,eAAe,GAAG4B,OAAO;QAC9BxB,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEuB,OAAO,CAAC;MACtD,CAAC;MACDrF,KAAK,EAAGA,KAAK,IAAI;QACf6D,OAAO,CAAC7D,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAChE;KACD,CAAC;EACJ;EAEAqE,UAAUA,CAACiB,EAAU;IACnBzB,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEwB,EAAE,CAAC;IAC1C,IAAI,CAAC/B,OAAO,GAAG,IAAI;IACnB,IAAI,CAACvD,KAAK,GAAG,IAAI;IAEjB,IAAI,CAACiD,aAAa,CAACsC,SAAS,CAACD,EAAE,CAAC,CAACV,SAAS,CAAC;MACzCC,IAAI,EAAGW,IAAI,IAAI;QACb3B,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE0B,IAAI,CAAC;QACpC,IAAI,CAAClD,MAAM,GAAGkD,IAAI;QAElB;QACA3B,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE,IAAI,CAACxB,MAAM,CAACmD,GAAG,CAAC;QAChE5B,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAACnB,QAAQ,CAAC;QAE5C;QACA,IAAI,IAAI,CAACL,MAAM,CAACoD,OAAO,IAAI,IAAI,CAACpD,MAAM,CAACoD,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;UACzD,IAAI,CAACC,kBAAkB,EAAE;;QAG3B,IAAI,CAACrC,OAAO,GAAG,KAAK;MACtB,CAAC;MACDvD,KAAK,EAAGA,KAAK,IAAI;QACf6D,OAAO,CAAC7D,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;QAC9D,IAAI,CAACA,KAAK,GACR,8EAA8E;QAChF,IAAI,CAACuD,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEA;EACAqC,kBAAkBA,CAAA;IAChB,IAAI,CAAC,IAAI,CAACtD,MAAM,CAACoD,OAAO,IAAI,IAAI,CAACpD,MAAM,CAACoD,OAAO,CAACC,MAAM,KAAK,CAAC,EAAE;MAC5D;;IAGF9B,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;IAEhE;IACA,IAAI,CAACxB,MAAM,CAACoD,OAAO,CAACG,OAAO,CAAEC,QAAQ,IAAI;MACvC;MACA,MAAMC,IAAI,GAAG,IAAI,CAACpC,cAAc,CAACqC,IAAI,CAClCC,CAAC,IAAKA,CAAC,CAACR,GAAG,KAAKK,QAAQ,IAAIG,CAAC,CAACX,EAAE,KAAKQ,QAAQ,CAC/C;MACD,IAAIC,IAAI,EAAE;QACRlC,OAAO,CAACC,GAAG,CACT,UAAUgC,QAAQ,yCAAyC,EAC3DC,IAAI,CACL;QAED;QACA,IAAI,CAACA,IAAI,CAACG,KAAK,IAAK,CAACH,IAAI,CAACI,UAAU,IAAI,CAACJ,IAAI,CAACK,IAAK,EAAE;UACnD;UACA,MAAMrB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;UAC3C,IAAIF,KAAK,EAAE;YACT,IAAI,CAAC5B,WAAW,CAACkD,WAAW,CAACP,QAAQ,EAAEf,KAAK,CAAC,CAACH,SAAS,CAAC;cACtDC,IAAI,EAAGyB,QAAa,IAAI;gBACtBzC,OAAO,CAACC,GAAG,CACT,4CAA4CgC,QAAQ,aAAa,EACjEQ,QAAQ,CACT;gBAED;gBACA,MAAMC,KAAK,GAAG,IAAI,CAAC5C,cAAc,CAAC6C,SAAS,CACxCP,CAAC,IAAKA,CAAC,CAACR,GAAG,KAAKK,QAAQ,IAAIG,CAAC,CAACX,EAAE,KAAKQ,QAAQ,CAC/C;gBACD,IAAIS,KAAK,KAAK,CAAC,CAAC,EAAE;kBAChB,IAAI,CAAC5C,cAAc,CAAC4C,KAAK,CAAC,GAAG;oBAC3B,GAAG,IAAI,CAAC5C,cAAc,CAAC4C,KAAK,CAAC;oBAC7B,GAAGD;mBACJ;;cAEL,CAAC;cACDtG,KAAK,EAAGA,KAAK,IAAI;gBACf6D,OAAO,CAAC7D,KAAK,CACX,+EAA+E8F,QAAQ,GAAG,EAC1F9F,KAAK,CACN;cACH;aACD,CAAC;;;OAGP,MAAM;QACL;QACA,MAAM+E,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC3C,IAAIF,KAAK,EAAE;UACT,IAAI,CAAC5B,WAAW,CAACkD,WAAW,CAACP,QAAQ,EAAEf,KAAK,CAAC,CAACH,SAAS,CAAC;YACtDC,IAAI,EAAGyB,QAAa,IAAI;cACtBzC,OAAO,CAACC,GAAG,CACT,4BAA4BgC,QAAQ,aAAa,EACjDQ,QAAQ,CACT;cACD;cACA,IACE,CAAC,IAAI,CAAC3C,cAAc,CAAC8C,IAAI,CACtBR,CAAC,IAAKA,CAAC,CAACR,GAAG,KAAKa,QAAQ,CAACb,GAAG,IAAIQ,CAAC,CAACX,EAAE,KAAKgB,QAAQ,CAAChB,EAAE,CACtD,EACD;gBACA,IAAI,CAAC3B,cAAc,CAAC+C,IAAI,CAACJ,QAAQ,CAAC;;YAEtC,CAAC;YACDtG,KAAK,EAAGA,KAAK,IAAI;cACf6D,OAAO,CAAC7D,KAAK,CACX,+DAA+D8F,QAAQ,GAAG,EAC1E9F,KAAK,CACN;YACH;WACD,CAAC;;;IAGR,CAAC,CAAC;EACJ;EAEA2G,eAAeA,CAACpE,IAAY;IAC1B;IACA,IAAI,IAAI,CAAC1B,UAAU,IAAI,IAAI,CAAC8B,QAAQ,EAAE;MACpC,OAAO,IAAI,CAACc,eAAe,CAACgD,IAAI,CAC7BG,CAAC,IAAKA,CAAC,CAACrE,IAAI,KAAKA,IAAI,IAAIqE,CAAC,CAACnB,GAAG,KAAK,IAAI,CAAC9C,QAAQ,CAClD;;IAEH;IACA,OAAO,IAAI,CAACc,eAAe,CAACgD,IAAI,CAAEG,CAAC,IAAKA,CAAC,CAACrE,IAAI,KAAKA,IAAI,CAAC;EAC1D;EAEAjB,UAAUA,CAACC,KAAa;IACtBsC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEvC,KAAK,CAAC;IACnC,IAAI,CAACe,MAAM,CAACC,IAAI,GAAGhB,KAAK;IAExB;IACA,IAAI,CAACiB,UAAU,GAAG,IAAI,CAACmE,eAAe,CAACpF,KAAK,CAAC;IAC7C,IAAI,IAAI,CAACiB,UAAU,EAAE;MACnBqB,OAAO,CAACgD,IAAI,CAAC,6BAA6B,CAAC;;IAG7C;IACA,IAAI,CAAChE,SAAS,GAAGtB,KAAK,CAACoE,MAAM,GAAG,CAAC,IAAIpE,KAAK,CAACoE,MAAM,GAAG,CAAC;IACrD,IAAI,IAAI,CAAC9C,SAAS,EAAE;MAClBgB,OAAO,CAACgD,IAAI,CAAC,4CAA4C,CAAC;;EAE9D;EAEAhF,iBAAiBA,CAACN,KAAa;IAC7BsC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEvC,KAAK,CAAC;IAC1C,IAAI,CAACe,MAAM,CAACG,WAAW,GAAGlB,KAAK;IAE/B;IACA,IAAI,CAACuB,gBAAgB,GAAGvB,KAAK,CAACoE,MAAM,GAAG,CAAC,IAAIpE,KAAK,CAACoE,MAAM,GAAG,EAAE;IAC7D,IAAI,IAAI,CAAC7C,gBAAgB,EAAE;MACzBe,OAAO,CAACgD,IAAI,CAAC,qDAAqD,CAAC;;EAEvE;EAEA5F,QAAQA,CAAA;IACN4C,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAACxB,MAAM,CAAC;IAEhD;IACA,IAAI,CAAC,IAAI,CAACA,MAAM,CAACC,IAAI,EAAE;MACrB,IAAI,CAACvC,KAAK,GAAG,gCAAgC;MAC7C;;IAGF,IAAI,IAAI,CAACsC,MAAM,CAACC,IAAI,CAACoD,MAAM,GAAG,CAAC,EAAE;MAC/B,IAAI,CAAC9C,SAAS,GAAG,IAAI;MACrB,IAAI,CAAC7C,KAAK,GAAG,yDAAyD;MACtE;;IAGF;IACA,IAAI,CAAC,IAAI,CAACsC,MAAM,CAACG,WAAW,EAAE;MAC5B,IAAI,CAACzC,KAAK,GAAG,yCAAyC;MACtD;;IAGF,IAAI,IAAI,CAACsC,MAAM,CAACG,WAAW,CAACkD,MAAM,GAAG,EAAE,EAAE;MACvC,IAAI,CAAC7C,gBAAgB,GAAG,IAAI;MAC5B,IAAI,CAAC9C,KAAK,GACR,kEAAkE;MACpE;;IAGF;IACA,IAAI,IAAI,CAAC2G,eAAe,CAAC,IAAI,CAACrE,MAAM,CAACC,IAAI,CAAC,EAAE;MAC1C,IAAI,CAACC,UAAU,GAAG,IAAI;MACtB,IAAI,CAACxC,KAAK,GACR,oEAAoE;MACtE;;IAGF,IAAI,CAAC4C,UAAU,GAAG,IAAI;IACtB,IAAI,CAAC5C,KAAK,GAAG,IAAI;IAEjB;IACA,MAAM8G,YAAY,GAAW;MAC3BvE,IAAI,EAAE,IAAI,CAACD,MAAM,CAACC,IAAI;MACtBE,WAAW,EAAE,IAAI,CAACH,MAAM,CAACG,WAAW,IAAI,EAAE;MAC1CC,KAAK,EAAE,IAAI,CAACJ,MAAM,CAACI;KACpB;IAED;IACA,IAAI,IAAI,CAAC7B,UAAU,IAAI,IAAI,CAAC8B,QAAQ,EAAE;MACpCmE,YAAY,CAACrB,GAAG,GAAG,IAAI,CAAC9C,QAAQ;;IAGlCkB,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEgD,YAAY,CAAC;IAE/C,IAAI,IAAI,CAACjG,UAAU,IAAI,IAAI,CAAC8B,QAAQ,EAAE;MACpC;MACA,IAAI,CAACM,aAAa,CAAC8D,YAAY,CAAC,IAAI,CAACpE,QAAQ,EAAEmE,YAAY,CAAC,CAAClC,SAAS,CAAC;QACrEC,IAAI,EAAGmC,QAAQ,IAAI;UACjBnD,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEkD,QAAQ,CAAC;UACxD,IAAI,CAACpE,UAAU,GAAG,KAAK;UACvB,IAAI,CAACU,mBAAmB,CAAC2D,WAAW,CAClC,aAAaD,QAAQ,CAACzE,IAAI,kCAAkC,CAC7D;UACD,IAAI,CAACc,MAAM,CAAC6D,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;QAC1C,CAAC;QACDlH,KAAK,EAAGA,KAAK,IAAI;UACf6D,OAAO,CAAC7D,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;UAClE,IAAI,CAACA,KAAK,GAAG,yCAAyCA,KAAK,CAACmH,OAAO,EAAE;UACrE,IAAI,CAACvE,UAAU,GAAG,KAAK;UACvB,IAAI,CAACU,mBAAmB,CAAC8D,SAAS,CAAC,WAAWpH,KAAK,CAACmH,OAAO,EAAE,CAAC;QAChE;OACD,CAAC;KACH,MAAM;MACL;MACA,IAAI,CAAClE,aAAa,CAACoE,SAAS,CAACP,YAAY,CAAC,CAAClC,SAAS,CAAC;QACnDC,IAAI,EAAGmC,QAAQ,IAAI;UACjBnD,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEkD,QAAQ,CAAC;UACpD,IAAI,CAACpE,UAAU,GAAG,KAAK;UACvB,IAAI,CAACU,mBAAmB,CAAC2D,WAAW,CAClC,aAAaD,QAAQ,CAACzE,IAAI,4BAA4B,CACvD;UACD,IAAI,CAACc,MAAM,CAAC6D,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;QAC1C,CAAC;QACDlH,KAAK,EAAGA,KAAK,IAAI;UACf6D,OAAO,CAAC7D,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;UAC3D,IAAI,CAACA,KAAK,GAAG,kCAAkCA,KAAK,CAACmH,OAAO,EAAE;UAC9D,IAAI,CAACvE,UAAU,GAAG,KAAK;UACvB,IAAI,CAACU,mBAAmB,CAAC8D,SAAS,CAAC,WAAWpH,KAAK,CAACmH,OAAO,EAAE,CAAC;QAChE;OACD,CAAC;;EAEN;EAEAnF,MAAMA,CAAA;IACJ6B,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAC7B,IAAI,CAACT,MAAM,CAAC6D,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;EAC1C;EAEA1G,YAAYA,CAAA;IACV,IAAI,CAAC,IAAI,CAACmC,QAAQ,EAAE;MAClB,IAAI,CAAC3C,KAAK,GAAG,+CAA+C;MAC5D;;IAGF,IACEsH,OAAO,CACL,gDAAgD,IAAI,CAAChF,MAAM,CAACC,IAAI,oCAAoC,CACrG,EACD;MACA,IAAI,CAACgB,OAAO,GAAG,IAAI;MACnB,IAAI,CAACvD,KAAK,GAAG,IAAI;MAEjB,IAAI,CAACiD,aAAa,CAACzC,YAAY,CAAC,IAAI,CAACmC,QAAQ,CAAC,CAACiC,SAAS,CAAC;QACvDC,IAAI,EAAEA,CAAA,KAAK;UACThB,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;UAC3C,IAAI,CAACP,OAAO,GAAG,KAAK;UACpB,IAAI,CAACD,mBAAmB,CAAC2D,WAAW,CAClC,aAAa,IAAI,CAAC3E,MAAM,CAACC,IAAI,gCAAgC,CAC9D;UACD,IAAI,CAACc,MAAM,CAAC6D,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;QAC1C,CAAC;QACDlH,KAAK,EAAGA,KAAK,IAAI;UACf6D,OAAO,CAAC7D,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;UAClE,IAAI,CAACA,KAAK,GAAG,qCAAqCA,KAAK,CAACmH,OAAO,EAAE;UACjE,IAAI,CAAC5D,OAAO,GAAG,KAAK;UACpB,IAAI,CAACD,mBAAmB,CAAC8D,SAAS,CAAC,WAAWpH,KAAK,CAACmH,OAAO,EAAE,CAAC;QAChE;OACD,CAAC;;EAEN;EAEA;EACAI,iBAAiBA,CAACzB,QAAgB,EAAEM,IAAA,GAAe,QAAQ;IACzDvC,OAAO,CAACC,GAAG,CACT,2CAA2C,EAC3CgC,QAAQ,EACR,UAAU,EACVM,IAAI,CACL;IACDvC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAACnB,QAAQ,CAAC;IAC1DkB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAACxB,MAAM,CAAC;IAEtD;IACA,MAAMK,QAAQ,GAAG,IAAI,CAACA,QAAQ,IAAK,IAAI,CAACL,MAAM,IAAI,IAAI,CAACA,MAAM,CAACmD,GAAI;IAElE5B,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEnB,QAAQ,CAAC;IAE1C,IAAI,CAACA,QAAQ,IAAI,CAACmD,QAAQ,EAAE;MAC1BjC,OAAO,CAAC7D,KAAK,CAAC,sCAAsC,CAAC;MACrD,IAAI,CAACA,KAAK,GAAG,sCAAsC;MACnD6D,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEnB,QAAQ,EAAE,WAAW,EAAEmD,QAAQ,CAAC;MAEzD;MACA,IAAI,CAACxC,mBAAmB,CAAC8D,SAAS,CAChC,sEAAsE,CACvE;MACD;;IAGF;IACA,IAAI,IAAI,CAAC9E,MAAM,CAACoD,OAAO,IAAI,IAAI,CAACpD,MAAM,CAACoD,OAAO,CAAC8B,QAAQ,CAAC1B,QAAQ,CAAC,EAAE;MACjE,IAAI,CAACxC,mBAAmB,CAAC8D,SAAS,CAChC,wCAAwC,CACzC;MACD;;IAGF;IACA,MAAMrB,IAAI,GAAG,IAAI,CAACpC,cAAc,CAACqC,IAAI,CAClCC,CAAC,IAAKA,CAAC,CAACR,GAAG,KAAKK,QAAQ,IAAIG,CAAC,CAACX,EAAE,KAAKQ,QAAQ,CAC/C;IACD,MAAM2B,QAAQ,GAAG1B,IAAI,GACjBA,IAAI,CAAC2B,SAAS,IAAI3B,IAAI,CAAC4B,QAAQ,GAC7B,GAAG5B,IAAI,CAAC2B,SAAS,IAAI3B,IAAI,CAAC4B,QAAQ,EAAE,GACpC5B,IAAI,CAACxD,IAAI,IAAIuD,QAAQ,GACvBA,QAAQ;IAEZ;IACA,MAAM8B,MAAM,GAAW;MACrBtC,EAAE,EAAEQ,QAAQ;MACZM,IAAI,EAAEA;KACP;IAED,IAAI,CAAC7C,OAAO,GAAG,IAAI;IAEnBM,OAAO,CAACC,GAAG,CACT,2BAA2B2D,QAAQ,WAAWrB,IAAI,eAAezD,QAAQ,EAAE,CAC5E;IAED,IAAI,CAACM,aAAa,CAACsE,iBAAiB,CAAC5E,QAAQ,EAAEiF,MAAM,CAAC,CAAChD,SAAS,CAAC;MAC/DC,IAAI,EAAGmC,QAAQ,IAAI;QACjBnD,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEkD,QAAQ,CAAC;QACnD,IAAI,CAAC1D,mBAAmB,CAAC2D,WAAW,CAClC,GAAGQ,QAAQ,uBACTrB,IAAI,KAAK,OAAO,GAAG,gBAAgB,GAAG,QACxC,aAAa,CACd;QACD;QACA,IAAI,CAAC/B,UAAU,CAAC1B,QAAQ,CAAC;QACzB,IAAI,CAACY,OAAO,GAAG,KAAK;MACtB,CAAC;MACDvD,KAAK,EAAGA,KAAK,IAAI;QACf6D,OAAO,CAAC7D,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QACzD,IAAI,CAACA,KAAK,GACR,+DAA+D;QACjE,IAAI,CAACsD,mBAAmB,CAAC8D,SAAS,CAChC,oCAAoC,GAAGpH,KAAK,CAACmH,OAAO,CACrD;QACD,IAAI,CAAC5D,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEA;EACAsE,aAAaA,CAAC/B,QAAgB;IAC5B;IACA,MAAMC,IAAI,GAAG,IAAI,CAACpC,cAAc,CAACqC,IAAI,CAClCC,CAAC,IAAKA,CAAC,CAACR,GAAG,KAAKK,QAAQ,IAAIG,CAAC,CAACX,EAAE,KAAKQ,QAAQ,CAC/C;IACD,IAAIC,IAAI,EAAE;MACR,IAAIA,IAAI,CAAC2B,SAAS,IAAI3B,IAAI,CAAC4B,QAAQ,EAAE;QACnC,OAAO,GAAG5B,IAAI,CAAC2B,SAAS,IAAI3B,IAAI,CAAC4B,QAAQ,EAAE;OAC5C,MAAM,IAAI5B,IAAI,CAACxD,IAAI,EAAE;QACpB,OAAOwD,IAAI,CAACxD,IAAI;;;IAIpB;IACA,MAAMqF,MAAM,GAAG,IAAI,CAAClE,gBAAgB,CAACsC,IAAI,CACtC8B,CAAC,IAAKA,CAAC,CAACrC,GAAG,KAAKK,QAAQ,IAAIgC,CAAC,CAACxC,EAAE,KAAKQ,QAAQ,CAC/C;IACD,IAAI8B,MAAM,IAAIA,MAAM,CAACrF,IAAI,EAAE;MACzB,OAAOqF,MAAM,CAACrF,IAAI;;IAGpB;IACA,OAAOuD,QAAQ;EACjB;EAEA;EACAiC,cAAcA,CAACjC,QAAgB;IAC7B,MAAMC,IAAI,GAAG,IAAI,CAACpC,cAAc,CAACqC,IAAI,CAClCC,CAAC,IAAKA,CAAC,CAACR,GAAG,KAAKK,QAAQ,IAAIG,CAAC,CAACX,EAAE,KAAKQ,QAAQ,CAC/C;IACD,IAAIC,IAAI,IAAIA,IAAI,CAACG,KAAK,EAAE;MACtB,OAAOH,IAAI,CAACG,KAAK;;IAEnB,OAAO,eAAe;EACxB;EAEA;EACA8B,mBAAmBA,CAAClC,QAAgB;IAClC,MAAMC,IAAI,GAAG,IAAI,CAACpC,cAAc,CAACqC,IAAI,CAClCC,CAAC,IAAKA,CAAC,CAACR,GAAG,KAAKK,QAAQ,IAAIG,CAAC,CAACX,EAAE,KAAKQ,QAAQ,CAC/C;IACD,IAAIC,IAAI,EAAE;MACR,IAAIA,IAAI,CAACI,UAAU,EAAE;QACnB,OAAOJ,IAAI,CAACI,UAAU,KAAK,UAAU,GAAG,UAAU,GAAG,YAAY;OAClE,MAAM,IAAIJ,IAAI,CAACK,IAAI,EAAE;QACpB,OAAOL,IAAI,CAACK,IAAI,KAAK,UAAU,GAAG,UAAU,GAAG,YAAY;;;IAG/D,OAAO,cAAc;EACvB;EAEA;EACA6B,aAAaA,CAACC,SAAiB;IAC7B;IACA;IACA,OAAO,QAAQ;EACjB;EAEAC,sBAAsBA,CAACrC,QAAgB;IACrCjC,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAEgC,QAAQ,CAAC;IACxEjC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAACnB,QAAQ,CAAC;IAC1DkB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAACxB,MAAM,CAAC;IAEtD;IACA,MAAMK,QAAQ,GAAG,IAAI,CAACA,QAAQ,IAAK,IAAI,CAACL,MAAM,IAAI,IAAI,CAACA,MAAM,CAACmD,GAAI;IAElE,IAAI,CAAC9C,QAAQ,EAAE;MACbkB,OAAO,CAAC7D,KAAK,CAAC,sBAAsB,CAAC;MACrC,IAAI,CAACA,KAAK,GAAG,wDAAwD;MACrE,IAAI,CAACsD,mBAAmB,CAAC8D,SAAS,CAChC,wDAAwD,CACzD;MACD;;IAGF,IAAI,CAACtB,QAAQ,EAAE;MACbjC,OAAO,CAAC7D,KAAK,CAAC,uBAAuB,CAAC;MACtC,IAAI,CAACA,KAAK,GAAG,yDAAyD;MACtE,IAAI,CAACsD,mBAAmB,CAAC8D,SAAS,CAChC,yDAAyD,CAC1D;MACD;;IAGF;IACA,MAAMgB,UAAU,GAAG,IAAI,CAACP,aAAa,CAAC/B,QAAQ,CAAC;IAE/CjC,OAAO,CAACC,GAAG,CACT,yCAAyCgC,QAAQ,KAAKsC,UAAU,iBAAiBzF,QAAQ,EAAE,CAC5F;IAED,IAAI;MACF,IACE2E,OAAO,CAAC,oCAAoCc,UAAU,eAAe,CAAC,EACtE;QACAvE,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;QAE7D,IAAI,CAACP,OAAO,GAAG,IAAI;QACnB,IAAI,CAACvD,KAAK,GAAG,IAAI;QAEjB;QACAsE,UAAU,CAAC,MAAK;UACd,IAAI,CAACrB,aAAa,CACfkF,sBAAsB,CAACxF,QAAQ,EAAEmD,QAAQ,CAAC,CAC1ClB,SAAS,CAAC;YACTC,IAAI,EAAGmC,QAAQ,IAAI;cACjBnD,OAAO,CAACC,GAAG,CACT,gBAAgBsE,UAAU,mCAAmC,EAC7DpB,QAAQ,CACT;cACD,IAAI,CAACzD,OAAO,GAAG,KAAK;cACpB,IAAI,CAACD,mBAAmB,CAAC2D,WAAW,CAClC,GAAGmB,UAAU,uCAAuC,CACrD;cAED;cACA,IAAI,CAAC/D,UAAU,CAAC1B,QAAQ,CAAC;YAC3B,CAAC;YACD3C,KAAK,EAAGA,KAAK,IAAI;cACf6D,OAAO,CAAC7D,KAAK,CACX,4CAA4CoI,UAAU,IAAI,EAC1DpI,KAAK,CACN;cACD6D,OAAO,CAAC7D,KAAK,CAAC,sBAAsB,EAAE;gBACpCqI,MAAM,EAAErI,KAAK,CAACqI,MAAM;gBACpBlB,OAAO,EAAEnH,KAAK,CAACmH,OAAO;gBACtBnH,KAAK,EAAEA;eACR,CAAC;cAEF,IAAI,CAACuD,OAAO,GAAG,KAAK;cACpB,IAAI,CAACvD,KAAK,GAAG,wCAAwCoI,UAAU,kBAC7DpI,KAAK,CAACmH,OAAO,IAAI,iBACnB,EAAE;cACF,IAAI,CAAC7D,mBAAmB,CAAC8D,SAAS,CAChC,qCAAqC,IAAI,CAACpH,KAAK,EAAE,CAClD;YACH;WACD,CAAC;QACN,CAAC,EAAE,GAAG,CAAC;OACR,MAAM;QACL6D,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;;KAEvD,CAAC,OAAO9D,KAAU,EAAE;MACnB6D,OAAO,CAAC7D,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D,IAAI,CAACA,KAAK,GAAG,cAAcA,KAAK,EAAEmH,OAAO,IAAI,iBAAiB,EAAE;MAChE,IAAI,CAAC7D,mBAAmB,CAAC8D,SAAS,CAAC,cAAc,IAAI,CAACpH,KAAK,EAAE,CAAC;;EAElE;EAEA;EACAQ,YAAYA,CAAA;IACVqD,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;IACzED,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAACnB,QAAQ,CAAC;IAC1DkB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAACxB,MAAM,CAAC;IAEtD;IACA,MAAMK,QAAQ,GAAG,IAAI,CAACA,QAAQ,IAAK,IAAI,CAACL,MAAM,IAAI,IAAI,CAACA,MAAM,CAACmD,GAAI;IAElE,IAAI,CAAC9C,QAAQ,EAAE;MACbkB,OAAO,CAAC7D,KAAK,CAAC,sBAAsB,CAAC;MACrC,IAAI,CAACA,KAAK,GAAG,yDAAyD;MACtE,IAAI,CAACsD,mBAAmB,CAAC8D,SAAS,CAChC,yDAAyD,CAC1D;MACD;;IAGFvD,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEnB,QAAQ,CAAC;IAE5D,IAAI;MACF,IACE2E,OAAO,CACL,gDAAgD,IAAI,CAAChF,MAAM,CAACC,IAAI,mCAAmC,CACpG,EACD;QACAsB,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;QAE7D,IAAI,CAACP,OAAO,GAAG,IAAI;QACnB,IAAI,CAACvD,KAAK,GAAG,IAAI;QAEjB;QACAsE,UAAU,CAAC,MAAK;UACd,IAAI,CAACrB,aAAa,CAACzC,YAAY,CAACmC,QAAQ,CAAC,CAACiC,SAAS,CAAC;YAClDC,IAAI,EAAGmC,QAAQ,IAAI;cACjBnD,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEkD,QAAQ,CAAC;cAC/D,IAAI,CAACzD,OAAO,GAAG,KAAK;cACpB,IAAI,CAACD,mBAAmB,CAAC2D,WAAW,CAClC,aAAa,IAAI,CAAC3E,MAAM,CAACC,IAAI,gCAAgC,CAC9D;cAED;cACA+B,UAAU,CAAC,MAAK;gBACd,IAAI,CAACjB,MAAM,CAAC6D,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;cAC1C,CAAC,EAAE,GAAG,CAAC;YACT,CAAC;YACDlH,KAAK,EAAGA,KAAK,IAAI;cACf6D,OAAO,CAAC7D,KAAK,CACX,4CAA4C,EAC5CA,KAAK,CACN;cACD6D,OAAO,CAAC7D,KAAK,CAAC,sBAAsB,EAAE;gBACpCqI,MAAM,EAAErI,KAAK,CAACqI,MAAM;gBACpBlB,OAAO,EAAEnH,KAAK,CAACmH,OAAO;gBACtBnH,KAAK,EAAEA;eACR,CAAC;cAEF,IAAI,CAACuD,OAAO,GAAG,KAAK;cACpB,IAAI,CAACvD,KAAK,GAAG,qCACXA,KAAK,CAACmH,OAAO,IAAI,iBACnB,EAAE;cACF,IAAI,CAAC7D,mBAAmB,CAAC8D,SAAS,CAChC,kCAAkC,IAAI,CAACpH,KAAK,EAAE,CAC/C;YACH;WACD,CAAC;QACJ,CAAC,EAAE,GAAG,CAAC;OACR,MAAM;QACL6D,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;;KAEvD,CAAC,OAAO9D,KAAU,EAAE;MACnB6D,OAAO,CAAC7D,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,IAAI,CAACA,KAAK,GAAG,cAAcA,KAAK,EAAEmH,OAAO,IAAI,iBAAiB,EAAE;MAChE,IAAI,CAAC7D,mBAAmB,CAAC8D,SAAS,CAAC,cAAc,IAAI,CAACpH,KAAK,EAAE,CAAC;;EAElE;;;uBA3sBW+C,mBAAmB,EAAAvD,EAAA,CAAA8I,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAAhJ,EAAA,CAAA8I,iBAAA,CAAAG,EAAA,CAAAC,aAAA,GAAAlJ,EAAA,CAAA8I,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAApJ,EAAA,CAAA8I,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAAtJ,EAAA,CAAA8I,iBAAA,CAAAO,EAAA,CAAAE,MAAA,GAAAvJ,EAAA,CAAA8I,iBAAA,CAAAU,EAAA,CAAAC,mBAAA;IAAA;EAAA;;;YAAnBlG,mBAAmB;MAAAmG,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UApS5BhK,EAAA,CAAAC,cAAA,aAEC;UAGGD,EAAA,CAAAE,SAAA,aAEO;UAMPF,EAAA,CAAAC,cAAA,aAA4D;UAExDD,EAAA,CAAAE,SAAA,aAAmE;UAWrEF,EAAA,CAAAG,YAAA,EAAM;UAIVH,EAAA,CAAAC,cAAA,cAAiD;UAG7CD,EAAA,CAAAE,SAAA,cAEO;UAKPF,EAAA,CAAAC,cAAA,eAEC;UAQOD,EAAA,CAAAI,MAAA,IACF;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,aAAsD;UACpDD,EAAA,CAAAI,MAAA,IAKF;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAGNH,EAAA,CAAAC,cAAA,kBAGC;UAFCD,EAAA,CAAAS,UAAA,mBAAAyJ,sDAAA;YAAA,OAASD,GAAA,CAAAzH,MAAA,EAAQ;UAAA,EAAC;UAGlBxC,EAAA,CAAAE,SAAA,aAAsC;UACtCF,EAAA,CAAAI,MAAA,gCACF;UAAAJ,EAAA,CAAAG,YAAA,EAAS;UAMfH,EAAA,CAAAgC,UAAA,KAAAmI,mCAAA,kBAiBM;UAGNnK,EAAA,CAAAgC,UAAA,KAAAoI,mCAAA,mBAoBM;UAGNpK,EAAA,CAAAgC,UAAA,KAAAqI,mCAAA,oBA0KM;UACRrK,EAAA,CAAAG,YAAA,EAAM;;;UA7OMH,EAAA,CAAAK,SAAA,IACF;UADEL,EAAA,CAAAM,kBAAA,MAAA2J,GAAA,CAAA5I,UAAA,0DACF;UAEErB,EAAA,CAAAK,SAAA,GAKF;UALEL,EAAA,CAAAM,kBAAA,MAAA2J,GAAA,CAAA5I,UAAA,sJAKF;UAgBLrB,EAAA,CAAAK,SAAA,GAAa;UAAbL,EAAA,CAAAiB,UAAA,SAAAgJ,GAAA,CAAAlG,OAAA,CAAa;UAmBV/D,EAAA,CAAAK,SAAA,GAAW;UAAXL,EAAA,CAAAiB,UAAA,SAAAgJ,GAAA,CAAAzJ,KAAA,CAAW;UAuBXR,EAAA,CAAAK,SAAA,GAAc;UAAdL,EAAA,CAAAiB,UAAA,UAAAgJ,GAAA,CAAAlG,OAAA,CAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}