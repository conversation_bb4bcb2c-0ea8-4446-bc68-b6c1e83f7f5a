{"ast": null, "code": "import { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@app/services/projects.service\";\nimport * as i2 from \"src/app/services/authuser.service\";\nimport * as i3 from \"src/app/services/rendus.service\";\nimport * as i4 from \"src/app/services/file.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/router\";\nfunction ProjectListComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 19);\n    i0.ɵɵelement(2, \"div\", 20)(3, \"div\", 21);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProjectListComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 23);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 24);\n    i0.ɵɵelement(3, \"path\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelement(4, \"div\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h3\", 26);\n    i0.ɵɵtext(6, \" Aucun projet disponible \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 27);\n    i0.ɵɵtext(8, \" Vos missions appara\\u00EEtront ici \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProjectListComponent_div_30_div_1_div_18_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53)(1, \"div\", 54)(2, \"div\", 55);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 56);\n    i0.ɵɵelement(4, \"path\", 57);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelement(5, \"div\", 58);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 59);\n    i0.ɵɵtext(7, \"Document\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"a\", 60);\n    i0.ɵɵelement(9, \"div\", 61)(10, \"div\", 62);\n    i0.ɵɵelementStart(11, \"span\", 63);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(12, \"svg\", 64);\n    i0.ɵɵelement(13, \"path\", 65);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(14, \" T\\u00E9l\\u00E9charger \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const file_r9 = ctx.$implicit;\n    const ctx_r8 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"href\", ctx_r8.getFileUrl(file_r9), i0.ɵɵsanitizeUrl);\n    i0.ɵɵattribute(\"download\", ctx_r8.getFileName(file_r9));\n  }\n}\nfunction ProjectListComponent_div_30_div_1_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"h4\", 50);\n    i0.ɵɵtext(2, \" Fichiers \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 51);\n    i0.ɵɵtemplate(4, ProjectListComponent_div_30_div_1_div_18_div_4_Template, 15, 2, \"div\", 52);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const projet_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", projet_r4.fichiers);\n  }\n}\nfunction ProjectListComponent_div_30_div_1_ng_container_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 66)(2, \"div\", 44);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 67);\n    i0.ɵɵelement(4, \"path\", 68);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelement(5, \"div\", 69);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\");\n    i0.ɵɵtext(7, \"Rendu\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nconst _c0 = function (a1) {\n  return [\"/front/projects/submit\", a1];\n};\nfunction ProjectListComponent_div_30_div_1_ng_container_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"a\", 70);\n    i0.ɵɵelement(2, \"div\", 71)(3, \"div\", 72);\n    i0.ɵɵelementStart(4, \"span\", 73);\n    i0.ɵɵtext(5, \" Rendre \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const projet_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(1, _c0, projet_r4._id));\n  }\n}\nconst _c1 = function (a1) {\n  return [\"/front/projects/detail\", a1];\n};\nfunction ProjectListComponent_div_30_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"div\", 31);\n    i0.ɵɵelement(2, \"div\", 32)(3, \"div\", 33);\n    i0.ɵɵelementStart(4, \"div\", 34)(5, \"h3\", 35);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 36)(8, \"span\", 37);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\", 38);\n    i0.ɵɵtext(11, \"\\u2022\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 37);\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"date\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(15, \"div\", 39)(16, \"p\", 40);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(18, ProjectListComponent_div_30_div_1_div_18_Template, 5, 1, \"div\", 41);\n    i0.ɵɵelementStart(19, \"div\", 42)(20, \"a\", 43)(21, \"div\", 44);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(22, \"svg\", 45);\n    i0.ɵɵelement(23, \"path\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelement(24, \"div\", 47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"span\");\n    i0.ɵɵtext(26, \"D\\u00E9tails\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(27, ProjectListComponent_div_30_div_1_ng_container_27_Template, 8, 0, \"ng-container\", 48);\n    i0.ɵɵtemplate(28, ProjectListComponent_div_30_div_1_ng_container_28_Template, 6, 3, \"ng-container\", 48);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const projet_r4 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", projet_r4.titre, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(projet_r4.groupe || \"Tous\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(14, 8, projet_r4.dateLimite, \"dd/MM/yyyy\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", projet_r4.description || \"Aucune description\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", projet_r4.fichiers && projet_r4.fichiers.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(11, _c1, projet_r4._id));\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isRendu(projet_r4._id));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.isRendu(projet_r4._id));\n  }\n}\nfunction ProjectListComponent_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵtemplate(1, ProjectListComponent_div_30_div_1_Template, 29, 13, \"div\", 29);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.projets);\n  }\n}\n// Composant pour afficher la liste des projets\nexport class ProjectListComponent {\n  constructor(projetService, authService, rendusService, fileService) {\n    this.projetService = projetService;\n    this.authService = authService;\n    this.rendusService = rendusService;\n    this.fileService = fileService;\n    this.projets = [];\n    this.rendusMap = new Map();\n    this.isLoading = true;\n    this.userGroup = '';\n  }\n  ngOnInit() {\n    // On garde cette ligne pour une utilisation future\n    this.userGroup = this.authService.getCurrentUser()?.groupe || '';\n    this.loadProjets();\n  }\n  loadProjets() {\n    this.isLoading = true;\n    this.projetService.getProjets().subscribe({\n      next: projets => {\n        // Afficher tous les projets sans filtrage\n        this.projets = projets;\n        this.isLoading = false;\n        // Vérifier quels projets ont déjà été rendus par l'étudiant\n        this.projets.forEach(projet => {\n          if (projet._id) {\n            this.checkRenduStatus(projet._id);\n          }\n        });\n      },\n      error: error => {\n        console.error('Erreur lors du chargement des projets', error);\n        this.isLoading = false;\n      }\n    });\n  }\n  checkRenduStatus(projetId) {\n    const etudiantId = this.authService.getCurrentUserId();\n    if (!etudiantId) return;\n    this.rendusService.checkRenduExists(projetId, etudiantId).subscribe({\n      next: exists => {\n        this.rendusMap.set(projetId, exists);\n      },\n      error: error => {\n        console.error(`Erreur lors de la vérification du rendu pour le projet ${projetId}`, error);\n      }\n    });\n  }\n  getFileUrl(filePath) {\n    if (!filePath) return '';\n    // Extraire uniquement le nom du fichier\n    let fileName = filePath;\n    // Si le chemin contient des slashes ou backslashes, prendre la dernière partie\n    if (filePath.includes('/') || filePath.includes('\\\\')) {\n      const parts = filePath.split(/[\\/\\\\]/);\n      fileName = parts[parts.length - 1];\n    }\n    // Utiliser la route qui pointe vers le bon emplacement\n    return `${environment.urlBackend}projets/telecharger/${fileName}`;\n  }\n  getFileName(filePath) {\n    if (!filePath) return 'fichier';\n    // Extraire uniquement le nom du fichier\n    if (filePath.includes('/') || filePath.includes('\\\\')) {\n      const parts = filePath.split(/[\\/\\\\]/);\n      return parts[parts.length - 1];\n    }\n    return filePath;\n  }\n  // Méthode pour vérifier si un projet a été rendu\n  isRendu(projetId) {\n    return projetId ? this.rendusMap.get(projetId) === true : false;\n  }\n  static {\n    this.ɵfac = function ProjectListComponent_Factory(t) {\n      return new (t || ProjectListComponent)(i0.ɵɵdirectiveInject(i1.ProjetService), i0.ɵɵdirectiveInject(i2.AuthuserService), i0.ɵɵdirectiveInject(i3.RendusService), i0.ɵɵdirectiveInject(i4.FileService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProjectListComponent,\n      selectors: [[\"app-project-list\"]],\n      decls: 31,\n      vars: 3,\n      consts: [[1, \"min-h-screen\", \"bg-[#edf1f4]\", \"dark:bg-[#121212]\", \"p-4\", \"md:p-6\", \"relative\"], [1, \"absolute\", \"inset-0\", \"overflow-hidden\", \"pointer-events-none\"], [1, \"absolute\", \"top-[15%]\", \"left-[10%]\", \"w-64\", \"h-64\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"bottom-[20%]\", \"right-[10%]\", \"w-80\", \"h-80\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"inset-0\", \"opacity-5\", \"dark:opacity-[0.03]\"], [1, \"h-full\", \"grid\", \"grid-cols-12\"], [1, \"border-r\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]\"], [1, \"max-w-6xl\", \"mx-auto\", \"relative\", \"z-10\"], [1, \"flex\", \"justify-between\", \"items-center\", \"mb-8\"], [1, \"text-3xl\", \"font-bold\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"text-sm\", \"md:text-base\", \"mt-1\"], [1, \"h-12\", \"w-12\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#3d4a85]\", \"dark:to-[#6d78c9]\", \"flex\", \"items-center\", \"justify-center\", \"text-white\", \"shadow-lg\", \"relative\", \"group\", \"overflow-hidden\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#3d4a85]\", \"dark:to-[#6d78c9]\", \"rounded-full\", \"opacity-0\", \"group-hover:opacity-100\", \"blur-xl\", \"transition-opacity\", \"duration-300\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-6\", \"w-6\", \"relative\", \"z-10\", \"group-hover:scale-110\", \"transition-transform\", \"duration-300\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"], [\"class\", \"flex justify-center my-12\", 4, \"ngIf\"], [\"class\", \"bg-white dark:bg-[#1e1e1e] rounded-xl shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)] p-8 text-center backdrop-blur-sm border border-[#edf1f4]/50 dark:border-[#2a2a2a]\", 4, \"ngIf\"], [\"class\", \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\", 4, \"ngIf\"], [1, \"flex\", \"justify-center\", \"my-12\"], [1, \"relative\"], [1, \"w-14\", \"h-14\", \"border-4\", \"border-[#4f5fad]/20\", \"dark:border-[#6d78c9]/20\", \"border-t-[#4f5fad]\", \"dark:border-t-[#6d78c9]\", \"rounded-full\", \"animate-spin\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/20\", \"dark:bg-[#6d78c9]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"bg-white\", \"dark:bg-[#1e1e1e]\", \"rounded-xl\", \"shadow-md\", \"dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)]\", \"p-8\", \"text-center\", \"backdrop-blur-sm\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\"], [1, \"w-24\", \"h-24\", \"mx-auto\", \"mb-6\", \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/10\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\", \"relative\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-12\", \"h-12\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"relative\", \"z-10\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"1.5\", \"d\", \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"], [1, \"text-xl\", \"font-medium\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\", \"mb-2\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mt-1\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"lg:grid-cols-3\", \"gap-6\"], [\"class\", \"bg-white dark:bg-[#1e1e1e] rounded-xl shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)] overflow-hidden hover:shadow-lg dark:hover:shadow-[0_8px_30px_rgba(0,0,0,0.3)] transition-all duration-300 hover:-translate-y-1 backdrop-blur-sm border border-[#edf1f4]/50 dark:border-[#2a2a2a] group\", 4, \"ngFor\", \"ngForOf\"], [1, \"bg-white\", \"dark:bg-[#1e1e1e]\", \"rounded-xl\", \"shadow-md\", \"dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)]\", \"overflow-hidden\", \"hover:shadow-lg\", \"dark:hover:shadow-[0_8px_30px_rgba(0,0,0,0.3)]\", \"transition-all\", \"duration-300\", \"hover:-translate-y-1\", \"backdrop-blur-sm\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"group\"], [1, \"relative\", \"overflow-hidden\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"opacity-0\", \"group-hover:opacity-100\", \"blur-md\", \"transition-opacity\", \"duration-300\"], [1, \"p-5\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"relative\"], [1, \"text-lg\", \"font-bold\", \"pr-10\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\", \"group-hover:scale-[1.01]\", \"transition-transform\", \"duration-300\", \"origin-left\"], [1, \"flex\", \"items-center\", \"mt-2\", \"text-xs\", \"space-x-2\"], [1, \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/10\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"px-2\", \"py-0.5\", \"rounded-full\", \"backdrop-blur-sm\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [1, \"p-5\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mb-4\", \"line-clamp-3\"], [\"class\", \"mb-4\", 4, \"ngIf\"], [1, \"flex\", \"justify-between\", \"items-center\", \"pt-3\", \"border-t\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\"], [1, \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"hover:text-[#3d4a85]\", \"dark:hover:text-[#4f5fad]\", \"text-sm\", \"font-medium\", \"flex\", \"items-center\", \"transition-colors\", \"relative\", \"group/details\", 3, \"routerLink\"], [1, \"relative\", \"mr-1\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"relative\", \"z-10\", \"group-hover/details:scale-110\", \"transition-transform\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/20\", \"dark:bg-[#6d78c9]/20\", \"opacity-0\", \"group-hover/details:opacity-100\", \"transition-opacity\", \"blur-md\", \"rounded-full\"], [4, \"ngIf\"], [1, \"mb-4\"], [1, \"text-xs\", \"font-semibold\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"uppercase\", \"tracking-wider\", \"mb-2\"], [1, \"space-y-2\"], [\"class\", \"flex items-center justify-between bg-[#edf1f4]/70 dark:bg-[#2a2a2a]/70 rounded-lg p-2.5 backdrop-blur-sm group/file hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] transition-colors\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"items-center\", \"justify-between\", \"bg-[#edf1f4]/70\", \"dark:bg-[#2a2a2a]/70\", \"rounded-lg\", \"p-2.5\", \"backdrop-blur-sm\", \"group/file\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"transition-colors\"], [1, \"flex\", \"items-center\", \"truncate\"], [1, \"relative\", \"mr-2\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"relative\", \"z-10\", \"group-hover/file:scale-110\", \"transition-transform\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/20\", \"dark:bg-[#6d78c9]/20\", \"opacity-0\", \"group-hover/file:opacity-100\", \"transition-opacity\", \"blur-md\", \"rounded-full\"], [1, \"text-xs\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"truncate\"], [\"download\", \"\", 1, \"relative\", \"overflow-hidden\", \"group/download\", 3, \"href\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#3d4a85]\", \"dark:to-[#6d78c9]\", \"rounded-lg\", \"transition-transform\", \"duration-300\", \"group-hover/download:scale-105\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#3d4a85]\", \"dark:to-[#6d78c9]\", \"rounded-lg\", \"opacity-0\", \"group-hover/download:opacity-100\", \"blur-md\", \"transition-opacity\", \"duration-300\"], [1, \"relative\", \"flex\", \"items-center\", \"text-white\", \"text-xs\", \"px-3\", \"py-1\", \"rounded-lg\", \"transition-all\", \"z-10\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-3\", \"h-3\", \"mr-1\", \"group-hover/download:scale-110\", \"transition-transform\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4\"], [1, \"bg-gradient-to-r\", \"from-green-100\", \"to-green-50\", \"dark:from-green-900/30\", \"dark:to-green-800/30\", \"text-green-800\", \"dark:text-green-400\", \"text-xs\", \"px-3\", \"py-1.5\", \"rounded-full\", \"flex\", \"items-center\", \"shadow-sm\", \"backdrop-blur-sm\"], [\"fill\", \"currentColor\", \"viewBox\", \"0 0 20 20\", 1, \"w-3\", \"h-3\", \"relative\", \"z-10\"], [\"fill-rule\", \"evenodd\", \"d\", \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\", \"clip-rule\", \"evenodd\"], [1, \"absolute\", \"inset-0\", \"bg-green-500/20\", \"blur-md\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"relative\", \"overflow-hidden\", \"group/submit\", 3, \"routerLink\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#3d4a85]\", \"dark:to-[#6d78c9]\", \"rounded-lg\", \"transition-transform\", \"duration-300\", \"group-hover/submit:scale-105\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#3d4a85]\", \"dark:to-[#6d78c9]\", \"rounded-lg\", \"opacity-0\", \"group-hover/submit:opacity-100\", \"blur-md\", \"transition-opacity\", \"duration-300\"], [1, \"relative\", \"flex\", \"items-center\", \"text-white\", \"text-sm\", \"font-medium\", \"px-3\", \"py-1.5\", \"rounded-lg\", \"transition-all\", \"z-10\"]],\n      template: function ProjectListComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵelement(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5);\n          i0.ɵɵelement(6, \"div\", 6)(7, \"div\", 6)(8, \"div\", 6)(9, \"div\", 6)(10, \"div\", 6)(11, \"div\", 6)(12, \"div\", 6)(13, \"div\", 6)(14, \"div\", 6)(15, \"div\", 6)(16, \"div\", 6);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(17, \"div\", 7)(18, \"div\", 8)(19, \"div\")(20, \"h1\", 9);\n          i0.ɵɵtext(21, \" Projets \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"p\", 10);\n          i0.ɵɵtext(23, \" Vos missions acad\\u00E9miques \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(24, \"div\", 11);\n          i0.ɵɵelement(25, \"div\", 12);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(26, \"svg\", 13);\n          i0.ɵɵelement(27, \"path\", 14);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(28, ProjectListComponent_div_28_Template, 4, 0, \"div\", 15);\n          i0.ɵɵtemplate(29, ProjectListComponent_div_29_Template, 9, 0, \"div\", 16);\n          i0.ɵɵtemplate(30, ProjectListComponent_div_30_Template, 2, 1, \"div\", 17);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(28);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.projets.length === 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n        }\n      },\n      dependencies: [i5.NgForOf, i5.NgIf, i6.RouterLink, i5.DatePipe],\n      styles: [\"\\n\\n.badge[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 9999px;\\n  font-size: 0.75rem;\\n  font-weight: 500;\\n}\\n\\n.badge-group[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.2);\\n  color: #ffffff;\\n}\\n\\n.badge-deadline[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.2);\\n  color: #ffffff;\\n}\\n\\n\\n\\n.line-clamp-3[_ngcontent-%COMP%] {\\n  display: -webkit-box;\\n  -webkit-line-clamp: 3;\\n  -webkit-box-orient: vertical;\\n  overflow: hidden;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInByb2plY3QtbGlzdC5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLDJCQUEyQjtBQUMzQjtFQUNFLG9CQUFvQjtFQUNwQixtQkFBbUI7RUFDbkIsdUJBQXVCO0VBQ3ZCLHFCQUFxQjtFQUNyQixrQkFBa0I7RUFDbEIsZ0JBQWdCO0FBQ2xCOztBQUVBO0VBQ0UsMENBQTBDO0VBQzFDLGNBQWM7QUFDaEI7O0FBRUE7RUFDRSwwQ0FBMEM7RUFDMUMsY0FBYztBQUNoQjs7QUFFQSxvREFBb0Q7QUFDcEQ7RUFDRSxvQkFBb0I7RUFDcEIscUJBQXFCO0VBQ3JCLDRCQUE0QjtFQUM1QixnQkFBZ0I7QUFDbEIiLCJmaWxlIjoicHJvamVjdC1saXN0LmNvbXBvbmVudC5jc3MiLCJzb3VyY2VzQ29udGVudCI6WyIvKiBTdHlsZXMgcG91ciBsZXMgYmFkZ2VzICovXG4uYmFkZ2Uge1xuICBkaXNwbGF5OiBpbmxpbmUtZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgcGFkZGluZzogMC4yNXJlbSAwLjVyZW07XG4gIGJvcmRlci1yYWRpdXM6IDk5OTlweDtcbiAgZm9udC1zaXplOiAwLjc1cmVtO1xuICBmb250LXdlaWdodDogNTAwO1xufVxuXG4uYmFkZ2UtZ3JvdXAge1xuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMik7XG4gIGNvbG9yOiAjZmZmZmZmO1xufVxuXG4uYmFkZ2UtZGVhZGxpbmUge1xuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMik7XG4gIGNvbG9yOiAjZmZmZmZmO1xufVxuXG4vKiBMaW1pdGVyIGxlIG5vbWJyZSBkZSBsaWduZXMgcG91ciBsYSBkZXNjcmlwdGlvbiAqL1xuLmxpbmUtY2xhbXAtMyB7XG4gIGRpc3BsYXk6IC13ZWJraXQtYm94O1xuICAtd2Via2l0LWxpbmUtY2xhbXA6IDM7XG4gIC13ZWJraXQtYm94LW9yaWVudDogdmVydGljYWw7XG4gIG92ZXJmbG93OiBoaWRkZW47XG59Il19 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvZnJvbnQvcHJvamVjdHMvcHJvamVjdC1saXN0L3Byb2plY3QtbGlzdC5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLDJCQUEyQjtBQUMzQjtFQUNFLG9CQUFvQjtFQUNwQixtQkFBbUI7RUFDbkIsdUJBQXVCO0VBQ3ZCLHFCQUFxQjtFQUNyQixrQkFBa0I7RUFDbEIsZ0JBQWdCO0FBQ2xCOztBQUVBO0VBQ0UsMENBQTBDO0VBQzFDLGNBQWM7QUFDaEI7O0FBRUE7RUFDRSwwQ0FBMEM7RUFDMUMsY0FBYztBQUNoQjs7QUFFQSxvREFBb0Q7QUFDcEQ7RUFDRSxvQkFBb0I7RUFDcEIscUJBQXFCO0VBQ3JCLDRCQUE0QjtFQUM1QixnQkFBZ0I7QUFDbEI7QUFDQSw0d0NBQTR3QyIsInNvdXJjZXNDb250ZW50IjpbIi8qIFN0eWxlcyBwb3VyIGxlcyBiYWRnZXMgKi9cbi5iYWRnZSB7XG4gIGRpc3BsYXk6IGlubGluZS1mbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBwYWRkaW5nOiAwLjI1cmVtIDAuNXJlbTtcbiAgYm9yZGVyLXJhZGl1czogOTk5OXB4O1xuICBmb250LXNpemU6IDAuNzVyZW07XG4gIGZvbnQtd2VpZ2h0OiA1MDA7XG59XG5cbi5iYWRnZS1ncm91cCB7XG4gIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4yKTtcbiAgY29sb3I6ICNmZmZmZmY7XG59XG5cbi5iYWRnZS1kZWFkbGluZSB7XG4gIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4yKTtcbiAgY29sb3I6ICNmZmZmZmY7XG59XG5cbi8qIExpbWl0ZXIgbGUgbm9tYnJlIGRlIGxpZ25lcyBwb3VyIGxhIGRlc2NyaXB0aW9uICovXG4ubGluZS1jbGFtcC0zIHtcbiAgZGlzcGxheTogLXdlYmtpdC1ib3g7XG4gIC13ZWJraXQtbGluZS1jbGFtcDogMztcbiAgLXdlYmtpdC1ib3gtb3JpZW50OiB2ZXJ0aWNhbDtcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["environment", "i0", "ɵɵnamespaceHTML", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵnamespaceSVG", "ɵɵtext", "ɵɵadvance", "ɵɵproperty", "ctx_r8", "getFileUrl", "file_r9", "ɵɵsanitizeUrl", "ɵɵattribute", "getFileName", "ɵɵtemplate", "ProjectListComponent_div_30_div_1_div_18_div_4_Template", "projet_r4", "fichiers", "ɵɵelementContainerStart", "ɵɵelementContainerEnd", "ɵɵpureFunction1", "_c0", "_id", "ProjectListComponent_div_30_div_1_div_18_Template", "ProjectListComponent_div_30_div_1_ng_container_27_Template", "ProjectListComponent_div_30_div_1_ng_container_28_Template", "ɵɵtextInterpolate1", "titre", "ɵɵtextInterpolate", "groupe", "ɵɵpipeBind2", "dateLimite", "description", "length", "_c1", "ctx_r3", "isRendu", "ProjectListComponent_div_30_div_1_Template", "ctx_r2", "projets", "ProjectListComponent", "constructor", "projetService", "authService", "rendusService", "fileService", "rendusMap", "Map", "isLoading", "userGroup", "ngOnInit", "getCurrentUser", "loadProjets", "getProjets", "subscribe", "next", "for<PERSON>ach", "projet", "checkRenduStatus", "error", "console", "projetId", "etudiantId", "getCurrentUserId", "checkRenduExists", "exists", "set", "filePath", "fileName", "includes", "parts", "split", "urlBackend", "get", "ɵɵdirectiveInject", "i1", "ProjetService", "i2", "AuthuserService", "i3", "RendusService", "i4", "FileService", "selectors", "decls", "vars", "consts", "template", "ProjectListComponent_Template", "rf", "ctx", "ProjectListComponent_div_28_Template", "ProjectListComponent_div_29_Template", "ProjectListComponent_div_30_Template"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\projects\\project-list\\project-list.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\projects\\project-list\\project-list.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { ProjetService } from '@app/services/projects.service';\nimport { Projet } from 'src/app/models/projet.model';\nimport { AuthuserService } from 'src/app/services/authuser.service';\nimport { RendusService } from 'src/app/services/rendus.service';\nimport { FileService } from 'src/app/services/file.service';\nimport { environment } from 'src/environments/environment';\n\n// Composant pour afficher la liste des projets\n@Component({\n  selector: 'app-project-list',\n  templateUrl: './project-list.component.html',\n  styleUrls: ['./project-list.component.css'],\n})\nexport class ProjectListComponent implements OnInit {\n  projets: Projet[] = [];\n  rendusMap: Map<string, boolean> = new Map();\n  isLoading = true;\n  userGroup: string = '';\n\n  constructor(\n    private projetService: ProjetService,\n    private authService: AuthuserService,\n    private rendusService: RendusService,\n    private fileService: FileService\n  ) {}\n\n  ngOnInit(): void {\n    // On garde cette ligne pour une utilisation future\n    this.userGroup = this.authService.getCurrentUser()?.groupe || '';\n    this.loadProjets();\n  }\n\n  loadProjets(): void {\n    this.isLoading = true;\n    this.projetService.getProjets().subscribe({\n      next: (projets: Projet[]) => {\n        // Afficher tous les projets sans filtrage\n        this.projets = projets;\n        this.isLoading = false;\n\n        // Vérifier quels projets ont déjà été rendus par l'étudiant\n        this.projets.forEach((projet) => {\n          if (projet._id) {\n            this.checkRenduStatus(projet._id);\n          }\n        });\n      },\n      error: (error: any) => {\n        console.error('Erreur lors du chargement des projets', error);\n        this.isLoading = false;\n      },\n    });\n  }\n\n  checkRenduStatus(projetId: string): void {\n    const etudiantId = this.authService.getCurrentUserId();\n    if (!etudiantId) return;\n\n    this.rendusService.checkRenduExists(projetId, etudiantId).subscribe({\n      next: (exists: boolean) => {\n        this.rendusMap.set(projetId, exists);\n      },\n      error: (error: any) => {\n        console.error(\n          `Erreur lors de la vérification du rendu pour le projet ${projetId}`,\n          error\n        );\n      },\n    });\n  }\n\n  getFileUrl(filePath: string): string {\n    if (!filePath) return '';\n\n    // Extraire uniquement le nom du fichier\n    let fileName = filePath;\n\n    // Si le chemin contient des slashes ou backslashes, prendre la dernière partie\n    if (filePath.includes('/') || filePath.includes('\\\\')) {\n      const parts = filePath.split(/[\\/\\\\]/);\n      fileName = parts[parts.length - 1];\n    }\n\n    // Utiliser la route qui pointe vers le bon emplacement\n    return `${environment.urlBackend}projets/telecharger/${fileName}`;\n  }\n\n  getFileName(filePath: string): string {\n    if (!filePath) return 'fichier';\n\n    // Extraire uniquement le nom du fichier\n    if (filePath.includes('/') || filePath.includes('\\\\')) {\n      const parts = filePath.split(/[\\/\\\\]/);\n      return parts[parts.length - 1];\n    }\n\n    return filePath;\n  }\n\n  // Méthode pour vérifier si un projet a été rendu\n  isRendu(projetId: string | undefined): boolean {\n    return projetId ? this.rendusMap.get(projetId) === true : false;\n  }\n}\n", "<div class=\"min-h-screen bg-[#edf1f4] dark:bg-[#121212] p-4 md:p-6 relative\">\n  <!-- Background decorative elements -->\n  <div class=\"absolute inset-0 overflow-hidden pointer-events-none\">\n    <div\n      class=\"absolute top-[15%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-br from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl\"\n    ></div>\n    <div\n      class=\"absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl\"\n    ></div>\n\n    <!-- Grid pattern -->\n    <div class=\"absolute inset-0 opacity-5 dark:opacity-[0.03]\">\n      <div class=\"h-full grid grid-cols-12\">\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n      </div>\n    </div>\n  </div>\n\n  <div class=\"max-w-6xl mx-auto relative z-10\">\n    <div class=\"flex justify-between items-center mb-8\">\n      <div>\n        <h1\n          class=\"text-3xl font-bold bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent\"\n        >\n          Projets\n        </h1>\n        <p class=\"text-[#6d6870] dark:text-[#a0a0a0] text-sm md:text-base mt-1\">\n          Vos missions académiques\n        </p>\n      </div>\n      <div\n        class=\"h-12 w-12 rounded-full bg-gradient-to-br from-[#3d4a85] to-[#4f5fad] dark:from-[#3d4a85] dark:to-[#6d78c9] flex items-center justify-center text-white shadow-lg relative group overflow-hidden\"\n      >\n        <!-- Glow effect -->\n        <div\n          class=\"absolute inset-0 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#3d4a85] dark:to-[#6d78c9] rounded-full opacity-0 group-hover:opacity-100 blur-xl transition-opacity duration-300\"\n        ></div>\n\n        <svg\n          xmlns=\"http://www.w3.org/2000/svg\"\n          class=\"h-6 w-6 relative z-10 group-hover:scale-110 transition-transform duration-300\"\n          fill=\"none\"\n          viewBox=\"0 0 24 24\"\n          stroke=\"currentColor\"\n        >\n          <path\n            stroke-linecap=\"round\"\n            stroke-linejoin=\"round\"\n            stroke-width=\"2\"\n            d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n          />\n        </svg>\n      </div>\n    </div>\n\n    <!-- Loading Indicator -->\n    <div *ngIf=\"isLoading\" class=\"flex justify-center my-12\">\n      <div class=\"relative\">\n        <div\n          class=\"w-14 h-14 border-4 border-[#4f5fad]/20 dark:border-[#6d78c9]/20 border-t-[#4f5fad] dark:border-t-[#6d78c9] rounded-full animate-spin\"\n        ></div>\n        <!-- Glow effect -->\n        <div\n          class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 blur-xl rounded-full transform scale-150 -z-10\"\n        ></div>\n      </div>\n    </div>\n\n    <!-- No Projects -->\n    <div\n      *ngIf=\"!isLoading && projets.length === 0\"\n      class=\"bg-white dark:bg-[#1e1e1e] rounded-xl shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)] p-8 text-center backdrop-blur-sm border border-[#edf1f4]/50 dark:border-[#2a2a2a]\"\n    >\n      <div\n        class=\"w-24 h-24 mx-auto mb-6 bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 rounded-full flex items-center justify-center relative\"\n      >\n        <svg\n          class=\"w-12 h-12 text-[#4f5fad] dark:text-[#6d78c9] relative z-10\"\n          fill=\"none\"\n          stroke=\"currentColor\"\n          viewBox=\"0 0 24 24\"\n        >\n          <path\n            stroke-linecap=\"round\"\n            stroke-linejoin=\"round\"\n            stroke-width=\"1.5\"\n            d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n          ></path>\n        </svg>\n        <!-- Glow effect -->\n        <div\n          class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 blur-xl rounded-full transform scale-150 -z-10\"\n        ></div>\n      </div>\n      <h3\n        class=\"text-xl font-medium bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent mb-2\"\n      >\n        Aucun projet disponible\n      </h3>\n      <p class=\"text-[#6d6870] dark:text-[#a0a0a0] mt-1\">\n        Vos missions apparaîtront ici\n      </p>\n    </div>\n\n    <!-- Projects Grid -->\n    <div\n      *ngIf=\"!isLoading\"\n      class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\"\n    >\n      <div\n        *ngFor=\"let projet of projets\"\n        class=\"bg-white dark:bg-[#1e1e1e] rounded-xl shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)] overflow-hidden hover:shadow-lg dark:hover:shadow-[0_8px_30px_rgba(0,0,0,0.3)] transition-all duration-300 hover:-translate-y-1 backdrop-blur-sm border border-[#edf1f4]/50 dark:border-[#2a2a2a] group\"\n      >\n        <!-- Header -->\n        <div class=\"relative overflow-hidden\">\n          <!-- Decorative gradient top border -->\n          <div\n            class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad]\"\n          ></div>\n\n          <!-- Glow effect on hover -->\n          <div\n            class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] opacity-0 group-hover:opacity-100 blur-md transition-opacity duration-300\"\n          ></div>\n\n          <div class=\"p-5 bg-white dark:bg-[#1e1e1e] relative\">\n            <h3\n              class=\"text-lg font-bold pr-10 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent group-hover:scale-[1.01] transition-transform duration-300 origin-left\"\n            >\n              {{ projet.titre }}\n            </h3>\n            <div class=\"flex items-center mt-2 text-xs space-x-2\">\n              <span\n                class=\"bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 text-[#4f5fad] dark:text-[#6d78c9] px-2 py-0.5 rounded-full backdrop-blur-sm\"\n                >{{ projet.groupe || \"Tous\" }}</span\n              >\n              <span class=\"text-[#6d6870] dark:text-[#a0a0a0]\">•</span>\n              <span\n                class=\"bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 text-[#4f5fad] dark:text-[#6d78c9] px-2 py-0.5 rounded-full backdrop-blur-sm\"\n                >{{ projet.dateLimite | date : \"dd/MM/yyyy\" }}</span\n              >\n            </div>\n          </div>\n        </div>\n\n        <!-- Content -->\n        <div class=\"p-5\">\n          <p\n            class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0] mb-4 line-clamp-3\"\n          >\n            {{ projet.description || \"Aucune description\" }}\n          </p>\n\n          <!-- Files -->\n          <div\n            *ngIf=\"projet.fichiers && projet.fichiers.length > 0\"\n            class=\"mb-4\"\n          >\n            <h4\n              class=\"text-xs font-semibold text-[#6d6870] dark:text-[#a0a0a0] uppercase tracking-wider mb-2\"\n            >\n              Fichiers\n            </h4>\n            <div class=\"space-y-2\">\n              <div\n                *ngFor=\"let file of projet.fichiers\"\n                class=\"flex items-center justify-between bg-[#edf1f4]/70 dark:bg-[#2a2a2a]/70 rounded-lg p-2.5 backdrop-blur-sm group/file hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] transition-colors\"\n              >\n                <div class=\"flex items-center truncate\">\n                  <div class=\"relative mr-2\">\n                    <svg\n                      class=\"w-4 h-4 text-[#4f5fad] dark:text-[#6d78c9] relative z-10 group-hover/file:scale-110 transition-transform\"\n                      fill=\"none\"\n                      stroke=\"currentColor\"\n                      viewBox=\"0 0 24 24\"\n                    >\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        stroke-width=\"2\"\n                        d=\"M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z\"\n                      ></path>\n                    </svg>\n                    <!-- Glow effect -->\n                    <div\n                      class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 opacity-0 group-hover/file:opacity-100 transition-opacity blur-md rounded-full\"\n                    ></div>\n                  </div>\n                  <span\n                    class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0] truncate\"\n                    >Document</span\n                  >\n                </div>\n                <a\n                  [href]=\"getFileUrl(file)\"\n                  download\n                  [attr.download]=\"getFileName(file)\"\n                  class=\"relative overflow-hidden group/download\"\n                >\n                  <div\n                    class=\"absolute inset-0 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#3d4a85] dark:to-[#6d78c9] rounded-lg transition-transform duration-300 group-hover/download:scale-105\"\n                  ></div>\n                  <div\n                    class=\"absolute inset-0 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#3d4a85] dark:to-[#6d78c9] rounded-lg opacity-0 group-hover/download:opacity-100 blur-md transition-opacity duration-300\"\n                  ></div>\n                  <span\n                    class=\"relative flex items-center text-white text-xs px-3 py-1 rounded-lg transition-all z-10\"\n                  >\n                    <svg\n                      class=\"w-3 h-3 mr-1 group-hover/download:scale-110 transition-transform\"\n                      fill=\"none\"\n                      stroke=\"currentColor\"\n                      viewBox=\"0 0 24 24\"\n                    >\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        stroke-width=\"2\"\n                        d=\"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4\"\n                      ></path>\n                    </svg>\n                    Télécharger\n                  </span>\n                </a>\n              </div>\n            </div>\n          </div>\n\n          <!-- Actions -->\n          <div\n            class=\"flex justify-between items-center pt-3 border-t border-[#edf1f4]/50 dark:border-[#2a2a2a]\"\n          >\n            <a\n              [routerLink]=\"['/front/projects/detail', projet._id]\"\n              class=\"text-[#4f5fad] dark:text-[#6d78c9] hover:text-[#3d4a85] dark:hover:text-[#4f5fad] text-sm font-medium flex items-center transition-colors relative group/details\"\n            >\n              <div class=\"relative mr-1\">\n                <svg\n                  class=\"w-4 h-4 relative z-10 group-hover/details:scale-110 transition-transform\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  viewBox=\"0 0 24 24\"\n                >\n                  <path\n                    stroke-linecap=\"round\"\n                    stroke-linejoin=\"round\"\n                    stroke-width=\"2\"\n                    d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                  ></path>\n                </svg>\n                <!-- Glow effect -->\n                <div\n                  class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 opacity-0 group-hover/details:opacity-100 transition-opacity blur-md rounded-full\"\n                ></div>\n              </div>\n              <span>Détails</span>\n            </a>\n\n            <ng-container *ngIf=\"isRendu(projet._id)\">\n              <span\n                class=\"bg-gradient-to-r from-green-100 to-green-50 dark:from-green-900/30 dark:to-green-800/30 text-green-800 dark:text-green-400 text-xs px-3 py-1.5 rounded-full flex items-center shadow-sm backdrop-blur-sm\"\n              >\n                <div class=\"relative mr-1\">\n                  <svg\n                    class=\"w-3 h-3 relative z-10\"\n                    fill=\"currentColor\"\n                    viewBox=\"0 0 20 20\"\n                  >\n                    <path\n                      fill-rule=\"evenodd\"\n                      d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\"\n                      clip-rule=\"evenodd\"\n                    ></path>\n                  </svg>\n                  <!-- Glow effect -->\n                  <div\n                    class=\"absolute inset-0 bg-green-500/20 blur-md rounded-full transform scale-150 -z-10\"\n                  ></div>\n                </div>\n                <span>Rendu</span>\n              </span>\n            </ng-container>\n\n            <ng-container *ngIf=\"!isRendu(projet._id)\">\n              <a\n                [routerLink]=\"['/front/projects/submit', projet._id]\"\n                class=\"relative overflow-hidden group/submit\"\n              >\n                <div\n                  class=\"absolute inset-0 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#3d4a85] dark:to-[#6d78c9] rounded-lg transition-transform duration-300 group-hover/submit:scale-105\"\n                ></div>\n                <div\n                  class=\"absolute inset-0 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#3d4a85] dark:to-[#6d78c9] rounded-lg opacity-0 group-hover/submit:opacity-100 blur-md transition-opacity duration-300\"\n                ></div>\n                <span\n                  class=\"relative flex items-center text-white text-sm font-medium px-3 py-1.5 rounded-lg transition-all z-10\"\n                >\n                  Rendre\n                </span>\n              </a>\n            </ng-container>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AAMA,SAASA,WAAW,QAAQ,8BAA8B;;;;;;;;;;;IC4DtDC,EAAA,CAAAC,eAAA,EAAyD;IAAzDD,EAAA,CAAAE,cAAA,cAAyD;IAErDF,EAAA,CAAAG,SAAA,cAEO;IAKTH,EAAA,CAAAI,YAAA,EAAM;;;;;;IAIRJ,EAAA,CAAAC,eAAA,EAGC;IAHDD,EAAA,CAAAE,cAAA,cAGC;IAIGF,EAAA,CAAAK,cAAA,EAKC;IALDL,EAAA,CAAAE,cAAA,cAKC;IACCF,EAAA,CAAAG,SAAA,eAKQ;IACVH,EAAA,CAAAI,YAAA,EAAM;IAENJ,EAAA,CAAAC,eAAA,EAEC;IAFDD,EAAA,CAAAG,SAAA,cAEO;IACTH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,aAEC;IACCF,EAAA,CAAAM,MAAA,gCACF;IAAAN,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAE,cAAA,YAAmD;IACjDF,EAAA,CAAAM,MAAA,2CACF;IAAAN,EAAA,CAAAI,YAAA,EAAI;;;;;IA+DIJ,EAAA,CAAAE,cAAA,cAGC;IAGKF,EAAA,CAAAK,cAAA,EAKC;IALDL,EAAA,CAAAE,cAAA,cAKC;IACCF,EAAA,CAAAG,SAAA,eAKQ;IACVH,EAAA,CAAAI,YAAA,EAAM;IAENJ,EAAA,CAAAC,eAAA,EAEC;IAFDD,EAAA,CAAAG,SAAA,cAEO;IACTH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,eAEG;IAAAF,EAAA,CAAAM,MAAA,eAAQ;IAAAN,EAAA,CAAAI,YAAA,EACV;IAEHJ,EAAA,CAAAE,cAAA,YAKC;IACCF,EAAA,CAAAG,SAAA,cAEO;IAIPH,EAAA,CAAAE,cAAA,gBAEC;IACCF,EAAA,CAAAK,cAAA,EAKC;IALDL,EAAA,CAAAE,cAAA,eAKC;IACCF,EAAA,CAAAG,SAAA,gBAKQ;IACVH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAM,MAAA,+BACF;IAAAN,EAAA,CAAAI,YAAA,EAAO;;;;;IA5BPJ,EAAA,CAAAO,SAAA,GAAyB;IAAzBP,EAAA,CAAAQ,UAAA,SAAAC,MAAA,CAAAC,UAAA,CAAAC,OAAA,GAAAX,EAAA,CAAAY,aAAA,CAAyB;IAEzBZ,EAAA,CAAAa,WAAA,aAAAJ,MAAA,CAAAK,WAAA,CAAAH,OAAA,EAAmC;;;;;IA1C3CX,EAAA,CAAAE,cAAA,cAGC;IAIGF,EAAA,CAAAM,MAAA,iBACF;IAAAN,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAE,cAAA,cAAuB;IACrBF,EAAA,CAAAe,UAAA,IAAAC,uDAAA,mBA4DM;IACRhB,EAAA,CAAAI,YAAA,EAAM;;;;IA5DeJ,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAQ,UAAA,YAAAS,SAAA,CAAAC,QAAA,CAAkB;;;;;IA6FvClB,EAAA,CAAAmB,uBAAA,GAA0C;IACxCnB,EAAA,CAAAE,cAAA,eAEC;IAEGF,EAAA,CAAAK,cAAA,EAIC;IAJDL,EAAA,CAAAE,cAAA,cAIC;IACCF,EAAA,CAAAG,SAAA,eAIQ;IACVH,EAAA,CAAAI,YAAA,EAAM;IAENJ,EAAA,CAAAC,eAAA,EAEC;IAFDD,EAAA,CAAAG,SAAA,cAEO;IACTH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,WAAM;IAAAF,EAAA,CAAAM,MAAA,YAAK;IAAAN,EAAA,CAAAI,YAAA,EAAO;IAEtBJ,EAAA,CAAAoB,qBAAA,EAAe;;;;;;;;IAEfpB,EAAA,CAAAmB,uBAAA,GAA2C;IACzCnB,EAAA,CAAAE,cAAA,YAGC;IACCF,EAAA,CAAAG,SAAA,cAEO;IAIPH,EAAA,CAAAE,cAAA,eAEC;IACCF,EAAA,CAAAM,MAAA,eACF;IAAAN,EAAA,CAAAI,YAAA,EAAO;IAEXJ,EAAA,CAAAoB,qBAAA,EAAe;;;;IAfXpB,EAAA,CAAAO,SAAA,GAAqD;IAArDP,EAAA,CAAAQ,UAAA,eAAAR,EAAA,CAAAqB,eAAA,IAAAC,GAAA,EAAAL,SAAA,CAAAM,GAAA,EAAqD;;;;;;;;IAhL/DvB,EAAA,CAAAE,cAAA,cAGC;IAIGF,EAAA,CAAAG,SAAA,cAEO;IAOPH,EAAA,CAAAE,cAAA,cAAqD;IAIjDF,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAE,cAAA,cAAsD;IAGjDF,EAAA,CAAAM,MAAA,GAA6B;IAAAN,EAAA,CAAAI,YAAA,EAC/B;IACDJ,EAAA,CAAAE,cAAA,gBAAiD;IAAAF,EAAA,CAAAM,MAAA,cAAC;IAAAN,EAAA,CAAAI,YAAA,EAAO;IACzDJ,EAAA,CAAAE,cAAA,gBAEG;IAAAF,EAAA,CAAAM,MAAA,IAA6C;;IAAAN,EAAA,CAAAI,YAAA,EAC/C;IAMPJ,EAAA,CAAAE,cAAA,eAAiB;IAIbF,EAAA,CAAAM,MAAA,IACF;IAAAN,EAAA,CAAAI,YAAA,EAAI;IAGJJ,EAAA,CAAAe,UAAA,KAAAS,iDAAA,kBAwEM;IAGNxB,EAAA,CAAAE,cAAA,eAEC;IAMKF,EAAA,CAAAK,cAAA,EAKC;IALDL,EAAA,CAAAE,cAAA,eAKC;IACCF,EAAA,CAAAG,SAAA,gBAKQ;IACVH,EAAA,CAAAI,YAAA,EAAM;IAENJ,EAAA,CAAAC,eAAA,EAEC;IAFDD,EAAA,CAAAG,SAAA,eAEO;IACTH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,YAAM;IAAAF,EAAA,CAAAM,MAAA,oBAAO;IAAAN,EAAA,CAAAI,YAAA,EAAO;IAGtBJ,EAAA,CAAAe,UAAA,KAAAU,0DAAA,2BAuBe;IAEfzB,EAAA,CAAAe,UAAA,KAAAW,0DAAA,2BAiBe;IACjB1B,EAAA,CAAAI,YAAA,EAAM;;;;;IA5KFJ,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAA2B,kBAAA,MAAAV,SAAA,CAAAW,KAAA,MACF;IAIK5B,EAAA,CAAAO,SAAA,GAA6B;IAA7BP,EAAA,CAAA6B,iBAAA,CAAAZ,SAAA,CAAAa,MAAA,WAA6B;IAK7B9B,EAAA,CAAAO,SAAA,GAA6C;IAA7CP,EAAA,CAAA6B,iBAAA,CAAA7B,EAAA,CAAA+B,WAAA,QAAAd,SAAA,CAAAe,UAAA,gBAA6C;IAWlDhC,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAA2B,kBAAA,MAAAV,SAAA,CAAAgB,WAAA,8BACF;IAIGjC,EAAA,CAAAO,SAAA,GAAmD;IAAnDP,EAAA,CAAAQ,UAAA,SAAAS,SAAA,CAAAC,QAAA,IAAAD,SAAA,CAAAC,QAAA,CAAAgB,MAAA,KAAmD;IA8ElDlC,EAAA,CAAAO,SAAA,GAAqD;IAArDP,EAAA,CAAAQ,UAAA,eAAAR,EAAA,CAAAqB,eAAA,KAAAc,GAAA,EAAAlB,SAAA,CAAAM,GAAA,EAAqD;IAyBxCvB,EAAA,CAAAO,SAAA,GAAyB;IAAzBP,EAAA,CAAAQ,UAAA,SAAA4B,MAAA,CAAAC,OAAA,CAAApB,SAAA,CAAAM,GAAA,EAAyB;IAyBzBvB,EAAA,CAAAO,SAAA,GAA0B;IAA1BP,EAAA,CAAAQ,UAAA,UAAA4B,MAAA,CAAAC,OAAA,CAAApB,SAAA,CAAAM,GAAA,EAA0B;;;;;;IAlLjDvB,EAAA,CAAAC,eAAA,EAGC;IAHDD,EAAA,CAAAE,cAAA,cAGC;IACCF,EAAA,CAAAe,UAAA,IAAAuB,0CAAA,oBAkMM;IACRtC,EAAA,CAAAI,YAAA,EAAM;;;;IAlMiBJ,EAAA,CAAAO,SAAA,GAAU;IAAVP,EAAA,CAAAQ,UAAA,YAAA+B,MAAA,CAAAC,OAAA,CAAU;;;ADhHrC;AAMA,OAAM,MAAOC,oBAAoB;EAM/BC,YACUC,aAA4B,EAC5BC,WAA4B,EAC5BC,aAA4B,EAC5BC,WAAwB;IAHxB,KAAAH,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IATrB,KAAAN,OAAO,GAAa,EAAE;IACtB,KAAAO,SAAS,GAAyB,IAAIC,GAAG,EAAE;IAC3C,KAAAC,SAAS,GAAG,IAAI;IAChB,KAAAC,SAAS,GAAW,EAAE;EAOnB;EAEHC,QAAQA,CAAA;IACN;IACA,IAAI,CAACD,SAAS,GAAG,IAAI,CAACN,WAAW,CAACQ,cAAc,EAAE,EAAEtB,MAAM,IAAI,EAAE;IAChE,IAAI,CAACuB,WAAW,EAAE;EACpB;EAEAA,WAAWA,CAAA;IACT,IAAI,CAACJ,SAAS,GAAG,IAAI;IACrB,IAAI,CAACN,aAAa,CAACW,UAAU,EAAE,CAACC,SAAS,CAAC;MACxCC,IAAI,EAAGhB,OAAiB,IAAI;QAC1B;QACA,IAAI,CAACA,OAAO,GAAGA,OAAO;QACtB,IAAI,CAACS,SAAS,GAAG,KAAK;QAEtB;QACA,IAAI,CAACT,OAAO,CAACiB,OAAO,CAAEC,MAAM,IAAI;UAC9B,IAAIA,MAAM,CAACnC,GAAG,EAAE;YACd,IAAI,CAACoC,gBAAgB,CAACD,MAAM,CAACnC,GAAG,CAAC;;QAErC,CAAC,CAAC;MACJ,CAAC;MACDqC,KAAK,EAAGA,KAAU,IAAI;QACpBC,OAAO,CAACD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;QAC7D,IAAI,CAACX,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACJ;EAEAU,gBAAgBA,CAACG,QAAgB;IAC/B,MAAMC,UAAU,GAAG,IAAI,CAACnB,WAAW,CAACoB,gBAAgB,EAAE;IACtD,IAAI,CAACD,UAAU,EAAE;IAEjB,IAAI,CAAClB,aAAa,CAACoB,gBAAgB,CAACH,QAAQ,EAAEC,UAAU,CAAC,CAACR,SAAS,CAAC;MAClEC,IAAI,EAAGU,MAAe,IAAI;QACxB,IAAI,CAACnB,SAAS,CAACoB,GAAG,CAACL,QAAQ,EAAEI,MAAM,CAAC;MACtC,CAAC;MACDN,KAAK,EAAGA,KAAU,IAAI;QACpBC,OAAO,CAACD,KAAK,CACX,0DAA0DE,QAAQ,EAAE,EACpEF,KAAK,CACN;MACH;KACD,CAAC;EACJ;EAEAlD,UAAUA,CAAC0D,QAAgB;IACzB,IAAI,CAACA,QAAQ,EAAE,OAAO,EAAE;IAExB;IACA,IAAIC,QAAQ,GAAGD,QAAQ;IAEvB;IACA,IAAIA,QAAQ,CAACE,QAAQ,CAAC,GAAG,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,IAAI,CAAC,EAAE;MACrD,MAAMC,KAAK,GAAGH,QAAQ,CAACI,KAAK,CAAC,QAAQ,CAAC;MACtCH,QAAQ,GAAGE,KAAK,CAACA,KAAK,CAACrC,MAAM,GAAG,CAAC,CAAC;;IAGpC;IACA,OAAO,GAAGnC,WAAW,CAAC0E,UAAU,uBAAuBJ,QAAQ,EAAE;EACnE;EAEAvD,WAAWA,CAACsD,QAAgB;IAC1B,IAAI,CAACA,QAAQ,EAAE,OAAO,SAAS;IAE/B;IACA,IAAIA,QAAQ,CAACE,QAAQ,CAAC,GAAG,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,IAAI,CAAC,EAAE;MACrD,MAAMC,KAAK,GAAGH,QAAQ,CAACI,KAAK,CAAC,QAAQ,CAAC;MACtC,OAAOD,KAAK,CAACA,KAAK,CAACrC,MAAM,GAAG,CAAC,CAAC;;IAGhC,OAAOkC,QAAQ;EACjB;EAEA;EACA/B,OAAOA,CAACyB,QAA4B;IAClC,OAAOA,QAAQ,GAAG,IAAI,CAACf,SAAS,CAAC2B,GAAG,CAACZ,QAAQ,CAAC,KAAK,IAAI,GAAG,KAAK;EACjE;;;uBAzFWrB,oBAAoB,EAAAzC,EAAA,CAAA2E,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAA7E,EAAA,CAAA2E,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAA/E,EAAA,CAAA2E,iBAAA,CAAAK,EAAA,CAAAC,aAAA,GAAAjF,EAAA,CAAA2E,iBAAA,CAAAO,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAApB1C,oBAAoB;MAAA2C,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCdjC1F,EAAA,CAAAE,cAAA,aAA6E;UAGzEF,EAAA,CAAAG,SAAA,aAEO;UAMPH,EAAA,CAAAE,cAAA,aAA4D;UAExDF,EAAA,CAAAG,SAAA,aAAmE;UAWrEH,EAAA,CAAAI,YAAA,EAAM;UAIVJ,EAAA,CAAAE,cAAA,cAA6C;UAMrCF,EAAA,CAAAM,MAAA,iBACF;UAAAN,EAAA,CAAAI,YAAA,EAAK;UACLJ,EAAA,CAAAE,cAAA,aAAwE;UACtEF,EAAA,CAAAM,MAAA,uCACF;UAAAN,EAAA,CAAAI,YAAA,EAAI;UAENJ,EAAA,CAAAE,cAAA,eAEC;UAECF,EAAA,CAAAG,SAAA,eAEO;UAEPH,EAAA,CAAAK,cAAA,EAMC;UANDL,EAAA,CAAAE,cAAA,eAMC;UACCF,EAAA,CAAAG,SAAA,gBAKE;UACJH,EAAA,CAAAI,YAAA,EAAM;UAKVJ,EAAA,CAAAe,UAAA,KAAA6E,oCAAA,kBAUM;UAGN5F,EAAA,CAAAe,UAAA,KAAA8E,oCAAA,kBAiCM;UAGN7F,EAAA,CAAAe,UAAA,KAAA+E,oCAAA,kBAuMM;UACR9F,EAAA,CAAAI,YAAA,EAAM;;;UAzPEJ,EAAA,CAAAO,SAAA,IAAe;UAAfP,EAAA,CAAAQ,UAAA,SAAAmF,GAAA,CAAA1C,SAAA,CAAe;UAclBjD,EAAA,CAAAO,SAAA,GAAwC;UAAxCP,EAAA,CAAAQ,UAAA,UAAAmF,GAAA,CAAA1C,SAAA,IAAA0C,GAAA,CAAAnD,OAAA,CAAAN,MAAA,OAAwC;UAoCxClC,EAAA,CAAAO,SAAA,GAAgB;UAAhBP,EAAA,CAAAQ,UAAA,UAAAmF,GAAA,CAAA1C,SAAA,CAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}