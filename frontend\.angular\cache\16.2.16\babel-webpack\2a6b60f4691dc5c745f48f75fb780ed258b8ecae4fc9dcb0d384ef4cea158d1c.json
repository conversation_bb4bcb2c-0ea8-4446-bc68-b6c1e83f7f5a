{"ast": null, "code": "// This file can be replaced during build by using the `fileReplacements` array.\n// `ng build` replaces `environment.ts` with `environment.prod.ts`.\n// The list of file replacements can be found in `angular.json`.\nexport const environment = {\n  production: false,\n  secret: '2cinfo1',\n  client: 'esprit',\n  urlBackend: 'http://localhost:3000/api/',\n  geminiApiKey: 'AIzaSyDCXc16FzaVWSJkW4RGboTZ8AD9_PTDL88'\n};\n/*\n * For easier debugging in development mode, you can import the following file\n * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.\n *\n * This import should be commented out in production mode because it will have a negative impact\n * on performance if an error is thrown.\n */\n// import 'zone.js/plugins/zone-error';  // Included with Angular CLI.", "map": {"version": 3, "names": ["environment", "production", "secret", "client", "urlBackend", "geminiApiKey"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\environments\\environment.ts"], "sourcesContent": ["// This file can be replaced during build by using the `fileReplacements` array.\r\n// `ng build` replaces `environment.ts` with `environment.prod.ts`.\r\n// The list of file replacements can be found in `angular.json`.\r\n\r\nexport const environment = {\r\n    production: false,\r\n    secret:'2cinfo1',\r\n    client:'esprit',\r\n    urlBackend:'http://localhost:3000/api/',\r\n    geminiApiKey :'AIzaSyDCXc16FzaVWSJkW4RGboTZ8AD9_PTDL88'\r\n  };\r\n  \r\n  /*\r\n   * For easier debugging in development mode, you can import the following file\r\n   * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.\r\n   *\r\n   * This import should be commented out in production mode because it will have a negative impact\r\n   * on performance if an error is thrown.\r\n   */\r\n  // import 'zone.js/plugins/zone-error';  // Included with Angular CLI."], "mappings": "AAAA;AACA;AACA;AAEA,OAAO,MAAMA,WAAW,GAAG;EACvBC,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAC,SAAS;EAChBC,MAAM,EAAC,QAAQ;EACfC,UAAU,EAAC,4BAA4B;EACvCC,YAAY,EAAE;CACf;AAED;;;;;;;AAOA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}