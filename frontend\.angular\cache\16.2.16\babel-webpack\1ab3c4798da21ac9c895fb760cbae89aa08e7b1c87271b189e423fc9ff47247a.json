{"ast": null, "code": "import { throwError } from 'rxjs';\nimport { catchError, tap } from 'rxjs/operators';\nimport { environment } from '../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let MembreService = /*#__PURE__*/(() => {\n  class MembreService {\n    constructor(http) {\n      this.http = http;\n      this.apiUrl = `${environment.urlBackend}teammembers`;\n      console.log('Membre API URL:', this.apiUrl);\n    }\n    getMembres() {\n      console.log('Fetching members from:', this.apiUrl);\n      return this.http.get(this.apiUrl).pipe(tap(data => console.log('Members received:', data)), catchError(this.handleError));\n    }\n    getMembre(id) {\n      console.log(`Fetching member with id ${id} from: ${this.apiUrl}/${id}`);\n      return this.http.get(`${this.apiUrl}/${id}`).pipe(tap(data => console.log('Member received:', data)), catchError(this.handleError));\n    }\n    addMembre(membre) {\n      console.log('Adding member:', membre);\n      return this.http.post(this.apiUrl, membre).pipe(tap(data => console.log('Member added, response:', data)), catchError(this.handleError));\n    }\n    deleteMembre(id) {\n      console.log(`Deleting member with id ${id}`);\n      return this.http.delete(`${this.apiUrl}/${id}`).pipe(tap(data => console.log('Member deleted, response:', data)), catchError(this.handleError));\n    }\n    handleError(error) {\n      let errorMessage = '';\n      if (error.error instanceof ErrorEvent) {\n        // Erreur côté client\n        errorMessage = `Erreur client: ${error.error.message}`;\n      } else {\n        // Erreur côté serveur\n        const status = error.status;\n        const message = error.error?.message || error.statusText;\n        errorMessage = `Erreur serveur: Code ${status}, Message: ${message}`;\n        // Log des détails supplémentaires pour le débogage\n        console.error('Error details:', {\n          status: error.status,\n          statusText: error.statusText,\n          url: error.url,\n          error: error.error\n        });\n        if (status === 0) {\n          console.error(\"Le serveur est-il en cours d'exécution? Vérifiez la connexion réseau.\");\n        }\n      }\n      console.error('API Error:', errorMessage);\n      return throwError(() => new Error(errorMessage));\n    }\n    static {\n      this.ɵfac = function MembreService_Factory(t) {\n        return new (t || MembreService)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: MembreService,\n        factory: MembreService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return MembreService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}